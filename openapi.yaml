# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /v1/boo_world/capture/add_boo_capture_record_boo_with_captor_resource:
        post:
            tags:
                - BooWorld
            description: 给一个捕获记录添加合照
            operationId: BooWorld_AddBooCaptureRecordBooWithCaptorResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.capture.v1.AddBooCaptureRecordBooWithCaptorResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.capture.v1.AddBooCaptureRecordBooWithCaptorResourceResponse'
    /v1/boo_world/capture/list_my_boo_captured_boo_records_by_location:
        post:
            tags:
                - BooWorld
            description: 获取我的鬼被抓的记录
            operationId: BooWorld_ListMyBooCapturedBooRecordsByLocation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.capture.v1.ListMyBooCapturedBooRecordsByLocationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.capture.v1.ListMyBooCapturedBooRecordsByLocationResponse'
    /v1/boo_world/capture/list_my_captured_boo_records:
        post:
            tags:
                - BooWorld
            description: 获取我抓到的鬼记录
            operationId: BooWorld_ListMyCapturedBooRecords
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.capture.v1.ListMyCapturedBooRecordsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.capture.v1.ListMyCapturedBooRecordsResponse'
    /v1/boo_world/capture/report_boo_capture_record:
        post:
            tags:
                - BooWorld
            description: 上报一个捕获记录
            operationId: BooWorld_ReportBooCaptureRecord
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.capture.v1.ReportBooCaptureRecordRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.capture.v1.ReportBooCaptureRecordResponse'
    /v1/boo_world/map/get_boo_world_user_summary:
        post:
            tags:
                - BooWorld
            description: boo world 登录用户summary信息
            operationId: BooWorld_GetBooWorldUserSummary
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.map.v1.GetBooWorldUserSummaryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.map.v1.GetBooWorldUserSummaryResponse'
    /v1/boo_world/map/get_nearby_boo:
        post:
            tags:
                - BooWorld
            description: 获取人模式下周围 boo
            operationId: BooWorld_GetNearbyBoo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.map.v1.GetNearbyBooRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.map.v1.GetNearbyBooResponse'
    /v1/boo_world/map/get_nearby_user_and_resource:
        post:
            tags:
                - BooWorld
            description: 获取鬼模式下周围的人及资源
            operationId: BooWorld_GetNearbyUserAndResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.map.v1.GetNearbyUserAndResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.map.v1.GetNearbyUserAndResourceResponse'
    /v1/boo_world/map/move_boo_to_location:
        post:
            tags:
                - BooWorld
            description: 移动自己的鬼
            operationId: BooWorld_MoveBooToLocation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.map.v1.MoveBooToLocationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.map.v1.MoveBooToLocationResponse'
    /v1/boo_world/resource/create:
        post:
            tags:
                - BooWorld
            description: 创建状态资源
            operationId: BooWorld_CreateResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.resource.v1.CreateResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.resource.v1.CreateResourceResponse'
    /v1/boo_world/resource/create_relation:
        post:
            tags:
                - BooWorld
            description: 创建状态资源关系
            operationId: BooWorld_CreateResourceRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.resource.v1.CreateResourceRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.resource.v1.CreateResourceRelationResponse'
    /v1/boo_world/resource/remove_relation:
        post:
            tags:
                - BooWorld
            description: 删除状态资源关系
            operationId: BooWorld_RemoveResourceRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.boo_world.resource.v1.RemoveResourceRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.boo_world.resource.v1.RemoveResourceRelationResponse'
    /v1/fizz/batch_list_events:
        post:
            tags:
                - FizzService
            operationId: FizzService_BatchListFizzEvents
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.events.v1.BatchListFizzEventsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.events.v1.BatchListFizzEventsResponse'
    /v1/fizz/create:
        post:
            tags:
                - FizzService
            description: 创建fizz
            operationId: FizzService_CreateFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.v1.CreateFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.v1.CreateFizzResponse'
    /v1/fizz/detail:
        post:
            tags:
                - FizzService
            description: 获取fizz详情
            operationId: FizzService_GetFizzDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.v1.GetFizzDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.v1.GetFizzDetailResponse'
    /v1/fizz/feed/get_new_recommend_medias:
        post:
            tags:
                - FizzService
            description: 获取新推荐fizz资源，端上定时轮训此接口达成气泡冒出效果
            operationId: FizzService_ListNewRecommendFizzMedias
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.feed.v1.ListNewRecommendFizzMediasRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.feed.v1.ListNewRecommendFizzMediasResponse'
    /v1/fizz/feed/get_recommend_medias:
        post:
            tags:
                - FizzService
            description: 获取fizz资源
            operationId: FizzService_ListFizzRecommendMedias
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.feed.v1.ListRecommendFizzMediasRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.feed.v1.ListRecommendFizzMediasResponse'
    /v1/fizz/feed/nearby:
        post:
            tags:
                - FizzService
            description: 获取附近fizz
            operationId: FizzService_ListFizzNearBy
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.feed.v1.ListFizzNearByRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.feed.v1.ListFizzNearByResponse'
    /v1/fizz/feed/recommend:
        post:
            tags:
                - FizzService
            description: 获取推荐fizz
            operationId: FizzService_ListRecommendFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.feed.v1.ListRecommendFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.feed.v1.ListRecommendFizzResponse'
    /v1/fizz/generate_example_resource_caption:
        post:
            tags:
                - FizzService
            description: 生成fizz示例资源描述
            operationId: FizzService_GenerateExampleResourceCaption
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.v1.GenerateExampleResourceCaptionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.v1.GenerateExampleResourceCaptionResponse'
    /v1/fizz/join:
        post:
            tags:
                - FizzService
            description: 加入fizz
            operationId: FizzService_JoinFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.user_relation.v1.JoinFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/fizz/join_condition_check:
        post:
            tags:
                - FizzService
            description: |-
                检查用户是否满足加入fizz的条件
                 这个接口会有缓存机制，如果用户已经检查过，除非字段发生变化，否则不会重复检查
                 缓存时间5分钟
            operationId: FizzService_JoinFizzConditionCheck
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.user_relation.v1.JoinFizzConditionCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.user_relation.v1.JoinFizzConditionCheckResponse'
    /v1/fizz/join_condition_check_cache:
        post:
            tags:
                - FizzService
            description: 获取fizz加入条件检查缓存
            operationId: FizzService_GetJoinFizzConditionCheckCache
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.user_relation.v1.GetJoinFizzConditionCheckCacheRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.user_relation.v1.JoinFizzConditionCheckResponse'
    /v1/fizz/list_user_created:
        post:
            tags:
                - FizzService
            description: list user created fizz
            operationId: FizzService_ListUserCreatedFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.v1.ListUserCreatedFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.v1.ListUserCreatedFizzResponse'
    /v1/fizz/media/add:
        post:
            tags:
                - FizzService
            description: 添加资源到fizz
            operationId: FizzService_AddMediaIntoFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.v1.AddMediaIntoFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.v1.AddMediaIntoFizzResponse'
    /v1/fizz/media/get_detail:
        post:
            tags:
                - FizzService
            description: fizz 资源详情
            operationId: FizzService_GetFizzMediaDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.v1.GetFizzMediaDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.v1.GetFizzMediaDetailResponse'
    /v1/fizz/media/list_created:
        post:
            tags:
                - FizzService
            description: |-
                ********** fizz media api group **********
                 获取用户创建的fizz资源
            operationId: FizzService_ListCreatedFizzMedias
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.v1.ListCreatedFizzMediasRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.v1.ListCreatedFizzMediasResponse'
    /v1/fizz/media/user_relation/create:
        post:
            tags:
                - FizzService
            description: |-
                创建fizz资源关系，目前支持
                 1. 点赞资源
            operationId: FizzService_CreateFizzMediaUserRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.user_relation.v1.CreateFizzMediaUserRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.v1.GetFizzMediaDetailResponse'
    /v1/fizz/media/user_relation/list:
        post:
            tags:
                - FizzService
            description: 获取fizz资源关系用户列表
            operationId: FizzService_ListMediaRelatedUsers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.user_relation.v1.ListMediaRelatedUsersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.user_relation.v1.ListMediaRelatedUsersResponse'
    /v1/fizz/media/user_relation/list_same_fizz_medias_relation_made_each_other:
        post:
            tags:
                - FizzService
            description: 获取同 fizz 下，互相点赞过的用户
            operationId: FizzService_ListSameFizzMediasRelationMadeEachOther
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.user_relation.v1.ListSameFizzMediasRelationMadeEachOtherRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.user_relation.v1.ListSameFizzMediasRelationMadeEachOtherResponse'
    /v1/fizz/media/user_relation/remove:
        post:
            tags:
                - FizzService
            description: |-
                移除fizz资源关系
                 1. 移除点赞关系
            operationId: FizzService_RemoveFizzMediaUserRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.user_relation.v1.RemoveFizzMediaUserRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.media.v1.GetFizzMediaDetailResponse'
    /v1/fizz/media/user_relation/send_message:
        post:
            tags:
                - FizzService
            description: 发送消息到fizz资源
            operationId: FizzService_SendMessageToFizzMedia
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.media.user_relation.v1.SendMessageToFizzMediaRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/fizz/peek/batch_get_matching_job:
        post:
            tags:
                - FizzService
            operationId: FizzService_BatchGetPeekMatchingJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.peek.v1.BatchGetPeekMatchingJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.peek.v1.BatchGetPeekMatchingJobResponse'
    /v1/fizz/peek/cancel_matching_job:
        post:
            tags:
                - FizzService
            description: '********** fizz peek api group **********'
            operationId: FizzService_CancelPeekMatchingJobs
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.peek.v1.CancelPeekMatchingJobsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/fizz/peek/get_match_resource_list:
        post:
            tags:
                - FizzService
            operationId: FizzService_GetPeekFizzMatchResourceList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.peek.v1.GetPeekFizzMatchResourceListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.peek.v1.GetPeekFizzMatchResourceListResponse'
    /v1/fizz/peek/list_matching_job:
        post:
            tags:
                - FizzService
            operationId: FizzService_ListPeekMatchingJobs
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.peek.v1.ListPeekMatchingJobsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.peek.v1.ListPeekMatchingJobsResponse'
    /v1/fizz/peek/submit_match:
        post:
            tags:
                - FizzService
            operationId: FizzService_SubmitPeekMatch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.peek.v1.SubmitPeekMatchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.peek.v1.SubmitPeekMatchResponse'
    /v1/fizz/peek/submit_matching_job:
        post:
            tags:
                - FizzService
            operationId: FizzService_SubmitPeekMatchingJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.peek.v1.SubmitPeekMatchingJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.peek.v1.SubmitPeekMatchingJobResponse'
    /v1/fizz/relay/begin_to_capturing_resource_for_unlock:
        post:
            tags:
                - FizzService
            description: 开始拍摄待解锁素材，改变用户的状态为 shooting
            operationId: FizzService_BeginToCapturingResourceForUnlock
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.BeginToCapturingResourceForUnlockRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.BeginToCapturingResourceForUnlockResponse'
    /v1/fizz/relay/create_play_room:
        post:
            tags:
                - FizzService
            description: |-
                ********** fizz relay api group **********
                 创建一个房间
            operationId: FizzService_CreateRelayPlayRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.CreatePlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.CreatePlayRoomResponse'
    /v1/fizz/relay/create_shot_resource:
        post:
            tags:
                - FizzService
            description: 在 room 内拍摄一个待解锁的素材
            operationId: FizzService_CreateRelayShotResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.CreateShotResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.CreateShotResourceResponse'
    /v1/fizz/relay/get_play_room_detail:
        post:
            tags:
                - FizzService
            description: 查询房间
            operationId: FizzService_GetRelayPlayRoomDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.GetPlayRoomDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.GetPlayRoomDetailResponse'
    /v1/fizz/relay/get_play_room_rtc_token:
        post:
            tags:
                - FizzService
            description: 获取房间的 rtc token
            operationId: FizzService_GetRelayPlayRoomRTCToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.GetPlayRoomRTCTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.GetPlayRoomRTCTokenResponse'
    /v1/fizz/relay/join_play_room:
        post:
            tags:
                - FizzService
            description: 加入一个房间
            operationId: FizzService_JoinRelayPlayRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.JoinPlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.JoinPlayRoomResponse'
    /v1/fizz/relay/share_play_room:
        post:
            tags:
                - FizzService
            description: 分享房间
            operationId: FizzService_ShareRelayPlayRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.SharePlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.SharePlayRoomResponse'
    /v1/fizz/relay/submit_ready:
        post:
            tags:
                - FizzService
            description: 在等待阶段，提交准备
            operationId: FizzService_SubmitReady
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.SubmitReadyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.SubmitReadyResponse'
    /v1/fizz/relay/try_unlock_resource:
        post:
            tags:
                - FizzService
            description: 尝试拍摄一个素材进行解锁当前资源
            operationId: FizzService_TryUnlockRelayResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.relay.v1.TryUnlockResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.relay.v1.TryUnlockResourceResponse'
    /v1/fizz/rush/add_emoji_to_question_resource:
        post:
            tags:
                - FizzService
            description: 给问题对应的素材点 emoji
            operationId: FizzService_AddEmojiToQuestionResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.AddEmojiToQuestionResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.AddEmojiToQuestionResourceResponse'
    /v1/fizz/rush/create_play_room:
        post:
            tags:
                - FizzService
            description: |-
                ******************** Rush fizz api group
                 创建一个 rush 游戏房间
            operationId: FizzService_CreateRushRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.CreateRushPlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.CreateRushPlayRoomResponse'
    /v1/fizz/rush/get_play_room:
        post:
            tags:
                - FizzService
            description: 获取一个 rush 游戏房间
            operationId: FizzService_GetRushRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.GetRushPlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.GetRushPlayRoomResponse'
    /v1/fizz/rush/get_play_room_rtc_token:
        post:
            tags:
                - FizzService
            description: 获取房间的 rtc token
            operationId: FizzService_GetRushPlayRoomRTCToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.GetRushPlayRoomRTCTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.GetRushPlayRoomRTCTokenResponse'
    /v1/fizz/rush/join_play_room:
        post:
            tags:
                - FizzService
            description: 加入一个 rush 游戏房间
            operationId: FizzService_JoinRushRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.JoinRushPlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.JoinRushPlayRoomResponse'
    /v1/fizz/rush/share_play_room:
        post:
            tags:
                - FizzService
            description: 分享一个 rush 游戏房间
            operationId: FizzService_ShareRushRoom
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.ShareRushPlayRoomRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.ShareRushPlayRoomResponse'
    /v1/fizz/rush/submit_question:
        post:
            tags:
                - FizzService
            description: 提交问题
            operationId: FizzService_SubmitQuestion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.SubmitQuestionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.SubmitQuestionResponse'
    /v1/fizz/rush/submit_question_ready:
        post:
            tags:
                - FizzService
            description: 提交问题准备
            operationId: FizzService_SubmitQuestionReady
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.SubmitQuestionReadyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.SubmitQuestionReadyResponse'
    /v1/fizz/rush/take_question_resource:
        post:
            tags:
                - FizzService
            description: 拍摄问题对应的素材
            operationId: FizzService_TakeQuestionResource
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.TakeQuestionResourceRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.TakeQuestionMaterialResponse'
    /v1/fizz/rush/transfer_bomb:
        post:
            tags:
                - FizzService
            description: 转移炸弹
            operationId: FizzService_TransferBomb
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.rush.v1.TransferBombRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.rush.v1.TransferBombResponse'
    /v1/fizz/search:
        post:
            tags:
                - FizzService
            description: 搜索fizz
            operationId: FizzService_SearchFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.search.v1.SearchFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.search.v1.SearchFizzResponse'
    /v1/fizz/top:
        post:
            tags:
                - FizzService
            description: 置顶fizz
            operationId: FizzService_TopFizz
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.v1.TopFizzRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/fizz/vibe/create_item:
        post:
            tags:
                - FizzService
            description: |-
                ********** fizz vibe api group **********
                 创建 vibe 内容
            operationId: FizzService_CreateVibeItem
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.CreateVibeItemRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.vibe.v1.CreateVibeItemResponse'
    /v1/fizz/vibe/get_recived_like_summary:
        post:
            tags:
                - FizzService
            description: 获取用户收到的 vibe 下的点赞汇总
            operationId: FizzService_GetRecivedLikeSummary
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.GetRecivedLikeSummaryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.vibe.v1.GetRecivedLikeSummaryResponse'
    /v1/fizz/vibe/get_recommend_users_and_items:
        post:
            tags:
                - FizzService
            description: 获取用户推荐的用户和 vibe 内容
            operationId: FizzService_GetRecommendUsersAndItems
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.GetRecommendUsersAndItemsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.vibe.v1.GetRecommendUsersAndItemsResponse'
    /v1/fizz/vibe/list_items:
        post:
            tags:
                - FizzService
            description: 获取 vibe items 列表
            operationId: FizzService_ListVibeItems
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.ListVibeItemsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.vibe.v1.ListVibeItemsResponse'
    /v1/fizz/vibe/list_received_likes:
        post:
            tags:
                - FizzService
            description: 获取用户收到的 vibe 下的点赞
            operationId: FizzService_ListReceivedLikes
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.ListReceivedLikesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.fizz.vibe.v1.ListReceivedLikesResponse'
    /v1/fizz/vibe/mark_recived_likes_creator_post_items_as_read:
        post:
            tags:
                - FizzService
            description: 标记用户收到的 vibe 下的点赞为已读
            operationId: FizzService_MarkRecivedLikesCreatorPostItemsAsRead
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.MarkRecivedLikesCreatorPostItemsAsReadRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/fizz/vibe/report_user_like_action:
        post:
            tags:
                - FizzService
            description: 上报用户点赞行为
            operationId: FizzService_ReportUserLikeAction
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.fizz.vibe.v1.ReportUserLikeActionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/im/send_message:
        post:
            tags:
                - IMService
            operationId: IMService_SendMessage
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.im.v1.SendMessageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/activity/list_users_by_consumption_status:
        post:
            tags:
                - Items
            operationId: Items_ListUsersByConsumptionStatus
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.activity.v1.ListUsersByConsumptionStatusRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.activity.v1.ListUsersByConsumptionStatusResponse'
    /v1/items/asr:
        post:
            tags:
                - Items
            description: 音频转文字
            operationId: Items_Asr
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.v1.AsrRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.v1.AsrResponse'
    /v1/items/batch_get_item_summaries:
        post:
            tags:
                - Items
            description: 批量获取item摘要
            operationId: Items_BatchGetItemSummaries
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.v1.BatchGetItemSummariesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.v1.BatchGetItemSummariesResponse'
    /v1/items/comments/create:
        post:
            tags:
                - Items
            description: |-
                comments 相关 ...
                 创建评论
            operationId: Items_CreateComment
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.comments.v1.CreateCommentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.comments.v1.CreateCommentResponse'
    /v1/items/comments/delete_comment_or_reply:
        post:
            tags:
                - Items
            description: 删除评论
            operationId: Items_DeleteCommentOrReply
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.comments.v1.DeleteCommentOrReplyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/comments/like_comment_or_reply:
        post:
            tags:
                - Items
            description: 点赞评论或回复
            operationId: Items_LikeCommentOrReply
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.comments.v1.LikeCommentOrReplyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/comments/list:
        post:
            tags:
                - Items
            description: 获取 item's 评论列表
            operationId: Items_ListComments
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.comments.v1.ListCommentsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.comments.v1.ListCommentsResponse'
    /v1/items/comments/list_replies:
        post:
            tags:
                - Items
            description: 获取 item's 评论回复列表
            operationId: Items_ListCommentReplies
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.comments.v1.ListCommentRepliesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.comments.v1.ListCommentRepliesResponse'
    /v1/items/comments/unlike_comment_or_reply:
        post:
            tags:
                - Items
            description: 取消点赞评论或回复
            operationId: Items_UnlikeCommentOrReply
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.comments.v1.UnlikeCommentOrReplyRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/create:
        post:
            tags:
                - Items
            description: 创建item，目前会直接发布而不是进入草稿态
            operationId: Items_CreateItem
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.v1.CreateItemRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.v1.CreateItemResponse'
    /v1/items/home_page_timeline:
        post:
            tags:
                - Items
            description: 首页时间轴
            operationId: Items_HomePageTimeline
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.v1.HomePageTimelineRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.v1.HomePageTimelineResponse'
    /v1/items/music_search:
        post:
            tags:
                - Items
            description: 音乐搜索
            operationId: Items_MusicSearch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.music.search.v1.SearchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.music.search.v1.SearchResponse'
    /v1/items/portal/create_moment:
        post:
            tags:
                - Items
            description: '********** portal **********'
            operationId: Items_CreateMoment
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.CreateMomentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.CreateMomentResponse'
    /v1/items/portal/create_moment_relation:
        post:
            tags:
                - Items
            operationId: Items_CreateMomentRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.CreateMomentRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.CreateMomentRelationResponse'
    /v1/items/portal/delete_moment:
        post:
            tags:
                - Items
            operationId: Items_DeleteMoment
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.DeleteMomentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/portal/get_moment:
        post:
            tags:
                - Items
            description: 通过 moment_id 获取 moment 详情
            operationId: Items_GetMoment
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.GetMomentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.GetMomentResponse'
    /v1/items/portal/get_portal:
        post:
            tags:
                - Items
            operationId: Items_GetPortal
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.GetPortalRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.GetPortalResponse'
    /v1/items/portal/get_user_created_portals_info:
        post:
            tags:
                - Items
            operationId: Items_GetUserCreatedPortalsInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.GetUserCreatedPortalsInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.GetUserCreatedPortalsInfoResponse'
    /v1/items/portal/list_could_append_moment_stories:
        post:
            tags:
                - Items
            operationId: Items_ListCouldAppendMomentStories
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.ListCouldAppendMomentStoriesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.ListCouldAppendMomentStoriesResponse'
    /v1/items/portal/list_moment_viewers:
        post:
            tags:
                - Items
            operationId: Items_ListMomentViewers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.ListMomentViewersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.ListMomentViewersResponse'
    /v1/items/portal/list_my_portals:
        post:
            tags:
                - Items
            operationId: Items_ListMyPortals
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.ListMyPortalsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.ListMyPortalsResponse'
    /v1/items/portal/list_user_created_portals_with_time_range:
        post:
            tags:
                - Items
            operationId: Items_ListUserCreatedPortalsWithTimeRange
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse'
    /v1/items/portal/remove_moment_relation:
        post:
            tags:
                - Items
            operationId: Items_RemoveMomentRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.RemoveMomentRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.RemoveMomentRelationResponse'
    /v1/items/portal/report_read:
        post:
            tags:
                - Items
            operationId: Items_ReportRead
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.ReportReadRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/portal/send_moment_invite:
        post:
            tags:
                - Items
            description: 分享给at的好友创建moment的消息,创建story/moment时调用
            operationId: Items_SendMomentInvite
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.SendMomentInviteRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.SendMomentInviteResponse'
    /v1/items/portal/trending_portals:
        post:
            tags:
                - Items
            operationId: Items_ListTrendingPortals
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.portal.v1.ListTrendingPortalsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.portal.v1.ListTrendingPortalsResponse'
    /v1/items/reaction/create:
        post:
            tags:
                - Items
            description: 创建 item reaction
            operationId: Items_CreateItemReaction
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.reaction.v1.CreateItemReactionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.reaction.v1.CreateItemReactionResponse'
    /v1/items/reaction/list_user_reacted:
        post:
            tags:
                - Items
            description: 获取用户 reacted 的 items
            operationId: Items_ListUserReactedItems
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.reaction.v1.ListUserReactedItemsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.reaction.v1.ListUserReactedItemsResponse'
    /v1/items/reaction/remove:
        post:
            tags:
                - Items
            description: 移除 item reaction
            operationId: Items_RemoveItemReaction
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.reaction.v1.RemoveItemReactionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.reaction.v1.RemoveItemReactionResponse'
    /v1/items/story/activity/get_unread_count:
        post:
            tags:
                - Items
            operationId: Items_GetActivityUnreadCount
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.activity.v1.GetActivityUnreadCountRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.activity.v1.GetActivityUnreadCountResponse'
    /v1/items/story/activity/list_activities:
        post:
            tags:
                - Items
            operationId: Items_ListActivities
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.activity.v1.ListActivitiesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.activity.v1.ListActivitiesResponse'
    /v1/items/story/activity/report_read:
        post:
            tags:
                - Items
            operationId: Items_ReportActivityRead
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.activity.v1.ReportActivityReadRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/story/consume_base_play_story:
        post:
            tags:
                - Items
            description: 消费 baseplay story
            operationId: Items_ConsumeBasePlayStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeBasePlayStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeBasePlayStoryResponse'
    /v1/items/story/consume_capsule_story:
        post:
            tags:
                - Items
            description: 消费 Capsule story
            operationId: Items_ConsumeCapsuleStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeCapsuleStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeCapsuleStoryResponse'
    /v1/items/story/consume_exchange_image_story:
        post:
            tags:
                - Items
            description: 消费换图 story
            operationId: Items_ConsumeExchangeImageStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeExchangeImageStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeExchangeImageStoryResponse'
    /v1/items/story/consume_now_shot_story:
        post:
            tags:
                - Items
            description: 消费 NowShot story
            operationId: Items_ConsumeNowShotStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeNowShotStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeNowShotStoryResponse'
    /v1/items/story/consume_roasted_story:
        post:
            tags:
                - Items
            description: 消费 roasted story
            operationId: Items_ConsumeRoastedStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeRoastedStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeRoastedStoryResponse'
    /v1/items/story/consume_roasted_topic:
        post:
            tags:
                - Items
            description: 消费 roasted topic
            operationId: Items_ConsumeRoastedTopic
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeRoastedTopicRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeRoastedTopicResponse'
    /v1/items/story/consume_turtle_soup_story:
        post:
            tags:
                - Items
            description: 消费海龟汤 story
            operationId: Items_ConsumeTurtleSoupStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeTurtleSoupStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeTurtleSoupStoryResponse'
    /v1/items/story/consume_unmute_story:
        post:
            tags:
                - Items
            description: 消费Unmute story
            operationId: Items_ConsumeUnmuteStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ConsumeUnmuteStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ConsumeUnmuteStoryResponse'
    /v1/items/story/copilot_capsule_story:
        post:
            tags:
                - Items
            description: Copilot Capsule story 创作
            operationId: Items_CopilotCapsuleStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CopilotCapsuleStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CopilotCapsuleStoryResponse'
    /v1/items/story/create_base_play_story:
        post:
            tags:
                - Items
            description: 创建baseplay的 story
            operationId: Items_CreateBasePlayStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateBasePlayStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateBasePlayStoryResponse'
    /v1/items/story/create_capsule_story:
        post:
            tags:
                - Items
            description: 创建 Capsule story
            operationId: Items_CreateCapsuleStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateCapsuleStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateCapsuleStoryResponse'
    /v1/items/story/create_exchange_image_story:
        post:
            tags:
                - Items
            description: 创建换图 story
            operationId: Items_CreateExchangeImageStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateExchangeImageStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateExchangeImageStoryResponse'
    /v1/items/story/create_now_shot_story:
        post:
            tags:
                - Items
            description: 创建 NowShot story
            operationId: Items_CreateNowShotStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateNowShotStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateNowShotStoryResponse'
    /v1/items/story/create_roasted_story:
        post:
            tags:
                - Items
            description: 创建 roasted story
            operationId: Items_CreateRoastedStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateRoastedStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateRoastedStoryResponse'
    /v1/items/story/create_story_reaction:
        post:
            tags:
                - Items
            description: story reactions ************
            operationId: Items_CreateStoryReaction
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.reaction.v1.CreateReactionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.reaction.v1.CreateReactionResponse'
    /v1/items/story/create_turtle_soup_story:
        post:
            tags:
                - Items
            description: 创建海龟汤 story
            operationId: Items_CreateTurtleSoupStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateTurtleSoupStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateTurtleSoupStoryResponse'
    /v1/items/story/create_unmute_story:
        post:
            tags:
                - Items
            description: 创建unmute story
            operationId: Items_CreateUnmuteStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.CreateUnmuteStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.CreateUnmuteStoryResponse'
    /v1/items/story/delete:
        post:
            tags:
                - Items
            description: |-
                ************ story 相关 ************
                 删除 story
            operationId: Items_DeleteStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.DeleteStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/story/delete_story_reaction:
        post:
            tags:
                - Items
            operationId: Items_DeleteStoryReaction
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.reaction.v1.DeleteReactionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.reaction.v1.DeleteReactionResponse'
    /v1/items/story/get_roasted_topics:
        post:
            tags:
                - Items
            description: 获取 roasted 的 topics
            operationId: Items_GetRoastedTopics
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.GetRoastedTopicsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.GetRoastedTopicsResponse'
    /v1/items/story/get_story_detail:
        post:
            tags:
                - Items
            description: 获取 story 详情
            operationId: Items_GetStoryDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.GetStoryDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.GetStoryDetailResponse'
    /v1/items/story/list_common_condition_templates:
        post:
            tags:
                - Items
            description: 获取Reveal/Type/Unmute玩法条件模板
            operationId: Items_ListCommonConditionTemplates
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListCommonStoryConditionTemplatesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListCommonStoryConditionTemplatesResponse'
    /v1/items/story/list_creator_story:
        post:
            tags:
                - Items
            description: 创作者的 Story 列表
            operationId: Items_ListCreatorStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListCreatorStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListCreatorStoryResponse'
    /v1/items/story/list_exchange_images_condition_templetes:
        post:
            tags:
                - Items
            description: 获取换图玩法条件模板
            operationId: Items_ListExchangeImageStoryConditionTemplates
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse'
    /v1/items/story/list_reaction_made_users:
        post:
            tags:
                - Items
            operationId: Items_ListStoryReactionMadeUsers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.reaction.v1.ListReactionMadeUsersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.reaction.v1.ListReactionMadeUsersResponse'
    /v1/items/story/list_same_author_story_with_anchor:
        post:
            tags:
                - Items
            description: 获取某个 Story 同作者一定时间范围内对的其他 story
            operationId: Items_ListSameAuthorStoryWithAnchor
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListSameAuthorStoryWithAnchorRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListSameAuthorStoryWithAnchorResponse'
    /v1/items/story/list_turtle_soup_condition_templates:
        post:
            tags:
                - Items
            description: 获取海龟汤玩法条件模板
            operationId: Items_ListTurtleSoupStoryConditionTemplates
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListTurtleSoupStoryConditionTemplatesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse'
    /v1/items/story/list_unlocked_story:
        post:
            tags:
                - Items
            description: 获取用户unlocked play
            operationId: Items_ListUnlockedStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListUnlockedStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListUnlockedStoryResponse'
    /v1/items/story/list_unmute_condition_templetes:
        post:
            tags:
                - Items
            description: 获取 Unmute 玩法条件模板
            operationId: Items_ListUnmuteStoryConditionTemplates
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse'
    /v1/items/story/report_share_stats:
        post:
            tags:
                - Items
            operationId: Items_ReportShareStat
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.v1.ReportShareStatRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.v1.ReportShareStatResponse'
    /v1/items/story/top:
        post:
            tags:
                - Items
            description: 置顶 story
            operationId: Items_TopStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.TopStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/items/story/update:
        post:
            tags:
                - Items
            description: 修改 story
            operationId: Items_UpdateStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v1.UpdateStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v1.UpdateStoryResponse'
    /v1/push/register_push_token:
        post:
            tags:
                - Push
            description: 注册推送令牌
            operationId: Push_RegisterPushToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.push.v1.RegisterPushTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/push/test_push:
        post:
            tags:
                - Push
            description: 测试推送
            operationId: Push_TestPush
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.push.v1.TestPushRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.push.v1.TestPushResponse'
    /v1/resource/batch_get_put_object_pre_sign_url:
        post:
            tags:
                - ResourceService
            description: |-
                *
                http://dev-api-doc.frolic.fun/#/ResourceService/ResourceService_GetPutObjectPreSignUrl

                新版本资源上传预请求接口，此后静态资源上传都要走此接口
                通过此接口获取上传的 put 请求地址，以下参数**必传**：
                {
                "scenario": "SCENARIO_COPILOT",  // 创作者工具固定传
                "contentType": "CONTENT_TYPE_UNSPECIFIED", // 文件类型，具体参考 https://github.com/FutureGadgets-co/boson/blob/main/api/resource/types/v1/types.proto#L27 处定义
                "contentLength": "string" // 文件大小
                }
                请求成功后，获取以下返回结果：
                {
                "uploadUrl": "string", //此后要用的 put 上传地址
                "objectKey": "string", //资源id
                "objectAccessUrl": "string",//资源公网访问地址
                "expiresAt": "string",//该 put 链接过期时间
                "contentType": "string"//可忽略
                }
                发起 put http 请求，body 内携带你要上传的文件即可，注意，需要携带 Expires 头，如：

                curl --location --request PUT 'https://future-gadgets-resource-01.s3.us-east-1.amazonaws.com/frolic/image/creator/copilot/f4485438-3051-706f-d402-54cd4e3fb638/202501140920/447242d126d84315b8b613139b2a48d4.png?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIA3FLD3ELBJZS5CFNY%2F20250114%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250114T092047Z&X-Amz-Expires=900&X-Amz-SignedHeaders=content-length%3Bcontent-type%3Bexpires%3Bhost&x-id=PutObject&X-Amz-Signature=c45b33afa2da6b24ab7101fd925fb2b48c2c3be42c4b23cd9981fe60e00ca178' \
                --header 'Expires: Tue, 14 Jan 2025 09:25:47 GMT' \
                --header 'Content-Type: image/png' \
                --data-binary '@/Users/<USER>/Downloads/96e8261ea3f348e99cb67ecc984816c4.png'
            operationId: ResourceService_BatchGetPutObjectPreSignUrl
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.resource.v1.BatchGetPutObjectPreSignUrlRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.resource.v1.BatchGetPutObjectPreSignUrlResponse'
    /v1/resource/get_put_object_pre_sign_url:
        post:
            tags:
                - ResourceService
            operationId: ResourceService_GetPutObjectPreSignUrl
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.resource.v1.GetPutObjectPreSignUrlRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.resource.v1.GetPutObjectPreSignUrlResponse'
    /v1/search:
        post:
            tags:
                - SearchService
            operationId: SearchService_Search
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.search.v1.SearchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.search.v1.SearchResponse'
    /v1/search_history:
        post:
            tags:
                - SearchService
            operationId: SearchService_GetSearchHistory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.search.v1.GetSearchHistoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.search.v1.GetSearchHistoryResponse'
    /v1/search_history/delete:
        post:
            tags:
                - SearchService
            operationId: SearchService_DeleteSearchHistory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.search.v1.DeleteSearchHistoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.search.v1.DeleteSearchHistoryResponse'
    /v1/search_history/upload:
        post:
            tags:
                - SearchService
            operationId: SearchService_UploadSearchHistory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.search.v1.UploadSearchHistoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.search.v1.UploadSearchHistoryResponse'
    /v1/tracking/batch_report:
        post:
            tags:
                - Tracking
            operationId: Tracking_TrackingBatchReport
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.tracking.v1.TrackingBatchReportRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/album/add_items_to_album:
        post:
            tags:
                - UsersService
            description: 向相册批量添加内容
            operationId: UsersService_AddItemsToAlbum
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.AddItemsToAlbumRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.AddItemsToAlbumResponse'
    /v1/users/album/batch_update_items_layout:
        post:
            tags:
                - UsersService
            description: 批量更新相册内容位置信息
            operationId: UsersService_BatchUpdateItemsLayout
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.BatchUpdateItemsLayoutRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.BatchUpdateItemsLayoutResponse'
    /v1/users/album/create_album:
        post:
            tags:
                - UsersService
            description: |-
                ********** albums api group **********
                 创建相册
            operationId: UsersService_CreateAlbum
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.CreateAlbumRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.CreateAlbumResponse'
    /v1/users/album/delete_album:
        post:
            tags:
                - UsersService
            description: 删除相册
            operationId: UsersService_DeleteAlbum
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.DeleteAlbumRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.DeleteAlbumResponse'
    /v1/users/album/list_album_items:
        post:
            tags:
                - UsersService
            description: 获取相册下内容列表
            operationId: UsersService_ListAlbumItems
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.ListAlbumItemsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.ListAlbumItemsResponse'
    /v1/users/album/list_albums:
        post:
            tags:
                - UsersService
            description: 获取相册列表
            operationId: UsersService_ListAlbums
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.ListAlbumsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.ListAlbumsResponse'
    /v1/users/album/remove_items_from_album:
        post:
            tags:
                - UsersService
            description: 向相册批量删除内容
            operationId: UsersService_RemoveItemsFromAlbum
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.RemoveItemsFromAlbumRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.RemoveItemsFromAlbumResponse'
    /v1/users/album/update_album:
        post:
            tags:
                - UsersService
            description: 更新相册标题
            operationId: UsersService_UpdateAlbum
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.UpdateAlbumRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.UpdateAlbumResponse'
    /v1/users/album/update_item_layout:
        post:
            tags:
                - UsersService
            description: 更新相册内容位置信息
            operationId: UsersService_UpdateItemLayout
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.albums.v1.UpdateItemLayoutRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.albums.v1.UpdateItemLayoutResponse'
    /v1/users/auth/get_im_token:
        post:
            tags:
                - UsersService
            description: 获取 IM Token
            operationId: UsersService_GetImToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.auth.v1.GetImTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.auth.v1.GetImTokenResponse'
    /v1/users/auth/login_or_register_with_verify_code:
        post:
            tags:
                - UsersService
            description: 手机 or 邮箱验证验证码并且登录
            operationId: UsersService_LoginOrRegisterWithVerifyCode
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.auth.v1.LoginOrRegisterWithVerifyCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.auth.v1.LoginOrRegisterWithVerifyCodeResponse'
    /v1/users/auth/send_verify_code:
        post:
            tags:
                - UsersService
            description: |-
                ********** auth api group **********
                 手机 or 邮箱发送验证码
            operationId: UsersService_SendVerifyCode
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.auth.v1.SendVerifyCodeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/auth/third_party_login:
        post:
            tags:
                - UsersService
            description: 三方 Oauth 登录
            operationId: UsersService_ThirdPartyLogin
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.auth.v1.ThirdPartyLoginRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.auth.v1.ThirdPartyLoginResponse'
    /v1/users/boo/create_avatar_job:
        post:
            tags:
                - UsersService
            description: 创建一个 头像 的生成任务
            operationId: UsersService_CreateAvatarJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.CreateAvatarJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.CreateAvatarJobResponse'
    /v1/users/boo/generate_boo_with_selected_avatar:
        post:
            tags:
                - UsersService
            description: 生成一个鬼
            operationId: UsersService_GenerateBooWithSelectedAvatar
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.GenerateBooWithSelectedAvatarRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.GenerateBooWithSelectedAvatarResponse'
    /v1/users/boo/get_latest_avatar_job:
        post:
            tags:
                - UsersService
            description: 获取最近的一个头像创建任务
            operationId: UsersService_GetLatestAvatarJob
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.GetLatestAvatarJobRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.GetLatestAvatarJobResponse'
    /v1/users/boo/get_latest_boo_show_info:
        post:
            tags:
                - UsersService
            description: |-
                ********** boo api group **********
                 获取最新的 Boo 展示信息
            operationId: UsersService_GetLatestBooShowInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.GetLatestBooShowInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.GetLatestBooShowInfoResponse'
    /v1/users/boo/record_boo_show:
        post:
            tags:
                - UsersService
            description: 记录 Boo 展示信息
            operationId: UsersService_RecordBooShow
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.RecordBooShowRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.RecordBooShowResponse'
    /v1/users/boo/report_capture_boo_action:
        post:
            tags:
                - UsersService
            description: 抓鬼结果上报接口
            operationId: UsersService_CaptureBooActionReport
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.CaptureBooActionReportRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.CaptureBooActionReportResponse'
    /v1/users/boo/select_avatar_as_used:
        post:
            tags:
                - UsersService
            description: 选择一个头像作为已使用
            operationId: UsersService_SelectAvatarAsUsed
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.SelectAvatarAsUsedRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.boo.v1.SelectAvatarAsUsedResponse'
    /v1/users/boo/send_avatar_as_boo_to_users:
        post:
            tags:
                - UsersService
            description: 发送一个头像作为鬼给某人
            operationId: UsersService_SendAvatarAsBooToUsers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.SendAvatarAsBooToUsersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/boo/send_capture_record_video:
        post:
            tags:
                - UsersService
            description: 发送抓鬼视频
            operationId: UsersService_SendCaptureRecordVideo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.boo.v1.SendCaptureRecordVideoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/highlights/create_highlight:
        post:
            tags:
                - UsersService
            description: 创建高光
            operationId: UsersService_CreateHighlight
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.highlights.v1.CreateHighlightRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.highlights.v1.CreateHighlightResponse'
    /v1/users/highlights/list_high_light_medias:
        post:
            tags:
                - UsersService
            description: 获取高光下的媒体
            operationId: UsersService_ListHighLightMedias
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.highlights.v1.ListHighLightMediasRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.highlights.v1.ListHighLightMediasResponse'
    /v1/users/highlights/list_user_created_highlights:
        post:
            tags:
                - UsersService
            description: 获取用户创建的高光
            operationId: UsersService_ListUserCreatedHighlights
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.highlights.v1.ListUserCreatedHighlightsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.highlights.v1.ListUserCreatedHighlightsResponse'
    /v1/users/highlights/remove_media_from_highlight:
        post:
            tags:
                - UsersService
            description: 从高光中移除媒体
            operationId: UsersService_RemoveMediaFromHighlight
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.highlights.v1.RemoveMediaFromHighlightRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/highlights/update_highlight:
        post:
            tags:
                - UsersService
            description: |-
                ********** highlights api group **********
                 update highlight
            operationId: UsersService_UpdateHighlight
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.highlights.v1.UpdateHighlightRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/info/batch_get_user_summaries:
        post:
            tags:
                - UsersService
            description: |-
                ********** info api group **********
                 批量获取用户信息
            operationId: UsersService_BatchGetUserSummaries
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.info.v1.BatchGetUserSummariesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.info.v1.BatchGetUserSummariesResponse'
    /v1/users/info/get_user_detail:
        post:
            tags:
                - UsersService
            description: 获取用户信息
            operationId: UsersService_GetUserDetail
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.info.v1.GetUserDetailRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.info.v1.GetUserDetailResponse'
    /v1/users/info/update_user_info:
        post:
            tags:
                - UsersService
            description: 更新用户信息
            operationId: UsersService_UpdateUserInfo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.info.v1.UpdateUserInfoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.info.v1.GetUserDetailResponse'
    /v1/users/notifications/batch_read_user_system_notifications:
        post:
            tags:
                - UsersService
            description: 批量读取用户系统通知
            operationId: UsersService_BatchReadUserSystemNotifications
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.notifications.v1.BatchReadUserSystemNotificationsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/notifications/get_user_system_notification_stat_summary:
        post:
            tags:
                - UsersService
            description: |-
                ********** notifications api group **********
                 获取用户系统通知统计摘要
            operationId: UsersService_GetUserSystemNotificationStatSummary
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.notifications.v1.GetUserSystemNotificationStatSummaryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.notifications.v1.GetUserSystemNotificationStatSummaryResponse'
    /v1/users/notifications/list_user_system_notifications:
        post:
            tags:
                - UsersService
            description: 获取用户系统通知列表
            operationId: UsersService_ListUserSystemNotifications
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.notifications.v1.ListUserSystemNotificationsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.notifications.v1.ListUserSystemNotificationsResponse'
    /v1/users/recommend/recommend_users:
        post:
            tags:
                - UsersService
            description: |-
                ********** recommend api group **********
                 推荐用户
            operationId: UsersService_RecommendUsers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.recommend.v1.RecommendUsersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.recommend.v1.RecommendUsersResponse'
    /v1/users/relations/create_relation:
        post:
            tags:
                - UsersService
            description: |-
                ********** relations api group **********
                 创建用户关系
            operationId: UsersService_CreateRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.relations.v1.CreateRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.relations.v1.UpdateRelationResponse'
    /v1/users/relations/list_relation_users:
        post:
            tags:
                - UsersService
            description: 获取关系下的用户列表
            operationId: UsersService_ListRelationUsers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.relations.v1.ListRelationUsersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.relations.v1.ListRelationUsersResponse'
    /v1/users/relations/remove_relation:
        post:
            tags:
                - UsersService
            description: 删除用户关系
            operationId: UsersService_RemoveRelation
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.relations.v1.RemoveRelationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.relations.v1.UpdateRelationResponse'
    /v1/users/rtm/get_rtm_token:
        post:
            tags:
                - UsersService
            description: |-
                ********** rtm api group **********
                 获取 RTM Token
            operationId: UsersService_GetRTMToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.rtm.v1.GetRTMTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.users.rtm.v1.GetRTMTokenResponse'
    /v1/users/rtm/rtm_callback:
        post:
            tags:
                - UsersService
            description: 声网 RTM 回调
            operationId: UsersService_RTMCallback
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.rtm.v1.RTMCallbackRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v1/users/save_user_push_token:
        post:
            tags:
                - UsersService
            description: |-
                ********** push api group **********
                 保存用户推送令牌
            operationId: UsersService_SaveUserPushToken
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.users.push.v1.SaveUserPushTokenRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v2/items/story/add_capture_boo_into_my_assist:
        post:
            tags:
                - Items
            operationId: Items_AddCaptureBooIntoMyAssist
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.AddCaptureBooIntoMyAssistRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v2/items/story/add_captured_boo_in_to_collected_stickers:
        post:
            tags:
                - Items
            operationId: Items_AddCapturedBooInToCollectedStickers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.AddCapturedBooInToCollectedStickersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.AddCapturedBooInToCollectedStickersResponse'
    /v2/items/story/auto_generate_area_emoji:
        post:
            tags:
                - Items
            description: 自动生成 area emoji
            operationId: Items_AutoGenerateAreaEmoji
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.AutoGenerateAreaEmojiRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.AutoGenerateAreaEmojiResponse'
    /v2/items/story/check_haunt_image:
        post:
            tags:
                - Items
            description: haunt 图片检测
            operationId: Items_CheckHauntImage
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ImageCheckRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ImageCheckResponse'
    /v2/items/story/collect_hide_sticker:
        post:
            tags:
                - Items
            description: 收藏 hide sticker
            operationId: Items_CollectHideSticker
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.hide_stickers.v1.CollectHideStickerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v2/items/story/consume_chatproxy:
        post:
            tags:
                - Items
            description: 消费 chatproxy
            operationId: Items_ConsumeChatProxy
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeChatProxyRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumeChatProxyResponseV2'
    /v2/items/story/consume_haunt_story:
        post:
            tags:
                - Items
            description: 消费 haunt story
            operationId: Items_ConsumeHauntStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeHauntStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumeHauntStoryResponse'
    /v2/items/story/consume_hide_story:
        post:
            tags:
                - Items
            description: 消费 hide story
            operationId: Items_ConsumeHideStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeHideStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumeHideStoryResponse'
    /v2/items/story/consume_now_shot_story:
        post:
            tags:
                - Items
            description: 消费 NowShot story v2
            operationId: Items_ConsumeNowShotStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeNowShotStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.StoryDetailResponseV2'
    /v2/items/story/consume_pin_story:
        post:
            tags:
                - Items
            description: 消费 pin story
            operationId: Items_ConsumePinStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumePinStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumePinStoryResponse'
    /v2/items/story/consume_roasted_story:
        post:
            tags:
                - Items
            description: 消费 roasted story v2
            operationId: Items_ConsumeRoastedStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeRoastedStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumeRoastedStoryResponseV2'
    /v2/items/story/consume_wassup_story:
        post:
            tags:
                - Items
            description: 消费 wassup v2
            operationId: Items_ConsumeWassupStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeWassupStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumeWassupStoryResponse'
    /v2/items/story/consume_who_story:
        post:
            tags:
                - Items
            description: 消费 who story
            operationId: Items_ConsumeWhoStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ConsumeWhoStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ConsumeWhoStoryResponseV2'
    /v2/items/story/create_chatproxy_story:
        post:
            tags:
                - Items
            description: 创建 chatproxy story
            operationId: Items_CreateChatProxyStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateChatProxyStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateChatProxyStoryResponseV2'
    /v2/items/story/create_exchange_image_story:
        post:
            tags:
                - Items
            description: 创建换图 story
            operationId: Items_CreateExchangeImageStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateExchangeImageStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateExchangeImageStoryResponseV2'
    /v2/items/story/create_haunt_story:
        post:
            tags:
                - Items
            description: 创建 haunt story
            operationId: Items_CreateHauntStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateHauntStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateHauntStoryResponse'
    /v2/items/story/create_hide_story:
        post:
            tags:
                - Items
            description: 创建 hide story
            operationId: Items_CreateHideStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateHideStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateHideStoryResponseV2'
    /v2/items/story/create_now_shot_story:
        post:
            tags:
                - Items
            description: 创建 NowShot V2
            operationId: Items_CreateNowShotStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateNowShotStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.StoryDetailResponseV2'
    /v2/items/story/create_pin_story:
        post:
            tags:
                - Items
            description: 创建 pin story
            operationId: Items_CreatePinStory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreatePinStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreatePinStoryResponse'
    /v2/items/story/create_turtle_soup_story:
        post:
            tags:
                - Items
            description: 创建海龟汤 story v2
            operationId: Items_CreateTurtleSoupStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateTurtleSoupStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateTurtleSoupStoryResponseV2'
    /v2/items/story/create_unmute_story:
        post:
            tags:
                - Items
            description: 创建unmute story
            operationId: Items_CreateUnmuteStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateUnmuteStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateUnmuteStoryResponseV2'
    /v2/items/story/create_wassup_story:
        post:
            tags:
                - Items
            description: 创建 wassup v2
            operationId: Items_CreateWassupStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateWassupStoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateWassupStoryResponse'
    /v2/items/story/create_who_story:
        post:
            tags:
                - Items
            description: 创建 who story
            operationId: Items_CreateWhoStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.CreateWhoStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.CreateWhoStoryResponseV2'
    /v2/items/story/generate_hide_image_mask:
        post:
            tags:
                - Items
            description: 生成 hide image mask
            operationId: Items_GenerateHideImageMask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.GenerateHideImageMaskRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.GenerateHideImageMaskResponse'
    /v2/items/story/get_chatproxy_next_topic:
        post:
            tags:
                - Items
            description: 获取 chatproxy 的下一个 topic
            operationId: Items_GetChatProxyNextTopic
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.GetChatProxyNextTopicRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.GetChatProxyNextTopicResponseV2'
    /v2/items/story/get_wassup_greetings:
        post:
            tags:
                - Items
            description: |-
                wassup v2
                 获取 wassup 的 greetings
            operationId: Items_GetWassupGreetings
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.GetWassupGreetingsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.GetWassupGreetingsResponse'
    /v2/items/story/get_wassup_next_question:
        post:
            tags:
                - Items
            description: 获取 wassup 的 next question
            operationId: Items_GetWassupNextQuestion
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.GetWassupNextQuestionRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.GetWassupNextQuestionResponse'
    /v2/items/story/list_creator_story:
        post:
            tags:
                - Items
            description: 创作者的 Story 列表
            operationId: Items_ListCreatorStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ListCreatorStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ListCreatorStoryResponseV2'
    /v2/items/story/list_following_creator_story:
        post:
            tags:
                - Items
            operationId: Items_ListFollowingCreatorStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ListFollowingCreatorStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ListFollowingCreatorStoryResponseV2'
    /v2/items/story/list_haunt_boo_assist:
        post:
            tags:
                - Items
            operationId: Items_ListHauntBooAssist
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ListHauntBooAssistRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ListHauntBooAssistResponse'
    /v2/items/story/list_haunt_questions:
        post:
            tags:
                - Items
            operationId: Items_ListHauntQuestions
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ListHauntQuestionsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ListHauntQuestionsResponse'
    /v2/items/story/list_haunt_random_avatars:
        post:
            tags:
                - Items
            operationId: Items_ListHauntRandomAvatars
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ListHauntRandomAvatarsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ListHauntRandomAvatarsResponse'
    /v2/items/story/list_home_page_story:
        post:
            tags:
                - Items
            description: 获取首页 story 列表
            operationId: Items_ListHomePageStoryV2
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ListHomePageStoryRequestV2'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ListHomePageStoryResponseV2'
    /v2/items/story/list_my_collected_hide_stickers:
        post:
            tags:
                - Items
            description: 获取用户收藏的 hide sticker
            operationId: Items_ListMyCollectedHideStickers
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.hide_stickers.v1.ListMyCollectedStickersRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse'
    /v2/items/story/manual_generate_area_emoji:
        post:
            tags:
                - Items
            description: 手动生成 area emoji
            operationId: Items_ManualGenerateAreaEmoji
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ManualGenerateAreaEmojiRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ManualGenerateAreaEmojiResponse'
    /v2/items/story/report_haunt_show:
        post:
            tags:
                - Items
            description: 上报 haunt boo 的展现
            operationId: Items_ReportHauntShow
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.ReportHauntShowRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.ReportHauntShowResponse'
    /v2/items/story/send_haunt_capture_video:
        post:
            tags:
                - Items
            operationId: Items_SendHauntCaptureVideo
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.SendHauntCaptureVideoRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v2/items/story/send_message:
        post:
            tags:
                - Items
            description: |-
                发送消息
                 本接口用于在用户完成对story的消费后，对产生的resource进行后续处理。接口名称为历史遗留问题;
            operationId: Items_SendMessage
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.v2.SendMessageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/api.items.story.v2.SendMessageResponse'
    /v2/items/story/top_hide_sticker:
        post:
            tags:
                - Items
            description: 置顶 hide sticker
            operationId: Items_TopHideSticker
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.hide_stickers.v1.TopCollectedStickerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v2/items/story/uncollect_hide_sticker:
        post:
            tags:
                - Items
            description: 取消收藏 hide sticker
            operationId: Items_UnCollectHideSticker
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.hide_stickers.v1.UnCollectHideStickerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
    /v2/items/story/untop_hide_sticker:
        post:
            tags:
                - Items
            description: 取消置顶 hide sticker
            operationId: Items_UnTopHideSticker
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/api.items.story.hide_stickers.v1.UnTopCollectedStickerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
components:
    schemas:
        api.artwork.ArtAsset:
            type: object
            properties:
                referenceId:
                    type: string
                type:
                    enum:
                        - ASSET_TYPE_UNSPECIFIED
                        - ASSET_TYPE_IMAGE
                        - ASSET_TYPE_VIDEO
                        - ASSET_TYPE_AUDIO
                        - ASSET_TYPE_TEXT
                        - ASSET_TYPE_CONTROL
                        - ASSET_TYPE_INTERACTIVE
                        - ASSET_TYPE_EMOJI
                        - ASSET_TYPE_LIVE_PHOTO
                    type: string
                    format: enum
                uri:
                    type: string
                layout:
                    $ref: '#/components/schemas/api.artwork.ArtLayout'
                decoration:
                    $ref: '#/components/schemas/api.artwork.ArtDecoration'
                transform:
                    $ref: '#/components/schemas/api.artwork.ArtTransform'
                posterUri:
                    type: string
                effects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.artwork.ArtEffect'
                userMention:
                    type: string
                text:
                    $ref: '#/components/schemas/api.artwork.ArtText'
                control:
                    $ref: '#/components/schemas/api.artwork.ArtControl'
                interactive:
                    $ref: '#/components/schemas/api.artwork.ArtInteractive'
        api.artwork.ArtControl:
            type: object
            properties:
                referenceId:
                    type: string
                controlType:
                    enum:
                        - CONTROL_TYPE_NONE
                        - CONTROL_TYPE_EMOJI_DRAG
                        - CONTROL_TYPE_PLAIN_BUTTON
                        - CONTROL_TYPE_BLUR
                        - CONTROL_TYPE_ERASE
                        - CONTROL_TYPE_PERSPECTIVE
                        - CONTROL_TYPE_AUDIO_MATCH
                    type: string
                    format: enum
                emojiDrag:
                    $ref: '#/components/schemas/api.artwork.ArtEmojiDrag'
                plainButton:
                    $ref: '#/components/schemas/api.artwork.ArtPlainButton'
                perspectiveCtrl:
                    $ref: '#/components/schemas/api.artwork.ArtPerspectiveControl'
                audioMatchText:
                    type: string
            description: 控制组件
        api.artwork.ArtDecoration:
            type: object
            properties:
                borderWidth:
                    type: number
                    format: float
                borderColor:
                    type: string
                cornerRadius:
                    type: number
                    format: float
                backgroundColor:
                    type: string
        api.artwork.ArtEffect:
            type: object
            properties:
                name:
                    type: string
                value:
                    type: string
            description: Effect
        api.artwork.ArtEmojiDrag:
            type: object
            properties:
                emojiDragType:
                    enum:
                        - EMOJI_TYPE_UNSPECIFIED
                        - EMOJI_DRAG_TYPE_TARGET
                        - EMOJI_DRAG_TYPE_TRIGGER
                    type: string
                    format: enum
                textList:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.artwork.ArtText'
                triggerTime:
                    type: number
                    format: float
                duration:
                    type: number
                    format: float
                triggerCount:
                    type: integer
                    format: int32
                startAt:
                    $ref: '#/components/schemas/api.artwork.ArtLayout'
                endAt:
                    $ref: '#/components/schemas/api.artwork.ArtLayout'
        api.artwork.ArtInteractive:
            type: object
            properties:
                triggerTime:
                    type: number
                    format: float
                duration:
                    type: number
                    format: float
                type:
                    enum:
                        - UNSPECIFIED
                        - STATIC
                        - DYNAMIC
                    type: string
                    format: enum
        api.artwork.ArtLayout:
            type: object
            properties:
                x:
                    type: number
                    format: float
                y:
                    type: number
                    format: float
                w:
                    type: number
                    format: float
                h:
                    type: number
                    format: float
        api.artwork.ArtPerspectiveControl:
            type: object
            properties:
                orientation:
                    type: integer
                    format: int32
                assetW:
                    type: integer
                    format: int32
                assetH:
                    type: integer
                    format: int32
            description: 图片视角控制
        api.artwork.ArtPlainButton:
            type: object
            properties:
                title:
                    type: string
                titleColor:
                    type: string
                bgColor:
                    type: string
                radius:
                    type: number
                    format: float
                borderWidth:
                    type: number
                    format: float
                layout:
                    $ref: '#/components/schemas/api.artwork.ArtLayout'
            description: Button
        api.artwork.ArtSketchBoard:
            type: object
            properties:
                referenceId:
                    type: string
                assets:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.artwork.ArtAsset'
                posterUri:
                    type: string
                direction:
                    enum:
                        - SEGUE_TYPE_UNSPECIFIED
                        - SEGUE_TYPE_SCROLL_H
                        - SEGUE_TYPE_SCROLL_V
                        - SEGUE_TYPE_CUSTOM
                    type: string
                    format: enum
        api.artwork.ArtText:
            type: object
            properties:
                text:
                    type: string
                fontName:
                    type: string
                fontSize:
                    type: string
                fontColor:
                    type: string
                alignment:
                    enum:
                        - TEXT_ALIGNMENT_UNSPECIFIED
                        - TEXT_ALIGNMENT_LEFT
                        - TEXT_ALIGNMENT_CENTER
                        - TEXT_ALIGNMENT_RIGHT
                    type: string
                    format: enum
            description: Text
        api.artwork.ArtTransform:
            type: object
            properties:
                scale:
                    type: number
                    format: float
                rotation:
                    type: number
                    format: float
        api.boo_world.capture.v1.AddBooCaptureRecordBooWithCaptorResourceRequest:
            type: object
            properties:
                booCaptureRecordId:
                    type: string
                booWithCaptorResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
            description: 给一个捕获记录添加合照
        api.boo_world.capture.v1.AddBooCaptureRecordBooWithCaptorResourceResponse:
            type: object
            properties: {}
        api.boo_world.capture.v1.ListMyBooCapturedBooRecordsByLocationRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
            description: 获取我的鬼被抓的记录
        api.boo_world.capture.v1.ListMyBooCapturedBooRecordsByLocationResponse:
            type: object
            properties:
                booCaptureRecords:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.types.v1.BooCaptureRecord'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.boo_world.capture.v1.ListMyCapturedBooRecordsRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
            description: 获取我抓到的鬼记录
        api.boo_world.capture.v1.ListMyCapturedBooRecordsResponse:
            type: object
            properties:
                booCaptureRecords:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.capture.v1.ListMyCapturedBooRecordsResponse_BooCaptureRecordWithDateStr'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.boo_world.capture.v1.ListMyCapturedBooRecordsResponse_BooCaptureRecordWithDateStr:
            type: object
            properties:
                booCaptureRecords:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.types.v1.BooCaptureRecord'
                dateStr:
                    type: string
                    description: '服务端会根据请求内的 Accept-Timezone 进行合理的分组 如果不传此值，则用 utc 时区 标准时间格式 example : 2026-01-01'
        api.boo_world.capture.v1.ReportBooCaptureRecordRequest:
            type: object
            properties:
                videoResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                capturedBooLocations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.capture.v1.ReportBooCaptureRecordRequest_CapturedBooLocation'
                capturedUserLocation:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
            description: 上报一个捕获记录
        api.boo_world.capture.v1.ReportBooCaptureRecordRequest_CapturedBooLocation:
            type: object
            properties:
                capturedBooLocation:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
                capturedBooId:
                    type: string
            description: 捕获时鬼所在的位置
        api.boo_world.capture.v1.ReportBooCaptureRecordResponse:
            type: object
            properties: {}
        api.boo_world.map.v1.GetBooWorldUserSummaryRequest:
            type: object
            properties: {}
        api.boo_world.map.v1.GetBooWorldUserSummaryResponse:
            type: object
            properties:
                latestUpdatedBooLocation:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
                capturedBooCount:
                    type: integer
                    description: 我抓到了多少个鬼
                    format: uint32
                capturedByCount:
                    type: integer
                    description: 我的鬼被多少人抓到
                    format: uint32
                latestPostedResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.resource.types.v1.ResourceWithRelation'
                    description: 我最近发布的资源
        api.boo_world.map.v1.GetNearbyBooRequest:
            type: object
            properties:
                location:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
                distance:
                    type: integer
                    description: 距离，单位米
                    format: uint32
            description: 人模式下获取周围的 boo
        api.boo_world.map.v1.GetNearbyBooResponse:
            type: object
            properties:
                boos:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.types.v1.BooWithLocation'
        api.boo_world.map.v1.GetNearbyUserAndResourceRequest:
            type: object
            properties:
                distance:
                    type: integer
                    description: 距离，单位米
                    format: uint32
                location:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
            description: 鬼模式下，获取周围的用户及资源
        api.boo_world.map.v1.GetNearbyUserAndResourceResponse:
            type: object
            properties:
                userAndResourceWithLocations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.types.v1.UserAndResourceWithLocation'
        api.boo_world.map.v1.MoveBooToLocationRequest:
            type: object
            properties:
                location:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
                userId:
                    type: string
        api.boo_world.map.v1.MoveBooToLocationResponse:
            type: object
            properties: {}
        api.boo_world.resource.types.v1.ResourceRelation:
            type: object
            properties:
                relationType:
                    enum:
                        - RESOURCE_RELATION_TYPE_UNSPECIFIED
                        - RESOURCE_RELATION_TYPE_LIKE
                    type: string
                    format: enum
                createdAt:
                    type: integer
                    format: uint32
        api.boo_world.resource.types.v1.ResourceWithRelation:
            type: object
            properties:
                id:
                    type: string
                    description: 这里的资源对象已经开始包含一些业务属性，不可以直接视为静态资源，所以设计了 id 字段
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                loginUserRelations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.resource.types.v1.ResourceRelation'
        api.boo_world.resource.v1.CreateResourceRelationRequest:
            type: object
            properties:
                resourceId:
                    type: string
                    description: id only number
                relationType:
                    enum:
                        - RESOURCE_RELATION_TYPE_UNSPECIFIED
                        - RESOURCE_RELATION_TYPE_LIKE
                    type: string
                    format: enum
        api.boo_world.resource.v1.CreateResourceRelationResponse:
            type: object
            properties:
                resource:
                    $ref: '#/components/schemas/api.boo_world.resource.types.v1.ResourceWithRelation'
        api.boo_world.resource.v1.CreateResourceRequest:
            type: object
            properties:
                staticResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
        api.boo_world.resource.v1.CreateResourceResponse:
            type: object
            properties:
                resource:
                    $ref: '#/components/schemas/api.boo_world.resource.types.v1.ResourceWithRelation'
        api.boo_world.resource.v1.RemoveResourceRelationRequest:
            type: object
            properties:
                resourceId:
                    type: string
                    description: id only number
                relationType:
                    enum:
                        - RESOURCE_RELATION_TYPE_UNSPECIFIED
                        - RESOURCE_RELATION_TYPE_LIKE
                    type: string
                    format: enum
        api.boo_world.resource.v1.RemoveResourceRelationResponse:
            type: object
            properties:
                resource:
                    $ref: '#/components/schemas/api.boo_world.resource.types.v1.ResourceWithRelation'
        api.boo_world.types.v1.BooCaptureRecord:
            type: object
            properties:
                id:
                    type: string
                captor:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                capturedBoo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.Boo'
                videoResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                booWithCaptorResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                createdAtUnixstamp:
                    type: integer
                    description: 捕获时间
                    format: uint32
                capturedBooLocation:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
                capturedUserLocation:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
            description: 抓鬼记录
        api.boo_world.types.v1.BooWithLocation:
            type: object
            properties:
                boo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.Boo'
                location:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
                creatorUserAndPostedResources:
                    $ref: '#/components/schemas/api.boo_world.types.v1.UserAndPostedResources'
        api.boo_world.types.v1.Location:
            type: object
            properties:
                latitude:
                    type: string
                longitude:
                    type: string
                address:
                    type: string
        api.boo_world.types.v1.UserAndPostedResources:
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.resource.types.v1.ResourceWithRelation'
                    description: 资源，时间倒叙，客户端暂时可以只取第一个
        api.boo_world.types.v1.UserAndResourceWithLocation:
            type: object
            properties:
                userAndPostedResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.boo_world.types.v1.UserAndPostedResources'
                location:
                    $ref: '#/components/schemas/api.boo_world.types.v1.Location'
        api.common.v1.ListRequest:
            type: object
            properties:
                pageToken:
                    type: string
                    description: 若为第一页，传空即可 否则，传上一页的 next_page_token
                pageSize:
                    type: string
        api.common.v1.ListResponse:
            type: object
            properties:
                hasMore:
                    type: boolean
                nextPageToken:
                    type: string
                    description: 若 has_more 为 true，则传下一页的 next_page_token 否则，此值无意义
        api.fizz.events.types.v1.FizzEvents:
            type: object
            properties:
                eventType:
                    enum:
                        - FIZZ_EVENT_TYPE_UNSPECIFIED
                        - FIZZ_EVENT_TYPE_MEDIA_ADDED
                        - FIZZ_EVENT_TYPE_USER_ADDED
                    type: string
                    description: 事件类型
                    format: enum
                id:
                    type: string
                    description: id
                mediaAddedEvent:
                    $ref: '#/components/schemas/api.fizz.events.types.v1.MediaAddedEvent'
                userAddedEvent:
                    $ref: '#/components/schemas/api.fizz.events.types.v1.UserAddedEvent'
        api.fizz.events.types.v1.MediaAddedEvent:
            type: object
            properties:
                media:
                    $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
                createdAtUnixstamp:
                    type: integer
                    description: 事件发生的时间
                    format: uint32
        api.fizz.events.types.v1.UserAddedEvent:
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                createdAtUnixstamp:
                    type: integer
                    description: 事件发生的时间
                    format: uint32
        api.fizz.events.v1.BatchListFizzEventsRequest:
            type: object
            properties:
                fizzEventsLists:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.events.v1.BatchListFizzEventsRequest_FizzEventsList'
        api.fizz.events.v1.BatchListFizzEventsRequest_FizzEventsList:
            type: object
            properties:
                fizzId:
                    type: string
                eventTypes:
                    type: array
                    items:
                        enum:
                            - FIZZ_EVENT_TYPE_UNSPECIFIED
                            - FIZZ_EVENT_TYPE_MEDIA_ADDED
                            - FIZZ_EVENT_TYPE_USER_ADDED
                        type: string
                        format: enum
                    description: 要获取的事件类型
                pageToken:
                    type: string
                    description: 分页参数，第一页传空，之后传上一次返回的 page_token
                pageSize:
                    type: integer
                    format: uint32
        api.fizz.events.v1.BatchListFizzEventsResponse:
            type: object
            properties:
                fizzEventsLists:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.events.v1.BatchListFizzEventsResponse_FizzEventsList'
        api.fizz.events.v1.BatchListFizzEventsResponse_FizzEventsList:
            type: object
            properties:
                fizzId:
                    type: string
                events:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.events.types.v1.FizzEvents'
                nextPageToken:
                    type: string
        api.fizz.feed.v1.ListFizzNearByRequest:
            type: object
            properties:
                location:
                    $ref: '#/components/schemas/api.fizz.types.v1.Location'
                distanceMeters:
                    type: integer
                    description: 距离，单位米
                    format: uint32
        api.fizz.feed.v1.ListFizzNearByResponse:
            type: object
            properties:
                fizzMinisWithLocation:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.feed.v1.ListFizzNearByResponse_FizzMiniWithLocation'
        api.fizz.feed.v1.ListFizzNearByResponse_FizzMiniWithLocation:
            type: object
            properties:
                fizzId:
                    type: string
                fizzCoverImageUrl:
                    type: string
                fizzName:
                    type: string
                deadAtUnixstamp:
                    type: integer
                    format: uint32
                location:
                    $ref: '#/components/schemas/api.fizz.types.v1.Location'
                distance:
                    type: integer
                    description: 距离，单位米
                    format: uint32
                joinRelation:
                    $ref: '#/components/schemas/api.fizz.user_relation.types.v1.FizzJoinRelation'
        api.fizz.feed.v1.ListRecommendFizzRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                filterFizzTypes:
                    type: array
                    items:
                        enum:
                            - FIZZ_TYPE_UNSPECIFIED
                            - FIZZ_TYPE_NORMAL
                            - FIZZ_TYPE_STIRUP
                            - FIZZ_TYPE_RELAY
                            - FIZZ_TYPE_PEEK
                        type: string
                        format: enum
                    description: 过滤型，目前如果不传，则走推荐引擎 临时供给给 @Yicheng 参数，方便只拉取老的 fizz type
        api.fizz.feed.v1.ListRecommendFizzResponse:
            type: object
            properties:
                fizzSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                sortRequestId:
                    type: string
        api.fizz.media.feed.v1.ListNewRecommendFizzMediasRequest:
            type: object
            properties:
                fizzId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.fizz.media.feed.v1.ListNewRecommendFizzMediasResponse:
            type: object
            properties:
                fizzMedias:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
                everyMediaAddDurationSeconds:
                    type: integer
                    description: 每个媒体为 fizz 增加的存续时间
                    format: uint32
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.fizz.media.feed.v1.ListRecommendFizzMediasRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                fizzId:
                    type: string
                filterRelationTypes:
                    type: array
                    items:
                        enum:
                            - FIZZ_MEDIA_USER_RELATION_TYPE_UNSPECIFIED
                            - FIZZ_MEDIA_USER_RELATION_TYPE_LIKE
                            - FIZZ_MEDIA_USER_RELATION_TYPE_VIEW
                        type: string
                        format: enum
                    description: 需要过滤的产生过的 relation 类型的列表，如果为空，则表示不过滤
        api.fizz.media.feed.v1.ListRecommendFizzMediasResponse:
            type: object
            properties:
                fizzMedias:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                sortRequestId:
                    type: string
        api.fizz.media.types.v1.FizzMedia:
            type: object
            properties:
                id:
                    type: string
                url:
                    type: string
                mediaResourceType:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    description: '资源类型 deprecated: 使用 resource 字段代替'
                    format: enum
                coverImageUrl:
                    type: string
                    description: 服务端会从 resources 选择合适的资源作为封面
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                stats:
                    $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMediaStats'
                userRelation:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMediaUserRelation'
                    description: 当前登录用户与media关系
                maxViewDuration:
                    type: integer
                    description: 该 fizz 一次查看的最大存续时间，单位秒
                    format: uint32
                remainingViewDuration:
                    type: integer
                    description: 该用户对该 fizz 的剩余浏览时长，单位秒
                    format: uint32
                createdAtUnixstamp:
                    type: integer
                    format: uint32
                updatedAtUnixstamp:
                    type: integer
                    format: uint32
                authorLikedLoginUserCreatedMedias:
                    type: boolean
                    description: media 的作者是否点赞过登录用户在此 media 所属 fizz 里的其他 media
                authorLikedLoginUserCreatedMediasRelationExtras:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.user_relation.v1.RelationExtra'
                    description: 点赞时，可能会携带外的信息，比如表情 当且仅当 author_liked_login_user_created_medias 为 true 时，relation_extra 才有效
        api.fizz.media.types.v1.FizzMediaDetail:
            type: object
            properties:
                summary:
                    $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
                likeUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMediaDetail_LikeUsers'
        api.fizz.media.types.v1.FizzMediaDetail_LikeUsers:
            type: object
            properties:
                userId:
                    type: string
                userName:
                    type: string
                userAvatarUrl:
                    type: string
        api.fizz.media.types.v1.FizzMediaStats:
            type: object
            properties:
                likeCount:
                    type: integer
                    format: uint32
                messageCount:
                    type: integer
                    description: 有多少人发过消息
                    format: uint32
                viewCount:
                    type: integer
                    description: 有多少人浏览过
                    format: uint32
        api.fizz.media.types.v1.FizzMediaUserRelation:
            type: object
            properties:
                relationType:
                    enum:
                        - FIZZ_MEDIA_USER_RELATION_TYPE_UNSPECIFIED
                        - FIZZ_MEDIA_USER_RELATION_TYPE_LIKE
                        - FIZZ_MEDIA_USER_RELATION_TYPE_VIEW
                    type: string
                    description: 关系类型
                    format: enum
                createdAtUnixstamp:
                    type: integer
                    description: 发生时间
                    format: uint32
                relationExtra:
                    $ref: '#/components/schemas/api.fizz.media.user_relation.v1.RelationExtra'
        api.fizz.media.user_relation.v1.CreateFizzMediaUserRelationRequest:
            type: object
            properties:
                mediaId:
                    type: string
                relationType:
                    enum:
                        - FIZZ_MEDIA_USER_RELATION_TYPE_UNSPECIFIED
                        - FIZZ_MEDIA_USER_RELATION_TYPE_LIKE
                        - FIZZ_MEDIA_USER_RELATION_TYPE_VIEW
                    type: string
                    format: enum
                relationExtra:
                    $ref: '#/components/schemas/api.fizz.media.user_relation.v1.RelationExtra'
        api.fizz.media.user_relation.v1.ListMediaRelatedUsersRequest:
            type: object
            properties:
                mediaId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                relationType:
                    enum:
                        - FIZZ_MEDIA_USER_RELATION_TYPE_UNSPECIFIED
                        - FIZZ_MEDIA_USER_RELATION_TYPE_LIKE
                        - FIZZ_MEDIA_USER_RELATION_TYPE_VIEW
                    type: string
                    format: enum
        api.fizz.media.user_relation.v1.ListMediaRelatedUsersResponse:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.fizz.media.user_relation.v1.ListSameFizzMediasRelationMadeEachOtherRequest:
            type: object
            properties:
                fizzId:
                    type: string
            description: 获取同 fizz 下，互相点赞过的用户
        api.fizz.media.user_relation.v1.ListSameFizzMediasRelationMadeEachOtherResponse:
            type: object
            properties:
                matchResults:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.user_relation.v1.ListSameFizzMediasRelationMadeEachOtherResponse_MatchResult'
        api.fizz.media.user_relation.v1.ListSameFizzMediasRelationMadeEachOtherResponse_MatchResult:
            type: object
            properties:
                matchedUser:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                matchedAtUnixTimestamp:
                    type: integer
                    format: uint32
            description: 考虑拓展性，这里额外包裹以下
        api.fizz.media.user_relation.v1.RelationExtra:
            type: object
            properties:
                likeEmoji:
                    type: string
        api.fizz.media.user_relation.v1.RemoveFizzMediaUserRelationRequest:
            type: object
            properties:
                mediaId:
                    type: string
                relationType:
                    enum:
                        - FIZZ_MEDIA_USER_RELATION_TYPE_UNSPECIFIED
                        - FIZZ_MEDIA_USER_RELATION_TYPE_LIKE
                        - FIZZ_MEDIA_USER_RELATION_TYPE_VIEW
                    type: string
                    format: enum
        api.fizz.media.user_relation.v1.SendMessageToFizzMediaRequest:
            type: object
            properties:
                mediaId:
                    type: string
                message:
                    type: string
        api.fizz.media.v1.AddMediaIntoFizzRequest:
            type: object
            properties:
                fizzId:
                    type: string
                mediaResourceType:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    description: 'deprecated: 使用 resources 字段代替'
                    format: enum
                objectKey:
                    type: string
                    description: 'deprecated: 使用 resources 字段代替'
                coverImageKey:
                    type: string
                    description: '如果是图片，则不需传这个值 视频必传 deprecated: 使用 resources 字段代替'
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                    description: 资源列表
        api.fizz.media.v1.AddMediaIntoFizzResponse:
            type: object
            properties:
                fizzMedia:
                    $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
        api.fizz.media.v1.GetFizzMediaDetailRequest:
            type: object
            properties:
                mediaId:
                    type: string
        api.fizz.media.v1.GetFizzMediaDetailResponse:
            type: object
            properties:
                fizzMediaDetail:
                    $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMediaDetail'
        api.fizz.media.v1.ListCreatedFizzMediasRequest:
            type: object
            properties:
                userId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.fizz.media.v1.ListCreatedFizzMediasResponse:
            type: object
            properties:
                createdMedias:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.v1.ListCreatedFizzMediasResponse_CreatedMedia'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.fizz.media.v1.ListCreatedFizzMediasResponse_CreatedMedia:
            type: object
            properties:
                media:
                    $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
                belongsToFizzSummary:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
        api.fizz.peek.types.v1.MatchResult:
            type: object
            properties:
                tryCount:
                    type: integer
                    description: 尝试了多少次
                    format: uint32
                tryResourceIds:
                    type: array
                    items:
                        type: string
                    description: 尝试过的资源 id
                failedCount:
                    type: integer
                    description: 失败次数
                    format: uint32
                maxFailedCount:
                    type: integer
                    description: 最大失败次数，客户端应该根据此值 和 失败次数 来决定是否允许用户继续尝试
                    format: uint32
                isMatched:
                    type: boolean
                    description: 登录用户是否已经成功把这个资源 match 了
        api.fizz.peek.types.v1.SubmitMatchingResource:
            type: object
            properties:
                id:
                    type: string
                    description: 资源 id，客户端创建时不需要填写，服务端会自动生成唯一 id
                forMatchingResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                matchingResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                createdAtUnixstamp:
                    type: integer
                    description: 创建时间，标准时间戳
                    format: uint32
        api.fizz.peek.types.v1.SubmitMatchingResourceWithResult:
            type: object
            properties:
                submitMatchingResource:
                    $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitMatchingResource'
                matchResult:
                    $ref: '#/components/schemas/api.fizz.peek.types.v1.MatchResult'
        api.fizz.peek.types.v1.SubmitPeekMatchingJob:
            type: object
            properties:
                jobId:
                    type: string
                matchFizzJobStatus:
                    enum:
                        - MATCH_FIZZ_JOB_STATUS_UNSPECIFIED
                        - MATCH_FIZZ_JOB_STATUS_CANCELED
                        - MATCH_FIZZ_JOB_STATUS_FAILED
                        - MATCH_FIZZ_JOB_STATUS_MATCHED
                        - MATCH_FIZZ_JOB_STATUS_MATCHING
                    type: string
                    format: enum
                SubmitMatchingResource:
                    $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitMatchingResource'
                submittedAtUnixstamp:
                    type: integer
                    description: 提交时间，标准时间戳
                    format: uint32
                matchedFizzs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
                    description: 匹配到的 fizz id，如果为空，则表示未匹配到 这里是数组，但目前只可能至多匹配一个 fizz，留成数组是为了将来的扩展
        api.fizz.peek.v1.BatchGetPeekMatchingJobRequest:
            type: object
            properties:
                jobIds:
                    type: array
                    items:
                        type: string
        api.fizz.peek.v1.BatchGetPeekMatchingJobResponse:
            type: object
            properties:
                submitPeekMatchingJobs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitPeekMatchingJob'
        api.fizz.peek.v1.CancelPeekMatchingJobsRequest:
            type: object
            properties:
                jobIds:
                    type: array
                    items:
                        type: string
        api.fizz.peek.v1.GetPeekFizzMatchResourceListRequest:
            type: object
            properties:
                fizzId:
                    type: string
            description: 获取 peek fizz 的 match resource list
        api.fizz.peek.v1.GetPeekFizzMatchResourceListResponse:
            type: object
            properties:
                matchResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitMatchingResourceWithResult'
        api.fizz.peek.v1.ListPeekMatchingJobsRequest:
            type: object
            properties: {}
        api.fizz.peek.v1.ListPeekMatchingJobsResponse:
            type: object
            properties:
                submitPeekMatchingJobs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitPeekMatchingJob'
        api.fizz.peek.v1.SubmitPeekMatchRequest:
            type: object
            properties:
                fizzId:
                    type: string
                matchingResourceId:
                    type: string
                    description: 选项资源 id
                targetResourceId:
                    type: string
                    description: 目标资源 id
            description: 提交匹配 实际上，matching_resource_id 如果等于 target_resource_id，则直接匹配成功了， 客户端可以自行判断结果 此接口会返回房间内的最新状态，用于重绘 UI
        api.fizz.peek.v1.SubmitPeekMatchResponse:
            type: object
            properties:
                matchResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitMatchingResourceWithResult'
        api.fizz.peek.v1.SubmitPeekMatchingJobRequest:
            type: object
            properties:
                forMatchingResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                matchingResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                botAutoCount:
                    type: integer
                    format: uint32
        api.fizz.peek.v1.SubmitPeekMatchingJobResponse:
            type: object
            properties:
                submitPeekMatchingJob:
                    $ref: '#/components/schemas/api.fizz.peek.types.v1.SubmitPeekMatchingJob'
        api.fizz.relay.types.v1.PlayRoom:
            type: object
            properties:
                id:
                    type: string
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                userWithShotResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.relay.types.v1.UserWithShotResource'
                    description: 房间内的所有用户，及他们的上下文
                status:
                    enum:
                        - PLAY_ROOM_STATUS_UNSPECIFIED
                        - PLAY_ROOM_STATUS_WAITING
                        - PLAY_ROOM_STATUS_PLAYING
                        - PLAY_ROOM_STATUS_IN_TRANSTION
                        - PLAY_ROOM_STATUS_FINISHED
                    type: string
                    format: enum
                playRoomStat:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoomStat'
                currentResourceWithCaption:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.ResourceWithCaption'
                loginUserShotResource:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.ResourceWithCaption'
                nextStatusRemainSeconds:
                    type: integer
                    description: 距离下一个状态变更的剩余秒数 目前仅当 room status 为 PLAY_ROOM_STATUS_PLAYING 或者 PLAY_ROOM_STATUS_IN_TRANSTION 有效
                    format: uint32
                rtmChannelId:
                    type: string
                    description: 房间的 rtm 频道 id，客户端需要订阅此频道，才能收到对应的消息
                rtcChannelId:
                    type: string
                    description: 房间的 rtc 频道 id，客户端需要加入此频道，才能进行通话
                maxJoinedUserCount:
                    type: integer
                    format: uint32
                minBeginToPlayUserCount:
                    type: integer
                    format: uint32
        api.fizz.relay.types.v1.PlayRoomStat:
            type: object
            properties:
                playingUserCount:
                    type: integer
                    description: 游玩中人数
                    format: uint32
                waitingUserCount:
                    type: integer
                    description: 等待中人数（未提交素材，或者没有提交 ready)
                    format: uint32
                readyUserCount:
                    type: integer
                    description: ready 人数
                    format: uint32
        api.fizz.relay.types.v1.ResourceForUnlock:
            type: object
            properties:
                creatorId:
                    type: string
                resource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                targetResourceId:
                    type: string
                submittedAtUnixstamp:
                    type: integer
                    format: uint32
                successUnlocked:
                    type: boolean
        api.fizz.relay.types.v1.ResourceWithCaption:
            type: object
            properties:
                id:
                    type: string
                    description: 创建的时候不需要填，服务端自己下发
                resource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                caption:
                    type: string
                loginUserUnlocked:
                    type: boolean
                    description: 登录用户是否解锁了此资源，自己的资源永远为 true 创建时不需要填此值
                userSubmmitedResource:
                    type: boolean
                    description: 用户时候是否已经点击了准备好了
                resourcesForUnlocks:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.relay.types.v1.ResourceForUnlock'
        api.fizz.relay.types.v1.UserWithShotResource:
            type: object
            properties:
                userInfo:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                shotResource:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.ResourceWithCaption'
                status:
                    enum:
                        - USER_STATUS_UNSPECIFIED
                        - USER_STATUS_WAITING
                        - USER_STATUS_WAITING_FOR_READY
                        - USER_STATUS_READY_TO_PLAY
                        - USER_STATUS_PLAYING
                        - USER_STATUS_VIEWING
                        - USER_STATUS_CAPTURING
                        - USER_STATUS_UNLOCK_FAILED
                        - USER_STATUS_UNLOCK_SUCCESS
                        - USER_STATUS_LEFT_FOR_TIMEOUT
                        - USER_STATUS_LEFT
                        - USER_STATUS_FINISHED
                    type: string
                    format: enum
        api.fizz.relay.v1.BeginToCapturingResourceForUnlockRequest:
            type: object
            properties:
                playRoomId:
                    type: string
            description: 开始拍摄待解锁素材，改变用户的状态为 shooting
        api.fizz.relay.v1.BeginToCapturingResourceForUnlockResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
        api.fizz.relay.v1.CreatePlayRoomRequest:
            type: object
            properties:
                minUserCount:
                    type: integer
                    description: 最少几个人 ready 后就能开始游戏
                    format: uint32
            description: 创建一个等待房间
        api.fizz.relay.v1.CreatePlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
                rtcToken:
                    type: string
        api.fizz.relay.v1.CreateShotResourceRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                shotResource:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.ResourceWithCaption'
            description: 在房间里拍摄一个素材
        api.fizz.relay.v1.CreateShotResourceResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
        api.fizz.relay.v1.GetPlayRoomDetailRequest:
            type: object
            properties:
                playRoomId:
                    type: string
        api.fizz.relay.v1.GetPlayRoomDetailResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
        api.fizz.relay.v1.GetPlayRoomRTCTokenRequest:
            type: object
            properties:
                playRoomId:
                    type: string
        api.fizz.relay.v1.GetPlayRoomRTCTokenResponse:
            type: object
            properties:
                rtcToken:
                    type: string
        api.fizz.relay.v1.JoinPlayRoomRequest:
            type: object
            properties:
                playRoomId:
                    type: string
            description: 加入一个等待房间
        api.fizz.relay.v1.JoinPlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
                rtcToken:
                    type: string
        api.fizz.relay.v1.SharePlayRoomRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                userIds:
                    type: array
                    items:
                        type: string
            description: 把房间分享给其他用户，目前仅处于 waiting 状态的房间可以分享
        api.fizz.relay.v1.SharePlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
        api.fizz.relay.v1.SubmitReadyRequest:
            type: object
            properties:
                playRoomId:
                    type: string
            description: 拍摄待解锁素材后，提交 ready to play 状态
        api.fizz.relay.v1.SubmitReadyResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
        api.fizz.relay.v1.TryUnlockResourceRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                shotResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
            description: 尝试拍摄一个素材进行解锁当前资源
        api.fizz.relay.v1.TryUnlockResourceResponse:
            type: object
            properties:
                isSuccess:
                    type: boolean
                playRoom:
                    $ref: '#/components/schemas/api.fizz.relay.types.v1.PlayRoom'
        api.fizz.resource.types.v1.Resource:
            type: object
            properties:
                type:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    format: enum
                resourceUrl:
                    type: string
                    description: 作为上传资源时，此字段可以不填
                resourceKey:
                    type: string
                    description: 作为上传资源时，此字段必填
                videoCoverImageUrl:
                    type: string
                    description: 视频封面图片url，如果type为RESOURCE_TYPE_VIDEO，会返回  video_cover_image_object_key 的 cdn 地址
                videoCoverImageObjectKey:
                    type: string
                    description: 视频封面图片object key，如果type为RESOURCE_TYPE_VIDEO，上传时必须设置
                caption:
                    type: string
                    description: 资源描述
                shootingModel:
                    enum:
                        - SHOOTING_MODEL_UNSPECIFIED
                        - SHOOTING_MODEL_DUO_CAMERA
                        - SHOOTING_MODEL_FRONT_CAMERA
                        - SHOOTING_MODEL_BACK_CAMERA
                        - SHOOTING_MODEL_PANO
                        - SHOOTING_MODEL_TIME_LAPSE
                        - SHOOTING_MODEL_SLOW_MOTION
                        - SHOOTING_MODEL_CINEMATIC
                        - SHOOTING_MODEL_WALK_AND_SHOOT
                        - SHOOTING_MODEL_TALK_TO_CAMERA
                        - SHOOTING_MODEL_CAPTION
                    type: string
                    description: 拍摄模式
                    format: enum
        api.fizz.rush.types.v1.Bomb:
            type: object
            properties:
                holderId:
                    type: string
                    description: 炸弹目前在谁的手上
                trajectoryUserIds:
                    type: array
                    items:
                        type: string
                    description: 炸弹的移动轨迹 user_ids ，如果游戏结束，则最后一个人作为 Bomber
        api.fizz.rush.types.v1.CaptureResourceForQuestion:
            type: object
            properties:
                id:
                    type: string
                resource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                userEmojiRelations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.rush.types.v1.CaptureResourceForQuestion_UserEmojiRelation'
        api.fizz.rush.types.v1.CaptureResourceForQuestion_UserEmojiRelation:
            type: object
            properties:
                userId:
                    type: string
                emoji:
                    type: string
        api.fizz.rush.types.v1.JoinedUserWithQuestion:
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                status:
                    enum:
                        - USER_STATUS_UNSPECIFIED
                        - USER_STATUS_WAITING
                        - USER_STATUS_WAITING_FOR_READY
                        - USER_STATUS_READY_TO_PLAY
                        - USER_STATUS_PLAYING
                        - USER_STATUS_WAITING_FOR_BOMBER_CAPTURE
                        - USER_STATUS_BOMBER_CAPTURING
                        - USER_STATUS_VIEWING
                        - USER_STATUS_LEFT_FOR_TIMEOUT
                        - USER_STATUS_LEFT
                        - USER_STATUS_FINISHED
                    type: string
                    format: enum
                question:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.Question'
        api.fizz.rush.types.v1.PlayRoom:
            type: object
            properties:
                id:
                    type: string
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                joinedUsersWithQuestions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.rush.types.v1.JoinedUserWithQuestion'
                    description: 房间内的所有用户，及他们的上下文
                status:
                    enum:
                        - PLAY_ROOM_STATUS_UNSPECIFIED
                        - PLAY_ROOM_STATUS_WAITING
                        - PLAY_ROOM_STATUS_PLAYING
                        - PLAY_ROOM_STATUS_WAITTING_BOMBER_CAPTURE
                        - PLAY_ROOM_STATUS_VIEWING
                        - PLAY_ROOM_STATUS_FINISHED
                    type: string
                    format: enum
                playRoomStat:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoomStat'
                currentQuestion:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.Question'
                nextStatusRemainSeconds:
                    type: integer
                    description: 距离下一个状态变更的剩余秒数 目前仅当 room status 为  PLAY_ROOM_STATUS_PLAYING // 抛炸弹中 .. PLAY_ROOM_STATUS_WAITTING_BOMBER_CAPTURE // 等待被炸的人拍摄素材中...  PLAY_ROOM_STATUS_VIEWING // 观看中... 有效
                    format: uint32
                bomb:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.Bomb'
                rtmChannelId:
                    type: string
                    description: 房间的 rtm 频道 id，客户端需要订阅此频道，才能收到对应的消息
                rtcChannelId:
                    type: string
                    description: 房间的 rtc 频道 id，客户端需要加入此频道，才能进行通话
                maxJoinedUserCount:
                    type: integer
                    format: uint32
                minBeginToPlayUserCount:
                    type: integer
                    format: uint32
        api.fizz.rush.types.v1.PlayRoomStat:
            type: object
            properties:
                playingUserCount:
                    type: integer
                    description: 游玩中人数
                    format: uint32
                waitingUserCount:
                    type: integer
                    description: 等待中人数（未提交问题，或者没有提交 ready)
                    format: uint32
                readyUserCount:
                    type: integer
                    description: ready 人数
                    format: uint32
                playedRoundCount:
                    type: integer
                    description: 已经进行的轮次
                    format: uint32
        api.fizz.rush.types.v1.Question:
            type: object
            properties:
                id:
                    type: string
                content:
                    type: string
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                captureResource:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.CaptureResourceForQuestion'
        api.fizz.rush.v1.AddEmojiToQuestionResourceRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                questionResourceId:
                    type: string
                emoji:
                    type: string
            description: 给问题对应的素材点 emoji
        api.fizz.rush.v1.AddEmojiToQuestionResourceResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.rush.v1.CreateRushPlayRoomRequest:
            type: object
            properties:
                maxUserCount:
                    type: integer
                    description: 如果不传 默认 10
                    format: uint32
                minUserCount:
                    type: integer
                    description: 如果不传 默认 6
                    format: uint32
            description: 创建一个 rush 游戏房间
        api.fizz.rush.v1.CreateRushPlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
                rtcToken:
                    type: string
        api.fizz.rush.v1.GetRushPlayRoomRTCTokenRequest:
            type: object
            properties:
                playRoomId:
                    type: string
        api.fizz.rush.v1.GetRushPlayRoomRTCTokenResponse:
            type: object
            properties:
                rtcToken:
                    type: string
        api.fizz.rush.v1.GetRushPlayRoomRequest:
            type: object
            properties:
                playRoomId:
                    type: string
            description: 获取一个游戏房间
        api.fizz.rush.v1.GetRushPlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.rush.v1.JoinRushPlayRoomRequest:
            type: object
            properties:
                playRoomId:
                    type: string
            description: 加入一个 rush 游戏房间
        api.fizz.rush.v1.JoinRushPlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
                rtcToken:
                    type: string
        api.fizz.rush.v1.ShareRushPlayRoomRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                userIds:
                    type: array
                    items:
                        type: string
            description: share 一个 rush 游戏房间
        api.fizz.rush.v1.ShareRushPlayRoomResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.rush.v1.SubmitQuestionReadyRequest:
            type: object
            properties:
                playRoomId:
                    type: string
            description: 提交问题准备
        api.fizz.rush.v1.SubmitQuestionReadyResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.rush.v1.SubmitQuestionRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                questionContent:
                    type: string
            description: 提交问题
        api.fizz.rush.v1.SubmitQuestionResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.rush.v1.TakeQuestionMaterialResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.rush.v1.TakeQuestionResourceRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                questionResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
            description: 拍摄问题对应的素材
        api.fizz.rush.v1.TransferBombRequest:
            type: object
            properties:
                playRoomId:
                    type: string
                userId:
                    type: string
                    description: 把炸弹转移到谁身上，如果不传，服务端随机
            description: 转移炸弹
        api.fizz.rush.v1.TransferBombResponse:
            type: object
            properties:
                playRoom:
                    $ref: '#/components/schemas/api.fizz.rush.types.v1.PlayRoom'
        api.fizz.search.v1.SearchFizzRequest:
            type: object
            properties:
                fizzDescription:
                    type: string
                exampleImageKey:
                    type: string
        api.fizz.search.v1.SearchFizzResponse:
            type: object
            properties:
                fizzSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
        api.fizz.types.v1.FizzConfig:
            type: object
            properties:
                joinCondition:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzJoinCondition'
                exampleResource:
                    $ref: '#/components/schemas/api.fizz.resource.types.v1.Resource'
                onlyMyFriendsVisible:
                    type: boolean
                    description: 是否仅我的朋友可见
                maxUserJoinLimitCount:
                    type: integer
                    description: 最大限制人数
                    format: uint32
        api.fizz.types.v1.FizzDetail:
            type: object
            properties:
                summary:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
        api.fizz.types.v1.FizzJoinCondition:
            type: object
            properties:
                question:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzJoinCondition_Question'
                distanceMeters:
                    type: integer
                    description: 距离限制，单位米，如果为空，则表示没有此条件
                    format: uint32
        api.fizz.types.v1.FizzJoinCondition_Question:
            type: object
            properties:
                question:
                    type: string
                answerPrompt:
                    type: string
        api.fizz.types.v1.FizzStats:
            type: object
            properties:
                joinCount:
                    type: integer
                    description: 加入人数
                    format: uint32
                photosCount:
                    type: integer
                    description: photos 数量
                    format: uint32
                videosCount:
                    type: integer
                    description: videos 数量
                    format: uint32
                messagesCount:
                    type: integer
                    description: 所有 media 的消息数量
                    format: uint32
        api.fizz.types.v1.FizzSummary:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                coverImageUrl:
                    type: string
                description:
                    type: string
                fizzType:
                    enum:
                        - FIZZ_TYPE_UNSPECIFIED
                        - FIZZ_TYPE_NORMAL
                        - FIZZ_TYPE_STIRUP
                        - FIZZ_TYPE_RELAY
                        - FIZZ_TYPE_PEEK
                    type: string
                    description: 类型
                    format: enum
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                remainingDurationSeconds:
                    type: integer
                    description: 剩余的时长，单位秒
                    format: uint32
                tags:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.types.v1.Tag'
                    description: 标签
                location:
                    $ref: '#/components/schemas/api.fizz.types.v1.Location'
                config:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzConfig'
                stats:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzStats'
                joinRelation:
                    $ref: '#/components/schemas/api.fizz.user_relation.types.v1.FizzJoinRelation'
                createdAtUnixstamp:
                    type: integer
                    format: uint32
                updatedAtUnixstamp:
                    type: integer
                    format: uint32
        api.fizz.types.v1.Location:
            type: object
            properties:
                latitude:
                    type: string
                    description: 经纬度
                longitude:
                    type: string
                address:
                    type: string
                    description: 地址
        api.fizz.types.v1.Tag:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
        api.fizz.user_relation.types.v1.FizzJoinRelation:
            type: object
            properties:
                joinAtUnixstamp:
                    type: integer
                    description: 如果为空，表示未加入
                    format: uint32
                checkResult:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.user_relation.types.v1.JoinFizzConditionCheckResult'
                    description: 最近的检查结果
        api.fizz.user_relation.types.v1.JoinFizzConditionCheckResult:
            type: object
            properties:
                checkType:
                    enum:
                        - CHECK_TYPE_UNSPECIFIED
                        - CHECK_TYPE_LOCATION
                        - CHECK_TYPE_PHOTO
                        - CHECK_TYPE_ANSWER
                    type: string
                    format: enum
                isSatisfied:
                    type: boolean
        api.fizz.user_relation.v1.GetJoinFizzConditionCheckCacheRequest:
            type: object
            properties:
                fizzId:
                    type: string
        api.fizz.user_relation.v1.JoinFizzConditionCheckRequest:
            type: object
            properties:
                fizzId:
                    type: string
                location:
                    $ref: '#/components/schemas/api.fizz.types.v1.Location'
                photoKey:
                    type: string
                    description: 用户想要加入的 fizz 时提交的照片
                answer:
                    type: string
                    description: 用户想要加入的 fizz 时提交的答案
        api.fizz.user_relation.v1.JoinFizzConditionCheckResponse:
            type: object
            properties:
                locationIsSatisfied:
                    type: boolean
                    description: deprecated 使用 check_results 字段 位置是否满足
                photoIsSatisfied:
                    type: boolean
                    description: deprecated 使用 check_results 字段 照片是否满足
                answerIsSatisfied:
                    type: boolean
                    description: deprecated 使用 check_results 字段 答案是否满足
                checkResults:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.user_relation.types.v1.JoinFizzConditionCheckResult'
                    description: 检查结果
            description: 检查用户是否满足加入 fizz 的条件，如果没有条件限制，则不返回对应字段
        api.fizz.user_relation.v1.JoinFizzRequest:
            type: object
            properties:
                fizzId:
                    type: string
                resource:
                    $ref: '#/components/schemas/api.fizz.user_relation.v1.JoinFizzRequest_Resource'
        api.fizz.user_relation.v1.JoinFizzRequest_Resource:
            type: object
            properties:
                type:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    format: enum
                resourceKey:
                    type: string
                videoCoverImageKey:
                    type: string
        api.fizz.v1.CreateFizzRequest:
            type: object
            properties:
                name:
                    type: string
                description:
                    type: string
                    description: 描述
                tagNames:
                    type: array
                    items:
                        type: string
                    description: 标签，可选
                exampleResource:
                    $ref: '#/components/schemas/api.fizz.v1.CreateFizzRequest_ExampleResource'
                onlyMyFriendsVisible:
                    type: boolean
                    description: 是否仅我的朋友可见
                maxUserJoinLimitCount:
                    type: integer
                    description: 最大限制人数
                    format: uint32
                location:
                    $ref: '#/components/schemas/api.fizz.types.v1.Location'
                joinCondition:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzJoinCondition'
                firstResource:
                    $ref: '#/components/schemas/api.fizz.v1.CreateFizzRequest_Resource'
                durationSeconds:
                    type: integer
                    description: 时长，单位秒，默认 24hr
                    format: int64
        api.fizz.v1.CreateFizzRequest_ExampleResource:
            type: object
            properties:
                type:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    description: 资源类型
                    format: enum
                resourceKey:
                    type: string
                    description: 资源 key 上传时必须传
                videoCoverImageKey:
                    type: string
                    description: 视频封面图片 key
                caption:
                    type: string
            description: 示例资源，用于展示
        api.fizz.v1.CreateFizzRequest_Resource:
            type: object
            properties:
                type:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    format: enum
                resourceKey:
                    type: string
                videoCoverImageKey:
                    type: string
            description: 第一张资源的key
        api.fizz.v1.CreateFizzResponse:
            type: object
            properties:
                fizz:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzDetail'
        api.fizz.v1.GenerateExampleResourceCaptionRequest:
            type: object
            properties:
                description:
                    type: string
                exampleResource:
                    $ref: '#/components/schemas/api.fizz.v1.GenerateExampleResourceCaptionRequest_Resource'
        api.fizz.v1.GenerateExampleResourceCaptionRequest_Resource:
            type: object
            properties:
                type:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                    type: string
                    format: enum
                resourceKey:
                    type: string
                videoCoverImageKey:
                    type: string
            description: 第一张资源的key
        api.fizz.v1.GenerateExampleResourceCaptionResponse:
            type: object
            properties:
                caption:
                    type: string
        api.fizz.v1.GetFizzDetailRequest:
            type: object
            properties:
                fizzId:
                    type: string
                lastGetTimestamp:
                    type: integer
                    description: 最后获取时间戳，单位秒，可选，如果传了，则只返回在此时间戳之后数据 example details's recently_joined_users and recently_published_medias ...
                    format: uint32
        api.fizz.v1.GetFizzDetailResponse:
            type: object
            properties:
                fizz:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzDetail'
        api.fizz.v1.ListUserCreatedFizzRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                userId:
                    type: string
                    description: 用户id，可选，如果不传或者传0，则用登录用户的 session
        api.fizz.v1.ListUserCreatedFizzResponse:
            type: object
            properties:
                createdFizzs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.v1.ListUserCreatedFizzResponse_CreatedFizz'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.fizz.v1.ListUserCreatedFizzResponse_CreatedFizz:
            type: object
            properties:
                fizz:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
                isTop:
                    type: boolean
                    description: 是否置顶
        api.fizz.v1.TopFizzRequest:
            type: object
            properties:
                fizzId:
                    type: string
                isTop:
                    type: boolean
                    description: 置顶 or 取消置顶，置顶后会展示在用户创建的 fizz 列表最上方，最多可置顶三个，最后的会被移除
        api.fizz.vibe.types.v1.Item:
            type: object
            properties:
                id:
                    type: string
                creator:
                    $ref: '#/components/schemas/api.fizz.vibe.types.v1.ItemCreator'
                stat:
                    $ref: '#/components/schemas/api.fizz.vibe.types.v1.ItemStat'
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.resource.types.v1.Resource'
                loginUserMadeRelations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.types.v1.ItemUserRelation'
                expiredAtUnixstamp:
                    type: integer
                    format: uint32
                createdAtUnixstamp:
                    type: integer
                    format: uint32
                updatedAtUnixstamp:
                    type: integer
                    format: uint32
        api.fizz.vibe.types.v1.ItemCreator:
            type: object
            properties:
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                likedSelfPostedItem:
                    type: boolean
        api.fizz.vibe.types.v1.ItemStat:
            type: object
            properties:
                totalLikesCount:
                    type: integer
                    format: uint32
                totalDislikesCount:
                    type: integer
                    format: uint32
        api.fizz.vibe.types.v1.ItemUserRelation:
            type: object
            properties:
                relationType:
                    enum:
                        - ITEM_USER_RELATION_TYPE_UNSPECIFIED
                        - ITEM_USER_RELATION_TYPE_LIKED
                        - ITEM_USER_RELATION_TYPE_DISLIKED
                    type: string
                    format: enum
                createdAtUnixstamp:
                    type: integer
                    format: uint32
        api.fizz.vibe.v1.CreateVibeItemRequest:
            type: object
            properties:
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.resource.types.v1.Resource'
        api.fizz.vibe.v1.CreateVibeItemResponse:
            type: object
            properties:
                item:
                    $ref: '#/components/schemas/api.fizz.vibe.types.v1.Item'
        api.fizz.vibe.v1.GetRecivedLikeSummaryRequest:
            type: object
            properties: {}
        api.fizz.vibe.v1.GetRecivedLikeSummaryResponse:
            type: object
            properties:
                unreadLikeSummary:
                    type: object
                    additionalProperties:
                        type: integer
                        format: uint32
                    description: 最近未读的所有点赞数量汇总 key 是 item_id
                totalUnreadLikeCount:
                    type: integer
                    format: uint32
                createdItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.types.v1.Item'
        api.fizz.vibe.v1.GetRecommendUsersAndItemsRequest:
            type: object
            properties: {}
        api.fizz.vibe.v1.GetRecommendUsersAndItemsResponse:
            type: object
            properties:
                recommendedUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                recommendedItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.types.v1.Item'
        api.fizz.vibe.v1.ListReceivedLikesRequest:
            type: object
            properties:
                listReceivedLikes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.v1.ListReceivedLikesRequest_ListReceivedLikesSubRequest'
        api.fizz.vibe.v1.ListReceivedLikesRequest_ListReceivedLikesSubRequest:
            type: object
            properties:
                itemId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.fizz.vibe.v1.ListReceivedLikesResponse:
            type: object
            properties:
                listReceivedLikes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.v1.ListReceivedLikesResponse_ListReceivedLikesSubResponse'
        api.fizz.vibe.v1.ListReceivedLikesResponse_ListReceivedLikesSubResponse:
            type: object
            properties:
                userPostedItem:
                    $ref: '#/components/schemas/api.fizz.vibe.types.v1.Item'
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.types.v1.Item'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.fizz.vibe.v1.ListVibeItemsRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.fizz.vibe.v1.ListVibeItemsResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.vibe.types.v1.Item'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.fizz.vibe.v1.MarkRecivedLikesCreatorPostItemsAsReadRequest:
            type: object
            properties:
                itemIds:
                    type: array
                    items:
                        type: string
        api.fizz.vibe.v1.ReportUserLikeActionRequest:
            type: object
            properties:
                isLike:
                    type: boolean
                itemId:
                    type: string
        api.im.message.types.v1.BooInteractionCustomMessagePayload:
            type: object
            properties:
                messageType:
                    type: string
                    description: 负载类型，参考 MessageType
                videoPayload:
                    $ref: '#/components/schemas/api.im.message.types.v1.BooInteractionCustomMessagePayload_VideoPayload'
                booAvatarUrl:
                    type: string
                    description: 附带的鬼的头像
        api.im.message.types.v1.BooInteractionCustomMessagePayload_VideoPayload:
            type: object
            properties:
                videoUrl:
                    type: string
                    description: 视频链接
                coverUrl:
                    type: string
                    description: 视频封面链接
        api.im.message.types.v1.CustomMessagePayload:
            type: object
            properties:
                storyExchangeImageInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryExchangeImageInteractionCustomMessagePayload'
                storyTurtleSoupInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload'
                storyFizzInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryFizzCustomMessagePayload'
                storyNowShotInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryNowShotCustomMessagePayload'
                storyUnmuteInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryUnmuteCustomMessagePayload'
                storyWassupInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryWassupCustomMessagePayload'
                relayFizzJoinInvite:
                    $ref: '#/components/schemas/api.im.message.types.v1.RelayFizzJoinInviteCustomMessagePayload'
                storyCapsuleInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryCapsuleCustomMessagePayload'
                rushFizzJoinInvite:
                    $ref: '#/components/schemas/api.im.message.types.v1.RushFizzJoinInviteCustomMessagePayload'
                booInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.BooInteractionCustomMessagePayload'
                storyRoastedInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryRoastedCustomMessagePayload'
                storyChatproxyInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryChatProxyCustomMessagePayload'
                storyWhoInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryWhoInteractionCustomMessagePayload'
                storyHauntInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryHauntCustomMessagePayload'
                storyBaseplayInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryBaseplayCustomMessagePayload'
                storyHideStickerUnlocked:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryHideStickerUnlockedCustomMessagePayload'
                storyShareCreateMoment:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload'
                storyMomentQuote:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryMomentQuoteCustomMessagePayload'
                storyPinInteraction:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryPinInteractionCustomMessagePayload'
                customMessageType:
                    type: string
                    description: 参考 CustomMessageType 在 agora sdk 内，收到消息后，最终会被转为 customEvents
                consumeStatus:
                    type: string
                    description: 参考 ConsumeStatus
        api.im.message.types.v1.Message:
            type: object
            properties:
                messageType:
                    enum:
                        - MESSAGE_TYPE_UNSPECIFIED
                        - MESSAGE_TYPE_TEXT
                        - MESSAGE_TYPE_IMAGE
                        - MESSAGE_TYPE_AUDIO
                        - MESSAGE_TYPE_VIDEO
                        - MESSAGE_TYPE_FILE
                        - MESSAGE_TYPE_LOCATION
                        - MESSAGE_TYPE_CUSTOM
                    type: string
                    format: enum
                body:
                    $ref: '#/components/schemas/api.im.message.types.v1.MessageBody'
        api.im.message.types.v1.MessageBody:
            type: object
            properties:
                content:
                    type: string
                    description: 当且仅当 message_type 为 TEXT 时有效
                customMessagePayload:
                    $ref: '#/components/schemas/api.im.message.types.v1.CustomMessagePayload'
        api.im.message.types.v1.RelayFizzJoinInviteCustomMessagePayload:
            type: object
            properties:
                roomId:
                    type: string
                creatorId:
                    type: string
                expireAtUnixstamp:
                    type: integer
                    description: 暂时没用，先留着
                    format: uint32
        api.im.message.types.v1.RushFizzJoinInviteCustomMessagePayload:
            type: object
            properties:
                roomId:
                    type: string
                creatorId:
                    type: string
                expireAtUnixstamp:
                    type: integer
                    description: 暂时没用，先留着
                    format: uint32
        api.im.message.types.v1.StoryBaseplayCustomMessagePayload:
            type: object
            properties:
                storyId:
                    type: string
                storyCoverUrl:
                    type: string
                consumerStoryCoverUrl:
                    type: string
                title:
                    type: string
        api.im.message.types.v1.StoryCapsuleCustomMessagePayload:
            type: object
            properties:
                storyId:
                    type: string
                    description: 消费者交互的 story id
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
                consumerImageUrl:
                    type: string
                    description: 消费者发送的图片
                title:
                    type: string
                    description: 当前消息的 title 比如 the other party replied to your story
                inDays:
                    type: integer
                    description: 参考 api.items.story.types.v1.CapsulePhotoInfo in days 几天内的图片
                    format: uint32
                moments:
                    type: integer
                    description: 参考 api.items.story.types.v1.CapsulePhotoInfo moments 几个瞬间
                    format: uint32
        api.im.message.types.v1.StoryChatProxyCustomMessagePayload:
            type: object
            properties:
                consumerVideoCoverUrl:
                    type: string
                    description: 消费者录制的视频封面
                consumerVideoUrl:
                    type: string
                    description: 消费者录制的视频
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
                storyId:
                    type: string
                    description: 交互的 Story 的 id
        api.im.message.types.v1.StoryExchangeImageInteractionCustomMessagePayload:
            type: object
            properties:
                consumerImageUrl:
                    type: string
                    description: 消费者发送的图片
                consumerInteractionImageUrl:
                    type: string
                    description: 消费者交互的图片
                storyId:
                    type: string
                    description: 消费者交互的 story id
                nodeId:
                    type: string
                    description: 消费者交互的节点 id
                title:
                    type: string
                    description: 当前消息的 title 比如 the other party replied to your story
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
        api.im.message.types.v1.StoryFizzCustomMessagePayload:
            type: object
            properties:
                payloadType:
                    type: string
                    description: 负载类型，参考 PayloadType
                fizzId:
                    type: string
                text:
                    type: string
                    description: 表示消费者发送的文字消息，当 payload_type 为 PAYLOAD_TYPE_IMAGE 和 PAYLOAD_TYPE_VIDEO 时，系统自动生成文字
                imageUrl:
                    type: string
                    description: 仅当 payload_type 为 PAYLOAD_TYPE_IMAGE 时有效
                videoPayload:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryFizzCustomMessagePayload_VideoPayload'
                expireAt:
                    type: string
                    description: 消息过期时间
                mediaId:
                    type: string
        api.im.message.types.v1.StoryFizzCustomMessagePayload_VideoPayload:
            type: object
            properties:
                videoUrl:
                    type: string
                    description: 视频链接
                coverUrl:
                    type: string
                    description: 视频封面链接
        api.im.message.types.v1.StoryHauntCustomMessagePayload:
            type: object
            properties:
                videoUrl:
                    type: string
                videoCoverUrl:
                    type: string
                booAvatarUrl:
                    type: string
                storyId:
                    type: string
        api.im.message.types.v1.StoryHideStickerUnlockedCustomMessagePayload:
            type: object
            properties:
                storyId:
                    type: string
                storyCoverUrl:
                    type: string
                stickerUrl:
                    type: string
                title:
                    type: string
        api.im.message.types.v1.StoryMomentQuoteCustomMessagePayload:
            type: object
            properties:
                momentId:
                    type: string
                    description: 引用的 moment id
                portalId:
                    type: string
                    description: moment 所在的 Portal id
                firstMomentCoverImageUrl:
                    type: string
                    description: moment 的封面图片
                text:
                    type: string
                    description: 发送的字符串内容
        api.im.message.types.v1.StoryNowShotCustomMessagePayload:
            type: object
            properties:
                payloadType:
                    type: string
                    description: 负载类型，参考 PayloadType
                consumerImageUrl:
                    type: string
                    description: 消费者发送的图片，如果 payload 是 video 则为 thumbnail
                consumerVideoUrl:
                    type: string
                    description: 消费者发送的视频
                consumerInteractionImageUrl:
                    type: string
                    description: 消费者交互的图片,如果 payload 是 video 则为 thumbnail
                consumerInteractionVideoUrl:
                    type: string
                    description: 消费者交互的视频
                storyId:
                    type: string
                    description: 消费者交互的 story id
                title:
                    type: string
                    description: 当前消息的 title 比如 the other party replied to your story
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
        api.im.message.types.v1.StoryPinInteractionCustomMessagePayload:
            type: object
            properties:
                storyId:
                    type: string
                backgroundImageUrl:
                    type: string
                successConsumeCostSeconds:
                    type: string
                failedConsumeImageUrl:
                    type: string
        api.im.message.types.v1.StoryRoastedCustomMessagePayload:
            type: object
            properties:
                consumerVideoCoverUrl:
                    type: string
                    description: 消费者录制的视频封面
                consumerVideoUrl:
                    type: string
                    description: 消费者录制的视频
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
                storyId:
                    type: string
                    description: 交互的 Story 的 id
        api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload:
            type: object
            properties:
                storyId:
                    type: string
                storyCoverUrl:
                    type: string
                momentId:
                    type: string
                shareCreateMomentType:
                    enum:
                        - SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED
                        - SHARE_CREATE_MOMENT_TYPE_STORY
                        - SHARE_CREATE_MOMENT_TYPE_MOMENT
                    type: string
                    format: enum
        api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload:
            type: object
            properties:
                payloadType:
                    type: string
                    description: 负载类型，参考 PayloadType
                storyInfoJsonStr:
                    type: string
                    description: '交互的 Story 的 json 字符串，结构为: {   "story_id": "string", // 交互的 Story 的 id   "story_cover_url": "string", // 交互的 Story 的封面   "caption": "string",         // 显示在屏幕中间的 caption   "tips": "string",           // 显示在屏幕中间的 tips   "ai_response": "string",    // ai 的回复   "end_message": "string",    // 隐藏意图，用于帮助 hit_words 命中   "end_message_font": "string" // 隐藏意图字体 }'
                text:
                    type: string
                    description: 仅当 payload_type 为 PAYLOAD_TYPE_TEXT 时有效 表示消费者或者创作者发送的文字消息
                endImagePayload:
                    $ref: '#/components/schemas/api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload_EndImagePayload'
                aiResponse:
                    type: string
                userText:
                    type: string
        api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload_EndImagePayload:
            type: object
            properties:
                endImageUrl:
                    type: string
                    description: 结局图片
                endMessage:
                    type: string
                    description: 谜底
        api.im.message.types.v1.StoryUnmuteCustomMessagePayload:
            type: object
            properties:
                consumerAudioUrl:
                    type: string
                    description: 消费者发送的音频
                consumerInteractionImageUrl:
                    type: string
                    description: 消费者交互的图片
                storyId:
                    type: string
                    description: 消费者交互的 story id
                title:
                    type: string
                    description: 当前消息的 title 比如 the other party replied to your story
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
                aiResponse:
                    type: string
                    description: ai 回复
        api.im.message.types.v1.StoryWassupCustomMessagePayload:
            type: object
            properties:
                consumerVideoCoverUrl:
                    type: string
                    description: 消费者录制的视频封面
                consumerVideoUrl:
                    type: string
                    description: 消费者录制的视频
                storyCoverUrl:
                    type: string
                    description: 交互的 Story 的封面
                storyId:
                    type: string
                    description: 交互的 Story 的 id
        api.im.message.types.v1.StoryWhoInteractionCustomMessagePayload:
            type: object
            properties:
                avatarUrls:
                    type: array
                    items:
                        type: string
                    description: 所有待选项
                selectedAvatarUrl:
                    type: string
                    description: 选中的头像
                selectedUserName:
                    type: string
                    description: 选中的用户名
                correct:
                    type: boolean
                    description: 是否正确
                triedTimes:
                    type: integer
                    description: 尝试了几次
                    format: uint32
                storyId:
                    type: string
                    description: 交互的 story id
        api.im.v1.SendMessageRequest:
            type: object
            properties:
                toUserId:
                    type: string
                message:
                    $ref: '#/components/schemas/api.im.message.types.v1.Message'
        api.items.comments.types.v1.Comment:
            type: object
            properties:
                id:
                    type: string
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserMini'
                content:
                    type: string
                status:
                    enum:
                        - COMMENT_REPLY_STATUS_UNSPECIFIED
                        - COMMENT_REPLY_STATUS_NORMAL
                        - COMMENT_REPLY_STATUS_DELETED
                    type: string
                    format: enum
                stats:
                    $ref: '#/components/schemas/api.items.comments.types.v1.CommentReplyStats'
                createdAtUnixTimestamp:
                    type: integer
                    description: 评论的创建时间
                    format: uint32
                loginUserRelation:
                    $ref: '#/components/schemas/api.items.comments.types.v1.LoginUserCommentReplyRelation'
                topReplies:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.comments.types.v1.Reply'
                    description: 评论的回复，服务端会计算热度返回适合的回复
            description: item 的评论
        api.items.comments.types.v1.CommentReplyStats:
            type: object
            properties:
                likesCount:
                    type: integer
                    format: uint32
                repliesCount:
                    type: integer
                    format: uint32
        api.items.comments.types.v1.LoginUserCommentReplyRelation:
            type: object
            properties:
                isLiked:
                    type: boolean
        api.items.comments.types.v1.Reply:
            type: object
            properties:
                id:
                    type: string
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserMini'
                content:
                    type: string
                replyType:
                    enum:
                        - REPLY_TYPE_UNSPECIFIED
                        - REPLY_TYPE_REPLY
                        - REPLY_TYPE_REPLY_REPLY
                    type: string
                    description: 回复的类型
                    format: enum
                replied:
                    $ref: '#/components/schemas/api.items.comments.types.v1.Reply'
                status:
                    enum:
                        - COMMENT_REPLY_STATUS_UNSPECIFIED
                        - COMMENT_REPLY_STATUS_NORMAL
                        - COMMENT_REPLY_STATUS_DELETED
                    type: string
                    description: 回复的状态
                    format: enum
                stats:
                    $ref: '#/components/schemas/api.items.comments.types.v1.CommentReplyStats'
                createdAtUnixTimestamp:
                    type: integer
                    description: 回复的创建时间
                    format: uint32
                loginUserRelation:
                    $ref: '#/components/schemas/api.items.comments.types.v1.LoginUserCommentReplyRelation'
        api.items.comments.v1.CreateCommentRequest:
            type: object
            properties:
                itemId:
                    type: string
                content:
                    type: string
                parentCommentId:
                    type: string
                    description: 以下两个值，如果 1. 均不为空，则表示是二级回复 2. 仅有 parent_comment_id，则表示是评论的回复， 3. 仅有 parent_reply_id，则表示是回复的回复， 4. 均为空，则表示是评论，
                parentReplyId:
                    type: string
            description: 创建评论
        api.items.comments.v1.CreateCommentResponse:
            type: object
            properties:
                comment:
                    $ref: '#/components/schemas/api.items.comments.types.v1.Comment'
        api.items.comments.v1.DeleteCommentOrReplyRequest:
            type: object
            properties:
                commentId:
                    type: string
                replyId:
                    type: string
            description: 删除评论 or 回复
        api.items.comments.v1.LikeCommentOrReplyRequest:
            type: object
            properties:
                commentId:
                    type: string
                replyId:
                    type: string
            description: 点赞评论或者回复
        api.items.comments.v1.ListCommentRepliesRequest:
            type: object
            properties:
                commentId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
            description: List item comment replies
        api.items.comments.v1.ListCommentRepliesResponse:
            type: object
            properties:
                replies:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.comments.types.v1.Reply'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.comments.v1.ListCommentsRequest:
            type: object
            properties:
                itemId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
            description: List item comments
        api.items.comments.v1.ListCommentsResponse:
            type: object
            properties:
                comments:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.comments.types.v1.Comment'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.comments.v1.UnlikeCommentOrReplyRequest:
            type: object
            properties:
                commentId:
                    type: string
                replyId:
                    type: string
            description: 取消点赞评论或者回复
        api.items.music.search.v1.SearchRequest:
            type: object
            properties:
                keyword:
                    type: string
                types:
                    type: array
                    items:
                        enum:
                            - SEARCH_TYPE_UNSPECIFIED
                            - SEARCH_TYPE_SONG
                            - SEARCH_TYPE_ALBUM
                            - SEARCH_TYPE_SINGER
                        type: string
                        format: enum
                    description: 搜索类型，如果是空数组，默认只搜索歌曲
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.music.search.v1.SearchResponse:
            type: object
            properties:
                results:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.music.search.v1.SearchResponse_Result'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.music.search.v1.SearchResponse_Result:
            type: object
            properties:
                songs:
                    $ref: '#/components/schemas/api.items.music.types.v1.Song'
                albums:
                    $ref: '#/components/schemas/api.items.music.types.v1.Album'
                singers:
                    $ref: '#/components/schemas/api.items.music.types.v1.Singer'
                type:
                    enum:
                        - SEARCH_TYPE_UNSPECIFIED
                        - SEARCH_TYPE_SONG
                        - SEARCH_TYPE_ALBUM
                        - SEARCH_TYPE_SINGER
                    type: string
                    format: enum
            description: 搜索结果
        api.items.music.types.v1.Album:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                title:
                    type: string
                subtitle:
                    type: string
                timePublicUnixTimestamp:
                    type: integer
                    description: 发布时间，标准 unix时间戳
                    format: uint32
            description: 音乐专辑实体
        api.items.music.types.v1.Singer:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                title:
                    type: string
            description: 音乐作者实体
        api.items.music.types.v1.Song:
            type: object
            properties:
                id:
                    type: string
                name:
                    type: string
                title:
                    type: string
                author:
                    type: string
                picImageUrl:
                    type: string
                playAudioUrl:
                    type: string
                durationSeconds:
                    type: integer
                    description: 歌曲时长，单位：秒
                    format: uint32
                album:
                    $ref: '#/components/schemas/api.items.music.types.v1.Album'
                singers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.music.types.v1.Singer'
            description: 音乐歌曲实体
        api.items.portal.moments.types.v1.Moment:
            type: object
            properties:
                id:
                    type: string
                momentType:
                    enum:
                        - MOMENT_TYPE_UNSPECIFIED
                        - MOMENT_TYPE_NORMAL
                        - MOMENT_TYPE_HAUNT
                    type: string
                    format: enum
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                attachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                loginUserRelations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.moments.types.v1.MomentRelation'
                avatars:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.boo.types.v1.Avatar'
                latestViewer:
                    $ref: '#/components/schemas/api.items.portal.moments.types.v1.Viewer'
                createdAtTimestamp:
                    type: integer
                    format: uint32
                stat:
                    $ref: '#/components/schemas/api.items.portal.moments.types.v1.Stat'
                hauntBooShowInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.HauntBooShowInfo'
        api.items.portal.moments.types.v1.MomentRelation:
            type: object
            properties:
                relationType:
                    enum:
                        - RELATION_TYPE_UNSPECIFIED
                        - RELATION_TYPE_LIKE
                    type: string
                    format: enum
                createdAtTimestamp:
                    type: integer
                    format: uint32
        api.items.portal.moments.types.v1.Stat:
            type: object
            properties:
                likeCount:
                    type: integer
                    format: uint32
        api.items.portal.moments.types.v1.Viewer:
            type: object
            properties:
                user:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                userRelations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.moments.types.v1.MomentRelation'
                    description: 此用户对 moment 的 relations
        api.items.portal.types.v1.Portal:
            type: object
            properties:
                id:
                    type: string
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                story:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                allPublishedMoments:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.moments.types.v1.Moment'
                    description: 发布的所有 moments
                lastReadMomentId:
                    type: string
                    description: 上次阅读的 moment page token，如果未读过，则为 空串 在请求 ListPortalMoments 时传递
                unreadMomentIds:
                    type: array
                    items:
                        type: string
                    description: 所有未读的 moment id
                expiredAtTimestamp:
                    type: integer
                    description: 过期时间
                    format: uint32
                stat:
                    $ref: '#/components/schemas/api.items.portal.types.v1.PortalStat'
        api.items.portal.types.v1.PortalStat:
            type: object
            properties:
                momentCount:
                    type: integer
                    description: 所有的 moment 数量
                    format: uint32
                unreadMomentCount:
                    type: integer
                    description: 未读的 moment 数量
                    format: uint32
        api.items.portal.types.v1.UserCreatedPortals:
            type: object
            properties:
                portals:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
                userHasPostedStories:
                    type: boolean
                userLastReadPortalId:
                    type: string
        api.items.portal.v1.CreateMomentRelationRequest:
            type: object
            properties:
                relationType:
                    enum:
                        - RELATION_TYPE_UNSPECIFIED
                        - RELATION_TYPE_LIKE
                    type: string
                    format: enum
                momentId:
                    type: string
        api.items.portal.v1.CreateMomentRelationResponse:
            type: object
            properties:
                moment:
                    $ref: '#/components/schemas/api.items.portal.moments.types.v1.Moment'
        api.items.portal.v1.CreateMomentRequest:
            type: object
            properties:
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                storyId:
                    type: string
                attachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                fromStoryId:
                    type: string
                    description: 从哪个 story 过来创建的
        api.items.portal.v1.CreateMomentResponse:
            type: object
            properties:
                moment:
                    $ref: '#/components/schemas/api.items.portal.moments.types.v1.Moment'
        api.items.portal.v1.DeleteMomentRequest:
            type: object
            properties:
                momentId:
                    type: string
        api.items.portal.v1.GetMomentRequest:
            type: object
            properties:
                momentId:
                    type: string
        api.items.portal.v1.GetMomentResponse:
            type: object
            properties:
                moment:
                    $ref: '#/components/schemas/api.items.portal.moments.types.v1.Moment'
        api.items.portal.v1.GetPortalRequest:
            type: object
            properties:
                portalId:
                    type: string
        api.items.portal.v1.GetPortalResponse:
            type: object
            properties:
                portal:
                    $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
        api.items.portal.v1.GetUserCreatedPortalsInfoRequest:
            type: object
            properties:
                userId:
                    type: string
        api.items.portal.v1.GetUserCreatedPortalsInfoResponse:
            type: object
            properties:
                userCreatedPortals:
                    $ref: '#/components/schemas/api.items.portal.types.v1.UserCreatedPortals'
        api.items.portal.v1.ListCouldAppendMomentStoriesRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.portal.v1.ListCouldAppendMomentStoriesResponse:
            type: object
            properties:
                stories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.portal.v1.ListMomentViewersRequest:
            type: object
            properties:
                momentId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.portal.v1.ListMomentViewersResponse:
            type: object
            properties:
                viewers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.moments.types.v1.Viewer'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.portal.v1.ListMyPortalsRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.portal.v1.ListMyPortalsResponse:
            type: object
            properties:
                userCreatedPortals:
                    $ref: '#/components/schemas/api.items.portal.types.v1.UserCreatedPortals'
                portals:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.portal.v1.ListTrendingPortalsRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.portal.v1.ListTrendingPortalsResponse:
            type: object
            properties:
                portals:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest:
            type: object
            properties:
                listReq:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                userId:
                    type: string
                    description: 如果不传，则默认拉取登录用户
        api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse:
            type: object
            properties:
                PortalsWithDates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate:
            type: object
            properties:
                dateZero:
                    type: integer
                    description: 这批 Portals 在用户时区的当日0点，客户端需要自行转化时间格式
                    format: uint32
                portals:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
        api.items.portal.v1.RemoveMomentRelationRequest:
            type: object
            properties:
                relationType:
                    enum:
                        - RELATION_TYPE_UNSPECIFIED
                        - RELATION_TYPE_LIKE
                    type: string
                    format: enum
                momentId:
                    type: string
        api.items.portal.v1.RemoveMomentRelationResponse:
            type: object
            properties:
                moment:
                    $ref: '#/components/schemas/api.items.portal.moments.types.v1.Moment'
        api.items.portal.v1.ReportReadRequest:
            type: object
            properties:
                momentId:
                    type: string
                portalId:
                    type: string
        api.items.portal.v1.SendMomentInviteRequest:
            type: object
            properties:
                type:
                    enum:
                        - INVITE_TARGET_TYPE_UNSPECIFIED
                        - INVITE_TARGET_TYPE_STORY
                        - INVITE_TARGET_TYPE_MOMENT
                    type: string
                    format: enum
                storyId:
                    type: string
                momentId:
                    type: string
                receiverIds:
                    type: array
                    items:
                        type: string
        api.items.portal.v1.SendMomentInviteResponse:
            type: object
            properties: {}
        api.items.reaction.types.v1.ItemReaction:
            type: object
            properties:
                type:
                    enum:
                        - REACTION_TYPE_UNSPECIFIED
                        - REACTION_TYPE_LIKE
                    type: string
                    format: enum
                happenedAt:
                    type: integer
                    description: unix 时间戳，单位秒
                    format: uint32
        api.items.reaction.v1.CreateItemReactionRequest:
            type: object
            properties:
                itemId:
                    type: string
                type:
                    enum:
                        - REACTION_TYPE_UNSPECIFIED
                        - REACTION_TYPE_LIKE
                    type: string
                    format: enum
        api.items.reaction.v1.CreateItemReactionResponse:
            type: object
            properties:
                item:
                    $ref: '#/components/schemas/api.items.types.v1.ItemDetail'
        api.items.reaction.v1.ListUserReactedItemsRequest:
            type: object
            properties:
                userId:
                    type: string
                    description: 可选，如果不传此值，则拉取登录用户的数据
                type:
                    enum:
                        - REACTION_TYPE_UNSPECIFIED
                        - REACTION_TYPE_LIKE
                    type: string
                    description: 必传，筛选的 reaction 类型
                    format: enum
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.reaction.v1.ListUserReactedItemsResponse:
            type: object
            properties:
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                itemSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.types.v1.ItemSummary'
        api.items.reaction.v1.RemoveItemReactionRequest:
            type: object
            properties:
                itemId:
                    type: string
                type:
                    enum:
                        - REACTION_TYPE_UNSPECIFIED
                        - REACTION_TYPE_LIKE
                    type: string
                    format: enum
        api.items.reaction.v1.RemoveItemReactionResponse:
            type: object
            properties:
                item:
                    $ref: '#/components/schemas/api.items.types.v1.ItemDetail'
        api.items.story.activity.types.v1.ActivityItem:
            type: object
            properties:
                id:
                    type: string
                    description: Activity id
                actor:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                story:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                type:
                    enum:
                        - ACTIVITY_TYPE_UNSPECIFIED
                        - ACTIVITY_TYPE_STORY_LIKE
                        - ACTIVITY_TYPE_STORY_COMMENT
                    type: string
                    description: Activity type
                    format: enum
                createdAtTimestamp:
                    type: integer
                    description: Created at (unix seconds)
                    format: uint32
                storyLike:
                    $ref: '#/components/schemas/api.items.story.activity.types.v1.StoryLikeActivity'
                storyComment:
                    $ref: '#/components/schemas/api.items.story.activity.types.v1.StoryCommentActivity'
            description: A single activity directed to the login user (receiver)
        api.items.story.activity.types.v1.StoryCommentActivity:
            type: object
            properties:
                comment:
                    $ref: '#/components/schemas/api.items.comments.types.v1.Comment'
            description: Comment details
        api.items.story.activity.types.v1.StoryLikeActivity:
            type: object
            properties:
                emoji:
                    type: string
            description: Like details
        api.items.story.activity.v1.GetActivityUnreadCountRequest:
            type: object
            properties: {}
            description: Get unread activity count for my stories
        api.items.story.activity.v1.GetActivityUnreadCountResponse:
            type: object
            properties:
                count:
                    type: integer
                    format: uint32
        api.items.story.activity.v1.ListActivitiesRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.story.activity.v1.ListActivitiesResponse:
            type: object
            properties:
                activities:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.activity.types.v1.ActivityItem'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.activity.v1.ListUsersByConsumptionStatusRequest:
            type: object
            properties:
                storyId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                consumptionStatus:
                    enum:
                        - CONSUMPTION_STATUS_UNSPECIFIED
                        - CONSUMPTION_STATUS_VIEWED
                        - CONSUMPTION_STATUS_PLAYED
                        - CONSUMPTION_STATUS_UNLOCKED
                    type: string
                    format: enum
        api.items.story.activity.v1.ListUsersByConsumptionStatusResponse:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.activity.v1.ReportActivityReadRequest:
            type: object
            properties: {}
            description: Mark activities as read up to latest
        api.items.story.hide_stickers.v1.CollectHideStickerRequest:
            type: object
            properties:
                stickerId:
                    type: string
        api.items.story.hide_stickers.v1.ListMyCollectedStickersRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse:
            type: object
            properties:
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                collectedStickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse_CollectedSticker'
        api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse_CollectedSticker:
            type: object
            properties:
                sticker:
                    $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                collectedAtUnixTimestamp:
                    type: integer
                    format: uint32
                isTop:
                    type: boolean
        api.items.story.hide_stickers.v1.TopCollectedStickerRequest:
            type: object
            properties:
                stickerId:
                    type: string
        api.items.story.hide_stickers.v1.UnCollectHideStickerRequest:
            type: object
            properties:
                stickerId:
                    type: string
        api.items.story.hide_stickers.v1.UnTopCollectedStickerRequest:
            type: object
            properties:
                stickerId:
                    type: string
        api.items.story.reaction.types.v1.Reaction:
            type: object
            properties:
                emoji:
                    type: string
                comment:
                    type: string
                createdAtTimestamp:
                    type: integer
                    format: uint32
        api.items.story.reaction.types.v1.ReactionMadeStatSummary:
            type: object
            properties:
                emojiStats:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.reaction.types.v1.ReactionMadeStatSummary_EmojiStat'
        api.items.story.reaction.types.v1.ReactionMadeStatSummary_EmojiStat:
            type: object
            properties:
                emoji:
                    type: string
                count:
                    type: integer
                    format: uint32
        api.items.story.reaction.v1.CreateReactionRequest:
            type: object
            properties:
                storyId:
                    type: string
                emoji:
                    type: string
        api.items.story.reaction.v1.CreateReactionResponse:
            type: object
            properties:
                story:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.reaction.v1.DeleteReactionRequest:
            type: object
            properties:
                storyId:
                    type: string
                emoji:
                    type: string
        api.items.story.reaction.v1.DeleteReactionResponse:
            type: object
            properties:
                story:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.reaction.v1.ListReactionMadeUsersRequest:
            type: object
            properties:
                storyId:
                    type: string
                emojis:
                    type: array
                    items:
                        type: string
                    description: 如果为空，则返回所有 emoji 的 reaction 用户
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.story.reaction.v1.ListReactionMadeUsersResponse:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.types.v1.AttachmentText:
            type: object
            properties:
                text:
                    type: string
                    description: 文本内容
                fontName:
                    type: string
                    description: 字体名称
                fontSize:
                    type: integer
                    description: 字体大小
                    format: uint32
                color:
                    type: string
                    description: 颜色
                x:
                    type: string
                    description: x centerY 相对坐标
                y:
                    type: string
                    description: y centerY 相对坐标
                width:
                    type: string
                    description: 宽度 相对屏幕宽度
                height:
                    type: string
                    description: 高度 相对屏幕高度
                alignment:
                    type: string
                    description: 对齐方式 center, left, right
                fillStyle:
                    type: string
                    description: fillStyle none, white, textcolor
                rotation:
                    type: string
                    description: 旋转
                scale:
                    type: string
                    description: 缩放
                atUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText_AtUser'
        api.items.story.types.v1.AttachmentText_AtUser:
            type: object
            properties:
                id:
                    type: string
                nickname:
                    type: string
                    description: 创作时，客户端可以不传，服务端下发时会自行填充
            description: '@的 user'
        api.items.story.types.v1.CapsuleAIScript:
            type: object
            properties:
                seconds:
                    type: integer
                    description: 第几秒
                    format: uint32
                question:
                    $ref: '#/components/schemas/api.items.story.types.v1.CapsuleQuestion'
        api.items.story.types.v1.CapsulePhotoInfo:
            type: object
            properties:
                inDays:
                    type: integer
                    description: 几天内的图片
                    format: uint32
                moments:
                    type: integer
                    description: 几个瞬间
                    format: uint32
        api.items.story.types.v1.CapsuleQuestion:
            type: object
            properties:
                question:
                    type: string
                ttsAudioUrl:
                    type: string
                    description: 创建的时候不需要传此值
                ttsAudioKey:
                    type: string
                    description: 创建的时候必传
                thinking:
                    type: string
                    description: 此字段暂时对端上无作用，消费时带回来即可
                words:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Word'
                    description: 词粒度信息
            description: ai 生成的问题和 tts
        api.items.story.types.v1.CapsuleQuestionWithUserAnswer:
            type: object
            properties:
                question:
                    $ref: '#/components/schemas/api.items.story.types.v1.CapsuleQuestion'
                userVoiceKey:
                    type: string
                    description: 用户回答的音频资源
        api.items.story.types.v1.ChatProxyQuestion:
            type: object
            properties:
                question:
                    type: string
                ttsAudioUrl:
                    type: string
                words:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Word'
        api.items.story.types.v1.CommonPlayConfig:
            type: object
            properties:
                cover:
                    $ref: '#/components/schemas/api.items.story.types.v1.Cover'
                resource:
                    $ref: '#/components/schemas/api.items.story.types.v1.Resource'
                coverCaptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                    description: V2 作者 at your friend 填写的文字，附着在封面资源上
                conditionV2:
                    $ref: '#/components/schemas/api.items.story.types.v1.Condition'
                maxTryCount:
                    type: integer
                    description: V2 最多允许尝试多少次
                    format: uint32
                resourceCaptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                    description: V2 作者 at your friend 填写的文字，附着在解锁后的资源上
        api.items.story.types.v1.CommonStoryPlayConditionTemplate:
            type: object
            properties:
                id:
                    type: string
                condition:
                    $ref: '#/components/schemas/api.items.story.types.v1.Condition'
        api.items.story.types.v1.Condition:
            type: object
            properties:
                hint:
                    $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                prompt:
                    type: string
                    description: llm 的 prompt，即 Creator Criteria
        api.items.story.types.v1.Cover:
            type: object
            properties:
                CoverType:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                        - RESOURCE_TYPE_NONE
                        - RESOURCE_TYPE_GRADIENT
                    type: string
                    description: CoverType，无图 none/渐变图 gradient/自定义上传图片 image/自定义上传视频 video
                    format: enum
                resourceKey:
                    type: string
                    description: 只在 CoverType 为 image 时生效，资源 key
                resourceUrl:
                    type: string
                    description: 只在 CoverType 为 image 时生效，资源 url，创建时可以留空，返回时服务会拼接
                gradient:
                    type: string
                    description: 只在 CoverType 为 gradient 时生效
                coverWidth:
                    type: integer
                    description: 封面宽度
                    format: uint32
                coverHeight:
                    type: integer
                    description: 封面高度
                    format: uint32
                thumbnailKey:
                    type: string
                    description: 只在 CoverType 为 video 时生效，资源 key
                thumbnailUrl:
                    type: string
                    description: 只在 CoverType 为 video 时生效，资源 url，创建时可以留空，返回时服务会拼接
        api.items.story.types.v1.ExampleCommonInfo:
            type: object
            properties:
                tips:
                    type: string
                    description: 示例的提示
                imageUrls:
                    type: array
                    items:
                        type: string
                    description: story 的图片 目前，当是换图玩法时： 1. 马赛克时，数组长度固定为1， 表示封面图 2. 线性解锁时，数组长度固定为2，第一个表示封面图，第二个表示下一个节点的封面图 当是海龟汤玩法时，数组长度固定为1，表示封面图
        api.items.story.types.v1.HauntBoo:
            type: object
            properties:
                id:
                    type: string
                avatar:
                    $ref: '#/components/schemas/api.users.boo.types.v1.Avatar'
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                generatedHint:
                    type: string
                videoUrl:
                    type: string
                creatorBoo:
                    type: boolean
                questionsWithAnswers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntQuestionWithAnswer'
        api.items.story.types.v1.HauntBooShowInfo:
            type: object
            properties:
                hauntBoos:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntBoo'
                    description: 第一个永远是 creator boo
                fromHauntStoryId:
                    type: string
                    description: 此次展现来源于哪个 Story
                showInfoSence:
                    enum:
                        - SHOW_INFO_SENCE_UNSPECIFIED
                        - SHOW_INFO_SENCE_FEED_POLLUTION
                        - SHOW_INFO_SENCE_CONTENT_POLLUTION
                    type: string
                    description: 展示场景
                    format: enum
                lastShowAtInUnixstamp:
                    type: integer
                    description: 最后一次展示时间，标准 unixstamp
                    format: uint32
                todayShowCount:
                    type: integer
                    description: 针对登录 session 的当日展现次数
                    format: uint32
                triedCount:
                    type: integer
                    description: 针对登录 session 这个 story 已经尝试了几次了
                    format: uint32
        api.items.story.types.v1.HauntPlayConfig:
            type: object
            properties:
                boosWithQuestionsAndAnswers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntBoo'
                    description: 第一个永远是作者自己的 Boo
                captions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
        api.items.story.types.v1.HauntPlayContext:
            type: object
            properties:
                tryCount:
                    type: integer
                    format: uint32
                unlocked:
                    type: boolean
        api.items.story.types.v1.HauntQuestion:
            type: object
            properties:
                title:
                    type: string
                description:
                    type: string
                    description: 填充默认值
                isRequired:
                    type: boolean
        api.items.story.types.v1.HauntQuestionWithAnswer:
            type: object
            properties:
                question:
                    $ref: '#/components/schemas/api.items.story.types.v1.HauntQuestion'
                answer:
                    type: string
        api.items.story.types.v1.HideSticker:
            type: object
            properties:
                id:
                    type: string
                fromStoryId:
                    type: string
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                transform:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerTransform'
        api.items.story.types.v1.ImageDetail:
            type: object
            properties:
                objectKey:
                    type: string
                    description: 照片地址
                latitude:
                    type: string
                    description: 照片拍摄纬度
                longitude:
                    type: string
                    description: 照片拍摄经度
                shootingTimestamp:
                    type: integer
                    description: 照片拍摄时间
                    format: uint32
        api.items.story.types.v1.MomentCreateAttr:
            type: object
            properties:
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                attachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                avatarIds:
                    type: array
                    items:
                        type: integer
                        format: int64
        api.items.story.types.v1.PinEmojiArear:
            type: object
            properties:
                x:
                    type: string
                y:
                    type: string
                width:
                    type: string
                height:
                    type: string
        api.items.story.types.v1.PinEmojiResource:
            type: object
            properties:
                generatedEmojiResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                defaultEmoji:
                    type: string
                area:
                    $ref: '#/components/schemas/api.items.story.types.v1.PinEmojiArear'
        api.items.story.types.v1.PortalBasicInfo:
            type: object
            properties:
                portalId:
                    type: string
                    description: Portal ID
                momentCount:
                    type: integer
                    description: Portal 下的 moment 总数
                    format: uint32
        api.items.story.types.v1.PrivacySetting:
            type: object
            properties:
                privacyType:
                    enum:
                        - PRIVACY_TYPE_UNSPECIFIED
                        - PRIVACY_TYPE_PUBLIC
                        - PRIVACY_TYPE_PRIVATE
                        - PRIVACY_TYPE_FRIEND
                        - PRIVACY_TYPE_FOLLOWER
                        - PRIVACY_TYPE_ALLOWLIST
                    type: string
                    description: 可见性
                    format: enum
                visibleBeforeTimestamp:
                    type: integer
                    description: 在某个时间戳前可见，默认为 timestamp 最大值：2038-01-19 03:14:07 UTC
                    format: uint32
                allowlistUserIds:
                    type: array
                    items:
                        type: string
                    description: 仅 PrivacyType 为 PRIVACY_TYPE_ALLOWLIST 时生效，白名单范围user ids
        api.items.story.types.v1.Resource:
            type: object
            properties:
                resourceType:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                        - RESOURCE_TYPE_NONE
                        - RESOURCE_TYPE_GRADIENT
                    type: string
                    format: enum
                resourceKey:
                    type: string
                    description: 资源 key
                resourceUrl:
                    type: string
                    description: 资源 url，创建时可以留空，返回时服务会拼接
                coverImageKey:
                    type: string
                    description: 如果是视频，则需要提供封面图 如果是图片，直接用 resource_key 即可
                coverImageUrl:
                    type: string
                    description: 封面图 url，创建时可以留空，返回时服务会拼接
                coverWidth:
                    type: integer
                    description: 封面宽度
                    format: uint32
                coverHeight:
                    type: integer
                    description: 封面高度
                    format: uint32
        api.items.story.types.v1.RoastedQuestion:
            type: object
            properties:
                question:
                    type: string
                ttsAudioUrl:
                    type: string
                    description: 创建的时候不需要传此值
                ttsAudioKey:
                    type: string
                    description: 创建的时候必传
                thinking:
                    type: string
                    description: 此字段暂时对端上无作用，消费时带回来即可
                words:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Word'
                    description: 词粒度信息
        api.items.story.types.v1.RoastedQuestionWithUserAnswer:
            type: object
            properties:
                question:
                    $ref: '#/components/schemas/api.items.story.types.v1.RoastedQuestion'
                userVoiceKey:
                    type: string
                    description: 用户回答的音频资源
        api.items.story.types.v1.StickerTransform:
            type: object
            properties:
                location:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerTransform_Location'
                size:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerTransform_Size'
        api.items.story.types.v1.StickerTransform_Location:
            type: object
            properties:
                x:
                    type: string
                    description: 浮点型，表示距离原点的比例 0 ~ 1
                y:
                    type: string
                    description: 浮点型，表示距离原点的比例 0 ~ 1
        api.items.story.types.v1.StickerTransform_Size:
            type: object
            properties:
                width:
                    type: string
                    description: 宽缩放比例，浮点型，0 ~ 1
                height:
                    type: string
                    description: 高缩放比例，浮点型，0 ~ 1
        api.items.story.types.v1.StickerWithTriggerType:
            type: object
            properties:
                triggerType:
                    enum:
                        - STICKER_TRIGGER_TYPE_UNSPECIFIED
                        - STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK
                        - STICKER_TRIGGER_TYPE_DRAG
                        - STICKER_TRIGGER_TYPE_LIKE
                        - STICKER_TRIGGER_TYPE_SHAKE
                        - STICKER_TRIGGER_TYPE_LONG_PRESS
                    type: string
                    format: enum
                continuousClickData:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData'
                dragData:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeDragData'
                likeData:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeLikeData'
                shakeData:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeShakeData'
                longPressData:
                    $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeLongPressData'
        api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData:
            type: object
            properties:
                stickersWithClickLocationsInCreation:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                    description: 创作时需要指定的 Stickers 及其所在的点击判定位置 服务端会根据 Location 及半径 聚合这些 stickers
                stickersWithClickLocationsInConsume:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume'
                needClickCount:
                    type: integer
                    format: uint32
            description: 连续点击
        api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume:
            type: object
            properties:
                stickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                x:
                    type: string
                y:
                    type: string
        api.items.story.types.v1.StickerWithTriggerTypeDragData:
            type: object
            properties:
                cutObjects:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerTypeDragData_CutObject'
            description: 拖动图层
        api.items.story.types.v1.StickerWithTriggerTypeDragData_CutObject:
            type: object
            properties:
                maskImageUrl:
                    type: string
                    description: 创作时忽略
                maskImageKey:
                    type: string
                    description: 消费时忽略
                sticker:
                    $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
            description: 抠图对象
        api.items.story.types.v1.StickerWithTriggerTypeLikeData:
            type: object
            properties:
                stickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
            description: 点赞
        api.items.story.types.v1.StickerWithTriggerTypeLongPressData:
            type: object
            properties:
                stickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                needLongPressDurationSeconds:
                    type: integer
                    format: uint32
            description: 长按
        api.items.story.types.v1.StickerWithTriggerTypeShakeData:
            type: object
            properties:
                stickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                needShakeCount:
                    type: integer
                    format: uint32
                needShakeDurationSeconds:
                    type: integer
                    format: uint32
            description: 摇动手机
        api.items.story.types.v1.StoryDetail:
            type: object
            properties:
                summary:
                    $ref: '#/components/schemas/api.items.story.types.v1.StorySummary'
                exchangeImageConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageConfig'
                turtleSoupConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupConfig'
                unmuteConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteConfig'
                turtleSoupMassConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupMassConfig'
                basePlayConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayBasePlayConfig'
                nowShotConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayNowShotConfig'
                roastedConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedConfig'
                wassupConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayWassupConfig'
                capsuleConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayCapsuleConfig'
                hideConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayHideConfig'
                chatproxyConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayChatProxyConfig'
                whoConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.WhoStoryPlayConfig'
                hauntConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.HauntPlayConfig'
                pinConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPinConfig'
                exchangeImageContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageContext'
                turtleSoupContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupContext'
                unmuteContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteContext'
                turtleSoupMassContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupMassContext'
                basePlayContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayBasePlayContext'
                nowShotContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayNowShotContext'
                roastedContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedContext'
                wassupContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayWassupContext'
                capsuleContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayCapsuleContext'
                hideContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayHideContext'
                chatproxyContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayChatProxyContext'
                whoContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.WhoStoryPlayContext'
                hauntContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.HauntPlayContext'
                pinContext:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPinContext'
                enabledPortalId:
                    type: string
                    description: 这是个临时字段，由于开发时间太紧张了，需要在所有的 unlock 接口内返回加入的 portal id 字段，无法通过 接口返回，所以临时加了这个字段，后续需要删除 @Larry
                hauntBooShowInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.HauntBooShowInfo'
                portalBasicInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.PortalBasicInfo'
        api.items.story.types.v1.StoryExchangeImageConditionTemplate:
            type: object
            properties:
                id:
                    type: string
                coverImageUrl:
                    type: string
                    description: 封面图片，创建时，客户端传 key
                defaultCondition:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageCondition'
                typ:
                    enum:
                        - TEMPLATE_TYPE_UNSPECIFIED
                        - TEMPLATE_TYPE_SYSTEM
                        - TEMPLATE_TYPE_USER_DIY
                    type: string
                    format: enum
        api.items.story.types.v1.StoryPinConfig:
            type: object
            properties:
                backgroundImage:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                pinEmojiResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.PinEmojiResource'
                caption:
                    $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
        api.items.story.types.v1.StoryPinContext:
            type: object
            properties:
                isUnlocked:
                    type: boolean
        api.items.story.types.v1.StoryPlayBasePlayConfig:
            type: object
            properties:
                nodes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayBasePlayConfig_Node'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
        api.items.story.types.v1.StoryPlayBasePlayConfig_Node:
            type: object
            properties:
                id:
                    type: string
                    description: 节点 id，客户端自定义，服务端不使用
                resource:
                    $ref: '#/components/schemas/api.items.story.types.v1.Resource'
                attachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                    description: TODO，需要在视频或者图片上增加的文字，位置，大小等信息 具体结构和 @fanqi 确认
        api.items.story.types.v1.StoryPlayBasePlayContext:
            type: object
            properties:
                currentNodeId:
                    type: string
                    description: 当前节点 id
                currentNodeIndex:
                    type: integer
                    description: 当前节点 index
                    format: uint32
                isUnlocked:
                    type: boolean
                    description: 是否解锁
        api.items.story.types.v1.StoryPlayBasePlayExample:
            type: object
            properties:
                coverImageUrl:
                    type: string
        api.items.story.types.v1.StoryPlayCapsuleConfig:
            type: object
            properties:
                coverImage:
                    $ref: '#/components/schemas/api.items.story.types.v1.Resource'
                video:
                    $ref: '#/components/schemas/api.items.story.types.v1.Resource'
                aiScripts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.CapsuleAIScript'
                    description: AI 脚本
                photoInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.CapsulePhotoInfo'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
            description: Capsule 玩法配置
        api.items.story.types.v1.StoryPlayCapsuleContext:
            type: object
            properties:
                isConsumed:
                    type: boolean
                    description: 是否消费过此 story
            description: Capsule 玩法上下文
        api.items.story.types.v1.StoryPlayCapsuleExample:
            type: object
            properties:
                commonInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.ExampleCommonInfo'
                photoInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.CapsulePhotoInfo'
            description: Capsule 玩法示例
        api.items.story.types.v1.StoryPlayChatProxyCaption:
            type: object
            properties:
                content:
                    type: string
                descrption:
                    type: string
                ttsAudioUrl:
                    type: string
                    description: 客户端创建时不传，服务端自动根据 content 进行生成
                words:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Word'
                    description: 客户端创建时不传，服务端自动根据 content 进行生成
        api.items.story.types.v1.StoryPlayChatProxyConfig:
            type: object
            properties:
                caption:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayChatProxyCaption'
                greeting:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayChatProxyGreeting'
                topics:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayChatProxyTopic'
                cover:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                coverAttachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                unlockResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                unlockResourceAttachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
            description: chatproxy 玩法配置
        api.items.story.types.v1.StoryPlayChatProxyContext:
            type: object
            properties:
                isUnlocked:
                    type: boolean
                    description: 是否解锁过此 story
            description: hide 玩法上下文
        api.items.story.types.v1.StoryPlayChatProxyGreeting:
            type: object
            properties:
                content:
                    type: string
                ttsAudioUrl:
                    type: string
                    description: 客户端创建时不传，服务端自动根据 content 进行生成
                words:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Word'
                    description: 客户端创建时不传，服务端自动根据 content 进行生成
        api.items.story.types.v1.StoryPlayChatProxyTopic:
            type: object
            properties:
                id:
                    type: string
                content:
                    type: string
        api.items.story.types.v1.StoryPlayExchangeImageCondition:
            type: object
            properties:
                tips:
                    type: string
                    description: 提示
                positiveGuide:
                    type: string
                    description: 正向引导
                positiveImageUrls:
                    type: array
                    items:
                        type: string
                    description: 正向引导图片
                positiveImageKeys:
                    type: array
                    items:
                        type: string
                    description: 正向引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
                partialMatchGuide:
                    type: string
                    description: 部分匹配引导
                partialMatchImageUrls:
                    type: array
                    items:
                        type: string
                    description: 部分匹配引导图片
                partialMatchImageKeys:
                    type: array
                    items:
                        type: string
                    description: 部分匹配引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
                negativeGuide:
                    type: string
                    description: 负向引导
                negativeImageUrls:
                    type: array
                    items:
                        type: string
                    description: 负向引导图片
                negativeImageKeys:
                    type: array
                    items:
                        type: string
                    description: 负向引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
                llmPrompt:
                    type: string
                    description: llm 的 prompt，即 Creator Criteria
            description: 换图玩法解锁条件
        api.items.story.types.v1.StoryPlayExchangeImageConfig:
            type: object
            properties:
                playMode:
                    type: string
                    description: 换图玩法类型，参考 ExchangeImagePlayMode
                nodes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageConfig_Node'
                    description: 节点列表，按照先后顺序排序，其中，节点的 next_node_id 为空时，表示该节点为终点 如果 play mode 为马赛克时，此数组应该只有一个 element
                commonPlayConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.CommonPlayConfig'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
            description: 换图玩法配置
        api.items.story.types.v1.StoryPlayExchangeImageConfig_Node:
            type: object
            properties:
                id:
                    type: string
                    description: 节点 id，由端上写入时确保唯一
                thumbnailUrl:
                    type: string
                    description: V1 视频首帧，当且仅当资源类型为视频时才有，已废弃，使用resource
                resourceType:
                    type: string
                    description: V1 资源类型，参考 ResourceType，已废弃，使用resource
                resourceUrl:
                    type: string
                    description: V1 资源 url，创建时，客户端传 key，已废弃，使用resource
                condition:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageCondition'
                allowUseAlbum:
                    type: boolean
                    description: 是否允许使用相册
                maxTryCount:
                    type: integer
                    description: 最多允许尝试多少次
                    format: uint32
                needCreateDiyTemplate:
                    type: boolean
            description: 换图玩法里的节点
        api.items.story.types.v1.StoryPlayExchangeImageContext:
            type: object
            properties:
                currentNodeId:
                    type: string
                    description: 用户当前玩到的 node id 主要是为了服务线性模式下，用户处于的状态 如果马赛克模式，此字段为固定值，永远为第一个 node id
                currentTryCount:
                    type: object
                    additionalProperties:
                        type: integer
                        format: uint32
                    description: 用户在当前 story 下的各个node尝试次数 key 为 node_id，value 为尝试次数 all values 的和应该等于当前 story 的总游玩次数
                currentSuccessProgress:
                    type: object
                    additionalProperties:
                        type: integer
                        format: uint32
                    description: 用户在各个 node 下的完成率 如果是换图模式，用户完成了这个 node，则固定为 1，否则为 0  如果是马赛克模式，就可以表达马赛克的消除程度
                isFinished:
                    type: boolean
                    description: 是否已经通关
                userExchangeImageUrls:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageContext_UserExchangeImageUrls'
                    description: 用户在各个 node 下的尝试记录
                tryCount:
                    type: integer
                    description: 尝试次数
                    format: uint32
                userTrialImageUrls:
                    type: array
                    items:
                        type: string
                userTrialImageKeys:
                    type: array
                    items:
                        type: string
            description: 换图玩法上下文
        api.items.story.types.v1.StoryPlayExchangeImageContext_UserExchangeImageUrls:
            type: object
            properties:
                nodeId:
                    type: string
                imageUrls:
                    type: array
                    items:
                        type: string
            description: 用户在各个 node 下的尝试记录
        api.items.story.types.v1.StoryPlayExchangeImageExample:
            type: object
            properties:
                commonInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.ExampleCommonInfo'
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageExample_Case'
                    description: 待交换的图片 url
        api.items.story.types.v1.StoryPlayExchangeImageExample_Case:
            type: object
            properties:
                exchangeImageUrl:
                    type: string
                    description: 待交换的图片 url
                aiResponse:
                    type: string
                    description: ai 的回复
        api.items.story.types.v1.StoryPlayHideConfig:
            type: object
            properties:
                stickerWithTriggerTypes:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StickerWithTriggerType'
                backgroundImage:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                attachmentTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                allStickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                    description: 创作侧忽略，此值是消费侧使用，会根据创作 sticker_with_trigger_types 计算而来
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
                cover:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
            description: hide 玩法配置
        api.items.story.types.v1.StoryPlayHideContext:
            type: object
            properties:
                isConsumed:
                    type: boolean
                    description: 是否消费过此 story
                unlockedStickers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                    description: 解锁获得的贴纸
            description: hide 玩法上下文
        api.items.story.types.v1.StoryPlayNowShotConfig:
            type: object
            properties:
                caption:
                    type: string
                    description: Begin Deprecated，实现完api之后删除 @dexin 显示在屏幕中间的caption
                resourceType:
                    type: string
                    description: 封面资源类型，参考 ResourceType
                resourceUrl:
                    type: string
                    description: 封面资源 url，创建时，客户端传 key
                thumbnailUrl:
                    type: string
                    description: 如果封面资源是视频时，需要传一个视频首帧，创作时给 key
                endResourceType:
                    type: string
                    description: 需要解锁的资源类型，参考 ResourceType
                endResourceUrl:
                    type: string
                    description: 需要解锁的资源 url，创建时，客户端传 key
                endThumbnailUrl:
                    type: string
                    description: 如果需要解锁的资源是视频时，需要传一个视频首帧，创作时给 key
                ttl:
                    type: integer
                    description: 倒计时时间 time to live，单位秒，默认1分钟
                    format: uint32
                commonConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.CommonPlayConfig'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
            description: NowShot 玩法配置
        api.items.story.types.v1.StoryPlayNowShotContext:
            type: object
            properties:
                status:
                    enum:
                        - CONSUME_STATUS_UNSPECIFIED
                        - CONSUME_STATUS_LOCK
                        - CONSUME_STATUS_UNLOCK
                        - CONSUME_STATUS_FAILED
                    type: string
                    format: enum
                startTime:
                    type: integer
                    description: 开始倒计时时间
                    format: uint32
                userSubmitImageUrls:
                    type: array
                    items:
                        type: string
                    description: 用户的尝试记录, deprecated @dexin
                resource:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Resource'
                    description: 用户的尝试记录, v2使用这个字段 @dexin
                ttl:
                    type: integer
                    description: TTL字段，可选，单位秒，用于存储用户自定义的倒计时时间
                    format: uint32
            description: NowShot 玩法上下文
        api.items.story.types.v1.StoryPlayNowShotExample:
            type: object
            properties:
                commonInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.ExampleCommonInfo'
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayNowShotExample_Case'
            description: NowShot 玩法示例
        api.items.story.types.v1.StoryPlayNowShotExample_Case:
            type: object
            properties:
                coverImageUrl:
                    type: string
                    description: 示例封面图片
        api.items.story.types.v1.StoryPlayRoastedConfig:
            type: object
            properties:
                topic:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedTopic'
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                cover:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
                    description: moment create attrs
        api.items.story.types.v1.StoryPlayRoastedContext:
            type: object
            properties:
                isConsumed:
                    type: boolean
                    description: 是否消费过此 story
        api.items.story.types.v1.StoryPlayRoastedExample:
            type: object
            properties:
                topic:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedTopic'
        api.items.story.types.v1.StoryPlayRoastedTopic:
            type: object
            properties:
                greeting:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedTopic_BotAnnouncement'
                ending:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedTopic_BotAnnouncement'
                questions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.RoastedQuestion'
                    description: 问题列表，至少有一个问题
                maxFollowup:
                    type: integer
                    description: 最大问题数, 按60%：20%：20%返回3:4:5
                    format: uint32
        api.items.story.types.v1.StoryPlayRoastedTopic_BotAnnouncement:
            type: object
            properties:
                content:
                    type: string
                    description: 播报的内容
                ttsAudioKey:
                    type: string
                    description: 播报的音频资源, 创建的时候必须传此值
                ttsAudioUrl:
                    type: string
                    description: 播报的音频资源 url，创建的时候不需要传此值
            description: 纯 bot 播报，用于开场白和结束语
        api.items.story.types.v1.StoryPlayTurtleConditionTemplate:
            type: object
            properties:
                id:
                    type: string
                summary:
                    type: string
                coverImageUrl:
                    type: string
                    description: 封面图片，创建时，客户端传 key
                caption:
                    type: string
                intentPrompt:
                    type: string
                typ:
                    enum:
                        - TEMPLATE_TYPE_UNSPECIFIED
                        - TEMPLATE_TYPE_SYSTEM
                        - TEMPLATE_TYPE_USER_DIY
                    type: string
                    format: enum
        api.items.story.types.v1.StoryPlayTurtleSoupConfig:
            type: object
            properties:
                caption:
                    type: string
                    description: 海龟汤玩法里的节点
                intentPrompt:
                    type: string
                resourceType:
                    type: string
                    description: 资源类型，参考 ResourceType
                resourceUrl:
                    type: string
                    description: 资源 url，创建时，客户端传 key
                endResourceUrl:
                    type: string
                    description: 结束资源 url，创建时，客户端传 key
                endResourceType:
                    type: string
                    description: 结束资源类型，参考 ResourceType
                endMessage:
                    type: string
                    description: 结束资源显示的文字
                endMessageFont:
                    type: string
                    description: 结束资源显示的文字字体
                thumbnailUrl:
                    type: string
                    description: 如果资源是视频时，需要传一个视频首帧，创作时给 key
                endThumbnailUrl:
                    type: string
                    description: 如果结束资源是是视频时，需要传一个视频首帧，创作时给 key
                customAiResponses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupConfig_CustomAiResponse'
                templateId:
                    type: string
                    description: 模板 id
                needCreateDiyTemplate:
                    type: boolean
                    description: 是否需要创建自定义模板
                maxTryCount:
                    type: integer
                    description: 最大尝试次数
                    format: uint32
                commonPlayConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.CommonPlayConfig'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
            description: 海龟汤玩法配置
        api.items.story.types.v1.StoryPlayTurtleSoupConfig_CustomAiResponse:
            type: object
            properties:
                ruleDescription:
                    type: string
                ruleResult:
                    type: string
            description: 自定义 ai 规则
        api.items.story.types.v1.StoryPlayTurtleSoupContext:
            type: object
            properties:
                isFinished:
                    type: boolean
                    description: 是否已经完成
                hitWords:
                    type: array
                    items:
                        type: string
                    description: 命中的 words，当且仅当 is_finished 为 false 时，此字段有效
                tips:
                    type: string
                    description: 显示在屏幕中间的 tips
                aiResponse:
                    type: string
                    description: ai 的回复
                tryCount:
                    type: integer
                    description: 尝试次数
                    format: uint32
            description: 海龟汤玩法上下文
        api.items.story.types.v1.StoryPlayTurtleSoupExample:
            type: object
            properties:
                commonInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.ExampleCommonInfo'
                endMessage:
                    type: string
                    description: 隐藏意图，用于帮助 hit_words 命中
                endMessageFont:
                    type: string
                    description: 隐藏意图字体
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupExample_Case'
                    description: 用户发送的消息示例
            description: 海龟汤玩法示例
        api.items.story.types.v1.StoryPlayTurtleSoupExample_Case:
            type: object
            properties:
                tips:
                    type: string
                userMessage:
                    type: string
                    description: 用户发送的消息示例
                hitWords:
                    type: array
                    items:
                        type: string
                    description: 示例的命中词
                aiResponse:
                    type: string
                    description: ai 的回复
        api.items.story.types.v1.StoryPlayTurtleSoupMassConfig:
            type: object
            properties:
                caption:
                    type: string
                    description: 海龟汤玩法里的节点
                intentPrompt:
                    type: string
                resourceType:
                    type: string
                    description: 资源类型，参考 ResourceType
                resourceUrl:
                    type: string
                    description: 资源 url，创建时，客户端传 key
                thumbnailUrl:
                    type: string
                    description: 马赛克玩法的海龟汤，只有一个资源 如果资源是视频时，需要传一个视频首帧，创作时给 key
                customAiResponses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupMassConfig_CustomAiResponse'
                templateId:
                    type: string
                    description: 模板 id
                needCreateDiyTemplate:
                    type: boolean
                    description: 是否需要创建自定义模板
                maxTryCount:
                    type: integer
                    description: 最大尝试次数
                    format: uint32
            description: 与海龟汤类似，只是没有 end_resource_url 和 end_resource_type，为了不混淆旧的数据结构，新开了类型
        api.items.story.types.v1.StoryPlayTurtleSoupMassConfig_CustomAiResponse:
            type: object
            properties:
                ruleDescription:
                    type: string
                ruleResult:
                    type: string
            description: 自定义 ai 规则
        api.items.story.types.v1.StoryPlayTurtleSoupMassContext:
            type: object
            properties:
                isFinished:
                    type: boolean
                    description: 是否已经完成
                tips:
                    type: string
                    description: 显示在屏幕中间的 tips
                aiResponse:
                    type: string
                    description: ai 的回复
                tryCount:
                    type: integer
                    description: 尝试次数
                    format: uint32
        api.items.story.types.v1.StoryPlayTurtleSoupMassExample:
            type: object
            properties:
                commonInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.ExampleCommonInfo'
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupMassExample_Case'
        api.items.story.types.v1.StoryPlayTurtleSoupMassExample_Case:
            type: object
            properties:
                tips:
                    type: string
                userMessage:
                    type: string
                aiResponse:
                    type: string
        api.items.story.types.v1.StoryPlayUnmuteCondition:
            type: object
            properties:
                prompt:
                    type: string
        api.items.story.types.v1.StoryPlayUnmuteConditionTemplate:
            type: object
            properties:
                id:
                    type: string
                defaultCondition:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteCondition'
                typ:
                    enum:
                        - TEMPLATE_TYPE_UNSPECIFIED
                        - TEMPLATE_TYPE_SYSTEM
                        - TEMPLATE_TYPE_USER_DIY
                    type: string
                    format: enum
                coverImageUrl:
                    type: string
                    description: 封面图片，创建时，客户端传 key
        api.items.story.types.v1.StoryPlayUnmuteConfig:
            type: object
            properties:
                prompt:
                    $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                resourceType:
                    type: string
                    description: 资源类型，参考 ResourceType
                resourceUrl:
                    type: string
                    description: 资源 url,第一段（封面内容）：公开展示，用于吸引点击；给出引导;创建时，客户端传 key
                endResourceType:
                    type: string
                    description: 结束资源类型，参考 ResourceType
                endResourceUrl:
                    type: string
                    description: 结束资源 url，第二段（隐藏内容）：默认打码，需通过语音互动完成后自动揭晓;创建时，客户端传 key
                customAiResponses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteConfig_CustomAiResponse'
                maxTryCount:
                    type: integer
                    description: 最大尝试次数
                    format: uint32
                thumbnailUrl:
                    type: string
                    description: 视频首帧，当且仅当资源类型为视频时才有
                endThumbnailUrl:
                    type: string
                    description: 结束视频首帧，当且仅当资源类型为视频时才有
                intention:
                    type: string
                    description: intention for llm
                commonPlayConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.CommonPlayConfig'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
        api.items.story.types.v1.StoryPlayUnmuteConfig_CustomAiResponse:
            type: object
            properties:
                ruleDescription:
                    type: string
                ruleResult:
                    type: string
            description: 自定义 ai 规则
        api.items.story.types.v1.StoryPlayUnmuteContext:
            type: object
            properties:
                isFinished:
                    type: boolean
                    description: 是否已经完成
                aiResponse:
                    type: string
                    description: ai 的回复
                tryCount:
                    type: integer
                    description: 尝试次数
                    format: uint32
                audioKeys:
                    type: array
                    items:
                        type: string
                    description: 历史尝试音频
            description: Unmute 玩法上下文
        api.items.story.types.v1.StoryPlayUnmuteExample:
            type: object
            properties:
                commonInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.ExampleCommonInfo'
                cases:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteExample_Case'
        api.items.story.types.v1.StoryPlayUnmuteExample_Case:
            type: object
            properties:
                prompt:
                    type: string
        api.items.story.types.v1.StoryPlayWassupBotMessage:
            type: object
            properties:
                content:
                    type: string
                ttsAudioUrl:
                    type: string
                    description: 客户端创建时不传，服务端自动根据 content 进行生成
                words:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.Word'
                    description: 客户端创建时不传，服务端自动根据 content 进行生成
        api.items.story.types.v1.StoryPlayWassupConfig:
            type: object
            properties:
                unlockResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                coverImageTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                coverImageResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                isMassCover:
                    type: boolean
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
        api.items.story.types.v1.StoryPlayWassupContext:
            type: object
            properties:
                isUnlocked:
                    type: boolean
        api.items.story.types.v1.StoryStats:
            type: object
            properties:
                reactionMadeStatSummary:
                    $ref: '#/components/schemas/api.items.story.reaction.types.v1.ReactionMadeStatSummary'
                shareStat:
                    type: integer
                    description: Share count
                    format: uint32
        api.items.story.types.v1.StorySummary:
            type: object
            properties:
                id:
                    type: string
                    description: 创作时，不要传
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                playType:
                    type: string
                    description: 游玩类型，参考 StoryPlayType
                subPlayType:
                    type: string
                    description: 游玩模式，换图玩法时，参考 ExchangeImagePlayMode 当且仅当换图玩法时，此字段有效
                status:
                    enum:
                        - STORY_STATUS_UNSPECIFIED
                        - STORY_STATUS_PUBLISHED
                        - STORY_STATUS_DELETED
                        - STORY_STATUS_GENERATING
                        - STORY_STATUS_GENERATION_FAILED
                    type: string
                    format: enum
                createdAtTimestamp:
                    type: integer
                    format: uint32
                exchangeImageExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageExample'
                turtleSoupExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupExample'
                unmuteExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteExample'
                turtleSoupMassExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupMassExample'
                basePlayExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayBasePlayExample'
                coverImageUrl:
                    type: string
                    description: 给站外分享用的封面图
                coverImageWidth:
                    type: integer
                    format: uint32
                coverImageHeight:
                    type: integer
                    format: uint32
                coverType:
                    type: string
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.types.v1.PrivacySetting'
                nowShotPlayExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayNowShotExample'
                roastedExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedExample'
                capsuleExample:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayCapsuleExample'
                stats:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryStats'
                loginUserMadeReactions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.reaction.types.v1.Reaction'
                unlockedUsers:
                    $ref: '#/components/schemas/api.items.story.types.v1.StorySummary_UnlockedUsersInfo'
                hasUnlocked:
                    type: boolean
                sortRequestId:
                    type: string
                    description: 当且仅当这个对象被推荐引擎露出时，才有此字段
        api.items.story.types.v1.StorySummary_UnlockedUsersInfo:
            type: object
            properties:
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                totalCount:
                    type: integer
                    format: uint32
                hasMore:
                    type: boolean
        api.items.story.types.v1.WhoStoryPlayConfig:
            type: object
            properties:
                unlockResourceTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                unlockResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                coverResourceTexts:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                coverResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                optionUserIds:
                    type: array
                    items:
                        type: string
                    description: 可选型，如果作者自己选择了备选，那么创作的时候，传入两个值即可； 如果创作者不选，那么传空 服务端会始终把创作者设为选项之一，客户端可以不传
                options:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.WhoStoryPlayOption'
                    description: options 是服务端根据 option_user_ids 进行的渲染结构 创作时，客户端不用传递此参数
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
            description: who 玩法配置
        api.items.story.types.v1.WhoStoryPlayContext:
            type: object
            properties:
                triedPoints:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.WhoStoryTryPoint'
                    description: 用户尝试点击过的位置
                enabledOptions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                    description: 目前可用的选项
                isUnlocked:
                    type: boolean
                    description: 是否已经成功了
                isConsumed:
                    type: boolean
                    description: 是否尝试过
                triedCount:
                    type: integer
                    description: 尝试次数
                    format: uint32
                maxTryCount:
                    type: integer
                    description: 最大尝试次数
                    format: uint32
        api.items.story.types.v1.WhoStoryPlayOption:
            type: object
            properties:
                correct:
                    type: boolean
                option:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
        api.items.story.types.v1.WhoStoryTryPoint:
            type: object
            properties:
                x:
                    type: string
                y:
                    type: string
        api.items.story.types.v1.Word:
            type: object
            properties:
                startTime:
                    type: integer
                    description: 起始时间,距离音频开始的毫秒偏移值。
                    format: uint32
                endTime:
                    type: integer
                    description: 结束时间,距离音频开始的毫秒偏移值。
                    format: uint32
                text:
                    type: string
                    description: 文本
        api.items.story.v1.ConsumeBasePlayStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                currentNodeIdx:
                    type: integer
                    description: 消费到第几个 node 了，从 0 开始
                    format: uint32
        api.items.story.v1.ConsumeBasePlayStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ConsumeCapsuleStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                sendToAuthor:
                    type: boolean
                    description: 把此视频发给原作者
                savedStoryId:
                    type: string
                    description: 保存为一个新的 story id
        api.items.story.v1.ConsumeCapsuleStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ConsumeExchangeImageStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                imageKey:
                    type: string
        api.items.story.v1.ConsumeExchangeImageStoryResponse:
            type: object
            properties:
                matchStatus:
                    type: string
                    description: 匹配状态，参考 api.items.story.types.v1.ExchangeImageMatchStatus
                aiResponse:
                    type: string
                    description: ai 回复
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ConsumeNowShotStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                userResourceKey:
                    type: string
                    description: 资源 url，创建时，客户端传 key
                startTime:
                    type: integer
                    format: uint32
                resourceType:
                    type: string
                    description: 封面资源类型，参考 ResourceType
                thumbnailUrl:
                    type: string
                    description: 如果资源是视频时，需要传一个视频首帧，创作时给 key
        api.items.story.v1.ConsumeNowShotStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ConsumeRoastedStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                userRecordedVideoKey:
                    type: string
                    description: 可选型，如果传了，会把此视频发给原作者
                userStoryId:
                    type: string
                    description: v2 中，客户端会将消费的视频保存成一个 story，服务端将story发给作者，并更新 story 的 privacy setting 使作者可以看到
        api.items.story.v1.ConsumeRoastedStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ConsumeRoastedTopicRequest:
            type: object
            properties:
                questionWithUserAnswer:
                    $ref: '#/components/schemas/api.items.story.types.v1.RoastedQuestionWithUserAnswer'
                fromStoryId:
                    type: string
                    description: 可选型，如果传了，则视为消费了这个 story
                questionCount:
                    type: integer
                    description: 在当前节点中，这是第几个追问，如果不是追问，则传 0
                    format: uint32
                totalVideoDurationSeconds:
                    type: integer
                    description: 目前用户总共拍摄了多久的视频，单位秒
                    format: uint32
        api.items.story.v1.ConsumeRoastedTopicResponse:
            type: object
            properties:
                nextQuestion:
                    $ref: '#/components/schemas/api.items.story.types.v1.RoastedQuestion'
        api.items.story.v1.ConsumeTurtleSoupStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                userMessage:
                    type: string
        api.items.story.v1.ConsumeTurtleSoupStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ConsumeUnmuteStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                userAudioKey:
                    type: string
        api.items.story.v1.ConsumeUnmuteStoryResponse:
            type: object
            properties:
                matchStatus:
                    type: string
                    description: 匹配状态，参考 api.items.story.types.v1.UnmuteMatchStatus
                aiResponse:
                    type: string
                    description: ai 回复
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.CopilotCapsuleStoryRequest:
            type: object
            properties:
                questionWithUserAnswer:
                    $ref: '#/components/schemas/api.items.story.types.v1.CapsuleQuestionWithUserAnswer'
                image:
                    $ref: '#/components/schemas/api.items.story.types.v1.ImageDetail'
                questionCount:
                    type: integer
                    description: 在当前节点中，这是第几个追问，如果不是追问，则传 0
                    format: uint32
                totalVideoDurationSeconds:
                    type: integer
                    description: 目前用户总共拍摄了多久的视频，单位秒
                    format: uint32
            description: 引导创作 capsule story 的请求
        api.items.story.v1.CopilotCapsuleStoryResponse:
            type: object
            properties:
                question:
                    $ref: '#/components/schemas/api.items.story.types.v1.CapsuleQuestion'
        api.items.story.v1.CreateBasePlayStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayBasePlayConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布baseplay的 story 请求
        api.items.story.v1.CreateBasePlayStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
            description: 发布baseplay的 story 响应
        api.items.story.v1.CreateCapsuleStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayCapsuleConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布 capsule 的 story 请求
        api.items.story.v1.CreateCapsuleStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.CreateExchangeImageStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布 story 的请求
        api.items.story.v1.CreateExchangeImageStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.CreateNowShotStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayNowShotConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布 nowshot 的 story 请求
        api.items.story.v1.CreateNowShotStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
            description: 发布 nowshot 的 story 响应
        api.items.story.v1.CreateRoastedStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
                fromStoryId:
                    type: string
                    description: 从哪个 story 过来创建的
            description: 发布 roasted 的 story 请求
        api.items.story.v1.CreateRoastedStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.CreateTurtleSoupStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupConfig'
                isMass:
                    type: boolean
                    description: 是否是马赛克玩法
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布海龟汤的 story 请求
        api.items.story.v1.CreateTurtleSoupStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
            description: 发布海龟汤的 story 响应
        api.items.story.v1.CreateUnmuteStoryRequest:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布unmute的 story 请求
        api.items.story.v1.CreateUnmuteStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
            description: 发布unmute的 story 响应
        api.items.story.v1.DeleteStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                    description: only number
            description: 删除 story 的请求
        api.items.story.v1.GetRoastedTopicsRequest:
            type: object
            properties:
                copyStoryId:
                    type: string
                    description: 如果传了，目前会直接返回对方的 story 的 topics
            description: 获取一个 roasted 的 topics
        api.items.story.v1.GetRoastedTopicsResponse:
            type: object
            properties:
                topic:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayRoastedTopic'
        api.items.story.v1.GetStoryDetailRequest:
            type: object
            properties:
                id:
                    type: string
        api.items.story.v1.GetStoryDetailResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ListCommonStoryConditionTemplatesRequest:
            type: object
            properties:
                storyPlayType:
                    enum:
                        - STORY_PLAY_TYPE_UNSPECIFIED
                        - STORY_PLAY_TYPE_EXCHANGE_IMAGE
                        - STORY_PLAY_TYPE_TURTLE_SOUP
                        - STORY_PLAY_TYPE_UNMUTE
                        - STORY_PLAY_TYPE_TURTLE_SOUP_MASS
                        - STORY_PLAY_TYPE_BASE_PLAY
                        - STORY_PLAY_TYPE_NOW_SHOT
                        - STORY_PLAY_TYPE_ROASTED
                        - STORY_PLAY_TYPE_CAPSULE
                        - STORY_PLAY_TYPE_HIDE
                        - STORY_PLAY_TYPE_CHATPROXY
                        - STORY_PLAY_TYPE_WHO
                        - STORY_PLAY_TYPE_WASSUP_V2
                        - STORY_PLAY_TYPE_HAUNT
                        - STORY_PLAY_TYPE_PIN
                    type: string
                    format: enum
        api.items.story.v1.ListCommonStoryConditionTemplatesResponse:
            type: object
            properties:
                conditionTemplates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.CommonStoryPlayConditionTemplate'
        api.items.story.v1.ListCreatorStoryRequest:
            type: object
            properties:
                creatorId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.story.v1.ListCreatorStoryResponse:
            type: object
            properties:
                createdStories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.v1.ListCreatorStoryResponse_CreatedStory'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.v1.ListCreatorStoryResponse_CreatedStory:
            type: object
            properties:
                story:
                    $ref: '#/components/schemas/api.items.story.types.v1.StorySummary'
                isTop:
                    type: boolean
                    description: 是否置顶
        api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest:
            type: object
            properties: {}
        api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse:
            type: object
            properties:
                conditionTemplates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryExchangeImageConditionTemplate'
        api.items.story.v1.ListSameAuthorStoryWithAnchorRequest:
            type: object
            properties:
                anchorStoryId:
                    type: string
                    description: 锚点 story id
                listRequests:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.v1.ListSameAuthorStoryWithAnchorRequest_ListRequest'
            description: 获取某个 Story 同作者一定时间范围内对的其他 story 通过多个 ListRequest + reverse 来实现同时正反向取多个 story
        api.items.story.v1.ListSameAuthorStoryWithAnchorRequest_ListRequest:
            type: object
            properties:
                filterPlayTypes:
                    type: array
                    items:
                        enum:
                            - STORY_PLAY_TYPE_UNSPECIFIED
                            - STORY_PLAY_TYPE_EXCHANGE_IMAGE
                            - STORY_PLAY_TYPE_TURTLE_SOUP
                            - STORY_PLAY_TYPE_UNMUTE
                            - STORY_PLAY_TYPE_TURTLE_SOUP_MASS
                            - STORY_PLAY_TYPE_BASE_PLAY
                            - STORY_PLAY_TYPE_NOW_SHOT
                            - STORY_PLAY_TYPE_ROASTED
                            - STORY_PLAY_TYPE_CAPSULE
                            - STORY_PLAY_TYPE_HIDE
                            - STORY_PLAY_TYPE_CHATPROXY
                            - STORY_PLAY_TYPE_WHO
                            - STORY_PLAY_TYPE_WASSUP_V2
                            - STORY_PLAY_TYPE_HAUNT
                            - STORY_PLAY_TYPE_PIN
                        type: string
                        format: enum
                    description: 过滤游玩类型，如果传空，则不过滤
                reverse:
                    type: boolean
                    description: 是否时间倒叙
                limit:
                    type: integer
                    description: 取多少个
                    format: uint32
        api.items.story.v1.ListSameAuthorStoryWithAnchorResponse:
            type: object
            properties:
                listResponses:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.v1.ListSameAuthorStoryWithAnchorResponse_ListResponse'
                    description: 有多少个 ListRequests 就有多少个 ListResponse
        api.items.story.v1.ListSameAuthorStoryWithAnchorResponse_ListResponse:
            type: object
            properties:
                reverse:
                    type: boolean
                    description: 是否倒叙
                stories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v1.ListTurtleSoupStoryConditionTemplatesRequest:
            type: object
            properties: {}
        api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse:
            type: object
            properties:
                conditionTemplates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleConditionTemplate'
        api.items.story.v1.ListUnlockedStoryRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                filterPlayTypes:
                    type: array
                    items:
                        enum:
                            - STORY_PLAY_TYPE_UNSPECIFIED
                            - STORY_PLAY_TYPE_EXCHANGE_IMAGE
                            - STORY_PLAY_TYPE_TURTLE_SOUP
                            - STORY_PLAY_TYPE_UNMUTE
                            - STORY_PLAY_TYPE_TURTLE_SOUP_MASS
                            - STORY_PLAY_TYPE_BASE_PLAY
                            - STORY_PLAY_TYPE_NOW_SHOT
                            - STORY_PLAY_TYPE_ROASTED
                            - STORY_PLAY_TYPE_CAPSULE
                            - STORY_PLAY_TYPE_HIDE
                            - STORY_PLAY_TYPE_CHATPROXY
                            - STORY_PLAY_TYPE_WHO
                            - STORY_PLAY_TYPE_WASSUP_V2
                            - STORY_PLAY_TYPE_HAUNT
                            - STORY_PLAY_TYPE_PIN
                        type: string
                        format: enum
                    description: 过滤类型，如果传空，则不过滤
            description: 获取解锁过的 story 列表
        api.items.story.v1.ListUnlockedStoryResponse:
            type: object
            properties:
                unlockedStories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest:
            type: object
            properties: {}
        api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse:
            type: object
            properties:
                conditionTemplates:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayUnmuteConditionTemplate'
        api.items.story.v1.PrivacySettingUpdateAttr:
            type: object
            properties:
                privacyType:
                    enum:
                        - PRIVACY_TYPE_UNSPECIFIED
                        - PRIVACY_TYPE_PUBLIC
                        - PRIVACY_TYPE_PRIVATE
                        - PRIVACY_TYPE_FRIEND
                        - PRIVACY_TYPE_FOLLOWER
                        - PRIVACY_TYPE_ALLOWLIST
                    type: string
                    format: enum
                visibleBeforeTimestamp:
                    type: integer
                    format: uint32
                allowlistUserIds:
                    type: array
                    items:
                        type: string
                    description: 仅 PrivacyType 为 PRIVACY_TYPE_ALLOWLIST 时生效，白名单范围user ids
        api.items.story.v1.TopStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                    description: only number
                isTop:
                    type: boolean
                    description: 是否置顶
        api.items.story.v1.UpdateStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                    description: only number
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
        api.items.story.v1.UpdateStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.AddCaptureBooIntoMyAssistRequest:
            type: object
            properties:
                booId:
                    type: string
                storyId:
                    type: string
        api.items.story.v2.AddCapturedBooInToCollectedStickersRequest:
            type: object
            properties:
                booId:
                    type: string
        api.items.story.v2.AddCapturedBooInToCollectedStickersResponse:
            type: object
            properties:
                addedCollectedSticker:
                    $ref: '#/components/schemas/api.items.story.v2.AddCapturedBooInToCollectedStickersResponse_CollectedSticker'
        api.items.story.v2.AddCapturedBooInToCollectedStickersResponse_CollectedSticker:
            type: object
            properties:
                sticker:
                    $ref: '#/components/schemas/api.items.story.types.v1.HideSticker'
                collectedAtUnixTimestamp:
                    type: integer
                    format: uint32
                isTop:
                    type: boolean
        api.items.story.v2.AutoGenerateAreaEmojiRequest:
            type: object
            properties:
                backgroundImage:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
        api.items.story.v2.AutoGenerateAreaEmojiResponse:
            type: object
            properties:
                pinEmojiResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.PinEmojiResource'
        api.items.story.v2.ConsumeChatProxyRequestV2:
            type: object
            properties:
                storyId:
                    type: string
                userVideoKey:
                    type: string
                userVideoCoverKey:
                    type: string
        api.items.story.v2.ConsumeChatProxyResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.ConsumeHauntStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                unlocked:
                    type: boolean
                isFromFeedOrFriends:
                    type: boolean
        api.items.story.v2.ConsumeHauntStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.ConsumeHideStoryRequest:
            type: object
            properties:
                stickerIds:
                    type: array
                    items:
                        type: string
                storyId:
                    type: string
        api.items.story.v2.ConsumeHideStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.ConsumeNowShotStoryRequestV2:
            type: object
            properties:
                storyId:
                    type: string
                succeeded:
                    type: boolean
                resource:
                    $ref: '#/components/schemas/api.items.story.types.v1.Resource'
                ttl:
                    type: integer
                    description: TTL字段，可选，单位秒，如果不传则不存储
                    format: uint32
        api.items.story.v2.ConsumePinStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                success:
                    type: boolean
                failedImage:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                successCostSeconds:
                    type: integer
                    format: uint32
        api.items.story.v2.ConsumePinStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.ConsumeRoastedStoryRequestV2:
            type: object
            properties:
                storyId:
                    type: string
        api.items.story.v2.ConsumeRoastedStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.ConsumeWassupStoryRequest:
            type: object
            properties:
                storyId:
                    type: string
                userVideoKey:
                    type: string
                userVideoCoverKey:
                    type: string
        api.items.story.v2.ConsumeWassupStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.ConsumeWhoStoryRequestV2:
            type: object
            properties:
                storyId:
                    type: string
                tryPoints:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.WhoStoryTryPoint'
                    description: 用户尝试的点，如果用户没点击，就不需要上报
                selectedOptionUserId:
                    type: string
                    description: 用户选择的选项，如果用户没选择，就不需要上报
        api.items.story.v2.ConsumeWhoStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateChatProxyStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayChatProxyConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
                fromStoryId:
                    type: string
                    description: 从哪个 story 过来创建的
        api.items.story.v2.CreateChatProxyStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateExchangeImageStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
                fromStoryId:
                    type: string
                    description: 从哪个 story 过来创建的
            description: 发布 reveal story 的请求
        api.items.story.v2.CreateExchangeImageStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateHauntStoryRequest:
            type: object
            properties:
                boosWithQuestionsAndAnswers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.v2.CreateHauntStoryRequest_BooWithQuestionAndAnswer'
                captions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
                momentCreateAttrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.MomentCreateAttr'
                cover:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
        api.items.story.v2.CreateHauntStoryRequest_BooWithQuestionAndAnswer:
            type: object
            properties:
                booId:
                    type: string
                userAvatarId:
                    type: string
                questionsWithAnswers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntQuestionWithAnswer'
        api.items.story.v2.CreateHauntStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateHideStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayHideConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
        api.items.story.v2.CreateHideStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateNowShotStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayNowShotConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
                fromStoryId:
                    type: string
                    description: 从哪个 story 过来创建的
            description: 发布now story
        api.items.story.v2.CreatePinStoryRequest:
            type: object
            properties:
                pinConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPinConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
        api.items.story.v2.CreatePinStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateTurtleSoupStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayTurtleSoupConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布 type story 的请求
        api.items.story.v2.CreateTurtleSoupStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateUnmuteStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayExchangeImageConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
            description: 发布 reveal story 的请求
        api.items.story.v2.CreateUnmuteStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateWassupStoryRequest:
            type: object
            properties:
                config:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayWassupConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
                fromStoryId:
                    type: string
                    description: 从哪个 story 过来创建的
        api.items.story.v2.CreateWassupStoryResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.CreateWhoStoryRequestV2:
            type: object
            properties:
                playConfig:
                    $ref: '#/components/schemas/api.items.story.types.v1.WhoStoryPlayConfig'
                privacySetting:
                    $ref: '#/components/schemas/api.items.story.v1.PrivacySettingUpdateAttr'
        api.items.story.v2.CreateWhoStoryResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.GenerateHideImageMaskRequest:
            type: object
            properties:
                backgroundImageKey:
                    type: string
                points:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.v2.GenerateHideImageMaskRequest_Point'
        api.items.story.v2.GenerateHideImageMaskRequest_Point:
            type: object
            properties:
                x:
                    type: integer
                    format: uint32
                y:
                    type: integer
                    format: uint32
        api.items.story.v2.GenerateHideImageMaskResponse:
            type: object
            properties:
                maskImageBase64:
                    type: string
        api.items.story.v2.GetChatProxyNextTopicRequestV2:
            type: object
            properties:
                storyId:
                    type: string
                userAudioKey:
                    type: string
                roundIndex:
                    type: integer
                    description: 如果是刚开始，则传0
                    format: uint32
        api.items.story.v2.GetChatProxyNextTopicResponseV2:
            type: object
            properties:
                question:
                    $ref: '#/components/schemas/api.items.story.types.v1.ChatProxyQuestion'
                end:
                    type: boolean
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.GetWassupGreetingsRequest:
            type: object
            properties:
                imageKeys:
                    type: array
                    items:
                        type: string
                audioAsrResult:
                    type: string
                    description: 如果视频，那么 image_keys 长度将大于1，且客户端需要把视频音频 asr 结果给到服务端 此值可能为空
        api.items.story.v2.GetWassupGreetingsResponse:
            type: object
            properties:
                greeting:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayWassupBotMessage'
        api.items.story.v2.GetWassupNextQuestionRequest:
            type: object
            properties:
                userAudioKey:
                    type: string
        api.items.story.v2.GetWassupNextQuestionResponse:
            type: object
            properties:
                message:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryPlayWassupBotMessage'
                isEnd:
                    type: boolean
        api.items.story.v2.ImageCheckRequest:
            type: object
            properties:
                imageKey:
                    type: string
                textCondition:
                    type: string
        api.items.story.v2.ImageCheckResponse:
            type: object
            properties:
                pass:
                    type: boolean
                point:
                    type: array
                    items:
                        type: string
                    description: float64 格式
                feedback:
                    type: string
        api.items.story.v2.ListCreatorStoryRequestV2:
            type: object
            properties:
                creatorId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.story.v2.ListCreatorStoryResponseV2:
            type: object
            properties:
                createdStories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.v2.ListCreatorStoryResponseV2_CreatedStory'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.v2.ListCreatorStoryResponseV2_CreatedStory:
            type: object
            properties:
                story:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                isTop:
                    type: boolean
                    description: 是否置顶
        api.items.story.v2.ListFollowingCreatorStoryRequestV2:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
            description: 获取关注的创作者 story 列表
        api.items.story.v2.ListFollowingCreatorStoryResponseV2:
            type: object
            properties:
                followingCreatorStories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                recommendedUnfollowedCreatorStories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                    description: 如果创作者的 story 列表为空，则会返回推荐的 story，用于曝光更多的作品 此数组当且仅当关注的创作者的 story 列表为空时才会返回 且理论上这批推荐的内容作者都是未关注的
                userCreatedPortals:
                    $ref: '#/components/schemas/api.items.portal.types.v1.UserCreatedPortals'
                portals:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
                portalListResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                hauntBooShowInfos:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntBooShowInfo'
                    description: 可能存在的 haunt boo 的展示信息，注意目前是返回数组，但是客户端应该只取第一个 服务端暂时会保证数组也只有一个，后续如果需要支持数组，则需要修改
        api.items.story.v2.ListHauntBooAssistRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.story.v2.ListHauntBooAssistResponse:
            type: object
            properties:
                boosWithQuestionsAndAnswers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntBoo'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.story.v2.ListHauntQuestionsRequest:
            type: object
            properties: {}
        api.items.story.v2.ListHauntQuestionsResponse:
            type: object
            properties:
                questions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntQuestion'
        api.items.story.v2.ListHauntRandomAvatarsRequest:
            type: object
            properties: {}
        api.items.story.v2.ListHauntRandomAvatarsResponse:
            type: object
            properties:
                avatarUrls:
                    type: array
                    items:
                        type: string
        api.items.story.v2.ListHomePageStoryRequestV2:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                useRecommended:
                    type: boolean
                    description: 是否使用推荐引擎
                filterPlayTypes:
                    type: array
                    items:
                        enum:
                            - STORY_PLAY_TYPE_UNSPECIFIED
                            - STORY_PLAY_TYPE_EXCHANGE_IMAGE
                            - STORY_PLAY_TYPE_TURTLE_SOUP
                            - STORY_PLAY_TYPE_UNMUTE
                            - STORY_PLAY_TYPE_TURTLE_SOUP_MASS
                            - STORY_PLAY_TYPE_BASE_PLAY
                            - STORY_PLAY_TYPE_NOW_SHOT
                            - STORY_PLAY_TYPE_ROASTED
                            - STORY_PLAY_TYPE_CAPSULE
                            - STORY_PLAY_TYPE_HIDE
                            - STORY_PLAY_TYPE_CHATPROXY
                            - STORY_PLAY_TYPE_WHO
                            - STORY_PLAY_TYPE_WASSUP_V2
                            - STORY_PLAY_TYPE_HAUNT
                            - STORY_PLAY_TYPE_PIN
                        type: string
                        format: enum
                    description: 过滤类型，如果传空，则不过滤 当且仅当不走推荐引擎时起作用
        api.items.story.v2.ListHomePageStoryResponseV2:
            type: object
            properties:
                stories:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                sortRequestId:
                    type: string
                    description: 推荐引擎的 sort_request_id
                userCreatedPortals:
                    $ref: '#/components/schemas/api.items.portal.types.v1.UserCreatedPortals'
                portals:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.portal.types.v1.Portal'
                portalListResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                hauntBooShowInfos:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.HauntBooShowInfo'
                    description: 可能存在的 haunt boo 的展示信息，注意目前是返回数组，但是客户端应该只取第一个 服务端暂时会保证数组也只有一个，后续如果需要支持数组，则需要修改
        api.items.story.v2.ManualGenerateAreaEmojiRequest:
            type: object
            properties:
                areas:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.PinEmojiArear'
                backgroundImage:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
        api.items.story.v2.ManualGenerateAreaEmojiResponse:
            type: object
            properties:
                pinEmojiResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.story.types.v1.PinEmojiResource'
        api.items.story.v2.ReportHauntShowRequest:
            type: object
            properties:
                booId:
                    type: string
                showInfoSence:
                    enum:
                        - SHOW_INFO_SENCE_UNSPECIFIED
                        - SHOW_INFO_SENCE_FEED_POLLUTION
                        - SHOW_INFO_SENCE_CONTENT_POLLUTION
                    type: string
                    format: enum
            description: 上报一次 haunt boo 的展现
        api.items.story.v2.ReportHauntShowResponse:
            type: object
            properties:
                updatedHauntBooShowInfo:
                    $ref: '#/components/schemas/api.items.story.types.v1.HauntBooShowInfo'
        api.items.story.v2.SendHauntCaptureVideoRequest:
            type: object
            properties:
                storyId:
                    type: string
                videoKey:
                    type: string
                videoCoverKey:
                    type: string
        api.items.story.v2.SendMessageRequest:
            type: object
            properties:
                storyId:
                    type: string
                messageType:
                    enum:
                        - MESSAGE_TYPE_UNSPECIFIED
                        - MESSAGE_TYPE_WASSUP
                        - MESSAGE_TYPE_CHATPROXY
                        - MESSAGE_TYPE_ROASTED
                        - MESSAGE_TYPE_BASEPLAY
                    type: string
                    description: 消息类型，用于区分不同的故事类型; deprecated(该字段不被使用，story类型通过story_id获取)
                    format: enum
                userVideoKey:
                    type: string
                userVideoCoverKey:
                    type: string
                consumerStoryCoverKey:
                    type: string
                    description: 消费者故事封面URL
                consumeStatus:
                    enum:
                        - CONSUME_STATUS_UNSPECIFIED
                        - CONSUME_STATUS_SUCCESSFUL
                        - CONSUME_STATUS_FAILED
                        - CONSUME_STATUS_PARTIAL_SUCCESSFUL
                    type: string
                    format: enum
            description: 发送消息请求
        api.items.story.v2.SendMessageResponse:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.story.v2.StoryDetailResponseV2:
            type: object
            properties:
                storyDetail:
                    $ref: '#/components/schemas/api.items.story.types.v1.StoryDetail'
        api.items.types.v1.ItemDetail:
            type: object
            properties:
                itemSummary:
                    $ref: '#/components/schemas/api.items.types.v1.ItemSummary'
        api.items.types.v1.ItemStat:
            type: object
            properties:
                viewsCount:
                    type: integer
                    description: 浏览数
                    format: uint32
                interactionsCount:
                    type: integer
                    description: 交互数
                    format: uint32
                commentsCount:
                    type: integer
                    description: 评论数
                    format: uint32
                reactionStats:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.types.v1.ItemStat_ReactionStat'
        api.items.types.v1.ItemStat_ReactionStat:
            type: object
            properties:
                type:
                    enum:
                        - REACTION_TYPE_UNSPECIFIED
                        - REACTION_TYPE_LIKE
                    type: string
                    format: enum
                count:
                    type: integer
                    format: uint32
            description: item 交互的统计数
        api.items.types.v1.ItemSummary:
            type: object
            properties:
                id:
                    type: string
                title:
                    type: string
                description:
                    type: string
                coverImageUrl:
                    type: string
                bgm:
                    $ref: '#/components/schemas/api.items.types.v1.ItemSummary_Bgm'
                author:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                publishedAtUnixTimestamp:
                    type: integer
                    description: 发布时间，标准 unix时间戳
                    format: uint32
                status:
                    enum:
                        - ITEM_STATUS_UNSPECIFIED
                        - ITEM_STATUS_NORMAL
                        - ITEM_STATUS_DRAFT
                        - ITEM_STATUS_USER_DELETED
                    type: string
                    description: 状态
                    format: enum
                stat:
                    $ref: '#/components/schemas/api.items.types.v1.ItemStat'
                loginUserReactions:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.reaction.types.v1.ItemReaction'
                    description: 当前登录用户 对 item 的反应记录，数组为空表示无操作，若元素存在，则表示已操作
                sketchboards:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.artwork.ArtSketchBoard'
        api.items.types.v1.ItemSummary_Bgm:
            type: object
            properties:
                song:
                    $ref: '#/components/schemas/api.items.music.types.v1.Song'
                audioPlayUrl:
                    type: string
                    description: 裁剪后的播放对象 url
            description: bgm 音频播放信息
        api.items.v1.AsrRequest:
            type: object
            properties:
                audioObjectKey:
                    type: string
                similarity:
                    type: number
                    description: 相似度阈值，默认0.8，如果客户端传了此值，返回值服务端会自己计算相似度并返回是否匹配，否则返回值里的 matched 无意义
                    format: float
                expectedTxt:
                    type: string
                    description: 期望的文本，如果客户端传了此值，返回值服务端会自己计算相似度并返回是否匹配，否则返回值里的 matched 无意义
        api.items.v1.AsrResponse:
            type: object
            properties:
                text:
                    type: string
                matched:
                    type: boolean
                    description: 是否匹配
        api.items.v1.BatchGetItemSummariesRequest:
            type: object
            properties:
                itemIds:
                    type: array
                    items:
                        type: string
        api.items.v1.BatchGetItemSummariesResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.types.v1.ItemSummary'
        api.items.v1.CreateItemRequest:
            type: object
            properties:
                title:
                    type: string
                description:
                    type: string
                coverImageObjectKey:
                    type: string
                bgm:
                    $ref: '#/components/schemas/api.items.v1.CreateItemRequest_Bgm'
                sketchboards:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.artwork.ArtSketchBoard'
        api.items.v1.CreateItemRequest_Bgm:
            type: object
            properties:
                song:
                    $ref: '#/components/schemas/api.items.music.types.v1.Song'
                audioPlayKey:
                    type: string
                    description: 裁剪后的播放对象 key
            description: bgm 音频播放信息
        api.items.v1.CreateItemResponse:
            type: object
            properties:
                item:
                    $ref: '#/components/schemas/api.items.types.v1.ItemDetail'
        api.items.v1.HomePageTimelineRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.items.v1.HomePageTimelineResponse:
            type: object
            properties:
                timelineCards:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.items.v1.HomePageTimelineResponse_TimelineCard'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.items.v1.HomePageTimelineResponse_TimelineCard:
            type: object
            properties:
                tag:
                    enum:
                        - CARD_TAG_UNSPECIFIED
                        - CARD_TAG_FRIEND
                    type: string
                    description: 用于目前 timeline 右上角显示 tag 信息
                    format: enum
                item:
                    $ref: '#/components/schemas/api.items.types.v1.ItemSummary'
        api.items.v1.ReportShareStatRequest:
            type: object
            properties:
                storyId:
                    type: string
        api.items.v1.ReportShareStatResponse:
            type: object
            properties: {}
        api.push.v1.RegisterPushTokenRequest:
            type: object
            properties:
                token:
                    type: string
                tokenType:
                    enum:
                        - PUSH_TOKEN_TYPE_UNSPECIFIED
                        - PUSH_TOKEN_TYPE_FIREBASE
                    type: string
                    format: enum
                deviceId:
                    type: string
        api.push.v1.TestPushRequest:
            type: object
            properties:
                userIds:
                    type: array
                    items:
                        type: string
                    description: 用户 id
                title:
                    type: string
                    description: 标题
                content:
                    type: string
                    description: 内容
                imageUrl:
                    type: string
                    description: 图片地址
                jumpScheme:
                    type: string
                    description: 跳转 scheme
        api.push.v1.TestPushResponse:
            type: object
            properties:
                result:
                    type: string
                    description: 结果
        api.resource.types.v1.Resource:
            type: object
            properties:
                resourceType:
                    enum:
                        - RESOURCE_TYPE_UNSPECIFIED
                        - RESOURCE_TYPE_IMAGE
                        - RESOURCE_TYPE_VIDEO
                        - RESOURCE_TYPE_AUDIO
                    type: string
                    format: enum
                objectKey:
                    type: string
                    description: 此值用于标记唯一资源
                objectAccessUrl:
                    type: string
                    description: 上传时不填
                coverImageObjectKey:
                    type: string
                    description: 如果是图片类型资源，选填： 1. 如果不填，服务端会自动图片本身作为 cover image 2. 如果填了，则使用填写的图片作为 cover image 如果是视频资源，则必填
                coverImageObjectAccessUrl:
                    type: string
                    description: 上传时不填 如果是图片类型资源，则自动图片本身作为 cover image
                coverImageWidth:
                    type: integer
                    description: 上传时可选，如果不填，则不返回
                    format: uint32
                coverImageHeight:
                    type: integer
                    description: 上传时可选，如果不填，则不返回
                    format: uint32
                scenario:
                    enum:
                        - SCENARIO_UNSPECIFIED
                        - SCENARIO_USER_AVATAR
                        - SCENARIO_ITEM_COVER
                        - SCENARIO_MUSIC
                        - SCENARIO_MUSIC_COVER
                        - SCENARIO_BGM
                        - SCENARIO_STORY_RESOURCE
                        - SCENARIO_FIZZ_RESOURCE
                        - SCENARIO_IM_RESOURCE
                        - SCENARIO_BOO_RESOURCE
                    type: string
                    description: 预留字段，暂时不写入
                    format: enum
                contentType:
                    enum:
                        - CONTENT_TYPE_UNSPECIFIED
                        - CONTENT_TYPE_IMAGE_APNG
                        - CONTENT_TYPE_IMAGE_PNG
                        - CONTENT_TYPE_IMAGE_JPEG
                        - CONTENT_TYPE_IMAGE_JPG
                        - CONTENT_TYPE_IMAGE_GIF
                        - CONTENT_TYPE_IMAGE_WEBP
                        - CONTENT_TYPE_TEXT_JSON
                        - CONTENT_TYPE_MINIGAME_BUNDLE
                        - CONTENT_TYPE_MINIGAME_SCENE_ZIP
                        - CONTENT_TYPE_MP3
                        - CONTENT_TYPE_AUDIO_WAV
                        - CONTENT_TYPE_AUDIO_OGG
                        - CONTENT_TYPE_AUDIO_FLAC
                        - CONTENT_TYPE_AUDIO_APE
                        - CONTENT_TYPE_AUDIO_WMA
                        - CONTENT_TYPE_AUDIO_MP4
                        - CONTENT_TYPE_VIDEO_MP4
                        - CONTENT_TYPE_VIDEO_AVI
                        - CONTENT_TYPE_VIDEO_MOV
                        - CONTENT_TYPE_VIDEO_WMV
                        - CONTENT_TYPE_VIDEO_FLV
                        - CONTENT_TYPE_IMAGE_BMP
                        - CONTENT_TYPE_IMAGE_TIFF
                        - CONTENT_TYPE_AUDIO_M4A
                        - CONTENT_TYPE_ZIP
                    type: string
                    format: enum
        api.resource.v1.BatchGetPutObjectPreSignUrlRequest:
            type: object
            properties:
                resources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.resource.v1.BatchGetPutObjectPreSignUrlRequest_Resource'
        api.resource.v1.BatchGetPutObjectPreSignUrlRequest_Resource:
            type: object
            properties:
                scenario:
                    enum:
                        - SCENARIO_UNSPECIFIED
                        - SCENARIO_USER_AVATAR
                        - SCENARIO_ITEM_COVER
                        - SCENARIO_MUSIC
                        - SCENARIO_MUSIC_COVER
                        - SCENARIO_BGM
                        - SCENARIO_STORY_RESOURCE
                        - SCENARIO_FIZZ_RESOURCE
                        - SCENARIO_IM_RESOURCE
                        - SCENARIO_BOO_RESOURCE
                    type: string
                    format: enum
                contentType:
                    enum:
                        - CONTENT_TYPE_UNSPECIFIED
                        - CONTENT_TYPE_IMAGE_APNG
                        - CONTENT_TYPE_IMAGE_PNG
                        - CONTENT_TYPE_IMAGE_JPEG
                        - CONTENT_TYPE_IMAGE_JPG
                        - CONTENT_TYPE_IMAGE_GIF
                        - CONTENT_TYPE_IMAGE_WEBP
                        - CONTENT_TYPE_TEXT_JSON
                        - CONTENT_TYPE_MINIGAME_BUNDLE
                        - CONTENT_TYPE_MINIGAME_SCENE_ZIP
                        - CONTENT_TYPE_MP3
                        - CONTENT_TYPE_AUDIO_WAV
                        - CONTENT_TYPE_AUDIO_OGG
                        - CONTENT_TYPE_AUDIO_FLAC
                        - CONTENT_TYPE_AUDIO_APE
                        - CONTENT_TYPE_AUDIO_WMA
                        - CONTENT_TYPE_AUDIO_MP4
                        - CONTENT_TYPE_VIDEO_MP4
                        - CONTENT_TYPE_VIDEO_AVI
                        - CONTENT_TYPE_VIDEO_MOV
                        - CONTENT_TYPE_VIDEO_WMV
                        - CONTENT_TYPE_VIDEO_FLV
                        - CONTENT_TYPE_IMAGE_BMP
                        - CONTENT_TYPE_IMAGE_TIFF
                        - CONTENT_TYPE_AUDIO_M4A
                        - CONTENT_TYPE_ZIP
                    type: string
                    format: enum
        api.resource.v1.BatchGetPutObjectPreSignUrlResponse:
            type: object
            properties:
                results:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.resource.v1.BatchGetPutObjectPreSignUrlResponse_Result'
        api.resource.v1.BatchGetPutObjectPreSignUrlResponse_Result:
            type: object
            properties:
                uploadUrl:
                    type: string
                    description: 上传地址，put 请求即可
                objectKey:
                    type: string
                objectAccessUrl:
                    type: string
                expiresAt:
                    type: string
                    description: '注意，此值在使用 upload url 上传时必填，用作 header : Expires: {expires_at}'
                contentType:
                    type: string
                    description: '注意，此值在使用 upload url 上传时必填, 用来s3计算sign，用作 header : Content-Type: {content_type}'
        api.resource.v1.GetPutObjectPreSignUrlRequest:
            type: object
            properties:
                scenario:
                    enum:
                        - SCENARIO_UNSPECIFIED
                        - SCENARIO_USER_AVATAR
                        - SCENARIO_ITEM_COVER
                        - SCENARIO_MUSIC
                        - SCENARIO_MUSIC_COVER
                        - SCENARIO_BGM
                        - SCENARIO_STORY_RESOURCE
                        - SCENARIO_FIZZ_RESOURCE
                        - SCENARIO_IM_RESOURCE
                        - SCENARIO_BOO_RESOURCE
                    type: string
                    format: enum
                contentType:
                    enum:
                        - CONTENT_TYPE_UNSPECIFIED
                        - CONTENT_TYPE_IMAGE_APNG
                        - CONTENT_TYPE_IMAGE_PNG
                        - CONTENT_TYPE_IMAGE_JPEG
                        - CONTENT_TYPE_IMAGE_JPG
                        - CONTENT_TYPE_IMAGE_GIF
                        - CONTENT_TYPE_IMAGE_WEBP
                        - CONTENT_TYPE_TEXT_JSON
                        - CONTENT_TYPE_MINIGAME_BUNDLE
                        - CONTENT_TYPE_MINIGAME_SCENE_ZIP
                        - CONTENT_TYPE_MP3
                        - CONTENT_TYPE_AUDIO_WAV
                        - CONTENT_TYPE_AUDIO_OGG
                        - CONTENT_TYPE_AUDIO_FLAC
                        - CONTENT_TYPE_AUDIO_APE
                        - CONTENT_TYPE_AUDIO_WMA
                        - CONTENT_TYPE_AUDIO_MP4
                        - CONTENT_TYPE_VIDEO_MP4
                        - CONTENT_TYPE_VIDEO_AVI
                        - CONTENT_TYPE_VIDEO_MOV
                        - CONTENT_TYPE_VIDEO_WMV
                        - CONTENT_TYPE_VIDEO_FLV
                        - CONTENT_TYPE_IMAGE_BMP
                        - CONTENT_TYPE_IMAGE_TIFF
                        - CONTENT_TYPE_AUDIO_M4A
                        - CONTENT_TYPE_ZIP
                    type: string
                    format: enum
        api.resource.v1.GetPutObjectPreSignUrlResponse:
            type: object
            properties:
                uploadUrl:
                    type: string
                    description: 上传地址，put 请求即可
                objectKey:
                    type: string
                objectAccessUrl:
                    type: string
                expiresAt:
                    type: string
                    description: '注意，此值在使用 upload url 上传时必填，用作 header : Expires: {expires_at}'
                contentType:
                    type: string
                    description: '注意，此值在使用 upload url 上传时必填, 用来s3计算sign，用作 header : Content-Type: {content_type}'
        api.search.v1.DeleteSearchHistoryRequest:
            type: object
            properties:
                searchIds:
                    type: array
                    items:
                        type: string
                    description: 要删除的搜索历史ID列表
        api.search.v1.DeleteSearchHistoryResponse:
            type: object
            properties:
                success:
                    type: boolean
                    description: 删除是否成功
        api.search.v1.GetSearchHistoryRequest:
            type: object
            properties:
                count:
                    type: integer
                    description: 返回的搜索历史条数
                    format: int32
        api.search.v1.GetSearchHistoryResponse:
            type: object
            properties:
                searchHistory:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.search.v1.SearchHistoryItem'
        api.search.v1.SearchHistoryItem:
            type: object
            properties:
                searchId:
                    type: string
                    description: 搜索ID
                searchText:
                    type: string
                    description: 搜索文本
                searchType:
                    enum:
                        - SEARCH_TYPE_UNSPECIFIED
                        - SEARCH_TYPE_USER
                        - SEARCH_TYPE_FOLLOWING
                        - SEARCH_TYPE_FRIEND
                    type: string
                    description: 搜索类型
                    format: enum
        api.search.v1.SearchHistoryUploadItem:
            type: object
            properties:
                searchText:
                    type: string
                    description: 搜索文本
                searchType:
                    type: string
                    description: 搜索类型
        api.search.v1.SearchRequest:
            type: object
            properties:
                keyword:
                    type: string
                searchType:
                    type: string
                    description: 搜索类型, 目前仅支持 1. 用户搜索 + 关注的人搜索，见 SearchType 2. 仅用户搜索 见 SearchType
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.search.v1.SearchResponse:
            type: object
            properties:
                results:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.search.v1.SearchResponse_Result'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.search.v1.SearchResponse_Result:
            type: object
            properties:
                userInfoSummary:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                searchType:
                    enum:
                        - SEARCH_TYPE_UNSPECIFIED
                        - SEARCH_TYPE_USER
                        - SEARCH_TYPE_FOLLOWING
                        - SEARCH_TYPE_FRIEND
                    type: string
                    format: enum
        api.search.v1.UploadSearchHistoryRequest:
            type: object
            properties:
                searchItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.search.v1.SearchHistoryUploadItem'
                    description: 要上传的搜索历史列表
        api.search.v1.UploadSearchHistoryResponse:
            type: object
            properties:
                searchIds:
                    type: array
                    items:
                        type: string
                    description: 返回生成的搜索ID列表
        api.tracking.v1.TrackingBatchReportRequest:
            type: object
            properties:
                eventsJsonStr:
                    type: array
                    items:
                        type: string
                    description: 客户端上报的事件 json_marshal 后的字符串数组 具体格式与推搜协定，服务端只会按照消息序号推送至 kafka
        api.users.albums.types.v1.Album:
            type: object
            properties:
                id:
                    type: string
                title:
                    type: string
                userId:
                    type: string
                createdAtUnixTimestamp:
                    type: integer
                    format: uint32
                updatedAtUnixTimestamp:
                    type: integer
                    format: uint32
        api.users.albums.types.v1.Geometry:
            type: object
            properties:
                position:
                    type: array
                    items:
                        type: number
                        format: double
                scale:
                    type: number
                    format: double
                rotation:
                    type: number
                    format: double
        api.users.albums.types.v1.Item:
            type: object
            properties:
                resourceAccessKeys:
                    type: array
                    items:
                        type: string
                resourceAccessUrls:
                    type: array
                    items:
                        type: string
                    description: 服务端下发
                albumItemType:
                    type: string
                    description: 参考 AlbumItemType
                geometry:
                    $ref: '#/components/schemas/api.users.albums.types.v1.Geometry'
                layout:
                    type: string
                storyId:
                    type: string
                cover:
                    $ref: '#/components/schemas/api.items.story.types.v1.Cover'
                hint:
                    $ref: '#/components/schemas/api.items.story.types.v1.AttachmentText'
                id:
                    type: string
                playType:
                    type: string
                    description: 服务端下发
        api.users.albums.v1.AddItemsToAlbumRequest:
            type: object
            properties:
                albumId:
                    type: string
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.albums.types.v1.Item'
        api.users.albums.v1.AddItemsToAlbumResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.albums.types.v1.Item'
        api.users.albums.v1.BatchUpdateItemsLayoutRequest:
            type: object
            properties:
                attrs:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.albums.v1.BatchUpdateItemsLayoutRequest_UpdateAttr'
                albumId:
                    type: string
        api.users.albums.v1.BatchUpdateItemsLayoutRequest_UpdateAttr:
            type: object
            properties:
                itemId:
                    type: string
                geometry:
                    $ref: '#/components/schemas/api.users.albums.types.v1.Geometry'
                layout:
                    type: string
        api.users.albums.v1.BatchUpdateItemsLayoutResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.albums.types.v1.Item'
        api.users.albums.v1.CreateAlbumRequest:
            type: object
            properties:
                title:
                    type: string
        api.users.albums.v1.CreateAlbumResponse:
            type: object
            properties:
                album:
                    $ref: '#/components/schemas/api.users.albums.types.v1.Album'
        api.users.albums.v1.DeleteAlbumRequest:
            type: object
            properties:
                albumId:
                    type: string
        api.users.albums.v1.DeleteAlbumResponse:
            type: object
            properties: {}
        api.users.albums.v1.ListAlbumItemsRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                albumId:
                    type: string
        api.users.albums.v1.ListAlbumItemsResponse:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.albums.types.v1.Item'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.users.albums.v1.ListAlbumsRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                userId:
                    type: string
        api.users.albums.v1.ListAlbumsResponse:
            type: object
            properties:
                albums:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.albums.types.v1.Album'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.users.albums.v1.RemoveItemsFromAlbumRequest:
            type: object
            properties:
                albumId:
                    type: string
                itemIds:
                    type: array
                    items:
                        type: string
        api.users.albums.v1.RemoveItemsFromAlbumResponse:
            type: object
            properties: {}
        api.users.albums.v1.UpdateAlbumRequest:
            type: object
            properties:
                albumId:
                    type: string
                    description: only number
                title:
                    type: string
        api.users.albums.v1.UpdateAlbumResponse:
            type: object
            properties:
                album:
                    $ref: '#/components/schemas/api.users.albums.types.v1.Album'
        api.users.albums.v1.UpdateItemLayoutRequest:
            type: object
            properties:
                itemId:
                    type: string
                geometry:
                    $ref: '#/components/schemas/api.users.albums.types.v1.Geometry'
                layout:
                    type: string
        api.users.albums.v1.UpdateItemLayoutResponse:
            type: object
            properties:
                item:
                    $ref: '#/components/schemas/api.users.albums.types.v1.Item'
        api.users.auth.v1.GetImTokenRequest:
            type: object
            properties: {}
        api.users.auth.v1.GetImTokenResponse:
            type: object
            properties:
                imToken:
                    type: string
        api.users.auth.v1.LoginOrRegisterWithVerifyCodeRequest:
            type: object
            properties:
                email:
                    type: string
                phone:
                    type: string
                    description: TOOD validator for phtone
                code:
                    type: string
                deviceId:
                    type: string
        api.users.auth.v1.LoginOrRegisterWithVerifyCodeResponse:
            type: object
            properties:
                loginUser:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoDetail'
                token:
                    type: string
                imToken:
                    type: string
        api.users.auth.v1.SendVerifyCodeRequest:
            type: object
            properties:
                email:
                    type: string
                phone:
                    type: string
                    description: TOOD validator for phtone
        api.users.auth.v1.ThirdPartyLoginRequest:
            type: object
            properties:
                bindType:
                    enum:
                        - BIND_TYPE_UNSPECIFIED
                        - BIND_TYPE_GOOGLE
                        - BIND_TYPE_APPLE
                        - BIND_TYPE_FACEBOOK
                        - BIND_TYPE_PHONE
                        - BIND_TYPE_EMAIL
                    type: string
                    format: enum
                token:
                    type: string
                deviceId:
                    type: string
        api.users.auth.v1.ThirdPartyLoginResponse:
            type: object
            properties:
                token:
                    type: string
                loginUser:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoDetail'
                imToken:
                    type: string
        api.users.binds.types.v1.UserBinds:
            type: object
            properties:
                type:
                    enum:
                        - BIND_TYPE_UNSPECIFIED
                        - BIND_TYPE_GOOGLE
                        - BIND_TYPE_APPLE
                        - BIND_TYPE_FACEBOOK
                        - BIND_TYPE_PHONE
                        - BIND_TYPE_EMAIL
                    type: string
                    format: enum
                code:
                    type: string
                    description: 非登录用户请求此值时，会掩码保护
        api.users.boo.types.v1.Avatar:
            type: object
            properties:
                id:
                    type: string
                resource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                selectedAsUsed:
                    type: boolean
        api.users.boo.types.v1.Boo:
            type: object
            properties:
                id:
                    type: string
                creator:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                animations:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.boo.types.v1.BooAnimation'
                avatarImageUrl:
                    type: string
                status:
                    enum:
                        - STATUS_UNSPECIFIED
                        - STATUS_GENERATING
                        - STATUS_GENERATED
                        - STATUS_FAILED
                    type: string
                    format: enum
                pickedStatus:
                    enum:
                        - PICKED_STATUS_UNSPECIFIED
                        - PICKED_STATUS_UNUSED
                        - PICKED_STATUS_PICKED
                    type: string
                    format: enum
            description: Boo 信息
        api.users.boo.types.v1.BooAnimation:
            type: object
            properties:
                id:
                    type: string
                emotionType:
                    enum:
                        - BOO_EMOTION_TYPE_UNSPECIFIED
                        - BOO_EMOTION_TYPE_ANGRY
                        - BOO_EMOTION_TYPE_SCARED
                        - BOO_EMOTION_TYPE_HAPPY
                        - BOO_EMOTION_TYPE_BORED
                        - BOO_EMOTION_TYPE_FUNNY
                        - BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED
                    type: string
                    format: enum
                videoPlayUrl:
                    type: string
                canBeGreeting:
                    type: boolean
                    description: 是否可以作为开通动画
                canBeCapturing:
                    type: boolean
                    description: 是否可以作为正在被抓的动画 -> scared
                canBeCaptureSuccess:
                    type: boolean
                    description: 是否可以作为抓鬼成功动画 -> dissmiss
                canBeCaptureFailed:
                    type: boolean
                    description: 是否可以作为抓鬼失败动画
                recommendPositions:
                    type: array
                    items:
                        enum:
                            - POSITION_UNSPECIFIED
                            - POSITION_TOP
                            - POSITION_BOTTOM
                            - POSITION_LEFT
                            - POSITION_RIGHT
                            - POSITION_CENTER
                            - POSITION_TOP_LEFT
                            - POSITION_TOP_RIGHT
                            - POSITION_BOTTOM_LEFT
                            - POSITION_BOTTOM_RIGHT
                        type: string
                        format: enum
                sceneAndDialogues:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.boo.types.v1.BooAnimation_SceneAndDialogue'
            description: boo 的动画，总是伴着情绪及播放素材等其他信息
        api.users.boo.types.v1.BooAnimation_SceneAndDialogue:
            type: object
            properties:
                scene:
                    enum:
                        - BOO_SCENE_UNSPECIFIED
                        - BOO_SCENE_FEED
                        - BOO_SCENE_DETAIL_PAGE
                        - BOO_SCENE_IM_CHAT
                        - BOO_SCENE_POLLUTE_FEED
                    type: string
                    format: enum
                dialogues:
                    type: array
                    items:
                        type: string
            description: 支持在哪些场景下渲染，并且有哪些台词
        api.users.boo.types.v1.BooShowInfo:
            type: object
            properties:
                showConfig:
                    $ref: '#/components/schemas/api.users.boo.types.v1.BooShowInfo_ShowConfig'
                receivedBoos:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.boo.types.v1.BooShowInfo_RecivedBoo'
            description: Boo 的渲染信息
        api.users.boo.types.v1.BooShowInfo_RecivedBoo:
            type: object
            properties:
                boo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.Boo'
                remainingShowCount:
                    type: integer
                    description: 剩余展示次数，全部展示完毕后，此对象会从数组内被移除 目前，每次收到新的 Boo 后，默认都是 10次
                    format: uint32
                showRecords:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.boo.types.v1.RecivedBoo_ShowRecord'
                    description: 历史上的展示记录
                validUntilInUnixstamp:
                    type: integer
                    description: 有效截止于，如果超过此时间，此元素也会从数组内被移除
                    format: uint32
                validScenes:
                    type: array
                    items:
                        enum:
                            - BOO_SCENE_UNSPECIFIED
                            - BOO_SCENE_FEED
                            - BOO_SCENE_DETAIL_PAGE
                            - BOO_SCENE_IM_CHAT
                            - BOO_SCENE_POLLUTE_FEED
                        type: string
                        format: enum
                    description: 这个鬼可出现的场景
            description: 用户收到的所有鬼
        api.users.boo.types.v1.BooShowInfo_ShowConfig:
            type: object
            properties:
                everyShowIntervalSeconds:
                    type: integer
                    description: 时间间隔，单位 s
                    format: uint32
        api.users.boo.types.v1.GenerateAvatarJob:
            type: object
            properties:
                id:
                    type: string
                creatorId:
                    type: string
                jobStatus:
                    enum:
                        - JOB_STATUS_UNSPECIFIED
                        - JOB_STATUS_PENDING
                        - JOB_STATUS_RUNNING
                        - JOB_STATUS_SUCCESS
                        - JOB_STATUS_FAILED
                    type: string
                    format: enum
                userUploadedPhotoResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                generatedAvatarResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.resource.types.v1.Resource'
                    description: 生成出来的头像，当且仅当 status = Success 后才会出现
                estimatedRemainingSeconds:
                    type: integer
                    description: 预估的剩余秒数
                    format: uint32
                executedSeconds:
                    type: integer
                    description: 已经执行了多久了，秒数
                    format: uint32
                updatedAtInUnixstamp:
                    type: integer
                    format: uint32
                createdAtInUnixstamp:
                    type: integer
                    format: uint32
        api.users.boo.types.v1.RecivedBoo_ShowRecord:
            type: object
            properties:
                scene:
                    enum:
                        - BOO_SCENE_UNSPECIFIED
                        - BOO_SCENE_FEED
                        - BOO_SCENE_DETAIL_PAGE
                        - BOO_SCENE_IM_CHAT
                        - BOO_SCENE_POLLUTE_FEED
                    type: string
                    format: enum
                emotionType:
                    enum:
                        - BOO_EMOTION_TYPE_UNSPECIFIED
                        - BOO_EMOTION_TYPE_ANGRY
                        - BOO_EMOTION_TYPE_SCARED
                        - BOO_EMOTION_TYPE_HAPPY
                        - BOO_EMOTION_TYPE_BORED
                        - BOO_EMOTION_TYPE_FUNNY
                        - BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED
                    type: string
                    format: enum
                showAtInUnixstamp:
                    type: integer
                    format: uint32
        api.users.boo.types.v1.UseAvatarCreatedInfo:
            type: object
            properties:
                maxCreatedJobsCount:
                    type: integer
                    description: 最多尝试几次头像生成
                    format: uint32
                remainCreatedJobsCount:
                    type: integer
                    description: 剩余几次生成头像的机会
                    format: uint32
                sentToUserIds:
                    type: array
                    items:
                        type: string
                    description: 给哪些人发过 Boo
                selectedAvatarResource:
                    $ref: '#/components/schemas/api.resource.types.v1.Resource'
                allGeneratedAvatarResources:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.resource.types.v1.Resource'
                    description: 历史里所有的头像
                currentActiveJob:
                    $ref: '#/components/schemas/api.users.boo.types.v1.GenerateAvatarJob'
                allGeneratedAvatars:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.boo.types.v1.Avatar'
                selectedAvatar:
                    $ref: '#/components/schemas/api.users.boo.types.v1.Avatar'
        api.users.boo.v1.CaptureBooActionReportRequest:
            type: object
            properties:
                booId:
                    type: string
                scene:
                    enum:
                        - BOO_SCENE_UNSPECIFIED
                        - BOO_SCENE_FEED
                        - BOO_SCENE_DETAIL_PAGE
                        - BOO_SCENE_IM_CHAT
                        - BOO_SCENE_POLLUTE_FEED
                    type: string
                    description: 场景
                    format: enum
                emotionType:
                    enum:
                        - BOO_EMOTION_TYPE_UNSPECIFIED
                        - BOO_EMOTION_TYPE_ANGRY
                        - BOO_EMOTION_TYPE_SCARED
                        - BOO_EMOTION_TYPE_HAPPY
                        - BOO_EMOTION_TYPE_BORED
                        - BOO_EMOTION_TYPE_FUNNY
                        - BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED
                    type: string
                    description: 情绪
                    format: enum
                actionType:
                    enum:
                        - USER_ACTION_TYPE_UNSPECIFIED
                        - USER_ACTION_TYPE_BOO_SHOW
                        - USER_ACTION_TYPE_BEGIN_CAPTURE
                        - USER_ACTION_TYPE_LEAVE_CAPTURE
                        - USER_ACTION_TYPE_CAPTURE_FAILED
                        - USER_ACTION_TYPE_CAPTURE_SUCCESS
                    type: string
                    description: 动作类型
                    format: enum
            description: 抓鬼动作上报，每次抓鬼成功后，需要从 view info 里移除这个鬼
        api.users.boo.v1.CaptureBooActionReportResponse:
            type: object
            properties:
                actionId:
                    type: string
                latestBooShowInfo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.BooShowInfo'
        api.users.boo.v1.CreateAvatarJobRequest:
            type: object
            properties:
                userPhotoObjectKey:
                    type: string
        api.users.boo.v1.CreateAvatarJobResponse:
            type: object
            properties:
                useAvatarCreatedInfo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.UseAvatarCreatedInfo'
        api.users.boo.v1.GenerateBooWithSelectedAvatarRequest:
            type: object
            properties: {}
        api.users.boo.v1.GenerateBooWithSelectedAvatarResponse:
            type: object
            properties:
                alreadyGenerated:
                    type: boolean
        api.users.boo.v1.GetLatestAvatarJobRequest:
            type: object
            properties: {}
        api.users.boo.v1.GetLatestAvatarJobResponse:
            type: object
            properties:
                useAvatarCreatedInfo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.UseAvatarCreatedInfo'
        api.users.boo.v1.GetLatestBooShowInfoRequest:
            type: object
            properties: {}
        api.users.boo.v1.GetLatestBooShowInfoResponse:
            type: object
            properties:
                latestBooShowInfo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.BooShowInfo'
        api.users.boo.v1.RecordBooShowRequest:
            type: object
            properties:
                booId:
                    type: string
                scene:
                    enum:
                        - BOO_SCENE_UNSPECIFIED
                        - BOO_SCENE_FEED
                        - BOO_SCENE_DETAIL_PAGE
                        - BOO_SCENE_IM_CHAT
                        - BOO_SCENE_POLLUTE_FEED
                    type: string
                    description: 场景
                    format: enum
                emotionType:
                    enum:
                        - BOO_EMOTION_TYPE_UNSPECIFIED
                        - BOO_EMOTION_TYPE_ANGRY
                        - BOO_EMOTION_TYPE_SCARED
                        - BOO_EMOTION_TYPE_HAPPY
                        - BOO_EMOTION_TYPE_BORED
                        - BOO_EMOTION_TYPE_FUNNY
                        - BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED
                    type: string
                    description: 情绪
                    format: enum
        api.users.boo.v1.RecordBooShowResponse:
            type: object
            properties:
                latestBooShowInfo:
                    $ref: '#/components/schemas/api.users.boo.types.v1.BooShowInfo'
        api.users.boo.v1.SelectAvatarAsUsedRequest:
            type: object
            properties:
                avatarObjectKey:
                    type: string
        api.users.boo.v1.SelectAvatarAsUsedResponse:
            type: object
            properties: {}
        api.users.boo.v1.SendAvatarAsBooToUsersRequest:
            type: object
            properties:
                toUserIds:
                    type: array
                    items:
                        type: string
                avatarObjectKey:
                    type: string
        api.users.boo.v1.SendCaptureRecordVideoRequest:
            type: object
            properties:
                booId:
                    type: string
                videoObjectKey:
                    type: string
                videoCoverObjectKey:
                    type: string
        api.users.highlights.types.v1.Highlight:
            type: object
            properties:
                id:
                    type: string
                title:
                    type: string
                coverImageUrl:
                    type: string
                stats:
                    $ref: '#/components/schemas/api.users.highlights.types.v1.Stats'
        api.users.highlights.types.v1.Stats:
            type: object
            properties:
                videosCount:
                    type: integer
                    format: uint32
                imagesCount:
                    type: integer
                    format: uint32
        api.users.highlights.v1.CreateHighlightRequest:
            type: object
            properties:
                title:
                    type: string
                coverMediaId:
                    type: string
                mediaIds:
                    type: array
                    items:
                        type: string
        api.users.highlights.v1.CreateHighlightResponse:
            type: object
            properties:
                highlight:
                    $ref: '#/components/schemas/api.users.highlights.types.v1.Highlight'
        api.users.highlights.v1.ListHighLightMediasRequest:
            type: object
            properties:
                highlightId:
                    type: string
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.users.highlights.v1.ListHighLightMediasResponse:
            type: object
            properties:
                medias:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.fizz.media.types.v1.FizzMedia'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.users.highlights.v1.ListUserCreatedHighlightsRequest:
            type: object
            properties:
                userId:
                    type: string
                    description: 用户 ID，如果为空，则表示当前登录用户
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.users.highlights.v1.ListUserCreatedHighlightsResponse:
            type: object
            properties:
                highlights:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.highlights.types.v1.Highlight'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.users.highlights.v1.RemoveMediaFromHighlightRequest:
            type: object
            properties:
                highlightId:
                    type: string
                mediaId:
                    type: string
        api.users.highlights.v1.UpdateHighlightRequest:
            type: object
            properties:
                highlightId:
                    type: string
                title:
                    type: string
                    description: 可选，不传则不更新
                coverMediaId:
                    type: string
                    description: 可选，不传则不更新
        api.users.info.types.v1.Closeness:
            type: object
            properties:
                continuousInteractionDays:
                    type: integer
                    description: 连续互动天数
                    format: uint32
        api.users.info.types.v1.UserInfoDetail:
            type: object
            properties:
                summary:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                binds:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.binds.types.v1.UserBinds'
                isVisitor:
                    type: boolean
                    description: 是否为游客，目前实际上服务端会判断 binds > 0 则不是游客，客户端可以先直接使用这个字段来判断
        api.users.info.types.v1.UserInfoSummary:
            type: object
            properties:
                id:
                    type: string
                nickname:
                    type: string
                uniqName:
                    type: string
                avatarUrl:
                    type: string
                    description: TODO 支持多尺寸
                bio:
                    type: string
                stat:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserStat'
                withLoginUserRelation:
                    $ref: '#/components/schemas/api.users.relations.types.v1.UserRelation'
                closeness:
                    $ref: '#/components/schemas/api.users.info.types.v1.Closeness'
        api.users.info.types.v1.UserMini:
            type: object
            properties:
                id:
                    type: string
                nickname:
                    type: string
                uniqName:
                    type: string
                avatarUrl:
                    type: string
        api.users.info.types.v1.UserStat:
            type: object
            properties:
                totalFollowingCount:
                    type: integer
                    description: 关注总数
                    format: uint32
                totalFollowersCount:
                    type: integer
                    description: 粉丝总数
                    format: uint32
                totalLikedsCount:
                    type: integer
                    description: 被赞数
                    format: uint32
                totalStoriesCount:
                    type: integer
                    description: 发布的 story 数量
                    format: uint32
        api.users.info.v1.BatchGetUserSummariesRequest:
            type: object
            properties:
                userIds:
                    type: array
                    items:
                        type: string
                    description: 允许为空，如果为空则取 token 对应的 user_id
        api.users.info.v1.BatchGetUserSummariesResponse:
            type: object
            properties:
                userSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
        api.users.info.v1.GetUserDetailRequest:
            type: object
            properties:
                userId:
                    type: string
                    description: 允许为空，如果为空则取 token 对应的 user_id
        api.users.info.v1.GetUserDetailResponse:
            type: object
            properties:
                userDetail:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoDetail'
        api.users.info.v1.UpdateUserInfoRequest:
            type: object
            properties:
                nickname:
                    type: string
                bio:
                    type: string
                avatarObjectKey:
                    type: string
        api.users.notifications.v1.BatchReadUserSystemNotificationsRequest:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: string
        api.users.notifications.v1.GetUserSystemNotificationStatSummaryRequest:
            type: object
            properties: {}
            description: 获取用户系统通知统计摘要
        api.users.notifications.v1.GetUserSystemNotificationStatSummaryResponse:
            type: object
            properties:
                unreadSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.notifications.v1.GetUserSystemNotificationStatSummaryResponse_UnreadSummary'
                joinedFizzUnreadSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.notifications.v1.GetUserSystemNotificationStatSummaryResponse_JoinedFizzUnreadSummary'
        api.users.notifications.v1.GetUserSystemNotificationStatSummaryResponse_JoinedFizzUnreadSummary:
            type: object
            properties:
                fizz:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
                count:
                    type: integer
                    format: uint32
                latestNotification:
                    $ref: '#/components/schemas/api.users.notifications.v1.types.SystemNotification'
            description: 由于 fizz 的系统通知比较特殊，单开一个结构来表达
        api.users.notifications.v1.GetUserSystemNotificationStatSummaryResponse_UnreadSummary:
            type: object
            properties:
                type:
                    enum:
                        - SYSTEM_NOTIFICATION_TYPE_UNSPECIFIED
                        - SYSTEM_NOTIFICATION_TYPE_JOINED_FIZZ_EVENTS
                        - SYSTEM_NOTIFICATION_TYPE_FOLLOWED_BY_OTHERS
                    type: string
                    format: enum
                count:
                    type: integer
                    format: uint32
        api.users.notifications.v1.ListUserSystemNotificationsRequest:
            type: object
            properties:
                types:
                    type: array
                    items:
                        enum:
                            - SYSTEM_NOTIFICATION_TYPE_UNSPECIFIED
                            - SYSTEM_NOTIFICATION_TYPE_JOINED_FIZZ_EVENTS
                            - SYSTEM_NOTIFICATION_TYPE_FOLLOWED_BY_OTHERS
                        type: string
                        format: enum
                    description: 需要过滤的通知类型，如果为空，则不进行过滤
                fizzId:
                    type: string
                    description: 需要过滤的 fizz id，当且仅当 types 包含 SYSTEM_NOTIFICATION_TYPE_JOINED_FIZZ_EVENTS 时有效
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.users.notifications.v1.ListUserSystemNotificationsResponse:
            type: object
            properties:
                notifications:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.notifications.v1.types.SystemNotification'
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
        api.users.notifications.v1.types.FollowedByOthersPayload:
            type: object
            properties:
                followedByOthers:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
        api.users.notifications.v1.types.JoinedFizzEventsPayload:
            type: object
            properties:
                joinedFizz:
                    $ref: '#/components/schemas/api.fizz.types.v1.FizzSummary'
                event:
                    $ref: '#/components/schemas/api.fizz.events.types.v1.FizzEvents'
        api.users.notifications.v1.types.SystemNotification:
            type: object
            properties:
                id:
                    type: string
                title:
                    type: string
                coverImageUrl:
                    type: string
                type:
                    enum:
                        - SYSTEM_NOTIFICATION_TYPE_UNSPECIFIED
                        - SYSTEM_NOTIFICATION_TYPE_JOINED_FIZZ_EVENTS
                        - SYSTEM_NOTIFICATION_TYPE_FOLLOWED_BY_OTHERS
                    type: string
                    format: enum
                createdAtUnixTimestamp:
                    type: integer
                    format: uint32
                payload:
                    $ref: '#/components/schemas/api.users.notifications.v1.types.SystemNotificationPayload'
                isRead:
                    type: boolean
        api.users.notifications.v1.types.SystemNotificationPayload:
            type: object
            properties:
                joinedFizzEvents:
                    $ref: '#/components/schemas/api.users.notifications.v1.types.JoinedFizzEventsPayload'
                followedByOthers:
                    $ref: '#/components/schemas/api.users.notifications.v1.types.FollowedByOthersPayload'
        api.users.push.v1.SaveUserPushTokenRequest:
            type: object
            properties:
                pushToken:
                    type: string
                isSandbox:
                    type: boolean
                    description: 是否是沙盒
                appVersion:
                    type: string
                    description: app 版本号
                osType:
                    type: string
                    description: os 类型，实际为枚举，ios || android
        api.users.recommend.v1.RecommendUser_RecommendReasonExtra:
            type: object
            properties:
                reasonType:
                    type: string
                relatedUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
            description: 只有富文本文案才需要这个字段
        api.users.recommend.v1.RecommendUsersRequest:
            type: object
            properties:
                scenario:
                    enum:
                        - RECOMMEND_SCENARIO_UNSPECIFIED
                        - RECOMMEND_SCENARIO_PERSON_PAGE
                        - RECOMMEND_SCENARIO_NOTIFICATION_PAGE
                        - RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS
                        - RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS
                    type: string
                    format: enum
                profileUserId:
                    type: string
                    description: 可选型，目前仅当场景为个人页推荐人时有效，传递个人页的 user_id
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
        api.users.recommend.v1.RecommendUsersResponse:
            type: object
            properties:
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                users:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.recommend.v1.RecommendUsersResponse_RecommendUser'
                sortRequestId:
                    type: string
        api.users.recommend.v1.RecommendUsersResponse_RecommendUser:
            type: object
            properties:
                recommendReason:
                    type: string
                    description: 推荐理由，推荐引擎返回
                user:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
                sortRequestId:
                    type: string
                recommendReasonExtra:
                    $ref: '#/components/schemas/api.users.recommend.v1.RecommendUser_RecommendReasonExtra'
                relatedStoryId:
                    type: string
        api.users.relations.types.v1.UserRelation:
            type: object
            properties:
                relationType:
                    enum:
                        - USER_RELATION_TYPE_UNSPECIFIED
                        - USER_RELATION_TYPE_FOLLOWING
                        - USER_RELATION_TYPE_BLOCKED
                        - USER_RELATION_TYPE_FOLLOWER
                        - USER_RELATION_TYPE_MUTUAL
                        - USER_RELATION_TYPE_BLOCKED_BY
                    type: string
                    format: enum
                happenedAt:
                    type: integer
                    description: unix 时间戳，单位秒
                    format: uint32
                happenedAtUnixmillStampStr:
                    type: string
                    description: unix 毫秒时间戳，单位毫秒
        api.users.relations.v1.CreateRelationRequest:
            type: object
            properties:
                relationType:
                    enum:
                        - USER_RELATION_TYPE_UNSPECIFIED
                        - USER_RELATION_TYPE_FOLLOWING
                        - USER_RELATION_TYPE_BLOCKED
                        - USER_RELATION_TYPE_FOLLOWER
                        - USER_RELATION_TYPE_MUTUAL
                        - USER_RELATION_TYPE_BLOCKED_BY
                    type: string
                    format: enum
                targetUserId:
                    type: string
        api.users.relations.v1.ListRelationUsersRequest:
            type: object
            properties:
                listRequest:
                    $ref: '#/components/schemas/api.common.v1.ListRequest'
                relationType:
                    enum:
                        - USER_RELATION_TYPE_UNSPECIFIED
                        - USER_RELATION_TYPE_FOLLOWING
                        - USER_RELATION_TYPE_BLOCKED
                        - USER_RELATION_TYPE_FOLLOWER
                        - USER_RELATION_TYPE_MUTUAL
                        - USER_RELATION_TYPE_BLOCKED_BY
                    type: string
                    description: 关系类型，目前仅支持关注和粉丝和朋友
                    format: enum
                userId:
                    type: string
                    description: 用户ID，可选，如果为空或者不传，会取当前 session 中的 user_id
            description: 获取关系下的用户列表
        api.users.relations.v1.ListRelationUsersResponse:
            type: object
            properties:
                listResponse:
                    $ref: '#/components/schemas/api.common.v1.ListResponse'
                userSummaries:
                    type: array
                    items:
                        $ref: '#/components/schemas/api.users.info.types.v1.UserInfoSummary'
        api.users.relations.v1.RemoveRelationRequest:
            type: object
            properties:
                relationType:
                    enum:
                        - USER_RELATION_TYPE_UNSPECIFIED
                        - USER_RELATION_TYPE_FOLLOWING
                        - USER_RELATION_TYPE_BLOCKED
                        - USER_RELATION_TYPE_FOLLOWER
                        - USER_RELATION_TYPE_MUTUAL
                        - USER_RELATION_TYPE_BLOCKED_BY
                    type: string
                    format: enum
                targetUserId:
                    type: string
        api.users.relations.v1.UpdateRelationResponse:
            type: object
            properties:
                userDetail:
                    $ref: '#/components/schemas/api.users.info.types.v1.UserInfoDetail'
        api.users.rtm.types.v1.RTMToken:
            type: object
            properties:
                token:
                    type: string
                expireAt:
                    type: integer
                    format: uint32
        api.users.rtm.v1.GetRTMTokenRequest:
            type: object
            properties: {}
        api.users.rtm.v1.GetRTMTokenResponse:
            type: object
            properties:
                rtmToken:
                    $ref: '#/components/schemas/api.users.rtm.types.v1.RTMToken'
        api.users.rtm.v1.RTMCallbackRequest:
            type: object
            properties:
                action:
                    type: string
                    description: 1. join 用户加入频道 2. leave 用户离开频道 3. timeout 保活超时
                status:
                    type: string
                    description: 1. active 用户人数从0-1 2. inactive 用户人数从1-0
                channelType:
                    type: string
                channel:
                    type: string
                uid:
                    type: string
                timestoken:
                    type: string
            description: 专用于声网 RTM 回调，用于通知客户端更新房间状态
tags:
    - name: BooWorld
    - name: FizzService
      description: fizz服务
    - name: IMService
    - name: Items
    - name: Push
    - name: ResourceService
    - name: SearchService
    - name: Tracking
    - name: UsersService
      description: 用户服务
