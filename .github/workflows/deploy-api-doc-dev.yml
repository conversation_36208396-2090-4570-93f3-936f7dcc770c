name: Deploy OpenAPI Doc to EKS

on:
  push:
    branches: [ "main" ]
    paths:
      - 'openapi.yaml'

env:
  AWS_REGION: us-east-1
  EKS_CLUSTER_NAME: fg-dev-cluster-02
  K8S_NAMESPACE: default
  ECR_REGISTRY: 509399607526.dkr.ecr.us-east-1.amazonaws.com
  ECR_REPOSITORY: flippop/boson-api-doc

permissions:
  contents: read

jobs:
  deploy-openapi-doc:
    runs-on: ubuntu-latest
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_V2 }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_V2 }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build, tag, and push image to Amazon ECR
      env:
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # 构建 Docker 镜像
        docker build -f Dockerfile.swagger -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker tag $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG $ECR_REGISTRY/$ECR_REPOSITORY:latest
        
        # 推送镜像到 ECR
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:latest
        
        echo "Image pushed: $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"

    - name: Update kube config
      run: |
        aws eks update-kubeconfig --name ${{ env.EKS_CLUSTER_NAME }} --region ${{ env.AWS_REGION }}
        kubectl get svc

    - name: Deploy Swagger UI
      run: |
        # 安装 kustomize
        curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash /dev/stdin 3.8.6
        
        # 创建 kustomization.yaml
        cat <<EOF > kustomization.yaml
        apiVersion: kustomize.config.k8s.io/v1beta1
        kind: Kustomization
        resources:
        - manifest/k8s/boson-api-swagger-deployment.yaml
        images:
        - name: REGISTRY/NAMESPACE/IMAGE:TAG
          newName: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}
          newTag: ${{ github.sha }}
        EOF
        
        # 应用配置
        ./kustomize build . | kubectl apply -f -
        
        # 等待部署完成
        kubectl rollout status deployment/boson-api-swagger-ui

    - name: Notify Feishu
      if: always()
      run: |
        STATUS="${{ job.status }}"
        COLOR=$([ "$STATUS" == "success" ] && echo "green" || echo "red")
        ICON=$([ "$STATUS" == "success" ] && echo "🚀" || echo "⚠️")
        STATUS_TEXT=$([ "$STATUS" == "success" ] && echo "成功" || echo "失败")
        
        COMMIT_MESSAGE=$(echo "${{ github.event.head_commit.message }}" | sed 's/"/\\"/g' | tr '\n' ' ')
        
        curl -X POST -H "Content-Type: application/json" \
        -d "{
          \"msg_type\": \"interactive\",
          \"card\": {
            \"header\": {
              \"title\": {
                \"tag\": \"plain_text\",
                \"content\": \"${ICON} [测试环境] BosonAPI OpenAPI 文档部署${STATUS_TEXT}\"
              },
              \"template\": \"${COLOR}\"
            },
            \"elements\": [
              {
                \"tag\": \"div\",
                \"text\": {
                  \"tag\": \"lark_md\",
                  \"content\": \"$([ "$STATUS" == "success" ] && echo "✅" || echo "❌") **部署状态**：${STATUS_TEXT}\\n🌍 **环境**：测试环境\\n📦 **仓库**：[${{ github.repository }}](${{ github.server_url }}/${{ github.repository }})\\n🔀 **分支**：${{ github.ref_name }}\\n📝 **提交**：[${{ github.sha }}](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }})\\n👤 **触发者**：${{ github.actor }}\\n\\n📝 **提交信息**：${COMMIT_MESSAGE}\"
                }
              }
            ]
          }
        }" ${{ secrets.FEISHU_WEBHOOK_URL }}