name: Deploy Schema Update to DEV DB

on:
  push:
    branches:
      - main
    paths:
      - 'internal/infra/scripts/migrations/**'

jobs:
  deploy-migrations:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install golang-migrate tool
        run: |
          wget -nv -O migrate.tgz https://github.com/golang-migrate/migrate/releases/download/v4.15.0/migrate.linux-amd64.tar.gz
          tar -xzf migrate.tgz
          sudo mv ./migrate /usr/local/bin/migrate
          chmod +x /usr/local/bin/migrate

      - name: Run migrations up
        run: |
          migrate -path internal/infra/scripts/migrations -database "${{ secrets.DEV_DB_URL }}" -verbose up
        continue-on-error: false

      - name: Notify Feishu
        if: always()
        run: |
          STATUS="${{ job.status }}"
          COLOR=$([ "$STATUS" == "success" ] && echo "green" || echo "red")
          ICON=$([ "$STATUS" == "success" ] && echo "🚀" || echo "⚠️")
          STATUS_TEXT=$([ "$STATUS" == "success" ] && echo "成功" || echo "失败")
          
          # 转义提交信息中的特殊字符
          COMMIT_MESSAGE=$(echo "${{ github.event.head_commit.message }}" | sed 's/"/\\"/g' | tr '\n' ' ')
          
          curl -X POST -H "Content-Type: application/json" \
          -d "{
            \"msg_type\": \"interactive\",
            \"card\": {
              \"header\": {
                \"title\": {
                  \"tag\": \"plain_text\",
                  \"content\": \"${ICON} [测试环境] 数据库迁移${STATUS_TEXT}\"
                },
                \"template\": \"${COLOR}\"
              },
              \"elements\": [
                {
                  \"tag\": \"div\",
                  \"text\": {
                    \"tag\": \"lark_md\",
                    \"content\": \"$([ "$STATUS" == "success" ] && echo "✅" || echo "❌") **迁移状态**：${STATUS_TEXT}\\n🌍 **环境**：测试环境\\n📦 **仓库**：[${{ github.repository }}](${{ github.server_url }}/${{ github.repository }})\\n🔀 **分支**：${{ github.ref_name }}\\n📝 **提交**：[${{ github.sha }}](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }})\\n👤 **触发者**：${{ github.actor }}\\n\\n📝 **提交信息**：${COMMIT_MESSAGE}\"
                  }
                }
              ]
            }
          }" ${{ secrets.FEISHU_WEBHOOK_URL }}