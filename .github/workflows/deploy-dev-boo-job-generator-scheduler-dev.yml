name: Build and Deploy Dev <PERSON>-Boo-Job-Generator-Scheduler to <PERSON><PERSON>

on:
  push:
    branches: [ "main" ]

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: 509399607526.dkr.ecr.us-east-1.amazonaws.com
  ECR_REPOSITORY: flippop/boson-boo-job-generator-scheduler
  IMAGE_TAG: ${{ github.sha }}
  EKS_CLUSTER_NAME: fg-dev-cluster-02
  K8S_NAMESPACE: default

permissions:
  contents: read

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: development

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_V2 }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_V2 }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Download config from S3
      run: |
        mkdir -p configs
        aws s3 cp s3://future-gadgets-service-confs-2/backend/boson/config.dev.yaml configs/config.yaml
        aws s3 cp s3://future-gadgets-service-confs-2/backend/boson/flippop-firebase-adminsdk.json configs/flippop-firebase-adminsdk.json

    - name: Build and push image to ECR
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}:${{ env.IMAGE_TAG }}
        platforms: linux/amd64

    - name: Update kube config
      run: |
        aws eks update-kubeconfig --name ${{ env.EKS_CLUSTER_NAME }} --region ${{ env.AWS_REGION }}
        # 添加验证步骤
        kubectl get svc
        
    - name: Deploy to EKS
      run: |
        # 安装 kustomize
        curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | bash /dev/stdin 3.8.6
        
        # 创建 kustomization.yaml
        cat <<EOF > kustomization.yaml
        apiVersion: kustomize.config.k8s.io/v1beta1
        kind: Kustomization
        resources:
        - manifest/k8s/boson-boo-job-generator-scheduler-deployment.yaml
        images:
        - name: REGISTRY/NAMESPACE/IMAGE:TAG
          newName: ${{ env.ECR_REGISTRY }}/${{ env.ECR_REPOSITORY }}
          newTag: ${{ env.IMAGE_TAG }}
        EOF
        
        # 应用配置前确保有正确的 KUBECONFIG
        kubectl config view
        kubectl config current-context
        
        # 使用 --validate=false 应用配置
        ./kustomize build . | kubectl apply -f -

    - name: Notify Feishu
      if: always()
      run: |
        STATUS="${{ job.status }}"
        COLOR=$([ "$STATUS" == "success" ] && echo "green" || echo "red")
        ICON=$([ "$STATUS" == "success" ] && echo "🚀" || echo "⚠️")
        STATUS_TEXT=$([ "$STATUS" == "success" ] && echo "成功" || echo "失败")
        
        # 转义提交信息中的特殊字符
        COMMIT_MESSAGE=$(echo "${{ github.event.head_commit.message }}" | sed 's/"/\\"/g' | tr '\n' ' ')
        
        curl -X POST -H "Content-Type: application/json" \
        -d "{
          \"msg_type\": \"interactive\",
          \"card\": {
            \"header\": {
              \"title\": {
                \"tag\": \"plain_text\",
                \"content\": \"${ICON} [测试环境] Build and Deploy Boson-Boo-Job-Generator-Scheduler to EKS 部署${STATUS_TEXT}\"
              },
              \"template\": \"${COLOR}\"
            },
            \"elements\": [
              {
                \"tag\": \"div\",
                \"text\": {
                  \"tag\": \"lark_md\",
                  \"content\": \"$([ "$STATUS" == "success" ] && echo "✅" || echo "❌") **部署状态**：${STATUS_TEXT}\\n🌍 **环境**：测试环境\\n📦 **仓库**：[${{ github.repository }}](${{ github.server_url }}/${{ github.repository }})\\n🔀 **分支**：${{ github.ref_name }}\\n📝 **提交**：[${{ github.sha }}](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }})\\n👤 **触发者**：${{ github.actor }}\\n\\n📝 **提交信息**：${COMMIT_MESSAGE}\"
                }
              }
            ]
          }
        }" ${{ secrets.FEISHU_WEBHOOK_URL }}