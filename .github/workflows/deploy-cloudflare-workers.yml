name: Deploy Cloudflare Workers

on:
  push:
    branches:
      - main
    paths:
      - 'manifest/cloudflare/**'
jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy Worker
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Install Wrangler
        run: npm install -g wrangler
        
      - name: Deploy Worker
        env:
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
        run: |
          wrangler deploy manifest/cloudflare/cdn.workers.js

      - name: Notify Feishu
        if: always()
        run: |
          STATUS="${{ job.status }}"
          COLOR=$([ "$STATUS" == "success" ] && echo "green" || echo "red")
          ICON=$([ "$STATUS" == "success" ] && echo "🚀" || echo "⚠️")
          STATUS_TEXT=$([ "$STATUS" == "success" ] && echo "成功" || echo "失败")
          
          COMMIT_MESSAGE=$(echo "${{ github.event.head_commit.message }}" | sed 's/"/\\"/g' | tr '\n' ' ')
          
          curl -X POST -H "Content-Type: application/json" \
          -d "{
            \"msg_type\": \"interactive\",
            \"card\": {
              \"header\": {
                \"title\": {
                  \"tag\": \"plain_text\",
                  \"content\": \"${ICON} Cloudflare Workers 部署${STATUS_TEXT}\"
                },
                \"template\": \"${COLOR}\"
              },
              \"elements\": [
                {
                  \"tag\": \"div\",
                  \"text\": {
                    \"tag\": \"lark_md\",
                    \"content\": \"$([ "$STATUS" == "success" ] && echo "✅" || echo "❌") **部署状态**：${STATUS_TEXT}\\n🌍 **环境**：生产环境\\n📦 **仓库**：[${{ github.repository }}](${{ github.server_url }}/${{ github.repository }})\\n🔀 **分支**：${{ github.ref_name }}\\n📝 **提交**：[${{ github.sha }}](${{ github.server_url }}/${{ github.repository }}/commit/${{ github.sha }})\\n👤 **触发者**：${{ github.actor }}\\n\\n📝 **提交信息**：${COMMIT_MESSAGE}\"
                  }
                }
              ]
            }
          }" ${{ secrets.FEISHU_WEBHOOK_URL }}
