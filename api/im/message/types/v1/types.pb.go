// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/im/message/types/v1/types.proto

package api_im_message_types_v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MessageType int32

const (
	MessageType_MESSAGE_TYPE_UNSPECIFIED MessageType = 0
	MessageType_MESSAGE_TYPE_TEXT        MessageType = 1
	MessageType_MESSAGE_TYPE_IMAGE       MessageType = 2
	MessageType_MESSAGE_TYPE_AUDIO       MessageType = 3
	MessageType_MESSAGE_TYPE_VIDEO       MessageType = 4
	MessageType_MESSAGE_TYPE_FILE        MessageType = 5
	MessageType_MESSAGE_TYPE_LOCATION    MessageType = 6
	MessageType_MESSAGE_TYPE_CUSTOM      MessageType = 7
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MESSAGE_TYPE_UNSPECIFIED",
		1: "MESSAGE_TYPE_TEXT",
		2: "MESSAGE_TYPE_IMAGE",
		3: "MESSAGE_TYPE_AUDIO",
		4: "MESSAGE_TYPE_VIDEO",
		5: "MESSAGE_TYPE_FILE",
		6: "MESSAGE_TYPE_LOCATION",
		7: "MESSAGE_TYPE_CUSTOM",
	}
	MessageType_value = map[string]int32{
		"MESSAGE_TYPE_UNSPECIFIED": 0,
		"MESSAGE_TYPE_TEXT":        1,
		"MESSAGE_TYPE_IMAGE":       2,
		"MESSAGE_TYPE_AUDIO":       3,
		"MESSAGE_TYPE_VIDEO":       4,
		"MESSAGE_TYPE_FILE":        5,
		"MESSAGE_TYPE_LOCATION":    6,
		"MESSAGE_TYPE_CUSTOM":      7,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{0}
}

type CustomMessageType int32

const (
	CustomMessageType_CUSTOM_MESSAGE_TYPE_UNSPECIFIED CustomMessageType = 0
	// 换图 story 交互消息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION CustomMessageType = 1
	// 海龟汤 story 交互消息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION CustomMessageType = 2
	// Fizz story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION CustomMessageType = 3
	// NowShot story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION CustomMessageType = 4
	// Unmute story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION CustomMessageType = 5
	// Wassup story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION CustomMessageType = 6
	// Capsule story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION CustomMessageType = 8
	// Rush fizz 邀请信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_RUSH_FIZZ_INVITE CustomMessageType = 9
	// Relay fizz 邀请信息 TODO 配合客户端去除 STORY 部分 @Larry
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE CustomMessageType = 7
	// Boo 发现/捕捉 ghost 消息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_BOO_INTERACTION CustomMessageType = 10
	// Roasted story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION CustomMessageType = 11
	// ChatProxy 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION CustomMessageType = 12
	// Who 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION CustomMessageType = 13
	// Haunt story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION CustomMessageType = 14
	// BasePlay story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION CustomMessageType = 15
	// Hide sticker 解锁消息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED CustomMessageType = 16
	// share create moment
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT CustomMessageType = 17
	// moment 引用类消息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_MOMENT_QUOTE CustomMessageType = 18
	// pin story 交互信息
	CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION CustomMessageType = 19
)

// Enum value maps for CustomMessageType.
var (
	CustomMessageType_name = map[int32]string{
		0:  "CUSTOM_MESSAGE_TYPE_UNSPECIFIED",
		1:  "CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION",
		2:  "CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION",
		3:  "CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION",
		4:  "CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION",
		5:  "CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION",
		6:  "CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION",
		8:  "CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION",
		9:  "CUSTOM_MESSAGE_TYPE_RUSH_FIZZ_INVITE",
		7:  "CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE",
		10: "CUSTOM_MESSAGE_TYPE_BOO_INTERACTION",
		11: "CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION",
		12: "CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION",
		13: "CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION",
		14: "CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION",
		15: "CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION",
		16: "CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED",
		17: "CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT",
		18: "CUSTOM_MESSAGE_TYPE_STORY_MOMENT_QUOTE",
		19: "CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION",
	}
	CustomMessageType_value = map[string]int32{
		"CUSTOM_MESSAGE_TYPE_UNSPECIFIED":                      0,
		"CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION": 1,
		"CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION":    2,
		"CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION":           3,
		"CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION":       4,
		"CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION":         5,
		"CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION":         6,
		"CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION":        8,
		"CUSTOM_MESSAGE_TYPE_RUSH_FIZZ_INVITE":                 9,
		"CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE":          7,
		"CUSTOM_MESSAGE_TYPE_BOO_INTERACTION":                  10,
		"CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION":        11,
		"CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION":      12,
		"CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION":            13,
		"CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION":          14,
		"CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION":       15,
		"CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED":      16,
		"CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT":        17,
		"CUSTOM_MESSAGE_TYPE_STORY_MOMENT_QUOTE":               18,
		"CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION":            19,
	}
)

func (x CustomMessageType) Enum() *CustomMessageType {
	p := new(CustomMessageType)
	*p = x
	return p
}

func (x CustomMessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CustomMessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[1].Descriptor()
}

func (CustomMessageType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[1]
}

func (x CustomMessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CustomMessageType.Descriptor instead.
func (CustomMessageType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{1}
}

type ConsumeStatus int32

const (
	ConsumeStatus_CONSUME_STATUS_UNSPECIFIED ConsumeStatus = 0
	// 消费成功
	ConsumeStatus_CONSUME_STATUS_SUCCESSFUL ConsumeStatus = 1
	// 消费失败
	ConsumeStatus_CONSUME_STATUS_FAILED ConsumeStatus = 2
	// 消费部分成功
	ConsumeStatus_CONSUME_STATUS_PARTIAL_SUCCESSFUL ConsumeStatus = 3
)

// Enum value maps for ConsumeStatus.
var (
	ConsumeStatus_name = map[int32]string{
		0: "CONSUME_STATUS_UNSPECIFIED",
		1: "CONSUME_STATUS_SUCCESSFUL",
		2: "CONSUME_STATUS_FAILED",
		3: "CONSUME_STATUS_PARTIAL_SUCCESSFUL",
	}
	ConsumeStatus_value = map[string]int32{
		"CONSUME_STATUS_UNSPECIFIED":        0,
		"CONSUME_STATUS_SUCCESSFUL":         1,
		"CONSUME_STATUS_FAILED":             2,
		"CONSUME_STATUS_PARTIAL_SUCCESSFUL": 3,
	}
)

func (x ConsumeStatus) Enum() *ConsumeStatus {
	p := new(ConsumeStatus)
	*p = x
	return p
}

func (x ConsumeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[2].Descriptor()
}

func (ConsumeStatus) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[2]
}

func (x ConsumeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsumeStatus.Descriptor instead.
func (ConsumeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{2}
}

type StoryNowShotCustomMessagePayload_PayloadType int32

const (
	StoryNowShotCustomMessagePayload_PAYLOAD_TYPE_UNSPECIFIED StoryNowShotCustomMessagePayload_PayloadType = 0
	StoryNowShotCustomMessagePayload_PAYLOAD_TYPE_TEXT        StoryNowShotCustomMessagePayload_PayloadType = 1
	StoryNowShotCustomMessagePayload_PAYLOAD_TYPE_IMAGE       StoryNowShotCustomMessagePayload_PayloadType = 2
	StoryNowShotCustomMessagePayload_PAYLOAD_TYPE_VIDEO       StoryNowShotCustomMessagePayload_PayloadType = 3
)

// Enum value maps for StoryNowShotCustomMessagePayload_PayloadType.
var (
	StoryNowShotCustomMessagePayload_PayloadType_name = map[int32]string{
		0: "PAYLOAD_TYPE_UNSPECIFIED",
		1: "PAYLOAD_TYPE_TEXT",
		2: "PAYLOAD_TYPE_IMAGE",
		3: "PAYLOAD_TYPE_VIDEO",
	}
	StoryNowShotCustomMessagePayload_PayloadType_value = map[string]int32{
		"PAYLOAD_TYPE_UNSPECIFIED": 0,
		"PAYLOAD_TYPE_TEXT":        1,
		"PAYLOAD_TYPE_IMAGE":       2,
		"PAYLOAD_TYPE_VIDEO":       3,
	}
)

func (x StoryNowShotCustomMessagePayload_PayloadType) Enum() *StoryNowShotCustomMessagePayload_PayloadType {
	p := new(StoryNowShotCustomMessagePayload_PayloadType)
	*p = x
	return p
}

func (x StoryNowShotCustomMessagePayload_PayloadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryNowShotCustomMessagePayload_PayloadType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[3].Descriptor()
}

func (StoryNowShotCustomMessagePayload_PayloadType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[3]
}

func (x StoryNowShotCustomMessagePayload_PayloadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryNowShotCustomMessagePayload_PayloadType.Descriptor instead.
func (StoryNowShotCustomMessagePayload_PayloadType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{9, 0}
}

type StoryTurtleSoupCustomMessagePayload_PayloadType int32

const (
	StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_UNSPECIFIED StoryTurtleSoupCustomMessagePayload_PayloadType = 0
	StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_TEXT        StoryTurtleSoupCustomMessagePayload_PayloadType = 1
	StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_END_IMAGE   StoryTurtleSoupCustomMessagePayload_PayloadType = 2
)

// Enum value maps for StoryTurtleSoupCustomMessagePayload_PayloadType.
var (
	StoryTurtleSoupCustomMessagePayload_PayloadType_name = map[int32]string{
		0: "PAYLOAD_TYPE_UNSPECIFIED",
		1: "PAYLOAD_TYPE_TEXT",
		2: "PAYLOAD_TYPE_END_IMAGE",
	}
	StoryTurtleSoupCustomMessagePayload_PayloadType_value = map[string]int32{
		"PAYLOAD_TYPE_UNSPECIFIED": 0,
		"PAYLOAD_TYPE_TEXT":        1,
		"PAYLOAD_TYPE_END_IMAGE":   2,
	}
)

func (x StoryTurtleSoupCustomMessagePayload_PayloadType) Enum() *StoryTurtleSoupCustomMessagePayload_PayloadType {
	p := new(StoryTurtleSoupCustomMessagePayload_PayloadType)
	*p = x
	return p
}

func (x StoryTurtleSoupCustomMessagePayload_PayloadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryTurtleSoupCustomMessagePayload_PayloadType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[4].Descriptor()
}

func (StoryTurtleSoupCustomMessagePayload_PayloadType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[4]
}

func (x StoryTurtleSoupCustomMessagePayload_PayloadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryTurtleSoupCustomMessagePayload_PayloadType.Descriptor instead.
func (StoryTurtleSoupCustomMessagePayload_PayloadType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{10, 0}
}

type StoryFizzCustomMessagePayload_PayloadType int32

const (
	StoryFizzCustomMessagePayload_PAYLOAD_TYPE_UNSPECIFIED StoryFizzCustomMessagePayload_PayloadType = 0
	StoryFizzCustomMessagePayload_PAYLOAD_TYPE_TEXT        StoryFizzCustomMessagePayload_PayloadType = 1
	StoryFizzCustomMessagePayload_PAYLOAD_TYPE_IMAGE       StoryFizzCustomMessagePayload_PayloadType = 2
	StoryFizzCustomMessagePayload_PAYLOAD_TYPE_VIDEO       StoryFizzCustomMessagePayload_PayloadType = 3
)

// Enum value maps for StoryFizzCustomMessagePayload_PayloadType.
var (
	StoryFizzCustomMessagePayload_PayloadType_name = map[int32]string{
		0: "PAYLOAD_TYPE_UNSPECIFIED",
		1: "PAYLOAD_TYPE_TEXT",
		2: "PAYLOAD_TYPE_IMAGE",
		3: "PAYLOAD_TYPE_VIDEO",
	}
	StoryFizzCustomMessagePayload_PayloadType_value = map[string]int32{
		"PAYLOAD_TYPE_UNSPECIFIED": 0,
		"PAYLOAD_TYPE_TEXT":        1,
		"PAYLOAD_TYPE_IMAGE":       2,
		"PAYLOAD_TYPE_VIDEO":       3,
	}
)

func (x StoryFizzCustomMessagePayload_PayloadType) Enum() *StoryFizzCustomMessagePayload_PayloadType {
	p := new(StoryFizzCustomMessagePayload_PayloadType)
	*p = x
	return p
}

func (x StoryFizzCustomMessagePayload_PayloadType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryFizzCustomMessagePayload_PayloadType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[5].Descriptor()
}

func (StoryFizzCustomMessagePayload_PayloadType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[5]
}

func (x StoryFizzCustomMessagePayload_PayloadType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryFizzCustomMessagePayload_PayloadType.Descriptor instead.
func (StoryFizzCustomMessagePayload_PayloadType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{11, 0}
}

type BooInteractionCustomMessagePayload_MessageType int32

const (
	BooInteractionCustomMessagePayload_MESSAGE_TYPE_UNSPECIFIED       BooInteractionCustomMessagePayload_MessageType = 0
	BooInteractionCustomMessagePayload_MESSAGE_TYPE_FOUND_BOO         BooInteractionCustomMessagePayload_MessageType = 1
	BooInteractionCustomMessagePayload_MESSAGE_TYPE_CAUGHT_BOO        BooInteractionCustomMessagePayload_MessageType = 2
	BooInteractionCustomMessagePayload_MESSAGE_TYPE_CAUGHT_FAILED_BOO BooInteractionCustomMessagePayload_MessageType = 3
)

// Enum value maps for BooInteractionCustomMessagePayload_MessageType.
var (
	BooInteractionCustomMessagePayload_MessageType_name = map[int32]string{
		0: "MESSAGE_TYPE_UNSPECIFIED",
		1: "MESSAGE_TYPE_FOUND_BOO",
		2: "MESSAGE_TYPE_CAUGHT_BOO",
		3: "MESSAGE_TYPE_CAUGHT_FAILED_BOO",
	}
	BooInteractionCustomMessagePayload_MessageType_value = map[string]int32{
		"MESSAGE_TYPE_UNSPECIFIED":       0,
		"MESSAGE_TYPE_FOUND_BOO":         1,
		"MESSAGE_TYPE_CAUGHT_BOO":        2,
		"MESSAGE_TYPE_CAUGHT_FAILED_BOO": 3,
	}
)

func (x BooInteractionCustomMessagePayload_MessageType) Enum() *BooInteractionCustomMessagePayload_MessageType {
	p := new(BooInteractionCustomMessagePayload_MessageType)
	*p = x
	return p
}

func (x BooInteractionCustomMessagePayload_MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooInteractionCustomMessagePayload_MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[6].Descriptor()
}

func (BooInteractionCustomMessagePayload_MessageType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[6]
}

func (x BooInteractionCustomMessagePayload_MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooInteractionCustomMessagePayload_MessageType.Descriptor instead.
func (BooInteractionCustomMessagePayload_MessageType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{14, 0}
}

type StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType int32

const (
	StoryShareCreateMomentCustomMessagePayload_SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType = 0
	StoryShareCreateMomentCustomMessagePayload_SHARE_CREATE_MOMENT_TYPE_STORY       StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType = 1
	StoryShareCreateMomentCustomMessagePayload_SHARE_CREATE_MOMENT_TYPE_MOMENT      StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType = 2
)

// Enum value maps for StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType.
var (
	StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType_name = map[int32]string{
		0: "SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED",
		1: "SHARE_CREATE_MOMENT_TYPE_STORY",
		2: "SHARE_CREATE_MOMENT_TYPE_MOMENT",
	}
	StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType_value = map[string]int32{
		"SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED": 0,
		"SHARE_CREATE_MOMENT_TYPE_STORY":       1,
		"SHARE_CREATE_MOMENT_TYPE_MOMENT":      2,
	}
)

func (x StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType) Enum() *StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType {
	p := new(StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType)
	*p = x
	return p
}

func (x StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_im_message_types_v1_types_proto_enumTypes[7].Descriptor()
}

func (StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType) Type() protoreflect.EnumType {
	return &file_api_im_message_types_v1_types_proto_enumTypes[7]
}

func (x StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType.Descriptor instead.
func (StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType) EnumDescriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{18, 0}
}

type MessageBody struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Data:
	//
	//	*MessageBody_Content
	//	*MessageBody_CustomMessagePayload
	Data          isMessageBody_Data `protobuf_oneof:"data"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageBody) Reset() {
	*x = MessageBody{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageBody) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageBody) ProtoMessage() {}

func (x *MessageBody) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageBody.ProtoReflect.Descriptor instead.
func (*MessageBody) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *MessageBody) GetData() isMessageBody_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MessageBody) GetContent() string {
	if x != nil {
		if x, ok := x.Data.(*MessageBody_Content); ok {
			return x.Content
		}
	}
	return ""
}

func (x *MessageBody) GetCustomMessagePayload() *CustomMessagePayload {
	if x != nil {
		if x, ok := x.Data.(*MessageBody_CustomMessagePayload); ok {
			return x.CustomMessagePayload
		}
	}
	return nil
}

type isMessageBody_Data interface {
	isMessageBody_Data()
}

type MessageBody_Content struct {
	// 当且仅当 message_type 为 TEXT 时有效
	Content string `protobuf:"bytes,1,opt,name=content,proto3,oneof"`
}

type MessageBody_CustomMessagePayload struct {
	// 当且仅当 message_type 为 CUSTOM 时有效
	CustomMessagePayload *CustomMessagePayload `protobuf:"bytes,2,opt,name=custom_message_payload,json=customMessagePayload,proto3,oneof"`
}

func (*MessageBody_Content) isMessageBody_Data() {}

func (*MessageBody_CustomMessagePayload) isMessageBody_Data() {}

type Message struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MessageType   MessageType            `protobuf:"varint,1,opt,name=message_type,json=messageType,proto3,enum=api.im.message.types.v1.MessageType" json:"message_type,omitempty"`
	Body          *MessageBody           `protobuf:"bytes,2,opt,name=body,proto3" json:"body,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *Message) GetMessageType() MessageType {
	if x != nil {
		return x.MessageType
	}
	return MessageType_MESSAGE_TYPE_UNSPECIFIED
}

func (x *Message) GetBody() *MessageBody {
	if x != nil {
		return x.Body
	}
	return nil
}

type StoryExchangeImageInteractionCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消费者发送的图片
	ConsumerImageUrl string `protobuf:"bytes,1,opt,name=consumer_image_url,json=consumerImageUrl,proto3" json:"consumer_image_url,omitempty"`
	// 消费者交互的图片
	ConsumerInteractionImageUrl string `protobuf:"bytes,2,opt,name=consumer_interaction_image_url,json=consumerInteractionImageUrl,proto3" json:"consumer_interaction_image_url,omitempty"`
	// 消费者交互的 story id
	StoryId string `protobuf:"bytes,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 消费者交互的节点 id
	NodeId string `protobuf:"bytes,4,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	// 当前消息的 title
	// 比如 the other party replied to your story
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,6,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"` // 消费结果
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) Reset() {
	*x = StoryExchangeImageInteractionCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryExchangeImageInteractionCustomMessagePayload) ProtoMessage() {}

func (x *StoryExchangeImageInteractionCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryExchangeImageInteractionCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryExchangeImageInteractionCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) GetConsumerImageUrl() string {
	if x != nil {
		return x.ConsumerImageUrl
	}
	return ""
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) GetConsumerInteractionImageUrl() string {
	if x != nil {
		return x.ConsumerInteractionImageUrl
	}
	return ""
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *StoryExchangeImageInteractionCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

type StoryChatProxyCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消费者录制的视频封面
	ConsumerVideoCoverUrl string `protobuf:"bytes,1,opt,name=consumer_video_cover_url,json=consumerVideoCoverUrl,proto3" json:"consumer_video_cover_url,omitempty"`
	// 消费者录制的视频
	ConsumerVideoUrl string `protobuf:"bytes,2,opt,name=consumer_video_url,json=consumerVideoUrl,proto3" json:"consumer_video_url,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,3,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	// 交互的 Story 的 id
	StoryId       string `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryChatProxyCustomMessagePayload) Reset() {
	*x = StoryChatProxyCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryChatProxyCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryChatProxyCustomMessagePayload) ProtoMessage() {}

func (x *StoryChatProxyCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryChatProxyCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryChatProxyCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{3}
}

func (x *StoryChatProxyCustomMessagePayload) GetConsumerVideoCoverUrl() string {
	if x != nil {
		return x.ConsumerVideoCoverUrl
	}
	return ""
}

func (x *StoryChatProxyCustomMessagePayload) GetConsumerVideoUrl() string {
	if x != nil {
		return x.ConsumerVideoUrl
	}
	return ""
}

func (x *StoryChatProxyCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryChatProxyCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type StoryRoastedCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消费者录制的视频封面
	ConsumerVideoCoverUrl string `protobuf:"bytes,1,opt,name=consumer_video_cover_url,json=consumerVideoCoverUrl,proto3" json:"consumer_video_cover_url,omitempty"`
	// 消费者录制的视频
	ConsumerVideoUrl string `protobuf:"bytes,2,opt,name=consumer_video_url,json=consumerVideoUrl,proto3" json:"consumer_video_url,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,3,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	// 交互的 Story 的 id
	StoryId       string `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryRoastedCustomMessagePayload) Reset() {
	*x = StoryRoastedCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryRoastedCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryRoastedCustomMessagePayload) ProtoMessage() {}

func (x *StoryRoastedCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryRoastedCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryRoastedCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{4}
}

func (x *StoryRoastedCustomMessagePayload) GetConsumerVideoCoverUrl() string {
	if x != nil {
		return x.ConsumerVideoCoverUrl
	}
	return ""
}

func (x *StoryRoastedCustomMessagePayload) GetConsumerVideoUrl() string {
	if x != nil {
		return x.ConsumerVideoUrl
	}
	return ""
}

func (x *StoryRoastedCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryRoastedCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type StoryUnmuteCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消费者发送的音频
	ConsumerAudioUrl string `protobuf:"bytes,1,opt,name=consumer_audio_url,json=consumerAudioUrl,proto3" json:"consumer_audio_url,omitempty"`
	// 消费者交互的图片
	ConsumerInteractionImageUrl string `protobuf:"bytes,2,opt,name=consumer_interaction_image_url,json=consumerInteractionImageUrl,proto3" json:"consumer_interaction_image_url,omitempty"`
	// 消费者交互的 story id
	StoryId string `protobuf:"bytes,3,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 当前消息的 title
	// 比如 the other party replied to your story
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,5,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	// ai 回复
	AiResponse    string `protobuf:"bytes,6,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryUnmuteCustomMessagePayload) Reset() {
	*x = StoryUnmuteCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryUnmuteCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryUnmuteCustomMessagePayload) ProtoMessage() {}

func (x *StoryUnmuteCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryUnmuteCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryUnmuteCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{5}
}

func (x *StoryUnmuteCustomMessagePayload) GetConsumerAudioUrl() string {
	if x != nil {
		return x.ConsumerAudioUrl
	}
	return ""
}

func (x *StoryUnmuteCustomMessagePayload) GetConsumerInteractionImageUrl() string {
	if x != nil {
		return x.ConsumerInteractionImageUrl
	}
	return ""
}

func (x *StoryUnmuteCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryUnmuteCustomMessagePayload) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *StoryUnmuteCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryUnmuteCustomMessagePayload) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

type StoryCapsuleCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消费者交互的 story id
	StoryId string `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,2,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	// 消费者发送的图片
	ConsumerImageUrl string `protobuf:"bytes,3,opt,name=consumer_image_url,json=consumerImageUrl,proto3" json:"consumer_image_url,omitempty"`
	// 当前消息的 title
	// 比如 the other party replied to your story
	Title string `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// 参考 api.items.story.types.v1.CapsulePhotoInfo in days
	// 几天内的图片
	InDays uint32 `protobuf:"varint,5,opt,name=in_days,json=inDays,proto3" json:"in_days,omitempty"`
	// 参考 api.items.story.types.v1.CapsulePhotoInfo moments
	// 几个瞬间
	Moments       uint32 `protobuf:"varint,6,opt,name=moments,proto3" json:"moments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryCapsuleCustomMessagePayload) Reset() {
	*x = StoryCapsuleCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryCapsuleCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryCapsuleCustomMessagePayload) ProtoMessage() {}

func (x *StoryCapsuleCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryCapsuleCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryCapsuleCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{6}
}

func (x *StoryCapsuleCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryCapsuleCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryCapsuleCustomMessagePayload) GetConsumerImageUrl() string {
	if x != nil {
		return x.ConsumerImageUrl
	}
	return ""
}

func (x *StoryCapsuleCustomMessagePayload) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *StoryCapsuleCustomMessagePayload) GetInDays() uint32 {
	if x != nil {
		return x.InDays
	}
	return 0
}

func (x *StoryCapsuleCustomMessagePayload) GetMoments() uint32 {
	if x != nil {
		return x.Moments
	}
	return 0
}

type StoryWhoInteractionCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 所有待选项
	AvatarUrls []string `protobuf:"bytes,1,rep,name=avatar_urls,json=avatarUrls,proto3" json:"avatar_urls,omitempty"`
	// 选中的头像
	SelectedAvatarUrl string `protobuf:"bytes,2,opt,name=selected_avatar_url,json=selectedAvatarUrl,proto3" json:"selected_avatar_url,omitempty"`
	// 选中的用户名
	SelectedUserName string `protobuf:"bytes,3,opt,name=selected_user_name,json=selectedUserName,proto3" json:"selected_user_name,omitempty"`
	// 是否正确
	Correct bool `protobuf:"varint,4,opt,name=correct,proto3" json:"correct,omitempty"`
	// 尝试了几次
	TriedTimes uint32 `protobuf:"varint,5,opt,name=tried_times,json=triedTimes,proto3" json:"tried_times,omitempty"`
	// 交互的 story id
	StoryId       string `protobuf:"bytes,6,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryWhoInteractionCustomMessagePayload) Reset() {
	*x = StoryWhoInteractionCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryWhoInteractionCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryWhoInteractionCustomMessagePayload) ProtoMessage() {}

func (x *StoryWhoInteractionCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryWhoInteractionCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryWhoInteractionCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{7}
}

func (x *StoryWhoInteractionCustomMessagePayload) GetAvatarUrls() []string {
	if x != nil {
		return x.AvatarUrls
	}
	return nil
}

func (x *StoryWhoInteractionCustomMessagePayload) GetSelectedAvatarUrl() string {
	if x != nil {
		return x.SelectedAvatarUrl
	}
	return ""
}

func (x *StoryWhoInteractionCustomMessagePayload) GetSelectedUserName() string {
	if x != nil {
		return x.SelectedUserName
	}
	return ""
}

func (x *StoryWhoInteractionCustomMessagePayload) GetCorrect() bool {
	if x != nil {
		return x.Correct
	}
	return false
}

func (x *StoryWhoInteractionCustomMessagePayload) GetTriedTimes() uint32 {
	if x != nil {
		return x.TriedTimes
	}
	return 0
}

func (x *StoryWhoInteractionCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type StoryWassupCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 消费者录制的视频封面
	ConsumerVideoCoverUrl string `protobuf:"bytes,1,opt,name=consumer_video_cover_url,json=consumerVideoCoverUrl,proto3" json:"consumer_video_cover_url,omitempty"`
	// 消费者录制的视频
	ConsumerVideoUrl string `protobuf:"bytes,2,opt,name=consumer_video_url,json=consumerVideoUrl,proto3" json:"consumer_video_url,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,3,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	// 交互的 Story 的 id
	StoryId       string `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryWassupCustomMessagePayload) Reset() {
	*x = StoryWassupCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryWassupCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryWassupCustomMessagePayload) ProtoMessage() {}

func (x *StoryWassupCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryWassupCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryWassupCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{8}
}

func (x *StoryWassupCustomMessagePayload) GetConsumerVideoCoverUrl() string {
	if x != nil {
		return x.ConsumerVideoCoverUrl
	}
	return ""
}

func (x *StoryWassupCustomMessagePayload) GetConsumerVideoUrl() string {
	if x != nil {
		return x.ConsumerVideoUrl
	}
	return ""
}

func (x *StoryWassupCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryWassupCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type StoryNowShotCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 负载类型，参考 PayloadType
	PayloadType string `protobuf:"bytes,1,opt,name=payload_type,json=payloadType,proto3" json:"payload_type,omitempty"`
	// 消费者发送的图片，如果 payload 是 video 则为 thumbnail
	ConsumerImageUrl string `protobuf:"bytes,2,opt,name=consumer_image_url,json=consumerImageUrl,proto3" json:"consumer_image_url,omitempty"`
	// 消费者发送的视频
	ConsumerVideoUrl string `protobuf:"bytes,3,opt,name=consumer_video_url,json=consumerVideoUrl,proto3" json:"consumer_video_url,omitempty"`
	// 消费者交互的图片,如果 payload 是 video 则为 thumbnail
	ConsumerInteractionImageUrl string `protobuf:"bytes,4,opt,name=consumer_interaction_image_url,json=consumerInteractionImageUrl,proto3" json:"consumer_interaction_image_url,omitempty"`
	// 消费者交互的视频
	ConsumerInteractionVideoUrl string `protobuf:"bytes,5,opt,name=consumer_interaction_video_url,json=consumerInteractionVideoUrl,proto3" json:"consumer_interaction_video_url,omitempty"`
	// 消费者交互的 story id
	StoryId string `protobuf:"bytes,6,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 当前消息的 title
	// 比如 the other party replied to your story
	Title string `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	// 交互的 Story 的封面
	StoryCoverUrl string `protobuf:"bytes,8,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryNowShotCustomMessagePayload) Reset() {
	*x = StoryNowShotCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryNowShotCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryNowShotCustomMessagePayload) ProtoMessage() {}

func (x *StoryNowShotCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryNowShotCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryNowShotCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{9}
}

func (x *StoryNowShotCustomMessagePayload) GetPayloadType() string {
	if x != nil {
		return x.PayloadType
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetConsumerImageUrl() string {
	if x != nil {
		return x.ConsumerImageUrl
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetConsumerVideoUrl() string {
	if x != nil {
		return x.ConsumerVideoUrl
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetConsumerInteractionImageUrl() string {
	if x != nil {
		return x.ConsumerInteractionImageUrl
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetConsumerInteractionVideoUrl() string {
	if x != nil {
		return x.ConsumerInteractionVideoUrl
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *StoryNowShotCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

type StoryTurtleSoupCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 负载类型，参考 PayloadType
	PayloadType string `protobuf:"bytes,1,opt,name=payload_type,json=payloadType,proto3" json:"payload_type,omitempty"`
	// 交互的 Story 的 json 字符串，结构为:
	//
	//	{
	//	  "story_id": "string", // 交互的 Story 的 id
	//	  "story_cover_url": "string", // 交互的 Story 的封面
	//	  "caption": "string",         // 显示在屏幕中间的 caption
	//	  "tips": "string",           // 显示在屏幕中间的 tips
	//	  "ai_response": "string",    // ai 的回复
	//	  "end_message": "string",    // 隐藏意图，用于帮助 hit_words 命中
	//	  "end_message_font": "string" // 隐藏意图字体
	//	}
	StoryInfoJsonStr string `protobuf:"bytes,2,opt,name=story_info_json_str,json=storyInfoJsonStr,proto3" json:"story_info_json_str,omitempty"`
	// Types that are valid to be assigned to Payload:
	//
	//	*StoryTurtleSoupCustomMessagePayload_Text
	//	*StoryTurtleSoupCustomMessagePayload_EndImagePayload_
	Payload       isStoryTurtleSoupCustomMessagePayload_Payload `protobuf_oneof:"payload"`
	AiResponse    string                                        `protobuf:"bytes,5,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	UserText      string                                        `protobuf:"bytes,6,opt,name=user_text,json=userText,proto3" json:"user_text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryTurtleSoupCustomMessagePayload) Reset() {
	*x = StoryTurtleSoupCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryTurtleSoupCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryTurtleSoupCustomMessagePayload) ProtoMessage() {}

func (x *StoryTurtleSoupCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryTurtleSoupCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryTurtleSoupCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{10}
}

func (x *StoryTurtleSoupCustomMessagePayload) GetPayloadType() string {
	if x != nil {
		return x.PayloadType
	}
	return ""
}

func (x *StoryTurtleSoupCustomMessagePayload) GetStoryInfoJsonStr() string {
	if x != nil {
		return x.StoryInfoJsonStr
	}
	return ""
}

func (x *StoryTurtleSoupCustomMessagePayload) GetPayload() isStoryTurtleSoupCustomMessagePayload_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *StoryTurtleSoupCustomMessagePayload) GetText() string {
	if x != nil {
		if x, ok := x.Payload.(*StoryTurtleSoupCustomMessagePayload_Text); ok {
			return x.Text
		}
	}
	return ""
}

func (x *StoryTurtleSoupCustomMessagePayload) GetEndImagePayload() *StoryTurtleSoupCustomMessagePayload_EndImagePayload {
	if x != nil {
		if x, ok := x.Payload.(*StoryTurtleSoupCustomMessagePayload_EndImagePayload_); ok {
			return x.EndImagePayload
		}
	}
	return nil
}

func (x *StoryTurtleSoupCustomMessagePayload) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

func (x *StoryTurtleSoupCustomMessagePayload) GetUserText() string {
	if x != nil {
		return x.UserText
	}
	return ""
}

type isStoryTurtleSoupCustomMessagePayload_Payload interface {
	isStoryTurtleSoupCustomMessagePayload_Payload()
}

type StoryTurtleSoupCustomMessagePayload_Text struct {
	// 仅当 payload_type 为 PAYLOAD_TYPE_TEXT 时有效
	// 表示消费者或者创作者发送的文字消息
	Text string `protobuf:"bytes,3,opt,name=text,proto3,oneof"`
}

type StoryTurtleSoupCustomMessagePayload_EndImagePayload_ struct {
	// 仅当 payload_type 为 PAYLOAD_TYPE_END_IMAGE 时有效
	EndImagePayload *StoryTurtleSoupCustomMessagePayload_EndImagePayload `protobuf:"bytes,4,opt,name=end_image_payload,json=endImagePayload,proto3,oneof"`
}

func (*StoryTurtleSoupCustomMessagePayload_Text) isStoryTurtleSoupCustomMessagePayload_Payload() {}

func (*StoryTurtleSoupCustomMessagePayload_EndImagePayload_) isStoryTurtleSoupCustomMessagePayload_Payload() {
}

type StoryFizzCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 负载类型，参考 PayloadType
	PayloadType string `protobuf:"bytes,1,opt,name=payload_type,json=payloadType,proto3" json:"payload_type,omitempty"`
	FizzId      string `protobuf:"bytes,2,opt,name=fizz_id,json=fizzId,proto3" json:"fizz_id,omitempty"`
	// 表示消费者发送的文字消息，当 payload_type 为 PAYLOAD_TYPE_IMAGE 和 PAYLOAD_TYPE_VIDEO 时，系统自动生成文字
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	// Types that are valid to be assigned to Payload:
	//
	//	*StoryFizzCustomMessagePayload_ImageUrl
	//	*StoryFizzCustomMessagePayload_VideoPayload_
	Payload isStoryFizzCustomMessagePayload_Payload `protobuf_oneof:"payload"`
	// 消息过期时间
	ExpireAt      string `protobuf:"bytes,6,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	MediaId       string `protobuf:"bytes,7,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryFizzCustomMessagePayload) Reset() {
	*x = StoryFizzCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryFizzCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryFizzCustomMessagePayload) ProtoMessage() {}

func (x *StoryFizzCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryFizzCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryFizzCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{11}
}

func (x *StoryFizzCustomMessagePayload) GetPayloadType() string {
	if x != nil {
		return x.PayloadType
	}
	return ""
}

func (x *StoryFizzCustomMessagePayload) GetFizzId() string {
	if x != nil {
		return x.FizzId
	}
	return ""
}

func (x *StoryFizzCustomMessagePayload) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *StoryFizzCustomMessagePayload) GetPayload() isStoryFizzCustomMessagePayload_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *StoryFizzCustomMessagePayload) GetImageUrl() string {
	if x != nil {
		if x, ok := x.Payload.(*StoryFizzCustomMessagePayload_ImageUrl); ok {
			return x.ImageUrl
		}
	}
	return ""
}

func (x *StoryFizzCustomMessagePayload) GetVideoPayload() *StoryFizzCustomMessagePayload_VideoPayload {
	if x != nil {
		if x, ok := x.Payload.(*StoryFizzCustomMessagePayload_VideoPayload_); ok {
			return x.VideoPayload
		}
	}
	return nil
}

func (x *StoryFizzCustomMessagePayload) GetExpireAt() string {
	if x != nil {
		return x.ExpireAt
	}
	return ""
}

func (x *StoryFizzCustomMessagePayload) GetMediaId() string {
	if x != nil {
		return x.MediaId
	}
	return ""
}

type isStoryFizzCustomMessagePayload_Payload interface {
	isStoryFizzCustomMessagePayload_Payload()
}

type StoryFizzCustomMessagePayload_ImageUrl struct {
	// 仅当 payload_type 为 PAYLOAD_TYPE_IMAGE 时有效
	ImageUrl string `protobuf:"bytes,4,opt,name=image_url,json=imageUrl,proto3,oneof"`
}

type StoryFizzCustomMessagePayload_VideoPayload_ struct {
	// 仅当 payload_type 为 PAYLOAD_TYPE_VIDEO 时有效
	VideoPayload *StoryFizzCustomMessagePayload_VideoPayload `protobuf:"bytes,5,opt,name=video_payload,json=videoPayload,proto3,oneof"`
}

func (*StoryFizzCustomMessagePayload_ImageUrl) isStoryFizzCustomMessagePayload_Payload() {}

func (*StoryFizzCustomMessagePayload_VideoPayload_) isStoryFizzCustomMessagePayload_Payload() {}

type RelayFizzJoinInviteCustomMessagePayload struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	RoomId    string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	CreatorId string                 `protobuf:"bytes,2,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// 暂时没用，先留着
	ExpireAtUnixstamp uint32 `protobuf:"varint,3,opt,name=expire_at_unixstamp,json=expireAtUnixstamp,proto3" json:"expire_at_unixstamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RelayFizzJoinInviteCustomMessagePayload) Reset() {
	*x = RelayFizzJoinInviteCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelayFizzJoinInviteCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelayFizzJoinInviteCustomMessagePayload) ProtoMessage() {}

func (x *RelayFizzJoinInviteCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelayFizzJoinInviteCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*RelayFizzJoinInviteCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{12}
}

func (x *RelayFizzJoinInviteCustomMessagePayload) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RelayFizzJoinInviteCustomMessagePayload) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *RelayFizzJoinInviteCustomMessagePayload) GetExpireAtUnixstamp() uint32 {
	if x != nil {
		return x.ExpireAtUnixstamp
	}
	return 0
}

type RushFizzJoinInviteCustomMessagePayload struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	RoomId    string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	CreatorId string                 `protobuf:"bytes,2,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// 暂时没用，先留着
	ExpireAtUnixstamp uint32 `protobuf:"varint,3,opt,name=expire_at_unixstamp,json=expireAtUnixstamp,proto3" json:"expire_at_unixstamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *RushFizzJoinInviteCustomMessagePayload) Reset() {
	*x = RushFizzJoinInviteCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RushFizzJoinInviteCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RushFizzJoinInviteCustomMessagePayload) ProtoMessage() {}

func (x *RushFizzJoinInviteCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RushFizzJoinInviteCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*RushFizzJoinInviteCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{13}
}

func (x *RushFizzJoinInviteCustomMessagePayload) GetRoomId() string {
	if x != nil {
		return x.RoomId
	}
	return ""
}

func (x *RushFizzJoinInviteCustomMessagePayload) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *RushFizzJoinInviteCustomMessagePayload) GetExpireAtUnixstamp() uint32 {
	if x != nil {
		return x.ExpireAtUnixstamp
	}
	return 0
}

type BooInteractionCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 负载类型，参考 MessageType
	MessageType  string                                           `protobuf:"bytes,1,opt,name=message_type,json=messageType,proto3" json:"message_type,omitempty"`
	VideoPayload *BooInteractionCustomMessagePayload_VideoPayload `protobuf:"bytes,2,opt,name=video_payload,json=videoPayload,proto3" json:"video_payload,omitempty"`
	// 附带的鬼的头像
	BooAvatarUrl  string `protobuf:"bytes,3,opt,name=boo_avatar_url,json=booAvatarUrl,proto3" json:"boo_avatar_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BooInteractionCustomMessagePayload) Reset() {
	*x = BooInteractionCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooInteractionCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooInteractionCustomMessagePayload) ProtoMessage() {}

func (x *BooInteractionCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooInteractionCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*BooInteractionCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{14}
}

func (x *BooInteractionCustomMessagePayload) GetMessageType() string {
	if x != nil {
		return x.MessageType
	}
	return ""
}

func (x *BooInteractionCustomMessagePayload) GetVideoPayload() *BooInteractionCustomMessagePayload_VideoPayload {
	if x != nil {
		return x.VideoPayload
	}
	return nil
}

func (x *BooInteractionCustomMessagePayload) GetBooAvatarUrl() string {
	if x != nil {
		return x.BooAvatarUrl
	}
	return ""
}

type StoryHauntCustomMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoUrl      string                 `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	VideoCoverUrl string                 `protobuf:"bytes,2,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty"`
	BooAvatarUrl  string                 `protobuf:"bytes,3,opt,name=boo_avatar_url,json=booAvatarUrl,proto3" json:"boo_avatar_url,omitempty"`
	StoryId       string                 `protobuf:"bytes,4,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryHauntCustomMessagePayload) Reset() {
	*x = StoryHauntCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryHauntCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryHauntCustomMessagePayload) ProtoMessage() {}

func (x *StoryHauntCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryHauntCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryHauntCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{15}
}

func (x *StoryHauntCustomMessagePayload) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *StoryHauntCustomMessagePayload) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *StoryHauntCustomMessagePayload) GetBooAvatarUrl() string {
	if x != nil {
		return x.BooAvatarUrl
	}
	return ""
}

func (x *StoryHauntCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type StoryBaseplayCustomMessagePayload struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	StoryId               string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	StoryCoverUrl         string                 `protobuf:"bytes,2,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	ConsumerStoryCoverUrl string                 `protobuf:"bytes,3,opt,name=consumer_story_cover_url,json=consumerStoryCoverUrl,proto3" json:"consumer_story_cover_url,omitempty"`
	Title                 string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *StoryBaseplayCustomMessagePayload) Reset() {
	*x = StoryBaseplayCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryBaseplayCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryBaseplayCustomMessagePayload) ProtoMessage() {}

func (x *StoryBaseplayCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryBaseplayCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryBaseplayCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{16}
}

func (x *StoryBaseplayCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryBaseplayCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryBaseplayCustomMessagePayload) GetConsumerStoryCoverUrl() string {
	if x != nil {
		return x.ConsumerStoryCoverUrl
	}
	return ""
}

func (x *StoryBaseplayCustomMessagePayload) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type StoryHideStickerUnlockedCustomMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	StoryCoverUrl string                 `protobuf:"bytes,2,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	StickerUrl    string                 `protobuf:"bytes,3,opt,name=sticker_url,json=stickerUrl,proto3" json:"sticker_url,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryHideStickerUnlockedCustomMessagePayload) Reset() {
	*x = StoryHideStickerUnlockedCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryHideStickerUnlockedCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryHideStickerUnlockedCustomMessagePayload) ProtoMessage() {}

func (x *StoryHideStickerUnlockedCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryHideStickerUnlockedCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryHideStickerUnlockedCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{17}
}

func (x *StoryHideStickerUnlockedCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryHideStickerUnlockedCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryHideStickerUnlockedCustomMessagePayload) GetStickerUrl() string {
	if x != nil {
		return x.StickerUrl
	}
	return ""
}

func (x *StoryHideStickerUnlockedCustomMessagePayload) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type StoryShareCreateMomentCustomMessagePayload struct {
	state                 protoimpl.MessageState                                           `protogen:"open.v1"`
	StoryId               string                                                           `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	StoryCoverUrl         string                                                           `protobuf:"bytes,2,opt,name=story_cover_url,json=storyCoverUrl,proto3" json:"story_cover_url,omitempty"`
	MomentId              string                                                           `protobuf:"bytes,3,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	ShareCreateMomentType StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType `protobuf:"varint,4,opt,name=share_create_moment_type,json=shareCreateMomentType,proto3,enum=api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType" json:"share_create_moment_type,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *StoryShareCreateMomentCustomMessagePayload) Reset() {
	*x = StoryShareCreateMomentCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryShareCreateMomentCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryShareCreateMomentCustomMessagePayload) ProtoMessage() {}

func (x *StoryShareCreateMomentCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryShareCreateMomentCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryShareCreateMomentCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{18}
}

func (x *StoryShareCreateMomentCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryShareCreateMomentCustomMessagePayload) GetStoryCoverUrl() string {
	if x != nil {
		return x.StoryCoverUrl
	}
	return ""
}

func (x *StoryShareCreateMomentCustomMessagePayload) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

func (x *StoryShareCreateMomentCustomMessagePayload) GetShareCreateMomentType() StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType {
	if x != nil {
		return x.ShareCreateMomentType
	}
	return StoryShareCreateMomentCustomMessagePayload_SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED
}

type StoryMomentQuoteCustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 引用的 moment id
	MomentId string `protobuf:"bytes,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	// moment 所在的 Portal id
	PortalId string `protobuf:"bytes,2,opt,name=portal_id,json=portalId,proto3" json:"portal_id,omitempty"`
	// moment 的封面图片
	FirstMomentCoverImageUrl string `protobuf:"bytes,3,opt,name=first_moment_cover_image_url,json=firstMomentCoverImageUrl,proto3" json:"first_moment_cover_image_url,omitempty"`
	// 发送的字符串内容
	Text          string `protobuf:"bytes,4,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryMomentQuoteCustomMessagePayload) Reset() {
	*x = StoryMomentQuoteCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryMomentQuoteCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryMomentQuoteCustomMessagePayload) ProtoMessage() {}

func (x *StoryMomentQuoteCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryMomentQuoteCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryMomentQuoteCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{19}
}

func (x *StoryMomentQuoteCustomMessagePayload) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

func (x *StoryMomentQuoteCustomMessagePayload) GetPortalId() string {
	if x != nil {
		return x.PortalId
	}
	return ""
}

func (x *StoryMomentQuoteCustomMessagePayload) GetFirstMomentCoverImageUrl() string {
	if x != nil {
		return x.FirstMomentCoverImageUrl
	}
	return ""
}

func (x *StoryMomentQuoteCustomMessagePayload) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type StoryPinInteractionCustomMessagePayload struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	StoryId                   string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	BackgroundImageUrl        string                 `protobuf:"bytes,2,opt,name=background_image_url,json=backgroundImageUrl,proto3" json:"background_image_url,omitempty"`
	SuccessConsumeCostSeconds string                 `protobuf:"bytes,3,opt,name=success_consume_cost_seconds,json=successConsumeCostSeconds,proto3" json:"success_consume_cost_seconds,omitempty"`
	FailedConsumeImageUrl     string                 `protobuf:"bytes,4,opt,name=failed_consume_image_url,json=failedConsumeImageUrl,proto3" json:"failed_consume_image_url,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *StoryPinInteractionCustomMessagePayload) Reset() {
	*x = StoryPinInteractionCustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPinInteractionCustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPinInteractionCustomMessagePayload) ProtoMessage() {}

func (x *StoryPinInteractionCustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPinInteractionCustomMessagePayload.ProtoReflect.Descriptor instead.
func (*StoryPinInteractionCustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{20}
}

func (x *StoryPinInteractionCustomMessagePayload) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryPinInteractionCustomMessagePayload) GetBackgroundImageUrl() string {
	if x != nil {
		return x.BackgroundImageUrl
	}
	return ""
}

func (x *StoryPinInteractionCustomMessagePayload) GetSuccessConsumeCostSeconds() string {
	if x != nil {
		return x.SuccessConsumeCostSeconds
	}
	return ""
}

func (x *StoryPinInteractionCustomMessagePayload) GetFailedConsumeImageUrl() string {
	if x != nil {
		return x.FailedConsumeImageUrl
	}
	return ""
}

type CustomMessagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 在 agora sdk 内，收到消息后，最终会被转为 customExts 的 map[string]string
	//
	// Types that are valid to be assigned to Payload:
	//
	//	*CustomMessagePayload_StoryExchangeImageInteraction
	//	*CustomMessagePayload_StoryTurtleSoupInteraction
	//	*CustomMessagePayload_StoryFizzInteraction
	//	*CustomMessagePayload_StoryNowShotInteraction
	//	*CustomMessagePayload_StoryUnmuteInteraction
	//	*CustomMessagePayload_StoryWassupInteraction
	//	*CustomMessagePayload_RelayFizzJoinInvite
	//	*CustomMessagePayload_StoryCapsuleInteraction
	//	*CustomMessagePayload_RushFizzJoinInvite
	//	*CustomMessagePayload_BooInteraction
	//	*CustomMessagePayload_StoryRoastedInteraction
	//	*CustomMessagePayload_StoryChatproxyInteraction
	//	*CustomMessagePayload_StoryWhoInteraction
	//	*CustomMessagePayload_StoryHauntInteraction
	//	*CustomMessagePayload_StoryBaseplayInteraction
	//	*CustomMessagePayload_StoryHideStickerUnlocked
	//	*CustomMessagePayload_StoryShareCreateMoment
	//	*CustomMessagePayload_StoryMomentQuote
	//	*CustomMessagePayload_StoryPinInteraction
	Payload isCustomMessagePayload_Payload `protobuf_oneof:"payload"`
	// 参考 CustomMessageType
	// 在 agora sdk 内，收到消息后，最终会被转为 customEvents
	CustomMessageType string `protobuf:"bytes,100,opt,name=custom_message_type,json=customMessageType,proto3" json:"custom_message_type,omitempty"`
	// 参考 ConsumeStatus
	ConsumeStatus string `protobuf:"bytes,101,opt,name=consume_status,json=consumeStatus,proto3" json:"consume_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CustomMessagePayload) Reset() {
	*x = CustomMessagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CustomMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomMessagePayload) ProtoMessage() {}

func (x *CustomMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomMessagePayload.ProtoReflect.Descriptor instead.
func (*CustomMessagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{21}
}

func (x *CustomMessagePayload) GetPayload() isCustomMessagePayload_Payload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryExchangeImageInteraction() *StoryExchangeImageInteractionCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryExchangeImageInteraction); ok {
			return x.StoryExchangeImageInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryTurtleSoupInteraction() *StoryTurtleSoupCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryTurtleSoupInteraction); ok {
			return x.StoryTurtleSoupInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryFizzInteraction() *StoryFizzCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryFizzInteraction); ok {
			return x.StoryFizzInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryNowShotInteraction() *StoryNowShotCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryNowShotInteraction); ok {
			return x.StoryNowShotInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryUnmuteInteraction() *StoryUnmuteCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryUnmuteInteraction); ok {
			return x.StoryUnmuteInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryWassupInteraction() *StoryWassupCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryWassupInteraction); ok {
			return x.StoryWassupInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetRelayFizzJoinInvite() *RelayFizzJoinInviteCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_RelayFizzJoinInvite); ok {
			return x.RelayFizzJoinInvite
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryCapsuleInteraction() *StoryCapsuleCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryCapsuleInteraction); ok {
			return x.StoryCapsuleInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetRushFizzJoinInvite() *RushFizzJoinInviteCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_RushFizzJoinInvite); ok {
			return x.RushFizzJoinInvite
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetBooInteraction() *BooInteractionCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_BooInteraction); ok {
			return x.BooInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryRoastedInteraction() *StoryRoastedCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryRoastedInteraction); ok {
			return x.StoryRoastedInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryChatproxyInteraction() *StoryChatProxyCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryChatproxyInteraction); ok {
			return x.StoryChatproxyInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryWhoInteraction() *StoryWhoInteractionCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryWhoInteraction); ok {
			return x.StoryWhoInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryHauntInteraction() *StoryHauntCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryHauntInteraction); ok {
			return x.StoryHauntInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryBaseplayInteraction() *StoryBaseplayCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryBaseplayInteraction); ok {
			return x.StoryBaseplayInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryHideStickerUnlocked() *StoryHideStickerUnlockedCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryHideStickerUnlocked); ok {
			return x.StoryHideStickerUnlocked
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryShareCreateMoment() *StoryShareCreateMomentCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryShareCreateMoment); ok {
			return x.StoryShareCreateMoment
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryMomentQuote() *StoryMomentQuoteCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryMomentQuote); ok {
			return x.StoryMomentQuote
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetStoryPinInteraction() *StoryPinInteractionCustomMessagePayload {
	if x != nil {
		if x, ok := x.Payload.(*CustomMessagePayload_StoryPinInteraction); ok {
			return x.StoryPinInteraction
		}
	}
	return nil
}

func (x *CustomMessagePayload) GetCustomMessageType() string {
	if x != nil {
		return x.CustomMessageType
	}
	return ""
}

func (x *CustomMessagePayload) GetConsumeStatus() string {
	if x != nil {
		return x.ConsumeStatus
	}
	return ""
}

type isCustomMessagePayload_Payload interface {
	isCustomMessagePayload_Payload()
}

type CustomMessagePayload_StoryExchangeImageInteraction struct {
	// 换图 story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION 时有效
	StoryExchangeImageInteraction *StoryExchangeImageInteractionCustomMessagePayload `protobuf:"bytes,1,opt,name=story_exchange_image_interaction,json=storyExchangeImageInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryTurtleSoupInteraction struct {
	// 海龟汤 story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION 时有效
	StoryTurtleSoupInteraction *StoryTurtleSoupCustomMessagePayload `protobuf:"bytes,2,opt,name=story_turtle_soup_interaction,json=storyTurtleSoupInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryFizzInteraction struct {
	// fizz story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION 时有效
	StoryFizzInteraction *StoryFizzCustomMessagePayload `protobuf:"bytes,3,opt,name=story_fizz_interaction,json=storyFizzInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryNowShotInteraction struct {
	// NowShot story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION 时有效
	StoryNowShotInteraction *StoryNowShotCustomMessagePayload `protobuf:"bytes,4,opt,name=story_now_shot_interaction,json=storyNowShotInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryUnmuteInteraction struct {
	// Unmute story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION 时有效
	StoryUnmuteInteraction *StoryUnmuteCustomMessagePayload `protobuf:"bytes,5,opt,name=story_unmute_interaction,json=storyUnmuteInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryWassupInteraction struct {
	// Wassup story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION 时有效
	StoryWassupInteraction *StoryWassupCustomMessagePayload `protobuf:"bytes,6,opt,name=story_wassup_interaction,json=storyWassupInteraction,proto3,oneof"`
}

type CustomMessagePayload_RelayFizzJoinInvite struct {
	// Relay fizz 邀请信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE 时有效
	RelayFizzJoinInvite *RelayFizzJoinInviteCustomMessagePayload `protobuf:"bytes,7,opt,name=relay_fizz_join_invite,json=relayFizzJoinInvite,proto3,oneof"`
}

type CustomMessagePayload_StoryCapsuleInteraction struct {
	// Capsule story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION 时有效
	StoryCapsuleInteraction *StoryCapsuleCustomMessagePayload `protobuf:"bytes,8,opt,name=story_capsule_interaction,json=storyCapsuleInteraction,proto3,oneof"`
}

type CustomMessagePayload_RushFizzJoinInvite struct {
	// Rush 邀请信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_RUSH_INVITE 时有效
	RushFizzJoinInvite *RushFizzJoinInviteCustomMessagePayload `protobuf:"bytes,9,opt,name=rush_fizz_join_invite,json=rushFizzJoinInvite,proto3,oneof"`
}

type CustomMessagePayload_BooInteraction struct {
	// Boo 交互信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_BOO_INTERACTION 时有效
	BooInteraction *BooInteractionCustomMessagePayload `protobuf:"bytes,10,opt,name=boo_interaction,json=booInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryRoastedInteraction struct {
	// Roasted story 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION 时有效
	StoryRoastedInteraction *StoryRoastedCustomMessagePayload `protobuf:"bytes,11,opt,name=story_roasted_interaction,json=storyRoastedInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryChatproxyInteraction struct {
	// ChatProxy 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION 时有效
	StoryChatproxyInteraction *StoryChatProxyCustomMessagePayload `protobuf:"bytes,12,opt,name=story_chatproxy_interaction,json=storyChatproxyInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryWhoInteraction struct {
	// Who 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION 时有效
	StoryWhoInteraction *StoryWhoInteractionCustomMessagePayload `protobuf:"bytes,13,opt,name=story_who_interaction,json=storyWhoInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryHauntInteraction struct {
	// Haunt 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION 时有效
	StoryHauntInteraction *StoryHauntCustomMessagePayload `protobuf:"bytes,14,opt,name=story_haunt_interaction,json=storyHauntInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryBaseplayInteraction struct {
	// baseplay story 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION 时有效
	StoryBaseplayInteraction *StoryBaseplayCustomMessagePayload `protobuf:"bytes,15,opt,name=story_baseplay_interaction,json=storyBaseplayInteraction,proto3,oneof"`
}

type CustomMessagePayload_StoryHideStickerUnlocked struct {
	// Hide sticker 解锁消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED 时有效
	StoryHideStickerUnlocked *StoryHideStickerUnlockedCustomMessagePayload `protobuf:"bytes,16,opt,name=story_hide_sticker_unlocked,json=storyHideStickerUnlocked,proto3,oneof"`
}

type CustomMessagePayload_StoryShareCreateMoment struct {
	// share create moment，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT 时有效
	StoryShareCreateMoment *StoryShareCreateMomentCustomMessagePayload `protobuf:"bytes,17,opt,name=story_share_create_moment,json=storyShareCreateMoment,proto3,oneof"`
}

type CustomMessagePayload_StoryMomentQuote struct {
	// moment 引用类消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_MOMENT_INTERACTION 时有效
	StoryMomentQuote *StoryMomentQuoteCustomMessagePayload `protobuf:"bytes,18,opt,name=story_moment_quote,json=storyMomentQuote,proto3,oneof"`
}

type CustomMessagePayload_StoryPinInteraction struct {
	// pin story 交互信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION 时有效
	StoryPinInteraction *StoryPinInteractionCustomMessagePayload `protobuf:"bytes,19,opt,name=story_pin_interaction,json=storyPinInteraction,proto3,oneof"`
}

func (*CustomMessagePayload_StoryExchangeImageInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryTurtleSoupInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryFizzInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryNowShotInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryUnmuteInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryWassupInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_RelayFizzJoinInvite) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryCapsuleInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_RushFizzJoinInvite) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_BooInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryRoastedInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryChatproxyInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryWhoInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryHauntInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryBaseplayInteraction) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryHideStickerUnlocked) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryShareCreateMoment) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryMomentQuote) isCustomMessagePayload_Payload() {}

func (*CustomMessagePayload_StoryPinInteraction) isCustomMessagePayload_Payload() {}

type StoryTurtleSoupCustomMessagePayload_EndImagePayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 结局图片
	EndImageUrl string `protobuf:"bytes,1,opt,name=end_image_url,json=endImageUrl,proto3" json:"end_image_url,omitempty"`
	// 谜底
	EndMessage    string `protobuf:"bytes,2,opt,name=end_message,json=endMessage,proto3" json:"end_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryTurtleSoupCustomMessagePayload_EndImagePayload) Reset() {
	*x = StoryTurtleSoupCustomMessagePayload_EndImagePayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryTurtleSoupCustomMessagePayload_EndImagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryTurtleSoupCustomMessagePayload_EndImagePayload) ProtoMessage() {}

func (x *StoryTurtleSoupCustomMessagePayload_EndImagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryTurtleSoupCustomMessagePayload_EndImagePayload.ProtoReflect.Descriptor instead.
func (*StoryTurtleSoupCustomMessagePayload_EndImagePayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{10, 0}
}

func (x *StoryTurtleSoupCustomMessagePayload_EndImagePayload) GetEndImageUrl() string {
	if x != nil {
		return x.EndImageUrl
	}
	return ""
}

func (x *StoryTurtleSoupCustomMessagePayload_EndImagePayload) GetEndMessage() string {
	if x != nil {
		return x.EndMessage
	}
	return ""
}

type StoryFizzCustomMessagePayload_VideoPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视频链接
	VideoUrl string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// 视频封面链接
	CoverUrl      string `protobuf:"bytes,2,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryFizzCustomMessagePayload_VideoPayload) Reset() {
	*x = StoryFizzCustomMessagePayload_VideoPayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryFizzCustomMessagePayload_VideoPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryFizzCustomMessagePayload_VideoPayload) ProtoMessage() {}

func (x *StoryFizzCustomMessagePayload_VideoPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryFizzCustomMessagePayload_VideoPayload.ProtoReflect.Descriptor instead.
func (*StoryFizzCustomMessagePayload_VideoPayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{11, 0}
}

func (x *StoryFizzCustomMessagePayload_VideoPayload) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *StoryFizzCustomMessagePayload_VideoPayload) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

type BooInteractionCustomMessagePayload_VideoPayload struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视频链接
	VideoUrl string `protobuf:"bytes,1,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	// 视频封面链接
	CoverUrl      string `protobuf:"bytes,2,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BooInteractionCustomMessagePayload_VideoPayload) Reset() {
	*x = BooInteractionCustomMessagePayload_VideoPayload{}
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooInteractionCustomMessagePayload_VideoPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooInteractionCustomMessagePayload_VideoPayload) ProtoMessage() {}

func (x *BooInteractionCustomMessagePayload_VideoPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_im_message_types_v1_types_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooInteractionCustomMessagePayload_VideoPayload.ProtoReflect.Descriptor instead.
func (*BooInteractionCustomMessagePayload_VideoPayload) Descriptor() ([]byte, []int) {
	return file_api_im_message_types_v1_types_proto_rawDescGZIP(), []int{14, 0}
}

func (x *BooInteractionCustomMessagePayload_VideoPayload) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *BooInteractionCustomMessagePayload_VideoPayload) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

var File_api_im_message_types_v1_types_proto protoreflect.FileDescriptor

const file_api_im_message_types_v1_types_proto_rawDesc = "" +
	"\n" +
	"#api/im/message/types/v1/types.proto\x12\x17api.im.message.types.v1\x1a\x17validate/validate.proto\"\x98\x01\n" +
	"\vMessageBody\x12\x1a\n" +
	"\acontent\x18\x01 \x01(\tH\x00R\acontent\x12e\n" +
	"\x16custom_message_payload\x18\x02 \x01(\v2-.api.im.message.types.v1.CustomMessagePayloadH\x00R\x14customMessagePayloadB\x06\n" +
	"\x04data\"\xa4\x01\n" +
	"\aMessage\x12U\n" +
	"\fmessage_type\x18\x01 \x01(\x0e2$.api.im.message.types.v1.MessageTypeB\f\xfaB\t\x82\x01\x06\x10\x01\x18\x01\x18\aR\vmessageType\x12B\n" +
	"\x04body\x18\x02 \x01(\v2$.api.im.message.types.v1.MessageBodyB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x04body\"\x98\x02\n" +
	"1StoryExchangeImageInteractionCustomMessagePayload\x12,\n" +
	"\x12consumer_image_url\x18\x01 \x01(\tR\x10consumerImageUrl\x12C\n" +
	"\x1econsumer_interaction_image_url\x18\x02 \x01(\tR\x1bconsumerInteractionImageUrl\x12\x19\n" +
	"\bstory_id\x18\x03 \x01(\tR\astoryId\x12\x17\n" +
	"\anode_id\x18\x04 \x01(\tR\x06nodeId\x12\x14\n" +
	"\x05title\x18\x05 \x01(\tR\x05title\x12&\n" +
	"\x0fstory_cover_url\x18\x06 \x01(\tR\rstoryCoverUrl\"\xce\x01\n" +
	"\"StoryChatProxyCustomMessagePayload\x127\n" +
	"\x18consumer_video_cover_url\x18\x01 \x01(\tR\x15consumerVideoCoverUrl\x12,\n" +
	"\x12consumer_video_url\x18\x02 \x01(\tR\x10consumerVideoUrl\x12&\n" +
	"\x0fstory_cover_url\x18\x03 \x01(\tR\rstoryCoverUrl\x12\x19\n" +
	"\bstory_id\x18\x04 \x01(\tR\astoryId\"\xcc\x01\n" +
	" StoryRoastedCustomMessagePayload\x127\n" +
	"\x18consumer_video_cover_url\x18\x01 \x01(\tR\x15consumerVideoCoverUrl\x12,\n" +
	"\x12consumer_video_url\x18\x02 \x01(\tR\x10consumerVideoUrl\x12&\n" +
	"\x0fstory_cover_url\x18\x03 \x01(\tR\rstoryCoverUrl\x12\x19\n" +
	"\bstory_id\x18\x04 \x01(\tR\astoryId\"\x8e\x02\n" +
	"\x1fStoryUnmuteCustomMessagePayload\x12,\n" +
	"\x12consumer_audio_url\x18\x01 \x01(\tR\x10consumerAudioUrl\x12C\n" +
	"\x1econsumer_interaction_image_url\x18\x02 \x01(\tR\x1bconsumerInteractionImageUrl\x12\x19\n" +
	"\bstory_id\x18\x03 \x01(\tR\astoryId\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12&\n" +
	"\x0fstory_cover_url\x18\x05 \x01(\tR\rstoryCoverUrl\x12\x1f\n" +
	"\vai_response\x18\x06 \x01(\tR\n" +
	"aiResponse\"\xdc\x01\n" +
	" StoryCapsuleCustomMessagePayload\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12&\n" +
	"\x0fstory_cover_url\x18\x02 \x01(\tR\rstoryCoverUrl\x12,\n" +
	"\x12consumer_image_url\x18\x03 \x01(\tR\x10consumerImageUrl\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x17\n" +
	"\ain_days\x18\x05 \x01(\rR\x06inDays\x12\x18\n" +
	"\amoments\x18\x06 \x01(\rR\amoments\"\xfe\x01\n" +
	"'StoryWhoInteractionCustomMessagePayload\x12\x1f\n" +
	"\vavatar_urls\x18\x01 \x03(\tR\n" +
	"avatarUrls\x12.\n" +
	"\x13selected_avatar_url\x18\x02 \x01(\tR\x11selectedAvatarUrl\x12,\n" +
	"\x12selected_user_name\x18\x03 \x01(\tR\x10selectedUserName\x12\x18\n" +
	"\acorrect\x18\x04 \x01(\bR\acorrect\x12\x1f\n" +
	"\vtried_times\x18\x05 \x01(\rR\n" +
	"triedTimes\x12\x19\n" +
	"\bstory_id\x18\x06 \x01(\tR\astoryId\"\xcb\x01\n" +
	"\x1fStoryWassupCustomMessagePayload\x127\n" +
	"\x18consumer_video_cover_url\x18\x01 \x01(\tR\x15consumerVideoCoverUrl\x12,\n" +
	"\x12consumer_video_url\x18\x02 \x01(\tR\x10consumerVideoUrl\x12&\n" +
	"\x0fstory_cover_url\x18\x03 \x01(\tR\rstoryCoverUrl\x12\x19\n" +
	"\bstory_id\x18\x04 \x01(\tR\astoryId\"\xf8\x03\n" +
	" StoryNowShotCustomMessagePayload\x12!\n" +
	"\fpayload_type\x18\x01 \x01(\tR\vpayloadType\x12,\n" +
	"\x12consumer_image_url\x18\x02 \x01(\tR\x10consumerImageUrl\x12,\n" +
	"\x12consumer_video_url\x18\x03 \x01(\tR\x10consumerVideoUrl\x12C\n" +
	"\x1econsumer_interaction_image_url\x18\x04 \x01(\tR\x1bconsumerInteractionImageUrl\x12C\n" +
	"\x1econsumer_interaction_video_url\x18\x05 \x01(\tR\x1bconsumerInteractionVideoUrl\x12\x19\n" +
	"\bstory_id\x18\x06 \x01(\tR\astoryId\x12\x14\n" +
	"\x05title\x18\a \x01(\tR\x05title\x12&\n" +
	"\x0fstory_cover_url\x18\b \x01(\tR\rstoryCoverUrl\"r\n" +
	"\vPayloadType\x12\x1c\n" +
	"\x18PAYLOAD_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11PAYLOAD_TYPE_TEXT\x10\x01\x12\x16\n" +
	"\x12PAYLOAD_TYPE_IMAGE\x10\x02\x12\x16\n" +
	"\x12PAYLOAD_TYPE_VIDEO\x10\x03\"\x8a\x04\n" +
	"#StoryTurtleSoupCustomMessagePayload\x12!\n" +
	"\fpayload_type\x18\x01 \x01(\tR\vpayloadType\x12-\n" +
	"\x13story_info_json_str\x18\x02 \x01(\tR\x10storyInfoJsonStr\x12\x14\n" +
	"\x04text\x18\x03 \x01(\tH\x00R\x04text\x12z\n" +
	"\x11end_image_payload\x18\x04 \x01(\v2L.api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload.EndImagePayloadH\x00R\x0fendImagePayload\x12\x1f\n" +
	"\vai_response\x18\x05 \x01(\tR\n" +
	"aiResponse\x12\x1b\n" +
	"\tuser_text\x18\x06 \x01(\tR\buserText\x1aV\n" +
	"\x0fEndImagePayload\x12\"\n" +
	"\rend_image_url\x18\x01 \x01(\tR\vendImageUrl\x12\x1f\n" +
	"\vend_message\x18\x02 \x01(\tR\n" +
	"endMessage\"^\n" +
	"\vPayloadType\x12\x1c\n" +
	"\x18PAYLOAD_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11PAYLOAD_TYPE_TEXT\x10\x01\x12\x1a\n" +
	"\x16PAYLOAD_TYPE_END_IMAGE\x10\x02B\t\n" +
	"\apayload\"\xfb\x03\n" +
	"\x1dStoryFizzCustomMessagePayload\x12!\n" +
	"\fpayload_type\x18\x01 \x01(\tR\vpayloadType\x12\x17\n" +
	"\afizz_id\x18\x02 \x01(\tR\x06fizzId\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\x12\x1d\n" +
	"\timage_url\x18\x04 \x01(\tH\x00R\bimageUrl\x12j\n" +
	"\rvideo_payload\x18\x05 \x01(\v2C.api.im.message.types.v1.StoryFizzCustomMessagePayload.VideoPayloadH\x00R\fvideoPayload\x12\x1b\n" +
	"\texpire_at\x18\x06 \x01(\tR\bexpireAt\x12\x19\n" +
	"\bmedia_id\x18\a \x01(\tR\amediaId\x1aH\n" +
	"\fVideoPayload\x12\x1b\n" +
	"\tvideo_url\x18\x01 \x01(\tR\bvideoUrl\x12\x1b\n" +
	"\tcover_url\x18\x02 \x01(\tR\bcoverUrl\"r\n" +
	"\vPayloadType\x12\x1c\n" +
	"\x18PAYLOAD_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11PAYLOAD_TYPE_TEXT\x10\x01\x12\x16\n" +
	"\x12PAYLOAD_TYPE_IMAGE\x10\x02\x12\x16\n" +
	"\x12PAYLOAD_TYPE_VIDEO\x10\x03B\t\n" +
	"\apayload\"\x91\x01\n" +
	"'RelayFizzJoinInviteCustomMessagePayload\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1d\n" +
	"\n" +
	"creator_id\x18\x02 \x01(\tR\tcreatorId\x12.\n" +
	"\x13expire_at_unixstamp\x18\x03 \x01(\rR\x11expireAtUnixstamp\"\x90\x01\n" +
	"&RushFizzJoinInviteCustomMessagePayload\x12\x17\n" +
	"\aroom_id\x18\x01 \x01(\tR\x06roomId\x12\x1d\n" +
	"\n" +
	"creator_id\x18\x02 \x01(\tR\tcreatorId\x12.\n" +
	"\x13expire_at_unixstamp\x18\x03 \x01(\rR\x11expireAtUnixstamp\"\xb1\x03\n" +
	"\"BooInteractionCustomMessagePayload\x12!\n" +
	"\fmessage_type\x18\x01 \x01(\tR\vmessageType\x12m\n" +
	"\rvideo_payload\x18\x02 \x01(\v2H.api.im.message.types.v1.BooInteractionCustomMessagePayload.VideoPayloadR\fvideoPayload\x12$\n" +
	"\x0eboo_avatar_url\x18\x03 \x01(\tR\fbooAvatarUrl\x1aH\n" +
	"\fVideoPayload\x12\x1b\n" +
	"\tvideo_url\x18\x01 \x01(\tR\bvideoUrl\x12\x1b\n" +
	"\tcover_url\x18\x02 \x01(\tR\bcoverUrl\"\x88\x01\n" +
	"\vMessageType\x12\x1c\n" +
	"\x18MESSAGE_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16MESSAGE_TYPE_FOUND_BOO\x10\x01\x12\x1b\n" +
	"\x17MESSAGE_TYPE_CAUGHT_BOO\x10\x02\x12\"\n" +
	"\x1eMESSAGE_TYPE_CAUGHT_FAILED_BOO\x10\x03\"\xa6\x01\n" +
	"\x1eStoryHauntCustomMessagePayload\x12\x1b\n" +
	"\tvideo_url\x18\x01 \x01(\tR\bvideoUrl\x12&\n" +
	"\x0fvideo_cover_url\x18\x02 \x01(\tR\rvideoCoverUrl\x12$\n" +
	"\x0eboo_avatar_url\x18\x03 \x01(\tR\fbooAvatarUrl\x12\x19\n" +
	"\bstory_id\x18\x04 \x01(\tR\astoryId\"\xb5\x01\n" +
	"!StoryBaseplayCustomMessagePayload\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12&\n" +
	"\x0fstory_cover_url\x18\x02 \x01(\tR\rstoryCoverUrl\x127\n" +
	"\x18consumer_story_cover_url\x18\x03 \x01(\tR\x15consumerStoryCoverUrl\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\"\xa8\x01\n" +
	",StoryHideStickerUnlockedCustomMessagePayload\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12&\n" +
	"\x0fstory_cover_url\x18\x02 \x01(\tR\rstoryCoverUrl\x12\x1f\n" +
	"\vsticker_url\x18\x03 \x01(\tR\n" +
	"stickerUrl\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\"\xae\x03\n" +
	"*StoryShareCreateMomentCustomMessagePayload\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12&\n" +
	"\x0fstory_cover_url\x18\x02 \x01(\tR\rstoryCoverUrl\x12\x1b\n" +
	"\tmoment_id\x18\x03 \x01(\tR\bmomentId\x12\x92\x01\n" +
	"\x18share_create_moment_type\x18\x04 \x01(\x0e2Y.api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload.ShareCreateMomentTypeR\x15shareCreateMomentType\"\x8a\x01\n" +
	"\x15ShareCreateMomentType\x12(\n" +
	"$SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eSHARE_CREATE_MOMENT_TYPE_STORY\x10\x01\x12#\n" +
	"\x1fSHARE_CREATE_MOMENT_TYPE_MOMENT\x10\x02\"\xb4\x01\n" +
	"$StoryMomentQuoteCustomMessagePayload\x12\x1b\n" +
	"\tmoment_id\x18\x01 \x01(\tR\bmomentId\x12\x1b\n" +
	"\tportal_id\x18\x02 \x01(\tR\bportalId\x12>\n" +
	"\x1cfirst_moment_cover_image_url\x18\x03 \x01(\tR\x18firstMomentCoverImageUrl\x12\x12\n" +
	"\x04text\x18\x04 \x01(\tR\x04text\"\xf0\x01\n" +
	"'StoryPinInteractionCustomMessagePayload\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x120\n" +
	"\x14background_image_url\x18\x02 \x01(\tR\x12backgroundImageUrl\x12?\n" +
	"\x1csuccess_consume_cost_seconds\x18\x03 \x01(\tR\x19successConsumeCostSeconds\x127\n" +
	"\x18failed_consume_image_url\x18\x04 \x01(\tR\x15failedConsumeImageUrl\"\x8c\x13\n" +
	"\x14CustomMessagePayload\x12\x95\x01\n" +
	" story_exchange_image_interaction\x18\x01 \x01(\v2J.api.im.message.types.v1.StoryExchangeImageInteractionCustomMessagePayloadH\x00R\x1dstoryExchangeImageInteraction\x12\x81\x01\n" +
	"\x1dstory_turtle_soup_interaction\x18\x02 \x01(\v2<.api.im.message.types.v1.StoryTurtleSoupCustomMessagePayloadH\x00R\x1astoryTurtleSoupInteraction\x12n\n" +
	"\x16story_fizz_interaction\x18\x03 \x01(\v26.api.im.message.types.v1.StoryFizzCustomMessagePayloadH\x00R\x14storyFizzInteraction\x12x\n" +
	"\x1astory_now_shot_interaction\x18\x04 \x01(\v29.api.im.message.types.v1.StoryNowShotCustomMessagePayloadH\x00R\x17storyNowShotInteraction\x12t\n" +
	"\x18story_unmute_interaction\x18\x05 \x01(\v28.api.im.message.types.v1.StoryUnmuteCustomMessagePayloadH\x00R\x16storyUnmuteInteraction\x12t\n" +
	"\x18story_wassup_interaction\x18\x06 \x01(\v28.api.im.message.types.v1.StoryWassupCustomMessagePayloadH\x00R\x16storyWassupInteraction\x12w\n" +
	"\x16relay_fizz_join_invite\x18\a \x01(\<EMAIL>\x00R\x13relayFizzJoinInvite\x12w\n" +
	"\x19story_capsule_interaction\x18\b \x01(\v29.api.im.message.types.v1.StoryCapsuleCustomMessagePayloadH\x00R\x17storyCapsuleInteraction\x12t\n" +
	"\x15rush_fizz_join_invite\x18\t \x01(\v2?.api.im.message.types.v1.RushFizzJoinInviteCustomMessagePayloadH\x00R\x12rushFizzJoinInvite\x12f\n" +
	"\x0fboo_interaction\x18\n" +
	" \x01(\v2;.api.im.message.types.v1.BooInteractionCustomMessagePayloadH\x00R\x0ebooInteraction\x12w\n" +
	"\x19story_roasted_interaction\x18\v \x01(\v29.api.im.message.types.v1.StoryRoastedCustomMessagePayloadH\x00R\x17storyRoastedInteraction\x12}\n" +
	"\x1bstory_chatproxy_interaction\x18\f \x01(\v2;.api.im.message.types.v1.StoryChatProxyCustomMessagePayloadH\x00R\x19storyChatproxyInteraction\x12v\n" +
	"\x15story_who_interaction\x18\r \x01(\<EMAIL>\x00R\x13storyWhoInteraction\x12q\n" +
	"\x17story_haunt_interaction\x18\x0e \x01(\v27.api.im.message.types.v1.StoryHauntCustomMessagePayloadH\x00R\x15storyHauntInteraction\x12z\n" +
	"\x1astory_baseplay_interaction\x18\x0f \x01(\v2:.api.im.message.types.v1.StoryBaseplayCustomMessagePayloadH\x00R\x18storyBaseplayInteraction\x12\x86\x01\n" +
	"\x1bstory_hide_sticker_unlocked\x18\x10 \x01(\v2E.api.im.message.types.v1.StoryHideStickerUnlockedCustomMessagePayloadH\x00R\x18storyHideStickerUnlocked\x12\x80\x01\n" +
	"\x19story_share_create_moment\x18\x11 \x01(\v2C.api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayloadH\x00R\x16storyShareCreateMoment\x12m\n" +
	"\x12story_moment_quote\x18\x12 \x01(\v2=.api.im.message.types.v1.StoryMomentQuoteCustomMessagePayloadH\x00R\x10storyMomentQuote\x12v\n" +
	"\x15story_pin_interaction\x18\x13 \x01(\<EMAIL>\x00R\x13storyPinInteraction\x12.\n" +
	"\x13custom_message_type\x18d \x01(\tR\x11customMessageType\x12%\n" +
	"\x0econsume_status\x18e \x01(\tR\rconsumeStatusB\t\n" +
	"\apayload*\xd5\x01\n" +
	"\vMessageType\x12\x1c\n" +
	"\x18MESSAGE_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11MESSAGE_TYPE_TEXT\x10\x01\x12\x16\n" +
	"\x12MESSAGE_TYPE_IMAGE\x10\x02\x12\x16\n" +
	"\x12MESSAGE_TYPE_AUDIO\x10\x03\x12\x16\n" +
	"\x12MESSAGE_TYPE_VIDEO\x10\x04\x12\x15\n" +
	"\x11MESSAGE_TYPE_FILE\x10\x05\x12\x19\n" +
	"\x15MESSAGE_TYPE_LOCATION\x10\x06\x12\x17\n" +
	"\x13MESSAGE_TYPE_CUSTOM\x10\a*\xe7\a\n" +
	"\x11CustomMessageType\x12#\n" +
	"\x1fCUSTOM_MESSAGE_TYPE_UNSPECIFIED\x10\x00\x128\n" +
	"4CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION\x10\x01\x125\n" +
	"1CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION\x10\x02\x12.\n" +
	"*CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION\x10\x03\x122\n" +
	".CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION\x10\x04\x120\n" +
	",CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION\x10\x05\x120\n" +
	",CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION\x10\x06\x121\n" +
	"-CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION\x10\b\x12(\n" +
	"$CUSTOM_MESSAGE_TYPE_RUSH_FIZZ_INVITE\x10\t\x12/\n" +
	"+CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE\x10\a\x12'\n" +
	"#CUSTOM_MESSAGE_TYPE_BOO_INTERACTION\x10\n" +
	"\x121\n" +
	"-CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION\x10\v\x123\n" +
	"/CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION\x10\f\x12-\n" +
	")CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION\x10\r\x12/\n" +
	"+CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION\x10\x0e\x122\n" +
	".CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION\x10\x0f\x123\n" +
	"/CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED\x10\x10\x121\n" +
	"-CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT\x10\x11\x12*\n" +
	"&CUSTOM_MESSAGE_TYPE_STORY_MOMENT_QUOTE\x10\x12\x12-\n" +
	")CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION\x10\x13*\x90\x01\n" +
	"\rConsumeStatus\x12\x1e\n" +
	"\x1aCONSUME_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19CONSUME_STATUS_SUCCESSFUL\x10\x01\x12\x19\n" +
	"\x15CONSUME_STATUS_FAILED\x10\x02\x12%\n" +
	"!CONSUME_STATUS_PARTIAL_SUCCESSFUL\x10\x03B7Z5boson/api/im/message/types/v1;api_im_message_types_v1b\x06proto3"

var (
	file_api_im_message_types_v1_types_proto_rawDescOnce sync.Once
	file_api_im_message_types_v1_types_proto_rawDescData []byte
)

func file_api_im_message_types_v1_types_proto_rawDescGZIP() []byte {
	file_api_im_message_types_v1_types_proto_rawDescOnce.Do(func() {
		file_api_im_message_types_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_im_message_types_v1_types_proto_rawDesc), len(file_api_im_message_types_v1_types_proto_rawDesc)))
	})
	return file_api_im_message_types_v1_types_proto_rawDescData
}

var file_api_im_message_types_v1_types_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_api_im_message_types_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_api_im_message_types_v1_types_proto_goTypes = []any{
	(MessageType)(0),       // 0: api.im.message.types.v1.MessageType
	(CustomMessageType)(0), // 1: api.im.message.types.v1.CustomMessageType
	(ConsumeStatus)(0),     // 2: api.im.message.types.v1.ConsumeStatus
	(StoryNowShotCustomMessagePayload_PayloadType)(0),                     // 3: api.im.message.types.v1.StoryNowShotCustomMessagePayload.PayloadType
	(StoryTurtleSoupCustomMessagePayload_PayloadType)(0),                  // 4: api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload.PayloadType
	(StoryFizzCustomMessagePayload_PayloadType)(0),                        // 5: api.im.message.types.v1.StoryFizzCustomMessagePayload.PayloadType
	(BooInteractionCustomMessagePayload_MessageType)(0),                   // 6: api.im.message.types.v1.BooInteractionCustomMessagePayload.MessageType
	(StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType)(0), // 7: api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload.ShareCreateMomentType
	(*MessageBody)(nil), // 8: api.im.message.types.v1.MessageBody
	(*Message)(nil),     // 9: api.im.message.types.v1.Message
	(*StoryExchangeImageInteractionCustomMessagePayload)(nil),   // 10: api.im.message.types.v1.StoryExchangeImageInteractionCustomMessagePayload
	(*StoryChatProxyCustomMessagePayload)(nil),                  // 11: api.im.message.types.v1.StoryChatProxyCustomMessagePayload
	(*StoryRoastedCustomMessagePayload)(nil),                    // 12: api.im.message.types.v1.StoryRoastedCustomMessagePayload
	(*StoryUnmuteCustomMessagePayload)(nil),                     // 13: api.im.message.types.v1.StoryUnmuteCustomMessagePayload
	(*StoryCapsuleCustomMessagePayload)(nil),                    // 14: api.im.message.types.v1.StoryCapsuleCustomMessagePayload
	(*StoryWhoInteractionCustomMessagePayload)(nil),             // 15: api.im.message.types.v1.StoryWhoInteractionCustomMessagePayload
	(*StoryWassupCustomMessagePayload)(nil),                     // 16: api.im.message.types.v1.StoryWassupCustomMessagePayload
	(*StoryNowShotCustomMessagePayload)(nil),                    // 17: api.im.message.types.v1.StoryNowShotCustomMessagePayload
	(*StoryTurtleSoupCustomMessagePayload)(nil),                 // 18: api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload
	(*StoryFizzCustomMessagePayload)(nil),                       // 19: api.im.message.types.v1.StoryFizzCustomMessagePayload
	(*RelayFizzJoinInviteCustomMessagePayload)(nil),             // 20: api.im.message.types.v1.RelayFizzJoinInviteCustomMessagePayload
	(*RushFizzJoinInviteCustomMessagePayload)(nil),              // 21: api.im.message.types.v1.RushFizzJoinInviteCustomMessagePayload
	(*BooInteractionCustomMessagePayload)(nil),                  // 22: api.im.message.types.v1.BooInteractionCustomMessagePayload
	(*StoryHauntCustomMessagePayload)(nil),                      // 23: api.im.message.types.v1.StoryHauntCustomMessagePayload
	(*StoryBaseplayCustomMessagePayload)(nil),                   // 24: api.im.message.types.v1.StoryBaseplayCustomMessagePayload
	(*StoryHideStickerUnlockedCustomMessagePayload)(nil),        // 25: api.im.message.types.v1.StoryHideStickerUnlockedCustomMessagePayload
	(*StoryShareCreateMomentCustomMessagePayload)(nil),          // 26: api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload
	(*StoryMomentQuoteCustomMessagePayload)(nil),                // 27: api.im.message.types.v1.StoryMomentQuoteCustomMessagePayload
	(*StoryPinInteractionCustomMessagePayload)(nil),             // 28: api.im.message.types.v1.StoryPinInteractionCustomMessagePayload
	(*CustomMessagePayload)(nil),                                // 29: api.im.message.types.v1.CustomMessagePayload
	(*StoryTurtleSoupCustomMessagePayload_EndImagePayload)(nil), // 30: api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload.EndImagePayload
	(*StoryFizzCustomMessagePayload_VideoPayload)(nil),          // 31: api.im.message.types.v1.StoryFizzCustomMessagePayload.VideoPayload
	(*BooInteractionCustomMessagePayload_VideoPayload)(nil),     // 32: api.im.message.types.v1.BooInteractionCustomMessagePayload.VideoPayload
}
var file_api_im_message_types_v1_types_proto_depIdxs = []int32{
	29, // 0: api.im.message.types.v1.MessageBody.custom_message_payload:type_name -> api.im.message.types.v1.CustomMessagePayload
	0,  // 1: api.im.message.types.v1.Message.message_type:type_name -> api.im.message.types.v1.MessageType
	8,  // 2: api.im.message.types.v1.Message.body:type_name -> api.im.message.types.v1.MessageBody
	30, // 3: api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload.end_image_payload:type_name -> api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload.EndImagePayload
	31, // 4: api.im.message.types.v1.StoryFizzCustomMessagePayload.video_payload:type_name -> api.im.message.types.v1.StoryFizzCustomMessagePayload.VideoPayload
	32, // 5: api.im.message.types.v1.BooInteractionCustomMessagePayload.video_payload:type_name -> api.im.message.types.v1.BooInteractionCustomMessagePayload.VideoPayload
	7,  // 6: api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload.share_create_moment_type:type_name -> api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload.ShareCreateMomentType
	10, // 7: api.im.message.types.v1.CustomMessagePayload.story_exchange_image_interaction:type_name -> api.im.message.types.v1.StoryExchangeImageInteractionCustomMessagePayload
	18, // 8: api.im.message.types.v1.CustomMessagePayload.story_turtle_soup_interaction:type_name -> api.im.message.types.v1.StoryTurtleSoupCustomMessagePayload
	19, // 9: api.im.message.types.v1.CustomMessagePayload.story_fizz_interaction:type_name -> api.im.message.types.v1.StoryFizzCustomMessagePayload
	17, // 10: api.im.message.types.v1.CustomMessagePayload.story_now_shot_interaction:type_name -> api.im.message.types.v1.StoryNowShotCustomMessagePayload
	13, // 11: api.im.message.types.v1.CustomMessagePayload.story_unmute_interaction:type_name -> api.im.message.types.v1.StoryUnmuteCustomMessagePayload
	16, // 12: api.im.message.types.v1.CustomMessagePayload.story_wassup_interaction:type_name -> api.im.message.types.v1.StoryWassupCustomMessagePayload
	20, // 13: api.im.message.types.v1.CustomMessagePayload.relay_fizz_join_invite:type_name -> api.im.message.types.v1.RelayFizzJoinInviteCustomMessagePayload
	14, // 14: api.im.message.types.v1.CustomMessagePayload.story_capsule_interaction:type_name -> api.im.message.types.v1.StoryCapsuleCustomMessagePayload
	21, // 15: api.im.message.types.v1.CustomMessagePayload.rush_fizz_join_invite:type_name -> api.im.message.types.v1.RushFizzJoinInviteCustomMessagePayload
	22, // 16: api.im.message.types.v1.CustomMessagePayload.boo_interaction:type_name -> api.im.message.types.v1.BooInteractionCustomMessagePayload
	12, // 17: api.im.message.types.v1.CustomMessagePayload.story_roasted_interaction:type_name -> api.im.message.types.v1.StoryRoastedCustomMessagePayload
	11, // 18: api.im.message.types.v1.CustomMessagePayload.story_chatproxy_interaction:type_name -> api.im.message.types.v1.StoryChatProxyCustomMessagePayload
	15, // 19: api.im.message.types.v1.CustomMessagePayload.story_who_interaction:type_name -> api.im.message.types.v1.StoryWhoInteractionCustomMessagePayload
	23, // 20: api.im.message.types.v1.CustomMessagePayload.story_haunt_interaction:type_name -> api.im.message.types.v1.StoryHauntCustomMessagePayload
	24, // 21: api.im.message.types.v1.CustomMessagePayload.story_baseplay_interaction:type_name -> api.im.message.types.v1.StoryBaseplayCustomMessagePayload
	25, // 22: api.im.message.types.v1.CustomMessagePayload.story_hide_sticker_unlocked:type_name -> api.im.message.types.v1.StoryHideStickerUnlockedCustomMessagePayload
	26, // 23: api.im.message.types.v1.CustomMessagePayload.story_share_create_moment:type_name -> api.im.message.types.v1.StoryShareCreateMomentCustomMessagePayload
	27, // 24: api.im.message.types.v1.CustomMessagePayload.story_moment_quote:type_name -> api.im.message.types.v1.StoryMomentQuoteCustomMessagePayload
	28, // 25: api.im.message.types.v1.CustomMessagePayload.story_pin_interaction:type_name -> api.im.message.types.v1.StoryPinInteractionCustomMessagePayload
	26, // [26:26] is the sub-list for method output_type
	26, // [26:26] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_api_im_message_types_v1_types_proto_init() }
func file_api_im_message_types_v1_types_proto_init() {
	if File_api_im_message_types_v1_types_proto != nil {
		return
	}
	file_api_im_message_types_v1_types_proto_msgTypes[0].OneofWrappers = []any{
		(*MessageBody_Content)(nil),
		(*MessageBody_CustomMessagePayload)(nil),
	}
	file_api_im_message_types_v1_types_proto_msgTypes[10].OneofWrappers = []any{
		(*StoryTurtleSoupCustomMessagePayload_Text)(nil),
		(*StoryTurtleSoupCustomMessagePayload_EndImagePayload_)(nil),
	}
	file_api_im_message_types_v1_types_proto_msgTypes[11].OneofWrappers = []any{
		(*StoryFizzCustomMessagePayload_ImageUrl)(nil),
		(*StoryFizzCustomMessagePayload_VideoPayload_)(nil),
	}
	file_api_im_message_types_v1_types_proto_msgTypes[21].OneofWrappers = []any{
		(*CustomMessagePayload_StoryExchangeImageInteraction)(nil),
		(*CustomMessagePayload_StoryTurtleSoupInteraction)(nil),
		(*CustomMessagePayload_StoryFizzInteraction)(nil),
		(*CustomMessagePayload_StoryNowShotInteraction)(nil),
		(*CustomMessagePayload_StoryUnmuteInteraction)(nil),
		(*CustomMessagePayload_StoryWassupInteraction)(nil),
		(*CustomMessagePayload_RelayFizzJoinInvite)(nil),
		(*CustomMessagePayload_StoryCapsuleInteraction)(nil),
		(*CustomMessagePayload_RushFizzJoinInvite)(nil),
		(*CustomMessagePayload_BooInteraction)(nil),
		(*CustomMessagePayload_StoryRoastedInteraction)(nil),
		(*CustomMessagePayload_StoryChatproxyInteraction)(nil),
		(*CustomMessagePayload_StoryWhoInteraction)(nil),
		(*CustomMessagePayload_StoryHauntInteraction)(nil),
		(*CustomMessagePayload_StoryBaseplayInteraction)(nil),
		(*CustomMessagePayload_StoryHideStickerUnlocked)(nil),
		(*CustomMessagePayload_StoryShareCreateMoment)(nil),
		(*CustomMessagePayload_StoryMomentQuote)(nil),
		(*CustomMessagePayload_StoryPinInteraction)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_im_message_types_v1_types_proto_rawDesc), len(file_api_im_message_types_v1_types_proto_rawDesc)),
			NumEnums:      8,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_im_message_types_v1_types_proto_goTypes,
		DependencyIndexes: file_api_im_message_types_v1_types_proto_depIdxs,
		EnumInfos:         file_api_im_message_types_v1_types_proto_enumTypes,
		MessageInfos:      file_api_im_message_types_v1_types_proto_msgTypes,
	}.Build()
	File_api_im_message_types_v1_types_proto = out.File
	file_api_im_message_types_v1_types_proto_goTypes = nil
	file_api_im_message_types_v1_types_proto_depIdxs = nil
}
