syntax = "proto3";

package api.im.message.types.v1;

import "validate/validate.proto";
option go_package = "boson/api/im/message/types/v1;api_im_message_types_v1";


enum MessageType {
	MESSAGE_TYPE_UNSPECIFIED = 0;
	MESSAGE_TYPE_TEXT = 1;
	MESSAGE_TYPE_IMAGE = 2;
	MESSAGE_TYPE_AUDIO = 3;
	MESSAGE_TYPE_VIDEO = 4;
	MESSAGE_TYPE_FILE = 5;
	MESSAGE_TYPE_LOCATION = 6;
	MESSAGE_TYPE_CUSTOM = 7;
}

message MessageBody {
	oneof data {
		// 当且仅当 message_type 为 TEXT 时有效
		string content = 1;
		// 当且仅当 message_type 为 CUSTOM 时有效
		CustomMessagePayload custom_message_payload = 2;
	}
}
message Message {
	MessageType message_type = 1[(validate.rules).enum = {
		defined_only: true,
		// 目前只支持文本消息 + 自定义消息
		in: [1, 7]
	}];
	MessageBody body = 2[(validate.rules).message.required = true];
}


enum CustomMessageType {
	CUSTOM_MESSAGE_TYPE_UNSPECIFIED = 0;
	// 换图 story 交互消息
	CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION = 1;
	// 海龟汤 story 交互消息
	CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION = 2;
	// Fizz story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION = 3;
	// NowShot story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION = 4;
	// Unmute story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION = 5;
	// Wassup story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION = 6;
	// Capsule story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION = 8;
	// Rush fizz 邀请信息
	CUSTOM_MESSAGE_TYPE_RUSH_FIZZ_INVITE = 9;
	// Relay fizz 邀请信息 TODO 配合客户端去除 STORY 部分 @Larry
	CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE = 7;
	// Boo 发现/捕捉 ghost 消息
	CUSTOM_MESSAGE_TYPE_BOO_INTERACTION = 10;
	// Roasted story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION = 11;
	// ChatProxy 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION = 12;
	// Who 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION = 13;
	// Haunt story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION = 14;
	// BasePlay story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION = 15;
	// Hide sticker 解锁消息
	CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED = 16;
	// share create moment
	CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT = 17;
	// moment 引用类消息
	CUSTOM_MESSAGE_TYPE_STORY_MOMENT_QUOTE = 18;
	// pin story 交互信息
	CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION = 19;
}

enum ConsumeStatus {
	CONSUME_STATUS_UNSPECIFIED = 0;
	// 消费成功
	CONSUME_STATUS_SUCCESSFUL = 1;
	// 消费失败
	CONSUME_STATUS_FAILED = 2;
	// 消费部分成功
	CONSUME_STATUS_PARTIAL_SUCCESSFUL = 3;
}

message StoryExchangeImageInteractionCustomMessagePayload {
	// 消费者发送的图片
	string consumer_image_url = 1;
	// 消费者交互的图片
	string consumer_interaction_image_url = 2;
	// 消费者交互的 story id
	string story_id = 3;
	// 消费者交互的节点 id
	string node_id = 4;
	// 当前消息的 title
	// 比如 the other party replied to your story
	string title = 5;
	// 交互的 Story 的封面
	string story_cover_url = 6;
	// 消费结果
}


message StoryChatProxyCustomMessagePayload {
	// 消费者录制的视频封面
	string consumer_video_cover_url = 1;
	// 消费者录制的视频
	string consumer_video_url = 2;
	// 交互的 Story 的封面
	string story_cover_url = 3;
	// 交互的 Story 的 id
	string story_id = 4;
}	

message StoryRoastedCustomMessagePayload {
	// 消费者录制的视频封面
	string consumer_video_cover_url = 1;
	// 消费者录制的视频
	string consumer_video_url = 2;
	// 交互的 Story 的封面
	string story_cover_url = 3;
	// 交互的 Story 的 id
	string story_id = 4;
}

message StoryUnmuteCustomMessagePayload {
	// 消费者发送的音频
	string consumer_audio_url = 1;
	// 消费者交互的图片
	string consumer_interaction_image_url = 2;
	// 消费者交互的 story id
	string story_id = 3;
	// 当前消息的 title
	// 比如 the other party replied to your story
	string title = 4;
	// 交互的 Story 的封面
	string story_cover_url = 5;
	// ai 回复
	string ai_response = 6;
}

message StoryCapsuleCustomMessagePayload {
	// 消费者交互的 story id
	string story_id = 1;
	// 交互的 Story 的封面
	string story_cover_url = 2;
	// 消费者发送的图片
	string consumer_image_url = 3;
	// 当前消息的 title
	// 比如 the other party replied to your story
	string title = 4;
	// 参考 api.items.story.types.v1.CapsulePhotoInfo in days
	// 几天内的图片
	uint32 in_days = 5;
	// 参考 api.items.story.types.v1.CapsulePhotoInfo moments
	// 几个瞬间
	uint32 moments = 6;
}

message StoryWhoInteractionCustomMessagePayload {
	// 所有待选项
	repeated string avatar_urls = 1;
	// 选中的头像
	string selected_avatar_url = 2;
	// 选中的用户名
	string selected_user_name = 3;
	// 是否正确
	bool correct = 4;
	// 尝试了几次
	uint32 tried_times = 5;
	// 交互的 story id
	string story_id = 6;
}

message StoryWassupCustomMessagePayload {
	// 消费者录制的视频封面
	string consumer_video_cover_url = 1;
	// 消费者录制的视频
	string consumer_video_url = 2;
	// 交互的 Story 的封面
	string story_cover_url = 3;
	// 交互的 Story 的 id
	string story_id = 4;
}

message StoryNowShotCustomMessagePayload {
	enum PayloadType {
		PAYLOAD_TYPE_UNSPECIFIED = 0;
		PAYLOAD_TYPE_TEXT = 1;
		PAYLOAD_TYPE_IMAGE = 2;
		PAYLOAD_TYPE_VIDEO = 3;
	}
	// 负载类型，参考 PayloadType
	string payload_type = 1;
	// 消费者发送的图片，如果 payload 是 video 则为 thumbnail
	string consumer_image_url = 2;
	// 消费者发送的视频
	string consumer_video_url = 3;
	// 消费者交互的图片,如果 payload 是 video 则为 thumbnail
	string consumer_interaction_image_url = 4;
	// 消费者交互的视频
	string consumer_interaction_video_url = 5;
	// 消费者交互的 story id
	string story_id = 6;
	// 当前消息的 title
	// 比如 the other party replied to your story
	string title = 7;
	// 交互的 Story 的封面
	string story_cover_url = 8;
}

message StoryTurtleSoupCustomMessagePayload {
	enum PayloadType {
		PAYLOAD_TYPE_UNSPECIFIED = 0;
		PAYLOAD_TYPE_TEXT = 1;
		PAYLOAD_TYPE_END_IMAGE = 2;
	}
	// 负载类型，参考 PayloadType
	string payload_type = 1;
	// 交互的 Story 的 json 字符串，结构为:
	// {
	//   "story_id": "string", // 交互的 Story 的 id
	//   "story_cover_url": "string", // 交互的 Story 的封面
	//   "caption": "string",         // 显示在屏幕中间的 caption
	//   "tips": "string",           // 显示在屏幕中间的 tips
	//   "ai_response": "string",    // ai 的回复
	//   "end_message": "string",    // 隐藏意图，用于帮助 hit_words 命中
	//   "end_message_font": "string" // 隐藏意图字体
	// }
	string story_info_json_str = 2;

	message EndImagePayload {
		// 结局图片
		string end_image_url = 1;
		// 谜底
		string end_message = 2;
	}

	oneof payload {
		// 仅当 payload_type 为 PAYLOAD_TYPE_TEXT 时有效
		// 表示消费者或者创作者发送的文字消息
		string text = 3;
		// 仅当 payload_type 为 PAYLOAD_TYPE_END_IMAGE 时有效
		EndImagePayload end_image_payload = 4;
	}
	string ai_response = 5;
	string user_text = 6;
}

message StoryFizzCustomMessagePayload {
	enum PayloadType {
		PAYLOAD_TYPE_UNSPECIFIED = 0;
		PAYLOAD_TYPE_TEXT = 1;
		PAYLOAD_TYPE_IMAGE = 2;
		PAYLOAD_TYPE_VIDEO = 3;
	}
	// 负载类型，参考 PayloadType
	string payload_type = 1;

	string fizz_id = 2;
	// 表示消费者发送的文字消息，当 payload_type 为 PAYLOAD_TYPE_IMAGE 和 PAYLOAD_TYPE_VIDEO 时，系统自动生成文字
	string text = 3;
	message VideoPayload {
		// 视频链接
		string video_url = 1;
		// 视频封面链接
		string cover_url = 2;
	}
	oneof payload {
		// 仅当 payload_type 为 PAYLOAD_TYPE_IMAGE 时有效
		string image_url = 4;
		// 仅当 payload_type 为 PAYLOAD_TYPE_VIDEO 时有效
		VideoPayload video_payload = 5;
	}
	// 消息过期时间
	string expire_at = 6;
	string media_id = 7;
}

message RelayFizzJoinInviteCustomMessagePayload {
	string room_id = 1;
	string creator_id = 2;
	// 暂时没用，先留着
	uint32 expire_at_unixstamp = 3;
}

message RushFizzJoinInviteCustomMessagePayload {
	string room_id = 1;
	string creator_id = 2;
	// 暂时没用，先留着
	uint32 expire_at_unixstamp = 3;
}

message BooInteractionCustomMessagePayload {
	enum MessageType {
		MESSAGE_TYPE_UNSPECIFIED = 0;
		MESSAGE_TYPE_FOUND_BOO = 1;
		MESSAGE_TYPE_CAUGHT_BOO = 2;
		MESSAGE_TYPE_CAUGHT_FAILED_BOO = 3;
	}
	// 负载类型，参考 MessageType
	string message_type = 1;
	message VideoPayload {
		// 视频链接
		string video_url = 1;
		// 视频封面链接
		string cover_url = 2;
	}
	VideoPayload video_payload = 2;
	// 附带的鬼的头像
	string boo_avatar_url = 3;
}

message StoryHauntCustomMessagePayload {
	string video_url = 1;
	string video_cover_url = 2;
	string boo_avatar_url = 3;
	string story_id = 4;
}

message StoryBaseplayCustomMessagePayload {
	string story_id = 1;
	string story_cover_url = 2;
	string consumer_story_cover_url = 3;
	string title = 4;
}

message StoryHideStickerUnlockedCustomMessagePayload {
	string story_id = 1;
	string story_cover_url = 2;
	string sticker_url = 3;
	string title = 4;
}

message StoryShareCreateMomentCustomMessagePayload {
	string story_id = 1;
	string story_cover_url = 2;
	string moment_id = 3;
	enum ShareCreateMomentType {
		SHARE_CREATE_MOMENT_TYPE_UNSPECIFIED = 0;
		SHARE_CREATE_MOMENT_TYPE_STORY = 1;
		SHARE_CREATE_MOMENT_TYPE_MOMENT = 2;
	}
	ShareCreateMomentType share_create_moment_type = 4;
}

message StoryMomentQuoteCustomMessagePayload {
	// 引用的 moment id
	string moment_id = 1;
	// moment 所在的 Portal id
	string portal_id = 2;
	// moment 的封面图片
	string first_moment_cover_image_url = 3;
	// 发送的字符串内容
	string text = 4;
}

message StoryPinInteractionCustomMessagePayload {
	string story_id = 1;
	string background_image_url = 2;
	string success_consume_cost_seconds = 3;
	string failed_consume_image_url = 4;
}

message CustomMessagePayload {
	// 在 agora sdk 内，收到消息后，最终会被转为 customExts 的 map[string]string
	oneof payload {
		// 换图 story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION 时有效	
		StoryExchangeImageInteractionCustomMessagePayload story_exchange_image_interaction = 1;
		// 海龟汤 story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION 时有效
		StoryTurtleSoupCustomMessagePayload story_turtle_soup_interaction = 2;
		// fizz story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION 时有效
		StoryFizzCustomMessagePayload story_fizz_interaction = 3;
		// NowShot story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION 时有效
		StoryNowShotCustomMessagePayload story_now_shot_interaction = 4;
		// Unmute story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION 时有效
		StoryUnmuteCustomMessagePayload story_unmute_interaction = 5;
		// Wassup story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION 时有效
		StoryWassupCustomMessagePayload story_wassup_interaction = 6;
		// Relay fizz 邀请信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE 时有效
		RelayFizzJoinInviteCustomMessagePayload relay_fizz_join_invite = 7;
		// Capsule story 交互消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION 时有效
		StoryCapsuleCustomMessagePayload story_capsule_interaction = 8;
		// Rush 邀请信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_RUSH_INVITE 时有效
		RushFizzJoinInviteCustomMessagePayload rush_fizz_join_invite = 9;
		// Boo 交互信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_BOO_INTERACTION 时有效
		BooInteractionCustomMessagePayload boo_interaction = 10;
		// Roasted story 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION 时有效
		StoryRoastedCustomMessagePayload story_roasted_interaction = 11;
		// ChatProxy 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION 时有效
		StoryChatProxyCustomMessagePayload story_chatproxy_interaction = 12;
		// Who 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION 时有效
		StoryWhoInteractionCustomMessagePayload story_who_interaction = 13;
		// Haunt 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION 时有效
		StoryHauntCustomMessagePayload story_haunt_interaction = 14;
		// baseplay story 交互信息,仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION 时有效
		StoryBaseplayCustomMessagePayload story_baseplay_interaction = 15;
		// Hide sticker 解锁消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED 时有效
		StoryHideStickerUnlockedCustomMessagePayload story_hide_sticker_unlocked = 16;
		// share create moment，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT 时有效
		StoryShareCreateMomentCustomMessagePayload story_share_create_moment = 17;
		// moment 引用类消息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_MOMENT_INTERACTION 时有效
		StoryMomentQuoteCustomMessagePayload story_moment_quote = 18;
		// pin story 交互信息，仅当 custom_message_type 为 CUSTOM_MESSAGE_TYPE_STORY_PIN_INTERACTION 时有效
		StoryPinInteractionCustomMessagePayload story_pin_interaction = 19;
	}

	// 参考 CustomMessageType
	// 在 agora sdk 内，收到消息后，最终会被转为 customEvents 
	string custom_message_type = 100;
	// 参考 ConsumeStatus
	string consume_status = 101;
}

