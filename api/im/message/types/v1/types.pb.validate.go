// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/im/message/types/v1/types.proto

package api_im_message_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MessageBody with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MessageBody) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MessageBody with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MessageBodyMultiError, or
// nil if none found.
func (m *MessageBody) ValidateAll() error {
	return m.validate(true)
}

func (m *MessageBody) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Data.(type) {
	case *MessageBody_Content:
		if v == nil {
			err := MessageBodyValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Content
	case *MessageBody_CustomMessagePayload:
		if v == nil {
			err := MessageBodyValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCustomMessagePayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MessageBodyValidationError{
						field:  "CustomMessagePayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MessageBodyValidationError{
						field:  "CustomMessagePayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCustomMessagePayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MessageBodyValidationError{
					field:  "CustomMessagePayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MessageBodyMultiError(errors)
	}

	return nil
}

// MessageBodyMultiError is an error wrapping multiple validation errors
// returned by MessageBody.ValidateAll() if the designated constraints aren't met.
type MessageBodyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MessageBodyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MessageBodyMultiError) AllErrors() []error { return m }

// MessageBodyValidationError is the validation error returned by
// MessageBody.Validate if the designated constraints aren't met.
type MessageBodyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MessageBodyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MessageBodyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MessageBodyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MessageBodyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MessageBodyValidationError) ErrorName() string { return "MessageBodyValidationError" }

// Error satisfies the builtin error interface
func (e MessageBodyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMessageBody.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MessageBodyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MessageBodyValidationError{}

// Validate checks the field values on Message with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Message) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Message with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MessageMultiError, or nil if none found.
func (m *Message) ValidateAll() error {
	return m.validate(true)
}

func (m *Message) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _Message_MessageType_InLookup[m.GetMessageType()]; !ok {
		err := MessageValidationError{
			field:  "MessageType",
			reason: "value must be in list [MESSAGE_TYPE_TEXT MESSAGE_TYPE_CUSTOM]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := MessageType_name[int32(m.GetMessageType())]; !ok {
		err := MessageValidationError{
			field:  "MessageType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBody() == nil {
		err := MessageValidationError{
			field:  "Body",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBody()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MessageValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MessageValidationError{
					field:  "Body",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBody()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MessageValidationError{
				field:  "Body",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MessageMultiError(errors)
	}

	return nil
}

// MessageMultiError is an error wrapping multiple validation errors returned
// by Message.ValidateAll() if the designated constraints aren't met.
type MessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MessageMultiError) AllErrors() []error { return m }

// MessageValidationError is the validation error returned by Message.Validate
// if the designated constraints aren't met.
type MessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MessageValidationError) ErrorName() string { return "MessageValidationError" }

// Error satisfies the builtin error interface
func (e MessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MessageValidationError{}

var _Message_MessageType_InLookup = map[MessageType]struct{}{
	1: {},
	7: {},
}

// Validate checks the field values on
// StoryExchangeImageInteractionCustomMessagePayload with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryExchangeImageInteractionCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryExchangeImageInteractionCustomMessagePayload with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// StoryExchangeImageInteractionCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryExchangeImageInteractionCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryExchangeImageInteractionCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsumerImageUrl

	// no validation rules for ConsumerInteractionImageUrl

	// no validation rules for StoryId

	// no validation rules for NodeId

	// no validation rules for Title

	// no validation rules for StoryCoverUrl

	if len(errors) > 0 {
		return StoryExchangeImageInteractionCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryExchangeImageInteractionCustomMessagePayloadMultiError is an error
// wrapping multiple validation errors returned by
// StoryExchangeImageInteractionCustomMessagePayload.ValidateAll() if the
// designated constraints aren't met.
type StoryExchangeImageInteractionCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryExchangeImageInteractionCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryExchangeImageInteractionCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryExchangeImageInteractionCustomMessagePayloadValidationError is the
// validation error returned by
// StoryExchangeImageInteractionCustomMessagePayload.Validate if the
// designated constraints aren't met.
type StoryExchangeImageInteractionCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryExchangeImageInteractionCustomMessagePayloadValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e StoryExchangeImageInteractionCustomMessagePayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoryExchangeImageInteractionCustomMessagePayloadValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e StoryExchangeImageInteractionCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryExchangeImageInteractionCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryExchangeImageInteractionCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryExchangeImageInteractionCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryExchangeImageInteractionCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryExchangeImageInteractionCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryExchangeImageInteractionCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryChatProxyCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryChatProxyCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryChatProxyCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryChatProxyCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryChatProxyCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryChatProxyCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsumerVideoCoverUrl

	// no validation rules for ConsumerVideoUrl

	// no validation rules for StoryCoverUrl

	// no validation rules for StoryId

	if len(errors) > 0 {
		return StoryChatProxyCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryChatProxyCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryChatProxyCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryChatProxyCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryChatProxyCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryChatProxyCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryChatProxyCustomMessagePayloadValidationError is the validation error
// returned by StoryChatProxyCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryChatProxyCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryChatProxyCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryChatProxyCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryChatProxyCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryChatProxyCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryChatProxyCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryChatProxyCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryChatProxyCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryChatProxyCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryChatProxyCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryChatProxyCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryRoastedCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryRoastedCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryRoastedCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryRoastedCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryRoastedCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryRoastedCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsumerVideoCoverUrl

	// no validation rules for ConsumerVideoUrl

	// no validation rules for StoryCoverUrl

	// no validation rules for StoryId

	if len(errors) > 0 {
		return StoryRoastedCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryRoastedCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryRoastedCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryRoastedCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryRoastedCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryRoastedCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryRoastedCustomMessagePayloadValidationError is the validation error
// returned by StoryRoastedCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryRoastedCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryRoastedCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryRoastedCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryRoastedCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryRoastedCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryRoastedCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryRoastedCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryRoastedCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryRoastedCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryRoastedCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryRoastedCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryUnmuteCustomMessagePayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryUnmuteCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryUnmuteCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryUnmuteCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryUnmuteCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryUnmuteCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsumerAudioUrl

	// no validation rules for ConsumerInteractionImageUrl

	// no validation rules for StoryId

	// no validation rules for Title

	// no validation rules for StoryCoverUrl

	// no validation rules for AiResponse

	if len(errors) > 0 {
		return StoryUnmuteCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryUnmuteCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by StoryUnmuteCustomMessagePayload.ValidateAll()
// if the designated constraints aren't met.
type StoryUnmuteCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryUnmuteCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryUnmuteCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryUnmuteCustomMessagePayloadValidationError is the validation error
// returned by StoryUnmuteCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryUnmuteCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryUnmuteCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryUnmuteCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryUnmuteCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryUnmuteCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryUnmuteCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryUnmuteCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryUnmuteCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryUnmuteCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryUnmuteCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryUnmuteCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryCapsuleCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryCapsuleCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryCapsuleCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryCapsuleCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryCapsuleCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryCapsuleCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for StoryCoverUrl

	// no validation rules for ConsumerImageUrl

	// no validation rules for Title

	// no validation rules for InDays

	// no validation rules for Moments

	if len(errors) > 0 {
		return StoryCapsuleCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryCapsuleCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryCapsuleCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryCapsuleCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryCapsuleCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryCapsuleCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryCapsuleCustomMessagePayloadValidationError is the validation error
// returned by StoryCapsuleCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryCapsuleCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryCapsuleCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryCapsuleCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryCapsuleCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryCapsuleCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryCapsuleCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryCapsuleCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryCapsuleCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryCapsuleCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryCapsuleCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryCapsuleCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryWhoInteractionCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StoryWhoInteractionCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryWhoInteractionCustomMessagePayload with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// StoryWhoInteractionCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryWhoInteractionCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryWhoInteractionCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SelectedAvatarUrl

	// no validation rules for SelectedUserName

	// no validation rules for Correct

	// no validation rules for TriedTimes

	// no validation rules for StoryId

	if len(errors) > 0 {
		return StoryWhoInteractionCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryWhoInteractionCustomMessagePayloadMultiError is an error wrapping
// multiple validation errors returned by
// StoryWhoInteractionCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryWhoInteractionCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryWhoInteractionCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryWhoInteractionCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryWhoInteractionCustomMessagePayloadValidationError is the validation
// error returned by StoryWhoInteractionCustomMessagePayload.Validate if the
// designated constraints aren't met.
type StoryWhoInteractionCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryWhoInteractionCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryWhoInteractionCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryWhoInteractionCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryWhoInteractionCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryWhoInteractionCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryWhoInteractionCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryWhoInteractionCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryWhoInteractionCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryWhoInteractionCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryWhoInteractionCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryWassupCustomMessagePayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryWassupCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryWassupCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryWassupCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryWassupCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryWassupCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsumerVideoCoverUrl

	// no validation rules for ConsumerVideoUrl

	// no validation rules for StoryCoverUrl

	// no validation rules for StoryId

	if len(errors) > 0 {
		return StoryWassupCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryWassupCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by StoryWassupCustomMessagePayload.ValidateAll()
// if the designated constraints aren't met.
type StoryWassupCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryWassupCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryWassupCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryWassupCustomMessagePayloadValidationError is the validation error
// returned by StoryWassupCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryWassupCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryWassupCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryWassupCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryWassupCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryWassupCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryWassupCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryWassupCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryWassupCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryWassupCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryWassupCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryWassupCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryNowShotCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryNowShotCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryNowShotCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryNowShotCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryNowShotCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryNowShotCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayloadType

	// no validation rules for ConsumerImageUrl

	// no validation rules for ConsumerVideoUrl

	// no validation rules for ConsumerInteractionImageUrl

	// no validation rules for ConsumerInteractionVideoUrl

	// no validation rules for StoryId

	// no validation rules for Title

	// no validation rules for StoryCoverUrl

	if len(errors) > 0 {
		return StoryNowShotCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryNowShotCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryNowShotCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryNowShotCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryNowShotCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryNowShotCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryNowShotCustomMessagePayloadValidationError is the validation error
// returned by StoryNowShotCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryNowShotCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryNowShotCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryNowShotCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryNowShotCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryNowShotCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryNowShotCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryNowShotCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryNowShotCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryNowShotCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryNowShotCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryNowShotCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryTurtleSoupCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryTurtleSoupCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryTurtleSoupCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryTurtleSoupCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryTurtleSoupCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryTurtleSoupCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayloadType

	// no validation rules for StoryInfoJsonStr

	// no validation rules for AiResponse

	// no validation rules for UserText

	switch v := m.Payload.(type) {
	case *StoryTurtleSoupCustomMessagePayload_Text:
		if v == nil {
			err := StoryTurtleSoupCustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Text
	case *StoryTurtleSoupCustomMessagePayload_EndImagePayload_:
		if v == nil {
			err := StoryTurtleSoupCustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEndImagePayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryTurtleSoupCustomMessagePayloadValidationError{
						field:  "EndImagePayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryTurtleSoupCustomMessagePayloadValidationError{
						field:  "EndImagePayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEndImagePayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryTurtleSoupCustomMessagePayloadValidationError{
					field:  "EndImagePayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StoryTurtleSoupCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryTurtleSoupCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryTurtleSoupCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryTurtleSoupCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryTurtleSoupCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryTurtleSoupCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryTurtleSoupCustomMessagePayloadValidationError is the validation error
// returned by StoryTurtleSoupCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryTurtleSoupCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryTurtleSoupCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryTurtleSoupCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryTurtleSoupCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryTurtleSoupCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryTurtleSoupCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryTurtleSoupCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryTurtleSoupCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryTurtleSoupCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryTurtleSoupCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryTurtleSoupCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryFizzCustomMessagePayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryFizzCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryFizzCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryFizzCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryFizzCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryFizzCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PayloadType

	// no validation rules for FizzId

	// no validation rules for Text

	// no validation rules for ExpireAt

	// no validation rules for MediaId

	switch v := m.Payload.(type) {
	case *StoryFizzCustomMessagePayload_ImageUrl:
		if v == nil {
			err := StoryFizzCustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ImageUrl
	case *StoryFizzCustomMessagePayload_VideoPayload_:
		if v == nil {
			err := StoryFizzCustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVideoPayload()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryFizzCustomMessagePayloadValidationError{
						field:  "VideoPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryFizzCustomMessagePayloadValidationError{
						field:  "VideoPayload",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVideoPayload()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryFizzCustomMessagePayloadValidationError{
					field:  "VideoPayload",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StoryFizzCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryFizzCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by StoryFizzCustomMessagePayload.ValidateAll()
// if the designated constraints aren't met.
type StoryFizzCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryFizzCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryFizzCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryFizzCustomMessagePayloadValidationError is the validation error
// returned by StoryFizzCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryFizzCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryFizzCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryFizzCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryFizzCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryFizzCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryFizzCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryFizzCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryFizzCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryFizzCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryFizzCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryFizzCustomMessagePayloadValidationError{}

// Validate checks the field values on RelayFizzJoinInviteCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RelayFizzJoinInviteCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RelayFizzJoinInviteCustomMessagePayload with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// RelayFizzJoinInviteCustomMessagePayloadMultiError, or nil if none found.
func (m *RelayFizzJoinInviteCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RelayFizzJoinInviteCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RoomId

	// no validation rules for CreatorId

	// no validation rules for ExpireAtUnixstamp

	if len(errors) > 0 {
		return RelayFizzJoinInviteCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// RelayFizzJoinInviteCustomMessagePayloadMultiError is an error wrapping
// multiple validation errors returned by
// RelayFizzJoinInviteCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type RelayFizzJoinInviteCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelayFizzJoinInviteCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelayFizzJoinInviteCustomMessagePayloadMultiError) AllErrors() []error { return m }

// RelayFizzJoinInviteCustomMessagePayloadValidationError is the validation
// error returned by RelayFizzJoinInviteCustomMessagePayload.Validate if the
// designated constraints aren't met.
type RelayFizzJoinInviteCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelayFizzJoinInviteCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelayFizzJoinInviteCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelayFizzJoinInviteCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelayFizzJoinInviteCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelayFizzJoinInviteCustomMessagePayloadValidationError) ErrorName() string {
	return "RelayFizzJoinInviteCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RelayFizzJoinInviteCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelayFizzJoinInviteCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelayFizzJoinInviteCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelayFizzJoinInviteCustomMessagePayloadValidationError{}

// Validate checks the field values on RushFizzJoinInviteCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RushFizzJoinInviteCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RushFizzJoinInviteCustomMessagePayload with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// RushFizzJoinInviteCustomMessagePayloadMultiError, or nil if none found.
func (m *RushFizzJoinInviteCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *RushFizzJoinInviteCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RoomId

	// no validation rules for CreatorId

	// no validation rules for ExpireAtUnixstamp

	if len(errors) > 0 {
		return RushFizzJoinInviteCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// RushFizzJoinInviteCustomMessagePayloadMultiError is an error wrapping
// multiple validation errors returned by
// RushFizzJoinInviteCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type RushFizzJoinInviteCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RushFizzJoinInviteCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RushFizzJoinInviteCustomMessagePayloadMultiError) AllErrors() []error { return m }

// RushFizzJoinInviteCustomMessagePayloadValidationError is the validation
// error returned by RushFizzJoinInviteCustomMessagePayload.Validate if the
// designated constraints aren't met.
type RushFizzJoinInviteCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RushFizzJoinInviteCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RushFizzJoinInviteCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RushFizzJoinInviteCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RushFizzJoinInviteCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RushFizzJoinInviteCustomMessagePayloadValidationError) ErrorName() string {
	return "RushFizzJoinInviteCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e RushFizzJoinInviteCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRushFizzJoinInviteCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RushFizzJoinInviteCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RushFizzJoinInviteCustomMessagePayloadValidationError{}

// Validate checks the field values on BooInteractionCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BooInteractionCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooInteractionCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BooInteractionCustomMessagePayloadMultiError, or nil if none found.
func (m *BooInteractionCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *BooInteractionCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MessageType

	if all {
		switch v := interface{}(m.GetVideoPayload()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BooInteractionCustomMessagePayloadValidationError{
					field:  "VideoPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BooInteractionCustomMessagePayloadValidationError{
					field:  "VideoPayload",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVideoPayload()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BooInteractionCustomMessagePayloadValidationError{
				field:  "VideoPayload",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BooAvatarUrl

	if len(errors) > 0 {
		return BooInteractionCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// BooInteractionCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// BooInteractionCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type BooInteractionCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooInteractionCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooInteractionCustomMessagePayloadMultiError) AllErrors() []error { return m }

// BooInteractionCustomMessagePayloadValidationError is the validation error
// returned by BooInteractionCustomMessagePayload.Validate if the designated
// constraints aren't met.
type BooInteractionCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooInteractionCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooInteractionCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooInteractionCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooInteractionCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooInteractionCustomMessagePayloadValidationError) ErrorName() string {
	return "BooInteractionCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e BooInteractionCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooInteractionCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooInteractionCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooInteractionCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryHauntCustomMessagePayload with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryHauntCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryHauntCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryHauntCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryHauntCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryHauntCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VideoUrl

	// no validation rules for VideoCoverUrl

	// no validation rules for BooAvatarUrl

	// no validation rules for StoryId

	if len(errors) > 0 {
		return StoryHauntCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryHauntCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by StoryHauntCustomMessagePayload.ValidateAll()
// if the designated constraints aren't met.
type StoryHauntCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryHauntCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryHauntCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryHauntCustomMessagePayloadValidationError is the validation error
// returned by StoryHauntCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryHauntCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryHauntCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryHauntCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryHauntCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryHauntCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryHauntCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryHauntCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryHauntCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryHauntCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryHauntCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryHauntCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryBaseplayCustomMessagePayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryBaseplayCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryBaseplayCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryBaseplayCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryBaseplayCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryBaseplayCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for StoryCoverUrl

	// no validation rules for ConsumerStoryCoverUrl

	// no validation rules for Title

	if len(errors) > 0 {
		return StoryBaseplayCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryBaseplayCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryBaseplayCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryBaseplayCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryBaseplayCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryBaseplayCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryBaseplayCustomMessagePayloadValidationError is the validation error
// returned by StoryBaseplayCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryBaseplayCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryBaseplayCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryBaseplayCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryBaseplayCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryBaseplayCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryBaseplayCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryBaseplayCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryBaseplayCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryBaseplayCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryBaseplayCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryBaseplayCustomMessagePayloadValidationError{}

// Validate checks the field values on
// StoryHideStickerUnlockedCustomMessagePayload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryHideStickerUnlockedCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryHideStickerUnlockedCustomMessagePayload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StoryHideStickerUnlockedCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryHideStickerUnlockedCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryHideStickerUnlockedCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for StoryCoverUrl

	// no validation rules for StickerUrl

	// no validation rules for Title

	if len(errors) > 0 {
		return StoryHideStickerUnlockedCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryHideStickerUnlockedCustomMessagePayloadMultiError is an error wrapping
// multiple validation errors returned by
// StoryHideStickerUnlockedCustomMessagePayload.ValidateAll() if the
// designated constraints aren't met.
type StoryHideStickerUnlockedCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryHideStickerUnlockedCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryHideStickerUnlockedCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryHideStickerUnlockedCustomMessagePayloadValidationError is the
// validation error returned by
// StoryHideStickerUnlockedCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryHideStickerUnlockedCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryHideStickerUnlockedCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryHideStickerUnlockedCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryHideStickerUnlockedCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryHideStickerUnlockedCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryHideStickerUnlockedCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryHideStickerUnlockedCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryHideStickerUnlockedCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryHideStickerUnlockedCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryHideStickerUnlockedCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryHideStickerUnlockedCustomMessagePayloadValidationError{}

// Validate checks the field values on
// StoryShareCreateMomentCustomMessagePayload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryShareCreateMomentCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryShareCreateMomentCustomMessagePayload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StoryShareCreateMomentCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryShareCreateMomentCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryShareCreateMomentCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for StoryCoverUrl

	// no validation rules for MomentId

	// no validation rules for ShareCreateMomentType

	if len(errors) > 0 {
		return StoryShareCreateMomentCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryShareCreateMomentCustomMessagePayloadMultiError is an error wrapping
// multiple validation errors returned by
// StoryShareCreateMomentCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryShareCreateMomentCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryShareCreateMomentCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryShareCreateMomentCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryShareCreateMomentCustomMessagePayloadValidationError is the validation
// error returned by StoryShareCreateMomentCustomMessagePayload.Validate if
// the designated constraints aren't met.
type StoryShareCreateMomentCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryShareCreateMomentCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryShareCreateMomentCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryShareCreateMomentCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryShareCreateMomentCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryShareCreateMomentCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryShareCreateMomentCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryShareCreateMomentCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryShareCreateMomentCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryShareCreateMomentCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryShareCreateMomentCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryMomentQuoteCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StoryMomentQuoteCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryMomentQuoteCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryMomentQuoteCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryMomentQuoteCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryMomentQuoteCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MomentId

	// no validation rules for PortalId

	// no validation rules for FirstMomentCoverImageUrl

	// no validation rules for Text

	if len(errors) > 0 {
		return StoryMomentQuoteCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryMomentQuoteCustomMessagePayloadMultiError is an error wrapping multiple
// validation errors returned by
// StoryMomentQuoteCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryMomentQuoteCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryMomentQuoteCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryMomentQuoteCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryMomentQuoteCustomMessagePayloadValidationError is the validation error
// returned by StoryMomentQuoteCustomMessagePayload.Validate if the designated
// constraints aren't met.
type StoryMomentQuoteCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryMomentQuoteCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryMomentQuoteCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryMomentQuoteCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryMomentQuoteCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryMomentQuoteCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryMomentQuoteCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryMomentQuoteCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryMomentQuoteCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryMomentQuoteCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryMomentQuoteCustomMessagePayloadValidationError{}

// Validate checks the field values on StoryPinInteractionCustomMessagePayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StoryPinInteractionCustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryPinInteractionCustomMessagePayload with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// StoryPinInteractionCustomMessagePayloadMultiError, or nil if none found.
func (m *StoryPinInteractionCustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPinInteractionCustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for BackgroundImageUrl

	// no validation rules for SuccessConsumeCostSeconds

	// no validation rules for FailedConsumeImageUrl

	if len(errors) > 0 {
		return StoryPinInteractionCustomMessagePayloadMultiError(errors)
	}

	return nil
}

// StoryPinInteractionCustomMessagePayloadMultiError is an error wrapping
// multiple validation errors returned by
// StoryPinInteractionCustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type StoryPinInteractionCustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPinInteractionCustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPinInteractionCustomMessagePayloadMultiError) AllErrors() []error { return m }

// StoryPinInteractionCustomMessagePayloadValidationError is the validation
// error returned by StoryPinInteractionCustomMessagePayload.Validate if the
// designated constraints aren't met.
type StoryPinInteractionCustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPinInteractionCustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPinInteractionCustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPinInteractionCustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPinInteractionCustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPinInteractionCustomMessagePayloadValidationError) ErrorName() string {
	return "StoryPinInteractionCustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPinInteractionCustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPinInteractionCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPinInteractionCustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPinInteractionCustomMessagePayloadValidationError{}

// Validate checks the field values on CustomMessagePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomMessagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomMessagePayload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomMessagePayloadMultiError, or nil if none found.
func (m *CustomMessagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomMessagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomMessageType

	// no validation rules for ConsumeStatus

	switch v := m.Payload.(type) {
	case *CustomMessagePayload_StoryExchangeImageInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryExchangeImageInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryExchangeImageInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryExchangeImageInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryExchangeImageInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryExchangeImageInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryTurtleSoupInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryTurtleSoupInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryTurtleSoupInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryTurtleSoupInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryTurtleSoupInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryTurtleSoupInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryFizzInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryFizzInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryFizzInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryFizzInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryFizzInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryFizzInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryNowShotInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryNowShotInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryNowShotInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryNowShotInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryNowShotInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryNowShotInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryUnmuteInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryUnmuteInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryUnmuteInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryUnmuteInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryUnmuteInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryUnmuteInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryWassupInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryWassupInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryWassupInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryWassupInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryWassupInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryWassupInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_RelayFizzJoinInvite:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRelayFizzJoinInvite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "RelayFizzJoinInvite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "RelayFizzJoinInvite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRelayFizzJoinInvite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "RelayFizzJoinInvite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryCapsuleInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryCapsuleInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryCapsuleInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryCapsuleInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryCapsuleInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryCapsuleInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_RushFizzJoinInvite:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRushFizzJoinInvite()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "RushFizzJoinInvite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "RushFizzJoinInvite",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRushFizzJoinInvite()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "RushFizzJoinInvite",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_BooInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBooInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "BooInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "BooInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBooInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "BooInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryRoastedInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryRoastedInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryRoastedInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryRoastedInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryRoastedInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryRoastedInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryChatproxyInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryChatproxyInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryChatproxyInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryChatproxyInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryChatproxyInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryChatproxyInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryWhoInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryWhoInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryWhoInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryWhoInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryWhoInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryWhoInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryHauntInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryHauntInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryHauntInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryHauntInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryHauntInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryHauntInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryBaseplayInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryBaseplayInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryBaseplayInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryBaseplayInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryBaseplayInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryBaseplayInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryHideStickerUnlocked:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryHideStickerUnlocked()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryHideStickerUnlocked",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryHideStickerUnlocked",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryHideStickerUnlocked()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryHideStickerUnlocked",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryShareCreateMoment:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryShareCreateMoment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryShareCreateMoment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryShareCreateMoment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryShareCreateMoment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryShareCreateMoment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryMomentQuote:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryMomentQuote()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryMomentQuote",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryMomentQuote",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryMomentQuote()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryMomentQuote",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CustomMessagePayload_StoryPinInteraction:
		if v == nil {
			err := CustomMessagePayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryPinInteraction()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryPinInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CustomMessagePayloadValidationError{
						field:  "StoryPinInteraction",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryPinInteraction()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CustomMessagePayloadValidationError{
					field:  "StoryPinInteraction",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CustomMessagePayloadMultiError(errors)
	}

	return nil
}

// CustomMessagePayloadMultiError is an error wrapping multiple validation
// errors returned by CustomMessagePayload.ValidateAll() if the designated
// constraints aren't met.
type CustomMessagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomMessagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomMessagePayloadMultiError) AllErrors() []error { return m }

// CustomMessagePayloadValidationError is the validation error returned by
// CustomMessagePayload.Validate if the designated constraints aren't met.
type CustomMessagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomMessagePayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomMessagePayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomMessagePayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomMessagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomMessagePayloadValidationError) ErrorName() string {
	return "CustomMessagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e CustomMessagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomMessagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomMessagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomMessagePayloadValidationError{}

// Validate checks the field values on
// StoryTurtleSoupCustomMessagePayload_EndImagePayload with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StoryTurtleSoupCustomMessagePayload_EndImagePayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryTurtleSoupCustomMessagePayload_EndImagePayload with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// StoryTurtleSoupCustomMessagePayload_EndImagePayloadMultiError, or nil if
// none found.
func (m *StoryTurtleSoupCustomMessagePayload_EndImagePayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryTurtleSoupCustomMessagePayload_EndImagePayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EndImageUrl

	// no validation rules for EndMessage

	if len(errors) > 0 {
		return StoryTurtleSoupCustomMessagePayload_EndImagePayloadMultiError(errors)
	}

	return nil
}

// StoryTurtleSoupCustomMessagePayload_EndImagePayloadMultiError is an error
// wrapping multiple validation errors returned by
// StoryTurtleSoupCustomMessagePayload_EndImagePayload.ValidateAll() if the
// designated constraints aren't met.
type StoryTurtleSoupCustomMessagePayload_EndImagePayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryTurtleSoupCustomMessagePayload_EndImagePayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryTurtleSoupCustomMessagePayload_EndImagePayloadMultiError) AllErrors() []error { return m }

// StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError is the
// validation error returned by
// StoryTurtleSoupCustomMessagePayload_EndImagePayload.Validate if the
// designated constraints aren't met.
type StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError) ErrorName() string {
	return "StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryTurtleSoupCustomMessagePayload_EndImagePayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryTurtleSoupCustomMessagePayload_EndImagePayloadValidationError{}

// Validate checks the field values on
// StoryFizzCustomMessagePayload_VideoPayload with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryFizzCustomMessagePayload_VideoPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryFizzCustomMessagePayload_VideoPayload with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StoryFizzCustomMessagePayload_VideoPayloadMultiError, or nil if none found.
func (m *StoryFizzCustomMessagePayload_VideoPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryFizzCustomMessagePayload_VideoPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VideoUrl

	// no validation rules for CoverUrl

	if len(errors) > 0 {
		return StoryFizzCustomMessagePayload_VideoPayloadMultiError(errors)
	}

	return nil
}

// StoryFizzCustomMessagePayload_VideoPayloadMultiError is an error wrapping
// multiple validation errors returned by
// StoryFizzCustomMessagePayload_VideoPayload.ValidateAll() if the designated
// constraints aren't met.
type StoryFizzCustomMessagePayload_VideoPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryFizzCustomMessagePayload_VideoPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryFizzCustomMessagePayload_VideoPayloadMultiError) AllErrors() []error { return m }

// StoryFizzCustomMessagePayload_VideoPayloadValidationError is the validation
// error returned by StoryFizzCustomMessagePayload_VideoPayload.Validate if
// the designated constraints aren't met.
type StoryFizzCustomMessagePayload_VideoPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryFizzCustomMessagePayload_VideoPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryFizzCustomMessagePayload_VideoPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryFizzCustomMessagePayload_VideoPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryFizzCustomMessagePayload_VideoPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryFizzCustomMessagePayload_VideoPayloadValidationError) ErrorName() string {
	return "StoryFizzCustomMessagePayload_VideoPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e StoryFizzCustomMessagePayload_VideoPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryFizzCustomMessagePayload_VideoPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryFizzCustomMessagePayload_VideoPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryFizzCustomMessagePayload_VideoPayloadValidationError{}

// Validate checks the field values on
// BooInteractionCustomMessagePayload_VideoPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BooInteractionCustomMessagePayload_VideoPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BooInteractionCustomMessagePayload_VideoPayload with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// BooInteractionCustomMessagePayload_VideoPayloadMultiError, or nil if none found.
func (m *BooInteractionCustomMessagePayload_VideoPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *BooInteractionCustomMessagePayload_VideoPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VideoUrl

	// no validation rules for CoverUrl

	if len(errors) > 0 {
		return BooInteractionCustomMessagePayload_VideoPayloadMultiError(errors)
	}

	return nil
}

// BooInteractionCustomMessagePayload_VideoPayloadMultiError is an error
// wrapping multiple validation errors returned by
// BooInteractionCustomMessagePayload_VideoPayload.ValidateAll() if the
// designated constraints aren't met.
type BooInteractionCustomMessagePayload_VideoPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooInteractionCustomMessagePayload_VideoPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooInteractionCustomMessagePayload_VideoPayloadMultiError) AllErrors() []error { return m }

// BooInteractionCustomMessagePayload_VideoPayloadValidationError is the
// validation error returned by
// BooInteractionCustomMessagePayload_VideoPayload.Validate if the designated
// constraints aren't met.
type BooInteractionCustomMessagePayload_VideoPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooInteractionCustomMessagePayload_VideoPayloadValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e BooInteractionCustomMessagePayload_VideoPayloadValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e BooInteractionCustomMessagePayload_VideoPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooInteractionCustomMessagePayload_VideoPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooInteractionCustomMessagePayload_VideoPayloadValidationError) ErrorName() string {
	return "BooInteractionCustomMessagePayload_VideoPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e BooInteractionCustomMessagePayload_VideoPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooInteractionCustomMessagePayload_VideoPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooInteractionCustomMessagePayload_VideoPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooInteractionCustomMessagePayload_VideoPayloadValidationError{}
