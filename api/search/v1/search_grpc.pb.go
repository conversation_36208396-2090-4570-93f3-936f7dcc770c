// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/search/v1/search.proto

package api_search_v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SearchService_Search_FullMethodName              = "/api.search.v1.SearchService/Search"
	SearchService_GetSearchHistory_FullMethodName    = "/api.search.v1.SearchService/GetSearchHistory"
	SearchService_DeleteSearchHistory_FullMethodName = "/api.search.v1.SearchService/DeleteSearchHistory"
	SearchService_UploadSearchHistory_FullMethodName = "/api.search.v1.SearchService/UploadSearchHistory"
)

// SearchServiceClient is the client API for SearchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SearchServiceClient interface {
	Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchResponse, error)
	GetSearchHistory(ctx context.Context, in *GetSearchHistoryRequest, opts ...grpc.CallOption) (*GetSearchHistoryResponse, error)
	DeleteSearchHistory(ctx context.Context, in *DeleteSearchHistoryRequest, opts ...grpc.CallOption) (*DeleteSearchHistoryResponse, error)
	UploadSearchHistory(ctx context.Context, in *UploadSearchHistoryRequest, opts ...grpc.CallOption) (*UploadSearchHistoryResponse, error)
}

type searchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSearchServiceClient(cc grpc.ClientConnInterface) SearchServiceClient {
	return &searchServiceClient{cc}
}

func (c *searchServiceClient) Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchResponse)
	err := c.cc.Invoke(ctx, SearchService_Search_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchServiceClient) GetSearchHistory(ctx context.Context, in *GetSearchHistoryRequest, opts ...grpc.CallOption) (*GetSearchHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSearchHistoryResponse)
	err := c.cc.Invoke(ctx, SearchService_GetSearchHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchServiceClient) DeleteSearchHistory(ctx context.Context, in *DeleteSearchHistoryRequest, opts ...grpc.CallOption) (*DeleteSearchHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSearchHistoryResponse)
	err := c.cc.Invoke(ctx, SearchService_DeleteSearchHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchServiceClient) UploadSearchHistory(ctx context.Context, in *UploadSearchHistoryRequest, opts ...grpc.CallOption) (*UploadSearchHistoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadSearchHistoryResponse)
	err := c.cc.Invoke(ctx, SearchService_UploadSearchHistory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SearchServiceServer is the server API for SearchService service.
// All implementations must embed UnimplementedSearchServiceServer
// for forward compatibility.
type SearchServiceServer interface {
	Search(context.Context, *SearchRequest) (*SearchResponse, error)
	GetSearchHistory(context.Context, *GetSearchHistoryRequest) (*GetSearchHistoryResponse, error)
	DeleteSearchHistory(context.Context, *DeleteSearchHistoryRequest) (*DeleteSearchHistoryResponse, error)
	UploadSearchHistory(context.Context, *UploadSearchHistoryRequest) (*UploadSearchHistoryResponse, error)
	mustEmbedUnimplementedSearchServiceServer()
}

// UnimplementedSearchServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSearchServiceServer struct{}

func (UnimplementedSearchServiceServer) Search(context.Context, *SearchRequest) (*SearchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Search not implemented")
}
func (UnimplementedSearchServiceServer) GetSearchHistory(context.Context, *GetSearchHistoryRequest) (*GetSearchHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSearchHistory not implemented")
}
func (UnimplementedSearchServiceServer) DeleteSearchHistory(context.Context, *DeleteSearchHistoryRequest) (*DeleteSearchHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSearchHistory not implemented")
}
func (UnimplementedSearchServiceServer) UploadSearchHistory(context.Context, *UploadSearchHistoryRequest) (*UploadSearchHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadSearchHistory not implemented")
}
func (UnimplementedSearchServiceServer) mustEmbedUnimplementedSearchServiceServer() {}
func (UnimplementedSearchServiceServer) testEmbeddedByValue()                       {}

// UnsafeSearchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SearchServiceServer will
// result in compilation errors.
type UnsafeSearchServiceServer interface {
	mustEmbedUnimplementedSearchServiceServer()
}

func RegisterSearchServiceServer(s grpc.ServiceRegistrar, srv SearchServiceServer) {
	// If the following call pancis, it indicates UnimplementedSearchServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SearchService_ServiceDesc, srv)
}

func _SearchService_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SearchService_Search_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).Search(ctx, req.(*SearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SearchService_GetSearchHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).GetSearchHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SearchService_GetSearchHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).GetSearchHistory(ctx, req.(*GetSearchHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SearchService_DeleteSearchHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSearchHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).DeleteSearchHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SearchService_DeleteSearchHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).DeleteSearchHistory(ctx, req.(*DeleteSearchHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SearchService_UploadSearchHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadSearchHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).UploadSearchHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SearchService_UploadSearchHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).UploadSearchHistory(ctx, req.(*UploadSearchHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SearchService_ServiceDesc is the grpc.ServiceDesc for SearchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SearchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.search.v1.SearchService",
	HandlerType: (*SearchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Search",
			Handler:    _SearchService_Search_Handler,
		},
		{
			MethodName: "GetSearchHistory",
			Handler:    _SearchService_GetSearchHistory_Handler,
		},
		{
			MethodName: "DeleteSearchHistory",
			Handler:    _SearchService_DeleteSearchHistory_Handler,
		},
		{
			MethodName: "UploadSearchHistory",
			Handler:    _SearchService_UploadSearchHistory_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/search/v1/search.proto",
}
