syntax = "proto3";

package api.search.v1;

option go_package = "boson/api/search/v1;api_search_v1";

import "api/common/v1/common.proto";
import "validate/validate.proto";
import "api/users/info/types/v1/types.proto";
import "google/api/annotations.proto";


service SearchService {
	rpc Search (SearchRequest) returns (SearchResponse) {
		option (google.api.http) = {
			post: "/v1/search"
			body: "*"
		};
	}
	rpc GetSearchHistory (GetSearchHistoryRequest) returns (GetSearchHistoryResponse) {
		option (google.api.http) = {
			post: "/v1/search_history"
			body: "*"
		};
	}
	rpc DeleteSearchHistory (DeleteSearchHistoryRequest) returns (DeleteSearchHistoryResponse) {
		option (google.api.http) = {
			post: "/v1/search_history/delete"
			body: "*"
		};
	}
	rpc UploadSearchHistory (UploadSearchHistoryRequest) returns (UploadSearchHistoryResponse) {
		option (google.api.http) = {
			post: "/v1/search_history/upload"
			body: "*"
		};
	}
}

// 目前仅支持用户搜索
enum SearchType {
	SEARCH_TYPE_UNSPECIFIED = 0;
	SEARCH_TYPE_USER = 1;
	// 搜索关注的人
	SEARCH_TYPE_FOLLOWING = 2;
	// 搜索朋友
	SEARCH_TYPE_FRIEND = 3;
}

message SearchRequest {
    string keyword = 1;
	// 搜索类型, 目前仅支持
	// 1. 用户搜索 + 关注的人搜索，见 SearchType
	// 2. 仅用户搜索
	// 见 SearchType
	string search_type = 2;
	api.common.v1.ListRequest list_request = 3[(validate.rules).message.required = true];
}

message SearchResponse {
	message Result {
		oneof result {
			api.users.info.types.v1.UserInfoSummary user_info_summary = 1;
		}
		SearchType search_type = 2;
	}
	repeated Result results = 1;
	api.common.v1.ListResponse list_response = 2;
}

message GetSearchHistoryRequest {
	// 返回的搜索历史条数
	int32 count = 1 [(validate.rules).int32 = {gte: 1, lte: 100}];
}

message SearchHistoryItem {
	// 搜索ID
	string search_id = 1 [(validate.rules).string.pattern = "^[0-9]+$"];
	// 搜索文本
	string search_text = 2;
	// 搜索类型
	SearchType search_type = 3;
}

message GetSearchHistoryResponse {
	repeated SearchHistoryItem search_history = 1;
}

message DeleteSearchHistoryRequest {
	// 要删除的搜索历史ID列表
	repeated string search_ids = 1 [(validate.rules).repeated = {min_items: 1, max_items: 100, items: {string: {pattern: "^[0-9]+$"}}}];
}

message DeleteSearchHistoryResponse {
	// 删除是否成功
	bool success = 1;
}

message SearchHistoryUploadItem {
	// 搜索文本
	string search_text = 1 [(validate.rules).string.min_len = 1];
	// 搜索类型
	string search_type = 2;
}

message UploadSearchHistoryRequest {
	// 要上传的搜索历史列表
	repeated SearchHistoryUploadItem search_items = 1 [(validate.rules).repeated = {min_items: 1, max_items: 100}];
}

message UploadSearchHistoryResponse {
	// 返回生成的搜索ID列表
	repeated string search_ids = 1;
}