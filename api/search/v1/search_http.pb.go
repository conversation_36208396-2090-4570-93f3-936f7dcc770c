// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: api/search/v1/search.proto

package api_search_v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationSearchServiceDeleteSearchHistory = "/api.search.v1.SearchService/DeleteSearchHistory"
const OperationSearchServiceGetSearchHistory = "/api.search.v1.SearchService/GetSearchHistory"
const OperationSearchServiceSearch = "/api.search.v1.SearchService/Search"
const OperationSearchServiceUploadSearchHistory = "/api.search.v1.SearchService/UploadSearchHistory"

type SearchServiceHTTPServer interface {
	DeleteSearchHistory(context.Context, *DeleteSearchHistoryRequest) (*DeleteSearchHistoryResponse, error)
	GetSearchHistory(context.Context, *GetSearchHistoryRequest) (*GetSearchHistoryResponse, error)
	Search(context.Context, *SearchRequest) (*SearchResponse, error)
	UploadSearchHistory(context.Context, *UploadSearchHistoryRequest) (*UploadSearchHistoryResponse, error)
}

func RegisterSearchServiceHTTPServer(s *http.Server, srv SearchServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/search", _SearchService_Search0_HTTP_Handler(srv))
	r.POST("/v1/search_history", _SearchService_GetSearchHistory0_HTTP_Handler(srv))
	r.POST("/v1/search_history/delete", _SearchService_DeleteSearchHistory0_HTTP_Handler(srv))
	r.POST("/v1/search_history/upload", _SearchService_UploadSearchHistory0_HTTP_Handler(srv))
}

func _SearchService_Search0_HTTP_Handler(srv SearchServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSearchServiceSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Search(ctx, req.(*SearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SearchResponse)
		return ctx.Result(200, reply)
	}
}

func _SearchService_GetSearchHistory0_HTTP_Handler(srv SearchServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetSearchHistoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSearchServiceGetSearchHistory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetSearchHistory(ctx, req.(*GetSearchHistoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetSearchHistoryResponse)
		return ctx.Result(200, reply)
	}
}

func _SearchService_DeleteSearchHistory0_HTTP_Handler(srv SearchServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteSearchHistoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSearchServiceDeleteSearchHistory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteSearchHistory(ctx, req.(*DeleteSearchHistoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteSearchHistoryResponse)
		return ctx.Result(200, reply)
	}
}

func _SearchService_UploadSearchHistory0_HTTP_Handler(srv SearchServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadSearchHistoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationSearchServiceUploadSearchHistory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadSearchHistory(ctx, req.(*UploadSearchHistoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadSearchHistoryResponse)
		return ctx.Result(200, reply)
	}
}

type SearchServiceHTTPClient interface {
	DeleteSearchHistory(ctx context.Context, req *DeleteSearchHistoryRequest, opts ...http.CallOption) (rsp *DeleteSearchHistoryResponse, err error)
	GetSearchHistory(ctx context.Context, req *GetSearchHistoryRequest, opts ...http.CallOption) (rsp *GetSearchHistoryResponse, err error)
	Search(ctx context.Context, req *SearchRequest, opts ...http.CallOption) (rsp *SearchResponse, err error)
	UploadSearchHistory(ctx context.Context, req *UploadSearchHistoryRequest, opts ...http.CallOption) (rsp *UploadSearchHistoryResponse, err error)
}

type SearchServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewSearchServiceHTTPClient(client *http.Client) SearchServiceHTTPClient {
	return &SearchServiceHTTPClientImpl{client}
}

func (c *SearchServiceHTTPClientImpl) DeleteSearchHistory(ctx context.Context, in *DeleteSearchHistoryRequest, opts ...http.CallOption) (*DeleteSearchHistoryResponse, error) {
	var out DeleteSearchHistoryResponse
	pattern := "/v1/search_history/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSearchServiceDeleteSearchHistory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SearchServiceHTTPClientImpl) GetSearchHistory(ctx context.Context, in *GetSearchHistoryRequest, opts ...http.CallOption) (*GetSearchHistoryResponse, error) {
	var out GetSearchHistoryResponse
	pattern := "/v1/search_history"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSearchServiceGetSearchHistory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SearchServiceHTTPClientImpl) Search(ctx context.Context, in *SearchRequest, opts ...http.CallOption) (*SearchResponse, error) {
	var out SearchResponse
	pattern := "/v1/search"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSearchServiceSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *SearchServiceHTTPClientImpl) UploadSearchHistory(ctx context.Context, in *UploadSearchHistoryRequest, opts ...http.CallOption) (*UploadSearchHistoryResponse, error) {
	var out UploadSearchHistoryResponse
	pattern := "/v1/search_history/upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationSearchServiceUploadSearchHistory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
