// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/search/v1/search.proto

package api_search_v1

import (
	v1 "boson/api/common/v1"
	v11 "boson/api/users/info/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 目前仅支持用户搜索
type SearchType int32

const (
	SearchType_SEARCH_TYPE_UNSPECIFIED SearchType = 0
	SearchType_SEARCH_TYPE_USER        SearchType = 1
	// 搜索关注的人
	SearchType_SEARCH_TYPE_FOLLOWING SearchType = 2
	// 搜索朋友
	SearchType_SEARCH_TYPE_FRIEND SearchType = 3
)

// Enum value maps for SearchType.
var (
	SearchType_name = map[int32]string{
		0: "SEARCH_TYPE_UNSPECIFIED",
		1: "SEARCH_TYPE_USER",
		2: "SEARCH_TYPE_FOLLOWING",
		3: "SEARCH_TYPE_FRIEND",
	}
	SearchType_value = map[string]int32{
		"SEARCH_TYPE_UNSPECIFIED": 0,
		"SEARCH_TYPE_USER":        1,
		"SEARCH_TYPE_FOLLOWING":   2,
		"SEARCH_TYPE_FRIEND":      3,
	}
)

func (x SearchType) Enum() *SearchType {
	p := new(SearchType)
	*p = x
	return p
}

func (x SearchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_search_v1_search_proto_enumTypes[0].Descriptor()
}

func (SearchType) Type() protoreflect.EnumType {
	return &file_api_search_v1_search_proto_enumTypes[0]
}

func (x SearchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchType.Descriptor instead.
func (SearchType) EnumDescriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{0}
}

type SearchRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Keyword string                 `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	// 搜索类型, 目前仅支持
	// 1. 用户搜索 + 关注的人搜索，见 SearchType
	// 2. 仅用户搜索
	// 见 SearchType
	SearchType    string          `protobuf:"bytes,2,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	ListRequest   *v1.ListRequest `protobuf:"bytes,3,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	mi := &file_api_search_v1_search_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{0}
}

func (x *SearchRequest) GetKeyword() string {
	if x != nil {
		return x.Keyword
	}
	return ""
}

func (x *SearchRequest) GetSearchType() string {
	if x != nil {
		return x.SearchType
	}
	return ""
}

func (x *SearchRequest) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type SearchResponse struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Results       []*SearchResponse_Result `protobuf:"bytes,1,rep,name=results,proto3" json:"results,omitempty"`
	ListResponse  *v1.ListResponse         `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	mi := &file_api_search_v1_search_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{1}
}

func (x *SearchResponse) GetResults() []*SearchResponse_Result {
	if x != nil {
		return x.Results
	}
	return nil
}

func (x *SearchResponse) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type GetSearchHistoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 返回的搜索历史条数
	Count         int32 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSearchHistoryRequest) Reset() {
	*x = GetSearchHistoryRequest{}
	mi := &file_api_search_v1_search_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSearchHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSearchHistoryRequest) ProtoMessage() {}

func (x *GetSearchHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSearchHistoryRequest.ProtoReflect.Descriptor instead.
func (*GetSearchHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{2}
}

func (x *GetSearchHistoryRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

type SearchHistoryItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索ID
	SearchId string `protobuf:"bytes,1,opt,name=search_id,json=searchId,proto3" json:"search_id,omitempty"`
	// 搜索文本
	SearchText string `protobuf:"bytes,2,opt,name=search_text,json=searchText,proto3" json:"search_text,omitempty"`
	// 搜索类型
	SearchType    SearchType `protobuf:"varint,3,opt,name=search_type,json=searchType,proto3,enum=api.search.v1.SearchType" json:"search_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchHistoryItem) Reset() {
	*x = SearchHistoryItem{}
	mi := &file_api_search_v1_search_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchHistoryItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchHistoryItem) ProtoMessage() {}

func (x *SearchHistoryItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchHistoryItem.ProtoReflect.Descriptor instead.
func (*SearchHistoryItem) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{3}
}

func (x *SearchHistoryItem) GetSearchId() string {
	if x != nil {
		return x.SearchId
	}
	return ""
}

func (x *SearchHistoryItem) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

func (x *SearchHistoryItem) GetSearchType() SearchType {
	if x != nil {
		return x.SearchType
	}
	return SearchType_SEARCH_TYPE_UNSPECIFIED
}

type GetSearchHistoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SearchHistory []*SearchHistoryItem   `protobuf:"bytes,1,rep,name=search_history,json=searchHistory,proto3" json:"search_history,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSearchHistoryResponse) Reset() {
	*x = GetSearchHistoryResponse{}
	mi := &file_api_search_v1_search_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSearchHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSearchHistoryResponse) ProtoMessage() {}

func (x *GetSearchHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSearchHistoryResponse.ProtoReflect.Descriptor instead.
func (*GetSearchHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{4}
}

func (x *GetSearchHistoryResponse) GetSearchHistory() []*SearchHistoryItem {
	if x != nil {
		return x.SearchHistory
	}
	return nil
}

type DeleteSearchHistoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 要删除的搜索历史ID列表
	SearchIds     []string `protobuf:"bytes,1,rep,name=search_ids,json=searchIds,proto3" json:"search_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSearchHistoryRequest) Reset() {
	*x = DeleteSearchHistoryRequest{}
	mi := &file_api_search_v1_search_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSearchHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSearchHistoryRequest) ProtoMessage() {}

func (x *DeleteSearchHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSearchHistoryRequest.ProtoReflect.Descriptor instead.
func (*DeleteSearchHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteSearchHistoryRequest) GetSearchIds() []string {
	if x != nil {
		return x.SearchIds
	}
	return nil
}

type DeleteSearchHistoryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 删除是否成功
	Success       bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSearchHistoryResponse) Reset() {
	*x = DeleteSearchHistoryResponse{}
	mi := &file_api_search_v1_search_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSearchHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSearchHistoryResponse) ProtoMessage() {}

func (x *DeleteSearchHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSearchHistoryResponse.ProtoReflect.Descriptor instead.
func (*DeleteSearchHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteSearchHistoryResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SearchHistoryUploadItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索文本
	SearchText string `protobuf:"bytes,1,opt,name=search_text,json=searchText,proto3" json:"search_text,omitempty"`
	// 搜索类型
	SearchType    string `protobuf:"bytes,2,opt,name=search_type,json=searchType,proto3" json:"search_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchHistoryUploadItem) Reset() {
	*x = SearchHistoryUploadItem{}
	mi := &file_api_search_v1_search_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchHistoryUploadItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchHistoryUploadItem) ProtoMessage() {}

func (x *SearchHistoryUploadItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchHistoryUploadItem.ProtoReflect.Descriptor instead.
func (*SearchHistoryUploadItem) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{7}
}

func (x *SearchHistoryUploadItem) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

func (x *SearchHistoryUploadItem) GetSearchType() string {
	if x != nil {
		return x.SearchType
	}
	return ""
}

type UploadSearchHistoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 要上传的搜索历史列表
	SearchItems   []*SearchHistoryUploadItem `protobuf:"bytes,1,rep,name=search_items,json=searchItems,proto3" json:"search_items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadSearchHistoryRequest) Reset() {
	*x = UploadSearchHistoryRequest{}
	mi := &file_api_search_v1_search_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadSearchHistoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadSearchHistoryRequest) ProtoMessage() {}

func (x *UploadSearchHistoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadSearchHistoryRequest.ProtoReflect.Descriptor instead.
func (*UploadSearchHistoryRequest) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{8}
}

func (x *UploadSearchHistoryRequest) GetSearchItems() []*SearchHistoryUploadItem {
	if x != nil {
		return x.SearchItems
	}
	return nil
}

type UploadSearchHistoryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 返回生成的搜索ID列表
	SearchIds     []string `protobuf:"bytes,1,rep,name=search_ids,json=searchIds,proto3" json:"search_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadSearchHistoryResponse) Reset() {
	*x = UploadSearchHistoryResponse{}
	mi := &file_api_search_v1_search_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadSearchHistoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadSearchHistoryResponse) ProtoMessage() {}

func (x *UploadSearchHistoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadSearchHistoryResponse.ProtoReflect.Descriptor instead.
func (*UploadSearchHistoryResponse) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{9}
}

func (x *UploadSearchHistoryResponse) GetSearchIds() []string {
	if x != nil {
		return x.SearchIds
	}
	return nil
}

type SearchResponse_Result struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Result:
	//
	//	*SearchResponse_Result_UserInfoSummary
	Result        isSearchResponse_Result_Result `protobuf_oneof:"result"`
	SearchType    SearchType                     `protobuf:"varint,2,opt,name=search_type,json=searchType,proto3,enum=api.search.v1.SearchType" json:"search_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchResponse_Result) Reset() {
	*x = SearchResponse_Result{}
	mi := &file_api_search_v1_search_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchResponse_Result) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse_Result) ProtoMessage() {}

func (x *SearchResponse_Result) ProtoReflect() protoreflect.Message {
	mi := &file_api_search_v1_search_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse_Result.ProtoReflect.Descriptor instead.
func (*SearchResponse_Result) Descriptor() ([]byte, []int) {
	return file_api_search_v1_search_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SearchResponse_Result) GetResult() isSearchResponse_Result_Result {
	if x != nil {
		return x.Result
	}
	return nil
}

func (x *SearchResponse_Result) GetUserInfoSummary() *v11.UserInfoSummary {
	if x != nil {
		if x, ok := x.Result.(*SearchResponse_Result_UserInfoSummary); ok {
			return x.UserInfoSummary
		}
	}
	return nil
}

func (x *SearchResponse_Result) GetSearchType() SearchType {
	if x != nil {
		return x.SearchType
	}
	return SearchType_SEARCH_TYPE_UNSPECIFIED
}

type isSearchResponse_Result_Result interface {
	isSearchResponse_Result_Result()
}

type SearchResponse_Result_UserInfoSummary struct {
	UserInfoSummary *v11.UserInfoSummary `protobuf:"bytes,1,opt,name=user_info_summary,json=userInfoSummary,proto3,oneof"`
}

func (*SearchResponse_Result_UserInfoSummary) isSearchResponse_Result_Result() {}

var File_api_search_v1_search_proto protoreflect.FileDescriptor

const file_api_search_v1_search_proto_rawDesc = "" +
	"\n" +
	"\x1aapi/search/v1/search.proto\x12\rapi.search.v1\x1a\x1aapi/common/v1/common.proto\x1a\x17validate/validate.proto\x1a#api/users/info/types/v1/types.proto\x1a\x1cgoogle/api/annotations.proto\"\x93\x01\n" +
	"\rSearchRequest\x12\x18\n" +
	"\akeyword\x18\x01 \x01(\tR\akeyword\x12\x1f\n" +
	"\vsearch_type\x18\x02 \x01(\tR\n" +
	"searchType\x12G\n" +
	"\flist_request\x18\x03 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xbb\x02\n" +
	"\x0eSearchResponse\x12>\n" +
	"\aresults\x18\x01 \x03(\v2$.api.search.v1.SearchResponse.ResultR\aresults\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x1a\xa6\x01\n" +
	"\x06Result\x12V\n" +
	"\x11user_info_summary\x18\x01 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryH\x00R\x0fuserInfoSummary\x12:\n" +
	"\vsearch_type\x18\x02 \x01(\x0e2\x19.api.search.v1.SearchTypeR\n" +
	"searchTypeB\b\n" +
	"\x06result\":\n" +
	"\x17GetSearchHistoryRequest\x12\x1f\n" +
	"\x05count\x18\x01 \x01(\x05B\t\xfaB\x06\x1a\x04\x18d(\x01R\x05count\"\x9e\x01\n" +
	"\x11SearchHistoryItem\x12,\n" +
	"\tsearch_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bsearchId\x12\x1f\n" +
	"\vsearch_text\x18\x02 \x01(\tR\n" +
	"searchText\x12:\n" +
	"\vsearch_type\x18\x03 \x01(\x0e2\x19.api.search.v1.SearchTypeR\n" +
	"searchType\"c\n" +
	"\x18GetSearchHistoryResponse\x12G\n" +
	"\x0esearch_history\x18\x01 \x03(\v2 .api.search.v1.SearchHistoryItemR\rsearchHistory\"U\n" +
	"\x1aDeleteSearchHistoryRequest\x127\n" +
	"\n" +
	"search_ids\x18\x01 \x03(\tB\x18\xfaB\x15\x92\x01\x12\b\x01\x10d\"\fr\n" +
	"2\b^[0-9]+$R\tsearchIds\"7\n" +
	"\x1bDeleteSearchHistoryResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"d\n" +
	"\x17SearchHistoryUploadItem\x12(\n" +
	"\vsearch_text\x18\x01 \x01(\tB\a\xfaB\x04r\x02\x10\x01R\n" +
	"searchText\x12\x1f\n" +
	"\vsearch_type\x18\x02 \x01(\tR\n" +
	"searchType\"s\n" +
	"\x1aUploadSearchHistoryRequest\x12U\n" +
	"\fsearch_items\x18\x01 \x03(\v2&.api.search.v1.SearchHistoryUploadItemB\n" +
	"\xfaB\a\x92\x01\x04\b\x01\x10dR\vsearchItems\"<\n" +
	"\x1bUploadSearchHistoryResponse\x12\x1d\n" +
	"\n" +
	"search_ids\x18\x01 \x03(\tR\tsearchIds*r\n" +
	"\n" +
	"SearchType\x12\x1b\n" +
	"\x17SEARCH_TYPE_UNSPECIFIED\x10\x00\x12\x14\n" +
	"\x10SEARCH_TYPE_USER\x10\x01\x12\x19\n" +
	"\x15SEARCH_TYPE_FOLLOWING\x10\x02\x12\x16\n" +
	"\x12SEARCH_TYPE_FRIEND\x10\x032\x9c\x04\n" +
	"\rSearchService\x12\\\n" +
	"\x06Search\x12\x1c.api.search.v1.SearchRequest\x1a\x1d.api.search.v1.SearchResponse\"\x15\x82\xd3\xe4\x93\x02\x0f:\x01*\"\n" +
	"/v1/search\x12\x82\x01\n" +
	"\x10GetSearchHistory\x12&.api.search.v1.GetSearchHistoryRequest\x1a'.api.search.v1.GetSearchHistoryResponse\"\x1d\x82\xd3\xe4\x93\x02\x17:\x01*\"\x12/v1/search_history\x12\x92\x01\n" +
	"\x13DeleteSearchHistory\x12).api.search.v1.DeleteSearchHistoryRequest\x1a*.api.search.v1.DeleteSearchHistoryResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/search_history/delete\x12\x92\x01\n" +
	"\x13UploadSearchHistory\x12).api.search.v1.UploadSearchHistoryRequest\x1a*.api.search.v1.UploadSearchHistoryResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/search_history/uploadB#Z!boson/api/search/v1;api_search_v1b\x06proto3"

var (
	file_api_search_v1_search_proto_rawDescOnce sync.Once
	file_api_search_v1_search_proto_rawDescData []byte
)

func file_api_search_v1_search_proto_rawDescGZIP() []byte {
	file_api_search_v1_search_proto_rawDescOnce.Do(func() {
		file_api_search_v1_search_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_search_v1_search_proto_rawDesc), len(file_api_search_v1_search_proto_rawDesc)))
	})
	return file_api_search_v1_search_proto_rawDescData
}

var file_api_search_v1_search_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_search_v1_search_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_search_v1_search_proto_goTypes = []any{
	(SearchType)(0),                     // 0: api.search.v1.SearchType
	(*SearchRequest)(nil),               // 1: api.search.v1.SearchRequest
	(*SearchResponse)(nil),              // 2: api.search.v1.SearchResponse
	(*GetSearchHistoryRequest)(nil),     // 3: api.search.v1.GetSearchHistoryRequest
	(*SearchHistoryItem)(nil),           // 4: api.search.v1.SearchHistoryItem
	(*GetSearchHistoryResponse)(nil),    // 5: api.search.v1.GetSearchHistoryResponse
	(*DeleteSearchHistoryRequest)(nil),  // 6: api.search.v1.DeleteSearchHistoryRequest
	(*DeleteSearchHistoryResponse)(nil), // 7: api.search.v1.DeleteSearchHistoryResponse
	(*SearchHistoryUploadItem)(nil),     // 8: api.search.v1.SearchHistoryUploadItem
	(*UploadSearchHistoryRequest)(nil),  // 9: api.search.v1.UploadSearchHistoryRequest
	(*UploadSearchHistoryResponse)(nil), // 10: api.search.v1.UploadSearchHistoryResponse
	(*SearchResponse_Result)(nil),       // 11: api.search.v1.SearchResponse.Result
	(*v1.ListRequest)(nil),              // 12: api.common.v1.ListRequest
	(*v1.ListResponse)(nil),             // 13: api.common.v1.ListResponse
	(*v11.UserInfoSummary)(nil),         // 14: api.users.info.types.v1.UserInfoSummary
}
var file_api_search_v1_search_proto_depIdxs = []int32{
	12, // 0: api.search.v1.SearchRequest.list_request:type_name -> api.common.v1.ListRequest
	11, // 1: api.search.v1.SearchResponse.results:type_name -> api.search.v1.SearchResponse.Result
	13, // 2: api.search.v1.SearchResponse.list_response:type_name -> api.common.v1.ListResponse
	0,  // 3: api.search.v1.SearchHistoryItem.search_type:type_name -> api.search.v1.SearchType
	4,  // 4: api.search.v1.GetSearchHistoryResponse.search_history:type_name -> api.search.v1.SearchHistoryItem
	8,  // 5: api.search.v1.UploadSearchHistoryRequest.search_items:type_name -> api.search.v1.SearchHistoryUploadItem
	14, // 6: api.search.v1.SearchResponse.Result.user_info_summary:type_name -> api.users.info.types.v1.UserInfoSummary
	0,  // 7: api.search.v1.SearchResponse.Result.search_type:type_name -> api.search.v1.SearchType
	1,  // 8: api.search.v1.SearchService.Search:input_type -> api.search.v1.SearchRequest
	3,  // 9: api.search.v1.SearchService.GetSearchHistory:input_type -> api.search.v1.GetSearchHistoryRequest
	6,  // 10: api.search.v1.SearchService.DeleteSearchHistory:input_type -> api.search.v1.DeleteSearchHistoryRequest
	9,  // 11: api.search.v1.SearchService.UploadSearchHistory:input_type -> api.search.v1.UploadSearchHistoryRequest
	2,  // 12: api.search.v1.SearchService.Search:output_type -> api.search.v1.SearchResponse
	5,  // 13: api.search.v1.SearchService.GetSearchHistory:output_type -> api.search.v1.GetSearchHistoryResponse
	7,  // 14: api.search.v1.SearchService.DeleteSearchHistory:output_type -> api.search.v1.DeleteSearchHistoryResponse
	10, // 15: api.search.v1.SearchService.UploadSearchHistory:output_type -> api.search.v1.UploadSearchHistoryResponse
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_search_v1_search_proto_init() }
func file_api_search_v1_search_proto_init() {
	if File_api_search_v1_search_proto != nil {
		return
	}
	file_api_search_v1_search_proto_msgTypes[10].OneofWrappers = []any{
		(*SearchResponse_Result_UserInfoSummary)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_search_v1_search_proto_rawDesc), len(file_api_search_v1_search_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_search_v1_search_proto_goTypes,
		DependencyIndexes: file_api_search_v1_search_proto_depIdxs,
		EnumInfos:         file_api_search_v1_search_proto_enumTypes,
		MessageInfos:      file_api_search_v1_search_proto_msgTypes,
	}.Build()
	File_api_search_v1_search_proto = out.File
	file_api_search_v1_search_proto_goTypes = nil
	file_api_search_v1_search_proto_depIdxs = nil
}
