// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/search/v1/search.proto

package api_search_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SearchRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRequestMultiError, or
// nil if none found.
func (m *SearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Keyword

	// no validation rules for SearchType

	if m.GetListRequest() == nil {
		err := SearchRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchRequestMultiError(errors)
	}

	return nil
}

// SearchRequestMultiError is an error wrapping multiple validation errors
// returned by SearchRequest.ValidateAll() if the designated constraints
// aren't met.
type SearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequestMultiError) AllErrors() []error { return m }

// SearchRequestValidationError is the validation error returned by
// SearchRequest.Validate if the designated constraints aren't met.
type SearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequestValidationError) ErrorName() string { return "SearchRequestValidationError" }

// Error satisfies the builtin error interface
func (e SearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequestValidationError{}

// Validate checks the field values on SearchResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchResponseMultiError,
// or nil if none found.
func (m *SearchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResults() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchResponseValidationError{
						field:  fmt.Sprintf("Results[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchResponseValidationError{
					field:  fmt.Sprintf("Results[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SearchResponseMultiError(errors)
	}

	return nil
}

// SearchResponseMultiError is an error wrapping multiple validation errors
// returned by SearchResponse.ValidateAll() if the designated constraints
// aren't met.
type SearchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchResponseMultiError) AllErrors() []error { return m }

// SearchResponseValidationError is the validation error returned by
// SearchResponse.Validate if the designated constraints aren't met.
type SearchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchResponseValidationError) ErrorName() string { return "SearchResponseValidationError" }

// Error satisfies the builtin error interface
func (e SearchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchResponseValidationError{}

// Validate checks the field values on GetSearchHistoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSearchHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSearchHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSearchHistoryRequestMultiError, or nil if none found.
func (m *GetSearchHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSearchHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if val := m.GetCount(); val < 1 || val > 100 {
		err := GetSearchHistoryRequestValidationError{
			field:  "Count",
			reason: "value must be inside range [1, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSearchHistoryRequestMultiError(errors)
	}

	return nil
}

// GetSearchHistoryRequestMultiError is an error wrapping multiple validation
// errors returned by GetSearchHistoryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSearchHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSearchHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSearchHistoryRequestMultiError) AllErrors() []error { return m }

// GetSearchHistoryRequestValidationError is the validation error returned by
// GetSearchHistoryRequest.Validate if the designated constraints aren't met.
type GetSearchHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSearchHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSearchHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSearchHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSearchHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSearchHistoryRequestValidationError) ErrorName() string {
	return "GetSearchHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSearchHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSearchHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSearchHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSearchHistoryRequestValidationError{}

// Validate checks the field values on SearchHistoryItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SearchHistoryItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchHistoryItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchHistoryItemMultiError, or nil if none found.
func (m *SearchHistoryItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchHistoryItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SearchHistoryItem_SearchId_Pattern.MatchString(m.GetSearchId()) {
		err := SearchHistoryItemValidationError{
			field:  "SearchId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SearchText

	// no validation rules for SearchType

	if len(errors) > 0 {
		return SearchHistoryItemMultiError(errors)
	}

	return nil
}

// SearchHistoryItemMultiError is an error wrapping multiple validation errors
// returned by SearchHistoryItem.ValidateAll() if the designated constraints
// aren't met.
type SearchHistoryItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchHistoryItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchHistoryItemMultiError) AllErrors() []error { return m }

// SearchHistoryItemValidationError is the validation error returned by
// SearchHistoryItem.Validate if the designated constraints aren't met.
type SearchHistoryItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchHistoryItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchHistoryItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchHistoryItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchHistoryItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchHistoryItemValidationError) ErrorName() string {
	return "SearchHistoryItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchHistoryItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchHistoryItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchHistoryItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchHistoryItemValidationError{}

var _SearchHistoryItem_SearchId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on GetSearchHistoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSearchHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSearchHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSearchHistoryResponseMultiError, or nil if none found.
func (m *GetSearchHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSearchHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSearchHistory() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSearchHistoryResponseValidationError{
						field:  fmt.Sprintf("SearchHistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSearchHistoryResponseValidationError{
						field:  fmt.Sprintf("SearchHistory[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSearchHistoryResponseValidationError{
					field:  fmt.Sprintf("SearchHistory[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSearchHistoryResponseMultiError(errors)
	}

	return nil
}

// GetSearchHistoryResponseMultiError is an error wrapping multiple validation
// errors returned by GetSearchHistoryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSearchHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSearchHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSearchHistoryResponseMultiError) AllErrors() []error { return m }

// GetSearchHistoryResponseValidationError is the validation error returned by
// GetSearchHistoryResponse.Validate if the designated constraints aren't met.
type GetSearchHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSearchHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSearchHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSearchHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSearchHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSearchHistoryResponseValidationError) ErrorName() string {
	return "GetSearchHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSearchHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSearchHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSearchHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSearchHistoryResponseValidationError{}

// Validate checks the field values on DeleteSearchHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSearchHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSearchHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSearchHistoryRequestMultiError, or nil if none found.
func (m *DeleteSearchHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSearchHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetSearchIds()); l < 1 || l > 100 {
		err := DeleteSearchHistoryRequestValidationError{
			field:  "SearchIds",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetSearchIds() {
		_, _ = idx, item

		if !_DeleteSearchHistoryRequest_SearchIds_Pattern.MatchString(item) {
			err := DeleteSearchHistoryRequestValidationError{
				field:  fmt.Sprintf("SearchIds[%v]", idx),
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DeleteSearchHistoryRequestMultiError(errors)
	}

	return nil
}

// DeleteSearchHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteSearchHistoryRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteSearchHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSearchHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSearchHistoryRequestMultiError) AllErrors() []error { return m }

// DeleteSearchHistoryRequestValidationError is the validation error returned
// by DeleteSearchHistoryRequest.Validate if the designated constraints aren't met.
type DeleteSearchHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSearchHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSearchHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSearchHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSearchHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSearchHistoryRequestValidationError) ErrorName() string {
	return "DeleteSearchHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSearchHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSearchHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSearchHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSearchHistoryRequestValidationError{}

var _DeleteSearchHistoryRequest_SearchIds_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on DeleteSearchHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSearchHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSearchHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSearchHistoryResponseMultiError, or nil if none found.
func (m *DeleteSearchHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSearchHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	if len(errors) > 0 {
		return DeleteSearchHistoryResponseMultiError(errors)
	}

	return nil
}

// DeleteSearchHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by DeleteSearchHistoryResponse.ValidateAll() if
// the designated constraints aren't met.
type DeleteSearchHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSearchHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSearchHistoryResponseMultiError) AllErrors() []error { return m }

// DeleteSearchHistoryResponseValidationError is the validation error returned
// by DeleteSearchHistoryResponse.Validate if the designated constraints
// aren't met.
type DeleteSearchHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSearchHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSearchHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSearchHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSearchHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSearchHistoryResponseValidationError) ErrorName() string {
	return "DeleteSearchHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSearchHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSearchHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSearchHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSearchHistoryResponseValidationError{}

// Validate checks the field values on SearchHistoryUploadItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchHistoryUploadItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchHistoryUploadItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchHistoryUploadItemMultiError, or nil if none found.
func (m *SearchHistoryUploadItem) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchHistoryUploadItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSearchText()) < 1 {
		err := SearchHistoryUploadItemValidationError{
			field:  "SearchText",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SearchType

	if len(errors) > 0 {
		return SearchHistoryUploadItemMultiError(errors)
	}

	return nil
}

// SearchHistoryUploadItemMultiError is an error wrapping multiple validation
// errors returned by SearchHistoryUploadItem.ValidateAll() if the designated
// constraints aren't met.
type SearchHistoryUploadItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchHistoryUploadItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchHistoryUploadItemMultiError) AllErrors() []error { return m }

// SearchHistoryUploadItemValidationError is the validation error returned by
// SearchHistoryUploadItem.Validate if the designated constraints aren't met.
type SearchHistoryUploadItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchHistoryUploadItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchHistoryUploadItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchHistoryUploadItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchHistoryUploadItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchHistoryUploadItemValidationError) ErrorName() string {
	return "SearchHistoryUploadItemValidationError"
}

// Error satisfies the builtin error interface
func (e SearchHistoryUploadItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchHistoryUploadItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchHistoryUploadItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchHistoryUploadItemValidationError{}

// Validate checks the field values on UploadSearchHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadSearchHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadSearchHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadSearchHistoryRequestMultiError, or nil if none found.
func (m *UploadSearchHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadSearchHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetSearchItems()); l < 1 || l > 100 {
		err := UploadSearchHistoryRequestValidationError{
			field:  "SearchItems",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetSearchItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UploadSearchHistoryRequestValidationError{
						field:  fmt.Sprintf("SearchItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UploadSearchHistoryRequestValidationError{
						field:  fmt.Sprintf("SearchItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UploadSearchHistoryRequestValidationError{
					field:  fmt.Sprintf("SearchItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UploadSearchHistoryRequestMultiError(errors)
	}

	return nil
}

// UploadSearchHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by UploadSearchHistoryRequest.ValidateAll() if
// the designated constraints aren't met.
type UploadSearchHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadSearchHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadSearchHistoryRequestMultiError) AllErrors() []error { return m }

// UploadSearchHistoryRequestValidationError is the validation error returned
// by UploadSearchHistoryRequest.Validate if the designated constraints aren't met.
type UploadSearchHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadSearchHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadSearchHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadSearchHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadSearchHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadSearchHistoryRequestValidationError) ErrorName() string {
	return "UploadSearchHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadSearchHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadSearchHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadSearchHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadSearchHistoryRequestValidationError{}

// Validate checks the field values on UploadSearchHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadSearchHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadSearchHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UploadSearchHistoryResponseMultiError, or nil if none found.
func (m *UploadSearchHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadSearchHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UploadSearchHistoryResponseMultiError(errors)
	}

	return nil
}

// UploadSearchHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by UploadSearchHistoryResponse.ValidateAll() if
// the designated constraints aren't met.
type UploadSearchHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadSearchHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadSearchHistoryResponseMultiError) AllErrors() []error { return m }

// UploadSearchHistoryResponseValidationError is the validation error returned
// by UploadSearchHistoryResponse.Validate if the designated constraints
// aren't met.
type UploadSearchHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadSearchHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadSearchHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadSearchHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadSearchHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadSearchHistoryResponseValidationError) ErrorName() string {
	return "UploadSearchHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UploadSearchHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadSearchHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadSearchHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadSearchHistoryResponseValidationError{}

// Validate checks the field values on SearchResponse_Result with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchResponse_Result) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResponse_Result with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchResponse_ResultMultiError, or nil if none found.
func (m *SearchResponse_Result) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResponse_Result) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SearchType

	switch v := m.Result.(type) {
	case *SearchResponse_Result_UserInfoSummary:
		if v == nil {
			err := SearchResponse_ResultValidationError{
				field:  "Result",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserInfoSummary()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SearchResponse_ResultValidationError{
						field:  "UserInfoSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SearchResponse_ResultValidationError{
						field:  "UserInfoSummary",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserInfoSummary()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SearchResponse_ResultValidationError{
					field:  "UserInfoSummary",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SearchResponse_ResultMultiError(errors)
	}

	return nil
}

// SearchResponse_ResultMultiError is an error wrapping multiple validation
// errors returned by SearchResponse_Result.ValidateAll() if the designated
// constraints aren't met.
type SearchResponse_ResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchResponse_ResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchResponse_ResultMultiError) AllErrors() []error { return m }

// SearchResponse_ResultValidationError is the validation error returned by
// SearchResponse_Result.Validate if the designated constraints aren't met.
type SearchResponse_ResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchResponse_ResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchResponse_ResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchResponse_ResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchResponse_ResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchResponse_ResultValidationError) ErrorName() string {
	return "SearchResponse_ResultValidationError"
}

// Error satisfies the builtin error interface
func (e SearchResponse_ResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResponse_Result.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchResponse_ResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchResponse_ResultValidationError{}
