// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/users/boo/v1/boo.proto

package api_users_boo_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	api_users_boo_types_v1 "boson/api/users/boo/types/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = api_users_boo_types_v1.BooShowScene(0)
)

// Validate checks the field values on CreateAvatarJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAvatarJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAvatarJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAvatarJobRequestMultiError, or nil if none found.
func (m *CreateAvatarJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAvatarJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserPhotoObjectKey

	if len(errors) > 0 {
		return CreateAvatarJobRequestMultiError(errors)
	}

	return nil
}

// CreateAvatarJobRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAvatarJobRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAvatarJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAvatarJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAvatarJobRequestMultiError) AllErrors() []error { return m }

// CreateAvatarJobRequestValidationError is the validation error returned by
// CreateAvatarJobRequest.Validate if the designated constraints aren't met.
type CreateAvatarJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAvatarJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAvatarJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAvatarJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAvatarJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAvatarJobRequestValidationError) ErrorName() string {
	return "CreateAvatarJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAvatarJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAvatarJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAvatarJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAvatarJobRequestValidationError{}

// Validate checks the field values on CreateAvatarJobResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAvatarJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAvatarJobResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAvatarJobResponseMultiError, or nil if none found.
func (m *CreateAvatarJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAvatarJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUseAvatarCreatedInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAvatarJobResponseValidationError{
					field:  "UseAvatarCreatedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAvatarJobResponseValidationError{
					field:  "UseAvatarCreatedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUseAvatarCreatedInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAvatarJobResponseValidationError{
				field:  "UseAvatarCreatedInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAvatarJobResponseMultiError(errors)
	}

	return nil
}

// CreateAvatarJobResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAvatarJobResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAvatarJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAvatarJobResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAvatarJobResponseMultiError) AllErrors() []error { return m }

// CreateAvatarJobResponseValidationError is the validation error returned by
// CreateAvatarJobResponse.Validate if the designated constraints aren't met.
type CreateAvatarJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAvatarJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAvatarJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAvatarJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAvatarJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAvatarJobResponseValidationError) ErrorName() string {
	return "CreateAvatarJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAvatarJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAvatarJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAvatarJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAvatarJobResponseValidationError{}

// Validate checks the field values on GetLatestAvatarJobRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLatestAvatarJobRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestAvatarJobRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLatestAvatarJobRequestMultiError, or nil if none found.
func (m *GetLatestAvatarJobRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestAvatarJobRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetLatestAvatarJobRequestMultiError(errors)
	}

	return nil
}

// GetLatestAvatarJobRequestMultiError is an error wrapping multiple validation
// errors returned by GetLatestAvatarJobRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLatestAvatarJobRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestAvatarJobRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestAvatarJobRequestMultiError) AllErrors() []error { return m }

// GetLatestAvatarJobRequestValidationError is the validation error returned by
// GetLatestAvatarJobRequest.Validate if the designated constraints aren't met.
type GetLatestAvatarJobRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestAvatarJobRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestAvatarJobRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestAvatarJobRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestAvatarJobRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestAvatarJobRequestValidationError) ErrorName() string {
	return "GetLatestAvatarJobRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestAvatarJobRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestAvatarJobRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestAvatarJobRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestAvatarJobRequestValidationError{}

// Validate checks the field values on GetLatestAvatarJobResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLatestAvatarJobResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestAvatarJobResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLatestAvatarJobResponseMultiError, or nil if none found.
func (m *GetLatestAvatarJobResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestAvatarJobResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUseAvatarCreatedInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestAvatarJobResponseValidationError{
					field:  "UseAvatarCreatedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestAvatarJobResponseValidationError{
					field:  "UseAvatarCreatedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUseAvatarCreatedInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestAvatarJobResponseValidationError{
				field:  "UseAvatarCreatedInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLatestAvatarJobResponseMultiError(errors)
	}

	return nil
}

// GetLatestAvatarJobResponseMultiError is an error wrapping multiple
// validation errors returned by GetLatestAvatarJobResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLatestAvatarJobResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestAvatarJobResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestAvatarJobResponseMultiError) AllErrors() []error { return m }

// GetLatestAvatarJobResponseValidationError is the validation error returned
// by GetLatestAvatarJobResponse.Validate if the designated constraints aren't met.
type GetLatestAvatarJobResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestAvatarJobResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestAvatarJobResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestAvatarJobResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestAvatarJobResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestAvatarJobResponseValidationError) ErrorName() string {
	return "GetLatestAvatarJobResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestAvatarJobResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestAvatarJobResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestAvatarJobResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestAvatarJobResponseValidationError{}

// Validate checks the field values on GenerateBooWithSelectedAvatarRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateBooWithSelectedAvatarRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateBooWithSelectedAvatarRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateBooWithSelectedAvatarRequestMultiError, or nil if none found.
func (m *GenerateBooWithSelectedAvatarRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateBooWithSelectedAvatarRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GenerateBooWithSelectedAvatarRequestMultiError(errors)
	}

	return nil
}

// GenerateBooWithSelectedAvatarRequestMultiError is an error wrapping multiple
// validation errors returned by
// GenerateBooWithSelectedAvatarRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateBooWithSelectedAvatarRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateBooWithSelectedAvatarRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateBooWithSelectedAvatarRequestMultiError) AllErrors() []error { return m }

// GenerateBooWithSelectedAvatarRequestValidationError is the validation error
// returned by GenerateBooWithSelectedAvatarRequest.Validate if the designated
// constraints aren't met.
type GenerateBooWithSelectedAvatarRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateBooWithSelectedAvatarRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateBooWithSelectedAvatarRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateBooWithSelectedAvatarRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateBooWithSelectedAvatarRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateBooWithSelectedAvatarRequestValidationError) ErrorName() string {
	return "GenerateBooWithSelectedAvatarRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateBooWithSelectedAvatarRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateBooWithSelectedAvatarRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateBooWithSelectedAvatarRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateBooWithSelectedAvatarRequestValidationError{}

// Validate checks the field values on GenerateBooWithSelectedAvatarResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GenerateBooWithSelectedAvatarResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateBooWithSelectedAvatarResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateBooWithSelectedAvatarResponseMultiError, or nil if none found.
func (m *GenerateBooWithSelectedAvatarResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateBooWithSelectedAvatarResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AlreadyGenerated

	if len(errors) > 0 {
		return GenerateBooWithSelectedAvatarResponseMultiError(errors)
	}

	return nil
}

// GenerateBooWithSelectedAvatarResponseMultiError is an error wrapping
// multiple validation errors returned by
// GenerateBooWithSelectedAvatarResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateBooWithSelectedAvatarResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateBooWithSelectedAvatarResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateBooWithSelectedAvatarResponseMultiError) AllErrors() []error { return m }

// GenerateBooWithSelectedAvatarResponseValidationError is the validation error
// returned by GenerateBooWithSelectedAvatarResponse.Validate if the
// designated constraints aren't met.
type GenerateBooWithSelectedAvatarResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateBooWithSelectedAvatarResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateBooWithSelectedAvatarResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateBooWithSelectedAvatarResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateBooWithSelectedAvatarResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateBooWithSelectedAvatarResponseValidationError) ErrorName() string {
	return "GenerateBooWithSelectedAvatarResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateBooWithSelectedAvatarResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateBooWithSelectedAvatarResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateBooWithSelectedAvatarResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateBooWithSelectedAvatarResponseValidationError{}

// Validate checks the field values on SendAvatarAsBooToUsersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendAvatarAsBooToUsersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAvatarAsBooToUsersRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendAvatarAsBooToUsersRequestMultiError, or nil if none found.
func (m *SendAvatarAsBooToUsersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAvatarAsBooToUsersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetToUserIds()); l < 1 || l > 100 {
		err := SendAvatarAsBooToUsersRequestValidationError{
			field:  "ToUserIds",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	_SendAvatarAsBooToUsersRequest_ToUserIds_Unique := make(map[string]struct{}, len(m.GetToUserIds()))

	for idx, item := range m.GetToUserIds() {
		_, _ = idx, item

		if _, exists := _SendAvatarAsBooToUsersRequest_ToUserIds_Unique[item]; exists {
			err := SendAvatarAsBooToUsersRequestValidationError{
				field:  fmt.Sprintf("ToUserIds[%v]", idx),
				reason: "repeated value must contain unique items",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		} else {
			_SendAvatarAsBooToUsersRequest_ToUserIds_Unique[item] = struct{}{}
		}

		if !_SendAvatarAsBooToUsersRequest_ToUserIds_Pattern.MatchString(item) {
			err := SendAvatarAsBooToUsersRequestValidationError{
				field:  fmt.Sprintf("ToUserIds[%v]", idx),
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for AvatarObjectKey

	if len(errors) > 0 {
		return SendAvatarAsBooToUsersRequestMultiError(errors)
	}

	return nil
}

// SendAvatarAsBooToUsersRequestMultiError is an error wrapping multiple
// validation errors returned by SendAvatarAsBooToUsersRequest.ValidateAll()
// if the designated constraints aren't met.
type SendAvatarAsBooToUsersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAvatarAsBooToUsersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAvatarAsBooToUsersRequestMultiError) AllErrors() []error { return m }

// SendAvatarAsBooToUsersRequestValidationError is the validation error
// returned by SendAvatarAsBooToUsersRequest.Validate if the designated
// constraints aren't met.
type SendAvatarAsBooToUsersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAvatarAsBooToUsersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAvatarAsBooToUsersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAvatarAsBooToUsersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAvatarAsBooToUsersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAvatarAsBooToUsersRequestValidationError) ErrorName() string {
	return "SendAvatarAsBooToUsersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendAvatarAsBooToUsersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAvatarAsBooToUsersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAvatarAsBooToUsersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAvatarAsBooToUsersRequestValidationError{}

var _SendAvatarAsBooToUsersRequest_ToUserIds_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on GetLatestBooShowInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLatestBooShowInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestBooShowInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLatestBooShowInfoRequestMultiError, or nil if none found.
func (m *GetLatestBooShowInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestBooShowInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetLatestBooShowInfoRequestMultiError(errors)
	}

	return nil
}

// GetLatestBooShowInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetLatestBooShowInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type GetLatestBooShowInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestBooShowInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestBooShowInfoRequestMultiError) AllErrors() []error { return m }

// GetLatestBooShowInfoRequestValidationError is the validation error returned
// by GetLatestBooShowInfoRequest.Validate if the designated constraints
// aren't met.
type GetLatestBooShowInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestBooShowInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestBooShowInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestBooShowInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestBooShowInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestBooShowInfoRequestValidationError) ErrorName() string {
	return "GetLatestBooShowInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestBooShowInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestBooShowInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestBooShowInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestBooShowInfoRequestValidationError{}

// Validate checks the field values on GetLatestBooShowInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLatestBooShowInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLatestBooShowInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLatestBooShowInfoResponseMultiError, or nil if none found.
func (m *GetLatestBooShowInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLatestBooShowInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLatestBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLatestBooShowInfoResponseValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLatestBooShowInfoResponseValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLatestBooShowInfoResponseValidationError{
				field:  "LatestBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLatestBooShowInfoResponseMultiError(errors)
	}

	return nil
}

// GetLatestBooShowInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetLatestBooShowInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLatestBooShowInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLatestBooShowInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLatestBooShowInfoResponseMultiError) AllErrors() []error { return m }

// GetLatestBooShowInfoResponseValidationError is the validation error returned
// by GetLatestBooShowInfoResponse.Validate if the designated constraints
// aren't met.
type GetLatestBooShowInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLatestBooShowInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLatestBooShowInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLatestBooShowInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLatestBooShowInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLatestBooShowInfoResponseValidationError) ErrorName() string {
	return "GetLatestBooShowInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLatestBooShowInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLatestBooShowInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLatestBooShowInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLatestBooShowInfoResponseValidationError{}

// Validate checks the field values on RecordBooShowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordBooShowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordBooShowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordBooShowRequestMultiError, or nil if none found.
func (m *RecordBooShowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordBooShowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BooId

	// no validation rules for Scene

	// no validation rules for EmotionType

	if len(errors) > 0 {
		return RecordBooShowRequestMultiError(errors)
	}

	return nil
}

// RecordBooShowRequestMultiError is an error wrapping multiple validation
// errors returned by RecordBooShowRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordBooShowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordBooShowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordBooShowRequestMultiError) AllErrors() []error { return m }

// RecordBooShowRequestValidationError is the validation error returned by
// RecordBooShowRequest.Validate if the designated constraints aren't met.
type RecordBooShowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordBooShowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordBooShowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordBooShowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordBooShowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordBooShowRequestValidationError) ErrorName() string {
	return "RecordBooShowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordBooShowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordBooShowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordBooShowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordBooShowRequestValidationError{}

// Validate checks the field values on RecordBooShowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecordBooShowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordBooShowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecordBooShowResponseMultiError, or nil if none found.
func (m *RecordBooShowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordBooShowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLatestBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordBooShowResponseValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordBooShowResponseValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordBooShowResponseValidationError{
				field:  "LatestBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordBooShowResponseMultiError(errors)
	}

	return nil
}

// RecordBooShowResponseMultiError is an error wrapping multiple validation
// errors returned by RecordBooShowResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordBooShowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordBooShowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordBooShowResponseMultiError) AllErrors() []error { return m }

// RecordBooShowResponseValidationError is the validation error returned by
// RecordBooShowResponse.Validate if the designated constraints aren't met.
type RecordBooShowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordBooShowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordBooShowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordBooShowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordBooShowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordBooShowResponseValidationError) ErrorName() string {
	return "RecordBooShowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordBooShowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordBooShowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordBooShowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordBooShowResponseValidationError{}

// Validate checks the field values on CaptureBooActionReportRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaptureBooActionReportRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptureBooActionReportRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CaptureBooActionReportRequestMultiError, or nil if none found.
func (m *CaptureBooActionReportRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptureBooActionReportRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_CaptureBooActionReportRequest_BooId_Pattern.MatchString(m.GetBooId()) {
		err := CaptureBooActionReportRequestValidationError{
			field:  "BooId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := api_users_boo_types_v1.BooShowScene_name[int32(m.GetScene())]; !ok {
		err := CaptureBooActionReportRequestValidationError{
			field:  "Scene",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := api_users_boo_types_v1.BooEmotionType_name[int32(m.GetEmotionType())]; !ok {
		err := CaptureBooActionReportRequestValidationError{
			field:  "EmotionType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := api_users_boo_types_v1.UserActionType_name[int32(m.GetActionType())]; !ok {
		err := CaptureBooActionReportRequestValidationError{
			field:  "ActionType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CaptureBooActionReportRequestMultiError(errors)
	}

	return nil
}

// CaptureBooActionReportRequestMultiError is an error wrapping multiple
// validation errors returned by CaptureBooActionReportRequest.ValidateAll()
// if the designated constraints aren't met.
type CaptureBooActionReportRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptureBooActionReportRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptureBooActionReportRequestMultiError) AllErrors() []error { return m }

// CaptureBooActionReportRequestValidationError is the validation error
// returned by CaptureBooActionReportRequest.Validate if the designated
// constraints aren't met.
type CaptureBooActionReportRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptureBooActionReportRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptureBooActionReportRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptureBooActionReportRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptureBooActionReportRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptureBooActionReportRequestValidationError) ErrorName() string {
	return "CaptureBooActionReportRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CaptureBooActionReportRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptureBooActionReportRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptureBooActionReportRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptureBooActionReportRequestValidationError{}

var _CaptureBooActionReportRequest_BooId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CaptureBooActionReportResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CaptureBooActionReportResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CaptureBooActionReportResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CaptureBooActionReportResponseMultiError, or nil if none found.
func (m *CaptureBooActionReportResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CaptureBooActionReportResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionId

	if all {
		switch v := interface{}(m.GetLatestBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CaptureBooActionReportResponseValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CaptureBooActionReportResponseValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CaptureBooActionReportResponseValidationError{
				field:  "LatestBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CaptureBooActionReportResponseMultiError(errors)
	}

	return nil
}

// CaptureBooActionReportResponseMultiError is an error wrapping multiple
// validation errors returned by CaptureBooActionReportResponse.ValidateAll()
// if the designated constraints aren't met.
type CaptureBooActionReportResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CaptureBooActionReportResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CaptureBooActionReportResponseMultiError) AllErrors() []error { return m }

// CaptureBooActionReportResponseValidationError is the validation error
// returned by CaptureBooActionReportResponse.Validate if the designated
// constraints aren't met.
type CaptureBooActionReportResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CaptureBooActionReportResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CaptureBooActionReportResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CaptureBooActionReportResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CaptureBooActionReportResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CaptureBooActionReportResponseValidationError) ErrorName() string {
	return "CaptureBooActionReportResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CaptureBooActionReportResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCaptureBooActionReportResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CaptureBooActionReportResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CaptureBooActionReportResponseValidationError{}

// Validate checks the field values on SendCaptureRecordVideoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendCaptureRecordVideoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendCaptureRecordVideoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SendCaptureRecordVideoRequestMultiError, or nil if none found.
func (m *SendCaptureRecordVideoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendCaptureRecordVideoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SendCaptureRecordVideoRequest_BooId_Pattern.MatchString(m.GetBooId()) {
		err := SendCaptureRecordVideoRequestValidationError{
			field:  "BooId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for VideoObjectKey

	// no validation rules for VideoCoverObjectKey

	if len(errors) > 0 {
		return SendCaptureRecordVideoRequestMultiError(errors)
	}

	return nil
}

// SendCaptureRecordVideoRequestMultiError is an error wrapping multiple
// validation errors returned by SendCaptureRecordVideoRequest.ValidateAll()
// if the designated constraints aren't met.
type SendCaptureRecordVideoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendCaptureRecordVideoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendCaptureRecordVideoRequestMultiError) AllErrors() []error { return m }

// SendCaptureRecordVideoRequestValidationError is the validation error
// returned by SendCaptureRecordVideoRequest.Validate if the designated
// constraints aren't met.
type SendCaptureRecordVideoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendCaptureRecordVideoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendCaptureRecordVideoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendCaptureRecordVideoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendCaptureRecordVideoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendCaptureRecordVideoRequestValidationError) ErrorName() string {
	return "SendCaptureRecordVideoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendCaptureRecordVideoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendCaptureRecordVideoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendCaptureRecordVideoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendCaptureRecordVideoRequestValidationError{}

var _SendCaptureRecordVideoRequest_BooId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on SendBooToUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendBooToUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBooToUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendBooToUserRequestMultiError, or nil if none found.
func (m *SendBooToUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBooToUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SendBooToUserRequest_BooId_Pattern.MatchString(m.GetBooId()) {
		err := SendBooToUserRequestValidationError{
			field:  "BooId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SendBooToUserRequest_UserId_Pattern.MatchString(m.GetUserId()) {
		err := SendBooToUserRequestValidationError{
			field:  "UserId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SendBooToUserRequestMultiError(errors)
	}

	return nil
}

// SendBooToUserRequestMultiError is an error wrapping multiple validation
// errors returned by SendBooToUserRequest.ValidateAll() if the designated
// constraints aren't met.
type SendBooToUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBooToUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBooToUserRequestMultiError) AllErrors() []error { return m }

// SendBooToUserRequestValidationError is the validation error returned by
// SendBooToUserRequest.Validate if the designated constraints aren't met.
type SendBooToUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBooToUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBooToUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBooToUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBooToUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBooToUserRequestValidationError) ErrorName() string {
	return "SendBooToUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendBooToUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBooToUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBooToUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBooToUserRequestValidationError{}

var _SendBooToUserRequest_BooId_Pattern = regexp.MustCompile("^[0-9]+$")

var _SendBooToUserRequest_UserId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on SendBooToUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendBooToUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendBooToUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendBooToUserResponseMultiError, or nil if none found.
func (m *SendBooToUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendBooToUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendBooToUserResponseMultiError(errors)
	}

	return nil
}

// SendBooToUserResponseMultiError is an error wrapping multiple validation
// errors returned by SendBooToUserResponse.ValidateAll() if the designated
// constraints aren't met.
type SendBooToUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendBooToUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendBooToUserResponseMultiError) AllErrors() []error { return m }

// SendBooToUserResponseValidationError is the validation error returned by
// SendBooToUserResponse.Validate if the designated constraints aren't met.
type SendBooToUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendBooToUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendBooToUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendBooToUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendBooToUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendBooToUserResponseValidationError) ErrorName() string {
	return "SendBooToUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendBooToUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendBooToUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendBooToUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendBooToUserResponseValidationError{}

// Validate checks the field values on SelectAvatarAsUsedRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectAvatarAsUsedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectAvatarAsUsedRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectAvatarAsUsedRequestMultiError, or nil if none found.
func (m *SelectAvatarAsUsedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectAvatarAsUsedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AvatarObjectKey

	if len(errors) > 0 {
		return SelectAvatarAsUsedRequestMultiError(errors)
	}

	return nil
}

// SelectAvatarAsUsedRequestMultiError is an error wrapping multiple validation
// errors returned by SelectAvatarAsUsedRequest.ValidateAll() if the
// designated constraints aren't met.
type SelectAvatarAsUsedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectAvatarAsUsedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectAvatarAsUsedRequestMultiError) AllErrors() []error { return m }

// SelectAvatarAsUsedRequestValidationError is the validation error returned by
// SelectAvatarAsUsedRequest.Validate if the designated constraints aren't met.
type SelectAvatarAsUsedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectAvatarAsUsedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectAvatarAsUsedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectAvatarAsUsedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectAvatarAsUsedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectAvatarAsUsedRequestValidationError) ErrorName() string {
	return "SelectAvatarAsUsedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SelectAvatarAsUsedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectAvatarAsUsedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectAvatarAsUsedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectAvatarAsUsedRequestValidationError{}

// Validate checks the field values on SelectAvatarAsUsedResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectAvatarAsUsedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectAvatarAsUsedResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectAvatarAsUsedResponseMultiError, or nil if none found.
func (m *SelectAvatarAsUsedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectAvatarAsUsedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SelectAvatarAsUsedResponseMultiError(errors)
	}

	return nil
}

// SelectAvatarAsUsedResponseMultiError is an error wrapping multiple
// validation errors returned by SelectAvatarAsUsedResponse.ValidateAll() if
// the designated constraints aren't met.
type SelectAvatarAsUsedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectAvatarAsUsedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectAvatarAsUsedResponseMultiError) AllErrors() []error { return m }

// SelectAvatarAsUsedResponseValidationError is the validation error returned
// by SelectAvatarAsUsedResponse.Validate if the designated constraints aren't met.
type SelectAvatarAsUsedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectAvatarAsUsedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectAvatarAsUsedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectAvatarAsUsedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectAvatarAsUsedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectAvatarAsUsedResponseValidationError) ErrorName() string {
	return "SelectAvatarAsUsedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SelectAvatarAsUsedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectAvatarAsUsedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectAvatarAsUsedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectAvatarAsUsedResponseValidationError{}
