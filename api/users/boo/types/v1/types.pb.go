// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/users/boo/types/v1/types.proto

package api_users_boo_types_v1

import (
	v1 "boson/api/resource/types/v1"
	v11 "boson/api/users/info/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BooSendRecordStatus int32

const (
	BooSendRecordStatus_BOO_SEND_RECORD_STATUS_UNSPECIFIED BooSendRecordStatus = 0
	// 等待发送，这是因为 boo 在生成中，需要等待 boo 生成完毕后，再发送
	BooSendRecordStatus_BOO_SEND_RECORD_STATUS_PENDING BooSendRecordStatus = 1
	// 发送成功，boo 生成完毕，并且发送成功
	BooSendRecordStatus_BOO_SEND_RECORD_STATUS_SUCCESS BooSendRecordStatus = 2
	// 发送的鬼被抓住了
	BooSendRecordStatus_BOO_SEND_RECORD_STATUS_CAPTURED BooSendRecordStatus = 3
)

// Enum value maps for BooSendRecordStatus.
var (
	BooSendRecordStatus_name = map[int32]string{
		0: "BOO_SEND_RECORD_STATUS_UNSPECIFIED",
		1: "BOO_SEND_RECORD_STATUS_PENDING",
		2: "BOO_SEND_RECORD_STATUS_SUCCESS",
		3: "BOO_SEND_RECORD_STATUS_CAPTURED",
	}
	BooSendRecordStatus_value = map[string]int32{
		"BOO_SEND_RECORD_STATUS_UNSPECIFIED": 0,
		"BOO_SEND_RECORD_STATUS_PENDING":     1,
		"BOO_SEND_RECORD_STATUS_SUCCESS":     2,
		"BOO_SEND_RECORD_STATUS_CAPTURED":    3,
	}
)

func (x BooSendRecordStatus) Enum() *BooSendRecordStatus {
	p := new(BooSendRecordStatus)
	*p = x
	return p
}

func (x BooSendRecordStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooSendRecordStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[0].Descriptor()
}

func (BooSendRecordStatus) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[0]
}

func (x BooSendRecordStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooSendRecordStatus.Descriptor instead.
func (BooSendRecordStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{0}
}

type JobStatus int32

const (
	JobStatus_JOB_STATUS_UNSPECIFIED JobStatus = 0
	// 等待调度
	JobStatus_JOB_STATUS_PENDING JobStatus = 1
	// 运行中
	JobStatus_JOB_STATUS_RUNNING JobStatus = 2
	// 成功
	JobStatus_JOB_STATUS_SUCCESS JobStatus = 3
	// 失败
	JobStatus_JOB_STATUS_FAILED JobStatus = 4
)

// Enum value maps for JobStatus.
var (
	JobStatus_name = map[int32]string{
		0: "JOB_STATUS_UNSPECIFIED",
		1: "JOB_STATUS_PENDING",
		2: "JOB_STATUS_RUNNING",
		3: "JOB_STATUS_SUCCESS",
		4: "JOB_STATUS_FAILED",
	}
	JobStatus_value = map[string]int32{
		"JOB_STATUS_UNSPECIFIED": 0,
		"JOB_STATUS_PENDING":     1,
		"JOB_STATUS_RUNNING":     2,
		"JOB_STATUS_SUCCESS":     3,
		"JOB_STATUS_FAILED":      4,
	}
)

func (x JobStatus) Enum() *JobStatus {
	p := new(JobStatus)
	*p = x
	return p
}

func (x JobStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (JobStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[1].Descriptor()
}

func (JobStatus) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[1]
}

func (x JobStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use JobStatus.Descriptor instead.
func (JobStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{1}
}

type UserActionType int32

const (
	UserActionType_USER_ACTION_TYPE_UNSPECIFIED UserActionType = 0
	// 闹鬼了
	UserActionType_USER_ACTION_TYPE_BOO_SHOW UserActionType = 1
	// 开始抓鬼
	UserActionType_USER_ACTION_TYPE_BEGIN_CAPTURE UserActionType = 2
	// 离开抓鬼
	UserActionType_USER_ACTION_TYPE_LEAVE_CAPTURE UserActionType = 3
	// 抓鬼失败
	UserActionType_USER_ACTION_TYPE_CAPTURE_FAILED UserActionType = 4
	// 抓鬼成功
	UserActionType_USER_ACTION_TYPE_CAPTURE_SUCCESS UserActionType = 5
)

// Enum value maps for UserActionType.
var (
	UserActionType_name = map[int32]string{
		0: "USER_ACTION_TYPE_UNSPECIFIED",
		1: "USER_ACTION_TYPE_BOO_SHOW",
		2: "USER_ACTION_TYPE_BEGIN_CAPTURE",
		3: "USER_ACTION_TYPE_LEAVE_CAPTURE",
		4: "USER_ACTION_TYPE_CAPTURE_FAILED",
		5: "USER_ACTION_TYPE_CAPTURE_SUCCESS",
	}
	UserActionType_value = map[string]int32{
		"USER_ACTION_TYPE_UNSPECIFIED":     0,
		"USER_ACTION_TYPE_BOO_SHOW":        1,
		"USER_ACTION_TYPE_BEGIN_CAPTURE":   2,
		"USER_ACTION_TYPE_LEAVE_CAPTURE":   3,
		"USER_ACTION_TYPE_CAPTURE_FAILED":  4,
		"USER_ACTION_TYPE_CAPTURE_SUCCESS": 5,
	}
)

func (x UserActionType) Enum() *UserActionType {
	p := new(UserActionType)
	*p = x
	return p
}

func (x UserActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[2].Descriptor()
}

func (UserActionType) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[2]
}

func (x UserActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserActionType.Descriptor instead.
func (UserActionType) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{2}
}

// 闹鬼场景
type BooShowScene int32

const (
	BooShowScene_BOO_SCENE_UNSPECIFIED BooShowScene = 0
	// Feed 流
	BooShowScene_BOO_SCENE_FEED BooShowScene = 1
	// 详情页
	BooShowScene_BOO_SCENE_DETAIL_PAGE BooShowScene = 2
	// IM 聊天
	BooShowScene_BOO_SCENE_IM_CHAT BooShowScene = 3
	// 污染 feed
	BooShowScene_BOO_SCENE_POLLUTE_FEED BooShowScene = 4
)

// Enum value maps for BooShowScene.
var (
	BooShowScene_name = map[int32]string{
		0: "BOO_SCENE_UNSPECIFIED",
		1: "BOO_SCENE_FEED",
		2: "BOO_SCENE_DETAIL_PAGE",
		3: "BOO_SCENE_IM_CHAT",
		4: "BOO_SCENE_POLLUTE_FEED",
	}
	BooShowScene_value = map[string]int32{
		"BOO_SCENE_UNSPECIFIED":  0,
		"BOO_SCENE_FEED":         1,
		"BOO_SCENE_DETAIL_PAGE":  2,
		"BOO_SCENE_IM_CHAT":      3,
		"BOO_SCENE_POLLUTE_FEED": 4,
	}
)

func (x BooShowScene) Enum() *BooShowScene {
	p := new(BooShowScene)
	*p = x
	return p
}

func (x BooShowScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooShowScene) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[3].Descriptor()
}

func (BooShowScene) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[3]
}

func (x BooShowScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooShowScene.Descriptor instead.
func (BooShowScene) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{3}
}

// Boo 情绪种类
type BooEmotionType int32

const (
	BooEmotionType_BOO_EMOTION_TYPE_UNSPECIFIED BooEmotionType = 0
	// 生气
	BooEmotionType_BOO_EMOTION_TYPE_ANGRY BooEmotionType = 1
	// 害怕
	BooEmotionType_BOO_EMOTION_TYPE_SCARED BooEmotionType = 2
	// 快乐
	BooEmotionType_BOO_EMOTION_TYPE_HAPPY BooEmotionType = 4
	// 无聊
	BooEmotionType_BOO_EMOTION_TYPE_BORED BooEmotionType = 5
	// 有趣
	BooEmotionType_BOO_EMOTION_TYPE_FUNNY BooEmotionType = 8
	// 抓住消失了
	BooEmotionType_BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED BooEmotionType = 9
)

// Enum value maps for BooEmotionType.
var (
	BooEmotionType_name = map[int32]string{
		0: "BOO_EMOTION_TYPE_UNSPECIFIED",
		1: "BOO_EMOTION_TYPE_ANGRY",
		2: "BOO_EMOTION_TYPE_SCARED",
		4: "BOO_EMOTION_TYPE_HAPPY",
		5: "BOO_EMOTION_TYPE_BORED",
		8: "BOO_EMOTION_TYPE_FUNNY",
		9: "BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED",
	}
	BooEmotionType_value = map[string]int32{
		"BOO_EMOTION_TYPE_UNSPECIFIED":          0,
		"BOO_EMOTION_TYPE_ANGRY":                1,
		"BOO_EMOTION_TYPE_SCARED":               2,
		"BOO_EMOTION_TYPE_HAPPY":                4,
		"BOO_EMOTION_TYPE_BORED":                5,
		"BOO_EMOTION_TYPE_FUNNY":                8,
		"BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED": 9,
	}
)

func (x BooEmotionType) Enum() *BooEmotionType {
	p := new(BooEmotionType)
	*p = x
	return p
}

func (x BooEmotionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooEmotionType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[4].Descriptor()
}

func (BooEmotionType) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[4]
}

func (x BooEmotionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooEmotionType.Descriptor instead.
func (BooEmotionType) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{4}
}

type Status int32

const (
	Status_STATUS_UNSPECIFIED Status = 0
	// 生成中
	Status_STATUS_GENERATING Status = 1
	// 生成成功
	Status_STATUS_GENERATED Status = 2
	// 生成失败
	Status_STATUS_FAILED Status = 3
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_GENERATING",
		2: "STATUS_GENERATED",
		3: "STATUS_FAILED",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_GENERATING":  1,
		"STATUS_GENERATED":   2,
		"STATUS_FAILED":      3,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[5].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[5]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{5}
}

type PickedStatus int32

const (
	PickedStatus_PICKED_STATUS_UNSPECIFIED PickedStatus = 0
	// 未使用
	PickedStatus_PICKED_STATUS_UNUSED PickedStatus = 1
	// 已使用
	PickedStatus_PICKED_STATUS_PICKED PickedStatus = 2
)

// Enum value maps for PickedStatus.
var (
	PickedStatus_name = map[int32]string{
		0: "PICKED_STATUS_UNSPECIFIED",
		1: "PICKED_STATUS_UNUSED",
		2: "PICKED_STATUS_PICKED",
	}
	PickedStatus_value = map[string]int32{
		"PICKED_STATUS_UNSPECIFIED": 0,
		"PICKED_STATUS_UNUSED":      1,
		"PICKED_STATUS_PICKED":      2,
	}
)

func (x PickedStatus) Enum() *PickedStatus {
	p := new(PickedStatus)
	*p = x
	return p
}

func (x PickedStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PickedStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[6].Descriptor()
}

func (PickedStatus) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[6]
}

func (x PickedStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PickedStatus.Descriptor instead.
func (PickedStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{6}
}

type BooType int32

const (
	BooType_BOO_TYPE_UNSPECIFIED BooType = 0
	// 用户创建的 boo
	BooType_BOO_TYPE_USER_CREATED BooType = 1
	// HAUNT BOO
	BooType_BOO_TYPE_HAUNT BooType = 2
)

// Enum value maps for BooType.
var (
	BooType_name = map[int32]string{
		0: "BOO_TYPE_UNSPECIFIED",
		1: "BOO_TYPE_USER_CREATED",
		2: "BOO_TYPE_HAUNT",
	}
	BooType_value = map[string]int32{
		"BOO_TYPE_UNSPECIFIED":  0,
		"BOO_TYPE_USER_CREATED": 1,
		"BOO_TYPE_HAUNT":        2,
	}
)

func (x BooType) Enum() *BooType {
	p := new(BooType)
	*p = x
	return p
}

func (x BooType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[7].Descriptor()
}

func (BooType) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[7]
}

func (x BooType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooType.Descriptor instead.
func (BooType) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{7}
}

type BooShowInfoUpdatedTopic int32

const (
	BooShowInfoUpdatedTopic_BOO_SHOW_INFO_UPDATED_TOPIC_UNSPECIFIED BooShowInfoUpdatedTopic = 0
	// 用于收到了新的 Boo 后，通知客户端更新 Boo 展示信息
	BooShowInfoUpdatedTopic_BOO_SHOW_INFO_UPDATED_BY_RECEIVED_NEW_BOO BooShowInfoUpdatedTopic = 1
)

// Enum value maps for BooShowInfoUpdatedTopic.
var (
	BooShowInfoUpdatedTopic_name = map[int32]string{
		0: "BOO_SHOW_INFO_UPDATED_TOPIC_UNSPECIFIED",
		1: "BOO_SHOW_INFO_UPDATED_BY_RECEIVED_NEW_BOO",
	}
	BooShowInfoUpdatedTopic_value = map[string]int32{
		"BOO_SHOW_INFO_UPDATED_TOPIC_UNSPECIFIED":   0,
		"BOO_SHOW_INFO_UPDATED_BY_RECEIVED_NEW_BOO": 1,
	}
)

func (x BooShowInfoUpdatedTopic) Enum() *BooShowInfoUpdatedTopic {
	p := new(BooShowInfoUpdatedTopic)
	*p = x
	return p
}

func (x BooShowInfoUpdatedTopic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooShowInfoUpdatedTopic) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[8].Descriptor()
}

func (BooShowInfoUpdatedTopic) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[8]
}

func (x BooShowInfoUpdatedTopic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooShowInfoUpdatedTopic.Descriptor instead.
func (BooShowInfoUpdatedTopic) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{8}
}

type AvatarCreatedInfoUpdatedTopic int32

const (
	AvatarCreatedInfoUpdatedTopic_AVATAR_CREATED_INFO_UPDATED_TOPIC_UNSPECIFIED AvatarCreatedInfoUpdatedTopic = 0
	// 头像生成完毕了
	AvatarCreatedInfoUpdatedTopic_AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATED AvatarCreatedInfoUpdatedTopic = 1
	// 用户选择了一个头像作为已使用
	AvatarCreatedInfoUpdatedTopic_AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_SELECTED_AS_USED AvatarCreatedInfoUpdatedTopic = 2
	// 用户新建了一个头像生成任务
	AvatarCreatedInfoUpdatedTopic_AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATE_JOB_CREATED AvatarCreatedInfoUpdatedTopic = 3
)

// Enum value maps for AvatarCreatedInfoUpdatedTopic.
var (
	AvatarCreatedInfoUpdatedTopic_name = map[int32]string{
		0: "AVATAR_CREATED_INFO_UPDATED_TOPIC_UNSPECIFIED",
		1: "AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATED",
		2: "AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_SELECTED_AS_USED",
		3: "AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATE_JOB_CREATED",
	}
	AvatarCreatedInfoUpdatedTopic_value = map[string]int32{
		"AVATAR_CREATED_INFO_UPDATED_TOPIC_UNSPECIFIED":              0,
		"AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATED":            1,
		"AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_SELECTED_AS_USED":     2,
		"AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATE_JOB_CREATED": 3,
	}
)

func (x AvatarCreatedInfoUpdatedTopic) Enum() *AvatarCreatedInfoUpdatedTopic {
	p := new(AvatarCreatedInfoUpdatedTopic)
	*p = x
	return p
}

func (x AvatarCreatedInfoUpdatedTopic) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AvatarCreatedInfoUpdatedTopic) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[9].Descriptor()
}

func (AvatarCreatedInfoUpdatedTopic) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[9]
}

func (x AvatarCreatedInfoUpdatedTopic) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AvatarCreatedInfoUpdatedTopic.Descriptor instead.
func (AvatarCreatedInfoUpdatedTopic) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{9}
}

type BooAnimation_Position int32

const (
	// 上下左右中间，坐上右上左下右下
	BooAnimation_POSITION_UNSPECIFIED  BooAnimation_Position = 0
	BooAnimation_POSITION_TOP          BooAnimation_Position = 1
	BooAnimation_POSITION_BOTTOM       BooAnimation_Position = 2
	BooAnimation_POSITION_LEFT         BooAnimation_Position = 3
	BooAnimation_POSITION_RIGHT        BooAnimation_Position = 4
	BooAnimation_POSITION_CENTER       BooAnimation_Position = 5
	BooAnimation_POSITION_TOP_LEFT     BooAnimation_Position = 6
	BooAnimation_POSITION_TOP_RIGHT    BooAnimation_Position = 7
	BooAnimation_POSITION_BOTTOM_LEFT  BooAnimation_Position = 8
	BooAnimation_POSITION_BOTTOM_RIGHT BooAnimation_Position = 9
)

// Enum value maps for BooAnimation_Position.
var (
	BooAnimation_Position_name = map[int32]string{
		0: "POSITION_UNSPECIFIED",
		1: "POSITION_TOP",
		2: "POSITION_BOTTOM",
		3: "POSITION_LEFT",
		4: "POSITION_RIGHT",
		5: "POSITION_CENTER",
		6: "POSITION_TOP_LEFT",
		7: "POSITION_TOP_RIGHT",
		8: "POSITION_BOTTOM_LEFT",
		9: "POSITION_BOTTOM_RIGHT",
	}
	BooAnimation_Position_value = map[string]int32{
		"POSITION_UNSPECIFIED":  0,
		"POSITION_TOP":          1,
		"POSITION_BOTTOM":       2,
		"POSITION_LEFT":         3,
		"POSITION_RIGHT":        4,
		"POSITION_CENTER":       5,
		"POSITION_TOP_LEFT":     6,
		"POSITION_TOP_RIGHT":    7,
		"POSITION_BOTTOM_LEFT":  8,
		"POSITION_BOTTOM_RIGHT": 9,
	}
)

func (x BooAnimation_Position) Enum() *BooAnimation_Position {
	p := new(BooAnimation_Position)
	*p = x
	return p
}

func (x BooAnimation_Position) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BooAnimation_Position) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_boo_types_v1_types_proto_enumTypes[10].Descriptor()
}

func (BooAnimation_Position) Type() protoreflect.EnumType {
	return &file_api_users_boo_types_v1_types_proto_enumTypes[10]
}

func (x BooAnimation_Position) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BooAnimation_Position.Descriptor instead.
func (BooAnimation_Position) EnumDescriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{4, 0}
}

type Avatar struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Resource       *v1.Resource           `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	SelectedAsUsed bool                   `protobuf:"varint,3,opt,name=selected_as_used,json=selectedAsUsed,proto3" json:"selected_as_used,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Avatar) Reset() {
	*x = Avatar{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Avatar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Avatar) ProtoMessage() {}

func (x *Avatar) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Avatar.ProtoReflect.Descriptor instead.
func (*Avatar) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *Avatar) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Avatar) GetResource() *v1.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *Avatar) GetSelectedAsUsed() bool {
	if x != nil {
		return x.SelectedAsUsed
	}
	return false
}

type UseAvatarCreatedInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 最多尝试几次头像生成
	MaxCreatedJobsCount uint32 `protobuf:"varint,1,opt,name=max_created_jobs_count,json=maxCreatedJobsCount,proto3" json:"max_created_jobs_count,omitempty"`
	// 剩余几次生成头像的机会
	RemainCreatedJobsCount uint32 `protobuf:"varint,2,opt,name=remain_created_jobs_count,json=remainCreatedJobsCount,proto3" json:"remain_created_jobs_count,omitempty"`
	// 给哪些人发过 Boo
	SentToUserIds []string `protobuf:"bytes,3,rep,name=sent_to_user_ids,json=sentToUserIds,proto3" json:"sent_to_user_ids,omitempty"`
	// 用户目前选择的头像
	SelectedAvatarResource *v1.Resource `protobuf:"bytes,4,opt,name=selected_avatar_resource,json=selectedAvatarResource,proto3" json:"selected_avatar_resource,omitempty"`
	// 历史里所有的头像
	AllGeneratedAvatarResources []*v1.Resource `protobuf:"bytes,5,rep,name=all_generated_avatar_resources,json=allGeneratedAvatarResources,proto3" json:"all_generated_avatar_resources,omitempty"`
	// 目前生效的 job，可能是 pending || success
	CurrentActiveJob    *GenerateAvatarJob `protobuf:"bytes,6,opt,name=current_active_job,json=currentActiveJob,proto3" json:"current_active_job,omitempty"`
	AllGeneratedAvatars []*Avatar          `protobuf:"bytes,7,rep,name=all_generated_avatars,json=allGeneratedAvatars,proto3" json:"all_generated_avatars,omitempty"`
	SelectedAvatar      *Avatar            `protobuf:"bytes,8,opt,name=selected_avatar,json=selectedAvatar,proto3" json:"selected_avatar,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *UseAvatarCreatedInfo) Reset() {
	*x = UseAvatarCreatedInfo{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UseAvatarCreatedInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UseAvatarCreatedInfo) ProtoMessage() {}

func (x *UseAvatarCreatedInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UseAvatarCreatedInfo.ProtoReflect.Descriptor instead.
func (*UseAvatarCreatedInfo) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *UseAvatarCreatedInfo) GetMaxCreatedJobsCount() uint32 {
	if x != nil {
		return x.MaxCreatedJobsCount
	}
	return 0
}

func (x *UseAvatarCreatedInfo) GetRemainCreatedJobsCount() uint32 {
	if x != nil {
		return x.RemainCreatedJobsCount
	}
	return 0
}

func (x *UseAvatarCreatedInfo) GetSentToUserIds() []string {
	if x != nil {
		return x.SentToUserIds
	}
	return nil
}

func (x *UseAvatarCreatedInfo) GetSelectedAvatarResource() *v1.Resource {
	if x != nil {
		return x.SelectedAvatarResource
	}
	return nil
}

func (x *UseAvatarCreatedInfo) GetAllGeneratedAvatarResources() []*v1.Resource {
	if x != nil {
		return x.AllGeneratedAvatarResources
	}
	return nil
}

func (x *UseAvatarCreatedInfo) GetCurrentActiveJob() *GenerateAvatarJob {
	if x != nil {
		return x.CurrentActiveJob
	}
	return nil
}

func (x *UseAvatarCreatedInfo) GetAllGeneratedAvatars() []*Avatar {
	if x != nil {
		return x.AllGeneratedAvatars
	}
	return nil
}

func (x *UseAvatarCreatedInfo) GetSelectedAvatar() *Avatar {
	if x != nil {
		return x.SelectedAvatar
	}
	return nil
}

type GenerateAvatarJob struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	Id                        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CreatorId                 string                 `protobuf:"bytes,2,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	JobStatus                 JobStatus              `protobuf:"varint,3,opt,name=job_status,json=jobStatus,proto3,enum=api.users.boo.types.v1.JobStatus" json:"job_status,omitempty"`
	UserUploadedPhotoResource *v1.Resource           `protobuf:"bytes,9,opt,name=user_uploaded_photo_resource,json=userUploadedPhotoResource,proto3" json:"user_uploaded_photo_resource,omitempty"`
	// 生成出来的头像，当且仅当 status = Success 后才会出现
	GeneratedAvatarResources []*v1.Resource `protobuf:"bytes,4,rep,name=generated_avatar_resources,json=generatedAvatarResources,proto3" json:"generated_avatar_resources,omitempty"`
	// 预估的剩余秒数
	EstimatedRemainingSeconds uint32 `protobuf:"varint,5,opt,name=estimated_remaining_seconds,json=estimatedRemainingSeconds,proto3" json:"estimated_remaining_seconds,omitempty"`
	// 已经执行了多久了，秒数
	ExecutedSeconds      uint32 `protobuf:"varint,6,opt,name=executed_seconds,json=executedSeconds,proto3" json:"executed_seconds,omitempty"`
	UpdatedAtInUnixstamp uint32 `protobuf:"varint,7,opt,name=updated_at_in_unixstamp,json=updatedAtInUnixstamp,proto3" json:"updated_at_in_unixstamp,omitempty"`
	CreatedAtInUnixstamp uint32 `protobuf:"varint,8,opt,name=created_at_in_unixstamp,json=createdAtInUnixstamp,proto3" json:"created_at_in_unixstamp,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GenerateAvatarJob) Reset() {
	*x = GenerateAvatarJob{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateAvatarJob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateAvatarJob) ProtoMessage() {}

func (x *GenerateAvatarJob) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateAvatarJob.ProtoReflect.Descriptor instead.
func (*GenerateAvatarJob) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{2}
}

func (x *GenerateAvatarJob) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GenerateAvatarJob) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *GenerateAvatarJob) GetJobStatus() JobStatus {
	if x != nil {
		return x.JobStatus
	}
	return JobStatus_JOB_STATUS_UNSPECIFIED
}

func (x *GenerateAvatarJob) GetUserUploadedPhotoResource() *v1.Resource {
	if x != nil {
		return x.UserUploadedPhotoResource
	}
	return nil
}

func (x *GenerateAvatarJob) GetGeneratedAvatarResources() []*v1.Resource {
	if x != nil {
		return x.GeneratedAvatarResources
	}
	return nil
}

func (x *GenerateAvatarJob) GetEstimatedRemainingSeconds() uint32 {
	if x != nil {
		return x.EstimatedRemainingSeconds
	}
	return 0
}

func (x *GenerateAvatarJob) GetExecutedSeconds() uint32 {
	if x != nil {
		return x.ExecutedSeconds
	}
	return 0
}

func (x *GenerateAvatarJob) GetUpdatedAtInUnixstamp() uint32 {
	if x != nil {
		return x.UpdatedAtInUnixstamp
	}
	return 0
}

func (x *GenerateAvatarJob) GetCreatedAtInUnixstamp() uint32 {
	if x != nil {
		return x.CreatedAtInUnixstamp
	}
	return 0
}

// Boo 信息
type Boo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Creator        *v11.UserInfoSummary   `protobuf:"bytes,2,opt,name=creator,proto3" json:"creator,omitempty"`
	Animations     []*BooAnimation        `protobuf:"bytes,3,rep,name=animations,proto3" json:"animations,omitempty"`
	AvatarImageUrl string                 `protobuf:"bytes,4,opt,name=avatar_image_url,json=avatarImageUrl,proto3" json:"avatar_image_url,omitempty"`
	Status         Status                 `protobuf:"varint,5,opt,name=status,proto3,enum=api.users.boo.types.v1.Status" json:"status,omitempty"`
	PickedStatus   PickedStatus           `protobuf:"varint,6,opt,name=picked_status,json=pickedStatus,proto3,enum=api.users.boo.types.v1.PickedStatus" json:"picked_status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Boo) Reset() {
	*x = Boo{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Boo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Boo) ProtoMessage() {}

func (x *Boo) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Boo.ProtoReflect.Descriptor instead.
func (*Boo) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{3}
}

func (x *Boo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Boo) GetCreator() *v11.UserInfoSummary {
	if x != nil {
		return x.Creator
	}
	return nil
}

func (x *Boo) GetAnimations() []*BooAnimation {
	if x != nil {
		return x.Animations
	}
	return nil
}

func (x *Boo) GetAvatarImageUrl() string {
	if x != nil {
		return x.AvatarImageUrl
	}
	return ""
}

func (x *Boo) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *Boo) GetPickedStatus() PickedStatus {
	if x != nil {
		return x.PickedStatus
	}
	return PickedStatus_PICKED_STATUS_UNSPECIFIED
}

// boo 的动画，总是伴着情绪及播放素材等其他信息
type BooAnimation struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Id           string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	EmotionType  BooEmotionType         `protobuf:"varint,2,opt,name=emotion_type,json=emotionType,proto3,enum=api.users.boo.types.v1.BooEmotionType" json:"emotion_type,omitempty"`
	VideoPlayUrl string                 `protobuf:"bytes,3,opt,name=video_play_url,json=videoPlayUrl,proto3" json:"video_play_url,omitempty"`
	// 是否可以作为开通动画
	CanBeGreeting bool `protobuf:"varint,4,opt,name=can_be_greeting,json=canBeGreeting,proto3" json:"can_be_greeting,omitempty"`
	// 是否可以作为正在被抓的动画 -> scared
	CanBeCapturing bool `protobuf:"varint,5,opt,name=can_be_capturing,json=canBeCapturing,proto3" json:"can_be_capturing,omitempty"`
	// 是否可以作为抓鬼成功动画 -> dissmiss
	CanBeCaptureSuccess bool `protobuf:"varint,6,opt,name=can_be_capture_success,json=canBeCaptureSuccess,proto3" json:"can_be_capture_success,omitempty"`
	// 是否可以作为抓鬼失败动画
	CanBeCaptureFailed bool                             `protobuf:"varint,7,opt,name=can_be_capture_failed,json=canBeCaptureFailed,proto3" json:"can_be_capture_failed,omitempty"`
	RecommendPositions []BooAnimation_Position          `protobuf:"varint,10,rep,packed,name=recommend_positions,json=recommendPositions,proto3,enum=api.users.boo.types.v1.BooAnimation_Position" json:"recommend_positions,omitempty"`
	SceneAndDialogues  []*BooAnimation_SceneAndDialogue `protobuf:"bytes,11,rep,name=scene_and_dialogues,json=sceneAndDialogues,proto3" json:"scene_and_dialogues,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *BooAnimation) Reset() {
	*x = BooAnimation{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooAnimation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooAnimation) ProtoMessage() {}

func (x *BooAnimation) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooAnimation.ProtoReflect.Descriptor instead.
func (*BooAnimation) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{4}
}

func (x *BooAnimation) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BooAnimation) GetEmotionType() BooEmotionType {
	if x != nil {
		return x.EmotionType
	}
	return BooEmotionType_BOO_EMOTION_TYPE_UNSPECIFIED
}

func (x *BooAnimation) GetVideoPlayUrl() string {
	if x != nil {
		return x.VideoPlayUrl
	}
	return ""
}

func (x *BooAnimation) GetCanBeGreeting() bool {
	if x != nil {
		return x.CanBeGreeting
	}
	return false
}

func (x *BooAnimation) GetCanBeCapturing() bool {
	if x != nil {
		return x.CanBeCapturing
	}
	return false
}

func (x *BooAnimation) GetCanBeCaptureSuccess() bool {
	if x != nil {
		return x.CanBeCaptureSuccess
	}
	return false
}

func (x *BooAnimation) GetCanBeCaptureFailed() bool {
	if x != nil {
		return x.CanBeCaptureFailed
	}
	return false
}

func (x *BooAnimation) GetRecommendPositions() []BooAnimation_Position {
	if x != nil {
		return x.RecommendPositions
	}
	return nil
}

func (x *BooAnimation) GetSceneAndDialogues() []*BooAnimation_SceneAndDialogue {
	if x != nil {
		return x.SceneAndDialogues
	}
	return nil
}

// Boo 的渲染信息
type BooShowInfo struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	ShowConfig    *BooShowInfo_ShowConfig   `protobuf:"bytes,1,opt,name=show_config,json=showConfig,proto3" json:"show_config,omitempty"`
	ReceivedBoos  []*BooShowInfo_RecivedBoo `protobuf:"bytes,3,rep,name=received_boos,json=receivedBoos,proto3" json:"received_boos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BooShowInfo) Reset() {
	*x = BooShowInfo{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooShowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooShowInfo) ProtoMessage() {}

func (x *BooShowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooShowInfo.ProtoReflect.Descriptor instead.
func (*BooShowInfo) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{5}
}

func (x *BooShowInfo) GetShowConfig() *BooShowInfo_ShowConfig {
	if x != nil {
		return x.ShowConfig
	}
	return nil
}

func (x *BooShowInfo) GetReceivedBoos() []*BooShowInfo_RecivedBoo {
	if x != nil {
		return x.ReceivedBoos
	}
	return nil
}

type BooShowInfoUpdatedMessage struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	Topic             BooShowInfoUpdatedTopic `protobuf:"varint,1,opt,name=topic,proto3,enum=api.users.boo.types.v1.BooShowInfoUpdatedTopic" json:"topic,omitempty"`
	LatestBooShowInfo *BooShowInfo            `protobuf:"bytes,2,opt,name=latest_boo_show_info,json=latestBooShowInfo,proto3" json:"latest_boo_show_info,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BooShowInfoUpdatedMessage) Reset() {
	*x = BooShowInfoUpdatedMessage{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooShowInfoUpdatedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooShowInfoUpdatedMessage) ProtoMessage() {}

func (x *BooShowInfoUpdatedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooShowInfoUpdatedMessage.ProtoReflect.Descriptor instead.
func (*BooShowInfoUpdatedMessage) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{6}
}

func (x *BooShowInfoUpdatedMessage) GetTopic() BooShowInfoUpdatedTopic {
	if x != nil {
		return x.Topic
	}
	return BooShowInfoUpdatedTopic_BOO_SHOW_INFO_UPDATED_TOPIC_UNSPECIFIED
}

func (x *BooShowInfoUpdatedMessage) GetLatestBooShowInfo() *BooShowInfo {
	if x != nil {
		return x.LatestBooShowInfo
	}
	return nil
}

type AvatarCreatedInfoUpdatedMessage struct {
	state                protoimpl.MessageState        `protogen:"open.v1"`
	Topic                AvatarCreatedInfoUpdatedTopic `protobuf:"varint,1,opt,name=topic,proto3,enum=api.users.boo.types.v1.AvatarCreatedInfoUpdatedTopic" json:"topic,omitempty"`
	UseAvatarCreatedInfo *UseAvatarCreatedInfo         `protobuf:"bytes,2,opt,name=use_avatar_created_info,json=useAvatarCreatedInfo,proto3" json:"use_avatar_created_info,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *AvatarCreatedInfoUpdatedMessage) Reset() {
	*x = AvatarCreatedInfoUpdatedMessage{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarCreatedInfoUpdatedMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarCreatedInfoUpdatedMessage) ProtoMessage() {}

func (x *AvatarCreatedInfoUpdatedMessage) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarCreatedInfoUpdatedMessage.ProtoReflect.Descriptor instead.
func (*AvatarCreatedInfoUpdatedMessage) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{7}
}

func (x *AvatarCreatedInfoUpdatedMessage) GetTopic() AvatarCreatedInfoUpdatedTopic {
	if x != nil {
		return x.Topic
	}
	return AvatarCreatedInfoUpdatedTopic_AVATAR_CREATED_INFO_UPDATED_TOPIC_UNSPECIFIED
}

func (x *AvatarCreatedInfoUpdatedMessage) GetUseAvatarCreatedInfo() *UseAvatarCreatedInfo {
	if x != nil {
		return x.UseAvatarCreatedInfo
	}
	return nil
}

// 支持在哪些场景下渲染，并且有哪些台词
type BooAnimation_SceneAndDialogue struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Scene         BooShowScene           `protobuf:"varint,1,opt,name=scene,proto3,enum=api.users.boo.types.v1.BooShowScene" json:"scene,omitempty"`
	Dialogues     []string               `protobuf:"bytes,2,rep,name=dialogues,proto3" json:"dialogues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BooAnimation_SceneAndDialogue) Reset() {
	*x = BooAnimation_SceneAndDialogue{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooAnimation_SceneAndDialogue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooAnimation_SceneAndDialogue) ProtoMessage() {}

func (x *BooAnimation_SceneAndDialogue) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooAnimation_SceneAndDialogue.ProtoReflect.Descriptor instead.
func (*BooAnimation_SceneAndDialogue) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{4, 0}
}

func (x *BooAnimation_SceneAndDialogue) GetScene() BooShowScene {
	if x != nil {
		return x.Scene
	}
	return BooShowScene_BOO_SCENE_UNSPECIFIED
}

func (x *BooAnimation_SceneAndDialogue) GetDialogues() []string {
	if x != nil {
		return x.Dialogues
	}
	return nil
}

type BooShowInfo_ShowConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 时间间隔，单位 s
	EveryShowIntervalSeconds uint32 `protobuf:"varint,1,opt,name=every_show_interval_seconds,json=everyShowIntervalSeconds,proto3" json:"every_show_interval_seconds,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *BooShowInfo_ShowConfig) Reset() {
	*x = BooShowInfo_ShowConfig{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooShowInfo_ShowConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooShowInfo_ShowConfig) ProtoMessage() {}

func (x *BooShowInfo_ShowConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooShowInfo_ShowConfig.ProtoReflect.Descriptor instead.
func (*BooShowInfo_ShowConfig) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{5, 0}
}

func (x *BooShowInfo_ShowConfig) GetEveryShowIntervalSeconds() uint32 {
	if x != nil {
		return x.EveryShowIntervalSeconds
	}
	return 0
}

// 用户收到的所有鬼
type BooShowInfo_RecivedBoo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Boo   *Boo                   `protobuf:"bytes,1,opt,name=boo,proto3" json:"boo,omitempty"`
	// 剩余展示次数，全部展示完毕后，此对象会从数组内被移除
	// 目前，每次收到新的 Boo 后，默认都是 10次
	RemainingShowCount uint32 `protobuf:"varint,2,opt,name=remaining_show_count,json=remainingShowCount,proto3" json:"remaining_show_count,omitempty"`
	// 历史上的展示记录
	ShowRecords []*BooShowInfo_RecivedBoo_ShowRecord `protobuf:"bytes,3,rep,name=show_records,json=showRecords,proto3" json:"show_records,omitempty"`
	// 有效截止于，如果超过此时间，此元素也会从数组内被移除
	ValidUntilInUnixstamp uint32 `protobuf:"varint,4,opt,name=valid_until_in_unixstamp,json=validUntilInUnixstamp,proto3" json:"valid_until_in_unixstamp,omitempty"`
	// 这个鬼可出现的场景
	ValidScenes   []BooShowScene `protobuf:"varint,5,rep,packed,name=valid_scenes,json=validScenes,proto3,enum=api.users.boo.types.v1.BooShowScene" json:"valid_scenes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BooShowInfo_RecivedBoo) Reset() {
	*x = BooShowInfo_RecivedBoo{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooShowInfo_RecivedBoo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooShowInfo_RecivedBoo) ProtoMessage() {}

func (x *BooShowInfo_RecivedBoo) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooShowInfo_RecivedBoo.ProtoReflect.Descriptor instead.
func (*BooShowInfo_RecivedBoo) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{5, 1}
}

func (x *BooShowInfo_RecivedBoo) GetBoo() *Boo {
	if x != nil {
		return x.Boo
	}
	return nil
}

func (x *BooShowInfo_RecivedBoo) GetRemainingShowCount() uint32 {
	if x != nil {
		return x.RemainingShowCount
	}
	return 0
}

func (x *BooShowInfo_RecivedBoo) GetShowRecords() []*BooShowInfo_RecivedBoo_ShowRecord {
	if x != nil {
		return x.ShowRecords
	}
	return nil
}

func (x *BooShowInfo_RecivedBoo) GetValidUntilInUnixstamp() uint32 {
	if x != nil {
		return x.ValidUntilInUnixstamp
	}
	return 0
}

func (x *BooShowInfo_RecivedBoo) GetValidScenes() []BooShowScene {
	if x != nil {
		return x.ValidScenes
	}
	return nil
}

type BooShowInfo_RecivedBoo_ShowRecord struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Scene             BooShowScene           `protobuf:"varint,1,opt,name=scene,proto3,enum=api.users.boo.types.v1.BooShowScene" json:"scene,omitempty"`
	EmotionType       BooEmotionType         `protobuf:"varint,2,opt,name=emotion_type,json=emotionType,proto3,enum=api.users.boo.types.v1.BooEmotionType" json:"emotion_type,omitempty"`
	ShowAtInUnixstamp uint32                 `protobuf:"varint,3,opt,name=show_at_in_unixstamp,json=showAtInUnixstamp,proto3" json:"show_at_in_unixstamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *BooShowInfo_RecivedBoo_ShowRecord) Reset() {
	*x = BooShowInfo_RecivedBoo_ShowRecord{}
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooShowInfo_RecivedBoo_ShowRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooShowInfo_RecivedBoo_ShowRecord) ProtoMessage() {}

func (x *BooShowInfo_RecivedBoo_ShowRecord) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_boo_types_v1_types_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooShowInfo_RecivedBoo_ShowRecord.ProtoReflect.Descriptor instead.
func (*BooShowInfo_RecivedBoo_ShowRecord) Descriptor() ([]byte, []int) {
	return file_api_users_boo_types_v1_types_proto_rawDescGZIP(), []int{5, 1, 0}
}

func (x *BooShowInfo_RecivedBoo_ShowRecord) GetScene() BooShowScene {
	if x != nil {
		return x.Scene
	}
	return BooShowScene_BOO_SCENE_UNSPECIFIED
}

func (x *BooShowInfo_RecivedBoo_ShowRecord) GetEmotionType() BooEmotionType {
	if x != nil {
		return x.EmotionType
	}
	return BooEmotionType_BOO_EMOTION_TYPE_UNSPECIFIED
}

func (x *BooShowInfo_RecivedBoo_ShowRecord) GetShowAtInUnixstamp() uint32 {
	if x != nil {
		return x.ShowAtInUnixstamp
	}
	return 0
}

var File_api_users_boo_types_v1_types_proto protoreflect.FileDescriptor

const file_api_users_boo_types_v1_types_proto_rawDesc = "" +
	"\n" +
	"\"api/users/boo/types/v1/types.proto\x12\x16api.users.boo.types.v1\x1a#api/users/info/types/v1/types.proto\x1a!api/resource/types/v1/types.proto\"\x7f\n" +
	"\x06Avatar\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12;\n" +
	"\bresource\x18\x02 \x01(\v2\x1f.api.resource.types.v1.ResourceR\bresource\x12(\n" +
	"\x10selected_as_used\x18\x03 \x01(\bR\x0eselectedAsUsed\"\xe6\x04\n" +
	"\x14UseAvatarCreatedInfo\x123\n" +
	"\x16max_created_jobs_count\x18\x01 \x01(\rR\x13maxCreatedJobsCount\x129\n" +
	"\x19remain_created_jobs_count\x18\x02 \x01(\rR\x16remainCreatedJobsCount\x12'\n" +
	"\x10sent_to_user_ids\x18\x03 \x03(\tR\rsentToUserIds\x12Y\n" +
	"\x18selected_avatar_resource\x18\x04 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x16selectedAvatarResource\x12d\n" +
	"\x1eall_generated_avatar_resources\x18\x05 \x03(\v2\x1f.api.resource.types.v1.ResourceR\x1ballGeneratedAvatarResources\x12W\n" +
	"\x12current_active_job\x18\x06 \x01(\v2).api.users.boo.types.v1.GenerateAvatarJobR\x10currentActiveJob\x12R\n" +
	"\x15all_generated_avatars\x18\a \x03(\v2\x1e.api.users.boo.types.v1.AvatarR\x13allGeneratedAvatars\x12G\n" +
	"\x0fselected_avatar\x18\b \x01(\v2\x1e.api.users.boo.types.v1.AvatarR\x0eselectedAvatar\"\x9e\x04\n" +
	"\x11GenerateAvatarJob\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1d\n" +
	"\n" +
	"creator_id\x18\x02 \x01(\tR\tcreatorId\x12@\n" +
	"\n" +
	"job_status\x18\x03 \x01(\x0e2!.api.users.boo.types.v1.JobStatusR\tjobStatus\x12`\n" +
	"\x1cuser_uploaded_photo_resource\x18\t \x01(\v2\x1f.api.resource.types.v1.ResourceR\x19userUploadedPhotoResource\x12]\n" +
	"\x1agenerated_avatar_resources\x18\x04 \x03(\v2\x1f.api.resource.types.v1.ResourceR\x18generatedAvatarResources\x12>\n" +
	"\x1bestimated_remaining_seconds\x18\x05 \x01(\rR\x19estimatedRemainingSeconds\x12)\n" +
	"\x10executed_seconds\x18\x06 \x01(\rR\x0fexecutedSeconds\x125\n" +
	"\x17updated_at_in_unixstamp\x18\a \x01(\rR\x14updatedAtInUnixstamp\x125\n" +
	"\x17created_at_in_unixstamp\x18\b \x01(\rR\x14createdAtInUnixstamp\"\xcc\x02\n" +
	"\x03Boo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12B\n" +
	"\acreator\x18\x02 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\acreator\x12D\n" +
	"\n" +
	"animations\x18\x03 \x03(\v2$.api.users.boo.types.v1.BooAnimationR\n" +
	"animations\x12(\n" +
	"\x10avatar_image_url\x18\x04 \x01(\tR\x0eavatarImageUrl\x126\n" +
	"\x06status\x18\x05 \x01(\x0e2\x1e.api.users.boo.types.v1.StatusR\x06status\x12I\n" +
	"\rpicked_status\x18\x06 \x01(\x0e2$.api.users.boo.types.v1.PickedStatusR\fpickedStatus\"\xec\x06\n" +
	"\fBooAnimation\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12I\n" +
	"\femotion_type\x18\x02 \x01(\x0e2&.api.users.boo.types.v1.BooEmotionTypeR\vemotionType\x12$\n" +
	"\x0evideo_play_url\x18\x03 \x01(\tR\fvideoPlayUrl\x12&\n" +
	"\x0fcan_be_greeting\x18\x04 \x01(\bR\rcanBeGreeting\x12(\n" +
	"\x10can_be_capturing\x18\x05 \x01(\bR\x0ecanBeCapturing\x123\n" +
	"\x16can_be_capture_success\x18\x06 \x01(\bR\x13canBeCaptureSuccess\x121\n" +
	"\x15can_be_capture_failed\x18\a \x01(\bR\x12canBeCaptureFailed\x12^\n" +
	"\x13recommend_positions\x18\n" +
	" \x03(\x0e2-.api.users.boo.types.v1.BooAnimation.PositionR\x12recommendPositions\x12e\n" +
	"\x13scene_and_dialogues\x18\v \x03(\v25.api.users.boo.types.v1.BooAnimation.SceneAndDialogueR\x11sceneAndDialogues\x1al\n" +
	"\x10SceneAndDialogue\x12:\n" +
	"\x05scene\x18\x01 \x01(\x0e2$.api.users.boo.types.v1.BooShowSceneR\x05scene\x12\x1c\n" +
	"\tdialogues\x18\x02 \x03(\tR\tdialogues\"\xeb\x01\n" +
	"\bPosition\x12\x18\n" +
	"\x14POSITION_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fPOSITION_TOP\x10\x01\x12\x13\n" +
	"\x0fPOSITION_BOTTOM\x10\x02\x12\x11\n" +
	"\rPOSITION_LEFT\x10\x03\x12\x12\n" +
	"\x0ePOSITION_RIGHT\x10\x04\x12\x13\n" +
	"\x0fPOSITION_CENTER\x10\x05\x12\x15\n" +
	"\x11POSITION_TOP_LEFT\x10\x06\x12\x16\n" +
	"\x12POSITION_TOP_RIGHT\x10\a\x12\x18\n" +
	"\x14POSITION_BOTTOM_LEFT\x10\b\x12\x19\n" +
	"\x15POSITION_BOTTOM_RIGHT\x10\t\"\x97\x06\n" +
	"\vBooShowInfo\x12O\n" +
	"\vshow_config\x18\x01 \x01(\v2..api.users.boo.types.v1.BooShowInfo.ShowConfigR\n" +
	"showConfig\x12S\n" +
	"\rreceived_boos\x18\x03 \x03(\v2..api.users.boo.types.v1.BooShowInfo.RecivedBooR\freceivedBoos\x1aK\n" +
	"\n" +
	"ShowConfig\x12=\n" +
	"\x1bevery_show_interval_seconds\x18\x01 \x01(\rR\x18everyShowIntervalSeconds\x1a\x94\x04\n" +
	"\n" +
	"RecivedBoo\x12-\n" +
	"\x03boo\x18\x01 \x01(\v2\x1b.api.users.boo.types.v1.BooR\x03boo\x120\n" +
	"\x14remaining_show_count\x18\x02 \x01(\rR\x12remainingShowCount\x12\\\n" +
	"\fshow_records\x18\x03 \x03(\v29.api.users.boo.types.v1.BooShowInfo.RecivedBoo.ShowRecordR\vshowRecords\x127\n" +
	"\x18valid_until_in_unixstamp\x18\x04 \x01(\rR\x15validUntilInUnixstamp\x12G\n" +
	"\fvalid_scenes\x18\x05 \x03(\x0e2$.api.users.boo.types.v1.BooShowSceneR\vvalidScenes\x1a\xc4\x01\n" +
	"\n" +
	"ShowRecord\x12:\n" +
	"\x05scene\x18\x01 \x01(\x0e2$.api.users.boo.types.v1.BooShowSceneR\x05scene\x12I\n" +
	"\femotion_type\x18\x02 \x01(\x0e2&.api.users.boo.types.v1.BooEmotionTypeR\vemotionType\x12/\n" +
	"\x14show_at_in_unixstamp\x18\x03 \x01(\rR\x11showAtInUnixstamp\"\xb8\x01\n" +
	"\x19BooShowInfoUpdatedMessage\x12E\n" +
	"\x05topic\x18\x01 \x01(\x0e2/.api.users.boo.types.v1.BooShowInfoUpdatedTopicR\x05topic\x12T\n" +
	"\x14latest_boo_show_info\x18\x02 \x01(\v2#.api.users.boo.types.v1.BooShowInfoR\x11latestBooShowInfo\"\xd3\x01\n" +
	"\x1fAvatarCreatedInfoUpdatedMessage\x12K\n" +
	"\x05topic\x18\x01 \x01(\x0e25.api.users.boo.types.v1.AvatarCreatedInfoUpdatedTopicR\x05topic\x12c\n" +
	"\x17use_avatar_created_info\x18\x02 \x01(\v2,.api.users.boo.types.v1.UseAvatarCreatedInfoR\x14useAvatarCreatedInfo*\xaa\x01\n" +
	"\x13BooSendRecordStatus\x12&\n" +
	"\"BOO_SEND_RECORD_STATUS_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eBOO_SEND_RECORD_STATUS_PENDING\x10\x01\x12\"\n" +
	"\x1eBOO_SEND_RECORD_STATUS_SUCCESS\x10\x02\x12#\n" +
	"\x1fBOO_SEND_RECORD_STATUS_CAPTURED\x10\x03*\x86\x01\n" +
	"\tJobStatus\x12\x1a\n" +
	"\x16JOB_STATUS_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12JOB_STATUS_PENDING\x10\x01\x12\x16\n" +
	"\x12JOB_STATUS_RUNNING\x10\x02\x12\x16\n" +
	"\x12JOB_STATUS_SUCCESS\x10\x03\x12\x15\n" +
	"\x11JOB_STATUS_FAILED\x10\x04*\xe4\x01\n" +
	"\x0eUserActionType\x12 \n" +
	"\x1cUSER_ACTION_TYPE_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19USER_ACTION_TYPE_BOO_SHOW\x10\x01\x12\"\n" +
	"\x1eUSER_ACTION_TYPE_BEGIN_CAPTURE\x10\x02\x12\"\n" +
	"\x1eUSER_ACTION_TYPE_LEAVE_CAPTURE\x10\x03\x12#\n" +
	"\x1fUSER_ACTION_TYPE_CAPTURE_FAILED\x10\x04\x12$\n" +
	" USER_ACTION_TYPE_CAPTURE_SUCCESS\x10\x05*\x8b\x01\n" +
	"\fBooShowScene\x12\x19\n" +
	"\x15BOO_SCENE_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eBOO_SCENE_FEED\x10\x01\x12\x19\n" +
	"\x15BOO_SCENE_DETAIL_PAGE\x10\x02\x12\x15\n" +
	"\x11BOO_SCENE_IM_CHAT\x10\x03\x12\x1a\n" +
	"\x16BOO_SCENE_POLLUTE_FEED\x10\x04*\xea\x01\n" +
	"\x0eBooEmotionType\x12 \n" +
	"\x1cBOO_EMOTION_TYPE_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16BOO_EMOTION_TYPE_ANGRY\x10\x01\x12\x1b\n" +
	"\x17BOO_EMOTION_TYPE_SCARED\x10\x02\x12\x1a\n" +
	"\x16BOO_EMOTION_TYPE_HAPPY\x10\x04\x12\x1a\n" +
	"\x16BOO_EMOTION_TYPE_BORED\x10\x05\x12\x1a\n" +
	"\x16BOO_EMOTION_TYPE_FUNNY\x10\b\x12)\n" +
	"%BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED\x10\t*`\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11STATUS_GENERATING\x10\x01\x12\x14\n" +
	"\x10STATUS_GENERATED\x10\x02\x12\x11\n" +
	"\rSTATUS_FAILED\x10\x03*a\n" +
	"\fPickedStatus\x12\x1d\n" +
	"\x19PICKED_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14PICKED_STATUS_UNUSED\x10\x01\x12\x18\n" +
	"\x14PICKED_STATUS_PICKED\x10\x02*R\n" +
	"\aBooType\x12\x18\n" +
	"\x14BOO_TYPE_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15BOO_TYPE_USER_CREATED\x10\x01\x12\x12\n" +
	"\x0eBOO_TYPE_HAUNT\x10\x02*u\n" +
	"\x17BooShowInfoUpdatedTopic\x12+\n" +
	"'BOO_SHOW_INFO_UPDATED_TOPIC_UNSPECIFIED\x10\x00\x12-\n" +
	")BOO_SHOW_INFO_UPDATED_BY_RECEIVED_NEW_BOO\x10\x01*\x83\x02\n" +
	"\x1dAvatarCreatedInfoUpdatedTopic\x121\n" +
	"-AVATAR_CREATED_INFO_UPDATED_TOPIC_UNSPECIFIED\x10\x00\x123\n" +
	"/AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATED\x10\x01\x12:\n" +
	"6AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_SELECTED_AS_USED\x10\x02\x12>\n" +
	":AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATE_JOB_CREATED\x10\x03B5Z3boson/api/users/boo/types/v1;api_users_boo_types_v1b\x06proto3"

var (
	file_api_users_boo_types_v1_types_proto_rawDescOnce sync.Once
	file_api_users_boo_types_v1_types_proto_rawDescData []byte
)

func file_api_users_boo_types_v1_types_proto_rawDescGZIP() []byte {
	file_api_users_boo_types_v1_types_proto_rawDescOnce.Do(func() {
		file_api_users_boo_types_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_users_boo_types_v1_types_proto_rawDesc), len(file_api_users_boo_types_v1_types_proto_rawDesc)))
	})
	return file_api_users_boo_types_v1_types_proto_rawDescData
}

var file_api_users_boo_types_v1_types_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_api_users_boo_types_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_users_boo_types_v1_types_proto_goTypes = []any{
	(BooSendRecordStatus)(0),                  // 0: api.users.boo.types.v1.BooSendRecordStatus
	(JobStatus)(0),                            // 1: api.users.boo.types.v1.JobStatus
	(UserActionType)(0),                       // 2: api.users.boo.types.v1.UserActionType
	(BooShowScene)(0),                         // 3: api.users.boo.types.v1.BooShowScene
	(BooEmotionType)(0),                       // 4: api.users.boo.types.v1.BooEmotionType
	(Status)(0),                               // 5: api.users.boo.types.v1.Status
	(PickedStatus)(0),                         // 6: api.users.boo.types.v1.PickedStatus
	(BooType)(0),                              // 7: api.users.boo.types.v1.BooType
	(BooShowInfoUpdatedTopic)(0),              // 8: api.users.boo.types.v1.BooShowInfoUpdatedTopic
	(AvatarCreatedInfoUpdatedTopic)(0),        // 9: api.users.boo.types.v1.AvatarCreatedInfoUpdatedTopic
	(BooAnimation_Position)(0),                // 10: api.users.boo.types.v1.BooAnimation.Position
	(*Avatar)(nil),                            // 11: api.users.boo.types.v1.Avatar
	(*UseAvatarCreatedInfo)(nil),              // 12: api.users.boo.types.v1.UseAvatarCreatedInfo
	(*GenerateAvatarJob)(nil),                 // 13: api.users.boo.types.v1.GenerateAvatarJob
	(*Boo)(nil),                               // 14: api.users.boo.types.v1.Boo
	(*BooAnimation)(nil),                      // 15: api.users.boo.types.v1.BooAnimation
	(*BooShowInfo)(nil),                       // 16: api.users.boo.types.v1.BooShowInfo
	(*BooShowInfoUpdatedMessage)(nil),         // 17: api.users.boo.types.v1.BooShowInfoUpdatedMessage
	(*AvatarCreatedInfoUpdatedMessage)(nil),   // 18: api.users.boo.types.v1.AvatarCreatedInfoUpdatedMessage
	(*BooAnimation_SceneAndDialogue)(nil),     // 19: api.users.boo.types.v1.BooAnimation.SceneAndDialogue
	(*BooShowInfo_ShowConfig)(nil),            // 20: api.users.boo.types.v1.BooShowInfo.ShowConfig
	(*BooShowInfo_RecivedBoo)(nil),            // 21: api.users.boo.types.v1.BooShowInfo.RecivedBoo
	(*BooShowInfo_RecivedBoo_ShowRecord)(nil), // 22: api.users.boo.types.v1.BooShowInfo.RecivedBoo.ShowRecord
	(*v1.Resource)(nil),                       // 23: api.resource.types.v1.Resource
	(*v11.UserInfoSummary)(nil),               // 24: api.users.info.types.v1.UserInfoSummary
}
var file_api_users_boo_types_v1_types_proto_depIdxs = []int32{
	23, // 0: api.users.boo.types.v1.Avatar.resource:type_name -> api.resource.types.v1.Resource
	23, // 1: api.users.boo.types.v1.UseAvatarCreatedInfo.selected_avatar_resource:type_name -> api.resource.types.v1.Resource
	23, // 2: api.users.boo.types.v1.UseAvatarCreatedInfo.all_generated_avatar_resources:type_name -> api.resource.types.v1.Resource
	13, // 3: api.users.boo.types.v1.UseAvatarCreatedInfo.current_active_job:type_name -> api.users.boo.types.v1.GenerateAvatarJob
	11, // 4: api.users.boo.types.v1.UseAvatarCreatedInfo.all_generated_avatars:type_name -> api.users.boo.types.v1.Avatar
	11, // 5: api.users.boo.types.v1.UseAvatarCreatedInfo.selected_avatar:type_name -> api.users.boo.types.v1.Avatar
	1,  // 6: api.users.boo.types.v1.GenerateAvatarJob.job_status:type_name -> api.users.boo.types.v1.JobStatus
	23, // 7: api.users.boo.types.v1.GenerateAvatarJob.user_uploaded_photo_resource:type_name -> api.resource.types.v1.Resource
	23, // 8: api.users.boo.types.v1.GenerateAvatarJob.generated_avatar_resources:type_name -> api.resource.types.v1.Resource
	24, // 9: api.users.boo.types.v1.Boo.creator:type_name -> api.users.info.types.v1.UserInfoSummary
	15, // 10: api.users.boo.types.v1.Boo.animations:type_name -> api.users.boo.types.v1.BooAnimation
	5,  // 11: api.users.boo.types.v1.Boo.status:type_name -> api.users.boo.types.v1.Status
	6,  // 12: api.users.boo.types.v1.Boo.picked_status:type_name -> api.users.boo.types.v1.PickedStatus
	4,  // 13: api.users.boo.types.v1.BooAnimation.emotion_type:type_name -> api.users.boo.types.v1.BooEmotionType
	10, // 14: api.users.boo.types.v1.BooAnimation.recommend_positions:type_name -> api.users.boo.types.v1.BooAnimation.Position
	19, // 15: api.users.boo.types.v1.BooAnimation.scene_and_dialogues:type_name -> api.users.boo.types.v1.BooAnimation.SceneAndDialogue
	20, // 16: api.users.boo.types.v1.BooShowInfo.show_config:type_name -> api.users.boo.types.v1.BooShowInfo.ShowConfig
	21, // 17: api.users.boo.types.v1.BooShowInfo.received_boos:type_name -> api.users.boo.types.v1.BooShowInfo.RecivedBoo
	8,  // 18: api.users.boo.types.v1.BooShowInfoUpdatedMessage.topic:type_name -> api.users.boo.types.v1.BooShowInfoUpdatedTopic
	16, // 19: api.users.boo.types.v1.BooShowInfoUpdatedMessage.latest_boo_show_info:type_name -> api.users.boo.types.v1.BooShowInfo
	9,  // 20: api.users.boo.types.v1.AvatarCreatedInfoUpdatedMessage.topic:type_name -> api.users.boo.types.v1.AvatarCreatedInfoUpdatedTopic
	12, // 21: api.users.boo.types.v1.AvatarCreatedInfoUpdatedMessage.use_avatar_created_info:type_name -> api.users.boo.types.v1.UseAvatarCreatedInfo
	3,  // 22: api.users.boo.types.v1.BooAnimation.SceneAndDialogue.scene:type_name -> api.users.boo.types.v1.BooShowScene
	14, // 23: api.users.boo.types.v1.BooShowInfo.RecivedBoo.boo:type_name -> api.users.boo.types.v1.Boo
	22, // 24: api.users.boo.types.v1.BooShowInfo.RecivedBoo.show_records:type_name -> api.users.boo.types.v1.BooShowInfo.RecivedBoo.ShowRecord
	3,  // 25: api.users.boo.types.v1.BooShowInfo.RecivedBoo.valid_scenes:type_name -> api.users.boo.types.v1.BooShowScene
	3,  // 26: api.users.boo.types.v1.BooShowInfo.RecivedBoo.ShowRecord.scene:type_name -> api.users.boo.types.v1.BooShowScene
	4,  // 27: api.users.boo.types.v1.BooShowInfo.RecivedBoo.ShowRecord.emotion_type:type_name -> api.users.boo.types.v1.BooEmotionType
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_api_users_boo_types_v1_types_proto_init() }
func file_api_users_boo_types_v1_types_proto_init() {
	if File_api_users_boo_types_v1_types_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_users_boo_types_v1_types_proto_rawDesc), len(file_api_users_boo_types_v1_types_proto_rawDesc)),
			NumEnums:      11,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_users_boo_types_v1_types_proto_goTypes,
		DependencyIndexes: file_api_users_boo_types_v1_types_proto_depIdxs,
		EnumInfos:         file_api_users_boo_types_v1_types_proto_enumTypes,
		MessageInfos:      file_api_users_boo_types_v1_types_proto_msgTypes,
	}.Build()
	File_api_users_boo_types_v1_types_proto = out.File
	file_api_users_boo_types_v1_types_proto_goTypes = nil
	file_api_users_boo_types_v1_types_proto_depIdxs = nil
}
