// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/users/boo/types/v1/types.proto

package api_users_boo_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Avatar with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Avatar) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Avatar with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AvatarMultiError, or nil if none found.
func (m *Avatar) ValidateAll() error {
	return m.validate(true)
}

func (m *Avatar) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AvatarValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AvatarValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AvatarValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SelectedAsUsed

	if len(errors) > 0 {
		return AvatarMultiError(errors)
	}

	return nil
}

// AvatarMultiError is an error wrapping multiple validation errors returned by
// Avatar.ValidateAll() if the designated constraints aren't met.
type AvatarMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AvatarMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AvatarMultiError) AllErrors() []error { return m }

// AvatarValidationError is the validation error returned by Avatar.Validate if
// the designated constraints aren't met.
type AvatarValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AvatarValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AvatarValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AvatarValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AvatarValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AvatarValidationError) ErrorName() string { return "AvatarValidationError" }

// Error satisfies the builtin error interface
func (e AvatarValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAvatar.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AvatarValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AvatarValidationError{}

// Validate checks the field values on UseAvatarCreatedInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UseAvatarCreatedInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UseAvatarCreatedInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UseAvatarCreatedInfoMultiError, or nil if none found.
func (m *UseAvatarCreatedInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UseAvatarCreatedInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaxCreatedJobsCount

	// no validation rules for RemainCreatedJobsCount

	if all {
		switch v := interface{}(m.GetSelectedAvatarResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UseAvatarCreatedInfoValidationError{
					field:  "SelectedAvatarResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UseAvatarCreatedInfoValidationError{
					field:  "SelectedAvatarResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedAvatarResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UseAvatarCreatedInfoValidationError{
				field:  "SelectedAvatarResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAllGeneratedAvatarResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UseAvatarCreatedInfoValidationError{
						field:  fmt.Sprintf("AllGeneratedAvatarResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UseAvatarCreatedInfoValidationError{
						field:  fmt.Sprintf("AllGeneratedAvatarResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UseAvatarCreatedInfoValidationError{
					field:  fmt.Sprintf("AllGeneratedAvatarResources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCurrentActiveJob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UseAvatarCreatedInfoValidationError{
					field:  "CurrentActiveJob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UseAvatarCreatedInfoValidationError{
					field:  "CurrentActiveJob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentActiveJob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UseAvatarCreatedInfoValidationError{
				field:  "CurrentActiveJob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAllGeneratedAvatars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UseAvatarCreatedInfoValidationError{
						field:  fmt.Sprintf("AllGeneratedAvatars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UseAvatarCreatedInfoValidationError{
						field:  fmt.Sprintf("AllGeneratedAvatars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UseAvatarCreatedInfoValidationError{
					field:  fmt.Sprintf("AllGeneratedAvatars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSelectedAvatar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UseAvatarCreatedInfoValidationError{
					field:  "SelectedAvatar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UseAvatarCreatedInfoValidationError{
					field:  "SelectedAvatar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedAvatar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UseAvatarCreatedInfoValidationError{
				field:  "SelectedAvatar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UseAvatarCreatedInfoMultiError(errors)
	}

	return nil
}

// UseAvatarCreatedInfoMultiError is an error wrapping multiple validation
// errors returned by UseAvatarCreatedInfo.ValidateAll() if the designated
// constraints aren't met.
type UseAvatarCreatedInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UseAvatarCreatedInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UseAvatarCreatedInfoMultiError) AllErrors() []error { return m }

// UseAvatarCreatedInfoValidationError is the validation error returned by
// UseAvatarCreatedInfo.Validate if the designated constraints aren't met.
type UseAvatarCreatedInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UseAvatarCreatedInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UseAvatarCreatedInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UseAvatarCreatedInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UseAvatarCreatedInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UseAvatarCreatedInfoValidationError) ErrorName() string {
	return "UseAvatarCreatedInfoValidationError"
}

// Error satisfies the builtin error interface
func (e UseAvatarCreatedInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUseAvatarCreatedInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UseAvatarCreatedInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UseAvatarCreatedInfoValidationError{}

// Validate checks the field values on GenerateAvatarJob with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GenerateAvatarJob) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateAvatarJob with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateAvatarJobMultiError, or nil if none found.
func (m *GenerateAvatarJob) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateAvatarJob) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreatorId

	// no validation rules for JobStatus

	if all {
		switch v := interface{}(m.GetUserUploadedPhotoResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateAvatarJobValidationError{
					field:  "UserUploadedPhotoResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateAvatarJobValidationError{
					field:  "UserUploadedPhotoResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserUploadedPhotoResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateAvatarJobValidationError{
				field:  "UserUploadedPhotoResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetGeneratedAvatarResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GenerateAvatarJobValidationError{
						field:  fmt.Sprintf("GeneratedAvatarResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GenerateAvatarJobValidationError{
						field:  fmt.Sprintf("GeneratedAvatarResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GenerateAvatarJobValidationError{
					field:  fmt.Sprintf("GeneratedAvatarResources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EstimatedRemainingSeconds

	// no validation rules for ExecutedSeconds

	// no validation rules for UpdatedAtInUnixstamp

	// no validation rules for CreatedAtInUnixstamp

	if len(errors) > 0 {
		return GenerateAvatarJobMultiError(errors)
	}

	return nil
}

// GenerateAvatarJobMultiError is an error wrapping multiple validation errors
// returned by GenerateAvatarJob.ValidateAll() if the designated constraints
// aren't met.
type GenerateAvatarJobMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateAvatarJobMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateAvatarJobMultiError) AllErrors() []error { return m }

// GenerateAvatarJobValidationError is the validation error returned by
// GenerateAvatarJob.Validate if the designated constraints aren't met.
type GenerateAvatarJobValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateAvatarJobValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateAvatarJobValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateAvatarJobValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateAvatarJobValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateAvatarJobValidationError) ErrorName() string {
	return "GenerateAvatarJobValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateAvatarJobValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateAvatarJob.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateAvatarJobValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateAvatarJobValidationError{}

// Validate checks the field values on Boo with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Boo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Boo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BooMultiError, or nil if none found.
func (m *Boo) ValidateAll() error {
	return m.validate(true)
}

func (m *Boo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetCreator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BooValidationError{
					field:  "Creator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BooValidationError{
					field:  "Creator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BooValidationError{
				field:  "Creator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAnimations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BooValidationError{
						field:  fmt.Sprintf("Animations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BooValidationError{
						field:  fmt.Sprintf("Animations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BooValidationError{
					field:  fmt.Sprintf("Animations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AvatarImageUrl

	// no validation rules for Status

	// no validation rules for PickedStatus

	if len(errors) > 0 {
		return BooMultiError(errors)
	}

	return nil
}

// BooMultiError is an error wrapping multiple validation errors returned by
// Boo.ValidateAll() if the designated constraints aren't met.
type BooMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooMultiError) AllErrors() []error { return m }

// BooValidationError is the validation error returned by Boo.Validate if the
// designated constraints aren't met.
type BooValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooValidationError) ErrorName() string { return "BooValidationError" }

// Error satisfies the builtin error interface
func (e BooValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBoo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooValidationError{}

// Validate checks the field values on BooAnimation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BooAnimation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooAnimation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BooAnimationMultiError, or
// nil if none found.
func (m *BooAnimation) ValidateAll() error {
	return m.validate(true)
}

func (m *BooAnimation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for EmotionType

	// no validation rules for VideoPlayUrl

	// no validation rules for CanBeGreeting

	// no validation rules for CanBeCapturing

	// no validation rules for CanBeCaptureSuccess

	// no validation rules for CanBeCaptureFailed

	for idx, item := range m.GetSceneAndDialogues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BooAnimationValidationError{
						field:  fmt.Sprintf("SceneAndDialogues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BooAnimationValidationError{
						field:  fmt.Sprintf("SceneAndDialogues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BooAnimationValidationError{
					field:  fmt.Sprintf("SceneAndDialogues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BooAnimationMultiError(errors)
	}

	return nil
}

// BooAnimationMultiError is an error wrapping multiple validation errors
// returned by BooAnimation.ValidateAll() if the designated constraints aren't met.
type BooAnimationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooAnimationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooAnimationMultiError) AllErrors() []error { return m }

// BooAnimationValidationError is the validation error returned by
// BooAnimation.Validate if the designated constraints aren't met.
type BooAnimationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooAnimationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooAnimationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooAnimationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooAnimationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooAnimationValidationError) ErrorName() string { return "BooAnimationValidationError" }

// Error satisfies the builtin error interface
func (e BooAnimationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooAnimation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooAnimationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooAnimationValidationError{}

// Validate checks the field values on BooShowInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BooShowInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooShowInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BooShowInfoMultiError, or
// nil if none found.
func (m *BooShowInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *BooShowInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShowConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BooShowInfoValidationError{
					field:  "ShowConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BooShowInfoValidationError{
					field:  "ShowConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShowConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BooShowInfoValidationError{
				field:  "ShowConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetReceivedBoos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BooShowInfoValidationError{
						field:  fmt.Sprintf("ReceivedBoos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BooShowInfoValidationError{
						field:  fmt.Sprintf("ReceivedBoos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BooShowInfoValidationError{
					field:  fmt.Sprintf("ReceivedBoos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BooShowInfoMultiError(errors)
	}

	return nil
}

// BooShowInfoMultiError is an error wrapping multiple validation errors
// returned by BooShowInfo.ValidateAll() if the designated constraints aren't met.
type BooShowInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooShowInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooShowInfoMultiError) AllErrors() []error { return m }

// BooShowInfoValidationError is the validation error returned by
// BooShowInfo.Validate if the designated constraints aren't met.
type BooShowInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooShowInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooShowInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooShowInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooShowInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooShowInfoValidationError) ErrorName() string { return "BooShowInfoValidationError" }

// Error satisfies the builtin error interface
func (e BooShowInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooShowInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooShowInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooShowInfoValidationError{}

// Validate checks the field values on BooShowInfoUpdatedMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BooShowInfoUpdatedMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooShowInfoUpdatedMessage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BooShowInfoUpdatedMessageMultiError, or nil if none found.
func (m *BooShowInfoUpdatedMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *BooShowInfoUpdatedMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Topic

	if all {
		switch v := interface{}(m.GetLatestBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BooShowInfoUpdatedMessageValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BooShowInfoUpdatedMessageValidationError{
					field:  "LatestBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BooShowInfoUpdatedMessageValidationError{
				field:  "LatestBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BooShowInfoUpdatedMessageMultiError(errors)
	}

	return nil
}

// BooShowInfoUpdatedMessageMultiError is an error wrapping multiple validation
// errors returned by BooShowInfoUpdatedMessage.ValidateAll() if the
// designated constraints aren't met.
type BooShowInfoUpdatedMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooShowInfoUpdatedMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooShowInfoUpdatedMessageMultiError) AllErrors() []error { return m }

// BooShowInfoUpdatedMessageValidationError is the validation error returned by
// BooShowInfoUpdatedMessage.Validate if the designated constraints aren't met.
type BooShowInfoUpdatedMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooShowInfoUpdatedMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooShowInfoUpdatedMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooShowInfoUpdatedMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooShowInfoUpdatedMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooShowInfoUpdatedMessageValidationError) ErrorName() string {
	return "BooShowInfoUpdatedMessageValidationError"
}

// Error satisfies the builtin error interface
func (e BooShowInfoUpdatedMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooShowInfoUpdatedMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooShowInfoUpdatedMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooShowInfoUpdatedMessageValidationError{}

// Validate checks the field values on AvatarCreatedInfoUpdatedMessage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AvatarCreatedInfoUpdatedMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AvatarCreatedInfoUpdatedMessage with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AvatarCreatedInfoUpdatedMessageMultiError, or nil if none found.
func (m *AvatarCreatedInfoUpdatedMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *AvatarCreatedInfoUpdatedMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Topic

	if all {
		switch v := interface{}(m.GetUseAvatarCreatedInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AvatarCreatedInfoUpdatedMessageValidationError{
					field:  "UseAvatarCreatedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AvatarCreatedInfoUpdatedMessageValidationError{
					field:  "UseAvatarCreatedInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUseAvatarCreatedInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AvatarCreatedInfoUpdatedMessageValidationError{
				field:  "UseAvatarCreatedInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AvatarCreatedInfoUpdatedMessageMultiError(errors)
	}

	return nil
}

// AvatarCreatedInfoUpdatedMessageMultiError is an error wrapping multiple
// validation errors returned by AvatarCreatedInfoUpdatedMessage.ValidateAll()
// if the designated constraints aren't met.
type AvatarCreatedInfoUpdatedMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AvatarCreatedInfoUpdatedMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AvatarCreatedInfoUpdatedMessageMultiError) AllErrors() []error { return m }

// AvatarCreatedInfoUpdatedMessageValidationError is the validation error
// returned by AvatarCreatedInfoUpdatedMessage.Validate if the designated
// constraints aren't met.
type AvatarCreatedInfoUpdatedMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AvatarCreatedInfoUpdatedMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AvatarCreatedInfoUpdatedMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AvatarCreatedInfoUpdatedMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AvatarCreatedInfoUpdatedMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AvatarCreatedInfoUpdatedMessageValidationError) ErrorName() string {
	return "AvatarCreatedInfoUpdatedMessageValidationError"
}

// Error satisfies the builtin error interface
func (e AvatarCreatedInfoUpdatedMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAvatarCreatedInfoUpdatedMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AvatarCreatedInfoUpdatedMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AvatarCreatedInfoUpdatedMessageValidationError{}

// Validate checks the field values on BooAnimation_SceneAndDialogue with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BooAnimation_SceneAndDialogue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooAnimation_SceneAndDialogue with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BooAnimation_SceneAndDialogueMultiError, or nil if none found.
func (m *BooAnimation_SceneAndDialogue) ValidateAll() error {
	return m.validate(true)
}

func (m *BooAnimation_SceneAndDialogue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scene

	if len(errors) > 0 {
		return BooAnimation_SceneAndDialogueMultiError(errors)
	}

	return nil
}

// BooAnimation_SceneAndDialogueMultiError is an error wrapping multiple
// validation errors returned by BooAnimation_SceneAndDialogue.ValidateAll()
// if the designated constraints aren't met.
type BooAnimation_SceneAndDialogueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooAnimation_SceneAndDialogueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooAnimation_SceneAndDialogueMultiError) AllErrors() []error { return m }

// BooAnimation_SceneAndDialogueValidationError is the validation error
// returned by BooAnimation_SceneAndDialogue.Validate if the designated
// constraints aren't met.
type BooAnimation_SceneAndDialogueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooAnimation_SceneAndDialogueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooAnimation_SceneAndDialogueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooAnimation_SceneAndDialogueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooAnimation_SceneAndDialogueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooAnimation_SceneAndDialogueValidationError) ErrorName() string {
	return "BooAnimation_SceneAndDialogueValidationError"
}

// Error satisfies the builtin error interface
func (e BooAnimation_SceneAndDialogueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooAnimation_SceneAndDialogue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooAnimation_SceneAndDialogueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooAnimation_SceneAndDialogueValidationError{}

// Validate checks the field values on BooShowInfo_ShowConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BooShowInfo_ShowConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooShowInfo_ShowConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BooShowInfo_ShowConfigMultiError, or nil if none found.
func (m *BooShowInfo_ShowConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *BooShowInfo_ShowConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EveryShowIntervalSeconds

	if len(errors) > 0 {
		return BooShowInfo_ShowConfigMultiError(errors)
	}

	return nil
}

// BooShowInfo_ShowConfigMultiError is an error wrapping multiple validation
// errors returned by BooShowInfo_ShowConfig.ValidateAll() if the designated
// constraints aren't met.
type BooShowInfo_ShowConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooShowInfo_ShowConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooShowInfo_ShowConfigMultiError) AllErrors() []error { return m }

// BooShowInfo_ShowConfigValidationError is the validation error returned by
// BooShowInfo_ShowConfig.Validate if the designated constraints aren't met.
type BooShowInfo_ShowConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooShowInfo_ShowConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooShowInfo_ShowConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooShowInfo_ShowConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooShowInfo_ShowConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooShowInfo_ShowConfigValidationError) ErrorName() string {
	return "BooShowInfo_ShowConfigValidationError"
}

// Error satisfies the builtin error interface
func (e BooShowInfo_ShowConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooShowInfo_ShowConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooShowInfo_ShowConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooShowInfo_ShowConfigValidationError{}

// Validate checks the field values on BooShowInfo_RecivedBoo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BooShowInfo_RecivedBoo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooShowInfo_RecivedBoo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BooShowInfo_RecivedBooMultiError, or nil if none found.
func (m *BooShowInfo_RecivedBoo) ValidateAll() error {
	return m.validate(true)
}

func (m *BooShowInfo_RecivedBoo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBoo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BooShowInfo_RecivedBooValidationError{
					field:  "Boo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BooShowInfo_RecivedBooValidationError{
					field:  "Boo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBoo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BooShowInfo_RecivedBooValidationError{
				field:  "Boo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RemainingShowCount

	for idx, item := range m.GetShowRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BooShowInfo_RecivedBooValidationError{
						field:  fmt.Sprintf("ShowRecords[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BooShowInfo_RecivedBooValidationError{
						field:  fmt.Sprintf("ShowRecords[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BooShowInfo_RecivedBooValidationError{
					field:  fmt.Sprintf("ShowRecords[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ValidUntilInUnixstamp

	if len(errors) > 0 {
		return BooShowInfo_RecivedBooMultiError(errors)
	}

	return nil
}

// BooShowInfo_RecivedBooMultiError is an error wrapping multiple validation
// errors returned by BooShowInfo_RecivedBoo.ValidateAll() if the designated
// constraints aren't met.
type BooShowInfo_RecivedBooMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooShowInfo_RecivedBooMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooShowInfo_RecivedBooMultiError) AllErrors() []error { return m }

// BooShowInfo_RecivedBooValidationError is the validation error returned by
// BooShowInfo_RecivedBoo.Validate if the designated constraints aren't met.
type BooShowInfo_RecivedBooValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooShowInfo_RecivedBooValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooShowInfo_RecivedBooValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooShowInfo_RecivedBooValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooShowInfo_RecivedBooValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooShowInfo_RecivedBooValidationError) ErrorName() string {
	return "BooShowInfo_RecivedBooValidationError"
}

// Error satisfies the builtin error interface
func (e BooShowInfo_RecivedBooValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooShowInfo_RecivedBoo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooShowInfo_RecivedBooValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooShowInfo_RecivedBooValidationError{}

// Validate checks the field values on BooShowInfo_RecivedBoo_ShowRecord with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BooShowInfo_RecivedBoo_ShowRecord) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BooShowInfo_RecivedBoo_ShowRecord
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// BooShowInfo_RecivedBoo_ShowRecordMultiError, or nil if none found.
func (m *BooShowInfo_RecivedBoo_ShowRecord) ValidateAll() error {
	return m.validate(true)
}

func (m *BooShowInfo_RecivedBoo_ShowRecord) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scene

	// no validation rules for EmotionType

	// no validation rules for ShowAtInUnixstamp

	if len(errors) > 0 {
		return BooShowInfo_RecivedBoo_ShowRecordMultiError(errors)
	}

	return nil
}

// BooShowInfo_RecivedBoo_ShowRecordMultiError is an error wrapping multiple
// validation errors returned by
// BooShowInfo_RecivedBoo_ShowRecord.ValidateAll() if the designated
// constraints aren't met.
type BooShowInfo_RecivedBoo_ShowRecordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BooShowInfo_RecivedBoo_ShowRecordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BooShowInfo_RecivedBoo_ShowRecordMultiError) AllErrors() []error { return m }

// BooShowInfo_RecivedBoo_ShowRecordValidationError is the validation error
// returned by BooShowInfo_RecivedBoo_ShowRecord.Validate if the designated
// constraints aren't met.
type BooShowInfo_RecivedBoo_ShowRecordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BooShowInfo_RecivedBoo_ShowRecordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BooShowInfo_RecivedBoo_ShowRecordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BooShowInfo_RecivedBoo_ShowRecordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BooShowInfo_RecivedBoo_ShowRecordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BooShowInfo_RecivedBoo_ShowRecordValidationError) ErrorName() string {
	return "BooShowInfo_RecivedBoo_ShowRecordValidationError"
}

// Error satisfies the builtin error interface
func (e BooShowInfo_RecivedBoo_ShowRecordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBooShowInfo_RecivedBoo_ShowRecord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BooShowInfo_RecivedBoo_ShowRecordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BooShowInfo_RecivedBoo_ShowRecordValidationError{}
