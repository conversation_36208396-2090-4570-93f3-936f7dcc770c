syntax = "proto3";

package api.users.boo.types.v1;

option go_package = "boson/api/users/boo/types/v1;api_users_boo_types_v1";

import "api/users/info/types/v1/types.proto";
import "api/resource/types/v1/types.proto";

enum BooSendRecordStatus {
	BOO_SEND_RECORD_STATUS_UNSPECIFIED = 0;
	// 等待发送，这是因为 boo 在生成中，需要等待 boo 生成完毕后，再发送
	BOO_SEND_RECORD_STATUS_PENDING = 1;
	// 发送成功，boo 生成完毕，并且发送成功
	BOO_SEND_RECORD_STATUS_SUCCESS = 2;
	// 发送的鬼被抓住了
	BOO_SEND_RECORD_STATUS_CAPTURED = 3;
}

enum JobStatus {
	JOB_STATUS_UNSPECIFIED = 0;
	// 等待调度
	JOB_STATUS_PENDING = 1;
	// 运行中
	JOB_STATUS_RUNNING = 2;
	// 成功
	JOB_STATUS_SUCCESS = 3;
	// 失败
	JOB_STATUS_FAILED = 4;
}



message Avatar {
	string id = 1;
	api.resource.types.v1.Resource resource = 2;
	bool selected_as_used = 3;
}

message UseAvatarCreatedInfo {
	// 最多尝试几次头像生成
	uint32 max_created_jobs_count = 1;
	// 剩余几次生成头像的机会
	uint32 remain_created_jobs_count = 2;
	// 给哪些人发过 Boo
	repeated string sent_to_user_ids = 3;
	// 用户目前选择的头像
	api.resource.types.v1.Resource selected_avatar_resource = 4;
	// 历史里所有的头像
	repeated api.resource.types.v1.Resource all_generated_avatar_resources = 5;
	// 目前生效的 job，可能是 pending || success
	GenerateAvatarJob current_active_job = 6;

	repeated Avatar all_generated_avatars = 7;
	Avatar selected_avatar = 8;
}

message GenerateAvatarJob {
	string id = 1;
	string creator_id = 2;
	JobStatus job_status = 3;	

	api.resource.types.v1.Resource user_uploaded_photo_resource = 9;

	// 生成出来的头像，当且仅当 status = Success 后才会出现
	repeated api.resource.types.v1.Resource generated_avatar_resources = 4;

	// 预估的剩余秒数
	uint32 estimated_remaining_seconds = 5;
	// 已经执行了多久了，秒数
	uint32 executed_seconds = 6;

	uint32 updated_at_in_unixstamp = 7;
	uint32 created_at_in_unixstamp = 8;
}


enum UserActionType {
	USER_ACTION_TYPE_UNSPECIFIED = 0;
	// 闹鬼了
	USER_ACTION_TYPE_BOO_SHOW = 1;
	// 开始抓鬼
	USER_ACTION_TYPE_BEGIN_CAPTURE = 2;
	// 离开抓鬼
	USER_ACTION_TYPE_LEAVE_CAPTURE = 3;
	// 抓鬼失败
	USER_ACTION_TYPE_CAPTURE_FAILED = 4;
	// 抓鬼成功
	USER_ACTION_TYPE_CAPTURE_SUCCESS = 5;
}

// 闹鬼场景
enum BooShowScene {
	BOO_SCENE_UNSPECIFIED = 0;
	// Feed 流
	BOO_SCENE_FEED = 1;
	// 详情页
	BOO_SCENE_DETAIL_PAGE = 2;
	// IM 聊天
	BOO_SCENE_IM_CHAT = 3;
	// 污染 feed
	BOO_SCENE_POLLUTE_FEED = 4;
}

// Boo 情绪种类
enum BooEmotionType {
	BOO_EMOTION_TYPE_UNSPECIFIED = 0;
	// 生气
	BOO_EMOTION_TYPE_ANGRY = 1;
	// 害怕
	BOO_EMOTION_TYPE_SCARED = 2;
	// 快乐
	BOO_EMOTION_TYPE_HAPPY = 4;
	// 无聊
	BOO_EMOTION_TYPE_BORED = 5;
	// 有趣
	BOO_EMOTION_TYPE_FUNNY = 8;
	// 抓住消失了
	BOO_EMOTION_TYPE_CAPTURED_DISAPPEARED = 9;
}

enum Status {
	STATUS_UNSPECIFIED = 0;
	// 生成中
	STATUS_GENERATING = 1;
	// 生成成功
	STATUS_GENERATED = 2;
	// 生成失败
	STATUS_FAILED = 3;
}
enum PickedStatus {
	PICKED_STATUS_UNSPECIFIED = 0;
	// 未使用
	PICKED_STATUS_UNUSED = 1;
	// 已使用
	PICKED_STATUS_PICKED = 2;
}

enum BooType {
	BOO_TYPE_UNSPECIFIED = 0;
	// 用户创建的 boo
	BOO_TYPE_USER_CREATED = 1;
	// HAUNT BOO
	BOO_TYPE_HAUNT = 2;
}

// Boo 信息
message Boo {
	string id = 1;
	api.users.info.types.v1.UserInfoSummary creator = 2;
	repeated BooAnimation animations = 3;
	
	string avatar_image_url = 4;


	Status status = 5;
	PickedStatus picked_status = 6;
}

// boo 的动画，总是伴着情绪及播放素材等其他信息
message BooAnimation {
	string id = 1;
	BooEmotionType emotion_type = 2;
	string video_play_url = 3;

	// 是否可以作为开通动画
	bool can_be_greeting = 4;
	// 是否可以作为正在被抓的动画 -> scared
	bool can_be_capturing = 5;
	// 是否可以作为抓鬼成功动画 -> dissmiss
	bool can_be_capture_success = 6;
	// 是否可以作为抓鬼失败动画
	bool can_be_capture_failed = 7;

	enum Position {
		// 上下左右中间，坐上右上左下右下
		POSITION_UNSPECIFIED = 0;
		POSITION_TOP = 1;
		POSITION_BOTTOM = 2;
		POSITION_LEFT = 3;
		POSITION_RIGHT = 4;
		POSITION_CENTER = 5;
		POSITION_TOP_LEFT = 6;
		POSITION_TOP_RIGHT = 7;
		POSITION_BOTTOM_LEFT = 8;
		POSITION_BOTTOM_RIGHT = 9;
	}
	repeated Position recommend_positions = 10;	

	// 支持在哪些场景下渲染，并且有哪些台词
	message SceneAndDialogue {
		BooShowScene scene = 1;
		repeated string dialogues = 2;
	}
	repeated SceneAndDialogue scene_and_dialogues = 11;
}

// Boo 的渲染信息
message BooShowInfo {
	message ShowConfig {
		// 时间间隔，单位 s
		uint32 every_show_interval_seconds = 1;
	}

	ShowConfig show_config = 1;

	// 用户收到的所有鬼
	message RecivedBoo {
		Boo boo = 1;
		// 剩余展示次数，全部展示完毕后，此对象会从数组内被移除
		// 目前，每次收到新的 Boo 后，默认都是 10次
		uint32 remaining_show_count = 2;
		message ShowRecord {
			BooShowScene scene = 1;
			BooEmotionType emotion_type = 2;
			uint32 show_at_in_unixstamp = 3;
		}
		// 历史上的展示记录
		repeated ShowRecord show_records = 3;
		// 有效截止于，如果超过此时间，此元素也会从数组内被移除
		uint32 valid_until_in_unixstamp = 4;
		// 这个鬼可出现的场景
		repeated BooShowScene valid_scenes = 5;
	}
	repeated RecivedBoo received_boos = 3;
}


enum BooShowInfoUpdatedTopic {
	BOO_SHOW_INFO_UPDATED_TOPIC_UNSPECIFIED = 0;
	// 用于收到了新的 Boo 后，通知客户端更新 Boo 展示信息
	BOO_SHOW_INFO_UPDATED_BY_RECEIVED_NEW_BOO = 1;
}

message BooShowInfoUpdatedMessage {
	BooShowInfoUpdatedTopic topic = 1;
	api.users.boo.types.v1.BooShowInfo latest_boo_show_info = 2;
}


enum AvatarCreatedInfoUpdatedTopic {
	AVATAR_CREATED_INFO_UPDATED_TOPIC_UNSPECIFIED = 0;
	// 头像生成完毕了
	AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATED = 1;
	// 用户选择了一个头像作为已使用		
	AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_SELECTED_AS_USED = 2;
	// 用户新建了一个头像生成任务
	AVATAR_CREATED_INFO_UPDATED_BY_AVATAR_GENERATE_JOB_CREATED = 3;
}
message AvatarCreatedInfoUpdatedMessage {
	AvatarCreatedInfoUpdatedTopic topic = 1;
	UseAvatarCreatedInfo use_avatar_created_info = 2;
}