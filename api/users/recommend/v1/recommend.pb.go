// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/users/recommend/v1/recommend.proto

package api_users_recommend_v1

import (
	v1 "boson/api/common/v1"
	v11 "boson/api/users/info/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 推荐场景
type RecmmendScenario int32

const (
	RecmmendScenario_RECOMMEND_SCENARIO_UNSPECIFIED RecmmendScenario = 0
	// 个人页推荐人
	RecmmendScenario_RECOMMEND_SCENARIO_PERSON_PAGE RecmmendScenario = 1
	// 通知页推荐人
	RecmmendScenario_RECOMMEND_SCENARIO_NOTIFICATION_PAGE RecmmendScenario = 2
	// 搜索页LokBox Stars栏推荐人
	RecmmendScenario_RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS RecmmendScenario = 3
	// 搜索页Find Friends栏推荐人
	RecmmendScenario_RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS RecmmendScenario = 4
)

// Enum value maps for RecmmendScenario.
var (
	RecmmendScenario_name = map[int32]string{
		0: "RECOMMEND_SCENARIO_UNSPECIFIED",
		1: "RECOMMEND_SCENARIO_PERSON_PAGE",
		2: "RECOMMEND_SCENARIO_NOTIFICATION_PAGE",
		3: "RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS",
		4: "RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS",
	}
	RecmmendScenario_value = map[string]int32{
		"RECOMMEND_SCENARIO_UNSPECIFIED":         0,
		"RECOMMEND_SCENARIO_PERSON_PAGE":         1,
		"RECOMMEND_SCENARIO_NOTIFICATION_PAGE":   2,
		"RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS": 3,
		"RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS": 4,
	}
)

func (x RecmmendScenario) Enum() *RecmmendScenario {
	p := new(RecmmendScenario)
	*p = x
	return p
}

func (x RecmmendScenario) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecmmendScenario) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_recommend_v1_recommend_proto_enumTypes[0].Descriptor()
}

func (RecmmendScenario) Type() protoreflect.EnumType {
	return &file_api_users_recommend_v1_recommend_proto_enumTypes[0]
}

func (x RecmmendScenario) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecmmendScenario.Descriptor instead.
func (RecmmendScenario) EnumDescriptor() ([]byte, []int) {
	return file_api_users_recommend_v1_recommend_proto_rawDescGZIP(), []int{0}
}

type RecommendUserReasonType int32

const (
	RecommendUserReasonType_REASON_TYPE_UNSPECIFIED RecommendUserReasonType = 0
	RecommendUserReasonType_REASON_TYPE_FOLLOWED_BY RecommendUserReasonType = 1
	RecommendUserReasonType_REASON_TYPE_FRIEND_WITH RecommendUserReasonType = 2
	RecommendUserReasonType_REASON_TYPE_FOLLOWS     RecommendUserReasonType = 3
)

// Enum value maps for RecommendUserReasonType.
var (
	RecommendUserReasonType_name = map[int32]string{
		0: "REASON_TYPE_UNSPECIFIED",
		1: "REASON_TYPE_FOLLOWED_BY",
		2: "REASON_TYPE_FRIEND_WITH",
		3: "REASON_TYPE_FOLLOWS",
	}
	RecommendUserReasonType_value = map[string]int32{
		"REASON_TYPE_UNSPECIFIED": 0,
		"REASON_TYPE_FOLLOWED_BY": 1,
		"REASON_TYPE_FRIEND_WITH": 2,
		"REASON_TYPE_FOLLOWS":     3,
	}
)

func (x RecommendUserReasonType) Enum() *RecommendUserReasonType {
	p := new(RecommendUserReasonType)
	*p = x
	return p
}

func (x RecommendUserReasonType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecommendUserReasonType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_users_recommend_v1_recommend_proto_enumTypes[1].Descriptor()
}

func (RecommendUserReasonType) Type() protoreflect.EnumType {
	return &file_api_users_recommend_v1_recommend_proto_enumTypes[1]
}

func (x RecommendUserReasonType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecommendUserReasonType.Descriptor instead.
func (RecommendUserReasonType) EnumDescriptor() ([]byte, []int) {
	return file_api_users_recommend_v1_recommend_proto_rawDescGZIP(), []int{1}
}

type RecommendUsersRequest struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Scenario RecmmendScenario       `protobuf:"varint,1,opt,name=scenario,proto3,enum=api.users.recommend.v1.RecmmendScenario" json:"scenario,omitempty"`
	// 可选型，目前仅当场景为个人页推荐人时有效，传递个人页的 user_id
	ProfileUserId *string         `protobuf:"bytes,2,opt,name=profile_user_id,json=profileUserId,proto3,oneof" json:"profile_user_id,omitempty"`
	ListRequest   *v1.ListRequest `protobuf:"bytes,3,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendUsersRequest) Reset() {
	*x = RecommendUsersRequest{}
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendUsersRequest) ProtoMessage() {}

func (x *RecommendUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendUsersRequest.ProtoReflect.Descriptor instead.
func (*RecommendUsersRequest) Descriptor() ([]byte, []int) {
	return file_api_users_recommend_v1_recommend_proto_rawDescGZIP(), []int{0}
}

func (x *RecommendUsersRequest) GetScenario() RecmmendScenario {
	if x != nil {
		return x.Scenario
	}
	return RecmmendScenario_RECOMMEND_SCENARIO_UNSPECIFIED
}

func (x *RecommendUsersRequest) GetProfileUserId() string {
	if x != nil && x.ProfileUserId != nil {
		return *x.ProfileUserId
	}
	return ""
}

func (x *RecommendUsersRequest) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type RecommendUsersResponse struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	ListResponse  *v1.ListResponse                        `protobuf:"bytes,1,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	Users         []*RecommendUsersResponse_RecommendUser `protobuf:"bytes,2,rep,name=users,proto3" json:"users,omitempty"`
	SortRequestId string                                  `protobuf:"bytes,3,opt,name=sort_request_id,json=sortRequestId,proto3" json:"sort_request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendUsersResponse) Reset() {
	*x = RecommendUsersResponse{}
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendUsersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendUsersResponse) ProtoMessage() {}

func (x *RecommendUsersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendUsersResponse.ProtoReflect.Descriptor instead.
func (*RecommendUsersResponse) Descriptor() ([]byte, []int) {
	return file_api_users_recommend_v1_recommend_proto_rawDescGZIP(), []int{1}
}

func (x *RecommendUsersResponse) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

func (x *RecommendUsersResponse) GetUsers() []*RecommendUsersResponse_RecommendUser {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *RecommendUsersResponse) GetSortRequestId() string {
	if x != nil {
		return x.SortRequestId
	}
	return ""
}

type RecommendUsersResponse_RecommendUser struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 推荐理由，推荐引擎返回
	RecommendReason      string                                                     `protobuf:"bytes,1,opt,name=recommend_reason,json=recommendReason,proto3" json:"recommend_reason,omitempty"`
	User                 *v11.UserInfoSummary                                       `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	SortRequestId        string                                                     `protobuf:"bytes,3,opt,name=sort_request_id,json=sortRequestId,proto3" json:"sort_request_id,omitempty"`
	RecommendReasonExtra *RecommendUsersResponse_RecommendUser_RecommendReasonExtra `protobuf:"bytes,4,opt,name=recommend_reason_extra,json=recommendReasonExtra,proto3" json:"recommend_reason_extra,omitempty"`
	RelatedStoryId       string                                                     `protobuf:"bytes,5,opt,name=related_story_id,json=relatedStoryId,proto3" json:"related_story_id,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *RecommendUsersResponse_RecommendUser) Reset() {
	*x = RecommendUsersResponse_RecommendUser{}
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendUsersResponse_RecommendUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendUsersResponse_RecommendUser) ProtoMessage() {}

func (x *RecommendUsersResponse_RecommendUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendUsersResponse_RecommendUser.ProtoReflect.Descriptor instead.
func (*RecommendUsersResponse_RecommendUser) Descriptor() ([]byte, []int) {
	return file_api_users_recommend_v1_recommend_proto_rawDescGZIP(), []int{1, 0}
}

func (x *RecommendUsersResponse_RecommendUser) GetRecommendReason() string {
	if x != nil {
		return x.RecommendReason
	}
	return ""
}

func (x *RecommendUsersResponse_RecommendUser) GetUser() *v11.UserInfoSummary {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RecommendUsersResponse_RecommendUser) GetSortRequestId() string {
	if x != nil {
		return x.SortRequestId
	}
	return ""
}

func (x *RecommendUsersResponse_RecommendUser) GetRecommendReasonExtra() *RecommendUsersResponse_RecommendUser_RecommendReasonExtra {
	if x != nil {
		return x.RecommendReasonExtra
	}
	return nil
}

func (x *RecommendUsersResponse_RecommendUser) GetRelatedStoryId() string {
	if x != nil {
		return x.RelatedStoryId
	}
	return ""
}

// 只有富文本文案才需要这个字段
type RecommendUsersResponse_RecommendUser_RecommendReasonExtra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReasonType    string                 `protobuf:"bytes,1,opt,name=reason_type,json=reasonType,proto3" json:"reason_type,omitempty"`
	RelatedUsers  []*v11.UserInfoSummary `protobuf:"bytes,2,rep,name=related_users,json=relatedUsers,proto3" json:"related_users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) Reset() {
	*x = RecommendUsersResponse_RecommendUser_RecommendReasonExtra{}
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendUsersResponse_RecommendUser_RecommendReasonExtra) ProtoMessage() {}

func (x *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) ProtoReflect() protoreflect.Message {
	mi := &file_api_users_recommend_v1_recommend_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendUsersResponse_RecommendUser_RecommendReasonExtra.ProtoReflect.Descriptor instead.
func (*RecommendUsersResponse_RecommendUser_RecommendReasonExtra) Descriptor() ([]byte, []int) {
	return file_api_users_recommend_v1_recommend_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) GetReasonType() string {
	if x != nil {
		return x.ReasonType
	}
	return ""
}

func (x *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) GetRelatedUsers() []*v11.UserInfoSummary {
	if x != nil {
		return x.RelatedUsers
	}
	return nil
}

var File_api_users_recommend_v1_recommend_proto protoreflect.FileDescriptor

const file_api_users_recommend_v1_recommend_proto_rawDesc = "" +
	"\n" +
	"&api/users/recommend/v1/recommend.proto\x12\x16api.users.recommend.v1\x1a\x1aapi/common/v1/common.proto\x1a\x17validate/validate.proto\x1a#api/users/info/types/v1/types.proto\"\xe7\x01\n" +
	"\x15RecommendUsersRequest\x12D\n" +
	"\bscenario\x18\x01 \x01(\x0e2(.api.users.recommend.v1.RecmmendScenarioR\bscenario\x12+\n" +
	"\x0fprofile_user_id\x18\x02 \x01(\tH\x00R\rprofileUserId\x88\x01\x01\x12G\n" +
	"\flist_request\x18\x03 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequestB\x12\n" +
	"\x10_profile_user_id\"\xb6\x05\n" +
	"\x16RecommendUsersResponse\x12@\n" +
	"\rlist_response\x18\x01 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x12R\n" +
	"\x05users\x18\x02 \x03(\v2<.api.users.recommend.v1.RecommendUsersResponse.RecommendUserR\x05users\x12&\n" +
	"\x0fsort_request_id\x18\x03 \x01(\tR\rsortRequestId\x1a\xdd\x03\n" +
	"\rRecommendUser\x12)\n" +
	"\x10recommend_reason\x18\x01 \x01(\tR\x0frecommendReason\x12<\n" +
	"\x04user\x18\x02 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\x04user\x12&\n" +
	"\x0fsort_request_id\x18\x03 \x01(\tR\rsortRequestId\x12\x87\x01\n" +
	"\x16recommend_reason_extra\x18\x04 \x01(\v2Q.api.users.recommend.v1.RecommendUsersResponse.RecommendUser.RecommendReasonExtraR\x14recommendReasonExtra\x12(\n" +
	"\x10related_story_id\x18\x05 \x01(\tR\x0erelatedStoryId\x1a\x86\x01\n" +
	"\x14RecommendReasonExtra\x12\x1f\n" +
	"\vreason_type\x18\x01 \x01(\tR\n" +
	"reasonType\x12M\n" +
	"\rrelated_users\x18\x02 \x03(\v2(.api.users.info.types.v1.UserInfoSummaryR\frelatedUsers*\xdc\x01\n" +
	"\x10RecmmendScenario\x12\"\n" +
	"\x1eRECOMMEND_SCENARIO_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eRECOMMEND_SCENARIO_PERSON_PAGE\x10\x01\x12(\n" +
	"$RECOMMEND_SCENARIO_NOTIFICATION_PAGE\x10\x02\x12*\n" +
	"&RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS\x10\x03\x12*\n" +
	"&RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS\x10\x04*\x89\x01\n" +
	"\x17RecommendUserReasonType\x12\x1b\n" +
	"\x17REASON_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17REASON_TYPE_FOLLOWED_BY\x10\x01\x12\x1b\n" +
	"\x17REASON_TYPE_FRIEND_WITH\x10\x02\x12\x17\n" +
	"\x13REASON_TYPE_FOLLOWS\x10\x03B5Z3boson/api/users/recommend/v1;api_users_recommend_v1b\x06proto3"

var (
	file_api_users_recommend_v1_recommend_proto_rawDescOnce sync.Once
	file_api_users_recommend_v1_recommend_proto_rawDescData []byte
)

func file_api_users_recommend_v1_recommend_proto_rawDescGZIP() []byte {
	file_api_users_recommend_v1_recommend_proto_rawDescOnce.Do(func() {
		file_api_users_recommend_v1_recommend_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_users_recommend_v1_recommend_proto_rawDesc), len(file_api_users_recommend_v1_recommend_proto_rawDesc)))
	})
	return file_api_users_recommend_v1_recommend_proto_rawDescData
}

var file_api_users_recommend_v1_recommend_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_users_recommend_v1_recommend_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_users_recommend_v1_recommend_proto_goTypes = []any{
	(RecmmendScenario)(0),                                             // 0: api.users.recommend.v1.RecmmendScenario
	(RecommendUserReasonType)(0),                                      // 1: api.users.recommend.v1.RecommendUserReasonType
	(*RecommendUsersRequest)(nil),                                     // 2: api.users.recommend.v1.RecommendUsersRequest
	(*RecommendUsersResponse)(nil),                                    // 3: api.users.recommend.v1.RecommendUsersResponse
	(*RecommendUsersResponse_RecommendUser)(nil),                      // 4: api.users.recommend.v1.RecommendUsersResponse.RecommendUser
	(*RecommendUsersResponse_RecommendUser_RecommendReasonExtra)(nil), // 5: api.users.recommend.v1.RecommendUsersResponse.RecommendUser.RecommendReasonExtra
	(*v1.ListRequest)(nil),                                            // 6: api.common.v1.ListRequest
	(*v1.ListResponse)(nil),                                           // 7: api.common.v1.ListResponse
	(*v11.UserInfoSummary)(nil),                                       // 8: api.users.info.types.v1.UserInfoSummary
}
var file_api_users_recommend_v1_recommend_proto_depIdxs = []int32{
	0, // 0: api.users.recommend.v1.RecommendUsersRequest.scenario:type_name -> api.users.recommend.v1.RecmmendScenario
	6, // 1: api.users.recommend.v1.RecommendUsersRequest.list_request:type_name -> api.common.v1.ListRequest
	7, // 2: api.users.recommend.v1.RecommendUsersResponse.list_response:type_name -> api.common.v1.ListResponse
	4, // 3: api.users.recommend.v1.RecommendUsersResponse.users:type_name -> api.users.recommend.v1.RecommendUsersResponse.RecommendUser
	8, // 4: api.users.recommend.v1.RecommendUsersResponse.RecommendUser.user:type_name -> api.users.info.types.v1.UserInfoSummary
	5, // 5: api.users.recommend.v1.RecommendUsersResponse.RecommendUser.recommend_reason_extra:type_name -> api.users.recommend.v1.RecommendUsersResponse.RecommendUser.RecommendReasonExtra
	8, // 6: api.users.recommend.v1.RecommendUsersResponse.RecommendUser.RecommendReasonExtra.related_users:type_name -> api.users.info.types.v1.UserInfoSummary
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_users_recommend_v1_recommend_proto_init() }
func file_api_users_recommend_v1_recommend_proto_init() {
	if File_api_users_recommend_v1_recommend_proto != nil {
		return
	}
	file_api_users_recommend_v1_recommend_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_users_recommend_v1_recommend_proto_rawDesc), len(file_api_users_recommend_v1_recommend_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_users_recommend_v1_recommend_proto_goTypes,
		DependencyIndexes: file_api_users_recommend_v1_recommend_proto_depIdxs,
		EnumInfos:         file_api_users_recommend_v1_recommend_proto_enumTypes,
		MessageInfos:      file_api_users_recommend_v1_recommend_proto_msgTypes,
	}.Build()
	File_api_users_recommend_v1_recommend_proto = out.File
	file_api_users_recommend_v1_recommend_proto_goTypes = nil
	file_api_users_recommend_v1_recommend_proto_depIdxs = nil
}
