syntax = "proto3";

package api.users.recommend.v1;
option go_package = "boson/api/users/recommend/v1;api_users_recommend_v1";

import "api/common/v1/common.proto";
import "validate/validate.proto";
import "api/users/info/types/v1/types.proto";

// 推荐场景
enum RecmmendScenario {
	RECOMMEND_SCENARIO_UNSPECIFIED = 0;
	// 个人页推荐人
	RECOMMEND_SCENARIO_PERSON_PAGE = 1;
	// 通知页推荐人
	RECOMMEND_SCENARIO_NOTIFICATION_PAGE = 2;
	// 搜索页LokBox Stars栏推荐人
	RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS = 3;
	// 搜索页Find Friends栏推荐人
	RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS = 4;
}

message RecommendUsersRequest {
	RecmmendScenario scenario = 1;
	// 可选型，目前仅当场景为个人页推荐人时有效，传递个人页的 user_id
	optional string profile_user_id = 2;
	api.common.v1.ListRequest list_request = 3[(validate.rules).message.required = true];
}

enum RecommendUserReasonType {
	REASON_TYPE_UNSPECIFIED = 0;
	REASON_TYPE_FOLLOWED_BY = 1;
	REASON_TYPE_FRIEND_WITH = 2;
	REASON_TYPE_FOLLOWS = 3;
}


message RecommendUsersResponse {
	message RecommendUser {
		// 推荐理由，推荐引擎返回
		string recommend_reason = 1;
		api.users.info.types.v1.UserInfoSummary user = 2;
		string sort_request_id = 3;
		// 只有富文本文案才需要这个字段
		message RecommendReasonExtra {
			string reason_type = 1;
			repeated api.users.info.types.v1.UserInfoSummary related_users = 2;
		}
		RecommendReasonExtra recommend_reason_extra = 4;
		string related_story_id = 5;
	}
	api.common.v1.ListResponse list_response = 1;
	repeated RecommendUser users = 2;
	string sort_request_id = 3;
}