// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/users/recommend/v1/recommend.proto

package api_users_recommend_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RecommendUsersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendUsersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendUsersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendUsersRequestMultiError, or nil if none found.
func (m *RecommendUsersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendUsersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scenario

	if m.GetListRequest() == nil {
		err := RecommendUsersRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendUsersRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendUsersRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendUsersRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.ProfileUserId != nil {
		// no validation rules for ProfileUserId
	}

	if len(errors) > 0 {
		return RecommendUsersRequestMultiError(errors)
	}

	return nil
}

// RecommendUsersRequestMultiError is an error wrapping multiple validation
// errors returned by RecommendUsersRequest.ValidateAll() if the designated
// constraints aren't met.
type RecommendUsersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendUsersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendUsersRequestMultiError) AllErrors() []error { return m }

// RecommendUsersRequestValidationError is the validation error returned by
// RecommendUsersRequest.Validate if the designated constraints aren't met.
type RecommendUsersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendUsersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendUsersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendUsersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendUsersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendUsersRequestValidationError) ErrorName() string {
	return "RecommendUsersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendUsersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendUsersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendUsersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendUsersRequestValidationError{}

// Validate checks the field values on RecommendUsersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendUsersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendUsersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendUsersResponseMultiError, or nil if none found.
func (m *RecommendUsersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendUsersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendUsersResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendUsersResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendUsersResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecommendUsersResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecommendUsersResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecommendUsersResponseValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SortRequestId

	if len(errors) > 0 {
		return RecommendUsersResponseMultiError(errors)
	}

	return nil
}

// RecommendUsersResponseMultiError is an error wrapping multiple validation
// errors returned by RecommendUsersResponse.ValidateAll() if the designated
// constraints aren't met.
type RecommendUsersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendUsersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendUsersResponseMultiError) AllErrors() []error { return m }

// RecommendUsersResponseValidationError is the validation error returned by
// RecommendUsersResponse.Validate if the designated constraints aren't met.
type RecommendUsersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendUsersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendUsersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendUsersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendUsersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendUsersResponseValidationError) ErrorName() string {
	return "RecommendUsersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendUsersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendUsersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendUsersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendUsersResponseValidationError{}

// Validate checks the field values on RecommendUsersResponse_RecommendUser
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *RecommendUsersResponse_RecommendUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecommendUsersResponse_RecommendUser
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecommendUsersResponse_RecommendUserMultiError, or nil if none found.
func (m *RecommendUsersResponse_RecommendUser) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendUsersResponse_RecommendUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RecommendReason

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendUsersResponse_RecommendUserValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendUsersResponse_RecommendUserValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendUsersResponse_RecommendUserValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortRequestId

	if all {
		switch v := interface{}(m.GetRecommendReasonExtra()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecommendUsersResponse_RecommendUserValidationError{
					field:  "RecommendReasonExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecommendUsersResponse_RecommendUserValidationError{
					field:  "RecommendReasonExtra",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRecommendReasonExtra()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecommendUsersResponse_RecommendUserValidationError{
				field:  "RecommendReasonExtra",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RelatedStoryId

	if len(errors) > 0 {
		return RecommendUsersResponse_RecommendUserMultiError(errors)
	}

	return nil
}

// RecommendUsersResponse_RecommendUserMultiError is an error wrapping multiple
// validation errors returned by
// RecommendUsersResponse_RecommendUser.ValidateAll() if the designated
// constraints aren't met.
type RecommendUsersResponse_RecommendUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendUsersResponse_RecommendUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendUsersResponse_RecommendUserMultiError) AllErrors() []error { return m }

// RecommendUsersResponse_RecommendUserValidationError is the validation error
// returned by RecommendUsersResponse_RecommendUser.Validate if the designated
// constraints aren't met.
type RecommendUsersResponse_RecommendUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendUsersResponse_RecommendUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecommendUsersResponse_RecommendUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecommendUsersResponse_RecommendUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecommendUsersResponse_RecommendUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecommendUsersResponse_RecommendUserValidationError) ErrorName() string {
	return "RecommendUsersResponse_RecommendUserValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendUsersResponse_RecommendUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendUsersResponse_RecommendUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendUsersResponse_RecommendUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendUsersResponse_RecommendUserValidationError{}

// Validate checks the field values on
// RecommendUsersResponse_RecommendUser_RecommendReasonExtra with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// RecommendUsersResponse_RecommendUser_RecommendReasonExtra with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RecommendUsersResponse_RecommendUser_RecommendReasonExtraMultiError, or nil
// if none found.
func (m *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) ValidateAll() error {
	return m.validate(true)
}

func (m *RecommendUsersResponse_RecommendUser_RecommendReasonExtra) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ReasonType

	for idx, item := range m.GetRelatedUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError{
						field:  fmt.Sprintf("RelatedUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError{
						field:  fmt.Sprintf("RelatedUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError{
					field:  fmt.Sprintf("RelatedUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RecommendUsersResponse_RecommendUser_RecommendReasonExtraMultiError(errors)
	}

	return nil
}

// RecommendUsersResponse_RecommendUser_RecommendReasonExtraMultiError is an
// error wrapping multiple validation errors returned by
// RecommendUsersResponse_RecommendUser_RecommendReasonExtra.ValidateAll() if
// the designated constraints aren't met.
type RecommendUsersResponse_RecommendUser_RecommendReasonExtraMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecommendUsersResponse_RecommendUser_RecommendReasonExtraMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecommendUsersResponse_RecommendUser_RecommendReasonExtraMultiError) AllErrors() []error {
	return m
}

// RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError is
// the validation error returned by
// RecommendUsersResponse_RecommendUser_RecommendReasonExtra.Validate if the
// designated constraints aren't met.
type RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError) ErrorName() string {
	return "RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError"
}

// Error satisfies the builtin error interface
func (e RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecommendUsersResponse_RecommendUser_RecommendReasonExtra.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecommendUsersResponse_RecommendUser_RecommendReasonExtraValidationError{}
