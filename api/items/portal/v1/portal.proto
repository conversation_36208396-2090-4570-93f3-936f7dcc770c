syntax = "proto3";

package api.items.portal.v1;

import "api/resource/types/v1/types.proto";
import "api/items/portal/types/v1/types.proto";
import "api/items/portal/moments/types/v1/types.proto";
import "api/items/story/types/v1/base_types.proto";
import "api/items/story/types/v1/types.proto";
import "api/common/v1/common.proto";
import "validate/validate.proto";


option go_package = "boson/api/items/portal/v1;v1";

message DeleteMomentRequest {
	string moment_id = 1 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}



message CreateMomentRequest {
	api.resource.types.v1.Resource resource = 1 [(validate.rules).message = {required: true}];
	string story_id = 2 [(validate.rules).string = {	
		pattern: "^[0-9]+$",
	}];
	repeated api.items.story.types.v1.AttachmentText attachment_texts = 3 [(validate.rules).repeated = {min_items: 0}];
	// 从哪个 story 过来创建的
	optional string from_story_id = 4 [(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message CreateMomentResponse {
	api.items.portal.moments.types.v1.Moment moment = 1 [(validate.rules).message = {required: true}];
}

message CreateMomentRelationRequest {
	api.items.portal.moments.types.v1.RelationType relation_type = 1 [(validate.rules).enum = {defined_only: true}];
	string moment_id = 2 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}

message CreateMomentRelationResponse {
	api.items.portal.moments.types.v1.Moment moment = 1 [(validate.rules).message = {required: true}];
}

message RemoveMomentRelationRequest {		
	api.items.portal.moments.types.v1.RelationType relation_type = 1 [(validate.rules).enum = {defined_only: true}];
	string moment_id = 2 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}

message RemoveMomentRelationResponse {
	api.items.portal.moments.types.v1.Moment moment = 1 [(validate.rules).message = {required: true}];
}


message GetUserCreatedPortalsInfoRequest {
	optional string user_id = 2;
}

message GetUserCreatedPortalsInfoResponse {
	api.items.portal.types.v1.UserCreatedPortals user_created_portals = 1;
}


message ListUserCreatedPortalsWithTimeRangeRequest {
	// page token = 空串时为第一页，即为用户当前时区的今天的24点
	api.common.v1.ListRequest listReq = 1[(validate.rules).message = {required: true}];
	// 如果不传，则默认拉取登录用户
	optional string user_id = 2;
}

message ListUserCreatedPortalsWithTimeRangeResponse {
	message PortalsWithDate {
		// 这批 Portals 在用户时区的当日0点，客户端需要自行转化时间格式
		uint32 date_zero = 1;
		repeated api.items.portal.types.v1.Portal portals = 2;
	}
	repeated PortalsWithDate PortalsWithDates = 1;
	api.common.v1.ListResponse list_response = 2;
}



message ListMyPortalsRequest {
	api.common.v1.ListRequest list_request = 1 [(validate.rules).message = {required: true}];	
}

message ListMyPortalsResponse {
	// 当且仅当请求第一页时，服务端会下发登录用户的所有portals为 user_created_portals 
	api.items.portal.types.v1.UserCreatedPortals user_created_portals = 1;
	repeated api.items.portal.types.v1.Portal portals = 2;
	api.common.v1.ListResponse list_response = 3;
}


message ListCouldAppendMomentStoriesRequest {
	api.common.v1.ListRequest list_request = 1 [(validate.rules).message = {required: true}];
}

message ListCouldAppendMomentStoriesResponse {
	repeated api.items.story.types.v1.StoryDetail stories = 1;
	api.common.v1.ListResponse list_response = 2;
}

message ReportReadRequest {
	string moment_id = 1 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	string portal_id = 2 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}


message GetPortalRequest {
	string portal_id = 1 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}

message GetPortalResponse {
	api.items.portal.types.v1.Portal portal = 1 [(validate.rules).message = {required: true}];
}

message GetMomentRequest {
	string moment_id = 1 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}

message GetMomentResponse {
	api.items.portal.moments.types.v1.Moment moment = 1 [(validate.rules).message = {required: true}];
}

message SendMomentInviteRequest {
	// 邀请目标类型
	enum InviteTargetType {
		INVITE_TARGET_TYPE_UNSPECIFIED = 0;
		INVITE_TARGET_TYPE_STORY = 1;
		INVITE_TARGET_TYPE_MOMENT = 2;
	}
	InviteTargetType type = 1;
	optional string story_id = 2 [(validate.rules).string = { pattern: "^[0-9]+$" }];
	optional string moment_id = 3 [(validate.rules).string = { pattern: "^[0-9]+$" }];
	repeated string receiver_ids = 4 [(validate.rules).repeated.items.string = { pattern: "^[0-9]+$" }, (validate.rules).repeated.min_items = 1];
}

message SendMomentInviteResponse {

}

message ListMomentViewersRequest {
	string moment_id = 1 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	api.common.v1.ListRequest list_request = 2 [(validate.rules).message = {required: true}];
}

message ListMomentViewersResponse {
	repeated api.items.portal.moments.types.v1.Viewer viewers = 1;
	api.common.v1.ListResponse list_response = 2;
}

message ListTrendingPortalsRequest {
	api.common.v1.ListRequest list_request = 1 [(validate.rules).message = {required: true}];
}

message ListTrendingPortalsResponse {
	repeated api.items.portal.types.v1.Portal portals = 1;
	api.common.v1.ListResponse list_response = 2;
}