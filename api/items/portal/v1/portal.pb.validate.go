// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/portal/v1/portal.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	v1 "boson/api/items/portal/moments/types/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = v1.RelationType(0)
)

// Validate checks the field values on DeleteMomentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteMomentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteMomentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteMomentRequestMultiError, or nil if none found.
func (m *DeleteMomentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteMomentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteMomentRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
		err := DeleteMomentRequestValidationError{
			field:  "MomentId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteMomentRequestMultiError(errors)
	}

	return nil
}

// DeleteMomentRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteMomentRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteMomentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteMomentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteMomentRequestMultiError) AllErrors() []error { return m }

// DeleteMomentRequestValidationError is the validation error returned by
// DeleteMomentRequest.Validate if the designated constraints aren't met.
type DeleteMomentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteMomentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteMomentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteMomentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteMomentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteMomentRequestValidationError) ErrorName() string {
	return "DeleteMomentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteMomentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteMomentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteMomentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteMomentRequestValidationError{}

var _DeleteMomentRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateMomentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMomentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMomentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMomentRequestMultiError, or nil if none found.
func (m *CreateMomentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMomentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetResource() == nil {
		err := CreateMomentRequestValidationError{
			field:  "Resource",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMomentRequestValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMomentRequestValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMomentRequestValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if !_CreateMomentRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := CreateMomentRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateMomentRequestValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateMomentRequestValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateMomentRequestValidationError{
					field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.FromStoryId != nil {

		if !_CreateMomentRequest_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := CreateMomentRequestValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateMomentRequestMultiError(errors)
	}

	return nil
}

// CreateMomentRequestMultiError is an error wrapping multiple validation
// errors returned by CreateMomentRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateMomentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMomentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMomentRequestMultiError) AllErrors() []error { return m }

// CreateMomentRequestValidationError is the validation error returned by
// CreateMomentRequest.Validate if the designated constraints aren't met.
type CreateMomentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMomentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMomentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMomentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMomentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMomentRequestValidationError) ErrorName() string {
	return "CreateMomentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMomentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMomentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMomentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMomentRequestValidationError{}

var _CreateMomentRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

var _CreateMomentRequest_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateMomentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMomentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMomentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMomentResponseMultiError, or nil if none found.
func (m *CreateMomentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMomentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMoment() == nil {
		err := CreateMomentResponseValidationError{
			field:  "Moment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMoment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMomentResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMomentResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMomentResponseValidationError{
				field:  "Moment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateMomentResponseMultiError(errors)
	}

	return nil
}

// CreateMomentResponseMultiError is an error wrapping multiple validation
// errors returned by CreateMomentResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateMomentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMomentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMomentResponseMultiError) AllErrors() []error { return m }

// CreateMomentResponseValidationError is the validation error returned by
// CreateMomentResponse.Validate if the designated constraints aren't met.
type CreateMomentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMomentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMomentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMomentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMomentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMomentResponseValidationError) ErrorName() string {
	return "CreateMomentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMomentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMomentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMomentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMomentResponseValidationError{}

// Validate checks the field values on CreateMomentRelationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMomentRelationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMomentRelationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMomentRelationRequestMultiError, or nil if none found.
func (m *CreateMomentRelationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMomentRelationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := v1.RelationType_name[int32(m.GetRelationType())]; !ok {
		err := CreateMomentRelationRequestValidationError{
			field:  "RelationType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateMomentRelationRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
		err := CreateMomentRelationRequestValidationError{
			field:  "MomentId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateMomentRelationRequestMultiError(errors)
	}

	return nil
}

// CreateMomentRelationRequestMultiError is an error wrapping multiple
// validation errors returned by CreateMomentRelationRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateMomentRelationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMomentRelationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMomentRelationRequestMultiError) AllErrors() []error { return m }

// CreateMomentRelationRequestValidationError is the validation error returned
// by CreateMomentRelationRequest.Validate if the designated constraints
// aren't met.
type CreateMomentRelationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMomentRelationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMomentRelationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMomentRelationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMomentRelationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMomentRelationRequestValidationError) ErrorName() string {
	return "CreateMomentRelationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMomentRelationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMomentRelationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMomentRelationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMomentRelationRequestValidationError{}

var _CreateMomentRelationRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateMomentRelationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateMomentRelationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateMomentRelationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateMomentRelationResponseMultiError, or nil if none found.
func (m *CreateMomentRelationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateMomentRelationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMoment() == nil {
		err := CreateMomentRelationResponseValidationError{
			field:  "Moment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMoment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateMomentRelationResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateMomentRelationResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateMomentRelationResponseValidationError{
				field:  "Moment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateMomentRelationResponseMultiError(errors)
	}

	return nil
}

// CreateMomentRelationResponseMultiError is an error wrapping multiple
// validation errors returned by CreateMomentRelationResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateMomentRelationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateMomentRelationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateMomentRelationResponseMultiError) AllErrors() []error { return m }

// CreateMomentRelationResponseValidationError is the validation error returned
// by CreateMomentRelationResponse.Validate if the designated constraints
// aren't met.
type CreateMomentRelationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateMomentRelationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateMomentRelationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateMomentRelationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateMomentRelationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateMomentRelationResponseValidationError) ErrorName() string {
	return "CreateMomentRelationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateMomentRelationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateMomentRelationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateMomentRelationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateMomentRelationResponseValidationError{}

// Validate checks the field values on RemoveMomentRelationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveMomentRelationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveMomentRelationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveMomentRelationRequestMultiError, or nil if none found.
func (m *RemoveMomentRelationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveMomentRelationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := v1.RelationType_name[int32(m.GetRelationType())]; !ok {
		err := RemoveMomentRelationRequestValidationError{
			field:  "RelationType",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_RemoveMomentRelationRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
		err := RemoveMomentRelationRequestValidationError{
			field:  "MomentId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RemoveMomentRelationRequestMultiError(errors)
	}

	return nil
}

// RemoveMomentRelationRequestMultiError is an error wrapping multiple
// validation errors returned by RemoveMomentRelationRequest.ValidateAll() if
// the designated constraints aren't met.
type RemoveMomentRelationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveMomentRelationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveMomentRelationRequestMultiError) AllErrors() []error { return m }

// RemoveMomentRelationRequestValidationError is the validation error returned
// by RemoveMomentRelationRequest.Validate if the designated constraints
// aren't met.
type RemoveMomentRelationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveMomentRelationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveMomentRelationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveMomentRelationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveMomentRelationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveMomentRelationRequestValidationError) ErrorName() string {
	return "RemoveMomentRelationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveMomentRelationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveMomentRelationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveMomentRelationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveMomentRelationRequestValidationError{}

var _RemoveMomentRelationRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on RemoveMomentRelationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RemoveMomentRelationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RemoveMomentRelationResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RemoveMomentRelationResponseMultiError, or nil if none found.
func (m *RemoveMomentRelationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RemoveMomentRelationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMoment() == nil {
		err := RemoveMomentRelationResponseValidationError{
			field:  "Moment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMoment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RemoveMomentRelationResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RemoveMomentRelationResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RemoveMomentRelationResponseValidationError{
				field:  "Moment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RemoveMomentRelationResponseMultiError(errors)
	}

	return nil
}

// RemoveMomentRelationResponseMultiError is an error wrapping multiple
// validation errors returned by RemoveMomentRelationResponse.ValidateAll() if
// the designated constraints aren't met.
type RemoveMomentRelationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RemoveMomentRelationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RemoveMomentRelationResponseMultiError) AllErrors() []error { return m }

// RemoveMomentRelationResponseValidationError is the validation error returned
// by RemoveMomentRelationResponse.Validate if the designated constraints
// aren't met.
type RemoveMomentRelationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RemoveMomentRelationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RemoveMomentRelationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RemoveMomentRelationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RemoveMomentRelationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RemoveMomentRelationResponseValidationError) ErrorName() string {
	return "RemoveMomentRelationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RemoveMomentRelationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRemoveMomentRelationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RemoveMomentRelationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RemoveMomentRelationResponseValidationError{}

// Validate checks the field values on GetUserCreatedPortalsInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetUserCreatedPortalsInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCreatedPortalsInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetUserCreatedPortalsInfoRequestMultiError, or nil if none found.
func (m *GetUserCreatedPortalsInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCreatedPortalsInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.UserId != nil {
		// no validation rules for UserId
	}

	if len(errors) > 0 {
		return GetUserCreatedPortalsInfoRequestMultiError(errors)
	}

	return nil
}

// GetUserCreatedPortalsInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetUserCreatedPortalsInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserCreatedPortalsInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCreatedPortalsInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCreatedPortalsInfoRequestMultiError) AllErrors() []error { return m }

// GetUserCreatedPortalsInfoRequestValidationError is the validation error
// returned by GetUserCreatedPortalsInfoRequest.Validate if the designated
// constraints aren't met.
type GetUserCreatedPortalsInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCreatedPortalsInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCreatedPortalsInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCreatedPortalsInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCreatedPortalsInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCreatedPortalsInfoRequestValidationError) ErrorName() string {
	return "GetUserCreatedPortalsInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCreatedPortalsInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCreatedPortalsInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCreatedPortalsInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCreatedPortalsInfoRequestValidationError{}

// Validate checks the field values on GetUserCreatedPortalsInfoResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetUserCreatedPortalsInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserCreatedPortalsInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetUserCreatedPortalsInfoResponseMultiError, or nil if none found.
func (m *GetUserCreatedPortalsInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserCreatedPortalsInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserCreatedPortals()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUserCreatedPortalsInfoResponseValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUserCreatedPortalsInfoResponseValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserCreatedPortals()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUserCreatedPortalsInfoResponseValidationError{
				field:  "UserCreatedPortals",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUserCreatedPortalsInfoResponseMultiError(errors)
	}

	return nil
}

// GetUserCreatedPortalsInfoResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetUserCreatedPortalsInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetUserCreatedPortalsInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserCreatedPortalsInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserCreatedPortalsInfoResponseMultiError) AllErrors() []error { return m }

// GetUserCreatedPortalsInfoResponseValidationError is the validation error
// returned by GetUserCreatedPortalsInfoResponse.Validate if the designated
// constraints aren't met.
type GetUserCreatedPortalsInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserCreatedPortalsInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserCreatedPortalsInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserCreatedPortalsInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserCreatedPortalsInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserCreatedPortalsInfoResponseValidationError) ErrorName() string {
	return "GetUserCreatedPortalsInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserCreatedPortalsInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserCreatedPortalsInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserCreatedPortalsInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserCreatedPortalsInfoResponseValidationError{}

// Validate checks the field values on
// ListUserCreatedPortalsWithTimeRangeRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListUserCreatedPortalsWithTimeRangeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListUserCreatedPortalsWithTimeRangeRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListUserCreatedPortalsWithTimeRangeRequestMultiError, or nil if none found.
func (m *ListUserCreatedPortalsWithTimeRangeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserCreatedPortalsWithTimeRangeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListReq() == nil {
		err := ListUserCreatedPortalsWithTimeRangeRequestValidationError{
			field:  "ListReq",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUserCreatedPortalsWithTimeRangeRequestValidationError{
					field:  "ListReq",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUserCreatedPortalsWithTimeRangeRequestValidationError{
					field:  "ListReq",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUserCreatedPortalsWithTimeRangeRequestValidationError{
				field:  "ListReq",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.UserId != nil {
		// no validation rules for UserId
	}

	if len(errors) > 0 {
		return ListUserCreatedPortalsWithTimeRangeRequestMultiError(errors)
	}

	return nil
}

// ListUserCreatedPortalsWithTimeRangeRequestMultiError is an error wrapping
// multiple validation errors returned by
// ListUserCreatedPortalsWithTimeRangeRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUserCreatedPortalsWithTimeRangeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserCreatedPortalsWithTimeRangeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserCreatedPortalsWithTimeRangeRequestMultiError) AllErrors() []error { return m }

// ListUserCreatedPortalsWithTimeRangeRequestValidationError is the validation
// error returned by ListUserCreatedPortalsWithTimeRangeRequest.Validate if
// the designated constraints aren't met.
type ListUserCreatedPortalsWithTimeRangeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserCreatedPortalsWithTimeRangeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserCreatedPortalsWithTimeRangeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserCreatedPortalsWithTimeRangeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserCreatedPortalsWithTimeRangeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserCreatedPortalsWithTimeRangeRequestValidationError) ErrorName() string {
	return "ListUserCreatedPortalsWithTimeRangeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserCreatedPortalsWithTimeRangeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserCreatedPortalsWithTimeRangeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserCreatedPortalsWithTimeRangeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserCreatedPortalsWithTimeRangeRequestValidationError{}

// Validate checks the field values on
// ListUserCreatedPortalsWithTimeRangeResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListUserCreatedPortalsWithTimeRangeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListUserCreatedPortalsWithTimeRangeResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListUserCreatedPortalsWithTimeRangeResponseMultiError, or nil if none found.
func (m *ListUserCreatedPortalsWithTimeRangeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserCreatedPortalsWithTimeRangeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPortalsWithDates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserCreatedPortalsWithTimeRangeResponseValidationError{
						field:  fmt.Sprintf("PortalsWithDates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserCreatedPortalsWithTimeRangeResponseValidationError{
						field:  fmt.Sprintf("PortalsWithDates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserCreatedPortalsWithTimeRangeResponseValidationError{
					field:  fmt.Sprintf("PortalsWithDates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUserCreatedPortalsWithTimeRangeResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUserCreatedPortalsWithTimeRangeResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUserCreatedPortalsWithTimeRangeResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListUserCreatedPortalsWithTimeRangeResponseMultiError(errors)
	}

	return nil
}

// ListUserCreatedPortalsWithTimeRangeResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListUserCreatedPortalsWithTimeRangeResponse.ValidateAll() if the designated
// constraints aren't met.
type ListUserCreatedPortalsWithTimeRangeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserCreatedPortalsWithTimeRangeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserCreatedPortalsWithTimeRangeResponseMultiError) AllErrors() []error { return m }

// ListUserCreatedPortalsWithTimeRangeResponseValidationError is the validation
// error returned by ListUserCreatedPortalsWithTimeRangeResponse.Validate if
// the designated constraints aren't met.
type ListUserCreatedPortalsWithTimeRangeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserCreatedPortalsWithTimeRangeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUserCreatedPortalsWithTimeRangeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUserCreatedPortalsWithTimeRangeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUserCreatedPortalsWithTimeRangeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUserCreatedPortalsWithTimeRangeResponseValidationError) ErrorName() string {
	return "ListUserCreatedPortalsWithTimeRangeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserCreatedPortalsWithTimeRangeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserCreatedPortalsWithTimeRangeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserCreatedPortalsWithTimeRangeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserCreatedPortalsWithTimeRangeResponseValidationError{}

// Validate checks the field values on ListMyPortalsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMyPortalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMyPortalsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMyPortalsRequestMultiError, or nil if none found.
func (m *ListMyPortalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMyPortalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListMyPortalsRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMyPortalsRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMyPortalsRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMyPortalsRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMyPortalsRequestMultiError(errors)
	}

	return nil
}

// ListMyPortalsRequestMultiError is an error wrapping multiple validation
// errors returned by ListMyPortalsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMyPortalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMyPortalsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMyPortalsRequestMultiError) AllErrors() []error { return m }

// ListMyPortalsRequestValidationError is the validation error returned by
// ListMyPortalsRequest.Validate if the designated constraints aren't met.
type ListMyPortalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMyPortalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMyPortalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMyPortalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMyPortalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMyPortalsRequestValidationError) ErrorName() string {
	return "ListMyPortalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMyPortalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMyPortalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMyPortalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMyPortalsRequestValidationError{}

// Validate checks the field values on ListMyPortalsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMyPortalsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMyPortalsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMyPortalsResponseMultiError, or nil if none found.
func (m *ListMyPortalsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMyPortalsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUserCreatedPortals()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMyPortalsResponseValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMyPortalsResponseValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserCreatedPortals()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMyPortalsResponseValidationError{
				field:  "UserCreatedPortals",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPortals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMyPortalsResponseValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMyPortalsResponseValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMyPortalsResponseValidationError{
					field:  fmt.Sprintf("Portals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMyPortalsResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMyPortalsResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMyPortalsResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMyPortalsResponseMultiError(errors)
	}

	return nil
}

// ListMyPortalsResponseMultiError is an error wrapping multiple validation
// errors returned by ListMyPortalsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListMyPortalsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMyPortalsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMyPortalsResponseMultiError) AllErrors() []error { return m }

// ListMyPortalsResponseValidationError is the validation error returned by
// ListMyPortalsResponse.Validate if the designated constraints aren't met.
type ListMyPortalsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMyPortalsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMyPortalsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMyPortalsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMyPortalsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMyPortalsResponseValidationError) ErrorName() string {
	return "ListMyPortalsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListMyPortalsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMyPortalsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMyPortalsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMyPortalsResponseValidationError{}

// Validate checks the field values on ListCouldAppendMomentStoriesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListCouldAppendMomentStoriesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCouldAppendMomentStoriesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListCouldAppendMomentStoriesRequestMultiError, or nil if none found.
func (m *ListCouldAppendMomentStoriesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCouldAppendMomentStoriesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListCouldAppendMomentStoriesRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCouldAppendMomentStoriesRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCouldAppendMomentStoriesRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCouldAppendMomentStoriesRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCouldAppendMomentStoriesRequestMultiError(errors)
	}

	return nil
}

// ListCouldAppendMomentStoriesRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListCouldAppendMomentStoriesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCouldAppendMomentStoriesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCouldAppendMomentStoriesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCouldAppendMomentStoriesRequestMultiError) AllErrors() []error { return m }

// ListCouldAppendMomentStoriesRequestValidationError is the validation error
// returned by ListCouldAppendMomentStoriesRequest.Validate if the designated
// constraints aren't met.
type ListCouldAppendMomentStoriesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCouldAppendMomentStoriesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCouldAppendMomentStoriesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCouldAppendMomentStoriesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCouldAppendMomentStoriesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCouldAppendMomentStoriesRequestValidationError) ErrorName() string {
	return "ListCouldAppendMomentStoriesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCouldAppendMomentStoriesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCouldAppendMomentStoriesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCouldAppendMomentStoriesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCouldAppendMomentStoriesRequestValidationError{}

// Validate checks the field values on ListCouldAppendMomentStoriesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCouldAppendMomentStoriesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCouldAppendMomentStoriesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListCouldAppendMomentStoriesResponseMultiError, or nil if none found.
func (m *ListCouldAppendMomentStoriesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCouldAppendMomentStoriesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCouldAppendMomentStoriesResponseValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCouldAppendMomentStoriesResponseValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCouldAppendMomentStoriesResponseValidationError{
					field:  fmt.Sprintf("Stories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCouldAppendMomentStoriesResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCouldAppendMomentStoriesResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCouldAppendMomentStoriesResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCouldAppendMomentStoriesResponseMultiError(errors)
	}

	return nil
}

// ListCouldAppendMomentStoriesResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListCouldAppendMomentStoriesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCouldAppendMomentStoriesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCouldAppendMomentStoriesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCouldAppendMomentStoriesResponseMultiError) AllErrors() []error { return m }

// ListCouldAppendMomentStoriesResponseValidationError is the validation error
// returned by ListCouldAppendMomentStoriesResponse.Validate if the designated
// constraints aren't met.
type ListCouldAppendMomentStoriesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCouldAppendMomentStoriesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCouldAppendMomentStoriesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCouldAppendMomentStoriesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCouldAppendMomentStoriesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCouldAppendMomentStoriesResponseValidationError) ErrorName() string {
	return "ListCouldAppendMomentStoriesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCouldAppendMomentStoriesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCouldAppendMomentStoriesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCouldAppendMomentStoriesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCouldAppendMomentStoriesResponseValidationError{}

// Validate checks the field values on ReportReadRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReportReadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportReadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportReadRequestMultiError, or nil if none found.
func (m *ReportReadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportReadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ReportReadRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
		err := ReportReadRequestValidationError{
			field:  "MomentId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ReportReadRequest_PortalId_Pattern.MatchString(m.GetPortalId()) {
		err := ReportReadRequestValidationError{
			field:  "PortalId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReportReadRequestMultiError(errors)
	}

	return nil
}

// ReportReadRequestMultiError is an error wrapping multiple validation errors
// returned by ReportReadRequest.ValidateAll() if the designated constraints
// aren't met.
type ReportReadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportReadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportReadRequestMultiError) AllErrors() []error { return m }

// ReportReadRequestValidationError is the validation error returned by
// ReportReadRequest.Validate if the designated constraints aren't met.
type ReportReadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportReadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportReadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportReadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportReadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportReadRequestValidationError) ErrorName() string {
	return "ReportReadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReportReadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportReadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportReadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportReadRequestValidationError{}

var _ReportReadRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

var _ReportReadRequest_PortalId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on GetPortalRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPortalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPortalRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPortalRequestMultiError, or nil if none found.
func (m *GetPortalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetPortalRequest_PortalId_Pattern.MatchString(m.GetPortalId()) {
		err := GetPortalRequestValidationError{
			field:  "PortalId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetPortalRequestMultiError(errors)
	}

	return nil
}

// GetPortalRequestMultiError is an error wrapping multiple validation errors
// returned by GetPortalRequest.ValidateAll() if the designated constraints
// aren't met.
type GetPortalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortalRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortalRequestMultiError) AllErrors() []error { return m }

// GetPortalRequestValidationError is the validation error returned by
// GetPortalRequest.Validate if the designated constraints aren't met.
type GetPortalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortalRequestValidationError) ErrorName() string { return "GetPortalRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetPortalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortalRequestValidationError{}

var _GetPortalRequest_PortalId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on GetPortalResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetPortalResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPortalResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPortalResponseMultiError, or nil if none found.
func (m *GetPortalResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPortalResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPortal() == nil {
		err := GetPortalResponseValidationError{
			field:  "Portal",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPortal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPortalResponseValidationError{
					field:  "Portal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPortalResponseValidationError{
					field:  "Portal",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPortalResponseValidationError{
				field:  "Portal",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPortalResponseMultiError(errors)
	}

	return nil
}

// GetPortalResponseMultiError is an error wrapping multiple validation errors
// returned by GetPortalResponse.ValidateAll() if the designated constraints
// aren't met.
type GetPortalResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPortalResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPortalResponseMultiError) AllErrors() []error { return m }

// GetPortalResponseValidationError is the validation error returned by
// GetPortalResponse.Validate if the designated constraints aren't met.
type GetPortalResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPortalResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPortalResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPortalResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPortalResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPortalResponseValidationError) ErrorName() string {
	return "GetPortalResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPortalResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPortalResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPortalResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPortalResponseValidationError{}

// Validate checks the field values on GetMomentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMomentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMomentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMomentRequestMultiError, or nil if none found.
func (m *GetMomentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMomentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetMomentRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
		err := GetMomentRequestValidationError{
			field:  "MomentId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetMomentRequestMultiError(errors)
	}

	return nil
}

// GetMomentRequestMultiError is an error wrapping multiple validation errors
// returned by GetMomentRequest.ValidateAll() if the designated constraints
// aren't met.
type GetMomentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMomentRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMomentRequestMultiError) AllErrors() []error { return m }

// GetMomentRequestValidationError is the validation error returned by
// GetMomentRequest.Validate if the designated constraints aren't met.
type GetMomentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMomentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMomentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMomentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMomentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMomentRequestValidationError) ErrorName() string { return "GetMomentRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetMomentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMomentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMomentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMomentRequestValidationError{}

var _GetMomentRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on GetMomentResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetMomentResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMomentResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetMomentResponseMultiError, or nil if none found.
func (m *GetMomentResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMomentResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetMoment() == nil {
		err := GetMomentResponseValidationError{
			field:  "Moment",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMoment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMomentResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMomentResponseValidationError{
					field:  "Moment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMoment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMomentResponseValidationError{
				field:  "Moment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetMomentResponseMultiError(errors)
	}

	return nil
}

// GetMomentResponseMultiError is an error wrapping multiple validation errors
// returned by GetMomentResponse.ValidateAll() if the designated constraints
// aren't met.
type GetMomentResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMomentResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMomentResponseMultiError) AllErrors() []error { return m }

// GetMomentResponseValidationError is the validation error returned by
// GetMomentResponse.Validate if the designated constraints aren't met.
type GetMomentResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMomentResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMomentResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMomentResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMomentResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMomentResponseValidationError) ErrorName() string {
	return "GetMomentResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMomentResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMomentResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMomentResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMomentResponseValidationError{}

// Validate checks the field values on SendMomentInviteRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendMomentInviteRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMomentInviteRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendMomentInviteRequestMultiError, or nil if none found.
func (m *SendMomentInviteRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMomentInviteRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if len(m.GetReceiverIds()) < 1 {
		err := SendMomentInviteRequestValidationError{
			field:  "ReceiverIds",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetReceiverIds() {
		_, _ = idx, item

		if !_SendMomentInviteRequest_ReceiverIds_Pattern.MatchString(item) {
			err := SendMomentInviteRequestValidationError{
				field:  fmt.Sprintf("ReceiverIds[%v]", idx),
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.StoryId != nil {

		if !_SendMomentInviteRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
			err := SendMomentInviteRequestValidationError{
				field:  "StoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.MomentId != nil {

		if !_SendMomentInviteRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
			err := SendMomentInviteRequestValidationError{
				field:  "MomentId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return SendMomentInviteRequestMultiError(errors)
	}

	return nil
}

// SendMomentInviteRequestMultiError is an error wrapping multiple validation
// errors returned by SendMomentInviteRequest.ValidateAll() if the designated
// constraints aren't met.
type SendMomentInviteRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMomentInviteRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMomentInviteRequestMultiError) AllErrors() []error { return m }

// SendMomentInviteRequestValidationError is the validation error returned by
// SendMomentInviteRequest.Validate if the designated constraints aren't met.
type SendMomentInviteRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMomentInviteRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMomentInviteRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMomentInviteRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMomentInviteRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMomentInviteRequestValidationError) ErrorName() string {
	return "SendMomentInviteRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendMomentInviteRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMomentInviteRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMomentInviteRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMomentInviteRequestValidationError{}

var _SendMomentInviteRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

var _SendMomentInviteRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

var _SendMomentInviteRequest_ReceiverIds_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on SendMomentInviteResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendMomentInviteResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMomentInviteResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendMomentInviteResponseMultiError, or nil if none found.
func (m *SendMomentInviteResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMomentInviteResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SendMomentInviteResponseMultiError(errors)
	}

	return nil
}

// SendMomentInviteResponseMultiError is an error wrapping multiple validation
// errors returned by SendMomentInviteResponse.ValidateAll() if the designated
// constraints aren't met.
type SendMomentInviteResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMomentInviteResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMomentInviteResponseMultiError) AllErrors() []error { return m }

// SendMomentInviteResponseValidationError is the validation error returned by
// SendMomentInviteResponse.Validate if the designated constraints aren't met.
type SendMomentInviteResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMomentInviteResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMomentInviteResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMomentInviteResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMomentInviteResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMomentInviteResponseValidationError) ErrorName() string {
	return "SendMomentInviteResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendMomentInviteResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMomentInviteResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMomentInviteResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMomentInviteResponseValidationError{}

// Validate checks the field values on ListMomentViewersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMomentViewersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMomentViewersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMomentViewersRequestMultiError, or nil if none found.
func (m *ListMomentViewersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMomentViewersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ListMomentViewersRequest_MomentId_Pattern.MatchString(m.GetMomentId()) {
		err := ListMomentViewersRequestValidationError{
			field:  "MomentId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetListRequest() == nil {
		err := ListMomentViewersRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMomentViewersRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMomentViewersRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMomentViewersRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMomentViewersRequestMultiError(errors)
	}

	return nil
}

// ListMomentViewersRequestMultiError is an error wrapping multiple validation
// errors returned by ListMomentViewersRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMomentViewersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMomentViewersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMomentViewersRequestMultiError) AllErrors() []error { return m }

// ListMomentViewersRequestValidationError is the validation error returned by
// ListMomentViewersRequest.Validate if the designated constraints aren't met.
type ListMomentViewersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMomentViewersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMomentViewersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMomentViewersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMomentViewersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMomentViewersRequestValidationError) ErrorName() string {
	return "ListMomentViewersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMomentViewersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMomentViewersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMomentViewersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMomentViewersRequestValidationError{}

var _ListMomentViewersRequest_MomentId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ListMomentViewersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMomentViewersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMomentViewersResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMomentViewersResponseMultiError, or nil if none found.
func (m *ListMomentViewersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMomentViewersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetViewers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListMomentViewersResponseValidationError{
						field:  fmt.Sprintf("Viewers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListMomentViewersResponseValidationError{
						field:  fmt.Sprintf("Viewers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListMomentViewersResponseValidationError{
					field:  fmt.Sprintf("Viewers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListMomentViewersResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListMomentViewersResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListMomentViewersResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListMomentViewersResponseMultiError(errors)
	}

	return nil
}

// ListMomentViewersResponseMultiError is an error wrapping multiple validation
// errors returned by ListMomentViewersResponse.ValidateAll() if the
// designated constraints aren't met.
type ListMomentViewersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMomentViewersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMomentViewersResponseMultiError) AllErrors() []error { return m }

// ListMomentViewersResponseValidationError is the validation error returned by
// ListMomentViewersResponse.Validate if the designated constraints aren't met.
type ListMomentViewersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMomentViewersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMomentViewersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMomentViewersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMomentViewersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMomentViewersResponseValidationError) ErrorName() string {
	return "ListMomentViewersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListMomentViewersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMomentViewersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMomentViewersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMomentViewersResponseValidationError{}

// Validate checks the field values on ListTrendingPortalsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTrendingPortalsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTrendingPortalsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTrendingPortalsRequestMultiError, or nil if none found.
func (m *ListTrendingPortalsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTrendingPortalsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListTrendingPortalsRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTrendingPortalsRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTrendingPortalsRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTrendingPortalsRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListTrendingPortalsRequestMultiError(errors)
	}

	return nil
}

// ListTrendingPortalsRequestMultiError is an error wrapping multiple
// validation errors returned by ListTrendingPortalsRequest.ValidateAll() if
// the designated constraints aren't met.
type ListTrendingPortalsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTrendingPortalsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTrendingPortalsRequestMultiError) AllErrors() []error { return m }

// ListTrendingPortalsRequestValidationError is the validation error returned
// by ListTrendingPortalsRequest.Validate if the designated constraints aren't met.
type ListTrendingPortalsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTrendingPortalsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTrendingPortalsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTrendingPortalsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTrendingPortalsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTrendingPortalsRequestValidationError) ErrorName() string {
	return "ListTrendingPortalsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTrendingPortalsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTrendingPortalsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTrendingPortalsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTrendingPortalsRequestValidationError{}

// Validate checks the field values on ListTrendingPortalsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTrendingPortalsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTrendingPortalsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTrendingPortalsResponseMultiError, or nil if none found.
func (m *ListTrendingPortalsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTrendingPortalsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPortals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTrendingPortalsResponseValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTrendingPortalsResponseValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTrendingPortalsResponseValidationError{
					field:  fmt.Sprintf("Portals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListTrendingPortalsResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListTrendingPortalsResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListTrendingPortalsResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListTrendingPortalsResponseMultiError(errors)
	}

	return nil
}

// ListTrendingPortalsResponseMultiError is an error wrapping multiple
// validation errors returned by ListTrendingPortalsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListTrendingPortalsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTrendingPortalsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTrendingPortalsResponseMultiError) AllErrors() []error { return m }

// ListTrendingPortalsResponseValidationError is the validation error returned
// by ListTrendingPortalsResponse.Validate if the designated constraints
// aren't met.
type ListTrendingPortalsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTrendingPortalsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTrendingPortalsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTrendingPortalsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTrendingPortalsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTrendingPortalsResponseValidationError) ErrorName() string {
	return "ListTrendingPortalsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTrendingPortalsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTrendingPortalsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTrendingPortalsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTrendingPortalsResponseValidationError{}

// Validate checks the field values on
// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateMultiError, or
// nil if none found.
func (m *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DateZero

	for idx, item := range m.GetPortals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError{
					field:  fmt.Sprintf("Portals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateMultiError(errors)
	}

	return nil
}

// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateMultiError is an
// error wrapping multiple validation errors returned by
// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate.ValidateAll()
// if the designated constraints aren't met.
type ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateMultiError) AllErrors() []error {
	return m
}

// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError
// is the validation error returned by
// ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate.Validate if the
// designated constraints aren't met.
type ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError) ErrorName() string {
	return "ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError"
}

// Error satisfies the builtin error interface
func (e ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDateValidationError{}
