// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/portal/v1/portal.proto

package v1

import (
	v14 "boson/api/common/v1"
	v12 "boson/api/items/portal/moments/types/v1"
	v13 "boson/api/items/portal/types/v1"
	v11 "boson/api/items/story/types/v1"
	v1 "boson/api/resource/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 邀请目标类型
type SendMomentInviteRequest_InviteTargetType int32

const (
	SendMomentInviteRequest_INVITE_TARGET_TYPE_UNSPECIFIED SendMomentInviteRequest_InviteTargetType = 0
	SendMomentInviteRequest_INVITE_TARGET_TYPE_STORY       SendMomentInviteRequest_InviteTargetType = 1
	SendMomentInviteRequest_INVITE_TARGET_TYPE_MOMENT      SendMomentInviteRequest_InviteTargetType = 2
)

// Enum value maps for SendMomentInviteRequest_InviteTargetType.
var (
	SendMomentInviteRequest_InviteTargetType_name = map[int32]string{
		0: "INVITE_TARGET_TYPE_UNSPECIFIED",
		1: "INVITE_TARGET_TYPE_STORY",
		2: "INVITE_TARGET_TYPE_MOMENT",
	}
	SendMomentInviteRequest_InviteTargetType_value = map[string]int32{
		"INVITE_TARGET_TYPE_UNSPECIFIED": 0,
		"INVITE_TARGET_TYPE_STORY":       1,
		"INVITE_TARGET_TYPE_MOMENT":      2,
	}
)

func (x SendMomentInviteRequest_InviteTargetType) Enum() *SendMomentInviteRequest_InviteTargetType {
	p := new(SendMomentInviteRequest_InviteTargetType)
	*p = x
	return p
}

func (x SendMomentInviteRequest_InviteTargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SendMomentInviteRequest_InviteTargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_portal_v1_portal_proto_enumTypes[0].Descriptor()
}

func (SendMomentInviteRequest_InviteTargetType) Type() protoreflect.EnumType {
	return &file_api_items_portal_v1_portal_proto_enumTypes[0]
}

func (x SendMomentInviteRequest_InviteTargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SendMomentInviteRequest_InviteTargetType.Descriptor instead.
func (SendMomentInviteRequest_InviteTargetType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{20, 0}
}

type DeleteMomentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MomentId      string                 `protobuf:"bytes,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMomentRequest) Reset() {
	*x = DeleteMomentRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMomentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMomentRequest) ProtoMessage() {}

func (x *DeleteMomentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMomentRequest.ProtoReflect.Descriptor instead.
func (*DeleteMomentRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteMomentRequest) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

type CreateMomentRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Resource        *v1.Resource           `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	StoryId         string                 `protobuf:"bytes,2,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	AttachmentTexts []*v11.AttachmentText  `protobuf:"bytes,3,rep,name=attachment_texts,json=attachmentTexts,proto3" json:"attachment_texts,omitempty"`
	// 从哪个 story 过来创建的
	FromStoryId   *string `protobuf:"bytes,4,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMomentRequest) Reset() {
	*x = CreateMomentRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMomentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMomentRequest) ProtoMessage() {}

func (x *CreateMomentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMomentRequest.ProtoReflect.Descriptor instead.
func (*CreateMomentRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{1}
}

func (x *CreateMomentRequest) GetResource() *v1.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *CreateMomentRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *CreateMomentRequest) GetAttachmentTexts() []*v11.AttachmentText {
	if x != nil {
		return x.AttachmentTexts
	}
	return nil
}

func (x *CreateMomentRequest) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

type CreateMomentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Moment        *v12.Moment            `protobuf:"bytes,1,opt,name=moment,proto3" json:"moment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMomentResponse) Reset() {
	*x = CreateMomentResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMomentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMomentResponse) ProtoMessage() {}

func (x *CreateMomentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMomentResponse.ProtoReflect.Descriptor instead.
func (*CreateMomentResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{2}
}

func (x *CreateMomentResponse) GetMoment() *v12.Moment {
	if x != nil {
		return x.Moment
	}
	return nil
}

type CreateMomentRelationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RelationType  v12.RelationType       `protobuf:"varint,1,opt,name=relation_type,json=relationType,proto3,enum=api.items.portal.moments.types.v1.RelationType" json:"relation_type,omitempty"`
	MomentId      string                 `protobuf:"bytes,2,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMomentRelationRequest) Reset() {
	*x = CreateMomentRelationRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMomentRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMomentRelationRequest) ProtoMessage() {}

func (x *CreateMomentRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMomentRelationRequest.ProtoReflect.Descriptor instead.
func (*CreateMomentRelationRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{3}
}

func (x *CreateMomentRelationRequest) GetRelationType() v12.RelationType {
	if x != nil {
		return x.RelationType
	}
	return v12.RelationType(0)
}

func (x *CreateMomentRelationRequest) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

type CreateMomentRelationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Moment        *v12.Moment            `protobuf:"bytes,1,opt,name=moment,proto3" json:"moment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMomentRelationResponse) Reset() {
	*x = CreateMomentRelationResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMomentRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMomentRelationResponse) ProtoMessage() {}

func (x *CreateMomentRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMomentRelationResponse.ProtoReflect.Descriptor instead.
func (*CreateMomentRelationResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{4}
}

func (x *CreateMomentRelationResponse) GetMoment() *v12.Moment {
	if x != nil {
		return x.Moment
	}
	return nil
}

type RemoveMomentRelationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RelationType  v12.RelationType       `protobuf:"varint,1,opt,name=relation_type,json=relationType,proto3,enum=api.items.portal.moments.types.v1.RelationType" json:"relation_type,omitempty"`
	MomentId      string                 `protobuf:"bytes,2,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveMomentRelationRequest) Reset() {
	*x = RemoveMomentRelationRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveMomentRelationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveMomentRelationRequest) ProtoMessage() {}

func (x *RemoveMomentRelationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveMomentRelationRequest.ProtoReflect.Descriptor instead.
func (*RemoveMomentRelationRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{5}
}

func (x *RemoveMomentRelationRequest) GetRelationType() v12.RelationType {
	if x != nil {
		return x.RelationType
	}
	return v12.RelationType(0)
}

func (x *RemoveMomentRelationRequest) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

type RemoveMomentRelationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Moment        *v12.Moment            `protobuf:"bytes,1,opt,name=moment,proto3" json:"moment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveMomentRelationResponse) Reset() {
	*x = RemoveMomentRelationResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveMomentRelationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveMomentRelationResponse) ProtoMessage() {}

func (x *RemoveMomentRelationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveMomentRelationResponse.ProtoReflect.Descriptor instead.
func (*RemoveMomentRelationResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{6}
}

func (x *RemoveMomentRelationResponse) GetMoment() *v12.Moment {
	if x != nil {
		return x.Moment
	}
	return nil
}

type GetUserCreatedPortalsInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        *string                `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserCreatedPortalsInfoRequest) Reset() {
	*x = GetUserCreatedPortalsInfoRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserCreatedPortalsInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCreatedPortalsInfoRequest) ProtoMessage() {}

func (x *GetUserCreatedPortalsInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCreatedPortalsInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserCreatedPortalsInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserCreatedPortalsInfoRequest) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

type GetUserCreatedPortalsInfoResponse struct {
	state              protoimpl.MessageState  `protogen:"open.v1"`
	UserCreatedPortals *v13.UserCreatedPortals `protobuf:"bytes,1,opt,name=user_created_portals,json=userCreatedPortals,proto3" json:"user_created_portals,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetUserCreatedPortalsInfoResponse) Reset() {
	*x = GetUserCreatedPortalsInfoResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserCreatedPortalsInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserCreatedPortalsInfoResponse) ProtoMessage() {}

func (x *GetUserCreatedPortalsInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserCreatedPortalsInfoResponse.ProtoReflect.Descriptor instead.
func (*GetUserCreatedPortalsInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserCreatedPortalsInfoResponse) GetUserCreatedPortals() *v13.UserCreatedPortals {
	if x != nil {
		return x.UserCreatedPortals
	}
	return nil
}

type ListUserCreatedPortalsWithTimeRangeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// page token = 空串时为第一页，即为用户当前时区的今天的24点
	ListReq *v14.ListRequest `protobuf:"bytes,1,opt,name=listReq,proto3" json:"listReq,omitempty"`
	// 如果不传，则默认拉取登录用户
	UserId        *string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserCreatedPortalsWithTimeRangeRequest) Reset() {
	*x = ListUserCreatedPortalsWithTimeRangeRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserCreatedPortalsWithTimeRangeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserCreatedPortalsWithTimeRangeRequest) ProtoMessage() {}

func (x *ListUserCreatedPortalsWithTimeRangeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserCreatedPortalsWithTimeRangeRequest.ProtoReflect.Descriptor instead.
func (*ListUserCreatedPortalsWithTimeRangeRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{9}
}

func (x *ListUserCreatedPortalsWithTimeRangeRequest) GetListReq() *v14.ListRequest {
	if x != nil {
		return x.ListReq
	}
	return nil
}

func (x *ListUserCreatedPortalsWithTimeRangeRequest) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

type ListUserCreatedPortalsWithTimeRangeResponse struct {
	state            protoimpl.MessageState                                         `protogen:"open.v1"`
	PortalsWithDates []*ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate `protobuf:"bytes,1,rep,name=PortalsWithDates,proto3" json:"PortalsWithDates,omitempty"`
	ListResponse     *v14.ListResponse                                              `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse) Reset() {
	*x = ListUserCreatedPortalsWithTimeRangeResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserCreatedPortalsWithTimeRangeResponse) ProtoMessage() {}

func (x *ListUserCreatedPortalsWithTimeRangeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserCreatedPortalsWithTimeRangeResponse.ProtoReflect.Descriptor instead.
func (*ListUserCreatedPortalsWithTimeRangeResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{10}
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse) GetPortalsWithDates() []*ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate {
	if x != nil {
		return x.PortalsWithDates
	}
	return nil
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse) GetListResponse() *v14.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ListMyPortalsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v14.ListRequest       `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMyPortalsRequest) Reset() {
	*x = ListMyPortalsRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMyPortalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMyPortalsRequest) ProtoMessage() {}

func (x *ListMyPortalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMyPortalsRequest.ProtoReflect.Descriptor instead.
func (*ListMyPortalsRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{11}
}

func (x *ListMyPortalsRequest) GetListRequest() *v14.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListMyPortalsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当且仅当请求第一页时，服务端会下发登录用户的所有portals为 user_created_portals
	UserCreatedPortals *v13.UserCreatedPortals `protobuf:"bytes,1,opt,name=user_created_portals,json=userCreatedPortals,proto3" json:"user_created_portals,omitempty"`
	Portals            []*v13.Portal           `protobuf:"bytes,2,rep,name=portals,proto3" json:"portals,omitempty"`
	ListResponse       *v14.ListResponse       `protobuf:"bytes,3,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListMyPortalsResponse) Reset() {
	*x = ListMyPortalsResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMyPortalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMyPortalsResponse) ProtoMessage() {}

func (x *ListMyPortalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMyPortalsResponse.ProtoReflect.Descriptor instead.
func (*ListMyPortalsResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{12}
}

func (x *ListMyPortalsResponse) GetUserCreatedPortals() *v13.UserCreatedPortals {
	if x != nil {
		return x.UserCreatedPortals
	}
	return nil
}

func (x *ListMyPortalsResponse) GetPortals() []*v13.Portal {
	if x != nil {
		return x.Portals
	}
	return nil
}

func (x *ListMyPortalsResponse) GetListResponse() *v14.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ListCouldAppendMomentStoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v14.ListRequest       `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCouldAppendMomentStoriesRequest) Reset() {
	*x = ListCouldAppendMomentStoriesRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCouldAppendMomentStoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCouldAppendMomentStoriesRequest) ProtoMessage() {}

func (x *ListCouldAppendMomentStoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCouldAppendMomentStoriesRequest.ProtoReflect.Descriptor instead.
func (*ListCouldAppendMomentStoriesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{13}
}

func (x *ListCouldAppendMomentStoriesRequest) GetListRequest() *v14.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListCouldAppendMomentStoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stories       []*v11.StoryDetail     `protobuf:"bytes,1,rep,name=stories,proto3" json:"stories,omitempty"`
	ListResponse  *v14.ListResponse      `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCouldAppendMomentStoriesResponse) Reset() {
	*x = ListCouldAppendMomentStoriesResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCouldAppendMomentStoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCouldAppendMomentStoriesResponse) ProtoMessage() {}

func (x *ListCouldAppendMomentStoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCouldAppendMomentStoriesResponse.ProtoReflect.Descriptor instead.
func (*ListCouldAppendMomentStoriesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{14}
}

func (x *ListCouldAppendMomentStoriesResponse) GetStories() []*v11.StoryDetail {
	if x != nil {
		return x.Stories
	}
	return nil
}

func (x *ListCouldAppendMomentStoriesResponse) GetListResponse() *v14.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ReportReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MomentId      string                 `protobuf:"bytes,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	PortalId      string                 `protobuf:"bytes,2,opt,name=portal_id,json=portalId,proto3" json:"portal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportReadRequest) Reset() {
	*x = ReportReadRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportReadRequest) ProtoMessage() {}

func (x *ReportReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportReadRequest.ProtoReflect.Descriptor instead.
func (*ReportReadRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{15}
}

func (x *ReportReadRequest) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

func (x *ReportReadRequest) GetPortalId() string {
	if x != nil {
		return x.PortalId
	}
	return ""
}

type GetPortalRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PortalId      string                 `protobuf:"bytes,1,opt,name=portal_id,json=portalId,proto3" json:"portal_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPortalRequest) Reset() {
	*x = GetPortalRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPortalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortalRequest) ProtoMessage() {}

func (x *GetPortalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortalRequest.ProtoReflect.Descriptor instead.
func (*GetPortalRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{16}
}

func (x *GetPortalRequest) GetPortalId() string {
	if x != nil {
		return x.PortalId
	}
	return ""
}

type GetPortalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Portal        *v13.Portal            `protobuf:"bytes,1,opt,name=portal,proto3" json:"portal,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPortalResponse) Reset() {
	*x = GetPortalResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPortalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPortalResponse) ProtoMessage() {}

func (x *GetPortalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPortalResponse.ProtoReflect.Descriptor instead.
func (*GetPortalResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{17}
}

func (x *GetPortalResponse) GetPortal() *v13.Portal {
	if x != nil {
		return x.Portal
	}
	return nil
}

type GetMomentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MomentId      string                 `protobuf:"bytes,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMomentRequest) Reset() {
	*x = GetMomentRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMomentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMomentRequest) ProtoMessage() {}

func (x *GetMomentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMomentRequest.ProtoReflect.Descriptor instead.
func (*GetMomentRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{18}
}

func (x *GetMomentRequest) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

type GetMomentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Moment        *v12.Moment            `protobuf:"bytes,1,opt,name=moment,proto3" json:"moment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMomentResponse) Reset() {
	*x = GetMomentResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMomentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMomentResponse) ProtoMessage() {}

func (x *GetMomentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMomentResponse.ProtoReflect.Descriptor instead.
func (*GetMomentResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{19}
}

func (x *GetMomentResponse) GetMoment() *v12.Moment {
	if x != nil {
		return x.Moment
	}
	return nil
}

type SendMomentInviteRequest struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	Type          SendMomentInviteRequest_InviteTargetType `protobuf:"varint,1,opt,name=type,proto3,enum=api.items.portal.v1.SendMomentInviteRequest_InviteTargetType" json:"type,omitempty"`
	StoryId       *string                                  `protobuf:"bytes,2,opt,name=story_id,json=storyId,proto3,oneof" json:"story_id,omitempty"`
	MomentId      *string                                  `protobuf:"bytes,3,opt,name=moment_id,json=momentId,proto3,oneof" json:"moment_id,omitempty"`
	ReceiverIds   []string                                 `protobuf:"bytes,4,rep,name=receiver_ids,json=receiverIds,proto3" json:"receiver_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMomentInviteRequest) Reset() {
	*x = SendMomentInviteRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMomentInviteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMomentInviteRequest) ProtoMessage() {}

func (x *SendMomentInviteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMomentInviteRequest.ProtoReflect.Descriptor instead.
func (*SendMomentInviteRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{20}
}

func (x *SendMomentInviteRequest) GetType() SendMomentInviteRequest_InviteTargetType {
	if x != nil {
		return x.Type
	}
	return SendMomentInviteRequest_INVITE_TARGET_TYPE_UNSPECIFIED
}

func (x *SendMomentInviteRequest) GetStoryId() string {
	if x != nil && x.StoryId != nil {
		return *x.StoryId
	}
	return ""
}

func (x *SendMomentInviteRequest) GetMomentId() string {
	if x != nil && x.MomentId != nil {
		return *x.MomentId
	}
	return ""
}

func (x *SendMomentInviteRequest) GetReceiverIds() []string {
	if x != nil {
		return x.ReceiverIds
	}
	return nil
}

type SendMomentInviteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMomentInviteResponse) Reset() {
	*x = SendMomentInviteResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMomentInviteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMomentInviteResponse) ProtoMessage() {}

func (x *SendMomentInviteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMomentInviteResponse.ProtoReflect.Descriptor instead.
func (*SendMomentInviteResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{21}
}

type ListMomentViewersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MomentId      string                 `protobuf:"bytes,1,opt,name=moment_id,json=momentId,proto3" json:"moment_id,omitempty"`
	ListRequest   *v14.ListRequest       `protobuf:"bytes,2,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMomentViewersRequest) Reset() {
	*x = ListMomentViewersRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMomentViewersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMomentViewersRequest) ProtoMessage() {}

func (x *ListMomentViewersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMomentViewersRequest.ProtoReflect.Descriptor instead.
func (*ListMomentViewersRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{22}
}

func (x *ListMomentViewersRequest) GetMomentId() string {
	if x != nil {
		return x.MomentId
	}
	return ""
}

func (x *ListMomentViewersRequest) GetListRequest() *v14.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListMomentViewersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Viewers       []*v12.Viewer          `protobuf:"bytes,1,rep,name=viewers,proto3" json:"viewers,omitempty"`
	ListResponse  *v14.ListResponse      `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMomentViewersResponse) Reset() {
	*x = ListMomentViewersResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMomentViewersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMomentViewersResponse) ProtoMessage() {}

func (x *ListMomentViewersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMomentViewersResponse.ProtoReflect.Descriptor instead.
func (*ListMomentViewersResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{23}
}

func (x *ListMomentViewersResponse) GetViewers() []*v12.Viewer {
	if x != nil {
		return x.Viewers
	}
	return nil
}

func (x *ListMomentViewersResponse) GetListResponse() *v14.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ListTrendingPortalsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v14.ListRequest       `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTrendingPortalsRequest) Reset() {
	*x = ListTrendingPortalsRequest{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTrendingPortalsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTrendingPortalsRequest) ProtoMessage() {}

func (x *ListTrendingPortalsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTrendingPortalsRequest.ProtoReflect.Descriptor instead.
func (*ListTrendingPortalsRequest) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{24}
}

func (x *ListTrendingPortalsRequest) GetListRequest() *v14.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListTrendingPortalsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Portals       []*v13.Portal          `protobuf:"bytes,1,rep,name=portals,proto3" json:"portals,omitempty"`
	ListResponse  *v14.ListResponse      `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTrendingPortalsResponse) Reset() {
	*x = ListTrendingPortalsResponse{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTrendingPortalsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTrendingPortalsResponse) ProtoMessage() {}

func (x *ListTrendingPortalsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTrendingPortalsResponse.ProtoReflect.Descriptor instead.
func (*ListTrendingPortalsResponse) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{25}
}

func (x *ListTrendingPortalsResponse) GetPortals() []*v13.Portal {
	if x != nil {
		return x.Portals
	}
	return nil
}

func (x *ListTrendingPortalsResponse) GetListResponse() *v14.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 这批 Portals 在用户时区的当日0点，客户端需要自行转化时间格式
	DateZero      uint32        `protobuf:"varint,1,opt,name=date_zero,json=dateZero,proto3" json:"date_zero,omitempty"`
	Portals       []*v13.Portal `protobuf:"bytes,2,rep,name=portals,proto3" json:"portals,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) Reset() {
	*x = ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate{}
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) ProtoMessage() {}

func (x *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_v1_portal_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate.ProtoReflect.Descriptor instead.
func (*ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) Descriptor() ([]byte, []int) {
	return file_api_items_portal_v1_portal_proto_rawDescGZIP(), []int{10, 0}
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) GetDateZero() uint32 {
	if x != nil {
		return x.DateZero
	}
	return 0
}

func (x *ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) GetPortals() []*v13.Portal {
	if x != nil {
		return x.Portals
	}
	return nil
}

var File_api_items_portal_v1_portal_proto protoreflect.FileDescriptor

const file_api_items_portal_v1_portal_proto_rawDesc = "" +
	"\n" +
	" api/items/portal/v1/portal.proto\x12\x13api.items.portal.v1\x1a!api/resource/types/v1/types.proto\x1a%api/items/portal/types/v1/types.proto\x1a-api/items/portal/moments/types/v1/types.proto\x1a)api/items/story/types/v1/base_types.proto\x1a$api/items/story/types/v1/types.proto\x1a\x1aapi/common/v1/common.proto\x1a\x17validate/validate.proto\"C\n" +
	"\x13DeleteMomentRequest\x12,\n" +
	"\tmoment_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bmomentId\"\xb3\x02\n" +
	"\x13CreateMomentRequest\x12E\n" +
	"\bresource\x18\x01 \x01(\v2\x1f.api.resource.types.v1.ResourceB\b\xfaB\x05\x8a\x01\x02\x10\x01R\bresource\x12*\n" +
	"\bstory_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12]\n" +
	"\x10attachment_texts\x18\x03 \x03(\v2(.api.items.story.types.v1.AttachmentTextB\b\xfaB\x05\x92\x01\x02\b\x00R\x0fattachmentTexts\x128\n" +
	"\rfrom_story_id\x18\x04 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x00R\vfromStoryId\x88\x01\x01B\x10\n" +
	"\x0e_from_story_id\"c\n" +
	"\x14CreateMomentResponse\x12K\n" +
	"\x06moment\x18\x01 \x01(\v2).api.items.portal.moments.types.v1.MomentB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x06moment\"\xab\x01\n" +
	"\x1bCreateMomentRelationRequest\x12^\n" +
	"\rrelation_type\x18\x01 \x01(\x0e2/.api.items.portal.moments.types.v1.RelationTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\frelationType\x12,\n" +
	"\tmoment_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bmomentId\"k\n" +
	"\x1cCreateMomentRelationResponse\x12K\n" +
	"\x06moment\x18\x01 \x01(\v2).api.items.portal.moments.types.v1.MomentB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x06moment\"\xab\x01\n" +
	"\x1bRemoveMomentRelationRequest\x12^\n" +
	"\rrelation_type\x18\x01 \x01(\x0e2/.api.items.portal.moments.types.v1.RelationTypeB\b\xfaB\x05\x82\x01\x02\x10\x01R\frelationType\x12,\n" +
	"\tmoment_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bmomentId\"k\n" +
	"\x1cRemoveMomentRelationResponse\x12K\n" +
	"\x06moment\x18\x01 \x01(\v2).api.items.portal.moments.types.v1.MomentB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x06moment\"L\n" +
	" GetUserCreatedPortalsInfoRequest\x12\x1c\n" +
	"\auser_id\x18\x02 \x01(\tH\x00R\x06userId\x88\x01\x01B\n" +
	"\n" +
	"\b_user_id\"\x84\x01\n" +
	"!GetUserCreatedPortalsInfoResponse\x12_\n" +
	"\x14user_created_portals\x18\x01 \x01(\v2-.api.items.portal.types.v1.UserCreatedPortalsR\x12userCreatedPortals\"\x96\x01\n" +
	"*ListUserCreatedPortalsWithTimeRangeRequest\x12>\n" +
	"\alistReq\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\alistReq\x12\x1c\n" +
	"\auser_id\x18\x02 \x01(\tH\x00R\x06userId\x88\x01\x01B\n" +
	"\n" +
	"\b_user_id\"\xda\x02\n" +
	"+ListUserCreatedPortalsWithTimeRangeResponse\x12|\n" +
	"\x10PortalsWithDates\x18\x01 \x03(\v2P.api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse.PortalsWithDateR\x10PortalsWithDates\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x1ak\n" +
	"\x0fPortalsWithDate\x12\x1b\n" +
	"\tdate_zero\x18\x01 \x01(\rR\bdateZero\x12;\n" +
	"\aportals\x18\x02 \x03(\v2!.api.items.portal.types.v1.PortalR\aportals\"_\n" +
	"\x14ListMyPortalsRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xf7\x01\n" +
	"\x15ListMyPortalsResponse\x12_\n" +
	"\x14user_created_portals\x18\x01 \x01(\v2-.api.items.portal.types.v1.UserCreatedPortalsR\x12userCreatedPortals\x12;\n" +
	"\aportals\x18\x02 \x03(\v2!.api.items.portal.types.v1.PortalR\aportals\x12@\n" +
	"\rlist_response\x18\x03 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"n\n" +
	"#ListCouldAppendMomentStoriesRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xa9\x01\n" +
	"$ListCouldAppendMomentStoriesResponse\x12?\n" +
	"\astories\x18\x01 \x03(\v2%.api.items.story.types.v1.StoryDetailR\astories\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"o\n" +
	"\x11ReportReadRequest\x12,\n" +
	"\tmoment_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bmomentId\x12,\n" +
	"\tportal_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bportalId\"@\n" +
	"\x10GetPortalRequest\x12,\n" +
	"\tportal_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bportalId\"X\n" +
	"\x11GetPortalResponse\x12C\n" +
	"\x06portal\x18\x01 \x01(\v2!.api.items.portal.types.v1.PortalB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x06portal\"@\n" +
	"\x10GetMomentRequest\x12,\n" +
	"\tmoment_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bmomentId\"`\n" +
	"\x11GetMomentResponse\x12K\n" +
	"\x06moment\x18\x01 \x01(\v2).api.items.portal.moments.types.v1.MomentB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x06moment\"\x9b\x03\n" +
	"\x17SendMomentInviteRequest\x12Q\n" +
	"\x04type\x18\x01 \x01(\x0e2=.api.items.portal.v1.SendMomentInviteRequest.InviteTargetTypeR\x04type\x12/\n" +
	"\bstory_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x00R\astoryId\x88\x01\x01\x121\n" +
	"\tmoment_id\x18\x03 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x01R\bmomentId\x88\x01\x01\x129\n" +
	"\freceiver_ids\x18\x04 \x03(\tB\x16\xfaB\x13\x92\x01\x10\b\x01\"\fr\n" +
	"2\b^[0-9]+$R\vreceiverIds\"s\n" +
	"\x10InviteTargetType\x12\"\n" +
	"\x1eINVITE_TARGET_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18INVITE_TARGET_TYPE_STORY\x10\x01\x12\x1d\n" +
	"\x19INVITE_TARGET_TYPE_MOMENT\x10\x02B\v\n" +
	"\t_story_idB\f\n" +
	"\n" +
	"_moment_id\"\x1a\n" +
	"\x18SendMomentInviteResponse\"\x91\x01\n" +
	"\x18ListMomentViewersRequest\x12,\n" +
	"\tmoment_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\bmomentId\x12G\n" +
	"\flist_request\x18\x02 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xa2\x01\n" +
	"\x19ListMomentViewersResponse\x12C\n" +
	"\aviewers\x18\x01 \x03(\v2).api.items.portal.moments.types.v1.ViewerR\aviewers\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"e\n" +
	"\x1aListTrendingPortalsRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\x9c\x01\n" +
	"\x1bListTrendingPortalsResponse\x12;\n" +
	"\aportals\x18\x01 \x03(\v2!.api.items.portal.types.v1.PortalR\aportals\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponseB\x1eZ\x1cboson/api/items/portal/v1;v1b\x06proto3"

var (
	file_api_items_portal_v1_portal_proto_rawDescOnce sync.Once
	file_api_items_portal_v1_portal_proto_rawDescData []byte
)

func file_api_items_portal_v1_portal_proto_rawDescGZIP() []byte {
	file_api_items_portal_v1_portal_proto_rawDescOnce.Do(func() {
		file_api_items_portal_v1_portal_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_portal_v1_portal_proto_rawDesc), len(file_api_items_portal_v1_portal_proto_rawDesc)))
	})
	return file_api_items_portal_v1_portal_proto_rawDescData
}

var file_api_items_portal_v1_portal_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_items_portal_v1_portal_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_api_items_portal_v1_portal_proto_goTypes = []any{
	(SendMomentInviteRequest_InviteTargetType)(0),                       // 0: api.items.portal.v1.SendMomentInviteRequest.InviteTargetType
	(*DeleteMomentRequest)(nil),                                         // 1: api.items.portal.v1.DeleteMomentRequest
	(*CreateMomentRequest)(nil),                                         // 2: api.items.portal.v1.CreateMomentRequest
	(*CreateMomentResponse)(nil),                                        // 3: api.items.portal.v1.CreateMomentResponse
	(*CreateMomentRelationRequest)(nil),                                 // 4: api.items.portal.v1.CreateMomentRelationRequest
	(*CreateMomentRelationResponse)(nil),                                // 5: api.items.portal.v1.CreateMomentRelationResponse
	(*RemoveMomentRelationRequest)(nil),                                 // 6: api.items.portal.v1.RemoveMomentRelationRequest
	(*RemoveMomentRelationResponse)(nil),                                // 7: api.items.portal.v1.RemoveMomentRelationResponse
	(*GetUserCreatedPortalsInfoRequest)(nil),                            // 8: api.items.portal.v1.GetUserCreatedPortalsInfoRequest
	(*GetUserCreatedPortalsInfoResponse)(nil),                           // 9: api.items.portal.v1.GetUserCreatedPortalsInfoResponse
	(*ListUserCreatedPortalsWithTimeRangeRequest)(nil),                  // 10: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest
	(*ListUserCreatedPortalsWithTimeRangeResponse)(nil),                 // 11: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse
	(*ListMyPortalsRequest)(nil),                                        // 12: api.items.portal.v1.ListMyPortalsRequest
	(*ListMyPortalsResponse)(nil),                                       // 13: api.items.portal.v1.ListMyPortalsResponse
	(*ListCouldAppendMomentStoriesRequest)(nil),                         // 14: api.items.portal.v1.ListCouldAppendMomentStoriesRequest
	(*ListCouldAppendMomentStoriesResponse)(nil),                        // 15: api.items.portal.v1.ListCouldAppendMomentStoriesResponse
	(*ReportReadRequest)(nil),                                           // 16: api.items.portal.v1.ReportReadRequest
	(*GetPortalRequest)(nil),                                            // 17: api.items.portal.v1.GetPortalRequest
	(*GetPortalResponse)(nil),                                           // 18: api.items.portal.v1.GetPortalResponse
	(*GetMomentRequest)(nil),                                            // 19: api.items.portal.v1.GetMomentRequest
	(*GetMomentResponse)(nil),                                           // 20: api.items.portal.v1.GetMomentResponse
	(*SendMomentInviteRequest)(nil),                                     // 21: api.items.portal.v1.SendMomentInviteRequest
	(*SendMomentInviteResponse)(nil),                                    // 22: api.items.portal.v1.SendMomentInviteResponse
	(*ListMomentViewersRequest)(nil),                                    // 23: api.items.portal.v1.ListMomentViewersRequest
	(*ListMomentViewersResponse)(nil),                                   // 24: api.items.portal.v1.ListMomentViewersResponse
	(*ListTrendingPortalsRequest)(nil),                                  // 25: api.items.portal.v1.ListTrendingPortalsRequest
	(*ListTrendingPortalsResponse)(nil),                                 // 26: api.items.portal.v1.ListTrendingPortalsResponse
	(*ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate)(nil), // 27: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse.PortalsWithDate
	(*v1.Resource)(nil),                                                 // 28: api.resource.types.v1.Resource
	(*v11.AttachmentText)(nil),                                          // 29: api.items.story.types.v1.AttachmentText
	(*v12.Moment)(nil),                                                  // 30: api.items.portal.moments.types.v1.Moment
	(v12.RelationType)(0),                                               // 31: api.items.portal.moments.types.v1.RelationType
	(*v13.UserCreatedPortals)(nil),                                      // 32: api.items.portal.types.v1.UserCreatedPortals
	(*v14.ListRequest)(nil),                                             // 33: api.common.v1.ListRequest
	(*v14.ListResponse)(nil),                                            // 34: api.common.v1.ListResponse
	(*v13.Portal)(nil),                                                  // 35: api.items.portal.types.v1.Portal
	(*v11.StoryDetail)(nil),                                             // 36: api.items.story.types.v1.StoryDetail
	(*v12.Viewer)(nil),                                                  // 37: api.items.portal.moments.types.v1.Viewer
}
var file_api_items_portal_v1_portal_proto_depIdxs = []int32{
	28, // 0: api.items.portal.v1.CreateMomentRequest.resource:type_name -> api.resource.types.v1.Resource
	29, // 1: api.items.portal.v1.CreateMomentRequest.attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	30, // 2: api.items.portal.v1.CreateMomentResponse.moment:type_name -> api.items.portal.moments.types.v1.Moment
	31, // 3: api.items.portal.v1.CreateMomentRelationRequest.relation_type:type_name -> api.items.portal.moments.types.v1.RelationType
	30, // 4: api.items.portal.v1.CreateMomentRelationResponse.moment:type_name -> api.items.portal.moments.types.v1.Moment
	31, // 5: api.items.portal.v1.RemoveMomentRelationRequest.relation_type:type_name -> api.items.portal.moments.types.v1.RelationType
	30, // 6: api.items.portal.v1.RemoveMomentRelationResponse.moment:type_name -> api.items.portal.moments.types.v1.Moment
	32, // 7: api.items.portal.v1.GetUserCreatedPortalsInfoResponse.user_created_portals:type_name -> api.items.portal.types.v1.UserCreatedPortals
	33, // 8: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest.listReq:type_name -> api.common.v1.ListRequest
	27, // 9: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse.PortalsWithDates:type_name -> api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse.PortalsWithDate
	34, // 10: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse.list_response:type_name -> api.common.v1.ListResponse
	33, // 11: api.items.portal.v1.ListMyPortalsRequest.list_request:type_name -> api.common.v1.ListRequest
	32, // 12: api.items.portal.v1.ListMyPortalsResponse.user_created_portals:type_name -> api.items.portal.types.v1.UserCreatedPortals
	35, // 13: api.items.portal.v1.ListMyPortalsResponse.portals:type_name -> api.items.portal.types.v1.Portal
	34, // 14: api.items.portal.v1.ListMyPortalsResponse.list_response:type_name -> api.common.v1.ListResponse
	33, // 15: api.items.portal.v1.ListCouldAppendMomentStoriesRequest.list_request:type_name -> api.common.v1.ListRequest
	36, // 16: api.items.portal.v1.ListCouldAppendMomentStoriesResponse.stories:type_name -> api.items.story.types.v1.StoryDetail
	34, // 17: api.items.portal.v1.ListCouldAppendMomentStoriesResponse.list_response:type_name -> api.common.v1.ListResponse
	35, // 18: api.items.portal.v1.GetPortalResponse.portal:type_name -> api.items.portal.types.v1.Portal
	30, // 19: api.items.portal.v1.GetMomentResponse.moment:type_name -> api.items.portal.moments.types.v1.Moment
	0,  // 20: api.items.portal.v1.SendMomentInviteRequest.type:type_name -> api.items.portal.v1.SendMomentInviteRequest.InviteTargetType
	33, // 21: api.items.portal.v1.ListMomentViewersRequest.list_request:type_name -> api.common.v1.ListRequest
	37, // 22: api.items.portal.v1.ListMomentViewersResponse.viewers:type_name -> api.items.portal.moments.types.v1.Viewer
	34, // 23: api.items.portal.v1.ListMomentViewersResponse.list_response:type_name -> api.common.v1.ListResponse
	33, // 24: api.items.portal.v1.ListTrendingPortalsRequest.list_request:type_name -> api.common.v1.ListRequest
	35, // 25: api.items.portal.v1.ListTrendingPortalsResponse.portals:type_name -> api.items.portal.types.v1.Portal
	34, // 26: api.items.portal.v1.ListTrendingPortalsResponse.list_response:type_name -> api.common.v1.ListResponse
	35, // 27: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse.PortalsWithDate.portals:type_name -> api.items.portal.types.v1.Portal
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_api_items_portal_v1_portal_proto_init() }
func file_api_items_portal_v1_portal_proto_init() {
	if File_api_items_portal_v1_portal_proto != nil {
		return
	}
	file_api_items_portal_v1_portal_proto_msgTypes[1].OneofWrappers = []any{}
	file_api_items_portal_v1_portal_proto_msgTypes[7].OneofWrappers = []any{}
	file_api_items_portal_v1_portal_proto_msgTypes[9].OneofWrappers = []any{}
	file_api_items_portal_v1_portal_proto_msgTypes[20].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_portal_v1_portal_proto_rawDesc), len(file_api_items_portal_v1_portal_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_portal_v1_portal_proto_goTypes,
		DependencyIndexes: file_api_items_portal_v1_portal_proto_depIdxs,
		EnumInfos:         file_api_items_portal_v1_portal_proto_enumTypes,
		MessageInfos:      file_api_items_portal_v1_portal_proto_msgTypes,
	}.Build()
	File_api_items_portal_v1_portal_proto = out.File
	file_api_items_portal_v1_portal_proto_goTypes = nil
	file_api_items_portal_v1_portal_proto_depIdxs = nil
}
