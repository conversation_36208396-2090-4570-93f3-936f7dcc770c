// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/portal/moments/types/v1/types.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Stat with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Stat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Stat with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in StatMultiError, or nil if none found.
func (m *Stat) ValidateAll() error {
	return m.validate(true)
}

func (m *Stat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LikeCount

	if len(errors) > 0 {
		return StatMultiError(errors)
	}

	return nil
}

// StatMultiError is an error wrapping multiple validation errors returned by
// Stat.ValidateAll() if the designated constraints aren't met.
type StatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StatMultiError) AllErrors() []error { return m }

// StatValidationError is the validation error returned by Stat.Validate if the
// designated constraints aren't met.
type StatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StatValidationError) ErrorName() string { return "StatValidationError" }

// Error satisfies the builtin error interface
func (e StatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StatValidationError{}

// Validate checks the field values on MomentRelation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MomentRelation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MomentRelation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MomentRelationMultiError,
// or nil if none found.
func (m *MomentRelation) ValidateAll() error {
	return m.validate(true)
}

func (m *MomentRelation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RelationType

	// no validation rules for CreatedAtTimestamp

	if len(errors) > 0 {
		return MomentRelationMultiError(errors)
	}

	return nil
}

// MomentRelationMultiError is an error wrapping multiple validation errors
// returned by MomentRelation.ValidateAll() if the designated constraints
// aren't met.
type MomentRelationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MomentRelationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MomentRelationMultiError) AllErrors() []error { return m }

// MomentRelationValidationError is the validation error returned by
// MomentRelation.Validate if the designated constraints aren't met.
type MomentRelationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MomentRelationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MomentRelationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MomentRelationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MomentRelationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MomentRelationValidationError) ErrorName() string { return "MomentRelationValidationError" }

// Error satisfies the builtin error interface
func (e MomentRelationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMomentRelation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MomentRelationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MomentRelationValidationError{}

// Validate checks the field values on Viewer with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Viewer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Viewer with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ViewerMultiError, or nil if none found.
func (m *Viewer) ValidateAll() error {
	return m.validate(true)
}

func (m *Viewer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUser()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ViewerValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ViewerValidationError{
					field:  "User",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUser()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ViewerValidationError{
				field:  "User",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUserRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ViewerValidationError{
						field:  fmt.Sprintf("UserRelations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ViewerValidationError{
						field:  fmt.Sprintf("UserRelations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ViewerValidationError{
					field:  fmt.Sprintf("UserRelations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ViewerMultiError(errors)
	}

	return nil
}

// ViewerMultiError is an error wrapping multiple validation errors returned by
// Viewer.ValidateAll() if the designated constraints aren't met.
type ViewerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ViewerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ViewerMultiError) AllErrors() []error { return m }

// ViewerValidationError is the validation error returned by Viewer.Validate if
// the designated constraints aren't met.
type ViewerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ViewerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ViewerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ViewerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ViewerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ViewerValidationError) ErrorName() string { return "ViewerValidationError" }

// Error satisfies the builtin error interface
func (e ViewerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sViewer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ViewerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ViewerValidationError{}

// Validate checks the field values on Moment with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Moment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Moment with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in MomentMultiError, or nil if none found.
func (m *Moment) ValidateAll() error {
	return m.validate(true)
}

func (m *Moment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for MomentType

	if all {
		switch v := interface{}(m.GetAuthor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "Author",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "Author",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MomentValidationError{
				field:  "Author",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MomentValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MomentValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MomentValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MomentValidationError{
					field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetLoginUserRelations() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MomentValidationError{
						field:  fmt.Sprintf("LoginUserRelations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MomentValidationError{
						field:  fmt.Sprintf("LoginUserRelations[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MomentValidationError{
					field:  fmt.Sprintf("LoginUserRelations[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAvatars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MomentValidationError{
						field:  fmt.Sprintf("Avatars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MomentValidationError{
						field:  fmt.Sprintf("Avatars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MomentValidationError{
					field:  fmt.Sprintf("Avatars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetLatestViewer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "LatestViewer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "LatestViewer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLatestViewer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MomentValidationError{
				field:  "LatestViewer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreatedAtTimestamp

	if all {
		switch v := interface{}(m.GetStat()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "Stat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "Stat",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStat()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MomentValidationError{
				field:  "Stat",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHauntBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "HauntBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MomentValidationError{
					field:  "HauntBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHauntBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MomentValidationError{
				field:  "HauntBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MomentMultiError(errors)
	}

	return nil
}

// MomentMultiError is an error wrapping multiple validation errors returned by
// Moment.ValidateAll() if the designated constraints aren't met.
type MomentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MomentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MomentMultiError) AllErrors() []error { return m }

// MomentValidationError is the validation error returned by Moment.Validate if
// the designated constraints aren't met.
type MomentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MomentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MomentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MomentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MomentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MomentValidationError) ErrorName() string { return "MomentValidationError" }

// Error satisfies the builtin error interface
func (e MomentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MomentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MomentValidationError{}
