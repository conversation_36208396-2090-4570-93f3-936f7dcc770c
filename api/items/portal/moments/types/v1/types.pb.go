// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/portal/moments/types/v1/types.proto

package v1

import (
	v12 "boson/api/items/story/types/v1"
	v11 "boson/api/resource/types/v1"
	v13 "boson/api/users/boo/types/v1"
	v1 "boson/api/users/info/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MomentType int32

const (
	MomentType_MOMENT_TYPE_UNSPECIFIED MomentType = 0
	MomentType_MOMENT_TYPE_NORMAL      MomentType = 1
	MomentType_MOMENT_TYPE_HAUNT       MomentType = 2
)

// Enum value maps for MomentType.
var (
	MomentType_name = map[int32]string{
		0: "MOMENT_TYPE_UNSPECIFIED",
		1: "MOMENT_TYPE_NORMAL",
		2: "MOMENT_TYPE_HAUNT",
	}
	MomentType_value = map[string]int32{
		"MOMENT_TYPE_UNSPECIFIED": 0,
		"MOMENT_TYPE_NORMAL":      1,
		"MOMENT_TYPE_HAUNT":       2,
	}
)

func (x MomentType) Enum() *MomentType {
	p := new(MomentType)
	*p = x
	return p
}

func (x MomentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MomentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_portal_moments_types_v1_types_proto_enumTypes[0].Descriptor()
}

func (MomentType) Type() protoreflect.EnumType {
	return &file_api_items_portal_moments_types_v1_types_proto_enumTypes[0]
}

func (x MomentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MomentType.Descriptor instead.
func (MomentType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{0}
}

type Status int32

const (
	Status_STATUS_UNSPECIFIED Status = 0
	Status_STATUS_PUBLISHED   Status = 1
	Status_STATUS_DELETED     Status = 2
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_PUBLISHED",
		2: "STATUS_DELETED",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"STATUS_PUBLISHED":   1,
		"STATUS_DELETED":     2,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_portal_moments_types_v1_types_proto_enumTypes[1].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_api_items_portal_moments_types_v1_types_proto_enumTypes[1]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{1}
}

// 关系类型
type RelationType int32

const (
	RelationType_RELATION_TYPE_UNSPECIFIED RelationType = 0
	RelationType_RELATION_TYPE_LIKE        RelationType = 1
)

// Enum value maps for RelationType.
var (
	RelationType_name = map[int32]string{
		0: "RELATION_TYPE_UNSPECIFIED",
		1: "RELATION_TYPE_LIKE",
	}
	RelationType_value = map[string]int32{
		"RELATION_TYPE_UNSPECIFIED": 0,
		"RELATION_TYPE_LIKE":        1,
	}
)

func (x RelationType) Enum() *RelationType {
	p := new(RelationType)
	*p = x
	return p
}

func (x RelationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_portal_moments_types_v1_types_proto_enumTypes[2].Descriptor()
}

func (RelationType) Type() protoreflect.EnumType {
	return &file_api_items_portal_moments_types_v1_types_proto_enumTypes[2]
}

func (x RelationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationType.Descriptor instead.
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{2}
}

type MomentCreateType int32

const (
	MomentCreateType_MOMENT_CREATE_TYPE_UNSPECIFIED MomentCreateType = 0
	MomentCreateType_MOMENT_CREATE_TYPE_INIT        MomentCreateType = 1
	MomentCreateType_MOMENT_CREATE_TYPE_APPEND      MomentCreateType = 2
)

// Enum value maps for MomentCreateType.
var (
	MomentCreateType_name = map[int32]string{
		0: "MOMENT_CREATE_TYPE_UNSPECIFIED",
		1: "MOMENT_CREATE_TYPE_INIT",
		2: "MOMENT_CREATE_TYPE_APPEND",
	}
	MomentCreateType_value = map[string]int32{
		"MOMENT_CREATE_TYPE_UNSPECIFIED": 0,
		"MOMENT_CREATE_TYPE_INIT":        1,
		"MOMENT_CREATE_TYPE_APPEND":      2,
	}
)

func (x MomentCreateType) Enum() *MomentCreateType {
	p := new(MomentCreateType)
	*p = x
	return p
}

func (x MomentCreateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MomentCreateType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_portal_moments_types_v1_types_proto_enumTypes[3].Descriptor()
}

func (MomentCreateType) Type() protoreflect.EnumType {
	return &file_api_items_portal_moments_types_v1_types_proto_enumTypes[3]
}

func (x MomentCreateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MomentCreateType.Descriptor instead.
func (MomentCreateType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{3}
}

type Stat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LikeCount     uint32                 `protobuf:"varint,1,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Stat) Reset() {
	*x = Stat{}
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Stat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Stat) ProtoMessage() {}

func (x *Stat) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Stat.ProtoReflect.Descriptor instead.
func (*Stat) Descriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *Stat) GetLikeCount() uint32 {
	if x != nil {
		return x.LikeCount
	}
	return 0
}

type MomentRelation struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	RelationType       RelationType           `protobuf:"varint,1,opt,name=relation_type,json=relationType,proto3,enum=api.items.portal.moments.types.v1.RelationType" json:"relation_type,omitempty"`
	CreatedAtTimestamp uint32                 `protobuf:"varint,2,opt,name=created_at_timestamp,json=createdAtTimestamp,proto3" json:"created_at_timestamp,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *MomentRelation) Reset() {
	*x = MomentRelation{}
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MomentRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MomentRelation) ProtoMessage() {}

func (x *MomentRelation) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MomentRelation.ProtoReflect.Descriptor instead.
func (*MomentRelation) Descriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *MomentRelation) GetRelationType() RelationType {
	if x != nil {
		return x.RelationType
	}
	return RelationType_RELATION_TYPE_UNSPECIFIED
}

func (x *MomentRelation) GetCreatedAtTimestamp() uint32 {
	if x != nil {
		return x.CreatedAtTimestamp
	}
	return 0
}

type Viewer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	User  *v1.UserInfoSummary    `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	// 此用户对 moment 的 relations
	UserRelations []*MomentRelation `protobuf:"bytes,3,rep,name=user_relations,json=userRelations,proto3" json:"user_relations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Viewer) Reset() {
	*x = Viewer{}
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Viewer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Viewer) ProtoMessage() {}

func (x *Viewer) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Viewer.ProtoReflect.Descriptor instead.
func (*Viewer) Descriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{2}
}

func (x *Viewer) GetUser() *v1.UserInfoSummary {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *Viewer) GetUserRelations() []*MomentRelation {
	if x != nil {
		return x.UserRelations
	}
	return nil
}

type Moment struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Id                 string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	MomentType         MomentType             `protobuf:"varint,2,opt,name=moment_type,json=momentType,proto3,enum=api.items.portal.moments.types.v1.MomentType" json:"moment_type,omitempty"`
	Author             *v1.UserInfoSummary    `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Resource           *v11.Resource          `protobuf:"bytes,4,opt,name=resource,proto3" json:"resource,omitempty"`
	AttachmentTexts    []*v12.AttachmentText  `protobuf:"bytes,5,rep,name=attachment_texts,json=attachmentTexts,proto3" json:"attachment_texts,omitempty"`
	LoginUserRelations []*MomentRelation      `protobuf:"bytes,6,rep,name=login_user_relations,json=loginUserRelations,proto3" json:"login_user_relations,omitempty"`
	Avatars            []*v13.Avatar          `protobuf:"bytes,7,rep,name=avatars,proto3" json:"avatars,omitempty"`
	// 最新的 viewer
	LatestViewer       *Viewer `protobuf:"bytes,8,opt,name=latest_viewer,json=latestViewer,proto3" json:"latest_viewer,omitempty"`
	CreatedAtTimestamp uint32  `protobuf:"varint,9,opt,name=created_at_timestamp,json=createdAtTimestamp,proto3" json:"created_at_timestamp,omitempty"`
	Stat               *Stat   `protobuf:"bytes,10,opt,name=stat,proto3" json:"stat,omitempty"`
	// 针对 haunt 的展示信息
	HauntBooShowInfo *v12.HauntBooShowInfo `protobuf:"bytes,11,opt,name=haunt_boo_show_info,json=hauntBooShowInfo,proto3" json:"haunt_boo_show_info,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Moment) Reset() {
	*x = Moment{}
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Moment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Moment) ProtoMessage() {}

func (x *Moment) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_portal_moments_types_v1_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Moment.ProtoReflect.Descriptor instead.
func (*Moment) Descriptor() ([]byte, []int) {
	return file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP(), []int{3}
}

func (x *Moment) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Moment) GetMomentType() MomentType {
	if x != nil {
		return x.MomentType
	}
	return MomentType_MOMENT_TYPE_UNSPECIFIED
}

func (x *Moment) GetAuthor() *v1.UserInfoSummary {
	if x != nil {
		return x.Author
	}
	return nil
}

func (x *Moment) GetResource() *v11.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *Moment) GetAttachmentTexts() []*v12.AttachmentText {
	if x != nil {
		return x.AttachmentTexts
	}
	return nil
}

func (x *Moment) GetLoginUserRelations() []*MomentRelation {
	if x != nil {
		return x.LoginUserRelations
	}
	return nil
}

func (x *Moment) GetAvatars() []*v13.Avatar {
	if x != nil {
		return x.Avatars
	}
	return nil
}

func (x *Moment) GetLatestViewer() *Viewer {
	if x != nil {
		return x.LatestViewer
	}
	return nil
}

func (x *Moment) GetCreatedAtTimestamp() uint32 {
	if x != nil {
		return x.CreatedAtTimestamp
	}
	return 0
}

func (x *Moment) GetStat() *Stat {
	if x != nil {
		return x.Stat
	}
	return nil
}

func (x *Moment) GetHauntBooShowInfo() *v12.HauntBooShowInfo {
	if x != nil {
		return x.HauntBooShowInfo
	}
	return nil
}

var File_api_items_portal_moments_types_v1_types_proto protoreflect.FileDescriptor

const file_api_items_portal_moments_types_v1_types_proto_rawDesc = "" +
	"\n" +
	"-api/items/portal/moments/types/v1/types.proto\x12!api.items.portal.moments.types.v1\x1a#api/users/info/types/v1/types.proto\x1a!api/resource/types/v1/types.proto\x1a)api/items/story/types/v1/base_types.proto\x1a\"api/users/boo/types/v1/types.proto\x1a*api/items/story/types/v1/haunt.types.proto\"%\n" +
	"\x04Stat\x12\x1d\n" +
	"\n" +
	"like_count\x18\x01 \x01(\rR\tlikeCount\"\x98\x01\n" +
	"\x0eMomentRelation\x12T\n" +
	"\rrelation_type\x18\x01 \x01(\x0e2/.api.items.portal.moments.types.v1.RelationTypeR\frelationType\x120\n" +
	"\x14created_at_timestamp\x18\x02 \x01(\rR\x12createdAtTimestamp\"\xa0\x01\n" +
	"\x06Viewer\x12<\n" +
	"\x04user\x18\x02 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\x04user\x12X\n" +
	"\x0euser_relations\x18\x03 \x03(\v21.api.items.portal.moments.types.v1.MomentRelationR\ruserRelations\"\xf5\x05\n" +
	"\x06Moment\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12N\n" +
	"\vmoment_type\x18\x02 \x01(\x0e2-.api.items.portal.moments.types.v1.MomentTypeR\n" +
	"momentType\x12@\n" +
	"\x06author\x18\x03 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\x06author\x12;\n" +
	"\bresource\x18\x04 \x01(\v2\x1f.api.resource.types.v1.ResourceR\bresource\x12S\n" +
	"\x10attachment_texts\x18\x05 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x0fattachmentTexts\x12c\n" +
	"\x14login_user_relations\x18\x06 \x03(\v21.api.items.portal.moments.types.v1.MomentRelationR\x12loginUserRelations\x128\n" +
	"\aavatars\x18\a \x03(\v2\x1e.api.users.boo.types.v1.AvatarR\aavatars\x12N\n" +
	"\rlatest_viewer\x18\b \x01(\v2).api.items.portal.moments.types.v1.ViewerR\flatestViewer\x120\n" +
	"\x14created_at_timestamp\x18\t \x01(\rR\x12createdAtTimestamp\x12;\n" +
	"\x04stat\x18\n" +
	" \x01(\v2'.api.items.portal.moments.types.v1.StatR\x04stat\x12Y\n" +
	"\x13haunt_boo_show_info\x18\v \x01(\v2*.api.items.story.types.v1.HauntBooShowInfoR\x10hauntBooShowInfo*X\n" +
	"\n" +
	"MomentType\x12\x1b\n" +
	"\x17MOMENT_TYPE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12MOMENT_TYPE_NORMAL\x10\x01\x12\x15\n" +
	"\x11MOMENT_TYPE_HAUNT\x10\x02*J\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\x14\n" +
	"\x10STATUS_PUBLISHED\x10\x01\x12\x12\n" +
	"\x0eSTATUS_DELETED\x10\x02*E\n" +
	"\fRelationType\x12\x1d\n" +
	"\x19RELATION_TYPE_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12RELATION_TYPE_LIKE\x10\x01*r\n" +
	"\x10MomentCreateType\x12\"\n" +
	"\x1eMOMENT_CREATE_TYPE_UNSPECIFIED\x10\x00\x12\x1b\n" +
	"\x17MOMENT_CREATE_TYPE_INIT\x10\x01\x12\x1d\n" +
	"\x19MOMENT_CREATE_TYPE_APPEND\x10\x02B,Z*boson/api/items/portal/moments/types/v1;v1b\x06proto3"

var (
	file_api_items_portal_moments_types_v1_types_proto_rawDescOnce sync.Once
	file_api_items_portal_moments_types_v1_types_proto_rawDescData []byte
)

func file_api_items_portal_moments_types_v1_types_proto_rawDescGZIP() []byte {
	file_api_items_portal_moments_types_v1_types_proto_rawDescOnce.Do(func() {
		file_api_items_portal_moments_types_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_portal_moments_types_v1_types_proto_rawDesc), len(file_api_items_portal_moments_types_v1_types_proto_rawDesc)))
	})
	return file_api_items_portal_moments_types_v1_types_proto_rawDescData
}

var file_api_items_portal_moments_types_v1_types_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_items_portal_moments_types_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_items_portal_moments_types_v1_types_proto_goTypes = []any{
	(MomentType)(0),              // 0: api.items.portal.moments.types.v1.MomentType
	(Status)(0),                  // 1: api.items.portal.moments.types.v1.Status
	(RelationType)(0),            // 2: api.items.portal.moments.types.v1.RelationType
	(MomentCreateType)(0),        // 3: api.items.portal.moments.types.v1.MomentCreateType
	(*Stat)(nil),                 // 4: api.items.portal.moments.types.v1.Stat
	(*MomentRelation)(nil),       // 5: api.items.portal.moments.types.v1.MomentRelation
	(*Viewer)(nil),               // 6: api.items.portal.moments.types.v1.Viewer
	(*Moment)(nil),               // 7: api.items.portal.moments.types.v1.Moment
	(*v1.UserInfoSummary)(nil),   // 8: api.users.info.types.v1.UserInfoSummary
	(*v11.Resource)(nil),         // 9: api.resource.types.v1.Resource
	(*v12.AttachmentText)(nil),   // 10: api.items.story.types.v1.AttachmentText
	(*v13.Avatar)(nil),           // 11: api.users.boo.types.v1.Avatar
	(*v12.HauntBooShowInfo)(nil), // 12: api.items.story.types.v1.HauntBooShowInfo
}
var file_api_items_portal_moments_types_v1_types_proto_depIdxs = []int32{
	2,  // 0: api.items.portal.moments.types.v1.MomentRelation.relation_type:type_name -> api.items.portal.moments.types.v1.RelationType
	8,  // 1: api.items.portal.moments.types.v1.Viewer.user:type_name -> api.users.info.types.v1.UserInfoSummary
	5,  // 2: api.items.portal.moments.types.v1.Viewer.user_relations:type_name -> api.items.portal.moments.types.v1.MomentRelation
	0,  // 3: api.items.portal.moments.types.v1.Moment.moment_type:type_name -> api.items.portal.moments.types.v1.MomentType
	8,  // 4: api.items.portal.moments.types.v1.Moment.author:type_name -> api.users.info.types.v1.UserInfoSummary
	9,  // 5: api.items.portal.moments.types.v1.Moment.resource:type_name -> api.resource.types.v1.Resource
	10, // 6: api.items.portal.moments.types.v1.Moment.attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	5,  // 7: api.items.portal.moments.types.v1.Moment.login_user_relations:type_name -> api.items.portal.moments.types.v1.MomentRelation
	11, // 8: api.items.portal.moments.types.v1.Moment.avatars:type_name -> api.users.boo.types.v1.Avatar
	6,  // 9: api.items.portal.moments.types.v1.Moment.latest_viewer:type_name -> api.items.portal.moments.types.v1.Viewer
	4,  // 10: api.items.portal.moments.types.v1.Moment.stat:type_name -> api.items.portal.moments.types.v1.Stat
	12, // 11: api.items.portal.moments.types.v1.Moment.haunt_boo_show_info:type_name -> api.items.story.types.v1.HauntBooShowInfo
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_items_portal_moments_types_v1_types_proto_init() }
func file_api_items_portal_moments_types_v1_types_proto_init() {
	if File_api_items_portal_moments_types_v1_types_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_portal_moments_types_v1_types_proto_rawDesc), len(file_api_items_portal_moments_types_v1_types_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_portal_moments_types_v1_types_proto_goTypes,
		DependencyIndexes: file_api_items_portal_moments_types_v1_types_proto_depIdxs,
		EnumInfos:         file_api_items_portal_moments_types_v1_types_proto_enumTypes,
		MessageInfos:      file_api_items_portal_moments_types_v1_types_proto_msgTypes,
	}.Build()
	File_api_items_portal_moments_types_v1_types_proto = out.File
	file_api_items_portal_moments_types_v1_types_proto_goTypes = nil
	file_api_items_portal_moments_types_v1_types_proto_depIdxs = nil
}
