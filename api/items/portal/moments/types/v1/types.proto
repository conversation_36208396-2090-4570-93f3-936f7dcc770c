syntax = "proto3";

package api.items.portal.moments.types.v1;

import "api/users/info/types/v1/types.proto";
import "api/resource/types/v1/types.proto";
import "api/items/story/types/v1/base_types.proto";
import "api/users/boo/types/v1/types.proto";
import "api/items/story/types/v1/haunt.types.proto";

option go_package = "boson/api/items/portal/moments/types/v1;v1";

enum MomentType {
	MOMENT_TYPE_UNSPECIFIED = 0;
	MOMENT_TYPE_NORMAL = 1;
	MOMENT_TYPE_HAUNT = 2;
}

enum Status {
	STATUS_UNSPECIFIED = 0;
	STATUS_PUBLISHED = 1;
	STATUS_DELETED = 2;
}

message Stat {
	uint32 like_count = 1;
}

// 关系类型
enum RelationType {
	RELATION_TYPE_UNSPECIFIED = 0;
	RELATION_TYPE_LIKE = 1;
}

message MomentRelation {
	RelationType relation_type = 1;
	uint32 created_at_timestamp = 2;
}

message Viewer {
	api.users.info.types.v1.UserInfoSummary user = 2;
	// 此用户对 moment 的 relations
	repeated MomentRelation user_relations = 3;
}

enum MomentCreateType {
	MOMENT_CREATE_TYPE_UNSPECIFIED = 0;
	MOMENT_CREATE_TYPE_INIT = 1;
	MOMENT_CREATE_TYPE_APPEND = 2;
}

message Moment {
    string id = 1;	
	MomentType moment_type = 2;

	api.users.info.types.v1.UserInfoSummary author = 3;
    api.resource.types.v1.Resource resource = 4;
	repeated api.items.story.types.v1.AttachmentText attachment_texts = 5;
	repeated MomentRelation login_user_relations = 6;
	
	repeated api.users.boo.types.v1.Avatar avatars = 7;

	// 最新的 viewer
	Viewer latest_viewer = 8;
	uint32 created_at_timestamp = 9;
	Stat stat = 10;

	// 针对 haunt 的展示信息
	api.items.story.types.v1.HauntBooShowInfo haunt_boo_show_info = 11;
}