// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/v1/items.proto

package v1

import (
	artwork "boson/api/artwork"
	v11 "boson/api/common/v1"
	v14 "boson/api/items/comments/v1"
	v13 "boson/api/items/music/search/v1"
	v12 "boson/api/items/music/types/v1"
	v110 "boson/api/items/portal/v1"
	v15 "boson/api/items/reaction/v1"
	v16 "boson/api/items/story/activity/v1"
	v18 "boson/api/items/story/hide_stickers/v1"
	v19 "boson/api/items/story/reaction/v1"
	v17 "boson/api/items/story/v1"
	v2 "boson/api/items/story/v2"
	v1 "boson/api/items/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HomePageTimelineResponse_CardTag int32

const (
	HomePageTimelineResponse_CARD_TAG_UNSPECIFIED HomePageTimelineResponse_CardTag = 0
	// FRIEND
	HomePageTimelineResponse_CARD_TAG_FRIEND HomePageTimelineResponse_CardTag = 1
)

// Enum value maps for HomePageTimelineResponse_CardTag.
var (
	HomePageTimelineResponse_CardTag_name = map[int32]string{
		0: "CARD_TAG_UNSPECIFIED",
		1: "CARD_TAG_FRIEND",
	}
	HomePageTimelineResponse_CardTag_value = map[string]int32{
		"CARD_TAG_UNSPECIFIED": 0,
		"CARD_TAG_FRIEND":      1,
	}
)

func (x HomePageTimelineResponse_CardTag) Enum() *HomePageTimelineResponse_CardTag {
	p := new(HomePageTimelineResponse_CardTag)
	*p = x
	return p
}

func (x HomePageTimelineResponse_CardTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HomePageTimelineResponse_CardTag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_v1_items_proto_enumTypes[0].Descriptor()
}

func (HomePageTimelineResponse_CardTag) Type() protoreflect.EnumType {
	return &file_api_items_v1_items_proto_enumTypes[0]
}

func (x HomePageTimelineResponse_CardTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HomePageTimelineResponse_CardTag.Descriptor instead.
func (HomePageTimelineResponse_CardTag) EnumDescriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{7, 0}
}

type ReportShareStatRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportShareStatRequest) Reset() {
	*x = ReportShareStatRequest{}
	mi := &file_api_items_v1_items_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportShareStatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportShareStatRequest) ProtoMessage() {}

func (x *ReportShareStatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportShareStatRequest.ProtoReflect.Descriptor instead.
func (*ReportShareStatRequest) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{0}
}

func (x *ReportShareStatRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type ReportShareStatResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportShareStatResponse) Reset() {
	*x = ReportShareStatResponse{}
	mi := &file_api_items_v1_items_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportShareStatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportShareStatResponse) ProtoMessage() {}

func (x *ReportShareStatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportShareStatResponse.ProtoReflect.Descriptor instead.
func (*ReportShareStatResponse) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{1}
}

type BatchGetItemSummariesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ItemIds       []string               `protobuf:"bytes,1,rep,name=item_ids,json=itemIds,proto3" json:"item_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetItemSummariesRequest) Reset() {
	*x = BatchGetItemSummariesRequest{}
	mi := &file_api_items_v1_items_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetItemSummariesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetItemSummariesRequest) ProtoMessage() {}

func (x *BatchGetItemSummariesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetItemSummariesRequest.ProtoReflect.Descriptor instead.
func (*BatchGetItemSummariesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{2}
}

func (x *BatchGetItemSummariesRequest) GetItemIds() []string {
	if x != nil {
		return x.ItemIds
	}
	return nil
}

type BatchGetItemSummariesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Items         []*v1.ItemSummary      `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetItemSummariesResponse) Reset() {
	*x = BatchGetItemSummariesResponse{}
	mi := &file_api_items_v1_items_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetItemSummariesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetItemSummariesResponse) ProtoMessage() {}

func (x *BatchGetItemSummariesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetItemSummariesResponse.ProtoReflect.Descriptor instead.
func (*BatchGetItemSummariesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{3}
}

func (x *BatchGetItemSummariesResponse) GetItems() []*v1.ItemSummary {
	if x != nil {
		return x.Items
	}
	return nil
}

type CreateItemRequest struct {
	state               protoimpl.MessageState    `protogen:"open.v1"`
	Title               string                    `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description         string                    `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	CoverImageObjectKey string                    `protobuf:"bytes,3,opt,name=cover_image_object_key,json=coverImageObjectKey,proto3" json:"cover_image_object_key,omitempty"`
	Bgm                 *CreateItemRequest_Bgm    `protobuf:"bytes,4,opt,name=bgm,proto3,oneof" json:"bgm,omitempty"`
	Sketchboards        []*artwork.ArtSketchBoard `protobuf:"bytes,5,rep,name=sketchboards,proto3" json:"sketchboards,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *CreateItemRequest) Reset() {
	*x = CreateItemRequest{}
	mi := &file_api_items_v1_items_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateItemRequest) ProtoMessage() {}

func (x *CreateItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateItemRequest.ProtoReflect.Descriptor instead.
func (*CreateItemRequest) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{4}
}

func (x *CreateItemRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateItemRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateItemRequest) GetCoverImageObjectKey() string {
	if x != nil {
		return x.CoverImageObjectKey
	}
	return ""
}

func (x *CreateItemRequest) GetBgm() *CreateItemRequest_Bgm {
	if x != nil {
		return x.Bgm
	}
	return nil
}

func (x *CreateItemRequest) GetSketchboards() []*artwork.ArtSketchBoard {
	if x != nil {
		return x.Sketchboards
	}
	return nil
}

type CreateItemResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Item          *v1.ItemDetail         `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateItemResponse) Reset() {
	*x = CreateItemResponse{}
	mi := &file_api_items_v1_items_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateItemResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateItemResponse) ProtoMessage() {}

func (x *CreateItemResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateItemResponse.ProtoReflect.Descriptor instead.
func (*CreateItemResponse) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{5}
}

func (x *CreateItemResponse) GetItem() *v1.ItemDetail {
	if x != nil {
		return x.Item
	}
	return nil
}

type HomePageTimelineRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v11.ListRequest       `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HomePageTimelineRequest) Reset() {
	*x = HomePageTimelineRequest{}
	mi := &file_api_items_v1_items_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HomePageTimelineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomePageTimelineRequest) ProtoMessage() {}

func (x *HomePageTimelineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomePageTimelineRequest.ProtoReflect.Descriptor instead.
func (*HomePageTimelineRequest) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{6}
}

func (x *HomePageTimelineRequest) GetListRequest() *v11.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type HomePageTimelineResponse struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	TimelineCards []*HomePageTimelineResponse_TimelineCard `protobuf:"bytes,1,rep,name=timeline_cards,json=timelineCards,proto3" json:"timeline_cards,omitempty"`
	ListResponse  *v11.ListResponse                        `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HomePageTimelineResponse) Reset() {
	*x = HomePageTimelineResponse{}
	mi := &file_api_items_v1_items_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HomePageTimelineResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomePageTimelineResponse) ProtoMessage() {}

func (x *HomePageTimelineResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomePageTimelineResponse.ProtoReflect.Descriptor instead.
func (*HomePageTimelineResponse) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{7}
}

func (x *HomePageTimelineResponse) GetTimelineCards() []*HomePageTimelineResponse_TimelineCard {
	if x != nil {
		return x.TimelineCards
	}
	return nil
}

func (x *HomePageTimelineResponse) GetListResponse() *v11.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type AsrRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AudioObjectKey string                 `protobuf:"bytes,1,opt,name=audio_object_key,json=audioObjectKey,proto3" json:"audio_object_key,omitempty"`
	// 相似度阈值，默认0.8，如果客户端传了此值，返回值服务端会自己计算相似度并返回是否匹配，否则返回值里的 matched 无意义
	Similarity *float32 `protobuf:"fixed32,2,opt,name=similarity,proto3,oneof" json:"similarity,omitempty"`
	// 期望的文本，如果客户端传了此值，返回值服务端会自己计算相似度并返回是否匹配，否则返回值里的 matched 无意义
	ExpectedTxt   *string `protobuf:"bytes,3,opt,name=expected_txt,json=expectedTxt,proto3,oneof" json:"expected_txt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsrRequest) Reset() {
	*x = AsrRequest{}
	mi := &file_api_items_v1_items_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsrRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsrRequest) ProtoMessage() {}

func (x *AsrRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsrRequest.ProtoReflect.Descriptor instead.
func (*AsrRequest) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{8}
}

func (x *AsrRequest) GetAudioObjectKey() string {
	if x != nil {
		return x.AudioObjectKey
	}
	return ""
}

func (x *AsrRequest) GetSimilarity() float32 {
	if x != nil && x.Similarity != nil {
		return *x.Similarity
	}
	return 0
}

func (x *AsrRequest) GetExpectedTxt() string {
	if x != nil && x.ExpectedTxt != nil {
		return *x.ExpectedTxt
	}
	return ""
}

type AsrResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Text  string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 是否匹配
	Matched       bool `protobuf:"varint,2,opt,name=matched,proto3" json:"matched,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AsrResponse) Reset() {
	*x = AsrResponse{}
	mi := &file_api_items_v1_items_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AsrResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AsrResponse) ProtoMessage() {}

func (x *AsrResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AsrResponse.ProtoReflect.Descriptor instead.
func (*AsrResponse) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{9}
}

func (x *AsrResponse) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *AsrResponse) GetMatched() bool {
	if x != nil {
		return x.Matched
	}
	return false
}

// bgm 音频播放信息
type CreateItemRequest_Bgm struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 裁剪的原始音乐
	Song *v12.Song `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	// 裁剪后的播放对象 key
	AudioPlayKey  string `protobuf:"bytes,2,opt,name=audio_play_key,json=audioPlayKey,proto3" json:"audio_play_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateItemRequest_Bgm) Reset() {
	*x = CreateItemRequest_Bgm{}
	mi := &file_api_items_v1_items_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateItemRequest_Bgm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateItemRequest_Bgm) ProtoMessage() {}

func (x *CreateItemRequest_Bgm) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateItemRequest_Bgm.ProtoReflect.Descriptor instead.
func (*CreateItemRequest_Bgm) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{4, 0}
}

func (x *CreateItemRequest_Bgm) GetSong() *v12.Song {
	if x != nil {
		return x.Song
	}
	return nil
}

func (x *CreateItemRequest_Bgm) GetAudioPlayKey() string {
	if x != nil {
		return x.AudioPlayKey
	}
	return ""
}

type HomePageTimelineResponse_TimelineCard struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用于目前 timeline 右上角显示 tag 信息
	Tag           HomePageTimelineResponse_CardTag `protobuf:"varint,1,opt,name=tag,proto3,enum=api.items.v1.HomePageTimelineResponse_CardTag" json:"tag,omitempty"`
	Item          *v1.ItemSummary                  `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HomePageTimelineResponse_TimelineCard) Reset() {
	*x = HomePageTimelineResponse_TimelineCard{}
	mi := &file_api_items_v1_items_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HomePageTimelineResponse_TimelineCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HomePageTimelineResponse_TimelineCard) ProtoMessage() {}

func (x *HomePageTimelineResponse_TimelineCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_v1_items_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HomePageTimelineResponse_TimelineCard.ProtoReflect.Descriptor instead.
func (*HomePageTimelineResponse_TimelineCard) Descriptor() ([]byte, []int) {
	return file_api_items_v1_items_proto_rawDescGZIP(), []int{7, 0}
}

func (x *HomePageTimelineResponse_TimelineCard) GetTag() HomePageTimelineResponse_CardTag {
	if x != nil {
		return x.Tag
	}
	return HomePageTimelineResponse_CARD_TAG_UNSPECIFIED
}

func (x *HomePageTimelineResponse_TimelineCard) GetItem() *v1.ItemSummary {
	if x != nil {
		return x.Item
	}
	return nil
}

var File_api_items_v1_items_proto protoreflect.FileDescriptor

const file_api_items_v1_items_proto_rawDesc = "" +
	"\n" +
	"\x18api/items/v1/items.proto\x12\fapi.items.v1\x1a\x19api/artwork/artwork.proto\x1a\x1eapi/items/types/v1/types.proto\x1a\x1aapi/common/v1/common.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x17validate/validate.proto\x1a&api/items/music/search/v1/search.proto\x1a$api/items/comments/v1/comments.proto\x1a$api/items/reaction/v1/reaction.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a$api/items/music/types/v1/types.proto\x1a\x1eapi/items/story/v1/story.proto\x1a&api/items/story/v1/capsule.story.proto\x1a&api/items/story/v1/roasted.story.proto\x1a*api/items/story/reaction/v1/reaction.proto\x1a\x1eapi/items/story/v2/story.proto\x1a\x1dapi/items/story/v2/hide.proto\x1a*api/items/story/activity/v1/activity.proto\x1a4api/items/story/hide_stickers/v1/hide_stickers.proto\x1a\"api/items/story/v2/chatproxy.proto\x1a api/items/portal/v1/portal.proto\x1a\x1capi/items/story/v2/who.proto\x1a\x1eapi/items/story/v2/haunt.proto\x1a\x1fapi/items/story/v2/wassup.proto\x1a api/items/story/v2/roasted.proto\x1a\x1capi/items/story/v2/pin.proto\"D\n" +
	"\x16ReportShareStatRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\"\x19\n" +
	"\x17ReportShareStatResponse\"U\n" +
	"\x1cBatchGetItemSummariesRequest\x125\n" +
	"\bitem_ids\x18\x01 \x03(\tB\x1a\xfaB\x17\x92\x01\x14\b\x01\x10\x14\x18\x01\"\fr\n" +
	"2\b^[0-9]+$R\aitemIds\"V\n" +
	"\x1dBatchGetItemSummariesResponse\x125\n" +
	"\x05items\x18\x01 \x03(\v2\x1f.api.items.types.v1.ItemSummaryR\x05items\"\xe6\x02\n" +
	"\x11CreateItemRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x123\n" +
	"\x16cover_image_object_key\x18\x03 \x01(\tR\x13coverImageObjectKey\x12:\n" +
	"\x03bgm\x18\x04 \x01(\v2#.api.items.v1.CreateItemRequest.BgmH\x00R\x03bgm\x88\x01\x01\x12?\n" +
	"\fsketchboards\x18\x05 \x03(\v2\x1b.api.artwork.ArtSketchBoardR\fsketchboards\x1a_\n" +
	"\x03Bgm\x122\n" +
	"\x04song\x18\x01 \x01(\v2\x1e.api.items.music.types.v1.SongR\x04song\x12$\n" +
	"\x0eaudio_play_key\x18\x02 \x01(\tR\faudioPlayKeyB\x06\n" +
	"\x04_bgm\"H\n" +
	"\x12CreateItemResponse\x122\n" +
	"\x04item\x18\x01 \x01(\v2\x1e.api.items.types.v1.ItemDetailR\x04item\"b\n" +
	"\x17HomePageTimelineRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xfa\x02\n" +
	"\x18HomePageTimelineResponse\x12Z\n" +
	"\x0etimeline_cards\x18\x01 \x03(\v23.api.items.v1.HomePageTimelineResponse.TimelineCardR\rtimelineCards\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x1a\x85\x01\n" +
	"\fTimelineCard\x12@\n" +
	"\x03tag\x18\x01 \x01(\x0e2..api.items.v1.HomePageTimelineResponse.CardTagR\x03tag\x123\n" +
	"\x04item\x18\x02 \x01(\v2\x1f.api.items.types.v1.ItemSummaryR\x04item\"8\n" +
	"\aCardTag\x12\x18\n" +
	"\x14CARD_TAG_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fCARD_TAG_FRIEND\x10\x01\"\xa3\x01\n" +
	"\n" +
	"AsrRequest\x12(\n" +
	"\x10audio_object_key\x18\x01 \x01(\tR\x0eaudioObjectKey\x12#\n" +
	"\n" +
	"similarity\x18\x02 \x01(\x02H\x00R\n" +
	"similarity\x88\x01\x01\x12&\n" +
	"\fexpected_txt\x18\x03 \x01(\tH\x01R\vexpectedTxt\x88\x01\x01B\r\n" +
	"\v_similarityB\x0f\n" +
	"\r_expected_txt\";\n" +
	"\vAsrResponse\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x18\n" +
	"\amatched\x18\x02 \x01(\bR\amatched2ڊ\x01\n" +
	"\x05Items\x12\x85\x01\n" +
	"\vMusicSearch\x12(.api.items.music.search.v1.SearchRequest\x1a).api.items.music.search.v1.SearchResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/items/music_search\x12\x90\x01\n" +
	"\rCreateComment\x12+.api.items.comments.v1.CreateCommentRequest\x1a,.api.items.comments.v1.CreateCommentResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/items/comments/create\x12\x8b\x01\n" +
	"\fListComments\x12*.api.items.comments.v1.ListCommentsRequest\x1a+.api.items.comments.v1.ListCommentsResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/v1/items/comments/list\x12\xa5\x01\n" +
	"\x12ListCommentReplies\x120.api.items.comments.v1.ListCommentRepliesRequest\x1a1.api.items.comments.v1.ListCommentRepliesResponse\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/v1/items/comments/list_replies\x12\x99\x01\n" +
	"\x14DeleteCommentOrReply\x122.api.items.comments.v1.DeleteCommentOrReplyRequest\x1a\x16.google.protobuf.Empty\"5\x82\xd3\xe4\x93\x02/:\x01*\"*/v1/items/comments/delete_comment_or_reply\x12\x93\x01\n" +
	"\x12LikeCommentOrReply\x120.api.items.comments.v1.LikeCommentOrReplyRequest\x1a\x16.google.protobuf.Empty\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v1/items/comments/like_comment_or_reply\x12\x99\x01\n" +
	"\x14UnlikeCommentOrReply\x122.api.items.comments.v1.UnlikeCommentOrReplyRequest\x1a\x16.google.protobuf.Empty\"5\x82\xd3\xe4\x93\x02/:\x01*\"*/v1/items/comments/unlike_comment_or_reply\x12T\n" +
	"\x03Asr\x12\x18.api.items.v1.AsrRequest\x1a\x19.api.items.v1.AsrResponse\"\x18\x82\xd3\xe4\x93\x02\x12:\x01*\"\r/v1/items/asr\x12\x8a\x01\n" +
	"\x10HomePageTimeline\x12%.api.items.v1.HomePageTimelineRequest\x1a&.api.items.v1.HomePageTimelineResponse\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v1/items/home_page_timeline\x12l\n" +
	"\n" +
	"CreateItem\x12\x1f.api.items.v1.CreateItemRequest\x1a .api.items.v1.CreateItemResponse\"\x1b\x82\xd3\xe4\x93\x02\x15:\x01*\"\x10/v1/items/create\x12\x9f\x01\n" +
	"\x15BatchGetItemSummaries\x12*.api.items.v1.BatchGetItemSummariesRequest\x1a+.api.items.v1.BatchGetItemSummariesResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/items/batch_get_item_summaries\x12\x9f\x01\n" +
	"\x12CreateItemReaction\x120.api.items.reaction.v1.CreateItemReactionRequest\x1a1.api.items.reaction.v1.CreateItemReactionResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/items/reaction/create\x12\x9f\x01\n" +
	"\x12RemoveItemReaction\x120.api.items.reaction.v1.RemoveItemReactionRequest\x1a1.api.items.reaction.v1.RemoveItemReactionResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/v1/items/reaction/remove\x12\xb0\x01\n" +
	"\x14ListUserReactedItems\x122.api.items.reaction.v1.ListUserReactedItemsRequest\x1a3.api.items.reaction.v1.ListUserReactedItemsResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v1/items/reaction/list_user_reacted\x12\xe3\x01\n" +
	"\x1cListUsersByConsumptionStatus\<EMAIL>\x1aA.api.items.story.activity.v1.ListUsersByConsumptionStatusResponse\">\x82\xd3\xe4\x93\x028:\x01*\"3/v1/items/activity/list_users_by_consumption_status\x12\xae\x01\n" +
	"\x0eListActivities\x122.api.items.story.activity.v1.ListActivitiesRequest\x1a3.api.items.story.activity.v1.ListActivitiesResponse\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v1/items/story/activity/list_activities\x12\xc7\x01\n" +
	"\x16GetActivityUnreadCount\x12:.api.items.story.activity.v1.GetActivityUnreadCountRequest\x1a;.api.items.story.activity.v1.GetActivityUnreadCountResponse\"4\x82\xd3\xe4\x93\x02.:\x01*\")/v1/items/story/activity/get_unread_count\x12\x95\x01\n" +
	"\x12ReportActivityRead\x126.api.items.story.activity.v1.ReportActivityReadRequest\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v1/items/story/activity/report_read\x12p\n" +
	"\vDeleteStory\x12&.api.items.story.v1.DeleteStoryRequest\x1a\x16.google.protobuf.Empty\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/items/story/delete\x12\x81\x01\n" +
	"\vUpdateStory\x12&.api.items.story.v1.UpdateStoryRequest\x1a'.api.items.story.v1.UpdateStoryResponse\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/v1/items/story/update\x12\xd3\x01\n" +
	"\x1dListSameAuthorStoryWithAnchor\x128.api.items.story.v1.ListSameAuthorStoryWithAnchorRequest\x1a9.api.items.story.v1.ListSameAuthorStoryWithAnchorResponse\"=\x82\xd3\xe4\x93\x027:\x01*\"2/v1/items/story/list_same_author_story_with_anchor\x12g\n" +
	"\bTopStory\x12#.api.items.story.v1.TopStoryRequest\x1a\x16.google.protobuf.Empty\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/v1/items/story/top\x12\xc7\x01\n" +
	"\x1bListFollowingCreatorStoryV2\x126.api.items.story.v2.ListFollowingCreatorStoryRequestV2\x1a7.api.items.story.v2.ListFollowingCreatorStoryResponseV2\"7\x82\xd3\xe4\x93\x021:\x01*\",/v2/items/story/list_following_creator_story\x12\xd7\x01\n" +
	"\x1cListCommonConditionTemplates\x12<.api.items.story.v1.ListCommonStoryConditionTemplatesRequest\x1a=.api.items.story.v1.ListCommonStoryConditionTemplatesResponse\":\x82\xd3\xe4\x93\x024:\x01*\"//v1/items/story/list_common_condition_templates\x12\xed\x01\n" +
	"%ListTurtleSoupStoryConditionTemplates\<EMAIL>\x1aA.api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse\"?\x82\xd3\xe4\x93\x029:\x01*\"4/v1/items/story/list_turtle_soup_condition_templates\x12\xfa\x01\n" +
	"(ListExchangeImageStoryConditionTemplates\x12C.api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest\x1aD.api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse\"C\x82\xd3\xe4\x93\x02=:\x01*\"8/v1/items/story/list_exchange_images_condition_templetes\x12\xdc\x01\n" +
	"!ListUnmuteStoryConditionTemplates\x12<.api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest\x1a=.api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse\":\x82\xd3\xe4\x93\x024:\x01*\"//v1/items/story/list_unmute_condition_templetes\x12\xbd\x01\n" +
	"\x18CreateExchangeImageStory\x123.api.items.story.v1.CreateExchangeImageStoryRequest\x1a4.api.items.story.v1.CreateExchangeImageStoryResponse\"6\x82\xd3\xe4\x93\x020:\x01*\"+/v1/items/story/create_exchange_image_story\x12\x9a\x01\n" +
	"\x10CreateWhoStoryV2\x12+.api.items.story.v2.CreateWhoStoryRequestV2\x1a,.api.items.story.v2.CreateWhoStoryResponseV2\"+\x82\xd3\xe4\x93\x02%:\x01*\" /v2/items/story/create_who_story\x12\x9e\x01\n" +
	"\x11ConsumeWhoStoryV2\x12,.api.items.story.v2.ConsumeWhoStoryRequestV2\x1a-.api.items.story.v2.ConsumeWhoStoryResponseV2\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v2/items/story/consume_who_story\x12\xc3\x01\n" +
	"\x1aCreateExchangeImageStoryV2\x125.api.items.story.v2.CreateExchangeImageStoryRequestV2\x1a6.api.items.story.v2.CreateExchangeImageStoryResponseV2\"6\x82\xd3\xe4\x93\x020:\x01*\"+/v2/items/story/create_exchange_image_story\x12\xb1\x01\n" +
	"\x15CreateTurtleSoupStory\x120.api.items.story.v1.CreateTurtleSoupStoryRequest\x1a1.api.items.story.v1.CreateTurtleSoupStoryResponse\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v1/items/story/create_turtle_soup_story\x12\x94\x01\n" +
	"\x0eCreatePinStory\x12).api.items.story.v2.CreatePinStoryRequest\x1a*.api.items.story.v2.CreatePinStoryResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /v2/items/story/create_pin_story\x12\x98\x01\n" +
	"\x0fConsumePinStory\x12*.api.items.story.v2.ConsumePinStoryRequest\x1a+.api.items.story.v2.ConsumePinStoryResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v2/items/story/consume_pin_story\x12\xb1\x01\n" +
	"\x15AutoGenerateAreaEmoji\x120.api.items.story.v2.AutoGenerateAreaEmojiRequest\x1a1.api.items.story.v2.AutoGenerateAreaEmojiResponse\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v2/items/story/auto_generate_area_emoji\x12\xb9\x01\n" +
	"\x17ManualGenerateAreaEmoji\x122.api.items.story.v2.ManualGenerateAreaEmojiRequest\x1a3.api.items.story.v2.ManualGenerateAreaEmojiResponse\"5\x82\xd3\xe4\x93\x02/:\x01*\"*/v2/items/story/manual_generate_area_emoji\x12\x9e\x01\n" +
	"\x11CreateHideStoryV2\x12,.api.items.story.v2.CreateHideStoryRequestV2\x1a-.api.items.story.v2.CreateHideStoryResponseV2\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v2/items/story/create_hide_story\x12\xb1\x01\n" +
	"\x15GenerateHideImageMask\x120.api.items.story.v2.GenerateHideImageMaskRequest\x1a1.api.items.story.v2.GenerateHideImageMaskResponse\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v2/items/story/generate_hide_image_mask\x12\x9a\x01\n" +
	"\x12CollectHideSticker\x12;.api.items.story.hide_stickers.v1.CollectHideStickerRequest\x1a\x16.google.protobuf.Empty\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v2/items/story/collect_hide_sticker\x12\xa0\x01\n" +
	"\x14UnCollectHideSticker\x12=.api.items.story.hide_stickers.v1.UnCollectHideStickerRequest\x1a\x16.google.protobuf.Empty\"1\x82\xd3\xe4\x93\x02+:\x01*\"&/v2/items/story/uncollect_hide_sticker\x12\x93\x01\n" +
	"\x0eTopHideSticker\x12<.api.items.story.hide_stickers.v1.TopCollectedStickerRequest\x1a\x16.google.protobuf.Empty\"+\x82\xd3\xe4\x93\x02%:\x01*\" /v2/items/story/top_hide_sticker\x12\x99\x01\n" +
	"\x10UnTopHideSticker\x12>.api.items.story.hide_stickers.v1.UnTopCollectedStickerRequest\x1a\x16.google.protobuf.Empty\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v2/items/story/untop_hide_sticker\x12\xde\x01\n" +
	"\x1bListMyCollectedHideStickers\<EMAIL>.hide_stickers.v1.ListMyCollectedStickersRequest\x1aA.api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse\":\x82\xd3\xe4\x93\x024:\x01*\"//v2/items/story/list_my_collected_hide_stickers\x12\xb7\x01\n" +
	"\x17CreateTurtleSoupStoryV2\x122.api.items.story.v2.CreateTurtleSoupStoryRequestV2\x1a3.api.items.story.v2.CreateTurtleSoupStoryResponseV2\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v2/items/story/create_turtle_soup_story\x12\x9c\x01\n" +
	"\x10GetRoastedTopics\x12+.api.items.story.v1.GetRoastedTopicsRequest\x1a,.api.items.story.v1.GetRoastedTopicsResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/items/story/get_roasted_topics\x12\xa4\x01\n" +
	"\x12CreateRoastedStory\x12-.api.items.story.v1.CreateRoastedStoryRequest\x1a..api.items.story.v1.CreateRoastedStoryResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v1/items/story/create_roasted_story\x12\xa9\x01\n" +
	"\x13CreateBasePlayStory\x12..api.items.story.v1.CreateBasePlayStoryRequest\x1a/.api.items.story.v1.CreateBasePlayStoryResponse\"1\x82\xd3\xe4\x93\x02+:\x01*\"&/v1/items/story/create_base_play_story\x12\xa0\x01\n" +
	"\x11CreateUnmuteStory\x12,.api.items.story.v1.CreateUnmuteStoryRequest\x1a-.api.items.story.v1.CreateUnmuteStoryResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/v1/items/story/create_unmute_story\x12\xb0\x01\n" +
	"\x14CreateChatProxyStory\x121.api.items.story.v2.CreateChatProxyStoryRequestV2\x1a2.api.items.story.v2.CreateChatProxyStoryResponseV2\"1\x82\xd3\xe4\x93\x02+:\x01*\"&/v2/items/story/create_chatproxy_story\x12\xb5\x01\n" +
	"\x15GetChatProxyNextTopic\x122.api.items.story.v2.GetChatProxyNextTopicRequestV2\x1a3.api.items.story.v2.GetChatProxyNextTopicResponseV2\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v2/items/story/get_chatproxy_next_topic\x12\x9f\x01\n" +
	"\x10ConsumeChatProxy\x12-.api.items.story.v2.ConsumeChatProxyRequestV2\x1a..api.items.story.v2.ConsumeChatProxyResponseV2\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v2/items/story/consume_chatproxy\x12\xa6\x01\n" +
	"\x13CreateUnmuteStoryV2\x12..api.items.story.v2.CreateUnmuteStoryRequestV2\x1a/.api.items.story.v2.CreateUnmuteStoryResponseV2\".\x82\xd3\xe4\x93\x02(:\x01*\"#/v2/items/story/create_unmute_story\x12\xa5\x01\n" +
	"\x12CreateNowShotStory\x12-.api.items.story.v1.CreateNowShotStoryRequest\x1a..api.items.story.v1.CreateNowShotStoryResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/create_now_shot_story\x12\xa4\x01\n" +
	"\x14CreateNowShotStoryV2\x12/.api.items.story.v2.CreateNowShotStoryRequestV2\x1a).api.items.story.v2.StoryDetailResponseV2\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v2/items/story/create_now_shot_story\x12\xa4\x01\n" +
	"\x12CreateCapsuleStory\x12-.api.items.story.v1.CreateCapsuleStoryRequest\x1a..api.items.story.v1.CreateCapsuleStoryResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v1/items/story/create_capsule_story\x12\x94\x01\n" +
	"\x0eGetStoryDetail\x12).api.items.story.v1.GetStoryDetailRequest\x1a*.api.items.story.v1.GetStoryDetailResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /v1/items/story/get_story_detail\x12\xa7\x01\n" +
	"\x13ListHomePageStoryV2\x12..api.items.story.v2.ListHomePageStoryRequestV2\x1a/.api.items.story.v2.ListHomePageStoryResponseV2\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v2/items/story/list_home_page_story\x12\x9c\x01\n" +
	"\x10ListCreatorStory\x12+.api.items.story.v1.ListCreatorStoryRequest\x1a,.api.items.story.v1.ListCreatorStoryResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/items/story/list_creator_story\x12\xa2\x01\n" +
	"\x12ListCreatorStoryV2\x12-.api.items.story.v2.ListCreatorStoryRequestV2\x1a..api.items.story.v2.ListCreatorStoryResponseV2\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v2/items/story/list_creator_story\x12\xa0\x01\n" +
	"\x11ListUnlockedStory\x12,.api.items.story.v1.ListUnlockedStoryRequest\x1a-.api.items.story.v1.ListUnlockedStoryResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/v1/items/story/list_unlocked_story\x12\xa8\x01\n" +
	"\x13ConsumeRoastedTopic\x12..api.items.story.v1.ConsumeRoastedTopicRequest\x1a/.api.items.story.v1.ConsumeRoastedTopicResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/consume_roasted_topic\x12\x9c\x01\n" +
	"\x10ConsumeHideStory\x12+.api.items.story.v2.ConsumeHideStoryRequest\x1a,.api.items.story.v2.ConsumeHideStoryResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v2/items/story/consume_hide_story\x12\xa8\x01\n" +
	"\x13ConsumeRoastedStory\x12..api.items.story.v1.ConsumeRoastedStoryRequest\x1a/.api.items.story.v1.ConsumeRoastedStoryResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/consume_roasted_story\x12\xae\x01\n" +
	"\x15ConsumeRoastedStoryV2\x120.api.items.story.v2.ConsumeRoastedStoryRequestV2\x1a1.api.items.story.v2.ConsumeRoastedStoryResponseV2\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v2/items/story/consume_roasted_story\x12\xad\x01\n" +
	"\x14ConsumeBasePlayStory\x12/.api.items.story.v1.ConsumeBasePlayStoryRequest\x1a0.api.items.story.v1.ConsumeBasePlayStoryResponse\"2\x82\xd3\xe4\x93\x02,:\x01*\"'/v1/items/story/consume_base_play_story\x12\xc1\x01\n" +
	"\x19ConsumeExchangeImageStory\x124.api.items.story.v1.ConsumeExchangeImageStoryRequest\x1a5.api.items.story.v1.ConsumeExchangeImageStoryResponse\"7\x82\xd3\xe4\x93\x021:\x01*\",/v1/items/story/consume_exchange_image_story\x12\xb5\x01\n" +
	"\x16ConsumeTurtleSoupStory\x121.api.items.story.v1.ConsumeTurtleSoupStoryRequest\x1a2.api.items.story.v1.ConsumeTurtleSoupStoryResponse\"4\x82\xd3\xe4\x93\x02.:\x01*\")/v1/items/story/consume_turtle_soup_story\x12\xa4\x01\n" +
	"\x12GetWassupGreetings\x12-.api.items.story.v2.GetWassupGreetingsRequest\x1a..api.items.story.v2.GetWassupGreetingsResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v2/items/story/get_wassup_greetings\x12\xb1\x01\n" +
	"\x15GetWassupNextQuestion\x120.api.items.story.v2.GetWassupNextQuestionRequest\x1a1.api.items.story.v2.GetWassupNextQuestionResponse\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v2/items/story/get_wassup_next_question\x12\xa2\x01\n" +
	"\x13CreateWassupStoryV2\x12,.api.items.story.v2.CreateWassupStoryRequest\x1a-.api.items.story.v2.CreateWassupStoryResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/v2/items/story/create_wassup_story\x12\xa6\x01\n" +
	"\x14ConsumeWassupStoryV2\x12-.api.items.story.v2.ConsumeWassupStoryRequest\x1a..api.items.story.v2.ConsumeWassupStoryResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v2/items/story/consume_wassup_story\x12\x87\x01\n" +
	"\vSendMessage\x12&.api.items.story.v2.SendMessageRequest\x1a'.api.items.story.v2.SendMessageResponse\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v2/items/story/send_message\x12\x98\x01\n" +
	"\x0fReportHauntShow\x12*.api.items.story.v2.ReportHauntShowRequest\x1a+.api.items.story.v2.ReportHauntShowResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v2/items/story/report_haunt_show\x12\x9e\x01\n" +
	"\x12CreateHauntStoryV2\x12+.api.items.story.v2.CreateHauntStoryRequest\x1a,.api.items.story.v2.CreateHauntStoryResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v2/items/story/create_haunt_story\x12\xa0\x01\n" +
	"\x11ConsumeHauntStory\x12,.api.items.story.v2.ConsumeHauntStoryRequest\x1a-.api.items.story.v2.ConsumeHauntStoryResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/v2/items/story/consume_haunt_story\x12\x96\x01\n" +
	"\x15SendHauntCaptureVideo\x120.api.items.story.v2.SendHauntCaptureVideoRequest\x1a\x16.google.protobuf.Empty\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v2/items/story/send_haunt_capture_video\x12\xb5\x01\n" +
	"\x16ListHauntRandomAvatars\x121.api.items.story.v2.ListHauntRandomAvatarsRequest\x1a2.api.items.story.v2.ListHauntRandomAvatarsResponse\"4\x82\xd3\xe4\x93\x02.:\x01*\")/v2/items/story/list_haunt_random_avatars\x12\xa4\x01\n" +
	"\x12ListHauntQuestions\x12-.api.items.story.v2.ListHauntQuestionsRequest\x1a..api.items.story.v2.ListHauntQuestionsResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v2/items/story/list_haunt_questions\x12\xa5\x01\n" +
	"\x12ListHauntBooAssist\x12-.api.items.story.v2.ListHauntBooAssistRequest\x1a..api.items.story.v2.ListHauntBooAssistResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v2/items/story/list_haunt_boo_assist\x12\xa4\x01\n" +
	"\x19AddCaptureBooIntoMyAssist\x124.api.items.story.v2.AddCaptureBooIntoMyAssistRequest\x1a\x16.google.protobuf.Empty\"9\x82\xd3\xe4\x93\x023:\x01*\"./v2/items/story/add_capture_boo_into_my_assist\x12\xec\x01\n" +
	"#AddCapturedBooInToCollectedStickers\x12>.api.items.story.v2.AddCapturedBooInToCollectedStickersRequest\x1a?.api.items.story.v2.AddCapturedBooInToCollectedStickersResponse\"D\x82\xd3\xe4\x93\x02>:\x01*\"9/v2/items/story/add_captured_boo_in_to_collected_stickers\x12\x8e\x01\n" +
	"\x0fCheckHauntImage\x12%.api.items.story.v2.ImageCheckRequest\x1a&.api.items.story.v2.ImageCheckResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v2/items/story/check_haunt_image\x12\xa4\x01\n" +
	"\x12ConsumeUnmuteStory\x12-.api.items.story.v1.ConsumeUnmuteStoryRequest\x1a..api.items.story.v1.ConsumeUnmuteStoryResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v1/items/story/consume_unmute_story\x12\xa9\x01\n" +
	"\x13ConsumeNowShotStory\x12..api.items.story.v1.ConsumeNowShotStoryRequest\x1a/.api.items.story.v1.ConsumeNowShotStoryResponse\"1\x82\xd3\xe4\x93\x02+:\x01*\"&/v1/items/story/consume_now_shot_story\x12\xa7\x01\n" +
	"\x15ConsumeNowShotStoryV2\x120.api.items.story.v2.ConsumeNowShotStoryRequestV2\x1a).api.items.story.v2.StoryDetailResponseV2\"1\x82\xd3\xe4\x93\x02+:\x01*\"&/v2/items/story/consume_now_shot_story\x12\xa8\x01\n" +
	"\x13ConsumeCapsuleStory\x12..api.items.story.v1.ConsumeCapsuleStoryRequest\x1a/.api.items.story.v1.ConsumeCapsuleStoryResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/consume_capsule_story\x12\xa8\x01\n" +
	"\x13CopilotCapsuleStory\x12..api.items.story.v1.CopilotCapsuleStoryRequest\x1a/.api.items.story.v1.CopilotCapsuleStoryResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/copilot_capsule_story\x12\xb0\x01\n" +
	"\x13CreateStoryReaction\x122.api.items.story.reaction.v1.CreateReactionRequest\x1a3.api.items.story.reaction.v1.CreateReactionResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/create_story_reaction\x12\xb0\x01\n" +
	"\x13DeleteStoryReaction\x122.api.items.story.reaction.v1.DeleteReactionRequest\x1a3.api.items.story.reaction.v1.DeleteReactionResponse\"0\x82\xd3\xe4\x93\x02*:\x01*\"%/v1/items/story/delete_story_reaction\x12\xc8\x01\n" +
	"\x1aListStoryReactionMadeUsers\x129.api.items.story.reaction.v1.ListReactionMadeUsersRequest\x1a:.api.items.story.reaction.v1.ListReactionMadeUsersResponse\"3\x82\xd3\xe4\x93\x02-:\x01*\"(/v1/items/story/list_reaction_made_users\x12\x8d\x01\n" +
	"\x0fReportShareStat\x12$.api.items.v1.ReportShareStatRequest\x1a%.api.items.v1.ReportShareStatResponse\"-\x82\xd3\xe4\x93\x02':\x01*\"\"/v1/items/story/report_share_stats\x12\x8e\x01\n" +
	"\fCreateMoment\x12(.api.items.portal.v1.CreateMomentRequest\x1a).api.items.portal.v1.CreateMomentResponse\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/items/portal/create_moment\x12\xc5\x01\n" +
	"\x19GetUserCreatedPortalsInfo\x125.api.items.portal.v1.GetUserCreatedPortalsInfoRequest\x1a6.api.items.portal.v1.GetUserCreatedPortalsInfoResponse\"9\x82\xd3\xe4\x93\x023:\x01*\"./v1/items/portal/get_user_created_portals_info\x12{\n" +
	"\fDeleteMoment\x12(.api.items.portal.v1.DeleteMomentRequest\x1a\x16.google.protobuf.Empty\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/v1/items/portal/delete_moment\x12\xaf\x01\n" +
	"\x14CreateMomentRelation\x120.api.items.portal.v1.CreateMomentRelationRequest\x1a1.api.items.portal.v1.CreateMomentRelationResponse\"2\x82\xd3\xe4\x93\x02,:\x01*\"'/v1/items/portal/create_moment_relation\x12\xaf\x01\n" +
	"\x14RemoveMomentRelation\x120.api.items.portal.v1.RemoveMomentRelationRequest\x1a1.api.items.portal.v1.RemoveMomentRelationResponse\"2\x82\xd3\xe4\x93\x02,:\x01*\"'/v1/items/portal/remove_moment_relation\x12\xef\x01\n" +
	"#ListUserCreatedPortalsWithTimeRange\x12?.api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest\<EMAIL>\"E\x82\xd3\xe4\x93\x02?:\x01*\":/v1/items/portal/list_user_created_portals_with_time_range\x12\x93\x01\n" +
	"\rListMyPortals\x12).api.items.portal.v1.ListMyPortalsRequest\x1a*.api.items.portal.v1.ListMyPortalsResponse\"+\x82\xd3\xe4\x93\x02%:\x01*\" /v1/items/portal/list_my_portals\x12\xd1\x01\n" +
	"\x1cListCouldAppendMomentStories\x128.api.items.portal.v1.ListCouldAppendMomentStoriesRequest\x1a9.api.items.portal.v1.ListCouldAppendMomentStoriesResponse\"<\x82\xd3\xe4\x93\x026:\x01*\"1/v1/items/portal/list_could_append_moment_stories\x12u\n" +
	"\n" +
	"ReportRead\x12&.api.items.portal.v1.ReportReadRequest\x1a\x16.google.protobuf.Empty\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/v1/items/portal/report_read\x12\x82\x01\n" +
	"\tGetPortal\x12%.api.items.portal.v1.GetPortalRequest\x1a&.api.items.portal.v1.GetPortalResponse\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/items/portal/get_portal\x12\xa3\x01\n" +
	"\x11ListMomentViewers\x12-.api.items.portal.v1.ListMomentViewersRequest\x1a..api.items.portal.v1.ListMomentViewersResponse\"/\x82\xd3\xe4\x93\x02):\x01*\"$/v1/items/portal/list_moment_viewers\x12\xa6\x01\n" +
	"\x13ListTrendingPortals\x12/.api.items.portal.v1.ListTrendingPortalsRequest\x1a0.api.items.portal.v1.ListTrendingPortalsResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/v1/items/portal/trending_portals\x12\x82\x01\n" +
	"\tGetMoment\x12%.api.items.portal.v1.GetMomentRequest\x1a&.api.items.portal.v1.GetMomentResponse\"&\x82\xd3\xe4\x93\x02 :\x01*\"\x1b/v1/items/portal/get_moment\x12\x9f\x01\n" +
	"\x10SendMomentInvite\x12,.api.items.portal.v1.SendMomentInviteRequest\x1a-.api.items.portal.v1.SendMomentInviteResponse\".\x82\xd3\xe4\x93\x02(:\x01*\"#/v1/items/portal/send_moment_inviteB\x17Z\x15boson/api/items/v1;v1b\x06proto3"

var (
	file_api_items_v1_items_proto_rawDescOnce sync.Once
	file_api_items_v1_items_proto_rawDescData []byte
)

func file_api_items_v1_items_proto_rawDescGZIP() []byte {
	file_api_items_v1_items_proto_rawDescOnce.Do(func() {
		file_api_items_v1_items_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_v1_items_proto_rawDesc), len(file_api_items_v1_items_proto_rawDesc)))
	})
	return file_api_items_v1_items_proto_rawDescData
}

var file_api_items_v1_items_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_items_v1_items_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_api_items_v1_items_proto_goTypes = []any{
	(HomePageTimelineResponse_CardTag)(0),                        // 0: api.items.v1.HomePageTimelineResponse.CardTag
	(*ReportShareStatRequest)(nil),                               // 1: api.items.v1.ReportShareStatRequest
	(*ReportShareStatResponse)(nil),                              // 2: api.items.v1.ReportShareStatResponse
	(*BatchGetItemSummariesRequest)(nil),                         // 3: api.items.v1.BatchGetItemSummariesRequest
	(*BatchGetItemSummariesResponse)(nil),                        // 4: api.items.v1.BatchGetItemSummariesResponse
	(*CreateItemRequest)(nil),                                    // 5: api.items.v1.CreateItemRequest
	(*CreateItemResponse)(nil),                                   // 6: api.items.v1.CreateItemResponse
	(*HomePageTimelineRequest)(nil),                              // 7: api.items.v1.HomePageTimelineRequest
	(*HomePageTimelineResponse)(nil),                             // 8: api.items.v1.HomePageTimelineResponse
	(*AsrRequest)(nil),                                           // 9: api.items.v1.AsrRequest
	(*AsrResponse)(nil),                                          // 10: api.items.v1.AsrResponse
	(*CreateItemRequest_Bgm)(nil),                                // 11: api.items.v1.CreateItemRequest.Bgm
	(*HomePageTimelineResponse_TimelineCard)(nil),                // 12: api.items.v1.HomePageTimelineResponse.TimelineCard
	(*v1.ItemSummary)(nil),                                       // 13: api.items.types.v1.ItemSummary
	(*artwork.ArtSketchBoard)(nil),                               // 14: api.artwork.ArtSketchBoard
	(*v1.ItemDetail)(nil),                                        // 15: api.items.types.v1.ItemDetail
	(*v11.ListRequest)(nil),                                      // 16: api.common.v1.ListRequest
	(*v11.ListResponse)(nil),                                     // 17: api.common.v1.ListResponse
	(*v12.Song)(nil),                                             // 18: api.items.music.types.v1.Song
	(*v13.SearchRequest)(nil),                                    // 19: api.items.music.search.v1.SearchRequest
	(*v14.CreateCommentRequest)(nil),                             // 20: api.items.comments.v1.CreateCommentRequest
	(*v14.ListCommentsRequest)(nil),                              // 21: api.items.comments.v1.ListCommentsRequest
	(*v14.ListCommentRepliesRequest)(nil),                        // 22: api.items.comments.v1.ListCommentRepliesRequest
	(*v14.DeleteCommentOrReplyRequest)(nil),                      // 23: api.items.comments.v1.DeleteCommentOrReplyRequest
	(*v14.LikeCommentOrReplyRequest)(nil),                        // 24: api.items.comments.v1.LikeCommentOrReplyRequest
	(*v14.UnlikeCommentOrReplyRequest)(nil),                      // 25: api.items.comments.v1.UnlikeCommentOrReplyRequest
	(*v15.CreateItemReactionRequest)(nil),                        // 26: api.items.reaction.v1.CreateItemReactionRequest
	(*v15.RemoveItemReactionRequest)(nil),                        // 27: api.items.reaction.v1.RemoveItemReactionRequest
	(*v15.ListUserReactedItemsRequest)(nil),                      // 28: api.items.reaction.v1.ListUserReactedItemsRequest
	(*v16.ListUsersByConsumptionStatusRequest)(nil),              // 29: api.items.story.activity.v1.ListUsersByConsumptionStatusRequest
	(*v16.ListActivitiesRequest)(nil),                            // 30: api.items.story.activity.v1.ListActivitiesRequest
	(*v16.GetActivityUnreadCountRequest)(nil),                    // 31: api.items.story.activity.v1.GetActivityUnreadCountRequest
	(*v16.ReportActivityReadRequest)(nil),                        // 32: api.items.story.activity.v1.ReportActivityReadRequest
	(*v17.DeleteStoryRequest)(nil),                               // 33: api.items.story.v1.DeleteStoryRequest
	(*v17.UpdateStoryRequest)(nil),                               // 34: api.items.story.v1.UpdateStoryRequest
	(*v17.ListSameAuthorStoryWithAnchorRequest)(nil),             // 35: api.items.story.v1.ListSameAuthorStoryWithAnchorRequest
	(*v17.TopStoryRequest)(nil),                                  // 36: api.items.story.v1.TopStoryRequest
	(*v2.ListFollowingCreatorStoryRequestV2)(nil),                // 37: api.items.story.v2.ListFollowingCreatorStoryRequestV2
	(*v17.ListCommonStoryConditionTemplatesRequest)(nil),         // 38: api.items.story.v1.ListCommonStoryConditionTemplatesRequest
	(*v17.ListTurtleSoupStoryConditionTemplatesRequest)(nil),     // 39: api.items.story.v1.ListTurtleSoupStoryConditionTemplatesRequest
	(*v17.ListExchangeImageStoryConditionTemplatesRequest)(nil),  // 40: api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest
	(*v17.ListUnmuteStoryConditionTemplatesRequest)(nil),         // 41: api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest
	(*v17.CreateExchangeImageStoryRequest)(nil),                  // 42: api.items.story.v1.CreateExchangeImageStoryRequest
	(*v2.CreateWhoStoryRequestV2)(nil),                           // 43: api.items.story.v2.CreateWhoStoryRequestV2
	(*v2.ConsumeWhoStoryRequestV2)(nil),                          // 44: api.items.story.v2.ConsumeWhoStoryRequestV2
	(*v2.CreateExchangeImageStoryRequestV2)(nil),                 // 45: api.items.story.v2.CreateExchangeImageStoryRequestV2
	(*v17.CreateTurtleSoupStoryRequest)(nil),                     // 46: api.items.story.v1.CreateTurtleSoupStoryRequest
	(*v2.CreatePinStoryRequest)(nil),                             // 47: api.items.story.v2.CreatePinStoryRequest
	(*v2.ConsumePinStoryRequest)(nil),                            // 48: api.items.story.v2.ConsumePinStoryRequest
	(*v2.AutoGenerateAreaEmojiRequest)(nil),                      // 49: api.items.story.v2.AutoGenerateAreaEmojiRequest
	(*v2.ManualGenerateAreaEmojiRequest)(nil),                    // 50: api.items.story.v2.ManualGenerateAreaEmojiRequest
	(*v2.CreateHideStoryRequestV2)(nil),                          // 51: api.items.story.v2.CreateHideStoryRequestV2
	(*v2.GenerateHideImageMaskRequest)(nil),                      // 52: api.items.story.v2.GenerateHideImageMaskRequest
	(*v18.CollectHideStickerRequest)(nil),                        // 53: api.items.story.hide_stickers.v1.CollectHideStickerRequest
	(*v18.UnCollectHideStickerRequest)(nil),                      // 54: api.items.story.hide_stickers.v1.UnCollectHideStickerRequest
	(*v18.TopCollectedStickerRequest)(nil),                       // 55: api.items.story.hide_stickers.v1.TopCollectedStickerRequest
	(*v18.UnTopCollectedStickerRequest)(nil),                     // 56: api.items.story.hide_stickers.v1.UnTopCollectedStickerRequest
	(*v18.ListMyCollectedStickersRequest)(nil),                   // 57: api.items.story.hide_stickers.v1.ListMyCollectedStickersRequest
	(*v2.CreateTurtleSoupStoryRequestV2)(nil),                    // 58: api.items.story.v2.CreateTurtleSoupStoryRequestV2
	(*v17.GetRoastedTopicsRequest)(nil),                          // 59: api.items.story.v1.GetRoastedTopicsRequest
	(*v17.CreateRoastedStoryRequest)(nil),                        // 60: api.items.story.v1.CreateRoastedStoryRequest
	(*v17.CreateBasePlayStoryRequest)(nil),                       // 61: api.items.story.v1.CreateBasePlayStoryRequest
	(*v17.CreateUnmuteStoryRequest)(nil),                         // 62: api.items.story.v1.CreateUnmuteStoryRequest
	(*v2.CreateChatProxyStoryRequestV2)(nil),                     // 63: api.items.story.v2.CreateChatProxyStoryRequestV2
	(*v2.GetChatProxyNextTopicRequestV2)(nil),                    // 64: api.items.story.v2.GetChatProxyNextTopicRequestV2
	(*v2.ConsumeChatProxyRequestV2)(nil),                         // 65: api.items.story.v2.ConsumeChatProxyRequestV2
	(*v2.CreateUnmuteStoryRequestV2)(nil),                        // 66: api.items.story.v2.CreateUnmuteStoryRequestV2
	(*v17.CreateNowShotStoryRequest)(nil),                        // 67: api.items.story.v1.CreateNowShotStoryRequest
	(*v2.CreateNowShotStoryRequestV2)(nil),                       // 68: api.items.story.v2.CreateNowShotStoryRequestV2
	(*v17.CreateCapsuleStoryRequest)(nil),                        // 69: api.items.story.v1.CreateCapsuleStoryRequest
	(*v17.GetStoryDetailRequest)(nil),                            // 70: api.items.story.v1.GetStoryDetailRequest
	(*v2.ListHomePageStoryRequestV2)(nil),                        // 71: api.items.story.v2.ListHomePageStoryRequestV2
	(*v17.ListCreatorStoryRequest)(nil),                          // 72: api.items.story.v1.ListCreatorStoryRequest
	(*v2.ListCreatorStoryRequestV2)(nil),                         // 73: api.items.story.v2.ListCreatorStoryRequestV2
	(*v17.ListUnlockedStoryRequest)(nil),                         // 74: api.items.story.v1.ListUnlockedStoryRequest
	(*v17.ConsumeRoastedTopicRequest)(nil),                       // 75: api.items.story.v1.ConsumeRoastedTopicRequest
	(*v2.ConsumeHideStoryRequest)(nil),                           // 76: api.items.story.v2.ConsumeHideStoryRequest
	(*v17.ConsumeRoastedStoryRequest)(nil),                       // 77: api.items.story.v1.ConsumeRoastedStoryRequest
	(*v2.ConsumeRoastedStoryRequestV2)(nil),                      // 78: api.items.story.v2.ConsumeRoastedStoryRequestV2
	(*v17.ConsumeBasePlayStoryRequest)(nil),                      // 79: api.items.story.v1.ConsumeBasePlayStoryRequest
	(*v17.ConsumeExchangeImageStoryRequest)(nil),                 // 80: api.items.story.v1.ConsumeExchangeImageStoryRequest
	(*v17.ConsumeTurtleSoupStoryRequest)(nil),                    // 81: api.items.story.v1.ConsumeTurtleSoupStoryRequest
	(*v2.GetWassupGreetingsRequest)(nil),                         // 82: api.items.story.v2.GetWassupGreetingsRequest
	(*v2.GetWassupNextQuestionRequest)(nil),                      // 83: api.items.story.v2.GetWassupNextQuestionRequest
	(*v2.CreateWassupStoryRequest)(nil),                          // 84: api.items.story.v2.CreateWassupStoryRequest
	(*v2.ConsumeWassupStoryRequest)(nil),                         // 85: api.items.story.v2.ConsumeWassupStoryRequest
	(*v2.SendMessageRequest)(nil),                                // 86: api.items.story.v2.SendMessageRequest
	(*v2.ReportHauntShowRequest)(nil),                            // 87: api.items.story.v2.ReportHauntShowRequest
	(*v2.CreateHauntStoryRequest)(nil),                           // 88: api.items.story.v2.CreateHauntStoryRequest
	(*v2.ConsumeHauntStoryRequest)(nil),                          // 89: api.items.story.v2.ConsumeHauntStoryRequest
	(*v2.SendHauntCaptureVideoRequest)(nil),                      // 90: api.items.story.v2.SendHauntCaptureVideoRequest
	(*v2.ListHauntRandomAvatarsRequest)(nil),                     // 91: api.items.story.v2.ListHauntRandomAvatarsRequest
	(*v2.ListHauntQuestionsRequest)(nil),                         // 92: api.items.story.v2.ListHauntQuestionsRequest
	(*v2.ListHauntBooAssistRequest)(nil),                         // 93: api.items.story.v2.ListHauntBooAssistRequest
	(*v2.AddCaptureBooIntoMyAssistRequest)(nil),                  // 94: api.items.story.v2.AddCaptureBooIntoMyAssistRequest
	(*v2.AddCapturedBooInToCollectedStickersRequest)(nil),        // 95: api.items.story.v2.AddCapturedBooInToCollectedStickersRequest
	(*v2.ImageCheckRequest)(nil),                                 // 96: api.items.story.v2.ImageCheckRequest
	(*v17.ConsumeUnmuteStoryRequest)(nil),                        // 97: api.items.story.v1.ConsumeUnmuteStoryRequest
	(*v17.ConsumeNowShotStoryRequest)(nil),                       // 98: api.items.story.v1.ConsumeNowShotStoryRequest
	(*v2.ConsumeNowShotStoryRequestV2)(nil),                      // 99: api.items.story.v2.ConsumeNowShotStoryRequestV2
	(*v17.ConsumeCapsuleStoryRequest)(nil),                       // 100: api.items.story.v1.ConsumeCapsuleStoryRequest
	(*v17.CopilotCapsuleStoryRequest)(nil),                       // 101: api.items.story.v1.CopilotCapsuleStoryRequest
	(*v19.CreateReactionRequest)(nil),                            // 102: api.items.story.reaction.v1.CreateReactionRequest
	(*v19.DeleteReactionRequest)(nil),                            // 103: api.items.story.reaction.v1.DeleteReactionRequest
	(*v19.ListReactionMadeUsersRequest)(nil),                     // 104: api.items.story.reaction.v1.ListReactionMadeUsersRequest
	(*v110.CreateMomentRequest)(nil),                             // 105: api.items.portal.v1.CreateMomentRequest
	(*v110.GetUserCreatedPortalsInfoRequest)(nil),                // 106: api.items.portal.v1.GetUserCreatedPortalsInfoRequest
	(*v110.DeleteMomentRequest)(nil),                             // 107: api.items.portal.v1.DeleteMomentRequest
	(*v110.CreateMomentRelationRequest)(nil),                     // 108: api.items.portal.v1.CreateMomentRelationRequest
	(*v110.RemoveMomentRelationRequest)(nil),                     // 109: api.items.portal.v1.RemoveMomentRelationRequest
	(*v110.ListUserCreatedPortalsWithTimeRangeRequest)(nil),      // 110: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest
	(*v110.ListMyPortalsRequest)(nil),                            // 111: api.items.portal.v1.ListMyPortalsRequest
	(*v110.ListCouldAppendMomentStoriesRequest)(nil),             // 112: api.items.portal.v1.ListCouldAppendMomentStoriesRequest
	(*v110.ReportReadRequest)(nil),                               // 113: api.items.portal.v1.ReportReadRequest
	(*v110.GetPortalRequest)(nil),                                // 114: api.items.portal.v1.GetPortalRequest
	(*v110.ListMomentViewersRequest)(nil),                        // 115: api.items.portal.v1.ListMomentViewersRequest
	(*v110.ListTrendingPortalsRequest)(nil),                      // 116: api.items.portal.v1.ListTrendingPortalsRequest
	(*v110.GetMomentRequest)(nil),                                // 117: api.items.portal.v1.GetMomentRequest
	(*v110.SendMomentInviteRequest)(nil),                         // 118: api.items.portal.v1.SendMomentInviteRequest
	(*v13.SearchResponse)(nil),                                   // 119: api.items.music.search.v1.SearchResponse
	(*v14.CreateCommentResponse)(nil),                            // 120: api.items.comments.v1.CreateCommentResponse
	(*v14.ListCommentsResponse)(nil),                             // 121: api.items.comments.v1.ListCommentsResponse
	(*v14.ListCommentRepliesResponse)(nil),                       // 122: api.items.comments.v1.ListCommentRepliesResponse
	(*emptypb.Empty)(nil),                                        // 123: google.protobuf.Empty
	(*v15.CreateItemReactionResponse)(nil),                       // 124: api.items.reaction.v1.CreateItemReactionResponse
	(*v15.RemoveItemReactionResponse)(nil),                       // 125: api.items.reaction.v1.RemoveItemReactionResponse
	(*v15.ListUserReactedItemsResponse)(nil),                     // 126: api.items.reaction.v1.ListUserReactedItemsResponse
	(*v16.ListUsersByConsumptionStatusResponse)(nil),             // 127: api.items.story.activity.v1.ListUsersByConsumptionStatusResponse
	(*v16.ListActivitiesResponse)(nil),                           // 128: api.items.story.activity.v1.ListActivitiesResponse
	(*v16.GetActivityUnreadCountResponse)(nil),                   // 129: api.items.story.activity.v1.GetActivityUnreadCountResponse
	(*v17.UpdateStoryResponse)(nil),                              // 130: api.items.story.v1.UpdateStoryResponse
	(*v17.ListSameAuthorStoryWithAnchorResponse)(nil),            // 131: api.items.story.v1.ListSameAuthorStoryWithAnchorResponse
	(*v2.ListFollowingCreatorStoryResponseV2)(nil),               // 132: api.items.story.v2.ListFollowingCreatorStoryResponseV2
	(*v17.ListCommonStoryConditionTemplatesResponse)(nil),        // 133: api.items.story.v1.ListCommonStoryConditionTemplatesResponse
	(*v17.ListTurtleSoupStoryConditionTemplatesResponse)(nil),    // 134: api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse
	(*v17.ListExchangeImageStoryConditionTemplatesResponse)(nil), // 135: api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse
	(*v17.ListUnmuteStoryConditionTemplatesResponse)(nil),        // 136: api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse
	(*v17.CreateExchangeImageStoryResponse)(nil),                 // 137: api.items.story.v1.CreateExchangeImageStoryResponse
	(*v2.CreateWhoStoryResponseV2)(nil),                          // 138: api.items.story.v2.CreateWhoStoryResponseV2
	(*v2.ConsumeWhoStoryResponseV2)(nil),                         // 139: api.items.story.v2.ConsumeWhoStoryResponseV2
	(*v2.CreateExchangeImageStoryResponseV2)(nil),                // 140: api.items.story.v2.CreateExchangeImageStoryResponseV2
	(*v17.CreateTurtleSoupStoryResponse)(nil),                    // 141: api.items.story.v1.CreateTurtleSoupStoryResponse
	(*v2.CreatePinStoryResponse)(nil),                            // 142: api.items.story.v2.CreatePinStoryResponse
	(*v2.ConsumePinStoryResponse)(nil),                           // 143: api.items.story.v2.ConsumePinStoryResponse
	(*v2.AutoGenerateAreaEmojiResponse)(nil),                     // 144: api.items.story.v2.AutoGenerateAreaEmojiResponse
	(*v2.ManualGenerateAreaEmojiResponse)(nil),                   // 145: api.items.story.v2.ManualGenerateAreaEmojiResponse
	(*v2.CreateHideStoryResponseV2)(nil),                         // 146: api.items.story.v2.CreateHideStoryResponseV2
	(*v2.GenerateHideImageMaskResponse)(nil),                     // 147: api.items.story.v2.GenerateHideImageMaskResponse
	(*v18.ListMyCollectedStickersResponse)(nil),                  // 148: api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse
	(*v2.CreateTurtleSoupStoryResponseV2)(nil),                   // 149: api.items.story.v2.CreateTurtleSoupStoryResponseV2
	(*v17.GetRoastedTopicsResponse)(nil),                         // 150: api.items.story.v1.GetRoastedTopicsResponse
	(*v17.CreateRoastedStoryResponse)(nil),                       // 151: api.items.story.v1.CreateRoastedStoryResponse
	(*v17.CreateBasePlayStoryResponse)(nil),                      // 152: api.items.story.v1.CreateBasePlayStoryResponse
	(*v17.CreateUnmuteStoryResponse)(nil),                        // 153: api.items.story.v1.CreateUnmuteStoryResponse
	(*v2.CreateChatProxyStoryResponseV2)(nil),                    // 154: api.items.story.v2.CreateChatProxyStoryResponseV2
	(*v2.GetChatProxyNextTopicResponseV2)(nil),                   // 155: api.items.story.v2.GetChatProxyNextTopicResponseV2
	(*v2.ConsumeChatProxyResponseV2)(nil),                        // 156: api.items.story.v2.ConsumeChatProxyResponseV2
	(*v2.CreateUnmuteStoryResponseV2)(nil),                       // 157: api.items.story.v2.CreateUnmuteStoryResponseV2
	(*v17.CreateNowShotStoryResponse)(nil),                       // 158: api.items.story.v1.CreateNowShotStoryResponse
	(*v2.StoryDetailResponseV2)(nil),                             // 159: api.items.story.v2.StoryDetailResponseV2
	(*v17.CreateCapsuleStoryResponse)(nil),                       // 160: api.items.story.v1.CreateCapsuleStoryResponse
	(*v17.GetStoryDetailResponse)(nil),                           // 161: api.items.story.v1.GetStoryDetailResponse
	(*v2.ListHomePageStoryResponseV2)(nil),                       // 162: api.items.story.v2.ListHomePageStoryResponseV2
	(*v17.ListCreatorStoryResponse)(nil),                         // 163: api.items.story.v1.ListCreatorStoryResponse
	(*v2.ListCreatorStoryResponseV2)(nil),                        // 164: api.items.story.v2.ListCreatorStoryResponseV2
	(*v17.ListUnlockedStoryResponse)(nil),                        // 165: api.items.story.v1.ListUnlockedStoryResponse
	(*v17.ConsumeRoastedTopicResponse)(nil),                      // 166: api.items.story.v1.ConsumeRoastedTopicResponse
	(*v2.ConsumeHideStoryResponse)(nil),                          // 167: api.items.story.v2.ConsumeHideStoryResponse
	(*v17.ConsumeRoastedStoryResponse)(nil),                      // 168: api.items.story.v1.ConsumeRoastedStoryResponse
	(*v2.ConsumeRoastedStoryResponseV2)(nil),                     // 169: api.items.story.v2.ConsumeRoastedStoryResponseV2
	(*v17.ConsumeBasePlayStoryResponse)(nil),                     // 170: api.items.story.v1.ConsumeBasePlayStoryResponse
	(*v17.ConsumeExchangeImageStoryResponse)(nil),                // 171: api.items.story.v1.ConsumeExchangeImageStoryResponse
	(*v17.ConsumeTurtleSoupStoryResponse)(nil),                   // 172: api.items.story.v1.ConsumeTurtleSoupStoryResponse
	(*v2.GetWassupGreetingsResponse)(nil),                        // 173: api.items.story.v2.GetWassupGreetingsResponse
	(*v2.GetWassupNextQuestionResponse)(nil),                     // 174: api.items.story.v2.GetWassupNextQuestionResponse
	(*v2.CreateWassupStoryResponse)(nil),                         // 175: api.items.story.v2.CreateWassupStoryResponse
	(*v2.ConsumeWassupStoryResponse)(nil),                        // 176: api.items.story.v2.ConsumeWassupStoryResponse
	(*v2.SendMessageResponse)(nil),                               // 177: api.items.story.v2.SendMessageResponse
	(*v2.ReportHauntShowResponse)(nil),                           // 178: api.items.story.v2.ReportHauntShowResponse
	(*v2.CreateHauntStoryResponse)(nil),                          // 179: api.items.story.v2.CreateHauntStoryResponse
	(*v2.ConsumeHauntStoryResponse)(nil),                         // 180: api.items.story.v2.ConsumeHauntStoryResponse
	(*v2.ListHauntRandomAvatarsResponse)(nil),                    // 181: api.items.story.v2.ListHauntRandomAvatarsResponse
	(*v2.ListHauntQuestionsResponse)(nil),                        // 182: api.items.story.v2.ListHauntQuestionsResponse
	(*v2.ListHauntBooAssistResponse)(nil),                        // 183: api.items.story.v2.ListHauntBooAssistResponse
	(*v2.AddCapturedBooInToCollectedStickersResponse)(nil),       // 184: api.items.story.v2.AddCapturedBooInToCollectedStickersResponse
	(*v2.ImageCheckResponse)(nil),                                // 185: api.items.story.v2.ImageCheckResponse
	(*v17.ConsumeUnmuteStoryResponse)(nil),                       // 186: api.items.story.v1.ConsumeUnmuteStoryResponse
	(*v17.ConsumeNowShotStoryResponse)(nil),                      // 187: api.items.story.v1.ConsumeNowShotStoryResponse
	(*v17.ConsumeCapsuleStoryResponse)(nil),                      // 188: api.items.story.v1.ConsumeCapsuleStoryResponse
	(*v17.CopilotCapsuleStoryResponse)(nil),                      // 189: api.items.story.v1.CopilotCapsuleStoryResponse
	(*v19.CreateReactionResponse)(nil),                           // 190: api.items.story.reaction.v1.CreateReactionResponse
	(*v19.DeleteReactionResponse)(nil),                           // 191: api.items.story.reaction.v1.DeleteReactionResponse
	(*v19.ListReactionMadeUsersResponse)(nil),                    // 192: api.items.story.reaction.v1.ListReactionMadeUsersResponse
	(*v110.CreateMomentResponse)(nil),                            // 193: api.items.portal.v1.CreateMomentResponse
	(*v110.GetUserCreatedPortalsInfoResponse)(nil),               // 194: api.items.portal.v1.GetUserCreatedPortalsInfoResponse
	(*v110.CreateMomentRelationResponse)(nil),                    // 195: api.items.portal.v1.CreateMomentRelationResponse
	(*v110.RemoveMomentRelationResponse)(nil),                    // 196: api.items.portal.v1.RemoveMomentRelationResponse
	(*v110.ListUserCreatedPortalsWithTimeRangeResponse)(nil),     // 197: api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse
	(*v110.ListMyPortalsResponse)(nil),                           // 198: api.items.portal.v1.ListMyPortalsResponse
	(*v110.ListCouldAppendMomentStoriesResponse)(nil),            // 199: api.items.portal.v1.ListCouldAppendMomentStoriesResponse
	(*v110.GetPortalResponse)(nil),                               // 200: api.items.portal.v1.GetPortalResponse
	(*v110.ListMomentViewersResponse)(nil),                       // 201: api.items.portal.v1.ListMomentViewersResponse
	(*v110.ListTrendingPortalsResponse)(nil),                     // 202: api.items.portal.v1.ListTrendingPortalsResponse
	(*v110.GetMomentResponse)(nil),                               // 203: api.items.portal.v1.GetMomentResponse
	(*v110.SendMomentInviteResponse)(nil),                        // 204: api.items.portal.v1.SendMomentInviteResponse
}
var file_api_items_v1_items_proto_depIdxs = []int32{
	13,  // 0: api.items.v1.BatchGetItemSummariesResponse.items:type_name -> api.items.types.v1.ItemSummary
	11,  // 1: api.items.v1.CreateItemRequest.bgm:type_name -> api.items.v1.CreateItemRequest.Bgm
	14,  // 2: api.items.v1.CreateItemRequest.sketchboards:type_name -> api.artwork.ArtSketchBoard
	15,  // 3: api.items.v1.CreateItemResponse.item:type_name -> api.items.types.v1.ItemDetail
	16,  // 4: api.items.v1.HomePageTimelineRequest.list_request:type_name -> api.common.v1.ListRequest
	12,  // 5: api.items.v1.HomePageTimelineResponse.timeline_cards:type_name -> api.items.v1.HomePageTimelineResponse.TimelineCard
	17,  // 6: api.items.v1.HomePageTimelineResponse.list_response:type_name -> api.common.v1.ListResponse
	18,  // 7: api.items.v1.CreateItemRequest.Bgm.song:type_name -> api.items.music.types.v1.Song
	0,   // 8: api.items.v1.HomePageTimelineResponse.TimelineCard.tag:type_name -> api.items.v1.HomePageTimelineResponse.CardTag
	13,  // 9: api.items.v1.HomePageTimelineResponse.TimelineCard.item:type_name -> api.items.types.v1.ItemSummary
	19,  // 10: api.items.v1.Items.MusicSearch:input_type -> api.items.music.search.v1.SearchRequest
	20,  // 11: api.items.v1.Items.CreateComment:input_type -> api.items.comments.v1.CreateCommentRequest
	21,  // 12: api.items.v1.Items.ListComments:input_type -> api.items.comments.v1.ListCommentsRequest
	22,  // 13: api.items.v1.Items.ListCommentReplies:input_type -> api.items.comments.v1.ListCommentRepliesRequest
	23,  // 14: api.items.v1.Items.DeleteCommentOrReply:input_type -> api.items.comments.v1.DeleteCommentOrReplyRequest
	24,  // 15: api.items.v1.Items.LikeCommentOrReply:input_type -> api.items.comments.v1.LikeCommentOrReplyRequest
	25,  // 16: api.items.v1.Items.UnlikeCommentOrReply:input_type -> api.items.comments.v1.UnlikeCommentOrReplyRequest
	9,   // 17: api.items.v1.Items.Asr:input_type -> api.items.v1.AsrRequest
	7,   // 18: api.items.v1.Items.HomePageTimeline:input_type -> api.items.v1.HomePageTimelineRequest
	5,   // 19: api.items.v1.Items.CreateItem:input_type -> api.items.v1.CreateItemRequest
	3,   // 20: api.items.v1.Items.BatchGetItemSummaries:input_type -> api.items.v1.BatchGetItemSummariesRequest
	26,  // 21: api.items.v1.Items.CreateItemReaction:input_type -> api.items.reaction.v1.CreateItemReactionRequest
	27,  // 22: api.items.v1.Items.RemoveItemReaction:input_type -> api.items.reaction.v1.RemoveItemReactionRequest
	28,  // 23: api.items.v1.Items.ListUserReactedItems:input_type -> api.items.reaction.v1.ListUserReactedItemsRequest
	29,  // 24: api.items.v1.Items.ListUsersByConsumptionStatus:input_type -> api.items.story.activity.v1.ListUsersByConsumptionStatusRequest
	30,  // 25: api.items.v1.Items.ListActivities:input_type -> api.items.story.activity.v1.ListActivitiesRequest
	31,  // 26: api.items.v1.Items.GetActivityUnreadCount:input_type -> api.items.story.activity.v1.GetActivityUnreadCountRequest
	32,  // 27: api.items.v1.Items.ReportActivityRead:input_type -> api.items.story.activity.v1.ReportActivityReadRequest
	33,  // 28: api.items.v1.Items.DeleteStory:input_type -> api.items.story.v1.DeleteStoryRequest
	34,  // 29: api.items.v1.Items.UpdateStory:input_type -> api.items.story.v1.UpdateStoryRequest
	35,  // 30: api.items.v1.Items.ListSameAuthorStoryWithAnchor:input_type -> api.items.story.v1.ListSameAuthorStoryWithAnchorRequest
	36,  // 31: api.items.v1.Items.TopStory:input_type -> api.items.story.v1.TopStoryRequest
	37,  // 32: api.items.v1.Items.ListFollowingCreatorStoryV2:input_type -> api.items.story.v2.ListFollowingCreatorStoryRequestV2
	38,  // 33: api.items.v1.Items.ListCommonConditionTemplates:input_type -> api.items.story.v1.ListCommonStoryConditionTemplatesRequest
	39,  // 34: api.items.v1.Items.ListTurtleSoupStoryConditionTemplates:input_type -> api.items.story.v1.ListTurtleSoupStoryConditionTemplatesRequest
	40,  // 35: api.items.v1.Items.ListExchangeImageStoryConditionTemplates:input_type -> api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest
	41,  // 36: api.items.v1.Items.ListUnmuteStoryConditionTemplates:input_type -> api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest
	42,  // 37: api.items.v1.Items.CreateExchangeImageStory:input_type -> api.items.story.v1.CreateExchangeImageStoryRequest
	43,  // 38: api.items.v1.Items.CreateWhoStoryV2:input_type -> api.items.story.v2.CreateWhoStoryRequestV2
	44,  // 39: api.items.v1.Items.ConsumeWhoStoryV2:input_type -> api.items.story.v2.ConsumeWhoStoryRequestV2
	45,  // 40: api.items.v1.Items.CreateExchangeImageStoryV2:input_type -> api.items.story.v2.CreateExchangeImageStoryRequestV2
	46,  // 41: api.items.v1.Items.CreateTurtleSoupStory:input_type -> api.items.story.v1.CreateTurtleSoupStoryRequest
	47,  // 42: api.items.v1.Items.CreatePinStory:input_type -> api.items.story.v2.CreatePinStoryRequest
	48,  // 43: api.items.v1.Items.ConsumePinStory:input_type -> api.items.story.v2.ConsumePinStoryRequest
	49,  // 44: api.items.v1.Items.AutoGenerateAreaEmoji:input_type -> api.items.story.v2.AutoGenerateAreaEmojiRequest
	50,  // 45: api.items.v1.Items.ManualGenerateAreaEmoji:input_type -> api.items.story.v2.ManualGenerateAreaEmojiRequest
	51,  // 46: api.items.v1.Items.CreateHideStoryV2:input_type -> api.items.story.v2.CreateHideStoryRequestV2
	52,  // 47: api.items.v1.Items.GenerateHideImageMask:input_type -> api.items.story.v2.GenerateHideImageMaskRequest
	53,  // 48: api.items.v1.Items.CollectHideSticker:input_type -> api.items.story.hide_stickers.v1.CollectHideStickerRequest
	54,  // 49: api.items.v1.Items.UnCollectHideSticker:input_type -> api.items.story.hide_stickers.v1.UnCollectHideStickerRequest
	55,  // 50: api.items.v1.Items.TopHideSticker:input_type -> api.items.story.hide_stickers.v1.TopCollectedStickerRequest
	56,  // 51: api.items.v1.Items.UnTopHideSticker:input_type -> api.items.story.hide_stickers.v1.UnTopCollectedStickerRequest
	57,  // 52: api.items.v1.Items.ListMyCollectedHideStickers:input_type -> api.items.story.hide_stickers.v1.ListMyCollectedStickersRequest
	58,  // 53: api.items.v1.Items.CreateTurtleSoupStoryV2:input_type -> api.items.story.v2.CreateTurtleSoupStoryRequestV2
	59,  // 54: api.items.v1.Items.GetRoastedTopics:input_type -> api.items.story.v1.GetRoastedTopicsRequest
	60,  // 55: api.items.v1.Items.CreateRoastedStory:input_type -> api.items.story.v1.CreateRoastedStoryRequest
	61,  // 56: api.items.v1.Items.CreateBasePlayStory:input_type -> api.items.story.v1.CreateBasePlayStoryRequest
	62,  // 57: api.items.v1.Items.CreateUnmuteStory:input_type -> api.items.story.v1.CreateUnmuteStoryRequest
	63,  // 58: api.items.v1.Items.CreateChatProxyStory:input_type -> api.items.story.v2.CreateChatProxyStoryRequestV2
	64,  // 59: api.items.v1.Items.GetChatProxyNextTopic:input_type -> api.items.story.v2.GetChatProxyNextTopicRequestV2
	65,  // 60: api.items.v1.Items.ConsumeChatProxy:input_type -> api.items.story.v2.ConsumeChatProxyRequestV2
	66,  // 61: api.items.v1.Items.CreateUnmuteStoryV2:input_type -> api.items.story.v2.CreateUnmuteStoryRequestV2
	67,  // 62: api.items.v1.Items.CreateNowShotStory:input_type -> api.items.story.v1.CreateNowShotStoryRequest
	68,  // 63: api.items.v1.Items.CreateNowShotStoryV2:input_type -> api.items.story.v2.CreateNowShotStoryRequestV2
	69,  // 64: api.items.v1.Items.CreateCapsuleStory:input_type -> api.items.story.v1.CreateCapsuleStoryRequest
	70,  // 65: api.items.v1.Items.GetStoryDetail:input_type -> api.items.story.v1.GetStoryDetailRequest
	71,  // 66: api.items.v1.Items.ListHomePageStoryV2:input_type -> api.items.story.v2.ListHomePageStoryRequestV2
	72,  // 67: api.items.v1.Items.ListCreatorStory:input_type -> api.items.story.v1.ListCreatorStoryRequest
	73,  // 68: api.items.v1.Items.ListCreatorStoryV2:input_type -> api.items.story.v2.ListCreatorStoryRequestV2
	74,  // 69: api.items.v1.Items.ListUnlockedStory:input_type -> api.items.story.v1.ListUnlockedStoryRequest
	75,  // 70: api.items.v1.Items.ConsumeRoastedTopic:input_type -> api.items.story.v1.ConsumeRoastedTopicRequest
	76,  // 71: api.items.v1.Items.ConsumeHideStory:input_type -> api.items.story.v2.ConsumeHideStoryRequest
	77,  // 72: api.items.v1.Items.ConsumeRoastedStory:input_type -> api.items.story.v1.ConsumeRoastedStoryRequest
	78,  // 73: api.items.v1.Items.ConsumeRoastedStoryV2:input_type -> api.items.story.v2.ConsumeRoastedStoryRequestV2
	79,  // 74: api.items.v1.Items.ConsumeBasePlayStory:input_type -> api.items.story.v1.ConsumeBasePlayStoryRequest
	80,  // 75: api.items.v1.Items.ConsumeExchangeImageStory:input_type -> api.items.story.v1.ConsumeExchangeImageStoryRequest
	81,  // 76: api.items.v1.Items.ConsumeTurtleSoupStory:input_type -> api.items.story.v1.ConsumeTurtleSoupStoryRequest
	82,  // 77: api.items.v1.Items.GetWassupGreetings:input_type -> api.items.story.v2.GetWassupGreetingsRequest
	83,  // 78: api.items.v1.Items.GetWassupNextQuestion:input_type -> api.items.story.v2.GetWassupNextQuestionRequest
	84,  // 79: api.items.v1.Items.CreateWassupStoryV2:input_type -> api.items.story.v2.CreateWassupStoryRequest
	85,  // 80: api.items.v1.Items.ConsumeWassupStoryV2:input_type -> api.items.story.v2.ConsumeWassupStoryRequest
	86,  // 81: api.items.v1.Items.SendMessage:input_type -> api.items.story.v2.SendMessageRequest
	87,  // 82: api.items.v1.Items.ReportHauntShow:input_type -> api.items.story.v2.ReportHauntShowRequest
	88,  // 83: api.items.v1.Items.CreateHauntStoryV2:input_type -> api.items.story.v2.CreateHauntStoryRequest
	89,  // 84: api.items.v1.Items.ConsumeHauntStory:input_type -> api.items.story.v2.ConsumeHauntStoryRequest
	90,  // 85: api.items.v1.Items.SendHauntCaptureVideo:input_type -> api.items.story.v2.SendHauntCaptureVideoRequest
	91,  // 86: api.items.v1.Items.ListHauntRandomAvatars:input_type -> api.items.story.v2.ListHauntRandomAvatarsRequest
	92,  // 87: api.items.v1.Items.ListHauntQuestions:input_type -> api.items.story.v2.ListHauntQuestionsRequest
	93,  // 88: api.items.v1.Items.ListHauntBooAssist:input_type -> api.items.story.v2.ListHauntBooAssistRequest
	94,  // 89: api.items.v1.Items.AddCaptureBooIntoMyAssist:input_type -> api.items.story.v2.AddCaptureBooIntoMyAssistRequest
	95,  // 90: api.items.v1.Items.AddCapturedBooInToCollectedStickers:input_type -> api.items.story.v2.AddCapturedBooInToCollectedStickersRequest
	96,  // 91: api.items.v1.Items.CheckHauntImage:input_type -> api.items.story.v2.ImageCheckRequest
	97,  // 92: api.items.v1.Items.ConsumeUnmuteStory:input_type -> api.items.story.v1.ConsumeUnmuteStoryRequest
	98,  // 93: api.items.v1.Items.ConsumeNowShotStory:input_type -> api.items.story.v1.ConsumeNowShotStoryRequest
	99,  // 94: api.items.v1.Items.ConsumeNowShotStoryV2:input_type -> api.items.story.v2.ConsumeNowShotStoryRequestV2
	100, // 95: api.items.v1.Items.ConsumeCapsuleStory:input_type -> api.items.story.v1.ConsumeCapsuleStoryRequest
	101, // 96: api.items.v1.Items.CopilotCapsuleStory:input_type -> api.items.story.v1.CopilotCapsuleStoryRequest
	102, // 97: api.items.v1.Items.CreateStoryReaction:input_type -> api.items.story.reaction.v1.CreateReactionRequest
	103, // 98: api.items.v1.Items.DeleteStoryReaction:input_type -> api.items.story.reaction.v1.DeleteReactionRequest
	104, // 99: api.items.v1.Items.ListStoryReactionMadeUsers:input_type -> api.items.story.reaction.v1.ListReactionMadeUsersRequest
	1,   // 100: api.items.v1.Items.ReportShareStat:input_type -> api.items.v1.ReportShareStatRequest
	105, // 101: api.items.v1.Items.CreateMoment:input_type -> api.items.portal.v1.CreateMomentRequest
	106, // 102: api.items.v1.Items.GetUserCreatedPortalsInfo:input_type -> api.items.portal.v1.GetUserCreatedPortalsInfoRequest
	107, // 103: api.items.v1.Items.DeleteMoment:input_type -> api.items.portal.v1.DeleteMomentRequest
	108, // 104: api.items.v1.Items.CreateMomentRelation:input_type -> api.items.portal.v1.CreateMomentRelationRequest
	109, // 105: api.items.v1.Items.RemoveMomentRelation:input_type -> api.items.portal.v1.RemoveMomentRelationRequest
	110, // 106: api.items.v1.Items.ListUserCreatedPortalsWithTimeRange:input_type -> api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest
	111, // 107: api.items.v1.Items.ListMyPortals:input_type -> api.items.portal.v1.ListMyPortalsRequest
	112, // 108: api.items.v1.Items.ListCouldAppendMomentStories:input_type -> api.items.portal.v1.ListCouldAppendMomentStoriesRequest
	113, // 109: api.items.v1.Items.ReportRead:input_type -> api.items.portal.v1.ReportReadRequest
	114, // 110: api.items.v1.Items.GetPortal:input_type -> api.items.portal.v1.GetPortalRequest
	115, // 111: api.items.v1.Items.ListMomentViewers:input_type -> api.items.portal.v1.ListMomentViewersRequest
	116, // 112: api.items.v1.Items.ListTrendingPortals:input_type -> api.items.portal.v1.ListTrendingPortalsRequest
	117, // 113: api.items.v1.Items.GetMoment:input_type -> api.items.portal.v1.GetMomentRequest
	118, // 114: api.items.v1.Items.SendMomentInvite:input_type -> api.items.portal.v1.SendMomentInviteRequest
	119, // 115: api.items.v1.Items.MusicSearch:output_type -> api.items.music.search.v1.SearchResponse
	120, // 116: api.items.v1.Items.CreateComment:output_type -> api.items.comments.v1.CreateCommentResponse
	121, // 117: api.items.v1.Items.ListComments:output_type -> api.items.comments.v1.ListCommentsResponse
	122, // 118: api.items.v1.Items.ListCommentReplies:output_type -> api.items.comments.v1.ListCommentRepliesResponse
	123, // 119: api.items.v1.Items.DeleteCommentOrReply:output_type -> google.protobuf.Empty
	123, // 120: api.items.v1.Items.LikeCommentOrReply:output_type -> google.protobuf.Empty
	123, // 121: api.items.v1.Items.UnlikeCommentOrReply:output_type -> google.protobuf.Empty
	10,  // 122: api.items.v1.Items.Asr:output_type -> api.items.v1.AsrResponse
	8,   // 123: api.items.v1.Items.HomePageTimeline:output_type -> api.items.v1.HomePageTimelineResponse
	6,   // 124: api.items.v1.Items.CreateItem:output_type -> api.items.v1.CreateItemResponse
	4,   // 125: api.items.v1.Items.BatchGetItemSummaries:output_type -> api.items.v1.BatchGetItemSummariesResponse
	124, // 126: api.items.v1.Items.CreateItemReaction:output_type -> api.items.reaction.v1.CreateItemReactionResponse
	125, // 127: api.items.v1.Items.RemoveItemReaction:output_type -> api.items.reaction.v1.RemoveItemReactionResponse
	126, // 128: api.items.v1.Items.ListUserReactedItems:output_type -> api.items.reaction.v1.ListUserReactedItemsResponse
	127, // 129: api.items.v1.Items.ListUsersByConsumptionStatus:output_type -> api.items.story.activity.v1.ListUsersByConsumptionStatusResponse
	128, // 130: api.items.v1.Items.ListActivities:output_type -> api.items.story.activity.v1.ListActivitiesResponse
	129, // 131: api.items.v1.Items.GetActivityUnreadCount:output_type -> api.items.story.activity.v1.GetActivityUnreadCountResponse
	123, // 132: api.items.v1.Items.ReportActivityRead:output_type -> google.protobuf.Empty
	123, // 133: api.items.v1.Items.DeleteStory:output_type -> google.protobuf.Empty
	130, // 134: api.items.v1.Items.UpdateStory:output_type -> api.items.story.v1.UpdateStoryResponse
	131, // 135: api.items.v1.Items.ListSameAuthorStoryWithAnchor:output_type -> api.items.story.v1.ListSameAuthorStoryWithAnchorResponse
	123, // 136: api.items.v1.Items.TopStory:output_type -> google.protobuf.Empty
	132, // 137: api.items.v1.Items.ListFollowingCreatorStoryV2:output_type -> api.items.story.v2.ListFollowingCreatorStoryResponseV2
	133, // 138: api.items.v1.Items.ListCommonConditionTemplates:output_type -> api.items.story.v1.ListCommonStoryConditionTemplatesResponse
	134, // 139: api.items.v1.Items.ListTurtleSoupStoryConditionTemplates:output_type -> api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse
	135, // 140: api.items.v1.Items.ListExchangeImageStoryConditionTemplates:output_type -> api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse
	136, // 141: api.items.v1.Items.ListUnmuteStoryConditionTemplates:output_type -> api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse
	137, // 142: api.items.v1.Items.CreateExchangeImageStory:output_type -> api.items.story.v1.CreateExchangeImageStoryResponse
	138, // 143: api.items.v1.Items.CreateWhoStoryV2:output_type -> api.items.story.v2.CreateWhoStoryResponseV2
	139, // 144: api.items.v1.Items.ConsumeWhoStoryV2:output_type -> api.items.story.v2.ConsumeWhoStoryResponseV2
	140, // 145: api.items.v1.Items.CreateExchangeImageStoryV2:output_type -> api.items.story.v2.CreateExchangeImageStoryResponseV2
	141, // 146: api.items.v1.Items.CreateTurtleSoupStory:output_type -> api.items.story.v1.CreateTurtleSoupStoryResponse
	142, // 147: api.items.v1.Items.CreatePinStory:output_type -> api.items.story.v2.CreatePinStoryResponse
	143, // 148: api.items.v1.Items.ConsumePinStory:output_type -> api.items.story.v2.ConsumePinStoryResponse
	144, // 149: api.items.v1.Items.AutoGenerateAreaEmoji:output_type -> api.items.story.v2.AutoGenerateAreaEmojiResponse
	145, // 150: api.items.v1.Items.ManualGenerateAreaEmoji:output_type -> api.items.story.v2.ManualGenerateAreaEmojiResponse
	146, // 151: api.items.v1.Items.CreateHideStoryV2:output_type -> api.items.story.v2.CreateHideStoryResponseV2
	147, // 152: api.items.v1.Items.GenerateHideImageMask:output_type -> api.items.story.v2.GenerateHideImageMaskResponse
	123, // 153: api.items.v1.Items.CollectHideSticker:output_type -> google.protobuf.Empty
	123, // 154: api.items.v1.Items.UnCollectHideSticker:output_type -> google.protobuf.Empty
	123, // 155: api.items.v1.Items.TopHideSticker:output_type -> google.protobuf.Empty
	123, // 156: api.items.v1.Items.UnTopHideSticker:output_type -> google.protobuf.Empty
	148, // 157: api.items.v1.Items.ListMyCollectedHideStickers:output_type -> api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse
	149, // 158: api.items.v1.Items.CreateTurtleSoupStoryV2:output_type -> api.items.story.v2.CreateTurtleSoupStoryResponseV2
	150, // 159: api.items.v1.Items.GetRoastedTopics:output_type -> api.items.story.v1.GetRoastedTopicsResponse
	151, // 160: api.items.v1.Items.CreateRoastedStory:output_type -> api.items.story.v1.CreateRoastedStoryResponse
	152, // 161: api.items.v1.Items.CreateBasePlayStory:output_type -> api.items.story.v1.CreateBasePlayStoryResponse
	153, // 162: api.items.v1.Items.CreateUnmuteStory:output_type -> api.items.story.v1.CreateUnmuteStoryResponse
	154, // 163: api.items.v1.Items.CreateChatProxyStory:output_type -> api.items.story.v2.CreateChatProxyStoryResponseV2
	155, // 164: api.items.v1.Items.GetChatProxyNextTopic:output_type -> api.items.story.v2.GetChatProxyNextTopicResponseV2
	156, // 165: api.items.v1.Items.ConsumeChatProxy:output_type -> api.items.story.v2.ConsumeChatProxyResponseV2
	157, // 166: api.items.v1.Items.CreateUnmuteStoryV2:output_type -> api.items.story.v2.CreateUnmuteStoryResponseV2
	158, // 167: api.items.v1.Items.CreateNowShotStory:output_type -> api.items.story.v1.CreateNowShotStoryResponse
	159, // 168: api.items.v1.Items.CreateNowShotStoryV2:output_type -> api.items.story.v2.StoryDetailResponseV2
	160, // 169: api.items.v1.Items.CreateCapsuleStory:output_type -> api.items.story.v1.CreateCapsuleStoryResponse
	161, // 170: api.items.v1.Items.GetStoryDetail:output_type -> api.items.story.v1.GetStoryDetailResponse
	162, // 171: api.items.v1.Items.ListHomePageStoryV2:output_type -> api.items.story.v2.ListHomePageStoryResponseV2
	163, // 172: api.items.v1.Items.ListCreatorStory:output_type -> api.items.story.v1.ListCreatorStoryResponse
	164, // 173: api.items.v1.Items.ListCreatorStoryV2:output_type -> api.items.story.v2.ListCreatorStoryResponseV2
	165, // 174: api.items.v1.Items.ListUnlockedStory:output_type -> api.items.story.v1.ListUnlockedStoryResponse
	166, // 175: api.items.v1.Items.ConsumeRoastedTopic:output_type -> api.items.story.v1.ConsumeRoastedTopicResponse
	167, // 176: api.items.v1.Items.ConsumeHideStory:output_type -> api.items.story.v2.ConsumeHideStoryResponse
	168, // 177: api.items.v1.Items.ConsumeRoastedStory:output_type -> api.items.story.v1.ConsumeRoastedStoryResponse
	169, // 178: api.items.v1.Items.ConsumeRoastedStoryV2:output_type -> api.items.story.v2.ConsumeRoastedStoryResponseV2
	170, // 179: api.items.v1.Items.ConsumeBasePlayStory:output_type -> api.items.story.v1.ConsumeBasePlayStoryResponse
	171, // 180: api.items.v1.Items.ConsumeExchangeImageStory:output_type -> api.items.story.v1.ConsumeExchangeImageStoryResponse
	172, // 181: api.items.v1.Items.ConsumeTurtleSoupStory:output_type -> api.items.story.v1.ConsumeTurtleSoupStoryResponse
	173, // 182: api.items.v1.Items.GetWassupGreetings:output_type -> api.items.story.v2.GetWassupGreetingsResponse
	174, // 183: api.items.v1.Items.GetWassupNextQuestion:output_type -> api.items.story.v2.GetWassupNextQuestionResponse
	175, // 184: api.items.v1.Items.CreateWassupStoryV2:output_type -> api.items.story.v2.CreateWassupStoryResponse
	176, // 185: api.items.v1.Items.ConsumeWassupStoryV2:output_type -> api.items.story.v2.ConsumeWassupStoryResponse
	177, // 186: api.items.v1.Items.SendMessage:output_type -> api.items.story.v2.SendMessageResponse
	178, // 187: api.items.v1.Items.ReportHauntShow:output_type -> api.items.story.v2.ReportHauntShowResponse
	179, // 188: api.items.v1.Items.CreateHauntStoryV2:output_type -> api.items.story.v2.CreateHauntStoryResponse
	180, // 189: api.items.v1.Items.ConsumeHauntStory:output_type -> api.items.story.v2.ConsumeHauntStoryResponse
	123, // 190: api.items.v1.Items.SendHauntCaptureVideo:output_type -> google.protobuf.Empty
	181, // 191: api.items.v1.Items.ListHauntRandomAvatars:output_type -> api.items.story.v2.ListHauntRandomAvatarsResponse
	182, // 192: api.items.v1.Items.ListHauntQuestions:output_type -> api.items.story.v2.ListHauntQuestionsResponse
	183, // 193: api.items.v1.Items.ListHauntBooAssist:output_type -> api.items.story.v2.ListHauntBooAssistResponse
	123, // 194: api.items.v1.Items.AddCaptureBooIntoMyAssist:output_type -> google.protobuf.Empty
	184, // 195: api.items.v1.Items.AddCapturedBooInToCollectedStickers:output_type -> api.items.story.v2.AddCapturedBooInToCollectedStickersResponse
	185, // 196: api.items.v1.Items.CheckHauntImage:output_type -> api.items.story.v2.ImageCheckResponse
	186, // 197: api.items.v1.Items.ConsumeUnmuteStory:output_type -> api.items.story.v1.ConsumeUnmuteStoryResponse
	187, // 198: api.items.v1.Items.ConsumeNowShotStory:output_type -> api.items.story.v1.ConsumeNowShotStoryResponse
	159, // 199: api.items.v1.Items.ConsumeNowShotStoryV2:output_type -> api.items.story.v2.StoryDetailResponseV2
	188, // 200: api.items.v1.Items.ConsumeCapsuleStory:output_type -> api.items.story.v1.ConsumeCapsuleStoryResponse
	189, // 201: api.items.v1.Items.CopilotCapsuleStory:output_type -> api.items.story.v1.CopilotCapsuleStoryResponse
	190, // 202: api.items.v1.Items.CreateStoryReaction:output_type -> api.items.story.reaction.v1.CreateReactionResponse
	191, // 203: api.items.v1.Items.DeleteStoryReaction:output_type -> api.items.story.reaction.v1.DeleteReactionResponse
	192, // 204: api.items.v1.Items.ListStoryReactionMadeUsers:output_type -> api.items.story.reaction.v1.ListReactionMadeUsersResponse
	2,   // 205: api.items.v1.Items.ReportShareStat:output_type -> api.items.v1.ReportShareStatResponse
	193, // 206: api.items.v1.Items.CreateMoment:output_type -> api.items.portal.v1.CreateMomentResponse
	194, // 207: api.items.v1.Items.GetUserCreatedPortalsInfo:output_type -> api.items.portal.v1.GetUserCreatedPortalsInfoResponse
	123, // 208: api.items.v1.Items.DeleteMoment:output_type -> google.protobuf.Empty
	195, // 209: api.items.v1.Items.CreateMomentRelation:output_type -> api.items.portal.v1.CreateMomentRelationResponse
	196, // 210: api.items.v1.Items.RemoveMomentRelation:output_type -> api.items.portal.v1.RemoveMomentRelationResponse
	197, // 211: api.items.v1.Items.ListUserCreatedPortalsWithTimeRange:output_type -> api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse
	198, // 212: api.items.v1.Items.ListMyPortals:output_type -> api.items.portal.v1.ListMyPortalsResponse
	199, // 213: api.items.v1.Items.ListCouldAppendMomentStories:output_type -> api.items.portal.v1.ListCouldAppendMomentStoriesResponse
	123, // 214: api.items.v1.Items.ReportRead:output_type -> google.protobuf.Empty
	200, // 215: api.items.v1.Items.GetPortal:output_type -> api.items.portal.v1.GetPortalResponse
	201, // 216: api.items.v1.Items.ListMomentViewers:output_type -> api.items.portal.v1.ListMomentViewersResponse
	202, // 217: api.items.v1.Items.ListTrendingPortals:output_type -> api.items.portal.v1.ListTrendingPortalsResponse
	203, // 218: api.items.v1.Items.GetMoment:output_type -> api.items.portal.v1.GetMomentResponse
	204, // 219: api.items.v1.Items.SendMomentInvite:output_type -> api.items.portal.v1.SendMomentInviteResponse
	115, // [115:220] is the sub-list for method output_type
	10,  // [10:115] is the sub-list for method input_type
	10,  // [10:10] is the sub-list for extension type_name
	10,  // [10:10] is the sub-list for extension extendee
	0,   // [0:10] is the sub-list for field type_name
}

func init() { file_api_items_v1_items_proto_init() }
func file_api_items_v1_items_proto_init() {
	if File_api_items_v1_items_proto != nil {
		return
	}
	file_api_items_v1_items_proto_msgTypes[4].OneofWrappers = []any{}
	file_api_items_v1_items_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_v1_items_proto_rawDesc), len(file_api_items_v1_items_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_items_v1_items_proto_goTypes,
		DependencyIndexes: file_api_items_v1_items_proto_depIdxs,
		EnumInfos:         file_api_items_v1_items_proto_enumTypes,
		MessageInfos:      file_api_items_v1_items_proto_msgTypes,
	}.Build()
	File_api_items_v1_items_proto = out.File
	file_api_items_v1_items_proto_goTypes = nil
	file_api_items_v1_items_proto_depIdxs = nil
}
