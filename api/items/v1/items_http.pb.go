// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.29.3
// source: api/items/v1/items.proto

package v1

import (
	v11 "boson/api/items/comments/v1"
	v1 "boson/api/items/music/search/v1"
	v17 "boson/api/items/portal/v1"
	v12 "boson/api/items/reaction/v1"
	v13 "boson/api/items/story/activity/v1"
	v15 "boson/api/items/story/hide_stickers/v1"
	v16 "boson/api/items/story/reaction/v1"
	v14 "boson/api/items/story/v1"
	v2 "boson/api/items/story/v2"
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationItemsAddCaptureBooIntoMyAssist = "/api.items.v1.Items/AddCaptureBooIntoMyAssist"
const OperationItemsAddCapturedBooInToCollectedStickers = "/api.items.v1.Items/AddCapturedBooInToCollectedStickers"
const OperationItemsAsr = "/api.items.v1.Items/Asr"
const OperationItemsAutoGenerateAreaEmoji = "/api.items.v1.Items/AutoGenerateAreaEmoji"
const OperationItemsBatchGetItemSummaries = "/api.items.v1.Items/BatchGetItemSummaries"
const OperationItemsCheckHauntImage = "/api.items.v1.Items/CheckHauntImage"
const OperationItemsCollectHideSticker = "/api.items.v1.Items/CollectHideSticker"
const OperationItemsConsumeBasePlayStory = "/api.items.v1.Items/ConsumeBasePlayStory"
const OperationItemsConsumeCapsuleStory = "/api.items.v1.Items/ConsumeCapsuleStory"
const OperationItemsConsumeChatProxy = "/api.items.v1.Items/ConsumeChatProxy"
const OperationItemsConsumeExchangeImageStory = "/api.items.v1.Items/ConsumeExchangeImageStory"
const OperationItemsConsumeHauntStory = "/api.items.v1.Items/ConsumeHauntStory"
const OperationItemsConsumeHideStory = "/api.items.v1.Items/ConsumeHideStory"
const OperationItemsConsumeNowShotStory = "/api.items.v1.Items/ConsumeNowShotStory"
const OperationItemsConsumeNowShotStoryV2 = "/api.items.v1.Items/ConsumeNowShotStoryV2"
const OperationItemsConsumePinStory = "/api.items.v1.Items/ConsumePinStory"
const OperationItemsConsumeRoastedStory = "/api.items.v1.Items/ConsumeRoastedStory"
const OperationItemsConsumeRoastedStoryV2 = "/api.items.v1.Items/ConsumeRoastedStoryV2"
const OperationItemsConsumeRoastedTopic = "/api.items.v1.Items/ConsumeRoastedTopic"
const OperationItemsConsumeTurtleSoupStory = "/api.items.v1.Items/ConsumeTurtleSoupStory"
const OperationItemsConsumeUnmuteStory = "/api.items.v1.Items/ConsumeUnmuteStory"
const OperationItemsConsumeWassupStoryV2 = "/api.items.v1.Items/ConsumeWassupStoryV2"
const OperationItemsConsumeWhoStoryV2 = "/api.items.v1.Items/ConsumeWhoStoryV2"
const OperationItemsCopilotCapsuleStory = "/api.items.v1.Items/CopilotCapsuleStory"
const OperationItemsCreateBasePlayStory = "/api.items.v1.Items/CreateBasePlayStory"
const OperationItemsCreateCapsuleStory = "/api.items.v1.Items/CreateCapsuleStory"
const OperationItemsCreateChatProxyStory = "/api.items.v1.Items/CreateChatProxyStory"
const OperationItemsCreateComment = "/api.items.v1.Items/CreateComment"
const OperationItemsCreateExchangeImageStory = "/api.items.v1.Items/CreateExchangeImageStory"
const OperationItemsCreateExchangeImageStoryV2 = "/api.items.v1.Items/CreateExchangeImageStoryV2"
const OperationItemsCreateHauntStoryV2 = "/api.items.v1.Items/CreateHauntStoryV2"
const OperationItemsCreateHideStoryV2 = "/api.items.v1.Items/CreateHideStoryV2"
const OperationItemsCreateItem = "/api.items.v1.Items/CreateItem"
const OperationItemsCreateItemReaction = "/api.items.v1.Items/CreateItemReaction"
const OperationItemsCreateMoment = "/api.items.v1.Items/CreateMoment"
const OperationItemsCreateMomentRelation = "/api.items.v1.Items/CreateMomentRelation"
const OperationItemsCreateNowShotStory = "/api.items.v1.Items/CreateNowShotStory"
const OperationItemsCreateNowShotStoryV2 = "/api.items.v1.Items/CreateNowShotStoryV2"
const OperationItemsCreatePinStory = "/api.items.v1.Items/CreatePinStory"
const OperationItemsCreateRoastedStory = "/api.items.v1.Items/CreateRoastedStory"
const OperationItemsCreateStoryReaction = "/api.items.v1.Items/CreateStoryReaction"
const OperationItemsCreateTurtleSoupStory = "/api.items.v1.Items/CreateTurtleSoupStory"
const OperationItemsCreateTurtleSoupStoryV2 = "/api.items.v1.Items/CreateTurtleSoupStoryV2"
const OperationItemsCreateUnmuteStory = "/api.items.v1.Items/CreateUnmuteStory"
const OperationItemsCreateUnmuteStoryV2 = "/api.items.v1.Items/CreateUnmuteStoryV2"
const OperationItemsCreateWassupStoryV2 = "/api.items.v1.Items/CreateWassupStoryV2"
const OperationItemsCreateWhoStoryV2 = "/api.items.v1.Items/CreateWhoStoryV2"
const OperationItemsDeleteCommentOrReply = "/api.items.v1.Items/DeleteCommentOrReply"
const OperationItemsDeleteMoment = "/api.items.v1.Items/DeleteMoment"
const OperationItemsDeleteStory = "/api.items.v1.Items/DeleteStory"
const OperationItemsDeleteStoryReaction = "/api.items.v1.Items/DeleteStoryReaction"
const OperationItemsGenerateHideImageMask = "/api.items.v1.Items/GenerateHideImageMask"
const OperationItemsGetActivityUnreadCount = "/api.items.v1.Items/GetActivityUnreadCount"
const OperationItemsGetChatProxyNextTopic = "/api.items.v1.Items/GetChatProxyNextTopic"
const OperationItemsGetMoment = "/api.items.v1.Items/GetMoment"
const OperationItemsGetPortal = "/api.items.v1.Items/GetPortal"
const OperationItemsGetRoastedTopics = "/api.items.v1.Items/GetRoastedTopics"
const OperationItemsGetStoryDetail = "/api.items.v1.Items/GetStoryDetail"
const OperationItemsGetUserCreatedPortalsInfo = "/api.items.v1.Items/GetUserCreatedPortalsInfo"
const OperationItemsGetWassupGreetings = "/api.items.v1.Items/GetWassupGreetings"
const OperationItemsGetWassupNextQuestion = "/api.items.v1.Items/GetWassupNextQuestion"
const OperationItemsHomePageTimeline = "/api.items.v1.Items/HomePageTimeline"
const OperationItemsLikeCommentOrReply = "/api.items.v1.Items/LikeCommentOrReply"
const OperationItemsListActivities = "/api.items.v1.Items/ListActivities"
const OperationItemsListCommentReplies = "/api.items.v1.Items/ListCommentReplies"
const OperationItemsListComments = "/api.items.v1.Items/ListComments"
const OperationItemsListCommonConditionTemplates = "/api.items.v1.Items/ListCommonConditionTemplates"
const OperationItemsListCouldAppendMomentStories = "/api.items.v1.Items/ListCouldAppendMomentStories"
const OperationItemsListCreatorStory = "/api.items.v1.Items/ListCreatorStory"
const OperationItemsListCreatorStoryV2 = "/api.items.v1.Items/ListCreatorStoryV2"
const OperationItemsListExchangeImageStoryConditionTemplates = "/api.items.v1.Items/ListExchangeImageStoryConditionTemplates"
const OperationItemsListFollowingCreatorStoryV2 = "/api.items.v1.Items/ListFollowingCreatorStoryV2"
const OperationItemsListHauntBooAssist = "/api.items.v1.Items/ListHauntBooAssist"
const OperationItemsListHauntQuestions = "/api.items.v1.Items/ListHauntQuestions"
const OperationItemsListHauntRandomAvatars = "/api.items.v1.Items/ListHauntRandomAvatars"
const OperationItemsListHomePageStoryV2 = "/api.items.v1.Items/ListHomePageStoryV2"
const OperationItemsListMomentViewers = "/api.items.v1.Items/ListMomentViewers"
const OperationItemsListMyCollectedHideStickers = "/api.items.v1.Items/ListMyCollectedHideStickers"
const OperationItemsListMyPortals = "/api.items.v1.Items/ListMyPortals"
const OperationItemsListSameAuthorStoryWithAnchor = "/api.items.v1.Items/ListSameAuthorStoryWithAnchor"
const OperationItemsListStoryReactionMadeUsers = "/api.items.v1.Items/ListStoryReactionMadeUsers"
const OperationItemsListTrendingPortals = "/api.items.v1.Items/ListTrendingPortals"
const OperationItemsListTurtleSoupStoryConditionTemplates = "/api.items.v1.Items/ListTurtleSoupStoryConditionTemplates"
const OperationItemsListUnlockedStory = "/api.items.v1.Items/ListUnlockedStory"
const OperationItemsListUnmuteStoryConditionTemplates = "/api.items.v1.Items/ListUnmuteStoryConditionTemplates"
const OperationItemsListUserCreatedPortalsWithTimeRange = "/api.items.v1.Items/ListUserCreatedPortalsWithTimeRange"
const OperationItemsListUserReactedItems = "/api.items.v1.Items/ListUserReactedItems"
const OperationItemsListUsersByConsumptionStatus = "/api.items.v1.Items/ListUsersByConsumptionStatus"
const OperationItemsManualGenerateAreaEmoji = "/api.items.v1.Items/ManualGenerateAreaEmoji"
const OperationItemsMusicSearch = "/api.items.v1.Items/MusicSearch"
const OperationItemsRemoveItemReaction = "/api.items.v1.Items/RemoveItemReaction"
const OperationItemsRemoveMomentRelation = "/api.items.v1.Items/RemoveMomentRelation"
const OperationItemsReportActivityRead = "/api.items.v1.Items/ReportActivityRead"
const OperationItemsReportHauntShow = "/api.items.v1.Items/ReportHauntShow"
const OperationItemsReportRead = "/api.items.v1.Items/ReportRead"
const OperationItemsReportShareStat = "/api.items.v1.Items/ReportShareStat"
const OperationItemsSendHauntCaptureVideo = "/api.items.v1.Items/SendHauntCaptureVideo"
const OperationItemsSendMessage = "/api.items.v1.Items/SendMessage"
const OperationItemsSendMomentInvite = "/api.items.v1.Items/SendMomentInvite"
const OperationItemsTopHideSticker = "/api.items.v1.Items/TopHideSticker"
const OperationItemsTopStory = "/api.items.v1.Items/TopStory"
const OperationItemsUnCollectHideSticker = "/api.items.v1.Items/UnCollectHideSticker"
const OperationItemsUnTopHideSticker = "/api.items.v1.Items/UnTopHideSticker"
const OperationItemsUnlikeCommentOrReply = "/api.items.v1.Items/UnlikeCommentOrReply"
const OperationItemsUpdateStory = "/api.items.v1.Items/UpdateStory"

type ItemsHTTPServer interface {
	AddCaptureBooIntoMyAssist(context.Context, *v2.AddCaptureBooIntoMyAssistRequest) (*emptypb.Empty, error)
	AddCapturedBooInToCollectedStickers(context.Context, *v2.AddCapturedBooInToCollectedStickersRequest) (*v2.AddCapturedBooInToCollectedStickersResponse, error)
	// Asr 音频转文字
	Asr(context.Context, *AsrRequest) (*AsrResponse, error)
	// AutoGenerateAreaEmoji 自动生成 area emoji
	AutoGenerateAreaEmoji(context.Context, *v2.AutoGenerateAreaEmojiRequest) (*v2.AutoGenerateAreaEmojiResponse, error)
	// BatchGetItemSummaries 批量获取item摘要
	BatchGetItemSummaries(context.Context, *BatchGetItemSummariesRequest) (*BatchGetItemSummariesResponse, error)
	// CheckHauntImage haunt 图片检测
	CheckHauntImage(context.Context, *v2.ImageCheckRequest) (*v2.ImageCheckResponse, error)
	// CollectHideSticker 收藏 hide sticker
	CollectHideSticker(context.Context, *v15.CollectHideStickerRequest) (*emptypb.Empty, error)
	// ConsumeBasePlayStory 消费 baseplay story
	ConsumeBasePlayStory(context.Context, *v14.ConsumeBasePlayStoryRequest) (*v14.ConsumeBasePlayStoryResponse, error)
	// ConsumeCapsuleStory 消费 Capsule story
	ConsumeCapsuleStory(context.Context, *v14.ConsumeCapsuleStoryRequest) (*v14.ConsumeCapsuleStoryResponse, error)
	// ConsumeChatProxy 消费 chatproxy
	ConsumeChatProxy(context.Context, *v2.ConsumeChatProxyRequestV2) (*v2.ConsumeChatProxyResponseV2, error)
	// ConsumeExchangeImageStory 消费换图 story
	ConsumeExchangeImageStory(context.Context, *v14.ConsumeExchangeImageStoryRequest) (*v14.ConsumeExchangeImageStoryResponse, error)
	// ConsumeHauntStory 消费 haunt story
	ConsumeHauntStory(context.Context, *v2.ConsumeHauntStoryRequest) (*v2.ConsumeHauntStoryResponse, error)
	// ConsumeHideStory 消费 hide story
	ConsumeHideStory(context.Context, *v2.ConsumeHideStoryRequest) (*v2.ConsumeHideStoryResponse, error)
	// ConsumeNowShotStory 消费 NowShot story
	ConsumeNowShotStory(context.Context, *v14.ConsumeNowShotStoryRequest) (*v14.ConsumeNowShotStoryResponse, error)
	// ConsumeNowShotStoryV2 消费 NowShot story v2
	ConsumeNowShotStoryV2(context.Context, *v2.ConsumeNowShotStoryRequestV2) (*v2.StoryDetailResponseV2, error)
	// ConsumePinStory 消费 pin story
	ConsumePinStory(context.Context, *v2.ConsumePinStoryRequest) (*v2.ConsumePinStoryResponse, error)
	// ConsumeRoastedStory 消费 roasted story
	ConsumeRoastedStory(context.Context, *v14.ConsumeRoastedStoryRequest) (*v14.ConsumeRoastedStoryResponse, error)
	// ConsumeRoastedStoryV2 消费 roasted story v2
	ConsumeRoastedStoryV2(context.Context, *v2.ConsumeRoastedStoryRequestV2) (*v2.ConsumeRoastedStoryResponseV2, error)
	// ConsumeRoastedTopic 消费 roasted topic
	ConsumeRoastedTopic(context.Context, *v14.ConsumeRoastedTopicRequest) (*v14.ConsumeRoastedTopicResponse, error)
	// ConsumeTurtleSoupStory 消费海龟汤 story
	ConsumeTurtleSoupStory(context.Context, *v14.ConsumeTurtleSoupStoryRequest) (*v14.ConsumeTurtleSoupStoryResponse, error)
	// ConsumeUnmuteStory 消费Unmute story
	ConsumeUnmuteStory(context.Context, *v14.ConsumeUnmuteStoryRequest) (*v14.ConsumeUnmuteStoryResponse, error)
	// ConsumeWassupStoryV2 消费 wassup v2
	ConsumeWassupStoryV2(context.Context, *v2.ConsumeWassupStoryRequest) (*v2.ConsumeWassupStoryResponse, error)
	// ConsumeWhoStoryV2 消费 who story
	ConsumeWhoStoryV2(context.Context, *v2.ConsumeWhoStoryRequestV2) (*v2.ConsumeWhoStoryResponseV2, error)
	// CopilotCapsuleStory Copilot Capsule story 创作
	CopilotCapsuleStory(context.Context, *v14.CopilotCapsuleStoryRequest) (*v14.CopilotCapsuleStoryResponse, error)
	// CreateBasePlayStory 创建baseplay的 story
	CreateBasePlayStory(context.Context, *v14.CreateBasePlayStoryRequest) (*v14.CreateBasePlayStoryResponse, error)
	// CreateCapsuleStory 创建 Capsule story
	CreateCapsuleStory(context.Context, *v14.CreateCapsuleStoryRequest) (*v14.CreateCapsuleStoryResponse, error)
	// CreateChatProxyStory 创建 chatproxy story
	CreateChatProxyStory(context.Context, *v2.CreateChatProxyStoryRequestV2) (*v2.CreateChatProxyStoryResponseV2, error)
	// CreateComment comments 相关 ...
	// 创建评论
	CreateComment(context.Context, *v11.CreateCommentRequest) (*v11.CreateCommentResponse, error)
	// CreateExchangeImageStory 创建换图 story
	CreateExchangeImageStory(context.Context, *v14.CreateExchangeImageStoryRequest) (*v14.CreateExchangeImageStoryResponse, error)
	// CreateExchangeImageStoryV2 创建换图 story
	CreateExchangeImageStoryV2(context.Context, *v2.CreateExchangeImageStoryRequestV2) (*v2.CreateExchangeImageStoryResponseV2, error)
	// CreateHauntStoryV2 创建 haunt story
	CreateHauntStoryV2(context.Context, *v2.CreateHauntStoryRequest) (*v2.CreateHauntStoryResponse, error)
	// CreateHideStoryV2 创建 hide story
	CreateHideStoryV2(context.Context, *v2.CreateHideStoryRequestV2) (*v2.CreateHideStoryResponseV2, error)
	// CreateItem 创建item，目前会直接发布而不是进入草稿态
	CreateItem(context.Context, *CreateItemRequest) (*CreateItemResponse, error)
	// CreateItemReaction 创建 item reaction
	CreateItemReaction(context.Context, *v12.CreateItemReactionRequest) (*v12.CreateItemReactionResponse, error)
	// CreateMoment ********** portal **********
	CreateMoment(context.Context, *v17.CreateMomentRequest) (*v17.CreateMomentResponse, error)
	CreateMomentRelation(context.Context, *v17.CreateMomentRelationRequest) (*v17.CreateMomentRelationResponse, error)
	// CreateNowShotStory 创建 NowShot story
	CreateNowShotStory(context.Context, *v14.CreateNowShotStoryRequest) (*v14.CreateNowShotStoryResponse, error)
	// CreateNowShotStoryV2 创建 NowShot V2
	CreateNowShotStoryV2(context.Context, *v2.CreateNowShotStoryRequestV2) (*v2.StoryDetailResponseV2, error)
	// CreatePinStory 创建 pin story
	CreatePinStory(context.Context, *v2.CreatePinStoryRequest) (*v2.CreatePinStoryResponse, error)
	// CreateRoastedStory 创建 roasted story
	CreateRoastedStory(context.Context, *v14.CreateRoastedStoryRequest) (*v14.CreateRoastedStoryResponse, error)
	// CreateStoryReaction story reactions ************
	CreateStoryReaction(context.Context, *v16.CreateReactionRequest) (*v16.CreateReactionResponse, error)
	// CreateTurtleSoupStory 创建海龟汤 story
	CreateTurtleSoupStory(context.Context, *v14.CreateTurtleSoupStoryRequest) (*v14.CreateTurtleSoupStoryResponse, error)
	// CreateTurtleSoupStoryV2 创建海龟汤 story v2
	CreateTurtleSoupStoryV2(context.Context, *v2.CreateTurtleSoupStoryRequestV2) (*v2.CreateTurtleSoupStoryResponseV2, error)
	// CreateUnmuteStory 创建unmute story
	CreateUnmuteStory(context.Context, *v14.CreateUnmuteStoryRequest) (*v14.CreateUnmuteStoryResponse, error)
	// CreateUnmuteStoryV2 创建unmute story
	CreateUnmuteStoryV2(context.Context, *v2.CreateUnmuteStoryRequestV2) (*v2.CreateUnmuteStoryResponseV2, error)
	// CreateWassupStoryV2 创建 wassup v2
	CreateWassupStoryV2(context.Context, *v2.CreateWassupStoryRequest) (*v2.CreateWassupStoryResponse, error)
	// CreateWhoStoryV2 创建 who story
	CreateWhoStoryV2(context.Context, *v2.CreateWhoStoryRequestV2) (*v2.CreateWhoStoryResponseV2, error)
	// DeleteCommentOrReply 删除评论
	DeleteCommentOrReply(context.Context, *v11.DeleteCommentOrReplyRequest) (*emptypb.Empty, error)
	DeleteMoment(context.Context, *v17.DeleteMomentRequest) (*emptypb.Empty, error)
	// DeleteStory ************ story 相关 ************
	// 删除 story
	DeleteStory(context.Context, *v14.DeleteStoryRequest) (*emptypb.Empty, error)
	DeleteStoryReaction(context.Context, *v16.DeleteReactionRequest) (*v16.DeleteReactionResponse, error)
	// GenerateHideImageMask 生成 hide image mask
	GenerateHideImageMask(context.Context, *v2.GenerateHideImageMaskRequest) (*v2.GenerateHideImageMaskResponse, error)
	GetActivityUnreadCount(context.Context, *v13.GetActivityUnreadCountRequest) (*v13.GetActivityUnreadCountResponse, error)
	// GetChatProxyNextTopic 获取 chatproxy 的下一个 topic
	GetChatProxyNextTopic(context.Context, *v2.GetChatProxyNextTopicRequestV2) (*v2.GetChatProxyNextTopicResponseV2, error)
	// GetMoment 通过 moment_id 获取 moment 详情
	GetMoment(context.Context, *v17.GetMomentRequest) (*v17.GetMomentResponse, error)
	GetPortal(context.Context, *v17.GetPortalRequest) (*v17.GetPortalResponse, error)
	// GetRoastedTopics 获取 roasted 的 topics
	GetRoastedTopics(context.Context, *v14.GetRoastedTopicsRequest) (*v14.GetRoastedTopicsResponse, error)
	// GetStoryDetail 获取 story 详情
	GetStoryDetail(context.Context, *v14.GetStoryDetailRequest) (*v14.GetStoryDetailResponse, error)
	GetUserCreatedPortalsInfo(context.Context, *v17.GetUserCreatedPortalsInfoRequest) (*v17.GetUserCreatedPortalsInfoResponse, error)
	// GetWassupGreetings wassup v2
	// 获取 wassup 的 greetings
	GetWassupGreetings(context.Context, *v2.GetWassupGreetingsRequest) (*v2.GetWassupGreetingsResponse, error)
	// GetWassupNextQuestion 获取 wassup 的 next question
	GetWassupNextQuestion(context.Context, *v2.GetWassupNextQuestionRequest) (*v2.GetWassupNextQuestionResponse, error)
	// HomePageTimeline 首页时间轴
	HomePageTimeline(context.Context, *HomePageTimelineRequest) (*HomePageTimelineResponse, error)
	// LikeCommentOrReply 点赞评论或回复
	LikeCommentOrReply(context.Context, *v11.LikeCommentOrReplyRequest) (*emptypb.Empty, error)
	ListActivities(context.Context, *v13.ListActivitiesRequest) (*v13.ListActivitiesResponse, error)
	// ListCommentReplies 获取 item's 评论回复列表
	ListCommentReplies(context.Context, *v11.ListCommentRepliesRequest) (*v11.ListCommentRepliesResponse, error)
	// ListComments 获取 item's 评论列表
	ListComments(context.Context, *v11.ListCommentsRequest) (*v11.ListCommentsResponse, error)
	// ListCommonConditionTemplates 获取Reveal/Type/Unmute玩法条件模板
	ListCommonConditionTemplates(context.Context, *v14.ListCommonStoryConditionTemplatesRequest) (*v14.ListCommonStoryConditionTemplatesResponse, error)
	ListCouldAppendMomentStories(context.Context, *v17.ListCouldAppendMomentStoriesRequest) (*v17.ListCouldAppendMomentStoriesResponse, error)
	// ListCreatorStory 创作者的 Story 列表
	ListCreatorStory(context.Context, *v14.ListCreatorStoryRequest) (*v14.ListCreatorStoryResponse, error)
	// ListCreatorStoryV2 创作者的 Story 列表
	ListCreatorStoryV2(context.Context, *v2.ListCreatorStoryRequestV2) (*v2.ListCreatorStoryResponseV2, error)
	// ListExchangeImageStoryConditionTemplates 获取换图玩法条件模板
	ListExchangeImageStoryConditionTemplates(context.Context, *v14.ListExchangeImageStoryConditionTemplatesRequest) (*v14.ListExchangeImageStoryConditionTemplatesResponse, error)
	ListFollowingCreatorStoryV2(context.Context, *v2.ListFollowingCreatorStoryRequestV2) (*v2.ListFollowingCreatorStoryResponseV2, error)
	ListHauntBooAssist(context.Context, *v2.ListHauntBooAssistRequest) (*v2.ListHauntBooAssistResponse, error)
	ListHauntQuestions(context.Context, *v2.ListHauntQuestionsRequest) (*v2.ListHauntQuestionsResponse, error)
	ListHauntRandomAvatars(context.Context, *v2.ListHauntRandomAvatarsRequest) (*v2.ListHauntRandomAvatarsResponse, error)
	// ListHomePageStoryV2 获取首页 story 列表
	ListHomePageStoryV2(context.Context, *v2.ListHomePageStoryRequestV2) (*v2.ListHomePageStoryResponseV2, error)
	ListMomentViewers(context.Context, *v17.ListMomentViewersRequest) (*v17.ListMomentViewersResponse, error)
	// ListMyCollectedHideStickers 获取用户收藏的 hide sticker
	ListMyCollectedHideStickers(context.Context, *v15.ListMyCollectedStickersRequest) (*v15.ListMyCollectedStickersResponse, error)
	ListMyPortals(context.Context, *v17.ListMyPortalsRequest) (*v17.ListMyPortalsResponse, error)
	// ListSameAuthorStoryWithAnchor 获取某个 Story 同作者一定时间范围内对的其他 story
	ListSameAuthorStoryWithAnchor(context.Context, *v14.ListSameAuthorStoryWithAnchorRequest) (*v14.ListSameAuthorStoryWithAnchorResponse, error)
	ListStoryReactionMadeUsers(context.Context, *v16.ListReactionMadeUsersRequest) (*v16.ListReactionMadeUsersResponse, error)
	ListTrendingPortals(context.Context, *v17.ListTrendingPortalsRequest) (*v17.ListTrendingPortalsResponse, error)
	// ListTurtleSoupStoryConditionTemplates 获取海龟汤玩法条件模板
	ListTurtleSoupStoryConditionTemplates(context.Context, *v14.ListTurtleSoupStoryConditionTemplatesRequest) (*v14.ListTurtleSoupStoryConditionTemplatesResponse, error)
	// ListUnlockedStory 获取用户unlocked play
	ListUnlockedStory(context.Context, *v14.ListUnlockedStoryRequest) (*v14.ListUnlockedStoryResponse, error)
	// ListUnmuteStoryConditionTemplates 获取 Unmute 玩法条件模板
	ListUnmuteStoryConditionTemplates(context.Context, *v14.ListUnmuteStoryConditionTemplatesRequest) (*v14.ListUnmuteStoryConditionTemplatesResponse, error)
	ListUserCreatedPortalsWithTimeRange(context.Context, *v17.ListUserCreatedPortalsWithTimeRangeRequest) (*v17.ListUserCreatedPortalsWithTimeRangeResponse, error)
	// ListUserReactedItems 获取用户 reacted 的 items
	ListUserReactedItems(context.Context, *v12.ListUserReactedItemsRequest) (*v12.ListUserReactedItemsResponse, error)
	ListUsersByConsumptionStatus(context.Context, *v13.ListUsersByConsumptionStatusRequest) (*v13.ListUsersByConsumptionStatusResponse, error)
	// ManualGenerateAreaEmoji 手动生成 area emoji
	ManualGenerateAreaEmoji(context.Context, *v2.ManualGenerateAreaEmojiRequest) (*v2.ManualGenerateAreaEmojiResponse, error)
	// MusicSearch 音乐搜索
	MusicSearch(context.Context, *v1.SearchRequest) (*v1.SearchResponse, error)
	// RemoveItemReaction 移除 item reaction
	RemoveItemReaction(context.Context, *v12.RemoveItemReactionRequest) (*v12.RemoveItemReactionResponse, error)
	RemoveMomentRelation(context.Context, *v17.RemoveMomentRelationRequest) (*v17.RemoveMomentRelationResponse, error)
	ReportActivityRead(context.Context, *v13.ReportActivityReadRequest) (*emptypb.Empty, error)
	// ReportHauntShow 上报 haunt boo 的展现
	ReportHauntShow(context.Context, *v2.ReportHauntShowRequest) (*v2.ReportHauntShowResponse, error)
	ReportRead(context.Context, *v17.ReportReadRequest) (*emptypb.Empty, error)
	ReportShareStat(context.Context, *ReportShareStatRequest) (*ReportShareStatResponse, error)
	SendHauntCaptureVideo(context.Context, *v2.SendHauntCaptureVideoRequest) (*emptypb.Empty, error)
	// SendMessage 发送消息
	// 本接口用于在用户完成对story的消费后，对产生的resource进行后续处理。接口名称为历史遗留问题;
	SendMessage(context.Context, *v2.SendMessageRequest) (*v2.SendMessageResponse, error)
	// SendMomentInvite 分享给at的好友创建moment的消息,创建story/moment时调用
	SendMomentInvite(context.Context, *v17.SendMomentInviteRequest) (*v17.SendMomentInviteResponse, error)
	// TopHideSticker 置顶 hide sticker
	TopHideSticker(context.Context, *v15.TopCollectedStickerRequest) (*emptypb.Empty, error)
	// TopStory 置顶 story
	TopStory(context.Context, *v14.TopStoryRequest) (*emptypb.Empty, error)
	// UnCollectHideSticker 取消收藏 hide sticker
	UnCollectHideSticker(context.Context, *v15.UnCollectHideStickerRequest) (*emptypb.Empty, error)
	// UnTopHideSticker 取消置顶 hide sticker
	UnTopHideSticker(context.Context, *v15.UnTopCollectedStickerRequest) (*emptypb.Empty, error)
	// UnlikeCommentOrReply 取消点赞评论或回复
	UnlikeCommentOrReply(context.Context, *v11.UnlikeCommentOrReplyRequest) (*emptypb.Empty, error)
	// UpdateStory 修改 story
	UpdateStory(context.Context, *v14.UpdateStoryRequest) (*v14.UpdateStoryResponse, error)
}

func RegisterItemsHTTPServer(s *http.Server, srv ItemsHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/items/music_search", _Items_MusicSearch0_HTTP_Handler(srv))
	r.POST("/v1/items/comments/create", _Items_CreateComment0_HTTP_Handler(srv))
	r.POST("/v1/items/comments/list", _Items_ListComments0_HTTP_Handler(srv))
	r.POST("/v1/items/comments/list_replies", _Items_ListCommentReplies0_HTTP_Handler(srv))
	r.POST("/v1/items/comments/delete_comment_or_reply", _Items_DeleteCommentOrReply0_HTTP_Handler(srv))
	r.POST("/v1/items/comments/like_comment_or_reply", _Items_LikeCommentOrReply0_HTTP_Handler(srv))
	r.POST("/v1/items/comments/unlike_comment_or_reply", _Items_UnlikeCommentOrReply0_HTTP_Handler(srv))
	r.POST("/v1/items/asr", _Items_Asr0_HTTP_Handler(srv))
	r.POST("/v1/items/home_page_timeline", _Items_HomePageTimeline0_HTTP_Handler(srv))
	r.POST("/v1/items/create", _Items_CreateItem0_HTTP_Handler(srv))
	r.POST("/v1/items/batch_get_item_summaries", _Items_BatchGetItemSummaries0_HTTP_Handler(srv))
	r.POST("/v1/items/reaction/create", _Items_CreateItemReaction0_HTTP_Handler(srv))
	r.POST("/v1/items/reaction/remove", _Items_RemoveItemReaction0_HTTP_Handler(srv))
	r.POST("/v1/items/reaction/list_user_reacted", _Items_ListUserReactedItems0_HTTP_Handler(srv))
	r.POST("/v1/items/activity/list_users_by_consumption_status", _Items_ListUsersByConsumptionStatus0_HTTP_Handler(srv))
	r.POST("/v1/items/story/activity/list_activities", _Items_ListActivities0_HTTP_Handler(srv))
	r.POST("/v1/items/story/activity/get_unread_count", _Items_GetActivityUnreadCount0_HTTP_Handler(srv))
	r.POST("/v1/items/story/activity/report_read", _Items_ReportActivityRead0_HTTP_Handler(srv))
	r.POST("/v1/items/story/delete", _Items_DeleteStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/update", _Items_UpdateStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_same_author_story_with_anchor", _Items_ListSameAuthorStoryWithAnchor0_HTTP_Handler(srv))
	r.POST("/v1/items/story/top", _Items_TopStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_following_creator_story", _Items_ListFollowingCreatorStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_common_condition_templates", _Items_ListCommonConditionTemplates0_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_turtle_soup_condition_templates", _Items_ListTurtleSoupStoryConditionTemplates0_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_exchange_images_condition_templetes", _Items_ListExchangeImageStoryConditionTemplates0_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_unmute_condition_templetes", _Items_ListUnmuteStoryConditionTemplates0_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_exchange_image_story", _Items_CreateExchangeImageStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_who_story", _Items_CreateWhoStoryV20_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_who_story", _Items_ConsumeWhoStoryV20_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_exchange_image_story", _Items_CreateExchangeImageStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_turtle_soup_story", _Items_CreateTurtleSoupStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_pin_story", _Items_CreatePinStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_pin_story", _Items_ConsumePinStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/auto_generate_area_emoji", _Items_AutoGenerateAreaEmoji0_HTTP_Handler(srv))
	r.POST("/v2/items/story/manual_generate_area_emoji", _Items_ManualGenerateAreaEmoji0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_hide_story", _Items_CreateHideStoryV20_HTTP_Handler(srv))
	r.POST("/v2/items/story/generate_hide_image_mask", _Items_GenerateHideImageMask0_HTTP_Handler(srv))
	r.POST("/v2/items/story/collect_hide_sticker", _Items_CollectHideSticker0_HTTP_Handler(srv))
	r.POST("/v2/items/story/uncollect_hide_sticker", _Items_UnCollectHideSticker0_HTTP_Handler(srv))
	r.POST("/v2/items/story/top_hide_sticker", _Items_TopHideSticker0_HTTP_Handler(srv))
	r.POST("/v2/items/story/untop_hide_sticker", _Items_UnTopHideSticker0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_my_collected_hide_stickers", _Items_ListMyCollectedHideStickers0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_turtle_soup_story", _Items_CreateTurtleSoupStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/get_roasted_topics", _Items_GetRoastedTopics0_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_roasted_story", _Items_CreateRoastedStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_base_play_story", _Items_CreateBasePlayStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_unmute_story", _Items_CreateUnmuteStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_chatproxy_story", _Items_CreateChatProxyStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/get_chatproxy_next_topic", _Items_GetChatProxyNextTopic0_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_chatproxy", _Items_ConsumeChatProxy0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_unmute_story", _Items_CreateUnmuteStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_now_shot_story", _Items_CreateNowShotStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_now_shot_story", _Items_CreateNowShotStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_capsule_story", _Items_CreateCapsuleStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/get_story_detail", _Items_GetStoryDetail0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_home_page_story", _Items_ListHomePageStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_creator_story", _Items_ListCreatorStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_creator_story", _Items_ListCreatorStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_unlocked_story", _Items_ListUnlockedStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_roasted_topic", _Items_ConsumeRoastedTopic0_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_hide_story", _Items_ConsumeHideStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_roasted_story", _Items_ConsumeRoastedStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_roasted_story", _Items_ConsumeRoastedStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_base_play_story", _Items_ConsumeBasePlayStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_exchange_image_story", _Items_ConsumeExchangeImageStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_turtle_soup_story", _Items_ConsumeTurtleSoupStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/get_wassup_greetings", _Items_GetWassupGreetings0_HTTP_Handler(srv))
	r.POST("/v2/items/story/get_wassup_next_question", _Items_GetWassupNextQuestion0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_wassup_story", _Items_CreateWassupStoryV20_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_wassup_story", _Items_ConsumeWassupStoryV20_HTTP_Handler(srv))
	r.POST("/v2/items/story/send_message", _Items_SendMessage1_HTTP_Handler(srv))
	r.POST("/v2/items/story/report_haunt_show", _Items_ReportHauntShow0_HTTP_Handler(srv))
	r.POST("/v2/items/story/create_haunt_story", _Items_CreateHauntStoryV20_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_haunt_story", _Items_ConsumeHauntStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/send_haunt_capture_video", _Items_SendHauntCaptureVideo0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_haunt_random_avatars", _Items_ListHauntRandomAvatars0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_haunt_questions", _Items_ListHauntQuestions0_HTTP_Handler(srv))
	r.POST("/v2/items/story/list_haunt_boo_assist", _Items_ListHauntBooAssist0_HTTP_Handler(srv))
	r.POST("/v2/items/story/add_capture_boo_into_my_assist", _Items_AddCaptureBooIntoMyAssist0_HTTP_Handler(srv))
	r.POST("/v2/items/story/add_captured_boo_in_to_collected_stickers", _Items_AddCapturedBooInToCollectedStickers0_HTTP_Handler(srv))
	r.POST("/v2/items/story/check_haunt_image", _Items_CheckHauntImage0_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_unmute_story", _Items_ConsumeUnmuteStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_now_shot_story", _Items_ConsumeNowShotStory0_HTTP_Handler(srv))
	r.POST("/v2/items/story/consume_now_shot_story", _Items_ConsumeNowShotStoryV20_HTTP_Handler(srv))
	r.POST("/v1/items/story/consume_capsule_story", _Items_ConsumeCapsuleStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/copilot_capsule_story", _Items_CopilotCapsuleStory0_HTTP_Handler(srv))
	r.POST("/v1/items/story/create_story_reaction", _Items_CreateStoryReaction0_HTTP_Handler(srv))
	r.POST("/v1/items/story/delete_story_reaction", _Items_DeleteStoryReaction0_HTTP_Handler(srv))
	r.POST("/v1/items/story/list_reaction_made_users", _Items_ListStoryReactionMadeUsers0_HTTP_Handler(srv))
	r.POST("/v1/items/story/report_share_stats", _Items_ReportShareStat0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/create_moment", _Items_CreateMoment0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/get_user_created_portals_info", _Items_GetUserCreatedPortalsInfo0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/delete_moment", _Items_DeleteMoment0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/create_moment_relation", _Items_CreateMomentRelation0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/remove_moment_relation", _Items_RemoveMomentRelation0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/list_user_created_portals_with_time_range", _Items_ListUserCreatedPortalsWithTimeRange0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/list_my_portals", _Items_ListMyPortals0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/list_could_append_moment_stories", _Items_ListCouldAppendMomentStories0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/report_read", _Items_ReportRead0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/get_portal", _Items_GetPortal0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/list_moment_viewers", _Items_ListMomentViewers0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/trending_portals", _Items_ListTrendingPortals0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/get_moment", _Items_GetMoment0_HTTP_Handler(srv))
	r.POST("/v1/items/portal/send_moment_invite", _Items_SendMomentInvite0_HTTP_Handler(srv))
}

func _Items_MusicSearch0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v1.SearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsMusicSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.MusicSearch(ctx, req.(*v1.SearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v1.SearchResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateComment0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.CreateCommentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateComment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateComment(ctx, req.(*v11.CreateCommentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.CreateCommentResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListComments0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.ListCommentsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListComments)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListComments(ctx, req.(*v11.ListCommentsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListCommentsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListCommentReplies0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.ListCommentRepliesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListCommentReplies)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCommentReplies(ctx, req.(*v11.ListCommentRepliesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v11.ListCommentRepliesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_DeleteCommentOrReply0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.DeleteCommentOrReplyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsDeleteCommentOrReply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteCommentOrReply(ctx, req.(*v11.DeleteCommentOrReplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_LikeCommentOrReply0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.LikeCommentOrReplyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsLikeCommentOrReply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.LikeCommentOrReply(ctx, req.(*v11.LikeCommentOrReplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_UnlikeCommentOrReply0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v11.UnlikeCommentOrReplyRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsUnlikeCommentOrReply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnlikeCommentOrReply(ctx, req.(*v11.UnlikeCommentOrReplyRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_Asr0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AsrRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsAsr)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Asr(ctx, req.(*AsrRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AsrResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_HomePageTimeline0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HomePageTimelineRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsHomePageTimeline)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.HomePageTimeline(ctx, req.(*HomePageTimelineRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HomePageTimelineResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateItem0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateItemRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateItem)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateItem(ctx, req.(*CreateItemRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateItemResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_BatchGetItemSummaries0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in BatchGetItemSummariesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsBatchGetItemSummaries)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.BatchGetItemSummaries(ctx, req.(*BatchGetItemSummariesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*BatchGetItemSummariesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateItemReaction0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.CreateItemReactionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateItemReaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateItemReaction(ctx, req.(*v12.CreateItemReactionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.CreateItemReactionResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_RemoveItemReaction0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.RemoveItemReactionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsRemoveItemReaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveItemReaction(ctx, req.(*v12.RemoveItemReactionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.RemoveItemReactionResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListUserReactedItems0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v12.ListUserReactedItemsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListUserReactedItems)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserReactedItems(ctx, req.(*v12.ListUserReactedItemsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v12.ListUserReactedItemsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListUsersByConsumptionStatus0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.ListUsersByConsumptionStatusRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListUsersByConsumptionStatus)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUsersByConsumptionStatus(ctx, req.(*v13.ListUsersByConsumptionStatusRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.ListUsersByConsumptionStatusResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListActivities0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.ListActivitiesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListActivities)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListActivities(ctx, req.(*v13.ListActivitiesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.ListActivitiesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_GetActivityUnreadCount0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.GetActivityUnreadCountRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetActivityUnreadCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetActivityUnreadCount(ctx, req.(*v13.GetActivityUnreadCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v13.GetActivityUnreadCountResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ReportActivityRead0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v13.ReportActivityReadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsReportActivityRead)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportActivityRead(ctx, req.(*v13.ReportActivityReadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_DeleteStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.DeleteStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsDeleteStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteStory(ctx, req.(*v14.DeleteStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_UpdateStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.UpdateStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsUpdateStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateStory(ctx, req.(*v14.UpdateStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.UpdateStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListSameAuthorStoryWithAnchor0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListSameAuthorStoryWithAnchorRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListSameAuthorStoryWithAnchor)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListSameAuthorStoryWithAnchor(ctx, req.(*v14.ListSameAuthorStoryWithAnchorRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListSameAuthorStoryWithAnchorResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_TopStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.TopStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsTopStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TopStory(ctx, req.(*v14.TopStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_ListFollowingCreatorStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ListFollowingCreatorStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListFollowingCreatorStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListFollowingCreatorStoryV2(ctx, req.(*v2.ListFollowingCreatorStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ListFollowingCreatorStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ListCommonConditionTemplates0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListCommonStoryConditionTemplatesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListCommonConditionTemplates)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCommonConditionTemplates(ctx, req.(*v14.ListCommonStoryConditionTemplatesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListCommonStoryConditionTemplatesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListTurtleSoupStoryConditionTemplates0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListTurtleSoupStoryConditionTemplatesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListTurtleSoupStoryConditionTemplates)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTurtleSoupStoryConditionTemplates(ctx, req.(*v14.ListTurtleSoupStoryConditionTemplatesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListTurtleSoupStoryConditionTemplatesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListExchangeImageStoryConditionTemplates0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListExchangeImageStoryConditionTemplatesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListExchangeImageStoryConditionTemplates)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExchangeImageStoryConditionTemplates(ctx, req.(*v14.ListExchangeImageStoryConditionTemplatesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListExchangeImageStoryConditionTemplatesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListUnmuteStoryConditionTemplates0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListUnmuteStoryConditionTemplatesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListUnmuteStoryConditionTemplates)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUnmuteStoryConditionTemplates(ctx, req.(*v14.ListUnmuteStoryConditionTemplatesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListUnmuteStoryConditionTemplatesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateExchangeImageStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateExchangeImageStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateExchangeImageStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateExchangeImageStory(ctx, req.(*v14.CreateExchangeImageStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateExchangeImageStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateWhoStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateWhoStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateWhoStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWhoStoryV2(ctx, req.(*v2.CreateWhoStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateWhoStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeWhoStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeWhoStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeWhoStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeWhoStoryV2(ctx, req.(*v2.ConsumeWhoStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumeWhoStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateExchangeImageStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateExchangeImageStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateExchangeImageStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateExchangeImageStoryV2(ctx, req.(*v2.CreateExchangeImageStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateExchangeImageStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateTurtleSoupStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateTurtleSoupStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateTurtleSoupStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTurtleSoupStory(ctx, req.(*v14.CreateTurtleSoupStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateTurtleSoupStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreatePinStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreatePinStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreatePinStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreatePinStory(ctx, req.(*v2.CreatePinStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreatePinStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumePinStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumePinStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumePinStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumePinStory(ctx, req.(*v2.ConsumePinStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumePinStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_AutoGenerateAreaEmoji0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.AutoGenerateAreaEmojiRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsAutoGenerateAreaEmoji)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AutoGenerateAreaEmoji(ctx, req.(*v2.AutoGenerateAreaEmojiRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.AutoGenerateAreaEmojiResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ManualGenerateAreaEmoji0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ManualGenerateAreaEmojiRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsManualGenerateAreaEmoji)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ManualGenerateAreaEmoji(ctx, req.(*v2.ManualGenerateAreaEmojiRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ManualGenerateAreaEmojiResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateHideStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateHideStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateHideStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateHideStoryV2(ctx, req.(*v2.CreateHideStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateHideStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_GenerateHideImageMask0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.GenerateHideImageMaskRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGenerateHideImageMask)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenerateHideImageMask(ctx, req.(*v2.GenerateHideImageMaskRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.GenerateHideImageMaskResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CollectHideSticker0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.CollectHideStickerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCollectHideSticker)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CollectHideSticker(ctx, req.(*v15.CollectHideStickerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_UnCollectHideSticker0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.UnCollectHideStickerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsUnCollectHideSticker)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnCollectHideSticker(ctx, req.(*v15.UnCollectHideStickerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_TopHideSticker0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.TopCollectedStickerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsTopHideSticker)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TopHideSticker(ctx, req.(*v15.TopCollectedStickerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_UnTopHideSticker0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.UnTopCollectedStickerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsUnTopHideSticker)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UnTopHideSticker(ctx, req.(*v15.UnTopCollectedStickerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_ListMyCollectedHideStickers0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v15.ListMyCollectedStickersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListMyCollectedHideStickers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMyCollectedHideStickers(ctx, req.(*v15.ListMyCollectedStickersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v15.ListMyCollectedStickersResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateTurtleSoupStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateTurtleSoupStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateTurtleSoupStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateTurtleSoupStoryV2(ctx, req.(*v2.CreateTurtleSoupStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateTurtleSoupStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_GetRoastedTopics0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.GetRoastedTopicsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetRoastedTopics)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRoastedTopics(ctx, req.(*v14.GetRoastedTopicsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.GetRoastedTopicsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateRoastedStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateRoastedStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateRoastedStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateRoastedStory(ctx, req.(*v14.CreateRoastedStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateRoastedStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateBasePlayStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateBasePlayStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateBasePlayStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateBasePlayStory(ctx, req.(*v14.CreateBasePlayStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateBasePlayStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateUnmuteStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateUnmuteStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateUnmuteStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUnmuteStory(ctx, req.(*v14.CreateUnmuteStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateUnmuteStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateChatProxyStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateChatProxyStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateChatProxyStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateChatProxyStory(ctx, req.(*v2.CreateChatProxyStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateChatProxyStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_GetChatProxyNextTopic0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.GetChatProxyNextTopicRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetChatProxyNextTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetChatProxyNextTopic(ctx, req.(*v2.GetChatProxyNextTopicRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.GetChatProxyNextTopicResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeChatProxy0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeChatProxyRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeChatProxy)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeChatProxy(ctx, req.(*v2.ConsumeChatProxyRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumeChatProxyResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateUnmuteStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateUnmuteStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateUnmuteStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateUnmuteStoryV2(ctx, req.(*v2.CreateUnmuteStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateUnmuteStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateNowShotStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateNowShotStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateNowShotStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateNowShotStory(ctx, req.(*v14.CreateNowShotStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateNowShotStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateNowShotStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateNowShotStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateNowShotStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateNowShotStoryV2(ctx, req.(*v2.CreateNowShotStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.StoryDetailResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateCapsuleStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CreateCapsuleStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateCapsuleStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateCapsuleStory(ctx, req.(*v14.CreateCapsuleStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CreateCapsuleStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_GetStoryDetail0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.GetStoryDetailRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetStoryDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetStoryDetail(ctx, req.(*v14.GetStoryDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.GetStoryDetailResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListHomePageStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ListHomePageStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListHomePageStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHomePageStoryV2(ctx, req.(*v2.ListHomePageStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ListHomePageStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ListCreatorStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListCreatorStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListCreatorStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCreatorStory(ctx, req.(*v14.ListCreatorStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListCreatorStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListCreatorStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ListCreatorStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListCreatorStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCreatorStoryV2(ctx, req.(*v2.ListCreatorStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ListCreatorStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ListUnlockedStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ListUnlockedStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListUnlockedStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUnlockedStory(ctx, req.(*v14.ListUnlockedStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ListUnlockedStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeRoastedTopic0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeRoastedTopicRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeRoastedTopic)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeRoastedTopic(ctx, req.(*v14.ConsumeRoastedTopicRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeRoastedTopicResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeHideStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeHideStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeHideStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeHideStory(ctx, req.(*v2.ConsumeHideStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumeHideStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeRoastedStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeRoastedStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeRoastedStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeRoastedStory(ctx, req.(*v14.ConsumeRoastedStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeRoastedStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeRoastedStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeRoastedStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeRoastedStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeRoastedStoryV2(ctx, req.(*v2.ConsumeRoastedStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumeRoastedStoryResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeBasePlayStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeBasePlayStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeBasePlayStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeBasePlayStory(ctx, req.(*v14.ConsumeBasePlayStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeBasePlayStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeExchangeImageStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeExchangeImageStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeExchangeImageStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeExchangeImageStory(ctx, req.(*v14.ConsumeExchangeImageStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeExchangeImageStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeTurtleSoupStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeTurtleSoupStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeTurtleSoupStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeTurtleSoupStory(ctx, req.(*v14.ConsumeTurtleSoupStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeTurtleSoupStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_GetWassupGreetings0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.GetWassupGreetingsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetWassupGreetings)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWassupGreetings(ctx, req.(*v2.GetWassupGreetingsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.GetWassupGreetingsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_GetWassupNextQuestion0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.GetWassupNextQuestionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetWassupNextQuestion)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetWassupNextQuestion(ctx, req.(*v2.GetWassupNextQuestionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.GetWassupNextQuestionResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateWassupStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateWassupStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateWassupStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateWassupStoryV2(ctx, req.(*v2.CreateWassupStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateWassupStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeWassupStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeWassupStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeWassupStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeWassupStoryV2(ctx, req.(*v2.ConsumeWassupStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumeWassupStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_SendMessage1_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.SendMessageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsSendMessage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendMessage(ctx, req.(*v2.SendMessageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.SendMessageResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ReportHauntShow0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ReportHauntShowRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsReportHauntShow)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportHauntShow(ctx, req.(*v2.ReportHauntShowRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ReportHauntShowResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateHauntStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.CreateHauntStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateHauntStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateHauntStoryV2(ctx, req.(*v2.CreateHauntStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.CreateHauntStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeHauntStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeHauntStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeHauntStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeHauntStory(ctx, req.(*v2.ConsumeHauntStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ConsumeHauntStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_SendHauntCaptureVideo0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.SendHauntCaptureVideoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsSendHauntCaptureVideo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendHauntCaptureVideo(ctx, req.(*v2.SendHauntCaptureVideoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_ListHauntRandomAvatars0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ListHauntRandomAvatarsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListHauntRandomAvatars)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHauntRandomAvatars(ctx, req.(*v2.ListHauntRandomAvatarsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ListHauntRandomAvatarsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListHauntQuestions0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ListHauntQuestionsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListHauntQuestions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHauntQuestions(ctx, req.(*v2.ListHauntQuestionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ListHauntQuestionsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListHauntBooAssist0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ListHauntBooAssistRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListHauntBooAssist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListHauntBooAssist(ctx, req.(*v2.ListHauntBooAssistRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ListHauntBooAssistResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_AddCaptureBooIntoMyAssist0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.AddCaptureBooIntoMyAssistRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsAddCaptureBooIntoMyAssist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddCaptureBooIntoMyAssist(ctx, req.(*v2.AddCaptureBooIntoMyAssistRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_AddCapturedBooInToCollectedStickers0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.AddCapturedBooInToCollectedStickersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsAddCapturedBooInToCollectedStickers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddCapturedBooInToCollectedStickers(ctx, req.(*v2.AddCapturedBooInToCollectedStickersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.AddCapturedBooInToCollectedStickersResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CheckHauntImage0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ImageCheckRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCheckHauntImage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckHauntImage(ctx, req.(*v2.ImageCheckRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.ImageCheckResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeUnmuteStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeUnmuteStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeUnmuteStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeUnmuteStory(ctx, req.(*v14.ConsumeUnmuteStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeUnmuteStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeNowShotStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeNowShotStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeNowShotStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeNowShotStory(ctx, req.(*v14.ConsumeNowShotStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeNowShotStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeNowShotStoryV20_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v2.ConsumeNowShotStoryRequestV2
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeNowShotStoryV2)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeNowShotStoryV2(ctx, req.(*v2.ConsumeNowShotStoryRequestV2))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v2.StoryDetailResponseV2)
		return ctx.Result(200, reply)
	}
}

func _Items_ConsumeCapsuleStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.ConsumeCapsuleStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsConsumeCapsuleStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ConsumeCapsuleStory(ctx, req.(*v14.ConsumeCapsuleStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.ConsumeCapsuleStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CopilotCapsuleStory0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v14.CopilotCapsuleStoryRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCopilotCapsuleStory)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CopilotCapsuleStory(ctx, req.(*v14.CopilotCapsuleStoryRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v14.CopilotCapsuleStoryResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateStoryReaction0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v16.CreateReactionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateStoryReaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateStoryReaction(ctx, req.(*v16.CreateReactionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v16.CreateReactionResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_DeleteStoryReaction0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v16.DeleteReactionRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsDeleteStoryReaction)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteStoryReaction(ctx, req.(*v16.DeleteReactionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v16.DeleteReactionResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListStoryReactionMadeUsers0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v16.ListReactionMadeUsersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListStoryReactionMadeUsers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListStoryReactionMadeUsers(ctx, req.(*v16.ListReactionMadeUsersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v16.ListReactionMadeUsersResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ReportShareStat0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ReportShareStatRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsReportShareStat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportShareStat(ctx, req.(*ReportShareStatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ReportShareStatResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateMoment0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.CreateMomentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateMoment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateMoment(ctx, req.(*v17.CreateMomentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.CreateMomentResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_GetUserCreatedPortalsInfo0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.GetUserCreatedPortalsInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetUserCreatedPortalsInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserCreatedPortalsInfo(ctx, req.(*v17.GetUserCreatedPortalsInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.GetUserCreatedPortalsInfoResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_DeleteMoment0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.DeleteMomentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsDeleteMoment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteMoment(ctx, req.(*v17.DeleteMomentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_CreateMomentRelation0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.CreateMomentRelationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsCreateMomentRelation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateMomentRelation(ctx, req.(*v17.CreateMomentRelationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.CreateMomentRelationResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_RemoveMomentRelation0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.RemoveMomentRelationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsRemoveMomentRelation)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveMomentRelation(ctx, req.(*v17.RemoveMomentRelationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.RemoveMomentRelationResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListUserCreatedPortalsWithTimeRange0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.ListUserCreatedPortalsWithTimeRangeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListUserCreatedPortalsWithTimeRange)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListUserCreatedPortalsWithTimeRange(ctx, req.(*v17.ListUserCreatedPortalsWithTimeRangeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.ListUserCreatedPortalsWithTimeRangeResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListMyPortals0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.ListMyPortalsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListMyPortals)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMyPortals(ctx, req.(*v17.ListMyPortalsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.ListMyPortalsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListCouldAppendMomentStories0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.ListCouldAppendMomentStoriesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListCouldAppendMomentStories)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListCouldAppendMomentStories(ctx, req.(*v17.ListCouldAppendMomentStoriesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.ListCouldAppendMomentStoriesResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ReportRead0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.ReportReadRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsReportRead)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ReportRead(ctx, req.(*v17.ReportReadRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*emptypb.Empty)
		return ctx.Result(200, reply)
	}
}

func _Items_GetPortal0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.GetPortalRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetPortal)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPortal(ctx, req.(*v17.GetPortalRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.GetPortalResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListMomentViewers0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.ListMomentViewersRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListMomentViewers)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListMomentViewers(ctx, req.(*v17.ListMomentViewersRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.ListMomentViewersResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_ListTrendingPortals0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.ListTrendingPortalsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsListTrendingPortals)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListTrendingPortals(ctx, req.(*v17.ListTrendingPortalsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.ListTrendingPortalsResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_GetMoment0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.GetMomentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsGetMoment)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMoment(ctx, req.(*v17.GetMomentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.GetMomentResponse)
		return ctx.Result(200, reply)
	}
}

func _Items_SendMomentInvite0_HTTP_Handler(srv ItemsHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in v17.SendMomentInviteRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationItemsSendMomentInvite)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SendMomentInvite(ctx, req.(*v17.SendMomentInviteRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*v17.SendMomentInviteResponse)
		return ctx.Result(200, reply)
	}
}

type ItemsHTTPClient interface {
	AddCaptureBooIntoMyAssist(ctx context.Context, req *v2.AddCaptureBooIntoMyAssistRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	AddCapturedBooInToCollectedStickers(ctx context.Context, req *v2.AddCapturedBooInToCollectedStickersRequest, opts ...http.CallOption) (rsp *v2.AddCapturedBooInToCollectedStickersResponse, err error)
	Asr(ctx context.Context, req *AsrRequest, opts ...http.CallOption) (rsp *AsrResponse, err error)
	AutoGenerateAreaEmoji(ctx context.Context, req *v2.AutoGenerateAreaEmojiRequest, opts ...http.CallOption) (rsp *v2.AutoGenerateAreaEmojiResponse, err error)
	BatchGetItemSummaries(ctx context.Context, req *BatchGetItemSummariesRequest, opts ...http.CallOption) (rsp *BatchGetItemSummariesResponse, err error)
	CheckHauntImage(ctx context.Context, req *v2.ImageCheckRequest, opts ...http.CallOption) (rsp *v2.ImageCheckResponse, err error)
	CollectHideSticker(ctx context.Context, req *v15.CollectHideStickerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ConsumeBasePlayStory(ctx context.Context, req *v14.ConsumeBasePlayStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeBasePlayStoryResponse, err error)
	ConsumeCapsuleStory(ctx context.Context, req *v14.ConsumeCapsuleStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeCapsuleStoryResponse, err error)
	ConsumeChatProxy(ctx context.Context, req *v2.ConsumeChatProxyRequestV2, opts ...http.CallOption) (rsp *v2.ConsumeChatProxyResponseV2, err error)
	ConsumeExchangeImageStory(ctx context.Context, req *v14.ConsumeExchangeImageStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeExchangeImageStoryResponse, err error)
	ConsumeHauntStory(ctx context.Context, req *v2.ConsumeHauntStoryRequest, opts ...http.CallOption) (rsp *v2.ConsumeHauntStoryResponse, err error)
	ConsumeHideStory(ctx context.Context, req *v2.ConsumeHideStoryRequest, opts ...http.CallOption) (rsp *v2.ConsumeHideStoryResponse, err error)
	ConsumeNowShotStory(ctx context.Context, req *v14.ConsumeNowShotStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeNowShotStoryResponse, err error)
	ConsumeNowShotStoryV2(ctx context.Context, req *v2.ConsumeNowShotStoryRequestV2, opts ...http.CallOption) (rsp *v2.StoryDetailResponseV2, err error)
	ConsumePinStory(ctx context.Context, req *v2.ConsumePinStoryRequest, opts ...http.CallOption) (rsp *v2.ConsumePinStoryResponse, err error)
	ConsumeRoastedStory(ctx context.Context, req *v14.ConsumeRoastedStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeRoastedStoryResponse, err error)
	ConsumeRoastedStoryV2(ctx context.Context, req *v2.ConsumeRoastedStoryRequestV2, opts ...http.CallOption) (rsp *v2.ConsumeRoastedStoryResponseV2, err error)
	ConsumeRoastedTopic(ctx context.Context, req *v14.ConsumeRoastedTopicRequest, opts ...http.CallOption) (rsp *v14.ConsumeRoastedTopicResponse, err error)
	ConsumeTurtleSoupStory(ctx context.Context, req *v14.ConsumeTurtleSoupStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeTurtleSoupStoryResponse, err error)
	ConsumeUnmuteStory(ctx context.Context, req *v14.ConsumeUnmuteStoryRequest, opts ...http.CallOption) (rsp *v14.ConsumeUnmuteStoryResponse, err error)
	ConsumeWassupStoryV2(ctx context.Context, req *v2.ConsumeWassupStoryRequest, opts ...http.CallOption) (rsp *v2.ConsumeWassupStoryResponse, err error)
	ConsumeWhoStoryV2(ctx context.Context, req *v2.ConsumeWhoStoryRequestV2, opts ...http.CallOption) (rsp *v2.ConsumeWhoStoryResponseV2, err error)
	CopilotCapsuleStory(ctx context.Context, req *v14.CopilotCapsuleStoryRequest, opts ...http.CallOption) (rsp *v14.CopilotCapsuleStoryResponse, err error)
	CreateBasePlayStory(ctx context.Context, req *v14.CreateBasePlayStoryRequest, opts ...http.CallOption) (rsp *v14.CreateBasePlayStoryResponse, err error)
	CreateCapsuleStory(ctx context.Context, req *v14.CreateCapsuleStoryRequest, opts ...http.CallOption) (rsp *v14.CreateCapsuleStoryResponse, err error)
	CreateChatProxyStory(ctx context.Context, req *v2.CreateChatProxyStoryRequestV2, opts ...http.CallOption) (rsp *v2.CreateChatProxyStoryResponseV2, err error)
	CreateComment(ctx context.Context, req *v11.CreateCommentRequest, opts ...http.CallOption) (rsp *v11.CreateCommentResponse, err error)
	CreateExchangeImageStory(ctx context.Context, req *v14.CreateExchangeImageStoryRequest, opts ...http.CallOption) (rsp *v14.CreateExchangeImageStoryResponse, err error)
	CreateExchangeImageStoryV2(ctx context.Context, req *v2.CreateExchangeImageStoryRequestV2, opts ...http.CallOption) (rsp *v2.CreateExchangeImageStoryResponseV2, err error)
	CreateHauntStoryV2(ctx context.Context, req *v2.CreateHauntStoryRequest, opts ...http.CallOption) (rsp *v2.CreateHauntStoryResponse, err error)
	CreateHideStoryV2(ctx context.Context, req *v2.CreateHideStoryRequestV2, opts ...http.CallOption) (rsp *v2.CreateHideStoryResponseV2, err error)
	CreateItem(ctx context.Context, req *CreateItemRequest, opts ...http.CallOption) (rsp *CreateItemResponse, err error)
	CreateItemReaction(ctx context.Context, req *v12.CreateItemReactionRequest, opts ...http.CallOption) (rsp *v12.CreateItemReactionResponse, err error)
	CreateMoment(ctx context.Context, req *v17.CreateMomentRequest, opts ...http.CallOption) (rsp *v17.CreateMomentResponse, err error)
	CreateMomentRelation(ctx context.Context, req *v17.CreateMomentRelationRequest, opts ...http.CallOption) (rsp *v17.CreateMomentRelationResponse, err error)
	CreateNowShotStory(ctx context.Context, req *v14.CreateNowShotStoryRequest, opts ...http.CallOption) (rsp *v14.CreateNowShotStoryResponse, err error)
	CreateNowShotStoryV2(ctx context.Context, req *v2.CreateNowShotStoryRequestV2, opts ...http.CallOption) (rsp *v2.StoryDetailResponseV2, err error)
	CreatePinStory(ctx context.Context, req *v2.CreatePinStoryRequest, opts ...http.CallOption) (rsp *v2.CreatePinStoryResponse, err error)
	CreateRoastedStory(ctx context.Context, req *v14.CreateRoastedStoryRequest, opts ...http.CallOption) (rsp *v14.CreateRoastedStoryResponse, err error)
	CreateStoryReaction(ctx context.Context, req *v16.CreateReactionRequest, opts ...http.CallOption) (rsp *v16.CreateReactionResponse, err error)
	CreateTurtleSoupStory(ctx context.Context, req *v14.CreateTurtleSoupStoryRequest, opts ...http.CallOption) (rsp *v14.CreateTurtleSoupStoryResponse, err error)
	CreateTurtleSoupStoryV2(ctx context.Context, req *v2.CreateTurtleSoupStoryRequestV2, opts ...http.CallOption) (rsp *v2.CreateTurtleSoupStoryResponseV2, err error)
	CreateUnmuteStory(ctx context.Context, req *v14.CreateUnmuteStoryRequest, opts ...http.CallOption) (rsp *v14.CreateUnmuteStoryResponse, err error)
	CreateUnmuteStoryV2(ctx context.Context, req *v2.CreateUnmuteStoryRequestV2, opts ...http.CallOption) (rsp *v2.CreateUnmuteStoryResponseV2, err error)
	CreateWassupStoryV2(ctx context.Context, req *v2.CreateWassupStoryRequest, opts ...http.CallOption) (rsp *v2.CreateWassupStoryResponse, err error)
	CreateWhoStoryV2(ctx context.Context, req *v2.CreateWhoStoryRequestV2, opts ...http.CallOption) (rsp *v2.CreateWhoStoryResponseV2, err error)
	DeleteCommentOrReply(ctx context.Context, req *v11.DeleteCommentOrReplyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteMoment(ctx context.Context, req *v17.DeleteMomentRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteStory(ctx context.Context, req *v14.DeleteStoryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	DeleteStoryReaction(ctx context.Context, req *v16.DeleteReactionRequest, opts ...http.CallOption) (rsp *v16.DeleteReactionResponse, err error)
	GenerateHideImageMask(ctx context.Context, req *v2.GenerateHideImageMaskRequest, opts ...http.CallOption) (rsp *v2.GenerateHideImageMaskResponse, err error)
	GetActivityUnreadCount(ctx context.Context, req *v13.GetActivityUnreadCountRequest, opts ...http.CallOption) (rsp *v13.GetActivityUnreadCountResponse, err error)
	GetChatProxyNextTopic(ctx context.Context, req *v2.GetChatProxyNextTopicRequestV2, opts ...http.CallOption) (rsp *v2.GetChatProxyNextTopicResponseV2, err error)
	GetMoment(ctx context.Context, req *v17.GetMomentRequest, opts ...http.CallOption) (rsp *v17.GetMomentResponse, err error)
	GetPortal(ctx context.Context, req *v17.GetPortalRequest, opts ...http.CallOption) (rsp *v17.GetPortalResponse, err error)
	GetRoastedTopics(ctx context.Context, req *v14.GetRoastedTopicsRequest, opts ...http.CallOption) (rsp *v14.GetRoastedTopicsResponse, err error)
	GetStoryDetail(ctx context.Context, req *v14.GetStoryDetailRequest, opts ...http.CallOption) (rsp *v14.GetStoryDetailResponse, err error)
	GetUserCreatedPortalsInfo(ctx context.Context, req *v17.GetUserCreatedPortalsInfoRequest, opts ...http.CallOption) (rsp *v17.GetUserCreatedPortalsInfoResponse, err error)
	GetWassupGreetings(ctx context.Context, req *v2.GetWassupGreetingsRequest, opts ...http.CallOption) (rsp *v2.GetWassupGreetingsResponse, err error)
	GetWassupNextQuestion(ctx context.Context, req *v2.GetWassupNextQuestionRequest, opts ...http.CallOption) (rsp *v2.GetWassupNextQuestionResponse, err error)
	HomePageTimeline(ctx context.Context, req *HomePageTimelineRequest, opts ...http.CallOption) (rsp *HomePageTimelineResponse, err error)
	LikeCommentOrReply(ctx context.Context, req *v11.LikeCommentOrReplyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ListActivities(ctx context.Context, req *v13.ListActivitiesRequest, opts ...http.CallOption) (rsp *v13.ListActivitiesResponse, err error)
	ListCommentReplies(ctx context.Context, req *v11.ListCommentRepliesRequest, opts ...http.CallOption) (rsp *v11.ListCommentRepliesResponse, err error)
	ListComments(ctx context.Context, req *v11.ListCommentsRequest, opts ...http.CallOption) (rsp *v11.ListCommentsResponse, err error)
	ListCommonConditionTemplates(ctx context.Context, req *v14.ListCommonStoryConditionTemplatesRequest, opts ...http.CallOption) (rsp *v14.ListCommonStoryConditionTemplatesResponse, err error)
	ListCouldAppendMomentStories(ctx context.Context, req *v17.ListCouldAppendMomentStoriesRequest, opts ...http.CallOption) (rsp *v17.ListCouldAppendMomentStoriesResponse, err error)
	ListCreatorStory(ctx context.Context, req *v14.ListCreatorStoryRequest, opts ...http.CallOption) (rsp *v14.ListCreatorStoryResponse, err error)
	ListCreatorStoryV2(ctx context.Context, req *v2.ListCreatorStoryRequestV2, opts ...http.CallOption) (rsp *v2.ListCreatorStoryResponseV2, err error)
	ListExchangeImageStoryConditionTemplates(ctx context.Context, req *v14.ListExchangeImageStoryConditionTemplatesRequest, opts ...http.CallOption) (rsp *v14.ListExchangeImageStoryConditionTemplatesResponse, err error)
	ListFollowingCreatorStoryV2(ctx context.Context, req *v2.ListFollowingCreatorStoryRequestV2, opts ...http.CallOption) (rsp *v2.ListFollowingCreatorStoryResponseV2, err error)
	ListHauntBooAssist(ctx context.Context, req *v2.ListHauntBooAssistRequest, opts ...http.CallOption) (rsp *v2.ListHauntBooAssistResponse, err error)
	ListHauntQuestions(ctx context.Context, req *v2.ListHauntQuestionsRequest, opts ...http.CallOption) (rsp *v2.ListHauntQuestionsResponse, err error)
	ListHauntRandomAvatars(ctx context.Context, req *v2.ListHauntRandomAvatarsRequest, opts ...http.CallOption) (rsp *v2.ListHauntRandomAvatarsResponse, err error)
	ListHomePageStoryV2(ctx context.Context, req *v2.ListHomePageStoryRequestV2, opts ...http.CallOption) (rsp *v2.ListHomePageStoryResponseV2, err error)
	ListMomentViewers(ctx context.Context, req *v17.ListMomentViewersRequest, opts ...http.CallOption) (rsp *v17.ListMomentViewersResponse, err error)
	ListMyCollectedHideStickers(ctx context.Context, req *v15.ListMyCollectedStickersRequest, opts ...http.CallOption) (rsp *v15.ListMyCollectedStickersResponse, err error)
	ListMyPortals(ctx context.Context, req *v17.ListMyPortalsRequest, opts ...http.CallOption) (rsp *v17.ListMyPortalsResponse, err error)
	ListSameAuthorStoryWithAnchor(ctx context.Context, req *v14.ListSameAuthorStoryWithAnchorRequest, opts ...http.CallOption) (rsp *v14.ListSameAuthorStoryWithAnchorResponse, err error)
	ListStoryReactionMadeUsers(ctx context.Context, req *v16.ListReactionMadeUsersRequest, opts ...http.CallOption) (rsp *v16.ListReactionMadeUsersResponse, err error)
	ListTrendingPortals(ctx context.Context, req *v17.ListTrendingPortalsRequest, opts ...http.CallOption) (rsp *v17.ListTrendingPortalsResponse, err error)
	ListTurtleSoupStoryConditionTemplates(ctx context.Context, req *v14.ListTurtleSoupStoryConditionTemplatesRequest, opts ...http.CallOption) (rsp *v14.ListTurtleSoupStoryConditionTemplatesResponse, err error)
	ListUnlockedStory(ctx context.Context, req *v14.ListUnlockedStoryRequest, opts ...http.CallOption) (rsp *v14.ListUnlockedStoryResponse, err error)
	ListUnmuteStoryConditionTemplates(ctx context.Context, req *v14.ListUnmuteStoryConditionTemplatesRequest, opts ...http.CallOption) (rsp *v14.ListUnmuteStoryConditionTemplatesResponse, err error)
	ListUserCreatedPortalsWithTimeRange(ctx context.Context, req *v17.ListUserCreatedPortalsWithTimeRangeRequest, opts ...http.CallOption) (rsp *v17.ListUserCreatedPortalsWithTimeRangeResponse, err error)
	ListUserReactedItems(ctx context.Context, req *v12.ListUserReactedItemsRequest, opts ...http.CallOption) (rsp *v12.ListUserReactedItemsResponse, err error)
	ListUsersByConsumptionStatus(ctx context.Context, req *v13.ListUsersByConsumptionStatusRequest, opts ...http.CallOption) (rsp *v13.ListUsersByConsumptionStatusResponse, err error)
	ManualGenerateAreaEmoji(ctx context.Context, req *v2.ManualGenerateAreaEmojiRequest, opts ...http.CallOption) (rsp *v2.ManualGenerateAreaEmojiResponse, err error)
	MusicSearch(ctx context.Context, req *v1.SearchRequest, opts ...http.CallOption) (rsp *v1.SearchResponse, err error)
	RemoveItemReaction(ctx context.Context, req *v12.RemoveItemReactionRequest, opts ...http.CallOption) (rsp *v12.RemoveItemReactionResponse, err error)
	RemoveMomentRelation(ctx context.Context, req *v17.RemoveMomentRelationRequest, opts ...http.CallOption) (rsp *v17.RemoveMomentRelationResponse, err error)
	ReportActivityRead(ctx context.Context, req *v13.ReportActivityReadRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ReportHauntShow(ctx context.Context, req *v2.ReportHauntShowRequest, opts ...http.CallOption) (rsp *v2.ReportHauntShowResponse, err error)
	ReportRead(ctx context.Context, req *v17.ReportReadRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	ReportShareStat(ctx context.Context, req *ReportShareStatRequest, opts ...http.CallOption) (rsp *ReportShareStatResponse, err error)
	SendHauntCaptureVideo(ctx context.Context, req *v2.SendHauntCaptureVideoRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	SendMessage(ctx context.Context, req *v2.SendMessageRequest, opts ...http.CallOption) (rsp *v2.SendMessageResponse, err error)
	SendMomentInvite(ctx context.Context, req *v17.SendMomentInviteRequest, opts ...http.CallOption) (rsp *v17.SendMomentInviteResponse, err error)
	TopHideSticker(ctx context.Context, req *v15.TopCollectedStickerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	TopStory(ctx context.Context, req *v14.TopStoryRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UnCollectHideSticker(ctx context.Context, req *v15.UnCollectHideStickerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UnTopHideSticker(ctx context.Context, req *v15.UnTopCollectedStickerRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UnlikeCommentOrReply(ctx context.Context, req *v11.UnlikeCommentOrReplyRequest, opts ...http.CallOption) (rsp *emptypb.Empty, err error)
	UpdateStory(ctx context.Context, req *v14.UpdateStoryRequest, opts ...http.CallOption) (rsp *v14.UpdateStoryResponse, err error)
}

type ItemsHTTPClientImpl struct {
	cc *http.Client
}

func NewItemsHTTPClient(client *http.Client) ItemsHTTPClient {
	return &ItemsHTTPClientImpl{client}
}

func (c *ItemsHTTPClientImpl) AddCaptureBooIntoMyAssist(ctx context.Context, in *v2.AddCaptureBooIntoMyAssistRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v2/items/story/add_capture_boo_into_my_assist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsAddCaptureBooIntoMyAssist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) AddCapturedBooInToCollectedStickers(ctx context.Context, in *v2.AddCapturedBooInToCollectedStickersRequest, opts ...http.CallOption) (*v2.AddCapturedBooInToCollectedStickersResponse, error) {
	var out v2.AddCapturedBooInToCollectedStickersResponse
	pattern := "/v2/items/story/add_captured_boo_in_to_collected_stickers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsAddCapturedBooInToCollectedStickers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) Asr(ctx context.Context, in *AsrRequest, opts ...http.CallOption) (*AsrResponse, error) {
	var out AsrResponse
	pattern := "/v1/items/asr"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsAsr))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) AutoGenerateAreaEmoji(ctx context.Context, in *v2.AutoGenerateAreaEmojiRequest, opts ...http.CallOption) (*v2.AutoGenerateAreaEmojiResponse, error) {
	var out v2.AutoGenerateAreaEmojiResponse
	pattern := "/v2/items/story/auto_generate_area_emoji"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsAutoGenerateAreaEmoji))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) BatchGetItemSummaries(ctx context.Context, in *BatchGetItemSummariesRequest, opts ...http.CallOption) (*BatchGetItemSummariesResponse, error) {
	var out BatchGetItemSummariesResponse
	pattern := "/v1/items/batch_get_item_summaries"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsBatchGetItemSummaries))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CheckHauntImage(ctx context.Context, in *v2.ImageCheckRequest, opts ...http.CallOption) (*v2.ImageCheckResponse, error) {
	var out v2.ImageCheckResponse
	pattern := "/v2/items/story/check_haunt_image"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCheckHauntImage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CollectHideSticker(ctx context.Context, in *v15.CollectHideStickerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v2/items/story/collect_hide_sticker"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCollectHideSticker))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeBasePlayStory(ctx context.Context, in *v14.ConsumeBasePlayStoryRequest, opts ...http.CallOption) (*v14.ConsumeBasePlayStoryResponse, error) {
	var out v14.ConsumeBasePlayStoryResponse
	pattern := "/v1/items/story/consume_base_play_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeBasePlayStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeCapsuleStory(ctx context.Context, in *v14.ConsumeCapsuleStoryRequest, opts ...http.CallOption) (*v14.ConsumeCapsuleStoryResponse, error) {
	var out v14.ConsumeCapsuleStoryResponse
	pattern := "/v1/items/story/consume_capsule_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeCapsuleStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeChatProxy(ctx context.Context, in *v2.ConsumeChatProxyRequestV2, opts ...http.CallOption) (*v2.ConsumeChatProxyResponseV2, error) {
	var out v2.ConsumeChatProxyResponseV2
	pattern := "/v2/items/story/consume_chatproxy"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeChatProxy))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeExchangeImageStory(ctx context.Context, in *v14.ConsumeExchangeImageStoryRequest, opts ...http.CallOption) (*v14.ConsumeExchangeImageStoryResponse, error) {
	var out v14.ConsumeExchangeImageStoryResponse
	pattern := "/v1/items/story/consume_exchange_image_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeExchangeImageStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeHauntStory(ctx context.Context, in *v2.ConsumeHauntStoryRequest, opts ...http.CallOption) (*v2.ConsumeHauntStoryResponse, error) {
	var out v2.ConsumeHauntStoryResponse
	pattern := "/v2/items/story/consume_haunt_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeHauntStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeHideStory(ctx context.Context, in *v2.ConsumeHideStoryRequest, opts ...http.CallOption) (*v2.ConsumeHideStoryResponse, error) {
	var out v2.ConsumeHideStoryResponse
	pattern := "/v2/items/story/consume_hide_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeHideStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeNowShotStory(ctx context.Context, in *v14.ConsumeNowShotStoryRequest, opts ...http.CallOption) (*v14.ConsumeNowShotStoryResponse, error) {
	var out v14.ConsumeNowShotStoryResponse
	pattern := "/v1/items/story/consume_now_shot_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeNowShotStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeNowShotStoryV2(ctx context.Context, in *v2.ConsumeNowShotStoryRequestV2, opts ...http.CallOption) (*v2.StoryDetailResponseV2, error) {
	var out v2.StoryDetailResponseV2
	pattern := "/v2/items/story/consume_now_shot_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeNowShotStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumePinStory(ctx context.Context, in *v2.ConsumePinStoryRequest, opts ...http.CallOption) (*v2.ConsumePinStoryResponse, error) {
	var out v2.ConsumePinStoryResponse
	pattern := "/v2/items/story/consume_pin_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumePinStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeRoastedStory(ctx context.Context, in *v14.ConsumeRoastedStoryRequest, opts ...http.CallOption) (*v14.ConsumeRoastedStoryResponse, error) {
	var out v14.ConsumeRoastedStoryResponse
	pattern := "/v1/items/story/consume_roasted_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeRoastedStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeRoastedStoryV2(ctx context.Context, in *v2.ConsumeRoastedStoryRequestV2, opts ...http.CallOption) (*v2.ConsumeRoastedStoryResponseV2, error) {
	var out v2.ConsumeRoastedStoryResponseV2
	pattern := "/v2/items/story/consume_roasted_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeRoastedStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeRoastedTopic(ctx context.Context, in *v14.ConsumeRoastedTopicRequest, opts ...http.CallOption) (*v14.ConsumeRoastedTopicResponse, error) {
	var out v14.ConsumeRoastedTopicResponse
	pattern := "/v1/items/story/consume_roasted_topic"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeRoastedTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeTurtleSoupStory(ctx context.Context, in *v14.ConsumeTurtleSoupStoryRequest, opts ...http.CallOption) (*v14.ConsumeTurtleSoupStoryResponse, error) {
	var out v14.ConsumeTurtleSoupStoryResponse
	pattern := "/v1/items/story/consume_turtle_soup_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeTurtleSoupStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeUnmuteStory(ctx context.Context, in *v14.ConsumeUnmuteStoryRequest, opts ...http.CallOption) (*v14.ConsumeUnmuteStoryResponse, error) {
	var out v14.ConsumeUnmuteStoryResponse
	pattern := "/v1/items/story/consume_unmute_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeUnmuteStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeWassupStoryV2(ctx context.Context, in *v2.ConsumeWassupStoryRequest, opts ...http.CallOption) (*v2.ConsumeWassupStoryResponse, error) {
	var out v2.ConsumeWassupStoryResponse
	pattern := "/v2/items/story/consume_wassup_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeWassupStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ConsumeWhoStoryV2(ctx context.Context, in *v2.ConsumeWhoStoryRequestV2, opts ...http.CallOption) (*v2.ConsumeWhoStoryResponseV2, error) {
	var out v2.ConsumeWhoStoryResponseV2
	pattern := "/v2/items/story/consume_who_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsConsumeWhoStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CopilotCapsuleStory(ctx context.Context, in *v14.CopilotCapsuleStoryRequest, opts ...http.CallOption) (*v14.CopilotCapsuleStoryResponse, error) {
	var out v14.CopilotCapsuleStoryResponse
	pattern := "/v1/items/story/copilot_capsule_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCopilotCapsuleStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateBasePlayStory(ctx context.Context, in *v14.CreateBasePlayStoryRequest, opts ...http.CallOption) (*v14.CreateBasePlayStoryResponse, error) {
	var out v14.CreateBasePlayStoryResponse
	pattern := "/v1/items/story/create_base_play_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateBasePlayStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateCapsuleStory(ctx context.Context, in *v14.CreateCapsuleStoryRequest, opts ...http.CallOption) (*v14.CreateCapsuleStoryResponse, error) {
	var out v14.CreateCapsuleStoryResponse
	pattern := "/v1/items/story/create_capsule_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateCapsuleStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateChatProxyStory(ctx context.Context, in *v2.CreateChatProxyStoryRequestV2, opts ...http.CallOption) (*v2.CreateChatProxyStoryResponseV2, error) {
	var out v2.CreateChatProxyStoryResponseV2
	pattern := "/v2/items/story/create_chatproxy_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateChatProxyStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateComment(ctx context.Context, in *v11.CreateCommentRequest, opts ...http.CallOption) (*v11.CreateCommentResponse, error) {
	var out v11.CreateCommentResponse
	pattern := "/v1/items/comments/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateComment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateExchangeImageStory(ctx context.Context, in *v14.CreateExchangeImageStoryRequest, opts ...http.CallOption) (*v14.CreateExchangeImageStoryResponse, error) {
	var out v14.CreateExchangeImageStoryResponse
	pattern := "/v1/items/story/create_exchange_image_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateExchangeImageStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateExchangeImageStoryV2(ctx context.Context, in *v2.CreateExchangeImageStoryRequestV2, opts ...http.CallOption) (*v2.CreateExchangeImageStoryResponseV2, error) {
	var out v2.CreateExchangeImageStoryResponseV2
	pattern := "/v2/items/story/create_exchange_image_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateExchangeImageStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateHauntStoryV2(ctx context.Context, in *v2.CreateHauntStoryRequest, opts ...http.CallOption) (*v2.CreateHauntStoryResponse, error) {
	var out v2.CreateHauntStoryResponse
	pattern := "/v2/items/story/create_haunt_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateHauntStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateHideStoryV2(ctx context.Context, in *v2.CreateHideStoryRequestV2, opts ...http.CallOption) (*v2.CreateHideStoryResponseV2, error) {
	var out v2.CreateHideStoryResponseV2
	pattern := "/v2/items/story/create_hide_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateHideStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateItem(ctx context.Context, in *CreateItemRequest, opts ...http.CallOption) (*CreateItemResponse, error) {
	var out CreateItemResponse
	pattern := "/v1/items/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateItem))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateItemReaction(ctx context.Context, in *v12.CreateItemReactionRequest, opts ...http.CallOption) (*v12.CreateItemReactionResponse, error) {
	var out v12.CreateItemReactionResponse
	pattern := "/v1/items/reaction/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateItemReaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateMoment(ctx context.Context, in *v17.CreateMomentRequest, opts ...http.CallOption) (*v17.CreateMomentResponse, error) {
	var out v17.CreateMomentResponse
	pattern := "/v1/items/portal/create_moment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateMoment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateMomentRelation(ctx context.Context, in *v17.CreateMomentRelationRequest, opts ...http.CallOption) (*v17.CreateMomentRelationResponse, error) {
	var out v17.CreateMomentRelationResponse
	pattern := "/v1/items/portal/create_moment_relation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateMomentRelation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateNowShotStory(ctx context.Context, in *v14.CreateNowShotStoryRequest, opts ...http.CallOption) (*v14.CreateNowShotStoryResponse, error) {
	var out v14.CreateNowShotStoryResponse
	pattern := "/v1/items/story/create_now_shot_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateNowShotStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateNowShotStoryV2(ctx context.Context, in *v2.CreateNowShotStoryRequestV2, opts ...http.CallOption) (*v2.StoryDetailResponseV2, error) {
	var out v2.StoryDetailResponseV2
	pattern := "/v2/items/story/create_now_shot_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateNowShotStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreatePinStory(ctx context.Context, in *v2.CreatePinStoryRequest, opts ...http.CallOption) (*v2.CreatePinStoryResponse, error) {
	var out v2.CreatePinStoryResponse
	pattern := "/v2/items/story/create_pin_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreatePinStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateRoastedStory(ctx context.Context, in *v14.CreateRoastedStoryRequest, opts ...http.CallOption) (*v14.CreateRoastedStoryResponse, error) {
	var out v14.CreateRoastedStoryResponse
	pattern := "/v1/items/story/create_roasted_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateRoastedStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateStoryReaction(ctx context.Context, in *v16.CreateReactionRequest, opts ...http.CallOption) (*v16.CreateReactionResponse, error) {
	var out v16.CreateReactionResponse
	pattern := "/v1/items/story/create_story_reaction"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateStoryReaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateTurtleSoupStory(ctx context.Context, in *v14.CreateTurtleSoupStoryRequest, opts ...http.CallOption) (*v14.CreateTurtleSoupStoryResponse, error) {
	var out v14.CreateTurtleSoupStoryResponse
	pattern := "/v1/items/story/create_turtle_soup_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateTurtleSoupStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateTurtleSoupStoryV2(ctx context.Context, in *v2.CreateTurtleSoupStoryRequestV2, opts ...http.CallOption) (*v2.CreateTurtleSoupStoryResponseV2, error) {
	var out v2.CreateTurtleSoupStoryResponseV2
	pattern := "/v2/items/story/create_turtle_soup_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateTurtleSoupStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateUnmuteStory(ctx context.Context, in *v14.CreateUnmuteStoryRequest, opts ...http.CallOption) (*v14.CreateUnmuteStoryResponse, error) {
	var out v14.CreateUnmuteStoryResponse
	pattern := "/v1/items/story/create_unmute_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateUnmuteStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateUnmuteStoryV2(ctx context.Context, in *v2.CreateUnmuteStoryRequestV2, opts ...http.CallOption) (*v2.CreateUnmuteStoryResponseV2, error) {
	var out v2.CreateUnmuteStoryResponseV2
	pattern := "/v2/items/story/create_unmute_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateUnmuteStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateWassupStoryV2(ctx context.Context, in *v2.CreateWassupStoryRequest, opts ...http.CallOption) (*v2.CreateWassupStoryResponse, error) {
	var out v2.CreateWassupStoryResponse
	pattern := "/v2/items/story/create_wassup_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateWassupStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) CreateWhoStoryV2(ctx context.Context, in *v2.CreateWhoStoryRequestV2, opts ...http.CallOption) (*v2.CreateWhoStoryResponseV2, error) {
	var out v2.CreateWhoStoryResponseV2
	pattern := "/v2/items/story/create_who_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsCreateWhoStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) DeleteCommentOrReply(ctx context.Context, in *v11.DeleteCommentOrReplyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/comments/delete_comment_or_reply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsDeleteCommentOrReply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) DeleteMoment(ctx context.Context, in *v17.DeleteMomentRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/portal/delete_moment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsDeleteMoment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) DeleteStory(ctx context.Context, in *v14.DeleteStoryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/story/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsDeleteStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) DeleteStoryReaction(ctx context.Context, in *v16.DeleteReactionRequest, opts ...http.CallOption) (*v16.DeleteReactionResponse, error) {
	var out v16.DeleteReactionResponse
	pattern := "/v1/items/story/delete_story_reaction"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsDeleteStoryReaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GenerateHideImageMask(ctx context.Context, in *v2.GenerateHideImageMaskRequest, opts ...http.CallOption) (*v2.GenerateHideImageMaskResponse, error) {
	var out v2.GenerateHideImageMaskResponse
	pattern := "/v2/items/story/generate_hide_image_mask"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGenerateHideImageMask))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetActivityUnreadCount(ctx context.Context, in *v13.GetActivityUnreadCountRequest, opts ...http.CallOption) (*v13.GetActivityUnreadCountResponse, error) {
	var out v13.GetActivityUnreadCountResponse
	pattern := "/v1/items/story/activity/get_unread_count"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetActivityUnreadCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetChatProxyNextTopic(ctx context.Context, in *v2.GetChatProxyNextTopicRequestV2, opts ...http.CallOption) (*v2.GetChatProxyNextTopicResponseV2, error) {
	var out v2.GetChatProxyNextTopicResponseV2
	pattern := "/v2/items/story/get_chatproxy_next_topic"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetChatProxyNextTopic))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetMoment(ctx context.Context, in *v17.GetMomentRequest, opts ...http.CallOption) (*v17.GetMomentResponse, error) {
	var out v17.GetMomentResponse
	pattern := "/v1/items/portal/get_moment"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetMoment))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetPortal(ctx context.Context, in *v17.GetPortalRequest, opts ...http.CallOption) (*v17.GetPortalResponse, error) {
	var out v17.GetPortalResponse
	pattern := "/v1/items/portal/get_portal"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetPortal))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetRoastedTopics(ctx context.Context, in *v14.GetRoastedTopicsRequest, opts ...http.CallOption) (*v14.GetRoastedTopicsResponse, error) {
	var out v14.GetRoastedTopicsResponse
	pattern := "/v1/items/story/get_roasted_topics"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetRoastedTopics))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetStoryDetail(ctx context.Context, in *v14.GetStoryDetailRequest, opts ...http.CallOption) (*v14.GetStoryDetailResponse, error) {
	var out v14.GetStoryDetailResponse
	pattern := "/v1/items/story/get_story_detail"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetStoryDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetUserCreatedPortalsInfo(ctx context.Context, in *v17.GetUserCreatedPortalsInfoRequest, opts ...http.CallOption) (*v17.GetUserCreatedPortalsInfoResponse, error) {
	var out v17.GetUserCreatedPortalsInfoResponse
	pattern := "/v1/items/portal/get_user_created_portals_info"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetUserCreatedPortalsInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetWassupGreetings(ctx context.Context, in *v2.GetWassupGreetingsRequest, opts ...http.CallOption) (*v2.GetWassupGreetingsResponse, error) {
	var out v2.GetWassupGreetingsResponse
	pattern := "/v2/items/story/get_wassup_greetings"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetWassupGreetings))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) GetWassupNextQuestion(ctx context.Context, in *v2.GetWassupNextQuestionRequest, opts ...http.CallOption) (*v2.GetWassupNextQuestionResponse, error) {
	var out v2.GetWassupNextQuestionResponse
	pattern := "/v2/items/story/get_wassup_next_question"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsGetWassupNextQuestion))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) HomePageTimeline(ctx context.Context, in *HomePageTimelineRequest, opts ...http.CallOption) (*HomePageTimelineResponse, error) {
	var out HomePageTimelineResponse
	pattern := "/v1/items/home_page_timeline"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsHomePageTimeline))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) LikeCommentOrReply(ctx context.Context, in *v11.LikeCommentOrReplyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/comments/like_comment_or_reply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsLikeCommentOrReply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListActivities(ctx context.Context, in *v13.ListActivitiesRequest, opts ...http.CallOption) (*v13.ListActivitiesResponse, error) {
	var out v13.ListActivitiesResponse
	pattern := "/v1/items/story/activity/list_activities"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListActivities))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListCommentReplies(ctx context.Context, in *v11.ListCommentRepliesRequest, opts ...http.CallOption) (*v11.ListCommentRepliesResponse, error) {
	var out v11.ListCommentRepliesResponse
	pattern := "/v1/items/comments/list_replies"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListCommentReplies))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListComments(ctx context.Context, in *v11.ListCommentsRequest, opts ...http.CallOption) (*v11.ListCommentsResponse, error) {
	var out v11.ListCommentsResponse
	pattern := "/v1/items/comments/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListComments))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListCommonConditionTemplates(ctx context.Context, in *v14.ListCommonStoryConditionTemplatesRequest, opts ...http.CallOption) (*v14.ListCommonStoryConditionTemplatesResponse, error) {
	var out v14.ListCommonStoryConditionTemplatesResponse
	pattern := "/v1/items/story/list_common_condition_templates"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListCommonConditionTemplates))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListCouldAppendMomentStories(ctx context.Context, in *v17.ListCouldAppendMomentStoriesRequest, opts ...http.CallOption) (*v17.ListCouldAppendMomentStoriesResponse, error) {
	var out v17.ListCouldAppendMomentStoriesResponse
	pattern := "/v1/items/portal/list_could_append_moment_stories"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListCouldAppendMomentStories))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListCreatorStory(ctx context.Context, in *v14.ListCreatorStoryRequest, opts ...http.CallOption) (*v14.ListCreatorStoryResponse, error) {
	var out v14.ListCreatorStoryResponse
	pattern := "/v1/items/story/list_creator_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListCreatorStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListCreatorStoryV2(ctx context.Context, in *v2.ListCreatorStoryRequestV2, opts ...http.CallOption) (*v2.ListCreatorStoryResponseV2, error) {
	var out v2.ListCreatorStoryResponseV2
	pattern := "/v2/items/story/list_creator_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListCreatorStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListExchangeImageStoryConditionTemplates(ctx context.Context, in *v14.ListExchangeImageStoryConditionTemplatesRequest, opts ...http.CallOption) (*v14.ListExchangeImageStoryConditionTemplatesResponse, error) {
	var out v14.ListExchangeImageStoryConditionTemplatesResponse
	pattern := "/v1/items/story/list_exchange_images_condition_templetes"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListExchangeImageStoryConditionTemplates))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListFollowingCreatorStoryV2(ctx context.Context, in *v2.ListFollowingCreatorStoryRequestV2, opts ...http.CallOption) (*v2.ListFollowingCreatorStoryResponseV2, error) {
	var out v2.ListFollowingCreatorStoryResponseV2
	pattern := "/v2/items/story/list_following_creator_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListFollowingCreatorStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListHauntBooAssist(ctx context.Context, in *v2.ListHauntBooAssistRequest, opts ...http.CallOption) (*v2.ListHauntBooAssistResponse, error) {
	var out v2.ListHauntBooAssistResponse
	pattern := "/v2/items/story/list_haunt_boo_assist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListHauntBooAssist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListHauntQuestions(ctx context.Context, in *v2.ListHauntQuestionsRequest, opts ...http.CallOption) (*v2.ListHauntQuestionsResponse, error) {
	var out v2.ListHauntQuestionsResponse
	pattern := "/v2/items/story/list_haunt_questions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListHauntQuestions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListHauntRandomAvatars(ctx context.Context, in *v2.ListHauntRandomAvatarsRequest, opts ...http.CallOption) (*v2.ListHauntRandomAvatarsResponse, error) {
	var out v2.ListHauntRandomAvatarsResponse
	pattern := "/v2/items/story/list_haunt_random_avatars"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListHauntRandomAvatars))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListHomePageStoryV2(ctx context.Context, in *v2.ListHomePageStoryRequestV2, opts ...http.CallOption) (*v2.ListHomePageStoryResponseV2, error) {
	var out v2.ListHomePageStoryResponseV2
	pattern := "/v2/items/story/list_home_page_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListHomePageStoryV2))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListMomentViewers(ctx context.Context, in *v17.ListMomentViewersRequest, opts ...http.CallOption) (*v17.ListMomentViewersResponse, error) {
	var out v17.ListMomentViewersResponse
	pattern := "/v1/items/portal/list_moment_viewers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListMomentViewers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListMyCollectedHideStickers(ctx context.Context, in *v15.ListMyCollectedStickersRequest, opts ...http.CallOption) (*v15.ListMyCollectedStickersResponse, error) {
	var out v15.ListMyCollectedStickersResponse
	pattern := "/v2/items/story/list_my_collected_hide_stickers"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListMyCollectedHideStickers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListMyPortals(ctx context.Context, in *v17.ListMyPortalsRequest, opts ...http.CallOption) (*v17.ListMyPortalsResponse, error) {
	var out v17.ListMyPortalsResponse
	pattern := "/v1/items/portal/list_my_portals"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListMyPortals))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListSameAuthorStoryWithAnchor(ctx context.Context, in *v14.ListSameAuthorStoryWithAnchorRequest, opts ...http.CallOption) (*v14.ListSameAuthorStoryWithAnchorResponse, error) {
	var out v14.ListSameAuthorStoryWithAnchorResponse
	pattern := "/v1/items/story/list_same_author_story_with_anchor"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListSameAuthorStoryWithAnchor))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListStoryReactionMadeUsers(ctx context.Context, in *v16.ListReactionMadeUsersRequest, opts ...http.CallOption) (*v16.ListReactionMadeUsersResponse, error) {
	var out v16.ListReactionMadeUsersResponse
	pattern := "/v1/items/story/list_reaction_made_users"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListStoryReactionMadeUsers))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListTrendingPortals(ctx context.Context, in *v17.ListTrendingPortalsRequest, opts ...http.CallOption) (*v17.ListTrendingPortalsResponse, error) {
	var out v17.ListTrendingPortalsResponse
	pattern := "/v1/items/portal/trending_portals"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListTrendingPortals))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListTurtleSoupStoryConditionTemplates(ctx context.Context, in *v14.ListTurtleSoupStoryConditionTemplatesRequest, opts ...http.CallOption) (*v14.ListTurtleSoupStoryConditionTemplatesResponse, error) {
	var out v14.ListTurtleSoupStoryConditionTemplatesResponse
	pattern := "/v1/items/story/list_turtle_soup_condition_templates"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListTurtleSoupStoryConditionTemplates))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListUnlockedStory(ctx context.Context, in *v14.ListUnlockedStoryRequest, opts ...http.CallOption) (*v14.ListUnlockedStoryResponse, error) {
	var out v14.ListUnlockedStoryResponse
	pattern := "/v1/items/story/list_unlocked_story"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListUnlockedStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListUnmuteStoryConditionTemplates(ctx context.Context, in *v14.ListUnmuteStoryConditionTemplatesRequest, opts ...http.CallOption) (*v14.ListUnmuteStoryConditionTemplatesResponse, error) {
	var out v14.ListUnmuteStoryConditionTemplatesResponse
	pattern := "/v1/items/story/list_unmute_condition_templetes"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListUnmuteStoryConditionTemplates))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListUserCreatedPortalsWithTimeRange(ctx context.Context, in *v17.ListUserCreatedPortalsWithTimeRangeRequest, opts ...http.CallOption) (*v17.ListUserCreatedPortalsWithTimeRangeResponse, error) {
	var out v17.ListUserCreatedPortalsWithTimeRangeResponse
	pattern := "/v1/items/portal/list_user_created_portals_with_time_range"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListUserCreatedPortalsWithTimeRange))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListUserReactedItems(ctx context.Context, in *v12.ListUserReactedItemsRequest, opts ...http.CallOption) (*v12.ListUserReactedItemsResponse, error) {
	var out v12.ListUserReactedItemsResponse
	pattern := "/v1/items/reaction/list_user_reacted"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListUserReactedItems))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ListUsersByConsumptionStatus(ctx context.Context, in *v13.ListUsersByConsumptionStatusRequest, opts ...http.CallOption) (*v13.ListUsersByConsumptionStatusResponse, error) {
	var out v13.ListUsersByConsumptionStatusResponse
	pattern := "/v1/items/activity/list_users_by_consumption_status"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsListUsersByConsumptionStatus))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ManualGenerateAreaEmoji(ctx context.Context, in *v2.ManualGenerateAreaEmojiRequest, opts ...http.CallOption) (*v2.ManualGenerateAreaEmojiResponse, error) {
	var out v2.ManualGenerateAreaEmojiResponse
	pattern := "/v2/items/story/manual_generate_area_emoji"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsManualGenerateAreaEmoji))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) MusicSearch(ctx context.Context, in *v1.SearchRequest, opts ...http.CallOption) (*v1.SearchResponse, error) {
	var out v1.SearchResponse
	pattern := "/v1/items/music_search"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsMusicSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) RemoveItemReaction(ctx context.Context, in *v12.RemoveItemReactionRequest, opts ...http.CallOption) (*v12.RemoveItemReactionResponse, error) {
	var out v12.RemoveItemReactionResponse
	pattern := "/v1/items/reaction/remove"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsRemoveItemReaction))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) RemoveMomentRelation(ctx context.Context, in *v17.RemoveMomentRelationRequest, opts ...http.CallOption) (*v17.RemoveMomentRelationResponse, error) {
	var out v17.RemoveMomentRelationResponse
	pattern := "/v1/items/portal/remove_moment_relation"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsRemoveMomentRelation))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ReportActivityRead(ctx context.Context, in *v13.ReportActivityReadRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/story/activity/report_read"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsReportActivityRead))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ReportHauntShow(ctx context.Context, in *v2.ReportHauntShowRequest, opts ...http.CallOption) (*v2.ReportHauntShowResponse, error) {
	var out v2.ReportHauntShowResponse
	pattern := "/v2/items/story/report_haunt_show"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsReportHauntShow))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ReportRead(ctx context.Context, in *v17.ReportReadRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/portal/report_read"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsReportRead))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) ReportShareStat(ctx context.Context, in *ReportShareStatRequest, opts ...http.CallOption) (*ReportShareStatResponse, error) {
	var out ReportShareStatResponse
	pattern := "/v1/items/story/report_share_stats"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsReportShareStat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) SendHauntCaptureVideo(ctx context.Context, in *v2.SendHauntCaptureVideoRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v2/items/story/send_haunt_capture_video"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsSendHauntCaptureVideo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) SendMessage(ctx context.Context, in *v2.SendMessageRequest, opts ...http.CallOption) (*v2.SendMessageResponse, error) {
	var out v2.SendMessageResponse
	pattern := "/v2/items/story/send_message"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsSendMessage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) SendMomentInvite(ctx context.Context, in *v17.SendMomentInviteRequest, opts ...http.CallOption) (*v17.SendMomentInviteResponse, error) {
	var out v17.SendMomentInviteResponse
	pattern := "/v1/items/portal/send_moment_invite"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsSendMomentInvite))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) TopHideSticker(ctx context.Context, in *v15.TopCollectedStickerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v2/items/story/top_hide_sticker"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsTopHideSticker))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) TopStory(ctx context.Context, in *v14.TopStoryRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/story/top"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsTopStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) UnCollectHideSticker(ctx context.Context, in *v15.UnCollectHideStickerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v2/items/story/uncollect_hide_sticker"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsUnCollectHideSticker))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) UnTopHideSticker(ctx context.Context, in *v15.UnTopCollectedStickerRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v2/items/story/untop_hide_sticker"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsUnTopHideSticker))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) UnlikeCommentOrReply(ctx context.Context, in *v11.UnlikeCommentOrReplyRequest, opts ...http.CallOption) (*emptypb.Empty, error) {
	var out emptypb.Empty
	pattern := "/v1/items/comments/unlike_comment_or_reply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsUnlikeCommentOrReply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ItemsHTTPClientImpl) UpdateStory(ctx context.Context, in *v14.UpdateStoryRequest, opts ...http.CallOption) (*v14.UpdateStoryResponse, error) {
	var out v14.UpdateStoryResponse
	pattern := "/v1/items/story/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationItemsUpdateStory))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
