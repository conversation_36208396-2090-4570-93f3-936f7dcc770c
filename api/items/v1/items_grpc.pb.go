// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.3
// source: api/items/v1/items.proto

package v1

import (
	v11 "boson/api/items/comments/v1"
	v1 "boson/api/items/music/search/v1"
	v17 "boson/api/items/portal/v1"
	v12 "boson/api/items/reaction/v1"
	v13 "boson/api/items/story/activity/v1"
	v15 "boson/api/items/story/hide_stickers/v1"
	v16 "boson/api/items/story/reaction/v1"
	v14 "boson/api/items/story/v1"
	v2 "boson/api/items/story/v2"
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Items_MusicSearch_FullMethodName                              = "/api.items.v1.Items/MusicSearch"
	Items_CreateComment_FullMethodName                            = "/api.items.v1.Items/CreateComment"
	Items_ListComments_FullMethodName                             = "/api.items.v1.Items/ListComments"
	Items_ListCommentReplies_FullMethodName                       = "/api.items.v1.Items/ListCommentReplies"
	Items_DeleteCommentOrReply_FullMethodName                     = "/api.items.v1.Items/DeleteCommentOrReply"
	Items_LikeCommentOrReply_FullMethodName                       = "/api.items.v1.Items/LikeCommentOrReply"
	Items_UnlikeCommentOrReply_FullMethodName                     = "/api.items.v1.Items/UnlikeCommentOrReply"
	Items_Asr_FullMethodName                                      = "/api.items.v1.Items/Asr"
	Items_HomePageTimeline_FullMethodName                         = "/api.items.v1.Items/HomePageTimeline"
	Items_CreateItem_FullMethodName                               = "/api.items.v1.Items/CreateItem"
	Items_BatchGetItemSummaries_FullMethodName                    = "/api.items.v1.Items/BatchGetItemSummaries"
	Items_CreateItemReaction_FullMethodName                       = "/api.items.v1.Items/CreateItemReaction"
	Items_RemoveItemReaction_FullMethodName                       = "/api.items.v1.Items/RemoveItemReaction"
	Items_ListUserReactedItems_FullMethodName                     = "/api.items.v1.Items/ListUserReactedItems"
	Items_ListUsersByConsumptionStatus_FullMethodName             = "/api.items.v1.Items/ListUsersByConsumptionStatus"
	Items_ListActivities_FullMethodName                           = "/api.items.v1.Items/ListActivities"
	Items_GetActivityUnreadCount_FullMethodName                   = "/api.items.v1.Items/GetActivityUnreadCount"
	Items_ReportActivityRead_FullMethodName                       = "/api.items.v1.Items/ReportActivityRead"
	Items_DeleteStory_FullMethodName                              = "/api.items.v1.Items/DeleteStory"
	Items_UpdateStory_FullMethodName                              = "/api.items.v1.Items/UpdateStory"
	Items_ListSameAuthorStoryWithAnchor_FullMethodName            = "/api.items.v1.Items/ListSameAuthorStoryWithAnchor"
	Items_TopStory_FullMethodName                                 = "/api.items.v1.Items/TopStory"
	Items_ListFollowingCreatorStoryV2_FullMethodName              = "/api.items.v1.Items/ListFollowingCreatorStoryV2"
	Items_ListCommonConditionTemplates_FullMethodName             = "/api.items.v1.Items/ListCommonConditionTemplates"
	Items_ListTurtleSoupStoryConditionTemplates_FullMethodName    = "/api.items.v1.Items/ListTurtleSoupStoryConditionTemplates"
	Items_ListExchangeImageStoryConditionTemplates_FullMethodName = "/api.items.v1.Items/ListExchangeImageStoryConditionTemplates"
	Items_ListUnmuteStoryConditionTemplates_FullMethodName        = "/api.items.v1.Items/ListUnmuteStoryConditionTemplates"
	Items_CreateExchangeImageStory_FullMethodName                 = "/api.items.v1.Items/CreateExchangeImageStory"
	Items_CreateWhoStoryV2_FullMethodName                         = "/api.items.v1.Items/CreateWhoStoryV2"
	Items_ConsumeWhoStoryV2_FullMethodName                        = "/api.items.v1.Items/ConsumeWhoStoryV2"
	Items_CreateExchangeImageStoryV2_FullMethodName               = "/api.items.v1.Items/CreateExchangeImageStoryV2"
	Items_CreateTurtleSoupStory_FullMethodName                    = "/api.items.v1.Items/CreateTurtleSoupStory"
	Items_CreatePinStory_FullMethodName                           = "/api.items.v1.Items/CreatePinStory"
	Items_ConsumePinStory_FullMethodName                          = "/api.items.v1.Items/ConsumePinStory"
	Items_AutoGenerateAreaEmoji_FullMethodName                    = "/api.items.v1.Items/AutoGenerateAreaEmoji"
	Items_ManualGenerateAreaEmoji_FullMethodName                  = "/api.items.v1.Items/ManualGenerateAreaEmoji"
	Items_CreateHideStoryV2_FullMethodName                        = "/api.items.v1.Items/CreateHideStoryV2"
	Items_GenerateHideImageMask_FullMethodName                    = "/api.items.v1.Items/GenerateHideImageMask"
	Items_CollectHideSticker_FullMethodName                       = "/api.items.v1.Items/CollectHideSticker"
	Items_UnCollectHideSticker_FullMethodName                     = "/api.items.v1.Items/UnCollectHideSticker"
	Items_TopHideSticker_FullMethodName                           = "/api.items.v1.Items/TopHideSticker"
	Items_UnTopHideSticker_FullMethodName                         = "/api.items.v1.Items/UnTopHideSticker"
	Items_ListMyCollectedHideStickers_FullMethodName              = "/api.items.v1.Items/ListMyCollectedHideStickers"
	Items_CreateTurtleSoupStoryV2_FullMethodName                  = "/api.items.v1.Items/CreateTurtleSoupStoryV2"
	Items_GetRoastedTopics_FullMethodName                         = "/api.items.v1.Items/GetRoastedTopics"
	Items_CreateRoastedStory_FullMethodName                       = "/api.items.v1.Items/CreateRoastedStory"
	Items_CreateBasePlayStory_FullMethodName                      = "/api.items.v1.Items/CreateBasePlayStory"
	Items_CreateUnmuteStory_FullMethodName                        = "/api.items.v1.Items/CreateUnmuteStory"
	Items_CreateChatProxyStory_FullMethodName                     = "/api.items.v1.Items/CreateChatProxyStory"
	Items_GetChatProxyNextTopic_FullMethodName                    = "/api.items.v1.Items/GetChatProxyNextTopic"
	Items_ConsumeChatProxy_FullMethodName                         = "/api.items.v1.Items/ConsumeChatProxy"
	Items_CreateUnmuteStoryV2_FullMethodName                      = "/api.items.v1.Items/CreateUnmuteStoryV2"
	Items_CreateNowShotStory_FullMethodName                       = "/api.items.v1.Items/CreateNowShotStory"
	Items_CreateNowShotStoryV2_FullMethodName                     = "/api.items.v1.Items/CreateNowShotStoryV2"
	Items_CreateCapsuleStory_FullMethodName                       = "/api.items.v1.Items/CreateCapsuleStory"
	Items_GetStoryDetail_FullMethodName                           = "/api.items.v1.Items/GetStoryDetail"
	Items_ListHomePageStoryV2_FullMethodName                      = "/api.items.v1.Items/ListHomePageStoryV2"
	Items_ListCreatorStory_FullMethodName                         = "/api.items.v1.Items/ListCreatorStory"
	Items_ListCreatorStoryV2_FullMethodName                       = "/api.items.v1.Items/ListCreatorStoryV2"
	Items_ListUnlockedStory_FullMethodName                        = "/api.items.v1.Items/ListUnlockedStory"
	Items_ConsumeRoastedTopic_FullMethodName                      = "/api.items.v1.Items/ConsumeRoastedTopic"
	Items_ConsumeHideStory_FullMethodName                         = "/api.items.v1.Items/ConsumeHideStory"
	Items_ConsumeRoastedStory_FullMethodName                      = "/api.items.v1.Items/ConsumeRoastedStory"
	Items_ConsumeRoastedStoryV2_FullMethodName                    = "/api.items.v1.Items/ConsumeRoastedStoryV2"
	Items_ConsumeBasePlayStory_FullMethodName                     = "/api.items.v1.Items/ConsumeBasePlayStory"
	Items_ConsumeExchangeImageStory_FullMethodName                = "/api.items.v1.Items/ConsumeExchangeImageStory"
	Items_ConsumeTurtleSoupStory_FullMethodName                   = "/api.items.v1.Items/ConsumeTurtleSoupStory"
	Items_GetWassupGreetings_FullMethodName                       = "/api.items.v1.Items/GetWassupGreetings"
	Items_GetWassupNextQuestion_FullMethodName                    = "/api.items.v1.Items/GetWassupNextQuestion"
	Items_CreateWassupStoryV2_FullMethodName                      = "/api.items.v1.Items/CreateWassupStoryV2"
	Items_ConsumeWassupStoryV2_FullMethodName                     = "/api.items.v1.Items/ConsumeWassupStoryV2"
	Items_SendMessage_FullMethodName                              = "/api.items.v1.Items/SendMessage"
	Items_ReportHauntShow_FullMethodName                          = "/api.items.v1.Items/ReportHauntShow"
	Items_CreateHauntStoryV2_FullMethodName                       = "/api.items.v1.Items/CreateHauntStoryV2"
	Items_ConsumeHauntStory_FullMethodName                        = "/api.items.v1.Items/ConsumeHauntStory"
	Items_SendHauntCaptureVideo_FullMethodName                    = "/api.items.v1.Items/SendHauntCaptureVideo"
	Items_ListHauntRandomAvatars_FullMethodName                   = "/api.items.v1.Items/ListHauntRandomAvatars"
	Items_ListHauntQuestions_FullMethodName                       = "/api.items.v1.Items/ListHauntQuestions"
	Items_ListHauntBooAssist_FullMethodName                       = "/api.items.v1.Items/ListHauntBooAssist"
	Items_AddCaptureBooIntoMyAssist_FullMethodName                = "/api.items.v1.Items/AddCaptureBooIntoMyAssist"
	Items_AddCapturedBooInToCollectedStickers_FullMethodName      = "/api.items.v1.Items/AddCapturedBooInToCollectedStickers"
	Items_CheckHauntImage_FullMethodName                          = "/api.items.v1.Items/CheckHauntImage"
	Items_ConsumeUnmuteStory_FullMethodName                       = "/api.items.v1.Items/ConsumeUnmuteStory"
	Items_ConsumeNowShotStory_FullMethodName                      = "/api.items.v1.Items/ConsumeNowShotStory"
	Items_ConsumeNowShotStoryV2_FullMethodName                    = "/api.items.v1.Items/ConsumeNowShotStoryV2"
	Items_ConsumeCapsuleStory_FullMethodName                      = "/api.items.v1.Items/ConsumeCapsuleStory"
	Items_CopilotCapsuleStory_FullMethodName                      = "/api.items.v1.Items/CopilotCapsuleStory"
	Items_CreateStoryReaction_FullMethodName                      = "/api.items.v1.Items/CreateStoryReaction"
	Items_DeleteStoryReaction_FullMethodName                      = "/api.items.v1.Items/DeleteStoryReaction"
	Items_ListStoryReactionMadeUsers_FullMethodName               = "/api.items.v1.Items/ListStoryReactionMadeUsers"
	Items_ReportShareStat_FullMethodName                          = "/api.items.v1.Items/ReportShareStat"
	Items_CreateMoment_FullMethodName                             = "/api.items.v1.Items/CreateMoment"
	Items_GetUserCreatedPortalsInfo_FullMethodName                = "/api.items.v1.Items/GetUserCreatedPortalsInfo"
	Items_DeleteMoment_FullMethodName                             = "/api.items.v1.Items/DeleteMoment"
	Items_CreateMomentRelation_FullMethodName                     = "/api.items.v1.Items/CreateMomentRelation"
	Items_RemoveMomentRelation_FullMethodName                     = "/api.items.v1.Items/RemoveMomentRelation"
	Items_ListUserCreatedPortalsWithTimeRange_FullMethodName      = "/api.items.v1.Items/ListUserCreatedPortalsWithTimeRange"
	Items_ListMyPortals_FullMethodName                            = "/api.items.v1.Items/ListMyPortals"
	Items_ListCouldAppendMomentStories_FullMethodName             = "/api.items.v1.Items/ListCouldAppendMomentStories"
	Items_ReportRead_FullMethodName                               = "/api.items.v1.Items/ReportRead"
	Items_GetPortal_FullMethodName                                = "/api.items.v1.Items/GetPortal"
	Items_ListMomentViewers_FullMethodName                        = "/api.items.v1.Items/ListMomentViewers"
	Items_ListTrendingPortals_FullMethodName                      = "/api.items.v1.Items/ListTrendingPortals"
	Items_GetMoment_FullMethodName                                = "/api.items.v1.Items/GetMoment"
	Items_SendMomentInvite_FullMethodName                         = "/api.items.v1.Items/SendMomentInvite"
)

// ItemsClient is the client API for Items service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ItemsClient interface {
	// 音乐搜索
	MusicSearch(ctx context.Context, in *v1.SearchRequest, opts ...grpc.CallOption) (*v1.SearchResponse, error)
	// comments 相关 ...
	// 创建评论
	CreateComment(ctx context.Context, in *v11.CreateCommentRequest, opts ...grpc.CallOption) (*v11.CreateCommentResponse, error)
	// 获取 item's 评论列表
	ListComments(ctx context.Context, in *v11.ListCommentsRequest, opts ...grpc.CallOption) (*v11.ListCommentsResponse, error)
	// 获取 item's 评论回复列表
	ListCommentReplies(ctx context.Context, in *v11.ListCommentRepliesRequest, opts ...grpc.CallOption) (*v11.ListCommentRepliesResponse, error)
	// 删除评论
	DeleteCommentOrReply(ctx context.Context, in *v11.DeleteCommentOrReplyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 点赞评论或回复
	LikeCommentOrReply(ctx context.Context, in *v11.LikeCommentOrReplyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 取消点赞评论或回复
	UnlikeCommentOrReply(ctx context.Context, in *v11.UnlikeCommentOrReplyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 音频转文字
	Asr(ctx context.Context, in *AsrRequest, opts ...grpc.CallOption) (*AsrResponse, error)
	// 首页时间轴
	HomePageTimeline(ctx context.Context, in *HomePageTimelineRequest, opts ...grpc.CallOption) (*HomePageTimelineResponse, error)
	// 创建item，目前会直接发布而不是进入草稿态
	CreateItem(ctx context.Context, in *CreateItemRequest, opts ...grpc.CallOption) (*CreateItemResponse, error)
	// 批量获取item摘要
	BatchGetItemSummaries(ctx context.Context, in *BatchGetItemSummariesRequest, opts ...grpc.CallOption) (*BatchGetItemSummariesResponse, error)
	// 创建 item reaction
	CreateItemReaction(ctx context.Context, in *v12.CreateItemReactionRequest, opts ...grpc.CallOption) (*v12.CreateItemReactionResponse, error)
	// 移除 item reaction
	RemoveItemReaction(ctx context.Context, in *v12.RemoveItemReactionRequest, opts ...grpc.CallOption) (*v12.RemoveItemReactionResponse, error)
	// 获取用户 reacted 的 items
	ListUserReactedItems(ctx context.Context, in *v12.ListUserReactedItemsRequest, opts ...grpc.CallOption) (*v12.ListUserReactedItemsResponse, error)
	ListUsersByConsumptionStatus(ctx context.Context, in *v13.ListUsersByConsumptionStatusRequest, opts ...grpc.CallOption) (*v13.ListUsersByConsumptionStatusResponse, error)
	ListActivities(ctx context.Context, in *v13.ListActivitiesRequest, opts ...grpc.CallOption) (*v13.ListActivitiesResponse, error)
	GetActivityUnreadCount(ctx context.Context, in *v13.GetActivityUnreadCountRequest, opts ...grpc.CallOption) (*v13.GetActivityUnreadCountResponse, error)
	ReportActivityRead(ctx context.Context, in *v13.ReportActivityReadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ************ story 相关 ************
	// 删除 story
	DeleteStory(ctx context.Context, in *v14.DeleteStoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 修改 story
	UpdateStory(ctx context.Context, in *v14.UpdateStoryRequest, opts ...grpc.CallOption) (*v14.UpdateStoryResponse, error)
	// 获取某个 Story 同作者一定时间范围内对的其他 story
	ListSameAuthorStoryWithAnchor(ctx context.Context, in *v14.ListSameAuthorStoryWithAnchorRequest, opts ...grpc.CallOption) (*v14.ListSameAuthorStoryWithAnchorResponse, error)
	// 置顶 story
	TopStory(ctx context.Context, in *v14.TopStoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListFollowingCreatorStoryV2(ctx context.Context, in *v2.ListFollowingCreatorStoryRequestV2, opts ...grpc.CallOption) (*v2.ListFollowingCreatorStoryResponseV2, error)
	// 获取Reveal/Type/Unmute玩法条件模板
	ListCommonConditionTemplates(ctx context.Context, in *v14.ListCommonStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListCommonStoryConditionTemplatesResponse, error)
	// 获取海龟汤玩法条件模板
	ListTurtleSoupStoryConditionTemplates(ctx context.Context, in *v14.ListTurtleSoupStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListTurtleSoupStoryConditionTemplatesResponse, error)
	// 获取换图玩法条件模板
	ListExchangeImageStoryConditionTemplates(ctx context.Context, in *v14.ListExchangeImageStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListExchangeImageStoryConditionTemplatesResponse, error)
	// 获取 Unmute 玩法条件模板
	ListUnmuteStoryConditionTemplates(ctx context.Context, in *v14.ListUnmuteStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListUnmuteStoryConditionTemplatesResponse, error)
	// 创建换图 story
	CreateExchangeImageStory(ctx context.Context, in *v14.CreateExchangeImageStoryRequest, opts ...grpc.CallOption) (*v14.CreateExchangeImageStoryResponse, error)
	// 创建 who story
	CreateWhoStoryV2(ctx context.Context, in *v2.CreateWhoStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateWhoStoryResponseV2, error)
	// 消费 who story
	ConsumeWhoStoryV2(ctx context.Context, in *v2.ConsumeWhoStoryRequestV2, opts ...grpc.CallOption) (*v2.ConsumeWhoStoryResponseV2, error)
	// 创建换图 story
	CreateExchangeImageStoryV2(ctx context.Context, in *v2.CreateExchangeImageStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateExchangeImageStoryResponseV2, error)
	// 创建海龟汤 story
	CreateTurtleSoupStory(ctx context.Context, in *v14.CreateTurtleSoupStoryRequest, opts ...grpc.CallOption) (*v14.CreateTurtleSoupStoryResponse, error)
	// 创建 pin story
	CreatePinStory(ctx context.Context, in *v2.CreatePinStoryRequest, opts ...grpc.CallOption) (*v2.CreatePinStoryResponse, error)
	// 消费 pin story
	ConsumePinStory(ctx context.Context, in *v2.ConsumePinStoryRequest, opts ...grpc.CallOption) (*v2.ConsumePinStoryResponse, error)
	// 自动生成 area emoji
	AutoGenerateAreaEmoji(ctx context.Context, in *v2.AutoGenerateAreaEmojiRequest, opts ...grpc.CallOption) (*v2.AutoGenerateAreaEmojiResponse, error)
	// 手动生成 area emoji
	ManualGenerateAreaEmoji(ctx context.Context, in *v2.ManualGenerateAreaEmojiRequest, opts ...grpc.CallOption) (*v2.ManualGenerateAreaEmojiResponse, error)
	// 创建 hide story
	CreateHideStoryV2(ctx context.Context, in *v2.CreateHideStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateHideStoryResponseV2, error)
	// 生成 hide image mask
	GenerateHideImageMask(ctx context.Context, in *v2.GenerateHideImageMaskRequest, opts ...grpc.CallOption) (*v2.GenerateHideImageMaskResponse, error)
	// 收藏 hide sticker
	CollectHideSticker(ctx context.Context, in *v15.CollectHideStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 取消收藏 hide sticker
	UnCollectHideSticker(ctx context.Context, in *v15.UnCollectHideStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 置顶 hide sticker
	TopHideSticker(ctx context.Context, in *v15.TopCollectedStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 取消置顶 hide sticker
	UnTopHideSticker(ctx context.Context, in *v15.UnTopCollectedStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 获取用户收藏的 hide sticker
	ListMyCollectedHideStickers(ctx context.Context, in *v15.ListMyCollectedStickersRequest, opts ...grpc.CallOption) (*v15.ListMyCollectedStickersResponse, error)
	// 创建海龟汤 story v2
	CreateTurtleSoupStoryV2(ctx context.Context, in *v2.CreateTurtleSoupStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateTurtleSoupStoryResponseV2, error)
	// 获取 roasted 的 topics
	GetRoastedTopics(ctx context.Context, in *v14.GetRoastedTopicsRequest, opts ...grpc.CallOption) (*v14.GetRoastedTopicsResponse, error)
	// 创建 roasted story
	CreateRoastedStory(ctx context.Context, in *v14.CreateRoastedStoryRequest, opts ...grpc.CallOption) (*v14.CreateRoastedStoryResponse, error)
	// 创建baseplay的 story
	CreateBasePlayStory(ctx context.Context, in *v14.CreateBasePlayStoryRequest, opts ...grpc.CallOption) (*v14.CreateBasePlayStoryResponse, error)
	// 创建unmute story
	CreateUnmuteStory(ctx context.Context, in *v14.CreateUnmuteStoryRequest, opts ...grpc.CallOption) (*v14.CreateUnmuteStoryResponse, error)
	// 创建 chatproxy story
	CreateChatProxyStory(ctx context.Context, in *v2.CreateChatProxyStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateChatProxyStoryResponseV2, error)
	// 获取 chatproxy 的下一个 topic
	GetChatProxyNextTopic(ctx context.Context, in *v2.GetChatProxyNextTopicRequestV2, opts ...grpc.CallOption) (*v2.GetChatProxyNextTopicResponseV2, error)
	// 消费 chatproxy
	ConsumeChatProxy(ctx context.Context, in *v2.ConsumeChatProxyRequestV2, opts ...grpc.CallOption) (*v2.ConsumeChatProxyResponseV2, error)
	// 创建unmute story
	CreateUnmuteStoryV2(ctx context.Context, in *v2.CreateUnmuteStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateUnmuteStoryResponseV2, error)
	// 创建 NowShot story
	CreateNowShotStory(ctx context.Context, in *v14.CreateNowShotStoryRequest, opts ...grpc.CallOption) (*v14.CreateNowShotStoryResponse, error)
	// 创建 NowShot V2
	CreateNowShotStoryV2(ctx context.Context, in *v2.CreateNowShotStoryRequestV2, opts ...grpc.CallOption) (*v2.StoryDetailResponseV2, error)
	// 创建 Capsule story
	CreateCapsuleStory(ctx context.Context, in *v14.CreateCapsuleStoryRequest, opts ...grpc.CallOption) (*v14.CreateCapsuleStoryResponse, error)
	// 获取 story 详情
	GetStoryDetail(ctx context.Context, in *v14.GetStoryDetailRequest, opts ...grpc.CallOption) (*v14.GetStoryDetailResponse, error)
	// 获取首页 story 列表
	ListHomePageStoryV2(ctx context.Context, in *v2.ListHomePageStoryRequestV2, opts ...grpc.CallOption) (*v2.ListHomePageStoryResponseV2, error)
	// 创作者的 Story 列表
	ListCreatorStory(ctx context.Context, in *v14.ListCreatorStoryRequest, opts ...grpc.CallOption) (*v14.ListCreatorStoryResponse, error)
	// 创作者的 Story 列表
	ListCreatorStoryV2(ctx context.Context, in *v2.ListCreatorStoryRequestV2, opts ...grpc.CallOption) (*v2.ListCreatorStoryResponseV2, error)
	// 获取用户unlocked play
	ListUnlockedStory(ctx context.Context, in *v14.ListUnlockedStoryRequest, opts ...grpc.CallOption) (*v14.ListUnlockedStoryResponse, error)
	// 消费 roasted topic
	ConsumeRoastedTopic(ctx context.Context, in *v14.ConsumeRoastedTopicRequest, opts ...grpc.CallOption) (*v14.ConsumeRoastedTopicResponse, error)
	// 消费 hide story
	ConsumeHideStory(ctx context.Context, in *v2.ConsumeHideStoryRequest, opts ...grpc.CallOption) (*v2.ConsumeHideStoryResponse, error)
	// 消费 roasted story
	ConsumeRoastedStory(ctx context.Context, in *v14.ConsumeRoastedStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeRoastedStoryResponse, error)
	// 消费 roasted story v2
	ConsumeRoastedStoryV2(ctx context.Context, in *v2.ConsumeRoastedStoryRequestV2, opts ...grpc.CallOption) (*v2.ConsumeRoastedStoryResponseV2, error)
	// 消费 baseplay story
	ConsumeBasePlayStory(ctx context.Context, in *v14.ConsumeBasePlayStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeBasePlayStoryResponse, error)
	// 消费换图 story
	ConsumeExchangeImageStory(ctx context.Context, in *v14.ConsumeExchangeImageStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeExchangeImageStoryResponse, error)
	// 消费海龟汤 story
	ConsumeTurtleSoupStory(ctx context.Context, in *v14.ConsumeTurtleSoupStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeTurtleSoupStoryResponse, error)
	// wassup v2
	// 获取 wassup 的 greetings
	GetWassupGreetings(ctx context.Context, in *v2.GetWassupGreetingsRequest, opts ...grpc.CallOption) (*v2.GetWassupGreetingsResponse, error)
	// 获取 wassup 的 next question
	GetWassupNextQuestion(ctx context.Context, in *v2.GetWassupNextQuestionRequest, opts ...grpc.CallOption) (*v2.GetWassupNextQuestionResponse, error)
	// 创建 wassup v2
	CreateWassupStoryV2(ctx context.Context, in *v2.CreateWassupStoryRequest, opts ...grpc.CallOption) (*v2.CreateWassupStoryResponse, error)
	// 消费 wassup v2
	ConsumeWassupStoryV2(ctx context.Context, in *v2.ConsumeWassupStoryRequest, opts ...grpc.CallOption) (*v2.ConsumeWassupStoryResponse, error)
	// 发送消息
	// 本接口用于在用户完成对story的消费后，对产生的resource进行后续处理。接口名称为历史遗留问题;
	SendMessage(ctx context.Context, in *v2.SendMessageRequest, opts ...grpc.CallOption) (*v2.SendMessageResponse, error)
	// 上报 haunt boo 的展现
	ReportHauntShow(ctx context.Context, in *v2.ReportHauntShowRequest, opts ...grpc.CallOption) (*v2.ReportHauntShowResponse, error)
	// 创建 haunt story
	CreateHauntStoryV2(ctx context.Context, in *v2.CreateHauntStoryRequest, opts ...grpc.CallOption) (*v2.CreateHauntStoryResponse, error)
	// 消费 haunt story
	ConsumeHauntStory(ctx context.Context, in *v2.ConsumeHauntStoryRequest, opts ...grpc.CallOption) (*v2.ConsumeHauntStoryResponse, error)
	SendHauntCaptureVideo(ctx context.Context, in *v2.SendHauntCaptureVideoRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	ListHauntRandomAvatars(ctx context.Context, in *v2.ListHauntRandomAvatarsRequest, opts ...grpc.CallOption) (*v2.ListHauntRandomAvatarsResponse, error)
	ListHauntQuestions(ctx context.Context, in *v2.ListHauntQuestionsRequest, opts ...grpc.CallOption) (*v2.ListHauntQuestionsResponse, error)
	ListHauntBooAssist(ctx context.Context, in *v2.ListHauntBooAssistRequest, opts ...grpc.CallOption) (*v2.ListHauntBooAssistResponse, error)
	AddCaptureBooIntoMyAssist(ctx context.Context, in *v2.AddCaptureBooIntoMyAssistRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	AddCapturedBooInToCollectedStickers(ctx context.Context, in *v2.AddCapturedBooInToCollectedStickersRequest, opts ...grpc.CallOption) (*v2.AddCapturedBooInToCollectedStickersResponse, error)
	// haunt 图片检测
	CheckHauntImage(ctx context.Context, in *v2.ImageCheckRequest, opts ...grpc.CallOption) (*v2.ImageCheckResponse, error)
	// 消费Unmute story
	ConsumeUnmuteStory(ctx context.Context, in *v14.ConsumeUnmuteStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeUnmuteStoryResponse, error)
	// 消费 NowShot story
	ConsumeNowShotStory(ctx context.Context, in *v14.ConsumeNowShotStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeNowShotStoryResponse, error)
	// 消费 NowShot story v2
	ConsumeNowShotStoryV2(ctx context.Context, in *v2.ConsumeNowShotStoryRequestV2, opts ...grpc.CallOption) (*v2.StoryDetailResponseV2, error)
	// 消费 Capsule story
	ConsumeCapsuleStory(ctx context.Context, in *v14.ConsumeCapsuleStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeCapsuleStoryResponse, error)
	// Copilot Capsule story 创作
	CopilotCapsuleStory(ctx context.Context, in *v14.CopilotCapsuleStoryRequest, opts ...grpc.CallOption) (*v14.CopilotCapsuleStoryResponse, error)
	// story reactions ************
	CreateStoryReaction(ctx context.Context, in *v16.CreateReactionRequest, opts ...grpc.CallOption) (*v16.CreateReactionResponse, error)
	DeleteStoryReaction(ctx context.Context, in *v16.DeleteReactionRequest, opts ...grpc.CallOption) (*v16.DeleteReactionResponse, error)
	ListStoryReactionMadeUsers(ctx context.Context, in *v16.ListReactionMadeUsersRequest, opts ...grpc.CallOption) (*v16.ListReactionMadeUsersResponse, error)
	ReportShareStat(ctx context.Context, in *ReportShareStatRequest, opts ...grpc.CallOption) (*ReportShareStatResponse, error)
	// ********** portal **********
	CreateMoment(ctx context.Context, in *v17.CreateMomentRequest, opts ...grpc.CallOption) (*v17.CreateMomentResponse, error)
	GetUserCreatedPortalsInfo(ctx context.Context, in *v17.GetUserCreatedPortalsInfoRequest, opts ...grpc.CallOption) (*v17.GetUserCreatedPortalsInfoResponse, error)
	DeleteMoment(ctx context.Context, in *v17.DeleteMomentRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	CreateMomentRelation(ctx context.Context, in *v17.CreateMomentRelationRequest, opts ...grpc.CallOption) (*v17.CreateMomentRelationResponse, error)
	RemoveMomentRelation(ctx context.Context, in *v17.RemoveMomentRelationRequest, opts ...grpc.CallOption) (*v17.RemoveMomentRelationResponse, error)
	ListUserCreatedPortalsWithTimeRange(ctx context.Context, in *v17.ListUserCreatedPortalsWithTimeRangeRequest, opts ...grpc.CallOption) (*v17.ListUserCreatedPortalsWithTimeRangeResponse, error)
	ListMyPortals(ctx context.Context, in *v17.ListMyPortalsRequest, opts ...grpc.CallOption) (*v17.ListMyPortalsResponse, error)
	ListCouldAppendMomentStories(ctx context.Context, in *v17.ListCouldAppendMomentStoriesRequest, opts ...grpc.CallOption) (*v17.ListCouldAppendMomentStoriesResponse, error)
	ReportRead(ctx context.Context, in *v17.ReportReadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	GetPortal(ctx context.Context, in *v17.GetPortalRequest, opts ...grpc.CallOption) (*v17.GetPortalResponse, error)
	ListMomentViewers(ctx context.Context, in *v17.ListMomentViewersRequest, opts ...grpc.CallOption) (*v17.ListMomentViewersResponse, error)
	ListTrendingPortals(ctx context.Context, in *v17.ListTrendingPortalsRequest, opts ...grpc.CallOption) (*v17.ListTrendingPortalsResponse, error)
	// 通过 moment_id 获取 moment 详情
	GetMoment(ctx context.Context, in *v17.GetMomentRequest, opts ...grpc.CallOption) (*v17.GetMomentResponse, error)
	// 分享给at的好友创建moment的消息,创建story/moment时调用
	SendMomentInvite(ctx context.Context, in *v17.SendMomentInviteRequest, opts ...grpc.CallOption) (*v17.SendMomentInviteResponse, error)
}

type itemsClient struct {
	cc grpc.ClientConnInterface
}

func NewItemsClient(cc grpc.ClientConnInterface) ItemsClient {
	return &itemsClient{cc}
}

func (c *itemsClient) MusicSearch(ctx context.Context, in *v1.SearchRequest, opts ...grpc.CallOption) (*v1.SearchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.SearchResponse)
	err := c.cc.Invoke(ctx, Items_MusicSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateComment(ctx context.Context, in *v11.CreateCommentRequest, opts ...grpc.CallOption) (*v11.CreateCommentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.CreateCommentResponse)
	err := c.cc.Invoke(ctx, Items_CreateComment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListComments(ctx context.Context, in *v11.ListCommentsRequest, opts ...grpc.CallOption) (*v11.ListCommentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.ListCommentsResponse)
	err := c.cc.Invoke(ctx, Items_ListComments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListCommentReplies(ctx context.Context, in *v11.ListCommentRepliesRequest, opts ...grpc.CallOption) (*v11.ListCommentRepliesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v11.ListCommentRepliesResponse)
	err := c.cc.Invoke(ctx, Items_ListCommentReplies_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) DeleteCommentOrReply(ctx context.Context, in *v11.DeleteCommentOrReplyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_DeleteCommentOrReply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) LikeCommentOrReply(ctx context.Context, in *v11.LikeCommentOrReplyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_LikeCommentOrReply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) UnlikeCommentOrReply(ctx context.Context, in *v11.UnlikeCommentOrReplyRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_UnlikeCommentOrReply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) Asr(ctx context.Context, in *AsrRequest, opts ...grpc.CallOption) (*AsrResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AsrResponse)
	err := c.cc.Invoke(ctx, Items_Asr_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) HomePageTimeline(ctx context.Context, in *HomePageTimelineRequest, opts ...grpc.CallOption) (*HomePageTimelineResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HomePageTimelineResponse)
	err := c.cc.Invoke(ctx, Items_HomePageTimeline_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateItem(ctx context.Context, in *CreateItemRequest, opts ...grpc.CallOption) (*CreateItemResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateItemResponse)
	err := c.cc.Invoke(ctx, Items_CreateItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) BatchGetItemSummaries(ctx context.Context, in *BatchGetItemSummariesRequest, opts ...grpc.CallOption) (*BatchGetItemSummariesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetItemSummariesResponse)
	err := c.cc.Invoke(ctx, Items_BatchGetItemSummaries_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateItemReaction(ctx context.Context, in *v12.CreateItemReactionRequest, opts ...grpc.CallOption) (*v12.CreateItemReactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.CreateItemReactionResponse)
	err := c.cc.Invoke(ctx, Items_CreateItemReaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) RemoveItemReaction(ctx context.Context, in *v12.RemoveItemReactionRequest, opts ...grpc.CallOption) (*v12.RemoveItemReactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.RemoveItemReactionResponse)
	err := c.cc.Invoke(ctx, Items_RemoveItemReaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListUserReactedItems(ctx context.Context, in *v12.ListUserReactedItemsRequest, opts ...grpc.CallOption) (*v12.ListUserReactedItemsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v12.ListUserReactedItemsResponse)
	err := c.cc.Invoke(ctx, Items_ListUserReactedItems_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListUsersByConsumptionStatus(ctx context.Context, in *v13.ListUsersByConsumptionStatusRequest, opts ...grpc.CallOption) (*v13.ListUsersByConsumptionStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.ListUsersByConsumptionStatusResponse)
	err := c.cc.Invoke(ctx, Items_ListUsersByConsumptionStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListActivities(ctx context.Context, in *v13.ListActivitiesRequest, opts ...grpc.CallOption) (*v13.ListActivitiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.ListActivitiesResponse)
	err := c.cc.Invoke(ctx, Items_ListActivities_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetActivityUnreadCount(ctx context.Context, in *v13.GetActivityUnreadCountRequest, opts ...grpc.CallOption) (*v13.GetActivityUnreadCountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v13.GetActivityUnreadCountResponse)
	err := c.cc.Invoke(ctx, Items_GetActivityUnreadCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ReportActivityRead(ctx context.Context, in *v13.ReportActivityReadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_ReportActivityRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) DeleteStory(ctx context.Context, in *v14.DeleteStoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_DeleteStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) UpdateStory(ctx context.Context, in *v14.UpdateStoryRequest, opts ...grpc.CallOption) (*v14.UpdateStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.UpdateStoryResponse)
	err := c.cc.Invoke(ctx, Items_UpdateStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListSameAuthorStoryWithAnchor(ctx context.Context, in *v14.ListSameAuthorStoryWithAnchorRequest, opts ...grpc.CallOption) (*v14.ListSameAuthorStoryWithAnchorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListSameAuthorStoryWithAnchorResponse)
	err := c.cc.Invoke(ctx, Items_ListSameAuthorStoryWithAnchor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) TopStory(ctx context.Context, in *v14.TopStoryRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_TopStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListFollowingCreatorStoryV2(ctx context.Context, in *v2.ListFollowingCreatorStoryRequestV2, opts ...grpc.CallOption) (*v2.ListFollowingCreatorStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ListFollowingCreatorStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_ListFollowingCreatorStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListCommonConditionTemplates(ctx context.Context, in *v14.ListCommonStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListCommonStoryConditionTemplatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListCommonStoryConditionTemplatesResponse)
	err := c.cc.Invoke(ctx, Items_ListCommonConditionTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListTurtleSoupStoryConditionTemplates(ctx context.Context, in *v14.ListTurtleSoupStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListTurtleSoupStoryConditionTemplatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListTurtleSoupStoryConditionTemplatesResponse)
	err := c.cc.Invoke(ctx, Items_ListTurtleSoupStoryConditionTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListExchangeImageStoryConditionTemplates(ctx context.Context, in *v14.ListExchangeImageStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListExchangeImageStoryConditionTemplatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListExchangeImageStoryConditionTemplatesResponse)
	err := c.cc.Invoke(ctx, Items_ListExchangeImageStoryConditionTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListUnmuteStoryConditionTemplates(ctx context.Context, in *v14.ListUnmuteStoryConditionTemplatesRequest, opts ...grpc.CallOption) (*v14.ListUnmuteStoryConditionTemplatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListUnmuteStoryConditionTemplatesResponse)
	err := c.cc.Invoke(ctx, Items_ListUnmuteStoryConditionTemplates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateExchangeImageStory(ctx context.Context, in *v14.CreateExchangeImageStoryRequest, opts ...grpc.CallOption) (*v14.CreateExchangeImageStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateExchangeImageStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateExchangeImageStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateWhoStoryV2(ctx context.Context, in *v2.CreateWhoStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateWhoStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateWhoStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateWhoStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeWhoStoryV2(ctx context.Context, in *v2.ConsumeWhoStoryRequestV2, opts ...grpc.CallOption) (*v2.ConsumeWhoStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumeWhoStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_ConsumeWhoStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateExchangeImageStoryV2(ctx context.Context, in *v2.CreateExchangeImageStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateExchangeImageStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateExchangeImageStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateExchangeImageStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateTurtleSoupStory(ctx context.Context, in *v14.CreateTurtleSoupStoryRequest, opts ...grpc.CallOption) (*v14.CreateTurtleSoupStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateTurtleSoupStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateTurtleSoupStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreatePinStory(ctx context.Context, in *v2.CreatePinStoryRequest, opts ...grpc.CallOption) (*v2.CreatePinStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreatePinStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreatePinStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumePinStory(ctx context.Context, in *v2.ConsumePinStoryRequest, opts ...grpc.CallOption) (*v2.ConsumePinStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumePinStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumePinStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) AutoGenerateAreaEmoji(ctx context.Context, in *v2.AutoGenerateAreaEmojiRequest, opts ...grpc.CallOption) (*v2.AutoGenerateAreaEmojiResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.AutoGenerateAreaEmojiResponse)
	err := c.cc.Invoke(ctx, Items_AutoGenerateAreaEmoji_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ManualGenerateAreaEmoji(ctx context.Context, in *v2.ManualGenerateAreaEmojiRequest, opts ...grpc.CallOption) (*v2.ManualGenerateAreaEmojiResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ManualGenerateAreaEmojiResponse)
	err := c.cc.Invoke(ctx, Items_ManualGenerateAreaEmoji_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateHideStoryV2(ctx context.Context, in *v2.CreateHideStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateHideStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateHideStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateHideStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GenerateHideImageMask(ctx context.Context, in *v2.GenerateHideImageMaskRequest, opts ...grpc.CallOption) (*v2.GenerateHideImageMaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.GenerateHideImageMaskResponse)
	err := c.cc.Invoke(ctx, Items_GenerateHideImageMask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CollectHideSticker(ctx context.Context, in *v15.CollectHideStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_CollectHideSticker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) UnCollectHideSticker(ctx context.Context, in *v15.UnCollectHideStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_UnCollectHideSticker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) TopHideSticker(ctx context.Context, in *v15.TopCollectedStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_TopHideSticker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) UnTopHideSticker(ctx context.Context, in *v15.UnTopCollectedStickerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_UnTopHideSticker_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListMyCollectedHideStickers(ctx context.Context, in *v15.ListMyCollectedStickersRequest, opts ...grpc.CallOption) (*v15.ListMyCollectedStickersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v15.ListMyCollectedStickersResponse)
	err := c.cc.Invoke(ctx, Items_ListMyCollectedHideStickers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateTurtleSoupStoryV2(ctx context.Context, in *v2.CreateTurtleSoupStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateTurtleSoupStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateTurtleSoupStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateTurtleSoupStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetRoastedTopics(ctx context.Context, in *v14.GetRoastedTopicsRequest, opts ...grpc.CallOption) (*v14.GetRoastedTopicsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.GetRoastedTopicsResponse)
	err := c.cc.Invoke(ctx, Items_GetRoastedTopics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateRoastedStory(ctx context.Context, in *v14.CreateRoastedStoryRequest, opts ...grpc.CallOption) (*v14.CreateRoastedStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateRoastedStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateRoastedStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateBasePlayStory(ctx context.Context, in *v14.CreateBasePlayStoryRequest, opts ...grpc.CallOption) (*v14.CreateBasePlayStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateBasePlayStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateBasePlayStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateUnmuteStory(ctx context.Context, in *v14.CreateUnmuteStoryRequest, opts ...grpc.CallOption) (*v14.CreateUnmuteStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateUnmuteStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateUnmuteStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateChatProxyStory(ctx context.Context, in *v2.CreateChatProxyStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateChatProxyStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateChatProxyStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateChatProxyStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetChatProxyNextTopic(ctx context.Context, in *v2.GetChatProxyNextTopicRequestV2, opts ...grpc.CallOption) (*v2.GetChatProxyNextTopicResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.GetChatProxyNextTopicResponseV2)
	err := c.cc.Invoke(ctx, Items_GetChatProxyNextTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeChatProxy(ctx context.Context, in *v2.ConsumeChatProxyRequestV2, opts ...grpc.CallOption) (*v2.ConsumeChatProxyResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumeChatProxyResponseV2)
	err := c.cc.Invoke(ctx, Items_ConsumeChatProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateUnmuteStoryV2(ctx context.Context, in *v2.CreateUnmuteStoryRequestV2, opts ...grpc.CallOption) (*v2.CreateUnmuteStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateUnmuteStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateUnmuteStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateNowShotStory(ctx context.Context, in *v14.CreateNowShotStoryRequest, opts ...grpc.CallOption) (*v14.CreateNowShotStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateNowShotStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateNowShotStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateNowShotStoryV2(ctx context.Context, in *v2.CreateNowShotStoryRequestV2, opts ...grpc.CallOption) (*v2.StoryDetailResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.StoryDetailResponseV2)
	err := c.cc.Invoke(ctx, Items_CreateNowShotStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateCapsuleStory(ctx context.Context, in *v14.CreateCapsuleStoryRequest, opts ...grpc.CallOption) (*v14.CreateCapsuleStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CreateCapsuleStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateCapsuleStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetStoryDetail(ctx context.Context, in *v14.GetStoryDetailRequest, opts ...grpc.CallOption) (*v14.GetStoryDetailResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.GetStoryDetailResponse)
	err := c.cc.Invoke(ctx, Items_GetStoryDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListHomePageStoryV2(ctx context.Context, in *v2.ListHomePageStoryRequestV2, opts ...grpc.CallOption) (*v2.ListHomePageStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ListHomePageStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_ListHomePageStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListCreatorStory(ctx context.Context, in *v14.ListCreatorStoryRequest, opts ...grpc.CallOption) (*v14.ListCreatorStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListCreatorStoryResponse)
	err := c.cc.Invoke(ctx, Items_ListCreatorStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListCreatorStoryV2(ctx context.Context, in *v2.ListCreatorStoryRequestV2, opts ...grpc.CallOption) (*v2.ListCreatorStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ListCreatorStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_ListCreatorStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListUnlockedStory(ctx context.Context, in *v14.ListUnlockedStoryRequest, opts ...grpc.CallOption) (*v14.ListUnlockedStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ListUnlockedStoryResponse)
	err := c.cc.Invoke(ctx, Items_ListUnlockedStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeRoastedTopic(ctx context.Context, in *v14.ConsumeRoastedTopicRequest, opts ...grpc.CallOption) (*v14.ConsumeRoastedTopicResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeRoastedTopicResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeRoastedTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeHideStory(ctx context.Context, in *v2.ConsumeHideStoryRequest, opts ...grpc.CallOption) (*v2.ConsumeHideStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumeHideStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeHideStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeRoastedStory(ctx context.Context, in *v14.ConsumeRoastedStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeRoastedStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeRoastedStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeRoastedStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeRoastedStoryV2(ctx context.Context, in *v2.ConsumeRoastedStoryRequestV2, opts ...grpc.CallOption) (*v2.ConsumeRoastedStoryResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumeRoastedStoryResponseV2)
	err := c.cc.Invoke(ctx, Items_ConsumeRoastedStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeBasePlayStory(ctx context.Context, in *v14.ConsumeBasePlayStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeBasePlayStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeBasePlayStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeBasePlayStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeExchangeImageStory(ctx context.Context, in *v14.ConsumeExchangeImageStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeExchangeImageStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeExchangeImageStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeExchangeImageStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeTurtleSoupStory(ctx context.Context, in *v14.ConsumeTurtleSoupStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeTurtleSoupStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeTurtleSoupStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeTurtleSoupStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetWassupGreetings(ctx context.Context, in *v2.GetWassupGreetingsRequest, opts ...grpc.CallOption) (*v2.GetWassupGreetingsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.GetWassupGreetingsResponse)
	err := c.cc.Invoke(ctx, Items_GetWassupGreetings_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetWassupNextQuestion(ctx context.Context, in *v2.GetWassupNextQuestionRequest, opts ...grpc.CallOption) (*v2.GetWassupNextQuestionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.GetWassupNextQuestionResponse)
	err := c.cc.Invoke(ctx, Items_GetWassupNextQuestion_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateWassupStoryV2(ctx context.Context, in *v2.CreateWassupStoryRequest, opts ...grpc.CallOption) (*v2.CreateWassupStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateWassupStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateWassupStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeWassupStoryV2(ctx context.Context, in *v2.ConsumeWassupStoryRequest, opts ...grpc.CallOption) (*v2.ConsumeWassupStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumeWassupStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeWassupStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) SendMessage(ctx context.Context, in *v2.SendMessageRequest, opts ...grpc.CallOption) (*v2.SendMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.SendMessageResponse)
	err := c.cc.Invoke(ctx, Items_SendMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ReportHauntShow(ctx context.Context, in *v2.ReportHauntShowRequest, opts ...grpc.CallOption) (*v2.ReportHauntShowResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ReportHauntShowResponse)
	err := c.cc.Invoke(ctx, Items_ReportHauntShow_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateHauntStoryV2(ctx context.Context, in *v2.CreateHauntStoryRequest, opts ...grpc.CallOption) (*v2.CreateHauntStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.CreateHauntStoryResponse)
	err := c.cc.Invoke(ctx, Items_CreateHauntStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeHauntStory(ctx context.Context, in *v2.ConsumeHauntStoryRequest, opts ...grpc.CallOption) (*v2.ConsumeHauntStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ConsumeHauntStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeHauntStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) SendHauntCaptureVideo(ctx context.Context, in *v2.SendHauntCaptureVideoRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_SendHauntCaptureVideo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListHauntRandomAvatars(ctx context.Context, in *v2.ListHauntRandomAvatarsRequest, opts ...grpc.CallOption) (*v2.ListHauntRandomAvatarsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ListHauntRandomAvatarsResponse)
	err := c.cc.Invoke(ctx, Items_ListHauntRandomAvatars_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListHauntQuestions(ctx context.Context, in *v2.ListHauntQuestionsRequest, opts ...grpc.CallOption) (*v2.ListHauntQuestionsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ListHauntQuestionsResponse)
	err := c.cc.Invoke(ctx, Items_ListHauntQuestions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListHauntBooAssist(ctx context.Context, in *v2.ListHauntBooAssistRequest, opts ...grpc.CallOption) (*v2.ListHauntBooAssistResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ListHauntBooAssistResponse)
	err := c.cc.Invoke(ctx, Items_ListHauntBooAssist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) AddCaptureBooIntoMyAssist(ctx context.Context, in *v2.AddCaptureBooIntoMyAssistRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_AddCaptureBooIntoMyAssist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) AddCapturedBooInToCollectedStickers(ctx context.Context, in *v2.AddCapturedBooInToCollectedStickersRequest, opts ...grpc.CallOption) (*v2.AddCapturedBooInToCollectedStickersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.AddCapturedBooInToCollectedStickersResponse)
	err := c.cc.Invoke(ctx, Items_AddCapturedBooInToCollectedStickers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CheckHauntImage(ctx context.Context, in *v2.ImageCheckRequest, opts ...grpc.CallOption) (*v2.ImageCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.ImageCheckResponse)
	err := c.cc.Invoke(ctx, Items_CheckHauntImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeUnmuteStory(ctx context.Context, in *v14.ConsumeUnmuteStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeUnmuteStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeUnmuteStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeUnmuteStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeNowShotStory(ctx context.Context, in *v14.ConsumeNowShotStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeNowShotStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeNowShotStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeNowShotStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeNowShotStoryV2(ctx context.Context, in *v2.ConsumeNowShotStoryRequestV2, opts ...grpc.CallOption) (*v2.StoryDetailResponseV2, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v2.StoryDetailResponseV2)
	err := c.cc.Invoke(ctx, Items_ConsumeNowShotStoryV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ConsumeCapsuleStory(ctx context.Context, in *v14.ConsumeCapsuleStoryRequest, opts ...grpc.CallOption) (*v14.ConsumeCapsuleStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.ConsumeCapsuleStoryResponse)
	err := c.cc.Invoke(ctx, Items_ConsumeCapsuleStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CopilotCapsuleStory(ctx context.Context, in *v14.CopilotCapsuleStoryRequest, opts ...grpc.CallOption) (*v14.CopilotCapsuleStoryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v14.CopilotCapsuleStoryResponse)
	err := c.cc.Invoke(ctx, Items_CopilotCapsuleStory_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateStoryReaction(ctx context.Context, in *v16.CreateReactionRequest, opts ...grpc.CallOption) (*v16.CreateReactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v16.CreateReactionResponse)
	err := c.cc.Invoke(ctx, Items_CreateStoryReaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) DeleteStoryReaction(ctx context.Context, in *v16.DeleteReactionRequest, opts ...grpc.CallOption) (*v16.DeleteReactionResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v16.DeleteReactionResponse)
	err := c.cc.Invoke(ctx, Items_DeleteStoryReaction_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListStoryReactionMadeUsers(ctx context.Context, in *v16.ListReactionMadeUsersRequest, opts ...grpc.CallOption) (*v16.ListReactionMadeUsersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v16.ListReactionMadeUsersResponse)
	err := c.cc.Invoke(ctx, Items_ListStoryReactionMadeUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ReportShareStat(ctx context.Context, in *ReportShareStatRequest, opts ...grpc.CallOption) (*ReportShareStatResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReportShareStatResponse)
	err := c.cc.Invoke(ctx, Items_ReportShareStat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateMoment(ctx context.Context, in *v17.CreateMomentRequest, opts ...grpc.CallOption) (*v17.CreateMomentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.CreateMomentResponse)
	err := c.cc.Invoke(ctx, Items_CreateMoment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetUserCreatedPortalsInfo(ctx context.Context, in *v17.GetUserCreatedPortalsInfoRequest, opts ...grpc.CallOption) (*v17.GetUserCreatedPortalsInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.GetUserCreatedPortalsInfoResponse)
	err := c.cc.Invoke(ctx, Items_GetUserCreatedPortalsInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) DeleteMoment(ctx context.Context, in *v17.DeleteMomentRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_DeleteMoment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) CreateMomentRelation(ctx context.Context, in *v17.CreateMomentRelationRequest, opts ...grpc.CallOption) (*v17.CreateMomentRelationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.CreateMomentRelationResponse)
	err := c.cc.Invoke(ctx, Items_CreateMomentRelation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) RemoveMomentRelation(ctx context.Context, in *v17.RemoveMomentRelationRequest, opts ...grpc.CallOption) (*v17.RemoveMomentRelationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.RemoveMomentRelationResponse)
	err := c.cc.Invoke(ctx, Items_RemoveMomentRelation_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListUserCreatedPortalsWithTimeRange(ctx context.Context, in *v17.ListUserCreatedPortalsWithTimeRangeRequest, opts ...grpc.CallOption) (*v17.ListUserCreatedPortalsWithTimeRangeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.ListUserCreatedPortalsWithTimeRangeResponse)
	err := c.cc.Invoke(ctx, Items_ListUserCreatedPortalsWithTimeRange_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListMyPortals(ctx context.Context, in *v17.ListMyPortalsRequest, opts ...grpc.CallOption) (*v17.ListMyPortalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.ListMyPortalsResponse)
	err := c.cc.Invoke(ctx, Items_ListMyPortals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListCouldAppendMomentStories(ctx context.Context, in *v17.ListCouldAppendMomentStoriesRequest, opts ...grpc.CallOption) (*v17.ListCouldAppendMomentStoriesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.ListCouldAppendMomentStoriesResponse)
	err := c.cc.Invoke(ctx, Items_ListCouldAppendMomentStories_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ReportRead(ctx context.Context, in *v17.ReportReadRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, Items_ReportRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetPortal(ctx context.Context, in *v17.GetPortalRequest, opts ...grpc.CallOption) (*v17.GetPortalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.GetPortalResponse)
	err := c.cc.Invoke(ctx, Items_GetPortal_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListMomentViewers(ctx context.Context, in *v17.ListMomentViewersRequest, opts ...grpc.CallOption) (*v17.ListMomentViewersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.ListMomentViewersResponse)
	err := c.cc.Invoke(ctx, Items_ListMomentViewers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) ListTrendingPortals(ctx context.Context, in *v17.ListTrendingPortalsRequest, opts ...grpc.CallOption) (*v17.ListTrendingPortalsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.ListTrendingPortalsResponse)
	err := c.cc.Invoke(ctx, Items_ListTrendingPortals_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) GetMoment(ctx context.Context, in *v17.GetMomentRequest, opts ...grpc.CallOption) (*v17.GetMomentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.GetMomentResponse)
	err := c.cc.Invoke(ctx, Items_GetMoment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *itemsClient) SendMomentInvite(ctx context.Context, in *v17.SendMomentInviteRequest, opts ...grpc.CallOption) (*v17.SendMomentInviteResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v17.SendMomentInviteResponse)
	err := c.cc.Invoke(ctx, Items_SendMomentInvite_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ItemsServer is the server API for Items service.
// All implementations must embed UnimplementedItemsServer
// for forward compatibility.
type ItemsServer interface {
	// 音乐搜索
	MusicSearch(context.Context, *v1.SearchRequest) (*v1.SearchResponse, error)
	// comments 相关 ...
	// 创建评论
	CreateComment(context.Context, *v11.CreateCommentRequest) (*v11.CreateCommentResponse, error)
	// 获取 item's 评论列表
	ListComments(context.Context, *v11.ListCommentsRequest) (*v11.ListCommentsResponse, error)
	// 获取 item's 评论回复列表
	ListCommentReplies(context.Context, *v11.ListCommentRepliesRequest) (*v11.ListCommentRepliesResponse, error)
	// 删除评论
	DeleteCommentOrReply(context.Context, *v11.DeleteCommentOrReplyRequest) (*emptypb.Empty, error)
	// 点赞评论或回复
	LikeCommentOrReply(context.Context, *v11.LikeCommentOrReplyRequest) (*emptypb.Empty, error)
	// 取消点赞评论或回复
	UnlikeCommentOrReply(context.Context, *v11.UnlikeCommentOrReplyRequest) (*emptypb.Empty, error)
	// 音频转文字
	Asr(context.Context, *AsrRequest) (*AsrResponse, error)
	// 首页时间轴
	HomePageTimeline(context.Context, *HomePageTimelineRequest) (*HomePageTimelineResponse, error)
	// 创建item，目前会直接发布而不是进入草稿态
	CreateItem(context.Context, *CreateItemRequest) (*CreateItemResponse, error)
	// 批量获取item摘要
	BatchGetItemSummaries(context.Context, *BatchGetItemSummariesRequest) (*BatchGetItemSummariesResponse, error)
	// 创建 item reaction
	CreateItemReaction(context.Context, *v12.CreateItemReactionRequest) (*v12.CreateItemReactionResponse, error)
	// 移除 item reaction
	RemoveItemReaction(context.Context, *v12.RemoveItemReactionRequest) (*v12.RemoveItemReactionResponse, error)
	// 获取用户 reacted 的 items
	ListUserReactedItems(context.Context, *v12.ListUserReactedItemsRequest) (*v12.ListUserReactedItemsResponse, error)
	ListUsersByConsumptionStatus(context.Context, *v13.ListUsersByConsumptionStatusRequest) (*v13.ListUsersByConsumptionStatusResponse, error)
	ListActivities(context.Context, *v13.ListActivitiesRequest) (*v13.ListActivitiesResponse, error)
	GetActivityUnreadCount(context.Context, *v13.GetActivityUnreadCountRequest) (*v13.GetActivityUnreadCountResponse, error)
	ReportActivityRead(context.Context, *v13.ReportActivityReadRequest) (*emptypb.Empty, error)
	// ************ story 相关 ************
	// 删除 story
	DeleteStory(context.Context, *v14.DeleteStoryRequest) (*emptypb.Empty, error)
	// 修改 story
	UpdateStory(context.Context, *v14.UpdateStoryRequest) (*v14.UpdateStoryResponse, error)
	// 获取某个 Story 同作者一定时间范围内对的其他 story
	ListSameAuthorStoryWithAnchor(context.Context, *v14.ListSameAuthorStoryWithAnchorRequest) (*v14.ListSameAuthorStoryWithAnchorResponse, error)
	// 置顶 story
	TopStory(context.Context, *v14.TopStoryRequest) (*emptypb.Empty, error)
	ListFollowingCreatorStoryV2(context.Context, *v2.ListFollowingCreatorStoryRequestV2) (*v2.ListFollowingCreatorStoryResponseV2, error)
	// 获取Reveal/Type/Unmute玩法条件模板
	ListCommonConditionTemplates(context.Context, *v14.ListCommonStoryConditionTemplatesRequest) (*v14.ListCommonStoryConditionTemplatesResponse, error)
	// 获取海龟汤玩法条件模板
	ListTurtleSoupStoryConditionTemplates(context.Context, *v14.ListTurtleSoupStoryConditionTemplatesRequest) (*v14.ListTurtleSoupStoryConditionTemplatesResponse, error)
	// 获取换图玩法条件模板
	ListExchangeImageStoryConditionTemplates(context.Context, *v14.ListExchangeImageStoryConditionTemplatesRequest) (*v14.ListExchangeImageStoryConditionTemplatesResponse, error)
	// 获取 Unmute 玩法条件模板
	ListUnmuteStoryConditionTemplates(context.Context, *v14.ListUnmuteStoryConditionTemplatesRequest) (*v14.ListUnmuteStoryConditionTemplatesResponse, error)
	// 创建换图 story
	CreateExchangeImageStory(context.Context, *v14.CreateExchangeImageStoryRequest) (*v14.CreateExchangeImageStoryResponse, error)
	// 创建 who story
	CreateWhoStoryV2(context.Context, *v2.CreateWhoStoryRequestV2) (*v2.CreateWhoStoryResponseV2, error)
	// 消费 who story
	ConsumeWhoStoryV2(context.Context, *v2.ConsumeWhoStoryRequestV2) (*v2.ConsumeWhoStoryResponseV2, error)
	// 创建换图 story
	CreateExchangeImageStoryV2(context.Context, *v2.CreateExchangeImageStoryRequestV2) (*v2.CreateExchangeImageStoryResponseV2, error)
	// 创建海龟汤 story
	CreateTurtleSoupStory(context.Context, *v14.CreateTurtleSoupStoryRequest) (*v14.CreateTurtleSoupStoryResponse, error)
	// 创建 pin story
	CreatePinStory(context.Context, *v2.CreatePinStoryRequest) (*v2.CreatePinStoryResponse, error)
	// 消费 pin story
	ConsumePinStory(context.Context, *v2.ConsumePinStoryRequest) (*v2.ConsumePinStoryResponse, error)
	// 自动生成 area emoji
	AutoGenerateAreaEmoji(context.Context, *v2.AutoGenerateAreaEmojiRequest) (*v2.AutoGenerateAreaEmojiResponse, error)
	// 手动生成 area emoji
	ManualGenerateAreaEmoji(context.Context, *v2.ManualGenerateAreaEmojiRequest) (*v2.ManualGenerateAreaEmojiResponse, error)
	// 创建 hide story
	CreateHideStoryV2(context.Context, *v2.CreateHideStoryRequestV2) (*v2.CreateHideStoryResponseV2, error)
	// 生成 hide image mask
	GenerateHideImageMask(context.Context, *v2.GenerateHideImageMaskRequest) (*v2.GenerateHideImageMaskResponse, error)
	// 收藏 hide sticker
	CollectHideSticker(context.Context, *v15.CollectHideStickerRequest) (*emptypb.Empty, error)
	// 取消收藏 hide sticker
	UnCollectHideSticker(context.Context, *v15.UnCollectHideStickerRequest) (*emptypb.Empty, error)
	// 置顶 hide sticker
	TopHideSticker(context.Context, *v15.TopCollectedStickerRequest) (*emptypb.Empty, error)
	// 取消置顶 hide sticker
	UnTopHideSticker(context.Context, *v15.UnTopCollectedStickerRequest) (*emptypb.Empty, error)
	// 获取用户收藏的 hide sticker
	ListMyCollectedHideStickers(context.Context, *v15.ListMyCollectedStickersRequest) (*v15.ListMyCollectedStickersResponse, error)
	// 创建海龟汤 story v2
	CreateTurtleSoupStoryV2(context.Context, *v2.CreateTurtleSoupStoryRequestV2) (*v2.CreateTurtleSoupStoryResponseV2, error)
	// 获取 roasted 的 topics
	GetRoastedTopics(context.Context, *v14.GetRoastedTopicsRequest) (*v14.GetRoastedTopicsResponse, error)
	// 创建 roasted story
	CreateRoastedStory(context.Context, *v14.CreateRoastedStoryRequest) (*v14.CreateRoastedStoryResponse, error)
	// 创建baseplay的 story
	CreateBasePlayStory(context.Context, *v14.CreateBasePlayStoryRequest) (*v14.CreateBasePlayStoryResponse, error)
	// 创建unmute story
	CreateUnmuteStory(context.Context, *v14.CreateUnmuteStoryRequest) (*v14.CreateUnmuteStoryResponse, error)
	// 创建 chatproxy story
	CreateChatProxyStory(context.Context, *v2.CreateChatProxyStoryRequestV2) (*v2.CreateChatProxyStoryResponseV2, error)
	// 获取 chatproxy 的下一个 topic
	GetChatProxyNextTopic(context.Context, *v2.GetChatProxyNextTopicRequestV2) (*v2.GetChatProxyNextTopicResponseV2, error)
	// 消费 chatproxy
	ConsumeChatProxy(context.Context, *v2.ConsumeChatProxyRequestV2) (*v2.ConsumeChatProxyResponseV2, error)
	// 创建unmute story
	CreateUnmuteStoryV2(context.Context, *v2.CreateUnmuteStoryRequestV2) (*v2.CreateUnmuteStoryResponseV2, error)
	// 创建 NowShot story
	CreateNowShotStory(context.Context, *v14.CreateNowShotStoryRequest) (*v14.CreateNowShotStoryResponse, error)
	// 创建 NowShot V2
	CreateNowShotStoryV2(context.Context, *v2.CreateNowShotStoryRequestV2) (*v2.StoryDetailResponseV2, error)
	// 创建 Capsule story
	CreateCapsuleStory(context.Context, *v14.CreateCapsuleStoryRequest) (*v14.CreateCapsuleStoryResponse, error)
	// 获取 story 详情
	GetStoryDetail(context.Context, *v14.GetStoryDetailRequest) (*v14.GetStoryDetailResponse, error)
	// 获取首页 story 列表
	ListHomePageStoryV2(context.Context, *v2.ListHomePageStoryRequestV2) (*v2.ListHomePageStoryResponseV2, error)
	// 创作者的 Story 列表
	ListCreatorStory(context.Context, *v14.ListCreatorStoryRequest) (*v14.ListCreatorStoryResponse, error)
	// 创作者的 Story 列表
	ListCreatorStoryV2(context.Context, *v2.ListCreatorStoryRequestV2) (*v2.ListCreatorStoryResponseV2, error)
	// 获取用户unlocked play
	ListUnlockedStory(context.Context, *v14.ListUnlockedStoryRequest) (*v14.ListUnlockedStoryResponse, error)
	// 消费 roasted topic
	ConsumeRoastedTopic(context.Context, *v14.ConsumeRoastedTopicRequest) (*v14.ConsumeRoastedTopicResponse, error)
	// 消费 hide story
	ConsumeHideStory(context.Context, *v2.ConsumeHideStoryRequest) (*v2.ConsumeHideStoryResponse, error)
	// 消费 roasted story
	ConsumeRoastedStory(context.Context, *v14.ConsumeRoastedStoryRequest) (*v14.ConsumeRoastedStoryResponse, error)
	// 消费 roasted story v2
	ConsumeRoastedStoryV2(context.Context, *v2.ConsumeRoastedStoryRequestV2) (*v2.ConsumeRoastedStoryResponseV2, error)
	// 消费 baseplay story
	ConsumeBasePlayStory(context.Context, *v14.ConsumeBasePlayStoryRequest) (*v14.ConsumeBasePlayStoryResponse, error)
	// 消费换图 story
	ConsumeExchangeImageStory(context.Context, *v14.ConsumeExchangeImageStoryRequest) (*v14.ConsumeExchangeImageStoryResponse, error)
	// 消费海龟汤 story
	ConsumeTurtleSoupStory(context.Context, *v14.ConsumeTurtleSoupStoryRequest) (*v14.ConsumeTurtleSoupStoryResponse, error)
	// wassup v2
	// 获取 wassup 的 greetings
	GetWassupGreetings(context.Context, *v2.GetWassupGreetingsRequest) (*v2.GetWassupGreetingsResponse, error)
	// 获取 wassup 的 next question
	GetWassupNextQuestion(context.Context, *v2.GetWassupNextQuestionRequest) (*v2.GetWassupNextQuestionResponse, error)
	// 创建 wassup v2
	CreateWassupStoryV2(context.Context, *v2.CreateWassupStoryRequest) (*v2.CreateWassupStoryResponse, error)
	// 消费 wassup v2
	ConsumeWassupStoryV2(context.Context, *v2.ConsumeWassupStoryRequest) (*v2.ConsumeWassupStoryResponse, error)
	// 发送消息
	// 本接口用于在用户完成对story的消费后，对产生的resource进行后续处理。接口名称为历史遗留问题;
	SendMessage(context.Context, *v2.SendMessageRequest) (*v2.SendMessageResponse, error)
	// 上报 haunt boo 的展现
	ReportHauntShow(context.Context, *v2.ReportHauntShowRequest) (*v2.ReportHauntShowResponse, error)
	// 创建 haunt story
	CreateHauntStoryV2(context.Context, *v2.CreateHauntStoryRequest) (*v2.CreateHauntStoryResponse, error)
	// 消费 haunt story
	ConsumeHauntStory(context.Context, *v2.ConsumeHauntStoryRequest) (*v2.ConsumeHauntStoryResponse, error)
	SendHauntCaptureVideo(context.Context, *v2.SendHauntCaptureVideoRequest) (*emptypb.Empty, error)
	ListHauntRandomAvatars(context.Context, *v2.ListHauntRandomAvatarsRequest) (*v2.ListHauntRandomAvatarsResponse, error)
	ListHauntQuestions(context.Context, *v2.ListHauntQuestionsRequest) (*v2.ListHauntQuestionsResponse, error)
	ListHauntBooAssist(context.Context, *v2.ListHauntBooAssistRequest) (*v2.ListHauntBooAssistResponse, error)
	AddCaptureBooIntoMyAssist(context.Context, *v2.AddCaptureBooIntoMyAssistRequest) (*emptypb.Empty, error)
	AddCapturedBooInToCollectedStickers(context.Context, *v2.AddCapturedBooInToCollectedStickersRequest) (*v2.AddCapturedBooInToCollectedStickersResponse, error)
	// haunt 图片检测
	CheckHauntImage(context.Context, *v2.ImageCheckRequest) (*v2.ImageCheckResponse, error)
	// 消费Unmute story
	ConsumeUnmuteStory(context.Context, *v14.ConsumeUnmuteStoryRequest) (*v14.ConsumeUnmuteStoryResponse, error)
	// 消费 NowShot story
	ConsumeNowShotStory(context.Context, *v14.ConsumeNowShotStoryRequest) (*v14.ConsumeNowShotStoryResponse, error)
	// 消费 NowShot story v2
	ConsumeNowShotStoryV2(context.Context, *v2.ConsumeNowShotStoryRequestV2) (*v2.StoryDetailResponseV2, error)
	// 消费 Capsule story
	ConsumeCapsuleStory(context.Context, *v14.ConsumeCapsuleStoryRequest) (*v14.ConsumeCapsuleStoryResponse, error)
	// Copilot Capsule story 创作
	CopilotCapsuleStory(context.Context, *v14.CopilotCapsuleStoryRequest) (*v14.CopilotCapsuleStoryResponse, error)
	// story reactions ************
	CreateStoryReaction(context.Context, *v16.CreateReactionRequest) (*v16.CreateReactionResponse, error)
	DeleteStoryReaction(context.Context, *v16.DeleteReactionRequest) (*v16.DeleteReactionResponse, error)
	ListStoryReactionMadeUsers(context.Context, *v16.ListReactionMadeUsersRequest) (*v16.ListReactionMadeUsersResponse, error)
	ReportShareStat(context.Context, *ReportShareStatRequest) (*ReportShareStatResponse, error)
	// ********** portal **********
	CreateMoment(context.Context, *v17.CreateMomentRequest) (*v17.CreateMomentResponse, error)
	GetUserCreatedPortalsInfo(context.Context, *v17.GetUserCreatedPortalsInfoRequest) (*v17.GetUserCreatedPortalsInfoResponse, error)
	DeleteMoment(context.Context, *v17.DeleteMomentRequest) (*emptypb.Empty, error)
	CreateMomentRelation(context.Context, *v17.CreateMomentRelationRequest) (*v17.CreateMomentRelationResponse, error)
	RemoveMomentRelation(context.Context, *v17.RemoveMomentRelationRequest) (*v17.RemoveMomentRelationResponse, error)
	ListUserCreatedPortalsWithTimeRange(context.Context, *v17.ListUserCreatedPortalsWithTimeRangeRequest) (*v17.ListUserCreatedPortalsWithTimeRangeResponse, error)
	ListMyPortals(context.Context, *v17.ListMyPortalsRequest) (*v17.ListMyPortalsResponse, error)
	ListCouldAppendMomentStories(context.Context, *v17.ListCouldAppendMomentStoriesRequest) (*v17.ListCouldAppendMomentStoriesResponse, error)
	ReportRead(context.Context, *v17.ReportReadRequest) (*emptypb.Empty, error)
	GetPortal(context.Context, *v17.GetPortalRequest) (*v17.GetPortalResponse, error)
	ListMomentViewers(context.Context, *v17.ListMomentViewersRequest) (*v17.ListMomentViewersResponse, error)
	ListTrendingPortals(context.Context, *v17.ListTrendingPortalsRequest) (*v17.ListTrendingPortalsResponse, error)
	// 通过 moment_id 获取 moment 详情
	GetMoment(context.Context, *v17.GetMomentRequest) (*v17.GetMomentResponse, error)
	// 分享给at的好友创建moment的消息,创建story/moment时调用
	SendMomentInvite(context.Context, *v17.SendMomentInviteRequest) (*v17.SendMomentInviteResponse, error)
	mustEmbedUnimplementedItemsServer()
}

// UnimplementedItemsServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedItemsServer struct{}

func (UnimplementedItemsServer) MusicSearch(context.Context, *v1.SearchRequest) (*v1.SearchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MusicSearch not implemented")
}
func (UnimplementedItemsServer) CreateComment(context.Context, *v11.CreateCommentRequest) (*v11.CreateCommentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateComment not implemented")
}
func (UnimplementedItemsServer) ListComments(context.Context, *v11.ListCommentsRequest) (*v11.ListCommentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListComments not implemented")
}
func (UnimplementedItemsServer) ListCommentReplies(context.Context, *v11.ListCommentRepliesRequest) (*v11.ListCommentRepliesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCommentReplies not implemented")
}
func (UnimplementedItemsServer) DeleteCommentOrReply(context.Context, *v11.DeleteCommentOrReplyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCommentOrReply not implemented")
}
func (UnimplementedItemsServer) LikeCommentOrReply(context.Context, *v11.LikeCommentOrReplyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LikeCommentOrReply not implemented")
}
func (UnimplementedItemsServer) UnlikeCommentOrReply(context.Context, *v11.UnlikeCommentOrReplyRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnlikeCommentOrReply not implemented")
}
func (UnimplementedItemsServer) Asr(context.Context, *AsrRequest) (*AsrResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Asr not implemented")
}
func (UnimplementedItemsServer) HomePageTimeline(context.Context, *HomePageTimelineRequest) (*HomePageTimelineResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HomePageTimeline not implemented")
}
func (UnimplementedItemsServer) CreateItem(context.Context, *CreateItemRequest) (*CreateItemResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateItem not implemented")
}
func (UnimplementedItemsServer) BatchGetItemSummaries(context.Context, *BatchGetItemSummariesRequest) (*BatchGetItemSummariesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetItemSummaries not implemented")
}
func (UnimplementedItemsServer) CreateItemReaction(context.Context, *v12.CreateItemReactionRequest) (*v12.CreateItemReactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateItemReaction not implemented")
}
func (UnimplementedItemsServer) RemoveItemReaction(context.Context, *v12.RemoveItemReactionRequest) (*v12.RemoveItemReactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveItemReaction not implemented")
}
func (UnimplementedItemsServer) ListUserReactedItems(context.Context, *v12.ListUserReactedItemsRequest) (*v12.ListUserReactedItemsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserReactedItems not implemented")
}
func (UnimplementedItemsServer) ListUsersByConsumptionStatus(context.Context, *v13.ListUsersByConsumptionStatusRequest) (*v13.ListUsersByConsumptionStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUsersByConsumptionStatus not implemented")
}
func (UnimplementedItemsServer) ListActivities(context.Context, *v13.ListActivitiesRequest) (*v13.ListActivitiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListActivities not implemented")
}
func (UnimplementedItemsServer) GetActivityUnreadCount(context.Context, *v13.GetActivityUnreadCountRequest) (*v13.GetActivityUnreadCountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActivityUnreadCount not implemented")
}
func (UnimplementedItemsServer) ReportActivityRead(context.Context, *v13.ReportActivityReadRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportActivityRead not implemented")
}
func (UnimplementedItemsServer) DeleteStory(context.Context, *v14.DeleteStoryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStory not implemented")
}
func (UnimplementedItemsServer) UpdateStory(context.Context, *v14.UpdateStoryRequest) (*v14.UpdateStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStory not implemented")
}
func (UnimplementedItemsServer) ListSameAuthorStoryWithAnchor(context.Context, *v14.ListSameAuthorStoryWithAnchorRequest) (*v14.ListSameAuthorStoryWithAnchorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSameAuthorStoryWithAnchor not implemented")
}
func (UnimplementedItemsServer) TopStory(context.Context, *v14.TopStoryRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TopStory not implemented")
}
func (UnimplementedItemsServer) ListFollowingCreatorStoryV2(context.Context, *v2.ListFollowingCreatorStoryRequestV2) (*v2.ListFollowingCreatorStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListFollowingCreatorStoryV2 not implemented")
}
func (UnimplementedItemsServer) ListCommonConditionTemplates(context.Context, *v14.ListCommonStoryConditionTemplatesRequest) (*v14.ListCommonStoryConditionTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCommonConditionTemplates not implemented")
}
func (UnimplementedItemsServer) ListTurtleSoupStoryConditionTemplates(context.Context, *v14.ListTurtleSoupStoryConditionTemplatesRequest) (*v14.ListTurtleSoupStoryConditionTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTurtleSoupStoryConditionTemplates not implemented")
}
func (UnimplementedItemsServer) ListExchangeImageStoryConditionTemplates(context.Context, *v14.ListExchangeImageStoryConditionTemplatesRequest) (*v14.ListExchangeImageStoryConditionTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExchangeImageStoryConditionTemplates not implemented")
}
func (UnimplementedItemsServer) ListUnmuteStoryConditionTemplates(context.Context, *v14.ListUnmuteStoryConditionTemplatesRequest) (*v14.ListUnmuteStoryConditionTemplatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUnmuteStoryConditionTemplates not implemented")
}
func (UnimplementedItemsServer) CreateExchangeImageStory(context.Context, *v14.CreateExchangeImageStoryRequest) (*v14.CreateExchangeImageStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateExchangeImageStory not implemented")
}
func (UnimplementedItemsServer) CreateWhoStoryV2(context.Context, *v2.CreateWhoStoryRequestV2) (*v2.CreateWhoStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWhoStoryV2 not implemented")
}
func (UnimplementedItemsServer) ConsumeWhoStoryV2(context.Context, *v2.ConsumeWhoStoryRequestV2) (*v2.ConsumeWhoStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeWhoStoryV2 not implemented")
}
func (UnimplementedItemsServer) CreateExchangeImageStoryV2(context.Context, *v2.CreateExchangeImageStoryRequestV2) (*v2.CreateExchangeImageStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateExchangeImageStoryV2 not implemented")
}
func (UnimplementedItemsServer) CreateTurtleSoupStory(context.Context, *v14.CreateTurtleSoupStoryRequest) (*v14.CreateTurtleSoupStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTurtleSoupStory not implemented")
}
func (UnimplementedItemsServer) CreatePinStory(context.Context, *v2.CreatePinStoryRequest) (*v2.CreatePinStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePinStory not implemented")
}
func (UnimplementedItemsServer) ConsumePinStory(context.Context, *v2.ConsumePinStoryRequest) (*v2.ConsumePinStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumePinStory not implemented")
}
func (UnimplementedItemsServer) AutoGenerateAreaEmoji(context.Context, *v2.AutoGenerateAreaEmojiRequest) (*v2.AutoGenerateAreaEmojiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AutoGenerateAreaEmoji not implemented")
}
func (UnimplementedItemsServer) ManualGenerateAreaEmoji(context.Context, *v2.ManualGenerateAreaEmojiRequest) (*v2.ManualGenerateAreaEmojiResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManualGenerateAreaEmoji not implemented")
}
func (UnimplementedItemsServer) CreateHideStoryV2(context.Context, *v2.CreateHideStoryRequestV2) (*v2.CreateHideStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHideStoryV2 not implemented")
}
func (UnimplementedItemsServer) GenerateHideImageMask(context.Context, *v2.GenerateHideImageMaskRequest) (*v2.GenerateHideImageMaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateHideImageMask not implemented")
}
func (UnimplementedItemsServer) CollectHideSticker(context.Context, *v15.CollectHideStickerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectHideSticker not implemented")
}
func (UnimplementedItemsServer) UnCollectHideSticker(context.Context, *v15.UnCollectHideStickerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnCollectHideSticker not implemented")
}
func (UnimplementedItemsServer) TopHideSticker(context.Context, *v15.TopCollectedStickerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TopHideSticker not implemented")
}
func (UnimplementedItemsServer) UnTopHideSticker(context.Context, *v15.UnTopCollectedStickerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnTopHideSticker not implemented")
}
func (UnimplementedItemsServer) ListMyCollectedHideStickers(context.Context, *v15.ListMyCollectedStickersRequest) (*v15.ListMyCollectedStickersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMyCollectedHideStickers not implemented")
}
func (UnimplementedItemsServer) CreateTurtleSoupStoryV2(context.Context, *v2.CreateTurtleSoupStoryRequestV2) (*v2.CreateTurtleSoupStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTurtleSoupStoryV2 not implemented")
}
func (UnimplementedItemsServer) GetRoastedTopics(context.Context, *v14.GetRoastedTopicsRequest) (*v14.GetRoastedTopicsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRoastedTopics not implemented")
}
func (UnimplementedItemsServer) CreateRoastedStory(context.Context, *v14.CreateRoastedStoryRequest) (*v14.CreateRoastedStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRoastedStory not implemented")
}
func (UnimplementedItemsServer) CreateBasePlayStory(context.Context, *v14.CreateBasePlayStoryRequest) (*v14.CreateBasePlayStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBasePlayStory not implemented")
}
func (UnimplementedItemsServer) CreateUnmuteStory(context.Context, *v14.CreateUnmuteStoryRequest) (*v14.CreateUnmuteStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUnmuteStory not implemented")
}
func (UnimplementedItemsServer) CreateChatProxyStory(context.Context, *v2.CreateChatProxyStoryRequestV2) (*v2.CreateChatProxyStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChatProxyStory not implemented")
}
func (UnimplementedItemsServer) GetChatProxyNextTopic(context.Context, *v2.GetChatProxyNextTopicRequestV2) (*v2.GetChatProxyNextTopicResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChatProxyNextTopic not implemented")
}
func (UnimplementedItemsServer) ConsumeChatProxy(context.Context, *v2.ConsumeChatProxyRequestV2) (*v2.ConsumeChatProxyResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeChatProxy not implemented")
}
func (UnimplementedItemsServer) CreateUnmuteStoryV2(context.Context, *v2.CreateUnmuteStoryRequestV2) (*v2.CreateUnmuteStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUnmuteStoryV2 not implemented")
}
func (UnimplementedItemsServer) CreateNowShotStory(context.Context, *v14.CreateNowShotStoryRequest) (*v14.CreateNowShotStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNowShotStory not implemented")
}
func (UnimplementedItemsServer) CreateNowShotStoryV2(context.Context, *v2.CreateNowShotStoryRequestV2) (*v2.StoryDetailResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNowShotStoryV2 not implemented")
}
func (UnimplementedItemsServer) CreateCapsuleStory(context.Context, *v14.CreateCapsuleStoryRequest) (*v14.CreateCapsuleStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCapsuleStory not implemented")
}
func (UnimplementedItemsServer) GetStoryDetail(context.Context, *v14.GetStoryDetailRequest) (*v14.GetStoryDetailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStoryDetail not implemented")
}
func (UnimplementedItemsServer) ListHomePageStoryV2(context.Context, *v2.ListHomePageStoryRequestV2) (*v2.ListHomePageStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHomePageStoryV2 not implemented")
}
func (UnimplementedItemsServer) ListCreatorStory(context.Context, *v14.ListCreatorStoryRequest) (*v14.ListCreatorStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCreatorStory not implemented")
}
func (UnimplementedItemsServer) ListCreatorStoryV2(context.Context, *v2.ListCreatorStoryRequestV2) (*v2.ListCreatorStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCreatorStoryV2 not implemented")
}
func (UnimplementedItemsServer) ListUnlockedStory(context.Context, *v14.ListUnlockedStoryRequest) (*v14.ListUnlockedStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUnlockedStory not implemented")
}
func (UnimplementedItemsServer) ConsumeRoastedTopic(context.Context, *v14.ConsumeRoastedTopicRequest) (*v14.ConsumeRoastedTopicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeRoastedTopic not implemented")
}
func (UnimplementedItemsServer) ConsumeHideStory(context.Context, *v2.ConsumeHideStoryRequest) (*v2.ConsumeHideStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeHideStory not implemented")
}
func (UnimplementedItemsServer) ConsumeRoastedStory(context.Context, *v14.ConsumeRoastedStoryRequest) (*v14.ConsumeRoastedStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeRoastedStory not implemented")
}
func (UnimplementedItemsServer) ConsumeRoastedStoryV2(context.Context, *v2.ConsumeRoastedStoryRequestV2) (*v2.ConsumeRoastedStoryResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeRoastedStoryV2 not implemented")
}
func (UnimplementedItemsServer) ConsumeBasePlayStory(context.Context, *v14.ConsumeBasePlayStoryRequest) (*v14.ConsumeBasePlayStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeBasePlayStory not implemented")
}
func (UnimplementedItemsServer) ConsumeExchangeImageStory(context.Context, *v14.ConsumeExchangeImageStoryRequest) (*v14.ConsumeExchangeImageStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeExchangeImageStory not implemented")
}
func (UnimplementedItemsServer) ConsumeTurtleSoupStory(context.Context, *v14.ConsumeTurtleSoupStoryRequest) (*v14.ConsumeTurtleSoupStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeTurtleSoupStory not implemented")
}
func (UnimplementedItemsServer) GetWassupGreetings(context.Context, *v2.GetWassupGreetingsRequest) (*v2.GetWassupGreetingsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWassupGreetings not implemented")
}
func (UnimplementedItemsServer) GetWassupNextQuestion(context.Context, *v2.GetWassupNextQuestionRequest) (*v2.GetWassupNextQuestionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetWassupNextQuestion not implemented")
}
func (UnimplementedItemsServer) CreateWassupStoryV2(context.Context, *v2.CreateWassupStoryRequest) (*v2.CreateWassupStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateWassupStoryV2 not implemented")
}
func (UnimplementedItemsServer) ConsumeWassupStoryV2(context.Context, *v2.ConsumeWassupStoryRequest) (*v2.ConsumeWassupStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeWassupStoryV2 not implemented")
}
func (UnimplementedItemsServer) SendMessage(context.Context, *v2.SendMessageRequest) (*v2.SendMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMessage not implemented")
}
func (UnimplementedItemsServer) ReportHauntShow(context.Context, *v2.ReportHauntShowRequest) (*v2.ReportHauntShowResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportHauntShow not implemented")
}
func (UnimplementedItemsServer) CreateHauntStoryV2(context.Context, *v2.CreateHauntStoryRequest) (*v2.CreateHauntStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateHauntStoryV2 not implemented")
}
func (UnimplementedItemsServer) ConsumeHauntStory(context.Context, *v2.ConsumeHauntStoryRequest) (*v2.ConsumeHauntStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeHauntStory not implemented")
}
func (UnimplementedItemsServer) SendHauntCaptureVideo(context.Context, *v2.SendHauntCaptureVideoRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendHauntCaptureVideo not implemented")
}
func (UnimplementedItemsServer) ListHauntRandomAvatars(context.Context, *v2.ListHauntRandomAvatarsRequest) (*v2.ListHauntRandomAvatarsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHauntRandomAvatars not implemented")
}
func (UnimplementedItemsServer) ListHauntQuestions(context.Context, *v2.ListHauntQuestionsRequest) (*v2.ListHauntQuestionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHauntQuestions not implemented")
}
func (UnimplementedItemsServer) ListHauntBooAssist(context.Context, *v2.ListHauntBooAssistRequest) (*v2.ListHauntBooAssistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListHauntBooAssist not implemented")
}
func (UnimplementedItemsServer) AddCaptureBooIntoMyAssist(context.Context, *v2.AddCaptureBooIntoMyAssistRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCaptureBooIntoMyAssist not implemented")
}
func (UnimplementedItemsServer) AddCapturedBooInToCollectedStickers(context.Context, *v2.AddCapturedBooInToCollectedStickersRequest) (*v2.AddCapturedBooInToCollectedStickersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCapturedBooInToCollectedStickers not implemented")
}
func (UnimplementedItemsServer) CheckHauntImage(context.Context, *v2.ImageCheckRequest) (*v2.ImageCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckHauntImage not implemented")
}
func (UnimplementedItemsServer) ConsumeUnmuteStory(context.Context, *v14.ConsumeUnmuteStoryRequest) (*v14.ConsumeUnmuteStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeUnmuteStory not implemented")
}
func (UnimplementedItemsServer) ConsumeNowShotStory(context.Context, *v14.ConsumeNowShotStoryRequest) (*v14.ConsumeNowShotStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeNowShotStory not implemented")
}
func (UnimplementedItemsServer) ConsumeNowShotStoryV2(context.Context, *v2.ConsumeNowShotStoryRequestV2) (*v2.StoryDetailResponseV2, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeNowShotStoryV2 not implemented")
}
func (UnimplementedItemsServer) ConsumeCapsuleStory(context.Context, *v14.ConsumeCapsuleStoryRequest) (*v14.ConsumeCapsuleStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConsumeCapsuleStory not implemented")
}
func (UnimplementedItemsServer) CopilotCapsuleStory(context.Context, *v14.CopilotCapsuleStoryRequest) (*v14.CopilotCapsuleStoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopilotCapsuleStory not implemented")
}
func (UnimplementedItemsServer) CreateStoryReaction(context.Context, *v16.CreateReactionRequest) (*v16.CreateReactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateStoryReaction not implemented")
}
func (UnimplementedItemsServer) DeleteStoryReaction(context.Context, *v16.DeleteReactionRequest) (*v16.DeleteReactionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteStoryReaction not implemented")
}
func (UnimplementedItemsServer) ListStoryReactionMadeUsers(context.Context, *v16.ListReactionMadeUsersRequest) (*v16.ListReactionMadeUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListStoryReactionMadeUsers not implemented")
}
func (UnimplementedItemsServer) ReportShareStat(context.Context, *ReportShareStatRequest) (*ReportShareStatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportShareStat not implemented")
}
func (UnimplementedItemsServer) CreateMoment(context.Context, *v17.CreateMomentRequest) (*v17.CreateMomentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMoment not implemented")
}
func (UnimplementedItemsServer) GetUserCreatedPortalsInfo(context.Context, *v17.GetUserCreatedPortalsInfoRequest) (*v17.GetUserCreatedPortalsInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserCreatedPortalsInfo not implemented")
}
func (UnimplementedItemsServer) DeleteMoment(context.Context, *v17.DeleteMomentRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMoment not implemented")
}
func (UnimplementedItemsServer) CreateMomentRelation(context.Context, *v17.CreateMomentRelationRequest) (*v17.CreateMomentRelationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMomentRelation not implemented")
}
func (UnimplementedItemsServer) RemoveMomentRelation(context.Context, *v17.RemoveMomentRelationRequest) (*v17.RemoveMomentRelationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveMomentRelation not implemented")
}
func (UnimplementedItemsServer) ListUserCreatedPortalsWithTimeRange(context.Context, *v17.ListUserCreatedPortalsWithTimeRangeRequest) (*v17.ListUserCreatedPortalsWithTimeRangeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListUserCreatedPortalsWithTimeRange not implemented")
}
func (UnimplementedItemsServer) ListMyPortals(context.Context, *v17.ListMyPortalsRequest) (*v17.ListMyPortalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMyPortals not implemented")
}
func (UnimplementedItemsServer) ListCouldAppendMomentStories(context.Context, *v17.ListCouldAppendMomentStoriesRequest) (*v17.ListCouldAppendMomentStoriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCouldAppendMomentStories not implemented")
}
func (UnimplementedItemsServer) ReportRead(context.Context, *v17.ReportReadRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportRead not implemented")
}
func (UnimplementedItemsServer) GetPortal(context.Context, *v17.GetPortalRequest) (*v17.GetPortalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPortal not implemented")
}
func (UnimplementedItemsServer) ListMomentViewers(context.Context, *v17.ListMomentViewersRequest) (*v17.ListMomentViewersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMomentViewers not implemented")
}
func (UnimplementedItemsServer) ListTrendingPortals(context.Context, *v17.ListTrendingPortalsRequest) (*v17.ListTrendingPortalsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTrendingPortals not implemented")
}
func (UnimplementedItemsServer) GetMoment(context.Context, *v17.GetMomentRequest) (*v17.GetMomentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMoment not implemented")
}
func (UnimplementedItemsServer) SendMomentInvite(context.Context, *v17.SendMomentInviteRequest) (*v17.SendMomentInviteResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendMomentInvite not implemented")
}
func (UnimplementedItemsServer) mustEmbedUnimplementedItemsServer() {}
func (UnimplementedItemsServer) testEmbeddedByValue()               {}

// UnsafeItemsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ItemsServer will
// result in compilation errors.
type UnsafeItemsServer interface {
	mustEmbedUnimplementedItemsServer()
}

func RegisterItemsServer(s grpc.ServiceRegistrar, srv ItemsServer) {
	// If the following call pancis, it indicates UnimplementedItemsServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Items_ServiceDesc, srv)
}

func _Items_MusicSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.SearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).MusicSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_MusicSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).MusicSearch(ctx, req.(*v1.SearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateComment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.CreateCommentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateComment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateComment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateComment(ctx, req.(*v11.CreateCommentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListComments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.ListCommentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListComments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListComments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListComments(ctx, req.(*v11.ListCommentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListCommentReplies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.ListCommentRepliesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListCommentReplies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListCommentReplies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListCommentReplies(ctx, req.(*v11.ListCommentRepliesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_DeleteCommentOrReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.DeleteCommentOrReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).DeleteCommentOrReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_DeleteCommentOrReply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).DeleteCommentOrReply(ctx, req.(*v11.DeleteCommentOrReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_LikeCommentOrReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.LikeCommentOrReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).LikeCommentOrReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_LikeCommentOrReply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).LikeCommentOrReply(ctx, req.(*v11.LikeCommentOrReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_UnlikeCommentOrReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v11.UnlikeCommentOrReplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).UnlikeCommentOrReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_UnlikeCommentOrReply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).UnlikeCommentOrReply(ctx, req.(*v11.UnlikeCommentOrReplyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_Asr_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AsrRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).Asr(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_Asr_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).Asr(ctx, req.(*AsrRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_HomePageTimeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HomePageTimelineRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).HomePageTimeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_HomePageTimeline_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).HomePageTimeline(ctx, req.(*HomePageTimelineRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateItem(ctx, req.(*CreateItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_BatchGetItemSummaries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetItemSummariesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).BatchGetItemSummaries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_BatchGetItemSummaries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).BatchGetItemSummaries(ctx, req.(*BatchGetItemSummariesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateItemReaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.CreateItemReactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateItemReaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateItemReaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateItemReaction(ctx, req.(*v12.CreateItemReactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_RemoveItemReaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.RemoveItemReactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).RemoveItemReaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_RemoveItemReaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).RemoveItemReaction(ctx, req.(*v12.RemoveItemReactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListUserReactedItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v12.ListUserReactedItemsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListUserReactedItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListUserReactedItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListUserReactedItems(ctx, req.(*v12.ListUserReactedItemsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListUsersByConsumptionStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.ListUsersByConsumptionStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListUsersByConsumptionStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListUsersByConsumptionStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListUsersByConsumptionStatus(ctx, req.(*v13.ListUsersByConsumptionStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListActivities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.ListActivitiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListActivities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListActivities_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListActivities(ctx, req.(*v13.ListActivitiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetActivityUnreadCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.GetActivityUnreadCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetActivityUnreadCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetActivityUnreadCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetActivityUnreadCount(ctx, req.(*v13.GetActivityUnreadCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ReportActivityRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v13.ReportActivityReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ReportActivityRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ReportActivityRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ReportActivityRead(ctx, req.(*v13.ReportActivityReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_DeleteStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.DeleteStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).DeleteStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_DeleteStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).DeleteStory(ctx, req.(*v14.DeleteStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_UpdateStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.UpdateStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).UpdateStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_UpdateStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).UpdateStory(ctx, req.(*v14.UpdateStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListSameAuthorStoryWithAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListSameAuthorStoryWithAnchorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListSameAuthorStoryWithAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListSameAuthorStoryWithAnchor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListSameAuthorStoryWithAnchor(ctx, req.(*v14.ListSameAuthorStoryWithAnchorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_TopStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.TopStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).TopStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_TopStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).TopStory(ctx, req.(*v14.TopStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListFollowingCreatorStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ListFollowingCreatorStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListFollowingCreatorStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListFollowingCreatorStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListFollowingCreatorStoryV2(ctx, req.(*v2.ListFollowingCreatorStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListCommonConditionTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListCommonStoryConditionTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListCommonConditionTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListCommonConditionTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListCommonConditionTemplates(ctx, req.(*v14.ListCommonStoryConditionTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListTurtleSoupStoryConditionTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListTurtleSoupStoryConditionTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListTurtleSoupStoryConditionTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListTurtleSoupStoryConditionTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListTurtleSoupStoryConditionTemplates(ctx, req.(*v14.ListTurtleSoupStoryConditionTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListExchangeImageStoryConditionTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListExchangeImageStoryConditionTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListExchangeImageStoryConditionTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListExchangeImageStoryConditionTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListExchangeImageStoryConditionTemplates(ctx, req.(*v14.ListExchangeImageStoryConditionTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListUnmuteStoryConditionTemplates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListUnmuteStoryConditionTemplatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListUnmuteStoryConditionTemplates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListUnmuteStoryConditionTemplates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListUnmuteStoryConditionTemplates(ctx, req.(*v14.ListUnmuteStoryConditionTemplatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateExchangeImageStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateExchangeImageStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateExchangeImageStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateExchangeImageStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateExchangeImageStory(ctx, req.(*v14.CreateExchangeImageStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateWhoStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateWhoStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateWhoStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateWhoStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateWhoStoryV2(ctx, req.(*v2.CreateWhoStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeWhoStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeWhoStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeWhoStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeWhoStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeWhoStoryV2(ctx, req.(*v2.ConsumeWhoStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateExchangeImageStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateExchangeImageStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateExchangeImageStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateExchangeImageStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateExchangeImageStoryV2(ctx, req.(*v2.CreateExchangeImageStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateTurtleSoupStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateTurtleSoupStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateTurtleSoupStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateTurtleSoupStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateTurtleSoupStory(ctx, req.(*v14.CreateTurtleSoupStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreatePinStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreatePinStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreatePinStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreatePinStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreatePinStory(ctx, req.(*v2.CreatePinStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumePinStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumePinStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumePinStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumePinStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumePinStory(ctx, req.(*v2.ConsumePinStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_AutoGenerateAreaEmoji_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.AutoGenerateAreaEmojiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).AutoGenerateAreaEmoji(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_AutoGenerateAreaEmoji_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).AutoGenerateAreaEmoji(ctx, req.(*v2.AutoGenerateAreaEmojiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ManualGenerateAreaEmoji_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ManualGenerateAreaEmojiRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ManualGenerateAreaEmoji(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ManualGenerateAreaEmoji_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ManualGenerateAreaEmoji(ctx, req.(*v2.ManualGenerateAreaEmojiRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateHideStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateHideStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateHideStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateHideStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateHideStoryV2(ctx, req.(*v2.CreateHideStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GenerateHideImageMask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GenerateHideImageMaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GenerateHideImageMask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GenerateHideImageMask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GenerateHideImageMask(ctx, req.(*v2.GenerateHideImageMaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CollectHideSticker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.CollectHideStickerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CollectHideSticker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CollectHideSticker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CollectHideSticker(ctx, req.(*v15.CollectHideStickerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_UnCollectHideSticker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.UnCollectHideStickerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).UnCollectHideSticker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_UnCollectHideSticker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).UnCollectHideSticker(ctx, req.(*v15.UnCollectHideStickerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_TopHideSticker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.TopCollectedStickerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).TopHideSticker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_TopHideSticker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).TopHideSticker(ctx, req.(*v15.TopCollectedStickerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_UnTopHideSticker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.UnTopCollectedStickerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).UnTopHideSticker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_UnTopHideSticker_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).UnTopHideSticker(ctx, req.(*v15.UnTopCollectedStickerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListMyCollectedHideStickers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v15.ListMyCollectedStickersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListMyCollectedHideStickers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListMyCollectedHideStickers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListMyCollectedHideStickers(ctx, req.(*v15.ListMyCollectedStickersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateTurtleSoupStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateTurtleSoupStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateTurtleSoupStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateTurtleSoupStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateTurtleSoupStoryV2(ctx, req.(*v2.CreateTurtleSoupStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetRoastedTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.GetRoastedTopicsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetRoastedTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetRoastedTopics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetRoastedTopics(ctx, req.(*v14.GetRoastedTopicsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateRoastedStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateRoastedStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateRoastedStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateRoastedStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateRoastedStory(ctx, req.(*v14.CreateRoastedStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateBasePlayStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateBasePlayStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateBasePlayStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateBasePlayStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateBasePlayStory(ctx, req.(*v14.CreateBasePlayStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateUnmuteStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateUnmuteStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateUnmuteStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateUnmuteStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateUnmuteStory(ctx, req.(*v14.CreateUnmuteStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateChatProxyStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateChatProxyStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateChatProxyStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateChatProxyStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateChatProxyStory(ctx, req.(*v2.CreateChatProxyStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetChatProxyNextTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetChatProxyNextTopicRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetChatProxyNextTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetChatProxyNextTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetChatProxyNextTopic(ctx, req.(*v2.GetChatProxyNextTopicRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeChatProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeChatProxyRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeChatProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeChatProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeChatProxy(ctx, req.(*v2.ConsumeChatProxyRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateUnmuteStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateUnmuteStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateUnmuteStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateUnmuteStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateUnmuteStoryV2(ctx, req.(*v2.CreateUnmuteStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateNowShotStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateNowShotStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateNowShotStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateNowShotStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateNowShotStory(ctx, req.(*v14.CreateNowShotStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateNowShotStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateNowShotStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateNowShotStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateNowShotStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateNowShotStoryV2(ctx, req.(*v2.CreateNowShotStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateCapsuleStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CreateCapsuleStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateCapsuleStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateCapsuleStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateCapsuleStory(ctx, req.(*v14.CreateCapsuleStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetStoryDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.GetStoryDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetStoryDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetStoryDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetStoryDetail(ctx, req.(*v14.GetStoryDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListHomePageStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ListHomePageStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListHomePageStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListHomePageStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListHomePageStoryV2(ctx, req.(*v2.ListHomePageStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListCreatorStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListCreatorStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListCreatorStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListCreatorStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListCreatorStory(ctx, req.(*v14.ListCreatorStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListCreatorStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ListCreatorStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListCreatorStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListCreatorStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListCreatorStoryV2(ctx, req.(*v2.ListCreatorStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListUnlockedStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ListUnlockedStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListUnlockedStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListUnlockedStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListUnlockedStory(ctx, req.(*v14.ListUnlockedStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeRoastedTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeRoastedTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeRoastedTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeRoastedTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeRoastedTopic(ctx, req.(*v14.ConsumeRoastedTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeHideStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeHideStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeHideStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeHideStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeHideStory(ctx, req.(*v2.ConsumeHideStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeRoastedStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeRoastedStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeRoastedStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeRoastedStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeRoastedStory(ctx, req.(*v14.ConsumeRoastedStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeRoastedStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeRoastedStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeRoastedStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeRoastedStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeRoastedStoryV2(ctx, req.(*v2.ConsumeRoastedStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeBasePlayStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeBasePlayStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeBasePlayStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeBasePlayStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeBasePlayStory(ctx, req.(*v14.ConsumeBasePlayStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeExchangeImageStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeExchangeImageStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeExchangeImageStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeExchangeImageStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeExchangeImageStory(ctx, req.(*v14.ConsumeExchangeImageStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeTurtleSoupStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeTurtleSoupStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeTurtleSoupStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeTurtleSoupStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeTurtleSoupStory(ctx, req.(*v14.ConsumeTurtleSoupStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetWassupGreetings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetWassupGreetingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetWassupGreetings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetWassupGreetings_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetWassupGreetings(ctx, req.(*v2.GetWassupGreetingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetWassupNextQuestion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.GetWassupNextQuestionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetWassupNextQuestion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetWassupNextQuestion_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetWassupNextQuestion(ctx, req.(*v2.GetWassupNextQuestionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateWassupStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateWassupStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateWassupStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateWassupStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateWassupStoryV2(ctx, req.(*v2.CreateWassupStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeWassupStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeWassupStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeWassupStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeWassupStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeWassupStoryV2(ctx, req.(*v2.ConsumeWassupStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_SendMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.SendMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).SendMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_SendMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).SendMessage(ctx, req.(*v2.SendMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ReportHauntShow_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ReportHauntShowRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ReportHauntShow(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ReportHauntShow_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ReportHauntShow(ctx, req.(*v2.ReportHauntShowRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateHauntStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.CreateHauntStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateHauntStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateHauntStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateHauntStoryV2(ctx, req.(*v2.CreateHauntStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeHauntStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeHauntStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeHauntStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeHauntStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeHauntStory(ctx, req.(*v2.ConsumeHauntStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_SendHauntCaptureVideo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.SendHauntCaptureVideoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).SendHauntCaptureVideo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_SendHauntCaptureVideo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).SendHauntCaptureVideo(ctx, req.(*v2.SendHauntCaptureVideoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListHauntRandomAvatars_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ListHauntRandomAvatarsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListHauntRandomAvatars(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListHauntRandomAvatars_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListHauntRandomAvatars(ctx, req.(*v2.ListHauntRandomAvatarsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListHauntQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ListHauntQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListHauntQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListHauntQuestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListHauntQuestions(ctx, req.(*v2.ListHauntQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListHauntBooAssist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ListHauntBooAssistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListHauntBooAssist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListHauntBooAssist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListHauntBooAssist(ctx, req.(*v2.ListHauntBooAssistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_AddCaptureBooIntoMyAssist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.AddCaptureBooIntoMyAssistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).AddCaptureBooIntoMyAssist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_AddCaptureBooIntoMyAssist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).AddCaptureBooIntoMyAssist(ctx, req.(*v2.AddCaptureBooIntoMyAssistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_AddCapturedBooInToCollectedStickers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.AddCapturedBooInToCollectedStickersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).AddCapturedBooInToCollectedStickers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_AddCapturedBooInToCollectedStickers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).AddCapturedBooInToCollectedStickers(ctx, req.(*v2.AddCapturedBooInToCollectedStickersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CheckHauntImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ImageCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CheckHauntImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CheckHauntImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CheckHauntImage(ctx, req.(*v2.ImageCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeUnmuteStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeUnmuteStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeUnmuteStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeUnmuteStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeUnmuteStory(ctx, req.(*v14.ConsumeUnmuteStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeNowShotStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeNowShotStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeNowShotStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeNowShotStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeNowShotStory(ctx, req.(*v14.ConsumeNowShotStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeNowShotStoryV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v2.ConsumeNowShotStoryRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeNowShotStoryV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeNowShotStoryV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeNowShotStoryV2(ctx, req.(*v2.ConsumeNowShotStoryRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ConsumeCapsuleStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.ConsumeCapsuleStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ConsumeCapsuleStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ConsumeCapsuleStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ConsumeCapsuleStory(ctx, req.(*v14.ConsumeCapsuleStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CopilotCapsuleStory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v14.CopilotCapsuleStoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CopilotCapsuleStory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CopilotCapsuleStory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CopilotCapsuleStory(ctx, req.(*v14.CopilotCapsuleStoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateStoryReaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v16.CreateReactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateStoryReaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateStoryReaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateStoryReaction(ctx, req.(*v16.CreateReactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_DeleteStoryReaction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v16.DeleteReactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).DeleteStoryReaction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_DeleteStoryReaction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).DeleteStoryReaction(ctx, req.(*v16.DeleteReactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListStoryReactionMadeUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v16.ListReactionMadeUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListStoryReactionMadeUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListStoryReactionMadeUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListStoryReactionMadeUsers(ctx, req.(*v16.ListReactionMadeUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ReportShareStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportShareStatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ReportShareStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ReportShareStat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ReportShareStat(ctx, req.(*ReportShareStatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateMoment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.CreateMomentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateMoment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateMoment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateMoment(ctx, req.(*v17.CreateMomentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetUserCreatedPortalsInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.GetUserCreatedPortalsInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetUserCreatedPortalsInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetUserCreatedPortalsInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetUserCreatedPortalsInfo(ctx, req.(*v17.GetUserCreatedPortalsInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_DeleteMoment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.DeleteMomentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).DeleteMoment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_DeleteMoment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).DeleteMoment(ctx, req.(*v17.DeleteMomentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_CreateMomentRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.CreateMomentRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).CreateMomentRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_CreateMomentRelation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).CreateMomentRelation(ctx, req.(*v17.CreateMomentRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_RemoveMomentRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.RemoveMomentRelationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).RemoveMomentRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_RemoveMomentRelation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).RemoveMomentRelation(ctx, req.(*v17.RemoveMomentRelationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListUserCreatedPortalsWithTimeRange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.ListUserCreatedPortalsWithTimeRangeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListUserCreatedPortalsWithTimeRange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListUserCreatedPortalsWithTimeRange_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListUserCreatedPortalsWithTimeRange(ctx, req.(*v17.ListUserCreatedPortalsWithTimeRangeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListMyPortals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.ListMyPortalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListMyPortals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListMyPortals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListMyPortals(ctx, req.(*v17.ListMyPortalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListCouldAppendMomentStories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.ListCouldAppendMomentStoriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListCouldAppendMomentStories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListCouldAppendMomentStories_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListCouldAppendMomentStories(ctx, req.(*v17.ListCouldAppendMomentStoriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ReportRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.ReportReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ReportRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ReportRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ReportRead(ctx, req.(*v17.ReportReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetPortal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.GetPortalRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetPortal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetPortal_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetPortal(ctx, req.(*v17.GetPortalRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListMomentViewers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.ListMomentViewersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListMomentViewers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListMomentViewers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListMomentViewers(ctx, req.(*v17.ListMomentViewersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_ListTrendingPortals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.ListTrendingPortalsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).ListTrendingPortals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_ListTrendingPortals_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).ListTrendingPortals(ctx, req.(*v17.ListTrendingPortalsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_GetMoment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.GetMomentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).GetMoment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_GetMoment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).GetMoment(ctx, req.(*v17.GetMomentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Items_SendMomentInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v17.SendMomentInviteRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ItemsServer).SendMomentInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Items_SendMomentInvite_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ItemsServer).SendMomentInvite(ctx, req.(*v17.SendMomentInviteRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Items_ServiceDesc is the grpc.ServiceDesc for Items service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Items_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.items.v1.Items",
	HandlerType: (*ItemsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MusicSearch",
			Handler:    _Items_MusicSearch_Handler,
		},
		{
			MethodName: "CreateComment",
			Handler:    _Items_CreateComment_Handler,
		},
		{
			MethodName: "ListComments",
			Handler:    _Items_ListComments_Handler,
		},
		{
			MethodName: "ListCommentReplies",
			Handler:    _Items_ListCommentReplies_Handler,
		},
		{
			MethodName: "DeleteCommentOrReply",
			Handler:    _Items_DeleteCommentOrReply_Handler,
		},
		{
			MethodName: "LikeCommentOrReply",
			Handler:    _Items_LikeCommentOrReply_Handler,
		},
		{
			MethodName: "UnlikeCommentOrReply",
			Handler:    _Items_UnlikeCommentOrReply_Handler,
		},
		{
			MethodName: "Asr",
			Handler:    _Items_Asr_Handler,
		},
		{
			MethodName: "HomePageTimeline",
			Handler:    _Items_HomePageTimeline_Handler,
		},
		{
			MethodName: "CreateItem",
			Handler:    _Items_CreateItem_Handler,
		},
		{
			MethodName: "BatchGetItemSummaries",
			Handler:    _Items_BatchGetItemSummaries_Handler,
		},
		{
			MethodName: "CreateItemReaction",
			Handler:    _Items_CreateItemReaction_Handler,
		},
		{
			MethodName: "RemoveItemReaction",
			Handler:    _Items_RemoveItemReaction_Handler,
		},
		{
			MethodName: "ListUserReactedItems",
			Handler:    _Items_ListUserReactedItems_Handler,
		},
		{
			MethodName: "ListUsersByConsumptionStatus",
			Handler:    _Items_ListUsersByConsumptionStatus_Handler,
		},
		{
			MethodName: "ListActivities",
			Handler:    _Items_ListActivities_Handler,
		},
		{
			MethodName: "GetActivityUnreadCount",
			Handler:    _Items_GetActivityUnreadCount_Handler,
		},
		{
			MethodName: "ReportActivityRead",
			Handler:    _Items_ReportActivityRead_Handler,
		},
		{
			MethodName: "DeleteStory",
			Handler:    _Items_DeleteStory_Handler,
		},
		{
			MethodName: "UpdateStory",
			Handler:    _Items_UpdateStory_Handler,
		},
		{
			MethodName: "ListSameAuthorStoryWithAnchor",
			Handler:    _Items_ListSameAuthorStoryWithAnchor_Handler,
		},
		{
			MethodName: "TopStory",
			Handler:    _Items_TopStory_Handler,
		},
		{
			MethodName: "ListFollowingCreatorStoryV2",
			Handler:    _Items_ListFollowingCreatorStoryV2_Handler,
		},
		{
			MethodName: "ListCommonConditionTemplates",
			Handler:    _Items_ListCommonConditionTemplates_Handler,
		},
		{
			MethodName: "ListTurtleSoupStoryConditionTemplates",
			Handler:    _Items_ListTurtleSoupStoryConditionTemplates_Handler,
		},
		{
			MethodName: "ListExchangeImageStoryConditionTemplates",
			Handler:    _Items_ListExchangeImageStoryConditionTemplates_Handler,
		},
		{
			MethodName: "ListUnmuteStoryConditionTemplates",
			Handler:    _Items_ListUnmuteStoryConditionTemplates_Handler,
		},
		{
			MethodName: "CreateExchangeImageStory",
			Handler:    _Items_CreateExchangeImageStory_Handler,
		},
		{
			MethodName: "CreateWhoStoryV2",
			Handler:    _Items_CreateWhoStoryV2_Handler,
		},
		{
			MethodName: "ConsumeWhoStoryV2",
			Handler:    _Items_ConsumeWhoStoryV2_Handler,
		},
		{
			MethodName: "CreateExchangeImageStoryV2",
			Handler:    _Items_CreateExchangeImageStoryV2_Handler,
		},
		{
			MethodName: "CreateTurtleSoupStory",
			Handler:    _Items_CreateTurtleSoupStory_Handler,
		},
		{
			MethodName: "CreatePinStory",
			Handler:    _Items_CreatePinStory_Handler,
		},
		{
			MethodName: "ConsumePinStory",
			Handler:    _Items_ConsumePinStory_Handler,
		},
		{
			MethodName: "AutoGenerateAreaEmoji",
			Handler:    _Items_AutoGenerateAreaEmoji_Handler,
		},
		{
			MethodName: "ManualGenerateAreaEmoji",
			Handler:    _Items_ManualGenerateAreaEmoji_Handler,
		},
		{
			MethodName: "CreateHideStoryV2",
			Handler:    _Items_CreateHideStoryV2_Handler,
		},
		{
			MethodName: "GenerateHideImageMask",
			Handler:    _Items_GenerateHideImageMask_Handler,
		},
		{
			MethodName: "CollectHideSticker",
			Handler:    _Items_CollectHideSticker_Handler,
		},
		{
			MethodName: "UnCollectHideSticker",
			Handler:    _Items_UnCollectHideSticker_Handler,
		},
		{
			MethodName: "TopHideSticker",
			Handler:    _Items_TopHideSticker_Handler,
		},
		{
			MethodName: "UnTopHideSticker",
			Handler:    _Items_UnTopHideSticker_Handler,
		},
		{
			MethodName: "ListMyCollectedHideStickers",
			Handler:    _Items_ListMyCollectedHideStickers_Handler,
		},
		{
			MethodName: "CreateTurtleSoupStoryV2",
			Handler:    _Items_CreateTurtleSoupStoryV2_Handler,
		},
		{
			MethodName: "GetRoastedTopics",
			Handler:    _Items_GetRoastedTopics_Handler,
		},
		{
			MethodName: "CreateRoastedStory",
			Handler:    _Items_CreateRoastedStory_Handler,
		},
		{
			MethodName: "CreateBasePlayStory",
			Handler:    _Items_CreateBasePlayStory_Handler,
		},
		{
			MethodName: "CreateUnmuteStory",
			Handler:    _Items_CreateUnmuteStory_Handler,
		},
		{
			MethodName: "CreateChatProxyStory",
			Handler:    _Items_CreateChatProxyStory_Handler,
		},
		{
			MethodName: "GetChatProxyNextTopic",
			Handler:    _Items_GetChatProxyNextTopic_Handler,
		},
		{
			MethodName: "ConsumeChatProxy",
			Handler:    _Items_ConsumeChatProxy_Handler,
		},
		{
			MethodName: "CreateUnmuteStoryV2",
			Handler:    _Items_CreateUnmuteStoryV2_Handler,
		},
		{
			MethodName: "CreateNowShotStory",
			Handler:    _Items_CreateNowShotStory_Handler,
		},
		{
			MethodName: "CreateNowShotStoryV2",
			Handler:    _Items_CreateNowShotStoryV2_Handler,
		},
		{
			MethodName: "CreateCapsuleStory",
			Handler:    _Items_CreateCapsuleStory_Handler,
		},
		{
			MethodName: "GetStoryDetail",
			Handler:    _Items_GetStoryDetail_Handler,
		},
		{
			MethodName: "ListHomePageStoryV2",
			Handler:    _Items_ListHomePageStoryV2_Handler,
		},
		{
			MethodName: "ListCreatorStory",
			Handler:    _Items_ListCreatorStory_Handler,
		},
		{
			MethodName: "ListCreatorStoryV2",
			Handler:    _Items_ListCreatorStoryV2_Handler,
		},
		{
			MethodName: "ListUnlockedStory",
			Handler:    _Items_ListUnlockedStory_Handler,
		},
		{
			MethodName: "ConsumeRoastedTopic",
			Handler:    _Items_ConsumeRoastedTopic_Handler,
		},
		{
			MethodName: "ConsumeHideStory",
			Handler:    _Items_ConsumeHideStory_Handler,
		},
		{
			MethodName: "ConsumeRoastedStory",
			Handler:    _Items_ConsumeRoastedStory_Handler,
		},
		{
			MethodName: "ConsumeRoastedStoryV2",
			Handler:    _Items_ConsumeRoastedStoryV2_Handler,
		},
		{
			MethodName: "ConsumeBasePlayStory",
			Handler:    _Items_ConsumeBasePlayStory_Handler,
		},
		{
			MethodName: "ConsumeExchangeImageStory",
			Handler:    _Items_ConsumeExchangeImageStory_Handler,
		},
		{
			MethodName: "ConsumeTurtleSoupStory",
			Handler:    _Items_ConsumeTurtleSoupStory_Handler,
		},
		{
			MethodName: "GetWassupGreetings",
			Handler:    _Items_GetWassupGreetings_Handler,
		},
		{
			MethodName: "GetWassupNextQuestion",
			Handler:    _Items_GetWassupNextQuestion_Handler,
		},
		{
			MethodName: "CreateWassupStoryV2",
			Handler:    _Items_CreateWassupStoryV2_Handler,
		},
		{
			MethodName: "ConsumeWassupStoryV2",
			Handler:    _Items_ConsumeWassupStoryV2_Handler,
		},
		{
			MethodName: "SendMessage",
			Handler:    _Items_SendMessage_Handler,
		},
		{
			MethodName: "ReportHauntShow",
			Handler:    _Items_ReportHauntShow_Handler,
		},
		{
			MethodName: "CreateHauntStoryV2",
			Handler:    _Items_CreateHauntStoryV2_Handler,
		},
		{
			MethodName: "ConsumeHauntStory",
			Handler:    _Items_ConsumeHauntStory_Handler,
		},
		{
			MethodName: "SendHauntCaptureVideo",
			Handler:    _Items_SendHauntCaptureVideo_Handler,
		},
		{
			MethodName: "ListHauntRandomAvatars",
			Handler:    _Items_ListHauntRandomAvatars_Handler,
		},
		{
			MethodName: "ListHauntQuestions",
			Handler:    _Items_ListHauntQuestions_Handler,
		},
		{
			MethodName: "ListHauntBooAssist",
			Handler:    _Items_ListHauntBooAssist_Handler,
		},
		{
			MethodName: "AddCaptureBooIntoMyAssist",
			Handler:    _Items_AddCaptureBooIntoMyAssist_Handler,
		},
		{
			MethodName: "AddCapturedBooInToCollectedStickers",
			Handler:    _Items_AddCapturedBooInToCollectedStickers_Handler,
		},
		{
			MethodName: "CheckHauntImage",
			Handler:    _Items_CheckHauntImage_Handler,
		},
		{
			MethodName: "ConsumeUnmuteStory",
			Handler:    _Items_ConsumeUnmuteStory_Handler,
		},
		{
			MethodName: "ConsumeNowShotStory",
			Handler:    _Items_ConsumeNowShotStory_Handler,
		},
		{
			MethodName: "ConsumeNowShotStoryV2",
			Handler:    _Items_ConsumeNowShotStoryV2_Handler,
		},
		{
			MethodName: "ConsumeCapsuleStory",
			Handler:    _Items_ConsumeCapsuleStory_Handler,
		},
		{
			MethodName: "CopilotCapsuleStory",
			Handler:    _Items_CopilotCapsuleStory_Handler,
		},
		{
			MethodName: "CreateStoryReaction",
			Handler:    _Items_CreateStoryReaction_Handler,
		},
		{
			MethodName: "DeleteStoryReaction",
			Handler:    _Items_DeleteStoryReaction_Handler,
		},
		{
			MethodName: "ListStoryReactionMadeUsers",
			Handler:    _Items_ListStoryReactionMadeUsers_Handler,
		},
		{
			MethodName: "ReportShareStat",
			Handler:    _Items_ReportShareStat_Handler,
		},
		{
			MethodName: "CreateMoment",
			Handler:    _Items_CreateMoment_Handler,
		},
		{
			MethodName: "GetUserCreatedPortalsInfo",
			Handler:    _Items_GetUserCreatedPortalsInfo_Handler,
		},
		{
			MethodName: "DeleteMoment",
			Handler:    _Items_DeleteMoment_Handler,
		},
		{
			MethodName: "CreateMomentRelation",
			Handler:    _Items_CreateMomentRelation_Handler,
		},
		{
			MethodName: "RemoveMomentRelation",
			Handler:    _Items_RemoveMomentRelation_Handler,
		},
		{
			MethodName: "ListUserCreatedPortalsWithTimeRange",
			Handler:    _Items_ListUserCreatedPortalsWithTimeRange_Handler,
		},
		{
			MethodName: "ListMyPortals",
			Handler:    _Items_ListMyPortals_Handler,
		},
		{
			MethodName: "ListCouldAppendMomentStories",
			Handler:    _Items_ListCouldAppendMomentStories_Handler,
		},
		{
			MethodName: "ReportRead",
			Handler:    _Items_ReportRead_Handler,
		},
		{
			MethodName: "GetPortal",
			Handler:    _Items_GetPortal_Handler,
		},
		{
			MethodName: "ListMomentViewers",
			Handler:    _Items_ListMomentViewers_Handler,
		},
		{
			MethodName: "ListTrendingPortals",
			Handler:    _Items_ListTrendingPortals_Handler,
		},
		{
			MethodName: "GetMoment",
			Handler:    _Items_GetMoment_Handler,
		},
		{
			MethodName: "SendMomentInvite",
			Handler:    _Items_SendMomentInvite_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/items/v1/items.proto",
}
