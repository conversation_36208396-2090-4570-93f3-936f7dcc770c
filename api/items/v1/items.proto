syntax = "proto3";

package api.items.v1;
option go_package = "boson/api/items/v1;v1";

import "api/artwork/artwork.proto";
import "api/items/types/v1/types.proto";
import "api/common/v1/common.proto";
import "google/api/annotations.proto";
import "validate/validate.proto";
import "api/items/music/search/v1/search.proto";
import "api/items/comments/v1/comments.proto";
import "api/items/reaction/v1/reaction.proto";
import "google/protobuf/empty.proto";
import "api/items/music/types/v1/types.proto";
import "api/items/story/v1/story.proto";
import "api/items/story/v1/capsule.story.proto";
import "api/items/story/v1/roasted.story.proto";
import "api/items/story/reaction/v1/reaction.proto";
import "api/items/story/v2/story.proto";
import "api/items/story/v2/hide.proto";
import "api/items/story/activity/v1/activity.proto";
import "api/items/story/hide_stickers/v1/hide_stickers.proto";
import "api/items/story/v2/chatproxy.proto";
import "api/items/portal/v1/portal.proto";
import "api/items/story/v2/who.proto";
import "api/items/story/v2/haunt.proto";
import "api/items/story/v2/wassup.proto";
import "api/items/story/v2/roasted.proto";
import "api/items/story/v2/pin.proto";

service Items {
	// 音乐搜索
	rpc MusicSearch (api.items.music.search.v1.SearchRequest) returns (api.items.music.search.v1.SearchResponse) {
		option (google.api.http) = {
			post: "/v1/items/music_search"
			body: "*"
		};
	};
	// comments 相关 ...
	// 创建评论
	rpc CreateComment (api.items.comments.v1.CreateCommentRequest) returns (api.items.comments.v1.CreateCommentResponse) {
		option (google.api.http) = {
			post: "/v1/items/comments/create"
			body: "*"
		};
	};	
	// 获取 item's 评论列表
	rpc ListComments (api.items.comments.v1.ListCommentsRequest) returns (api.items.comments.v1.ListCommentsResponse) {
		option (google.api.http) = {
			post: "/v1/items/comments/list"
			body: "*"
		};
	};
	// 获取 item's 评论回复列表
	rpc ListCommentReplies (api.items.comments.v1.ListCommentRepliesRequest) returns (api.items.comments.v1.ListCommentRepliesResponse) {
		option (google.api.http) = {
			post: "/v1/items/comments/list_replies"
			body: "*"
		};
	};
	// 删除评论
	rpc DeleteCommentOrReply (api.items.comments.v1.DeleteCommentOrReplyRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/comments/delete_comment_or_reply"
			body: "*"
		};
	};
	// 点赞评论或回复
	rpc LikeCommentOrReply (api.items.comments.v1.LikeCommentOrReplyRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/comments/like_comment_or_reply"
			body: "*"
		};
	};
	// 取消点赞评论或回复
	rpc UnlikeCommentOrReply (api.items.comments.v1.UnlikeCommentOrReplyRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/comments/unlike_comment_or_reply"
			body: "*"
		};
	};


	// 音频转文字
	rpc Asr (AsrRequest) returns (AsrResponse) {
		option (google.api.http) = {
			post: "/v1/items/asr"
			body: "*"
		};
	};
	

	// 首页时间轴
	rpc HomePageTimeline (HomePageTimelineRequest) returns (HomePageTimelineResponse) {
		option (google.api.http) = {
			post: "/v1/items/home_page_timeline"
			body: "*"
		};
	};
	// 创建item，目前会直接发布而不是进入草稿态
	rpc CreateItem (CreateItemRequest) returns (CreateItemResponse) {
		option (google.api.http) = {
			post: "/v1/items/create"
			body: "*"
		};
	};
	// 批量获取item摘要
	rpc BatchGetItemSummaries (BatchGetItemSummariesRequest) returns (BatchGetItemSummariesResponse) {
		option (google.api.http) = {
			post: "/v1/items/batch_get_item_summaries"
			body: "*"
		};
	};

	// 创建 item reaction
	rpc CreateItemReaction (api.items.reaction.v1.CreateItemReactionRequest) returns (api.items.reaction.v1.CreateItemReactionResponse) {
		option (google.api.http) = {
			post: "/v1/items/reaction/create"
			body: "*"
		};
	};

	// 移除 item reaction
	rpc RemoveItemReaction (api.items.reaction.v1.RemoveItemReactionRequest) returns (api.items.reaction.v1.RemoveItemReactionResponse) {
		option (google.api.http) = {
			post: "/v1/items/reaction/remove"
			body: "*"
		};
	};

	// 获取用户 reacted 的 items
	rpc ListUserReactedItems (api.items.reaction.v1.ListUserReactedItemsRequest) returns (api.items.reaction.v1.ListUserReactedItemsResponse) {
		option (google.api.http) = {
			post: "/v1/items/reaction/list_user_reacted"
			body: "*"
		};
	}; 

	rpc ListUsersByConsumptionStatus(api.items.story.activity.v1.ListUsersByConsumptionStatusRequest) returns (api.items.story.activity.v1.ListUsersByConsumptionStatusResponse) {
		option (google.api.http) = {
			post: "/v1/items/activity/list_users_by_consumption_status"
			body: "*"
		};
	};

	rpc ListActivities (api.items.story.activity.v1.ListActivitiesRequest) returns (api.items.story.activity.v1.ListActivitiesResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/activity/list_activities"
			body: "*"
		};
	};

	rpc GetActivityUnreadCount (api.items.story.activity.v1.GetActivityUnreadCountRequest) returns (api.items.story.activity.v1.GetActivityUnreadCountResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/activity/get_unread_count"
			body: "*"
		};
	};

	rpc ReportActivityRead (api.items.story.activity.v1.ReportActivityReadRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/story/activity/report_read"
			body: "*"
		};
	};

	// ************ story 相关 ************
	// 删除 story
	rpc DeleteStory (api.items.story.v1.DeleteStoryRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/story/delete"
			body: "*"
		};
	};
	// 修改 story
	rpc UpdateStory(api.items.story.v1.UpdateStoryRequest) returns (api.items.story.v1.UpdateStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/update"
			body: "*"
		};
	};
	// 获取某个 Story 同作者一定时间范围内对的其他 story
	rpc ListSameAuthorStoryWithAnchor (api.items.story.v1.ListSameAuthorStoryWithAnchorRequest) returns (api.items.story.v1.ListSameAuthorStoryWithAnchorResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_same_author_story_with_anchor"
			body: "*"
		};
	};
	// 置顶 story
	rpc TopStory (api.items.story.v1.TopStoryRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/story/top"
			body: "*"
		};
	};
	rpc ListFollowingCreatorStoryV2 (api.items.story.v2.ListFollowingCreatorStoryRequestV2) returns (api.items.story.v2.ListFollowingCreatorStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/list_following_creator_story"
			body: "*"
		};
	};
	// 获取Reveal/Type/Unmute玩法条件模板
	rpc ListCommonConditionTemplates (api.items.story.v1.ListCommonStoryConditionTemplatesRequest) returns (api.items.story.v1.ListCommonStoryConditionTemplatesResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_common_condition_templates"
			body: "*"
		};
	};
	// 获取海龟汤玩法条件模板
	rpc ListTurtleSoupStoryConditionTemplates (api.items.story.v1.ListTurtleSoupStoryConditionTemplatesRequest) returns (api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_turtle_soup_condition_templates"
			body: "*"
		};
	};

	// 获取换图玩法条件模板
	rpc ListExchangeImageStoryConditionTemplates (api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest) returns (api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_exchange_images_condition_templetes"
			body: "*"
		};
	};

	// 获取 Unmute 玩法条件模板
	rpc ListUnmuteStoryConditionTemplates (api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest) returns (api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_unmute_condition_templetes"
			body: "*"
		};
	};

	// 创建换图 story
	rpc CreateExchangeImageStory (api.items.story.v1.CreateExchangeImageStoryRequest) returns (api.items.story.v1.CreateExchangeImageStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_exchange_image_story"
			body: "*"
		};
	};

	// 创建 who story
	rpc CreateWhoStoryV2 (api.items.story.v2.CreateWhoStoryRequestV2) returns (api.items.story.v2.CreateWhoStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_who_story"
			body: "*"
		};
	};
	// 消费 who story
	rpc ConsumeWhoStoryV2 (api.items.story.v2.ConsumeWhoStoryRequestV2) returns (api.items.story.v2.ConsumeWhoStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_who_story"
			body: "*"
		};
	};

	// 创建换图 story
	rpc CreateExchangeImageStoryV2 (api.items.story.v2.CreateExchangeImageStoryRequestV2) returns (api.items.story.v2.CreateExchangeImageStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_exchange_image_story"
			body: "*"
		};
	};

	// 创建海龟汤 story
	rpc CreateTurtleSoupStory (api.items.story.v1.CreateTurtleSoupStoryRequest) returns (api.items.story.v1.CreateTurtleSoupStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_turtle_soup_story"
			body: "*"
		};
	};
	// 创建 pin story
	rpc CreatePinStory (api.items.story.v2.CreatePinStoryRequest) returns (api.items.story.v2.CreatePinStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/create_pin_story"
			body: "*"
		};
	};
	// 消费 pin story
	rpc ConsumePinStory (api.items.story.v2.ConsumePinStoryRequest) returns (api.items.story.v2.ConsumePinStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_pin_story"
			body: "*"
		};
	};
	// 自动生成 area emoji	
	rpc AutoGenerateAreaEmoji (api.items.story.v2.AutoGenerateAreaEmojiRequest) returns (api.items.story.v2.AutoGenerateAreaEmojiResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/auto_generate_area_emoji"
			body: "*"
		};
	};
	// 手动生成 area emoji
	rpc ManualGenerateAreaEmoji (api.items.story.v2.ManualGenerateAreaEmojiRequest) returns (api.items.story.v2.ManualGenerateAreaEmojiResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/manual_generate_area_emoji"
			body: "*"
		};
	};

	// 创建 hide story
	rpc CreateHideStoryV2 (api.items.story.v2.CreateHideStoryRequestV2) returns (api.items.story.v2.CreateHideStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_hide_story"
			body: "*"
		};
	};
	// 生成 hide image mask
	rpc GenerateHideImageMask (api.items.story.v2.GenerateHideImageMaskRequest) returns (api.items.story.v2.GenerateHideImageMaskResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/generate_hide_image_mask"
			body: "*"
		};
	};
	// 收藏 hide sticker
	rpc CollectHideSticker (api.items.story.hide_stickers.v1.CollectHideStickerRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v2/items/story/collect_hide_sticker"
			body: "*"
		};
	};
	// 取消收藏 hide sticker
	rpc UnCollectHideSticker (api.items.story.hide_stickers.v1.UnCollectHideStickerRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v2/items/story/uncollect_hide_sticker"
			body: "*"
		};
	};
	// 置顶 hide sticker
	rpc TopHideSticker (api.items.story.hide_stickers.v1.TopCollectedStickerRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v2/items/story/top_hide_sticker"
			body: "*"
		};
	};
	// 取消置顶 hide sticker
	rpc UnTopHideSticker (api.items.story.hide_stickers.v1.UnTopCollectedStickerRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v2/items/story/untop_hide_sticker"
			body: "*"
		};
	};
	// 获取用户收藏的 hide sticker
	rpc ListMyCollectedHideStickers (api.items.story.hide_stickers.v1.ListMyCollectedStickersRequest) returns (api.items.story.hide_stickers.v1.ListMyCollectedStickersResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/list_my_collected_hide_stickers"
			body: "*"
		};
	};


	// 创建海龟汤 story v2
	rpc CreateTurtleSoupStoryV2 (api.items.story.v2.CreateTurtleSoupStoryRequestV2) returns (api.items.story.v2.CreateTurtleSoupStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_turtle_soup_story"
			body: "*"
		};
	};

	// 获取 roasted 的 topics
	rpc GetRoastedTopics (api.items.story.v1.GetRoastedTopicsRequest) returns (api.items.story.v1.GetRoastedTopicsResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/get_roasted_topics"
			body: "*"
		};
	};

	// 创建 roasted story
	rpc CreateRoastedStory (api.items.story.v1.CreateRoastedStoryRequest) returns (api.items.story.v1.CreateRoastedStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_roasted_story"
			body: "*"
		};
	};

	// 创建baseplay的 story
	rpc CreateBasePlayStory (api.items.story.v1.CreateBasePlayStoryRequest) returns (api.items.story.v1.CreateBasePlayStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_base_play_story"
			body: "*"
		};
	};

	// 创建unmute story
	rpc CreateUnmuteStory (api.items.story.v1.CreateUnmuteStoryRequest) returns (api.items.story.v1.CreateUnmuteStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_unmute_story"
			body: "*"
		};
	};

	// 创建 chatproxy story
	rpc CreateChatProxyStory (api.items.story.v2.CreateChatProxyStoryRequestV2) returns (api.items.story.v2.CreateChatProxyStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_chatproxy_story"
			body: "*"
		};
	};
	// 获取 chatproxy 的下一个 topic	
	rpc GetChatProxyNextTopic (api.items.story.v2.GetChatProxyNextTopicRequestV2) returns (api.items.story.v2.GetChatProxyNextTopicResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/get_chatproxy_next_topic"
			body: "*"
		};
	};
	// 消费 chatproxy
	rpc ConsumeChatProxy (api.items.story.v2.ConsumeChatProxyRequestV2) returns (api.items.story.v2.ConsumeChatProxyResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_chatproxy"
			body: "*"
		};
	};

	// 创建unmute story
	rpc CreateUnmuteStoryV2 (api.items.story.v2.CreateUnmuteStoryRequestV2) returns (api.items.story.v2.CreateUnmuteStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_unmute_story"
			body: "*"
		};
	};

	// 创建 NowShot story
	rpc CreateNowShotStory (api.items.story.v1.CreateNowShotStoryRequest) returns (api.items.story.v1.CreateNowShotStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_now_shot_story"
			body: "*"
		};
	};

	// 创建 NowShot V2
	rpc CreateNowShotStoryV2 (api.items.story.v2.CreateNowShotStoryRequestV2) returns (api.items.story.v2.StoryDetailResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/create_now_shot_story"
			body: "*"
		};
	};

	// 创建 Capsule story
	rpc CreateCapsuleStory (api.items.story.v1.CreateCapsuleStoryRequest) returns (api.items.story.v1.CreateCapsuleStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_capsule_story"
			body: "*"
		};
	};

	// 获取 story 详情
	rpc GetStoryDetail (api.items.story.v1.GetStoryDetailRequest) returns (api.items.story.v1.GetStoryDetailResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/get_story_detail"
			body: "*"
		};
	};

	// 获取首页 story 列表	
	rpc ListHomePageStoryV2 (api.items.story.v2.ListHomePageStoryRequestV2) returns (api.items.story.v2.ListHomePageStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/list_home_page_story"
			body: "*"
		};
	};
	// 创作者的 Story 列表
	rpc ListCreatorStory (api.items.story.v1.ListCreatorStoryRequest) returns (api.items.story.v1.ListCreatorStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_creator_story"
			body: "*"
		};
	};
	// 创作者的 Story 列表
	rpc ListCreatorStoryV2 (api.items.story.v2.ListCreatorStoryRequestV2) returns (api.items.story.v2.ListCreatorStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/list_creator_story"
			body: "*"
		};
	};

	// 获取用户unlocked play
	rpc ListUnlockedStory (api.items.story.v1.ListUnlockedStoryRequest) returns (api.items.story.v1.ListUnlockedStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_unlocked_story"
			body: "*"
		};
	};
	// 消费 roasted topic 
	rpc ConsumeRoastedTopic (api.items.story.v1.ConsumeRoastedTopicRequest) returns (api.items.story.v1.ConsumeRoastedTopicResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_roasted_topic"
			body: "*"
		};
	};
	// 消费 hide story
	rpc ConsumeHideStory (api.items.story.v2.ConsumeHideStoryRequest) returns (api.items.story.v2.ConsumeHideStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_hide_story"
			body: "*"
		};
	};
	// 消费 roasted story
	rpc ConsumeRoastedStory (api.items.story.v1.ConsumeRoastedStoryRequest) returns (api.items.story.v1.ConsumeRoastedStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_roasted_story"
			body: "*"
		};
	};
	// 消费 roasted story v2
	rpc ConsumeRoastedStoryV2 (api.items.story.v2.ConsumeRoastedStoryRequestV2) returns (api.items.story.v2.ConsumeRoastedStoryResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_roasted_story"
			body: "*"
		};
	};
	// 消费 baseplay story
	rpc ConsumeBasePlayStory (api.items.story.v1.ConsumeBasePlayStoryRequest) returns (api.items.story.v1.ConsumeBasePlayStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_base_play_story"
			body: "*"
		};
	};

	// 消费换图 story
	rpc ConsumeExchangeImageStory (api.items.story.v1.ConsumeExchangeImageStoryRequest) returns (api.items.story.v1.ConsumeExchangeImageStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_exchange_image_story"
			body: "*"
		};
	};

	// 消费海龟汤 story
	rpc ConsumeTurtleSoupStory (api.items.story.v1.ConsumeTurtleSoupStoryRequest) returns (api.items.story.v1.ConsumeTurtleSoupStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_turtle_soup_story"
			body: "*"
		};
	};

	// wassup v2
	// 获取 wassup 的 greetings
	rpc GetWassupGreetings (api.items.story.v2.GetWassupGreetingsRequest) returns (api.items.story.v2.GetWassupGreetingsResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/get_wassup_greetings"
			body: "*"
		};
	};
	// 获取 wassup 的 next question
	rpc GetWassupNextQuestion (api.items.story.v2.GetWassupNextQuestionRequest) returns (api.items.story.v2.GetWassupNextQuestionResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/get_wassup_next_question"
			body: "*"
		};
	};
	// 创建 wassup v2
	rpc CreateWassupStoryV2 (api.items.story.v2.CreateWassupStoryRequest) returns (api.items.story.v2.CreateWassupStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/create_wassup_story"
			body: "*"
		};
	};
	// 消费 wassup v2
	rpc ConsumeWassupStoryV2 (api.items.story.v2.ConsumeWassupStoryRequest) returns (api.items.story.v2.ConsumeWassupStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_wassup_story"
			body: "*"
		};
	};

	// 发送消息
	// 本接口用于在用户完成对story的消费后，对产生的resource进行后续处理。接口名称为历史遗留问题;
	rpc SendMessage (api.items.story.v2.SendMessageRequest) returns (api.items.story.v2.SendMessageResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/send_message"
			body: "*"
		};
	};
	
	// 上报 haunt boo 的展现
	rpc ReportHauntShow (api.items.story.v2.ReportHauntShowRequest) returns (api.items.story.v2.ReportHauntShowResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/report_haunt_show"
			body: "*"
		};
	};

	// 创建 haunt story
	rpc CreateHauntStoryV2 (api.items.story.v2.CreateHauntStoryRequest) returns (api.items.story.v2.CreateHauntStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/create_haunt_story"
			body: "*"
		};
	};
	// 消费 haunt story
	rpc ConsumeHauntStory (api.items.story.v2.ConsumeHauntStoryRequest) returns (api.items.story.v2.ConsumeHauntStoryResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_haunt_story"
			body: "*"
		};
	};
	rpc SendHauntCaptureVideo (api.items.story.v2.SendHauntCaptureVideoRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v2/items/story/send_haunt_capture_video"
			body: "*"
		};
	};
	rpc ListHauntRandomAvatars (api.items.story.v2.ListHauntRandomAvatarsRequest) returns (api.items.story.v2.ListHauntRandomAvatarsResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/list_haunt_random_avatars"
			body: "*"
		};
	};
	rpc ListHauntQuestions (api.items.story.v2.ListHauntQuestionsRequest) returns (api.items.story.v2.ListHauntQuestionsResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/list_haunt_questions"
			body: "*"
		};
	};
	rpc ListHauntBooAssist (api.items.story.v2.ListHauntBooAssistRequest) returns (api.items.story.v2.ListHauntBooAssistResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/list_haunt_boo_assist"
			body: "*"
		};
	};
	rpc AddCaptureBooIntoMyAssist (api.items.story.v2.AddCaptureBooIntoMyAssistRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v2/items/story/add_capture_boo_into_my_assist"
			body: "*"
		};
	};
	rpc AddCapturedBooInToCollectedStickers (api.items.story.v2.AddCapturedBooInToCollectedStickersRequest) returns (api.items.story.v2.AddCapturedBooInToCollectedStickersResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/add_captured_boo_in_to_collected_stickers"
			body: "*"
		};
	};
	// haunt 图片检测
	rpc CheckHauntImage (api.items.story.v2.ImageCheckRequest) returns (api.items.story.v2.ImageCheckResponse) {
		option (google.api.http) = {
			post: "/v2/items/story/check_haunt_image"
			body: "*"
		};
	};

	// 消费Unmute story
	rpc ConsumeUnmuteStory (api.items.story.v1.ConsumeUnmuteStoryRequest) returns (api.items.story.v1.ConsumeUnmuteStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_unmute_story"
			body: "*"
		};
	};

	// 消费 NowShot story
	rpc ConsumeNowShotStory (api.items.story.v1.ConsumeNowShotStoryRequest) returns (api.items.story.v1.ConsumeNowShotStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_now_shot_story"
			body: "*"
		};
	};

	// 消费 NowShot story v2
	rpc ConsumeNowShotStoryV2 (api.items.story.v2.ConsumeNowShotStoryRequestV2) returns (api.items.story.v2.StoryDetailResponseV2) {
		option (google.api.http) = {
			post: "/v2/items/story/consume_now_shot_story"
			body: "*"
		};
	};

	// 消费 Capsule story
	rpc ConsumeCapsuleStory (api.items.story.v1.ConsumeCapsuleStoryRequest) returns (api.items.story.v1.ConsumeCapsuleStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/consume_capsule_story"
			body: "*"
		};
	};

	// Copilot Capsule story 创作
	rpc CopilotCapsuleStory (api.items.story.v1.CopilotCapsuleStoryRequest) returns (api.items.story.v1.CopilotCapsuleStoryResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/copilot_capsule_story"
			body: "*"
		};
	};
	// story reactions ************
	rpc CreateStoryReaction (api.items.story.reaction.v1.CreateReactionRequest) returns (api.items.story.reaction.v1.CreateReactionResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/create_story_reaction"
			body: "*"
		};
	};
	rpc DeleteStoryReaction (api.items.story.reaction.v1.DeleteReactionRequest) returns (api.items.story.reaction.v1.DeleteReactionResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/delete_story_reaction"
			body: "*"
		};
	};
	rpc ListStoryReactionMadeUsers (api.items.story.reaction.v1.ListReactionMadeUsersRequest) returns (api.items.story.reaction.v1.ListReactionMadeUsersResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/list_reaction_made_users"
			body: "*"
		};
	};
	rpc ReportShareStat (ReportShareStatRequest) returns (ReportShareStatResponse) {
		option (google.api.http) = {
			post: "/v1/items/story/report_share_stats"
			body: "*"
		};
	};
	// ********** portal **********
	rpc CreateMoment (api.items.portal.v1.CreateMomentRequest) returns (api.items.portal.v1.CreateMomentResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/create_moment"
			body: "*"
		};
	};
	rpc GetUserCreatedPortalsInfo (api.items.portal.v1.GetUserCreatedPortalsInfoRequest) returns (api.items.portal.v1.GetUserCreatedPortalsInfoResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/get_user_created_portals_info"
			body: "*"
		};
	};
	rpc DeleteMoment (api.items.portal.v1.DeleteMomentRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/portal/delete_moment"
			body: "*"
		};
	};
	rpc CreateMomentRelation (api.items.portal.v1.CreateMomentRelationRequest) returns (api.items.portal.v1.CreateMomentRelationResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/create_moment_relation"
			body: "*"
		};
	};
	rpc RemoveMomentRelation (api.items.portal.v1.RemoveMomentRelationRequest) returns (api.items.portal.v1.RemoveMomentRelationResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/remove_moment_relation"
			body: "*"
		};
	};
	rpc ListUserCreatedPortalsWithTimeRange (api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeRequest) returns (api.items.portal.v1.ListUserCreatedPortalsWithTimeRangeResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/list_user_created_portals_with_time_range"
			body: "*"
		};
	};
	rpc ListMyPortals (api.items.portal.v1.ListMyPortalsRequest) returns (api.items.portal.v1.ListMyPortalsResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/list_my_portals"
			body: "*"
		};
	};
	rpc ListCouldAppendMomentStories (api.items.portal.v1.ListCouldAppendMomentStoriesRequest) returns (api.items.portal.v1.ListCouldAppendMomentStoriesResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/list_could_append_moment_stories"
			body: "*"
		};
	};
	rpc ReportRead (api.items.portal.v1.ReportReadRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/v1/items/portal/report_read"
			body: "*"
		};
	};
	rpc GetPortal(api.items.portal.v1.GetPortalRequest) returns (api.items.portal.v1.GetPortalResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/get_portal"
			body: "*"
		};
	}
	rpc ListMomentViewers (api.items.portal.v1.ListMomentViewersRequest) returns (api.items.portal.v1.ListMomentViewersResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/list_moment_viewers"
			body: "*"
		};
	};
	rpc ListTrendingPortals (api.items.portal.v1.ListTrendingPortalsRequest) returns (api.items.portal.v1.ListTrendingPortalsResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/trending_portals"
			body: "*"
		};
	};
	// 通过 moment_id 获取 moment 详情
	rpc GetMoment(api.items.portal.v1.GetMomentRequest) returns (api.items.portal.v1.GetMomentResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/get_moment"
			body: "*"
		};
	};
	// 分享给at的好友创建moment的消息,创建story/moment时调用
	rpc SendMomentInvite (api.items.portal.v1.SendMomentInviteRequest) returns (api.items.portal.v1.SendMomentInviteResponse) {
		option (google.api.http) = {
			post: "/v1/items/portal/send_moment_invite"
			body: "*"
		};
	};
}

message ReportShareStatRequest{
	string story_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message ReportShareStatResponse{

}

message BatchGetItemSummariesRequest {
	repeated string item_ids = 1[(validate.rules) = {
		repeated: {
			min_items: 1, 
			max_items: 20,
			unique: true,
			items: {
				string: {
					pattern: "^[0-9]+$"
				}
			}
		}
	}];
}

message BatchGetItemSummariesResponse {
	repeated api.items.types.v1.ItemSummary items = 1;
}	

message CreateItemRequest {
	string title = 1;
	string description = 2;
	string cover_image_object_key = 3;
	// bgm 音频播放信息
	message Bgm {
		// 裁剪的原始音乐
		api.items.music.types.v1.Song song = 1;
		// 裁剪后的播放对象 key 
		string audio_play_key = 2;
	}
	optional Bgm bgm = 4;	
	repeated api.artwork.ArtSketchBoard sketchboards = 5;
}
message CreateItemResponse {
	api.items.types.v1.ItemDetail item = 1;
}

message HomePageTimelineRequest {
	api.common.v1.ListRequest list_request = 1[(validate.rules).message.required = true];
}

message HomePageTimelineResponse {
	enum CardTag {
		CARD_TAG_UNSPECIFIED = 0;
		// FRIEND 
		CARD_TAG_FRIEND = 1;
	}
	message TimelineCard {
		// 用于目前 timeline 右上角显示 tag 信息
		CardTag tag = 1;
		api.items.types.v1.ItemSummary item = 2;
	}
	repeated TimelineCard timeline_cards = 1;
	api.common.v1.ListResponse list_response = 2;
}


message AsrRequest {
	string audio_object_key = 1;
	// 相似度阈值，默认0.8，如果客户端传了此值，返回值服务端会自己计算相似度并返回是否匹配，否则返回值里的 matched 无意义
	optional float similarity = 2;
	// 期望的文本，如果客户端传了此值，返回值服务端会自己计算相似度并返回是否匹配，否则返回值里的 matched 无意义
	optional string expected_txt = 3;
}

message AsrResponse {
	string text = 1;
	// 是否匹配
	bool matched = 2;
}
