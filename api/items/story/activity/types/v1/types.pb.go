// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/activity/types/v1/types.proto

package api_items_story_activity_types_v1

import (
	v12 "boson/api/items/comments/types/v1"
	v11 "boson/api/items/story/types/v1"
	v1 "boson/api/users/info/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumptionStatus int32

const (
	ConsumptionStatus_CONSUMPTION_STATUS_UNSPECIFIED ConsumptionStatus = 0
	ConsumptionStatus_CONSUMPTION_STATUS_VIEWED      ConsumptionStatus = 1
	ConsumptionStatus_CONSUMPTION_STATUS_PLAYED      ConsumptionStatus = 2
	ConsumptionStatus_CONSUMPTION_STATUS_UNLOCKED    ConsumptionStatus = 3
)

// Enum value maps for ConsumptionStatus.
var (
	ConsumptionStatus_name = map[int32]string{
		0: "CONSUMPTION_STATUS_UNSPECIFIED",
		1: "CONSUMPTION_STATUS_VIEWED",
		2: "CONSUMPTION_STATUS_PLAYED",
		3: "CONSUMPTION_STATUS_UNLOCKED",
	}
	ConsumptionStatus_value = map[string]int32{
		"CONSUMPTION_STATUS_UNSPECIFIED": 0,
		"CONSUMPTION_STATUS_VIEWED":      1,
		"CONSUMPTION_STATUS_PLAYED":      2,
		"CONSUMPTION_STATUS_UNLOCKED":    3,
	}
)

func (x ConsumptionStatus) Enum() *ConsumptionStatus {
	p := new(ConsumptionStatus)
	*p = x
	return p
}

func (x ConsumptionStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumptionStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_activity_types_v1_types_proto_enumTypes[0].Descriptor()
}

func (ConsumptionStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_activity_types_v1_types_proto_enumTypes[0]
}

func (x ConsumptionStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsumptionStatus.Descriptor instead.
func (ConsumptionStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_activity_types_v1_types_proto_rawDescGZIP(), []int{0}
}

type ActivityType int32

const (
	ActivityType_ACTIVITY_TYPE_UNSPECIFIED ActivityType = 0
	// Someone liked your story
	ActivityType_ACTIVITY_TYPE_STORY_LIKE ActivityType = 1
	// Someone commented on your story
	ActivityType_ACTIVITY_TYPE_STORY_COMMENT ActivityType = 2
)

// Enum value maps for ActivityType.
var (
	ActivityType_name = map[int32]string{
		0: "ACTIVITY_TYPE_UNSPECIFIED",
		1: "ACTIVITY_TYPE_STORY_LIKE",
		2: "ACTIVITY_TYPE_STORY_COMMENT",
	}
	ActivityType_value = map[string]int32{
		"ACTIVITY_TYPE_UNSPECIFIED":   0,
		"ACTIVITY_TYPE_STORY_LIKE":    1,
		"ACTIVITY_TYPE_STORY_COMMENT": 2,
	}
)

func (x ActivityType) Enum() *ActivityType {
	p := new(ActivityType)
	*p = x
	return p
}

func (x ActivityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_activity_types_v1_types_proto_enumTypes[1].Descriptor()
}

func (ActivityType) Type() protoreflect.EnumType {
	return &file_api_items_story_activity_types_v1_types_proto_enumTypes[1]
}

func (x ActivityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityType.Descriptor instead.
func (ActivityType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_activity_types_v1_types_proto_rawDescGZIP(), []int{1}
}

// A single activity directed to the login user (receiver)
type ActivityItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Activity id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The user who performed the activity
	Actor *v1.UserInfoSummary `protobuf:"bytes,2,opt,name=actor,proto3" json:"actor,omitempty"`
	// The story that received this activity
	Story *v11.StoryDetail `protobuf:"bytes,3,opt,name=story,proto3" json:"story,omitempty"`
	// Activity type
	Type ActivityType `protobuf:"varint,4,opt,name=type,proto3,enum=api.items.story.activity.types.v1.ActivityType" json:"type,omitempty"`
	// Created at (unix seconds)
	CreatedAtTimestamp uint32 `protobuf:"varint,5,opt,name=created_at_timestamp,json=createdAtTimestamp,proto3" json:"created_at_timestamp,omitempty"`
	// Types that are valid to be assigned to Detail:
	//
	//	*ActivityItem_StoryLike
	//	*ActivityItem_StoryComment
	Detail        isActivityItem_Detail `protobuf_oneof:"detail"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivityItem) Reset() {
	*x = ActivityItem{}
	mi := &file_api_items_story_activity_types_v1_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivityItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivityItem) ProtoMessage() {}

func (x *ActivityItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_types_v1_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivityItem.ProtoReflect.Descriptor instead.
func (*ActivityItem) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_types_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *ActivityItem) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ActivityItem) GetActor() *v1.UserInfoSummary {
	if x != nil {
		return x.Actor
	}
	return nil
}

func (x *ActivityItem) GetStory() *v11.StoryDetail {
	if x != nil {
		return x.Story
	}
	return nil
}

func (x *ActivityItem) GetType() ActivityType {
	if x != nil {
		return x.Type
	}
	return ActivityType_ACTIVITY_TYPE_UNSPECIFIED
}

func (x *ActivityItem) GetCreatedAtTimestamp() uint32 {
	if x != nil {
		return x.CreatedAtTimestamp
	}
	return 0
}

func (x *ActivityItem) GetDetail() isActivityItem_Detail {
	if x != nil {
		return x.Detail
	}
	return nil
}

func (x *ActivityItem) GetStoryLike() *StoryLikeActivity {
	if x != nil {
		if x, ok := x.Detail.(*ActivityItem_StoryLike); ok {
			return x.StoryLike
		}
	}
	return nil
}

func (x *ActivityItem) GetStoryComment() *StoryCommentActivity {
	if x != nil {
		if x, ok := x.Detail.(*ActivityItem_StoryComment); ok {
			return x.StoryComment
		}
	}
	return nil
}

type isActivityItem_Detail interface {
	isActivityItem_Detail()
}

type ActivityItem_StoryLike struct {
	StoryLike *StoryLikeActivity `protobuf:"bytes,10,opt,name=story_like,json=storyLike,proto3,oneof"`
}

type ActivityItem_StoryComment struct {
	StoryComment *StoryCommentActivity `protobuf:"bytes,11,opt,name=story_comment,json=storyComment,proto3,oneof"`
}

func (*ActivityItem_StoryLike) isActivityItem_Detail() {}

func (*ActivityItem_StoryComment) isActivityItem_Detail() {}

// Like details
type StoryLikeActivity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Emoji         string                 `protobuf:"bytes,1,opt,name=emoji,proto3" json:"emoji,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryLikeActivity) Reset() {
	*x = StoryLikeActivity{}
	mi := &file_api_items_story_activity_types_v1_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryLikeActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryLikeActivity) ProtoMessage() {}

func (x *StoryLikeActivity) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_types_v1_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryLikeActivity.ProtoReflect.Descriptor instead.
func (*StoryLikeActivity) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_types_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *StoryLikeActivity) GetEmoji() string {
	if x != nil {
		return x.Emoji
	}
	return ""
}

// Comment details
type StoryCommentActivity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Comment       *v12.Comment           `protobuf:"bytes,1,opt,name=comment,proto3" json:"comment,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryCommentActivity) Reset() {
	*x = StoryCommentActivity{}
	mi := &file_api_items_story_activity_types_v1_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryCommentActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryCommentActivity) ProtoMessage() {}

func (x *StoryCommentActivity) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_types_v1_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryCommentActivity.ProtoReflect.Descriptor instead.
func (*StoryCommentActivity) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_types_v1_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryCommentActivity) GetComment() *v12.Comment {
	if x != nil {
		return x.Comment
	}
	return nil
}

var File_api_items_story_activity_types_v1_types_proto protoreflect.FileDescriptor

const file_api_items_story_activity_types_v1_types_proto_rawDesc = "" +
	"\n" +
	"-api/items/story/activity/types/v1/types.proto\x12!api.items.story.activity.types.v1\x1a#api/users/info/types/v1/types.proto\x1a$api/items/story/types/v1/types.proto\x1a'api/items/comments/types/v1/types.proto\"\xd3\x03\n" +
	"\fActivityItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12>\n" +
	"\x05actor\x18\x02 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\x05actor\x12;\n" +
	"\x05story\x18\x03 \x01(\v2%.api.items.story.types.v1.StoryDetailR\x05story\x12C\n" +
	"\x04type\x18\x04 \x01(\x0e2/.api.items.story.activity.types.v1.ActivityTypeR\x04type\x120\n" +
	"\x14created_at_timestamp\x18\x05 \x01(\rR\x12createdAtTimestamp\x12U\n" +
	"\n" +
	"story_like\x18\n" +
	" \x01(\v24.api.items.story.activity.types.v1.StoryLikeActivityH\x00R\tstoryLike\x12^\n" +
	"\rstory_comment\x18\v \x01(\v27.api.items.story.activity.types.v1.StoryCommentActivityH\x00R\fstoryCommentB\b\n" +
	"\x06detail\")\n" +
	"\x11StoryLikeActivity\x12\x14\n" +
	"\x05emoji\x18\x01 \x01(\tR\x05emoji\"V\n" +
	"\x14StoryCommentActivity\x12>\n" +
	"\acomment\x18\x01 \x01(\v2$.api.items.comments.types.v1.CommentR\acomment*\x96\x01\n" +
	"\x11ConsumptionStatus\x12\"\n" +
	"\x1eCONSUMPTION_STATUS_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19CONSUMPTION_STATUS_VIEWED\x10\x01\x12\x1d\n" +
	"\x19CONSUMPTION_STATUS_PLAYED\x10\x02\x12\x1f\n" +
	"\x1bCONSUMPTION_STATUS_UNLOCKED\x10\x03*l\n" +
	"\fActivityType\x12\x1d\n" +
	"\x19ACTIVITY_TYPE_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18ACTIVITY_TYPE_STORY_LIKE\x10\x01\x12\x1f\n" +
	"\x1bACTIVITY_TYPE_STORY_COMMENT\x10\x02BKZIboson/api/items/story/activity/types/v1;api_items_story_activity_types_v1b\x06proto3"

var (
	file_api_items_story_activity_types_v1_types_proto_rawDescOnce sync.Once
	file_api_items_story_activity_types_v1_types_proto_rawDescData []byte
)

func file_api_items_story_activity_types_v1_types_proto_rawDescGZIP() []byte {
	file_api_items_story_activity_types_v1_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_activity_types_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_activity_types_v1_types_proto_rawDesc), len(file_api_items_story_activity_types_v1_types_proto_rawDesc)))
	})
	return file_api_items_story_activity_types_v1_types_proto_rawDescData
}

var file_api_items_story_activity_types_v1_types_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_items_story_activity_types_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_items_story_activity_types_v1_types_proto_goTypes = []any{
	(ConsumptionStatus)(0),       // 0: api.items.story.activity.types.v1.ConsumptionStatus
	(ActivityType)(0),            // 1: api.items.story.activity.types.v1.ActivityType
	(*ActivityItem)(nil),         // 2: api.items.story.activity.types.v1.ActivityItem
	(*StoryLikeActivity)(nil),    // 3: api.items.story.activity.types.v1.StoryLikeActivity
	(*StoryCommentActivity)(nil), // 4: api.items.story.activity.types.v1.StoryCommentActivity
	(*v1.UserInfoSummary)(nil),   // 5: api.users.info.types.v1.UserInfoSummary
	(*v11.StoryDetail)(nil),      // 6: api.items.story.types.v1.StoryDetail
	(*v12.Comment)(nil),          // 7: api.items.comments.types.v1.Comment
}
var file_api_items_story_activity_types_v1_types_proto_depIdxs = []int32{
	5, // 0: api.items.story.activity.types.v1.ActivityItem.actor:type_name -> api.users.info.types.v1.UserInfoSummary
	6, // 1: api.items.story.activity.types.v1.ActivityItem.story:type_name -> api.items.story.types.v1.StoryDetail
	1, // 2: api.items.story.activity.types.v1.ActivityItem.type:type_name -> api.items.story.activity.types.v1.ActivityType
	3, // 3: api.items.story.activity.types.v1.ActivityItem.story_like:type_name -> api.items.story.activity.types.v1.StoryLikeActivity
	4, // 4: api.items.story.activity.types.v1.ActivityItem.story_comment:type_name -> api.items.story.activity.types.v1.StoryCommentActivity
	7, // 5: api.items.story.activity.types.v1.StoryCommentActivity.comment:type_name -> api.items.comments.types.v1.Comment
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_items_story_activity_types_v1_types_proto_init() }
func file_api_items_story_activity_types_v1_types_proto_init() {
	if File_api_items_story_activity_types_v1_types_proto != nil {
		return
	}
	file_api_items_story_activity_types_v1_types_proto_msgTypes[0].OneofWrappers = []any{
		(*ActivityItem_StoryLike)(nil),
		(*ActivityItem_StoryComment)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_activity_types_v1_types_proto_rawDesc), len(file_api_items_story_activity_types_v1_types_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_activity_types_v1_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_activity_types_v1_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_activity_types_v1_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_activity_types_v1_types_proto_msgTypes,
	}.Build()
	File_api_items_story_activity_types_v1_types_proto = out.File
	file_api_items_story_activity_types_v1_types_proto_goTypes = nil
	file_api_items_story_activity_types_v1_types_proto_depIdxs = nil
}
