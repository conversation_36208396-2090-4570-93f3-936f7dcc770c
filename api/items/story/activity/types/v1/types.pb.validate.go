// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/activity/types/v1/types.proto

package api_items_story_activity_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ActivityItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActivityItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActivityItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ActivityItemMultiError, or
// nil if none found.
func (m *ActivityItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ActivityItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetActor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivityItemValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivityItemValidationError{
					field:  "Actor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivityItemValidationError{
				field:  "Actor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActivityItemValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActivityItemValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActivityItemValidationError{
				field:  "Story",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for CreatedAtTimestamp

	switch v := m.Detail.(type) {
	case *ActivityItem_StoryLike:
		if v == nil {
			err := ActivityItemValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryLike()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityItemValidationError{
						field:  "StoryLike",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityItemValidationError{
						field:  "StoryLike",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryLike()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityItemValidationError{
					field:  "StoryLike",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActivityItem_StoryComment:
		if v == nil {
			err := ActivityItemValidationError{
				field:  "Detail",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStoryComment()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActivityItemValidationError{
						field:  "StoryComment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActivityItemValidationError{
						field:  "StoryComment",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStoryComment()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActivityItemValidationError{
					field:  "StoryComment",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActivityItemMultiError(errors)
	}

	return nil
}

// ActivityItemMultiError is an error wrapping multiple validation errors
// returned by ActivityItem.ValidateAll() if the designated constraints aren't met.
type ActivityItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActivityItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActivityItemMultiError) AllErrors() []error { return m }

// ActivityItemValidationError is the validation error returned by
// ActivityItem.Validate if the designated constraints aren't met.
type ActivityItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActivityItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActivityItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActivityItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActivityItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActivityItemValidationError) ErrorName() string { return "ActivityItemValidationError" }

// Error satisfies the builtin error interface
func (e ActivityItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActivityItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActivityItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActivityItemValidationError{}

// Validate checks the field values on StoryLikeActivity with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StoryLikeActivity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryLikeActivity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryLikeActivityMultiError, or nil if none found.
func (m *StoryLikeActivity) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryLikeActivity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Emoji

	if len(errors) > 0 {
		return StoryLikeActivityMultiError(errors)
	}

	return nil
}

// StoryLikeActivityMultiError is an error wrapping multiple validation errors
// returned by StoryLikeActivity.ValidateAll() if the designated constraints
// aren't met.
type StoryLikeActivityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryLikeActivityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryLikeActivityMultiError) AllErrors() []error { return m }

// StoryLikeActivityValidationError is the validation error returned by
// StoryLikeActivity.Validate if the designated constraints aren't met.
type StoryLikeActivityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryLikeActivityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryLikeActivityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryLikeActivityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryLikeActivityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryLikeActivityValidationError) ErrorName() string {
	return "StoryLikeActivityValidationError"
}

// Error satisfies the builtin error interface
func (e StoryLikeActivityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryLikeActivity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryLikeActivityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryLikeActivityValidationError{}

// Validate checks the field values on StoryCommentActivity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryCommentActivity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryCommentActivity with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryCommentActivityMultiError, or nil if none found.
func (m *StoryCommentActivity) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryCommentActivity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetComment()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryCommentActivityValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryCommentActivityValidationError{
					field:  "Comment",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComment()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryCommentActivityValidationError{
				field:  "Comment",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoryCommentActivityMultiError(errors)
	}

	return nil
}

// StoryCommentActivityMultiError is an error wrapping multiple validation
// errors returned by StoryCommentActivity.ValidateAll() if the designated
// constraints aren't met.
type StoryCommentActivityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryCommentActivityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryCommentActivityMultiError) AllErrors() []error { return m }

// StoryCommentActivityValidationError is the validation error returned by
// StoryCommentActivity.Validate if the designated constraints aren't met.
type StoryCommentActivityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryCommentActivityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryCommentActivityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryCommentActivityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryCommentActivityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryCommentActivityValidationError) ErrorName() string {
	return "StoryCommentActivityValidationError"
}

// Error satisfies the builtin error interface
func (e StoryCommentActivityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryCommentActivity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryCommentActivityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryCommentActivityValidationError{}
