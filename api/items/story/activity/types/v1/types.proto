syntax = "proto3";

package api.items.story.activity.types.v1;

option go_package = "boson/api/items/story/activity/types/v1;api_items_story_activity_types_v1";

import "api/users/info/types/v1/types.proto";
import "api/items/story/types/v1/types.proto";
import "api/items/comments/types/v1/types.proto";

enum ConsumptionStatus {
  CONSUMPTION_STATUS_UNSPECIFIED = 0;
  CONSUMPTION_STATUS_VIEWED = 1;
  CONSUMPTION_STATUS_PLAYED = 2;
  CONSUMPTION_STATUS_UNLOCKED = 3;
}

enum ActivityType {
  ACTIVITY_TYPE_UNSPECIFIED = 0;
  // Someone liked your story
  ACTIVITY_TYPE_STORY_LIKE = 1;
  // Someone commented on your story
  ACTIVITY_TYPE_STORY_COMMENT = 2;
}

// A single activity directed to the login user (receiver)
message ActivityItem {
  // Activity id
  string id = 1;
  // The user who performed the activity
  api.users.info.types.v1.UserInfoSummary actor = 2;
  // The story that received this activity
  api.items.story.types.v1.StoryDetail story = 3;
  // Activity type
  ActivityType type = 4;
  // Created at (unix seconds)
  uint32 created_at_timestamp = 5;

  oneof detail {
    StoryLikeActivity story_like = 10;
    StoryCommentActivity story_comment = 11;
  }
}

// Like details
message StoryLikeActivity {
  string emoji = 1;
}

// Comment details
message StoryCommentActivity {
  api.items.comments.types.v1.Comment comment = 1;
}