// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/activity/v1/activity.proto

package api_items_story_activity_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	api_items_story_activity_types_v1 "boson/api/items/story/activity/types/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = api_items_story_activity_types_v1.ConsumptionStatus(0)
)

// Validate checks the field values on ListUsersByConsumptionStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListUsersByConsumptionStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUsersByConsumptionStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListUsersByConsumptionStatusRequestMultiError, or nil if none found.
func (m *ListUsersByConsumptionStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUsersByConsumptionStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ListUsersByConsumptionStatusRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := ListUsersByConsumptionStatusRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetListRequest() == nil {
		err := ListUsersByConsumptionStatusRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUsersByConsumptionStatusRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUsersByConsumptionStatusRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUsersByConsumptionStatusRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsumptionStatus

	if len(errors) > 0 {
		return ListUsersByConsumptionStatusRequestMultiError(errors)
	}

	return nil
}

// ListUsersByConsumptionStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListUsersByConsumptionStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUsersByConsumptionStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUsersByConsumptionStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUsersByConsumptionStatusRequestMultiError) AllErrors() []error { return m }

// ListUsersByConsumptionStatusRequestValidationError is the validation error
// returned by ListUsersByConsumptionStatusRequest.Validate if the designated
// constraints aren't met.
type ListUsersByConsumptionStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUsersByConsumptionStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUsersByConsumptionStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUsersByConsumptionStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUsersByConsumptionStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUsersByConsumptionStatusRequestValidationError) ErrorName() string {
	return "ListUsersByConsumptionStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUsersByConsumptionStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUsersByConsumptionStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUsersByConsumptionStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUsersByConsumptionStatusRequestValidationError{}

var _ListUsersByConsumptionStatusRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ListUsersByConsumptionStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListUsersByConsumptionStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUsersByConsumptionStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListUsersByConsumptionStatusResponseMultiError, or nil if none found.
func (m *ListUsersByConsumptionStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUsersByConsumptionStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUsersByConsumptionStatusResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUsersByConsumptionStatusResponseValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUsersByConsumptionStatusResponseValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUsersByConsumptionStatusResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUsersByConsumptionStatusResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUsersByConsumptionStatusResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListUsersByConsumptionStatusResponseMultiError(errors)
	}

	return nil
}

// ListUsersByConsumptionStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListUsersByConsumptionStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type ListUsersByConsumptionStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUsersByConsumptionStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUsersByConsumptionStatusResponseMultiError) AllErrors() []error { return m }

// ListUsersByConsumptionStatusResponseValidationError is the validation error
// returned by ListUsersByConsumptionStatusResponse.Validate if the designated
// constraints aren't met.
type ListUsersByConsumptionStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUsersByConsumptionStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUsersByConsumptionStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUsersByConsumptionStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUsersByConsumptionStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUsersByConsumptionStatusResponseValidationError) ErrorName() string {
	return "ListUsersByConsumptionStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListUsersByConsumptionStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUsersByConsumptionStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUsersByConsumptionStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUsersByConsumptionStatusResponseValidationError{}

// Validate checks the field values on ListActivitiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListActivitiesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListActivitiesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListActivitiesRequestMultiError, or nil if none found.
func (m *ListActivitiesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListActivitiesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListActivitiesRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListActivitiesRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListActivitiesRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListActivitiesRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListActivitiesRequestMultiError(errors)
	}

	return nil
}

// ListActivitiesRequestMultiError is an error wrapping multiple validation
// errors returned by ListActivitiesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListActivitiesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListActivitiesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListActivitiesRequestMultiError) AllErrors() []error { return m }

// ListActivitiesRequestValidationError is the validation error returned by
// ListActivitiesRequest.Validate if the designated constraints aren't met.
type ListActivitiesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListActivitiesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListActivitiesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListActivitiesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListActivitiesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListActivitiesRequestValidationError) ErrorName() string {
	return "ListActivitiesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListActivitiesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListActivitiesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListActivitiesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListActivitiesRequestValidationError{}

// Validate checks the field values on ListActivitiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListActivitiesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListActivitiesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListActivitiesResponseMultiError, or nil if none found.
func (m *ListActivitiesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListActivitiesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetActivities() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListActivitiesResponseValidationError{
						field:  fmt.Sprintf("Activities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListActivitiesResponseValidationError{
						field:  fmt.Sprintf("Activities[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListActivitiesResponseValidationError{
					field:  fmt.Sprintf("Activities[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListActivitiesResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListActivitiesResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListActivitiesResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListActivitiesResponseMultiError(errors)
	}

	return nil
}

// ListActivitiesResponseMultiError is an error wrapping multiple validation
// errors returned by ListActivitiesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListActivitiesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListActivitiesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListActivitiesResponseMultiError) AllErrors() []error { return m }

// ListActivitiesResponseValidationError is the validation error returned by
// ListActivitiesResponse.Validate if the designated constraints aren't met.
type ListActivitiesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListActivitiesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListActivitiesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListActivitiesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListActivitiesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListActivitiesResponseValidationError) ErrorName() string {
	return "ListActivitiesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListActivitiesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListActivitiesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListActivitiesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListActivitiesResponseValidationError{}

// Validate checks the field values on GetActivityUnreadCountRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivityUnreadCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivityUnreadCountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActivityUnreadCountRequestMultiError, or nil if none found.
func (m *GetActivityUnreadCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivityUnreadCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetActivityUnreadCountRequestMultiError(errors)
	}

	return nil
}

// GetActivityUnreadCountRequestMultiError is an error wrapping multiple
// validation errors returned by GetActivityUnreadCountRequest.ValidateAll()
// if the designated constraints aren't met.
type GetActivityUnreadCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivityUnreadCountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivityUnreadCountRequestMultiError) AllErrors() []error { return m }

// GetActivityUnreadCountRequestValidationError is the validation error
// returned by GetActivityUnreadCountRequest.Validate if the designated
// constraints aren't met.
type GetActivityUnreadCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivityUnreadCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivityUnreadCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivityUnreadCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivityUnreadCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivityUnreadCountRequestValidationError) ErrorName() string {
	return "GetActivityUnreadCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivityUnreadCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivityUnreadCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivityUnreadCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivityUnreadCountRequestValidationError{}

// Validate checks the field values on GetActivityUnreadCountResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetActivityUnreadCountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetActivityUnreadCountResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetActivityUnreadCountResponseMultiError, or nil if none found.
func (m *GetActivityUnreadCountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActivityUnreadCountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return GetActivityUnreadCountResponseMultiError(errors)
	}

	return nil
}

// GetActivityUnreadCountResponseMultiError is an error wrapping multiple
// validation errors returned by GetActivityUnreadCountResponse.ValidateAll()
// if the designated constraints aren't met.
type GetActivityUnreadCountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActivityUnreadCountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActivityUnreadCountResponseMultiError) AllErrors() []error { return m }

// GetActivityUnreadCountResponseValidationError is the validation error
// returned by GetActivityUnreadCountResponse.Validate if the designated
// constraints aren't met.
type GetActivityUnreadCountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActivityUnreadCountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActivityUnreadCountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActivityUnreadCountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActivityUnreadCountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActivityUnreadCountResponseValidationError) ErrorName() string {
	return "GetActivityUnreadCountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActivityUnreadCountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActivityUnreadCountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActivityUnreadCountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActivityUnreadCountResponseValidationError{}

// Validate checks the field values on ReportActivityReadRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportActivityReadRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportActivityReadRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportActivityReadRequestMultiError, or nil if none found.
func (m *ReportActivityReadRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportActivityReadRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReportActivityReadRequestMultiError(errors)
	}

	return nil
}

// ReportActivityReadRequestMultiError is an error wrapping multiple validation
// errors returned by ReportActivityReadRequest.ValidateAll() if the
// designated constraints aren't met.
type ReportActivityReadRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportActivityReadRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportActivityReadRequestMultiError) AllErrors() []error { return m }

// ReportActivityReadRequestValidationError is the validation error returned by
// ReportActivityReadRequest.Validate if the designated constraints aren't met.
type ReportActivityReadRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportActivityReadRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportActivityReadRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportActivityReadRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportActivityReadRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportActivityReadRequestValidationError) ErrorName() string {
	return "ReportActivityReadRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReportActivityReadRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportActivityReadRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportActivityReadRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportActivityReadRequestValidationError{}

// Validate checks the field values on ReportActivityReadResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportActivityReadResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportActivityReadResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportActivityReadResponseMultiError, or nil if none found.
func (m *ReportActivityReadResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportActivityReadResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ReportActivityReadResponseMultiError(errors)
	}

	return nil
}

// ReportActivityReadResponseMultiError is an error wrapping multiple
// validation errors returned by ReportActivityReadResponse.ValidateAll() if
// the designated constraints aren't met.
type ReportActivityReadResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportActivityReadResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportActivityReadResponseMultiError) AllErrors() []error { return m }

// ReportActivityReadResponseValidationError is the validation error returned
// by ReportActivityReadResponse.Validate if the designated constraints aren't met.
type ReportActivityReadResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportActivityReadResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportActivityReadResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportActivityReadResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportActivityReadResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportActivityReadResponseValidationError) ErrorName() string {
	return "ReportActivityReadResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReportActivityReadResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportActivityReadResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportActivityReadResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportActivityReadResponseValidationError{}
