syntax = "proto3";

package api.items.story.activity.v1;

option go_package = "boson/api/items/story/activity/v1;api_items_story_activity_v1";

import "api/users/info/types/v1/types.proto";
import "api/common/v1/common.proto";
import "api/items/story/activity/types/v1/types.proto";
import "validate/validate.proto";

message ListUsersByConsumptionStatusRequest {
  string story_id = 1[(validate.rules).string = {
    // only number
    pattern: "^[0-9]+$",
  }];
  api.common.v1.ListRequest list_request = 2[(validate.rules).message = {
    required: true,
  }];
  api.items.story.activity.types.v1.ConsumptionStatus consumption_status = 3;
}

message ListUsersByConsumptionStatusResponse {
  repeated api.users.info.types.v1.UserInfoSummary users = 1;
  api.common.v1.ListResponse list_response = 2;
}

message ListActivitiesRequest {
  api.common.v1.ListRequest list_request = 1 [(validate.rules).message = { required: true }];
}

message ListActivitiesResponse {
  repeated api.items.story.activity.types.v1.ActivityItem activities = 1;
  api.common.v1.ListResponse list_response = 2;
}

// Get unread activity count for my stories
message GetActivityUnreadCountRequest {}

message GetActivityUnreadCountResponse {
  uint32 count = 1;
}

// Mark activities as read up to latest
message ReportActivityReadRequest {}

message ReportActivityReadResponse {}
