// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/activity/v1/activity.proto

package api_items_story_activity_v1

import (
	v1 "boson/api/common/v1"
	v11 "boson/api/items/story/activity/types/v1"
	v12 "boson/api/users/info/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ListUsersByConsumptionStatusRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	StoryId           string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ListRequest       *v1.ListRequest        `protobuf:"bytes,2,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	ConsumptionStatus v11.ConsumptionStatus  `protobuf:"varint,3,opt,name=consumption_status,json=consumptionStatus,proto3,enum=api.items.story.activity.types.v1.ConsumptionStatus" json:"consumption_status,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListUsersByConsumptionStatusRequest) Reset() {
	*x = ListUsersByConsumptionStatusRequest{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersByConsumptionStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersByConsumptionStatusRequest) ProtoMessage() {}

func (x *ListUsersByConsumptionStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersByConsumptionStatusRequest.ProtoReflect.Descriptor instead.
func (*ListUsersByConsumptionStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{0}
}

func (x *ListUsersByConsumptionStatusRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ListUsersByConsumptionStatusRequest) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

func (x *ListUsersByConsumptionStatusRequest) GetConsumptionStatus() v11.ConsumptionStatus {
	if x != nil {
		return x.ConsumptionStatus
	}
	return v11.ConsumptionStatus(0)
}

type ListUsersByConsumptionStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*v12.UserInfoSummary `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	ListResponse  *v1.ListResponse       `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersByConsumptionStatusResponse) Reset() {
	*x = ListUsersByConsumptionStatusResponse{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersByConsumptionStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersByConsumptionStatusResponse) ProtoMessage() {}

func (x *ListUsersByConsumptionStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersByConsumptionStatusResponse.ProtoReflect.Descriptor instead.
func (*ListUsersByConsumptionStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{1}
}

func (x *ListUsersByConsumptionStatusResponse) GetUsers() []*v12.UserInfoSummary {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *ListUsersByConsumptionStatusResponse) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ListActivitiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v1.ListRequest        `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActivitiesRequest) Reset() {
	*x = ListActivitiesRequest{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActivitiesRequest) ProtoMessage() {}

func (x *ListActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActivitiesRequest.ProtoReflect.Descriptor instead.
func (*ListActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{2}
}

func (x *ListActivitiesRequest) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListActivitiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Activities    []*v11.ActivityItem    `protobuf:"bytes,1,rep,name=activities,proto3" json:"activities,omitempty"`
	ListResponse  *v1.ListResponse       `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActivitiesResponse) Reset() {
	*x = ListActivitiesResponse{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActivitiesResponse) ProtoMessage() {}

func (x *ListActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActivitiesResponse.ProtoReflect.Descriptor instead.
func (*ListActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{3}
}

func (x *ListActivitiesResponse) GetActivities() []*v11.ActivityItem {
	if x != nil {
		return x.Activities
	}
	return nil
}

func (x *ListActivitiesResponse) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

// Get unread activity count for my stories
type GetActivityUnreadCountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActivityUnreadCountRequest) Reset() {
	*x = GetActivityUnreadCountRequest{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActivityUnreadCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityUnreadCountRequest) ProtoMessage() {}

func (x *GetActivityUnreadCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityUnreadCountRequest.ProtoReflect.Descriptor instead.
func (*GetActivityUnreadCountRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{4}
}

type GetActivityUnreadCountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         uint32                 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActivityUnreadCountResponse) Reset() {
	*x = GetActivityUnreadCountResponse{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActivityUnreadCountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivityUnreadCountResponse) ProtoMessage() {}

func (x *GetActivityUnreadCountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivityUnreadCountResponse.ProtoReflect.Descriptor instead.
func (*GetActivityUnreadCountResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{5}
}

func (x *GetActivityUnreadCountResponse) GetCount() uint32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// Mark activities as read up to latest
type ReportActivityReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportActivityReadRequest) Reset() {
	*x = ReportActivityReadRequest{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportActivityReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportActivityReadRequest) ProtoMessage() {}

func (x *ReportActivityReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportActivityReadRequest.ProtoReflect.Descriptor instead.
func (*ReportActivityReadRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{6}
}

type ReportActivityReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportActivityReadResponse) Reset() {
	*x = ReportActivityReadResponse{}
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportActivityReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportActivityReadResponse) ProtoMessage() {}

func (x *ReportActivityReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_activity_v1_activity_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportActivityReadResponse.ProtoReflect.Descriptor instead.
func (*ReportActivityReadResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_activity_v1_activity_proto_rawDescGZIP(), []int{7}
}

var File_api_items_story_activity_v1_activity_proto protoreflect.FileDescriptor

const file_api_items_story_activity_v1_activity_proto_rawDesc = "" +
	"\n" +
	"*api/items/story/activity/v1/activity.proto\x12\x1bapi.items.story.activity.v1\x1a#api/users/info/types/v1/types.proto\x1a\x1aapi/common/v1/common.proto\x1a-api/items/story/activity/types/v1/types.proto\x1a\x17validate/validate.proto\"\xff\x01\n" +
	"#ListUsersByConsumptionStatusRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12G\n" +
	"\flist_request\x18\x02 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\x12c\n" +
	"\x12consumption_status\x18\x03 \x01(\x0e24.api.items.story.activity.types.v1.ConsumptionStatusR\x11consumptionStatus\"\xa8\x01\n" +
	"$ListUsersByConsumptionStatusResponse\x12>\n" +
	"\x05users\x18\x01 \x03(\v2(.api.users.info.types.v1.UserInfoSummaryR\x05users\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"`\n" +
	"\x15ListActivitiesRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xab\x01\n" +
	"\x16ListActivitiesResponse\x12O\n" +
	"\n" +
	"activities\x18\x01 \x03(\v2/.api.items.story.activity.types.v1.ActivityItemR\n" +
	"activities\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"\x1f\n" +
	"\x1dGetActivityUnreadCountRequest\"6\n" +
	"\x1eGetActivityUnreadCountResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\rR\x05count\"\x1b\n" +
	"\x19ReportActivityReadRequest\"\x1c\n" +
	"\x1aReportActivityReadResponseB?Z=boson/api/items/story/activity/v1;api_items_story_activity_v1b\x06proto3"

var (
	file_api_items_story_activity_v1_activity_proto_rawDescOnce sync.Once
	file_api_items_story_activity_v1_activity_proto_rawDescData []byte
)

func file_api_items_story_activity_v1_activity_proto_rawDescGZIP() []byte {
	file_api_items_story_activity_v1_activity_proto_rawDescOnce.Do(func() {
		file_api_items_story_activity_v1_activity_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_activity_v1_activity_proto_rawDesc), len(file_api_items_story_activity_v1_activity_proto_rawDesc)))
	})
	return file_api_items_story_activity_v1_activity_proto_rawDescData
}

var file_api_items_story_activity_v1_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_items_story_activity_v1_activity_proto_goTypes = []any{
	(*ListUsersByConsumptionStatusRequest)(nil),  // 0: api.items.story.activity.v1.ListUsersByConsumptionStatusRequest
	(*ListUsersByConsumptionStatusResponse)(nil), // 1: api.items.story.activity.v1.ListUsersByConsumptionStatusResponse
	(*ListActivitiesRequest)(nil),                // 2: api.items.story.activity.v1.ListActivitiesRequest
	(*ListActivitiesResponse)(nil),               // 3: api.items.story.activity.v1.ListActivitiesResponse
	(*GetActivityUnreadCountRequest)(nil),        // 4: api.items.story.activity.v1.GetActivityUnreadCountRequest
	(*GetActivityUnreadCountResponse)(nil),       // 5: api.items.story.activity.v1.GetActivityUnreadCountResponse
	(*ReportActivityReadRequest)(nil),            // 6: api.items.story.activity.v1.ReportActivityReadRequest
	(*ReportActivityReadResponse)(nil),           // 7: api.items.story.activity.v1.ReportActivityReadResponse
	(*v1.ListRequest)(nil),                       // 8: api.common.v1.ListRequest
	(v11.ConsumptionStatus)(0),                   // 9: api.items.story.activity.types.v1.ConsumptionStatus
	(*v12.UserInfoSummary)(nil),                  // 10: api.users.info.types.v1.UserInfoSummary
	(*v1.ListResponse)(nil),                      // 11: api.common.v1.ListResponse
	(*v11.ActivityItem)(nil),                     // 12: api.items.story.activity.types.v1.ActivityItem
}
var file_api_items_story_activity_v1_activity_proto_depIdxs = []int32{
	8,  // 0: api.items.story.activity.v1.ListUsersByConsumptionStatusRequest.list_request:type_name -> api.common.v1.ListRequest
	9,  // 1: api.items.story.activity.v1.ListUsersByConsumptionStatusRequest.consumption_status:type_name -> api.items.story.activity.types.v1.ConsumptionStatus
	10, // 2: api.items.story.activity.v1.ListUsersByConsumptionStatusResponse.users:type_name -> api.users.info.types.v1.UserInfoSummary
	11, // 3: api.items.story.activity.v1.ListUsersByConsumptionStatusResponse.list_response:type_name -> api.common.v1.ListResponse
	8,  // 4: api.items.story.activity.v1.ListActivitiesRequest.list_request:type_name -> api.common.v1.ListRequest
	12, // 5: api.items.story.activity.v1.ListActivitiesResponse.activities:type_name -> api.items.story.activity.types.v1.ActivityItem
	11, // 6: api.items.story.activity.v1.ListActivitiesResponse.list_response:type_name -> api.common.v1.ListResponse
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_items_story_activity_v1_activity_proto_init() }
func file_api_items_story_activity_v1_activity_proto_init() {
	if File_api_items_story_activity_v1_activity_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_activity_v1_activity_proto_rawDesc), len(file_api_items_story_activity_v1_activity_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_activity_v1_activity_proto_goTypes,
		DependencyIndexes: file_api_items_story_activity_v1_activity_proto_depIdxs,
		MessageInfos:      file_api_items_story_activity_v1_activity_proto_msgTypes,
	}.Build()
	File_api_items_story_activity_v1_activity_proto = out.File
	file_api_items_story_activity_v1_activity_proto_goTypes = nil
	file_api_items_story_activity_v1_activity_proto_depIdxs = nil
}
