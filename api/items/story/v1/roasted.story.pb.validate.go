// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v1/roasted.story.proto

package api_items_story_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ConsumeRoastedTopicRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeRoastedTopicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeRoastedTopicRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeRoastedTopicRequestMultiError, or nil if none found.
func (m *ConsumeRoastedTopicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeRoastedTopicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetQuestionWithUserAnswer() == nil {
		err := ConsumeRoastedTopicRequestValidationError{
			field:  "QuestionWithUserAnswer",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetQuestionWithUserAnswer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeRoastedTopicRequestValidationError{
					field:  "QuestionWithUserAnswer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeRoastedTopicRequestValidationError{
					field:  "QuestionWithUserAnswer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestionWithUserAnswer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeRoastedTopicRequestValidationError{
				field:  "QuestionWithUserAnswer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QuestionCount

	// no validation rules for TotalVideoDurationSeconds

	if m.FromStoryId != nil {

		if !_ConsumeRoastedTopicRequest_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := ConsumeRoastedTopicRequestValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ConsumeRoastedTopicRequestMultiError(errors)
	}

	return nil
}

// ConsumeRoastedTopicRequestMultiError is an error wrapping multiple
// validation errors returned by ConsumeRoastedTopicRequest.ValidateAll() if
// the designated constraints aren't met.
type ConsumeRoastedTopicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeRoastedTopicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeRoastedTopicRequestMultiError) AllErrors() []error { return m }

// ConsumeRoastedTopicRequestValidationError is the validation error returned
// by ConsumeRoastedTopicRequest.Validate if the designated constraints aren't met.
type ConsumeRoastedTopicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeRoastedTopicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeRoastedTopicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeRoastedTopicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeRoastedTopicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeRoastedTopicRequestValidationError) ErrorName() string {
	return "ConsumeRoastedTopicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeRoastedTopicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeRoastedTopicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeRoastedTopicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeRoastedTopicRequestValidationError{}

var _ConsumeRoastedTopicRequest_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ConsumeRoastedTopicResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeRoastedTopicResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeRoastedTopicResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeRoastedTopicResponseMultiError, or nil if none found.
func (m *ConsumeRoastedTopicResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeRoastedTopicResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.NextQuestion != nil {

		if all {
			switch v := interface{}(m.GetNextQuestion()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConsumeRoastedTopicResponseValidationError{
						field:  "NextQuestion",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConsumeRoastedTopicResponseValidationError{
						field:  "NextQuestion",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNextQuestion()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConsumeRoastedTopicResponseValidationError{
					field:  "NextQuestion",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ConsumeRoastedTopicResponseMultiError(errors)
	}

	return nil
}

// ConsumeRoastedTopicResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeRoastedTopicResponse.ValidateAll() if
// the designated constraints aren't met.
type ConsumeRoastedTopicResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeRoastedTopicResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeRoastedTopicResponseMultiError) AllErrors() []error { return m }

// ConsumeRoastedTopicResponseValidationError is the validation error returned
// by ConsumeRoastedTopicResponse.Validate if the designated constraints
// aren't met.
type ConsumeRoastedTopicResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeRoastedTopicResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeRoastedTopicResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeRoastedTopicResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeRoastedTopicResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeRoastedTopicResponseValidationError) ErrorName() string {
	return "ConsumeRoastedTopicResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeRoastedTopicResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeRoastedTopicResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeRoastedTopicResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeRoastedTopicResponseValidationError{}

// Validate checks the field values on ConsumeRoastedStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeRoastedStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeRoastedStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeRoastedStoryRequestMultiError, or nil if none found.
func (m *ConsumeRoastedStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeRoastedStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ConsumeRoastedStoryRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := ConsumeRoastedStoryRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UserStoryId

	if m.UserRecordedVideoKey != nil {
		// no validation rules for UserRecordedVideoKey
	}

	if len(errors) > 0 {
		return ConsumeRoastedStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeRoastedStoryRequestMultiError is an error wrapping multiple
// validation errors returned by ConsumeRoastedStoryRequest.ValidateAll() if
// the designated constraints aren't met.
type ConsumeRoastedStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeRoastedStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeRoastedStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeRoastedStoryRequestValidationError is the validation error returned
// by ConsumeRoastedStoryRequest.Validate if the designated constraints aren't met.
type ConsumeRoastedStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeRoastedStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeRoastedStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeRoastedStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeRoastedStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeRoastedStoryRequestValidationError) ErrorName() string {
	return "ConsumeRoastedStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeRoastedStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeRoastedStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeRoastedStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeRoastedStoryRequestValidationError{}

var _ConsumeRoastedStoryRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ConsumeRoastedStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeRoastedStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeRoastedStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeRoastedStoryResponseMultiError, or nil if none found.
func (m *ConsumeRoastedStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeRoastedStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeRoastedStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeRoastedStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeRoastedStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeRoastedStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeRoastedStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeRoastedStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type ConsumeRoastedStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeRoastedStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeRoastedStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeRoastedStoryResponseValidationError is the validation error returned
// by ConsumeRoastedStoryResponse.Validate if the designated constraints
// aren't met.
type ConsumeRoastedStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeRoastedStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeRoastedStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeRoastedStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeRoastedStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeRoastedStoryResponseValidationError) ErrorName() string {
	return "ConsumeRoastedStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeRoastedStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeRoastedStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeRoastedStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeRoastedStoryResponseValidationError{}

// Validate checks the field values on GetRoastedTopicsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRoastedTopicsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRoastedTopicsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRoastedTopicsRequestMultiError, or nil if none found.
func (m *GetRoastedTopicsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRoastedTopicsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.CopyStoryId != nil {
		// no validation rules for CopyStoryId
	}

	if len(errors) > 0 {
		return GetRoastedTopicsRequestMultiError(errors)
	}

	return nil
}

// GetRoastedTopicsRequestMultiError is an error wrapping multiple validation
// errors returned by GetRoastedTopicsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRoastedTopicsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRoastedTopicsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRoastedTopicsRequestMultiError) AllErrors() []error { return m }

// GetRoastedTopicsRequestValidationError is the validation error returned by
// GetRoastedTopicsRequest.Validate if the designated constraints aren't met.
type GetRoastedTopicsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRoastedTopicsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRoastedTopicsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRoastedTopicsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRoastedTopicsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRoastedTopicsRequestValidationError) ErrorName() string {
	return "GetRoastedTopicsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRoastedTopicsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRoastedTopicsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRoastedTopicsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRoastedTopicsRequestValidationError{}

// Validate checks the field values on GetRoastedTopicsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRoastedTopicsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRoastedTopicsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRoastedTopicsResponseMultiError, or nil if none found.
func (m *GetRoastedTopicsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRoastedTopicsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRoastedTopicsResponseValidationError{
					field:  "Topic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRoastedTopicsResponseValidationError{
					field:  "Topic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRoastedTopicsResponseValidationError{
				field:  "Topic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRoastedTopicsResponseMultiError(errors)
	}

	return nil
}

// GetRoastedTopicsResponseMultiError is an error wrapping multiple validation
// errors returned by GetRoastedTopicsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRoastedTopicsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRoastedTopicsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRoastedTopicsResponseMultiError) AllErrors() []error { return m }

// GetRoastedTopicsResponseValidationError is the validation error returned by
// GetRoastedTopicsResponse.Validate if the designated constraints aren't met.
type GetRoastedTopicsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRoastedTopicsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRoastedTopicsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRoastedTopicsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRoastedTopicsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRoastedTopicsResponseValidationError) ErrorName() string {
	return "GetRoastedTopicsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRoastedTopicsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRoastedTopicsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRoastedTopicsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRoastedTopicsResponseValidationError{}

// Validate checks the field values on CreateRoastedStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRoastedStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRoastedStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRoastedStoryRequestMultiError, or nil if none found.
func (m *CreateRoastedStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRoastedStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPlayConfig() == nil {
		err := CreateRoastedStoryRequestValidationError{
			field:  "PlayConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRoastedStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRoastedStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRoastedStoryRequestValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateRoastedStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateRoastedStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateRoastedStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.FromStoryId != nil {

		if !_CreateRoastedStoryRequest_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := CreateRoastedStoryRequestValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateRoastedStoryRequestMultiError(errors)
	}

	return nil
}

// CreateRoastedStoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreateRoastedStoryRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateRoastedStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRoastedStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRoastedStoryRequestMultiError) AllErrors() []error { return m }

// CreateRoastedStoryRequestValidationError is the validation error returned by
// CreateRoastedStoryRequest.Validate if the designated constraints aren't met.
type CreateRoastedStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRoastedStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRoastedStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRoastedStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRoastedStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRoastedStoryRequestValidationError) ErrorName() string {
	return "CreateRoastedStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRoastedStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRoastedStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRoastedStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRoastedStoryRequestValidationError{}

var _CreateRoastedStoryRequest_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateRoastedStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRoastedStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRoastedStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRoastedStoryResponseMultiError, or nil if none found.
func (m *CreateRoastedStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRoastedStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRoastedStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRoastedStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRoastedStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRoastedStoryResponseMultiError(errors)
	}

	return nil
}

// CreateRoastedStoryResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRoastedStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateRoastedStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRoastedStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRoastedStoryResponseMultiError) AllErrors() []error { return m }

// CreateRoastedStoryResponseValidationError is the validation error returned
// by CreateRoastedStoryResponse.Validate if the designated constraints aren't met.
type CreateRoastedStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRoastedStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRoastedStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRoastedStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRoastedStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRoastedStoryResponseValidationError) ErrorName() string {
	return "CreateRoastedStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRoastedStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRoastedStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRoastedStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRoastedStoryResponseValidationError{}
