// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v1/story.proto

package api_items_story_v1

import (
	v11 "boson/api/common/v1"
	v1 "boson/api/items/story/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumeNowShotStoryRequest_ResourceType int32

const (
	ConsumeNowShotStoryRequest_RESOURCE_TYPE_UNSPECIFIED ConsumeNowShotStoryRequest_ResourceType = 0
	ConsumeNowShotStoryRequest_RESOURCE_TYPE_IMAGE       ConsumeNowShotStoryRequest_ResourceType = 1
	ConsumeNowShotStoryRequest_RESOURCE_TYPE_VIDEO       ConsumeNowShotStoryRequest_ResourceType = 2
)

// Enum value maps for ConsumeNowShotStoryRequest_ResourceType.
var (
	ConsumeNowShotStoryRequest_ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
	}
	ConsumeNowShotStoryRequest_ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
	}
)

func (x ConsumeNowShotStoryRequest_ResourceType) Enum() *ConsumeNowShotStoryRequest_ResourceType {
	p := new(ConsumeNowShotStoryRequest_ResourceType)
	*p = x
	return p
}

func (x ConsumeNowShotStoryRequest_ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConsumeNowShotStoryRequest_ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_v1_story_proto_enumTypes[0].Descriptor()
}

func (ConsumeNowShotStoryRequest_ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_v1_story_proto_enumTypes[0]
}

func (x ConsumeNowShotStoryRequest_ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConsumeNowShotStoryRequest_ResourceType.Descriptor instead.
func (ConsumeNowShotStoryRequest_ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{41, 0}
}

type UpdateStoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// only number
	StoryId string `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 隐私设置，可选型，如果不传，则表示不修改
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateStoryRequest) Reset() {
	*x = UpdateStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStoryRequest) ProtoMessage() {}

func (x *UpdateStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStoryRequest.ProtoReflect.Descriptor instead.
func (*UpdateStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *UpdateStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

type UpdateStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateStoryResponse) Reset() {
	*x = UpdateStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateStoryResponse) ProtoMessage() {}

func (x *UpdateStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateStoryResponse.ProtoReflect.Descriptor instead.
func (*UpdateStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type TopStoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// only number
	StoryId string `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 是否置顶
	IsTop         bool `protobuf:"varint,2,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TopStoryRequest) Reset() {
	*x = TopStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TopStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopStoryRequest) ProtoMessage() {}

func (x *TopStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopStoryRequest.ProtoReflect.Descriptor instead.
func (*TopStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{2}
}

func (x *TopStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *TopStoryRequest) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

// 删除 story 的请求
type DeleteStoryRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// only number
	StoryId       string `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteStoryRequest) Reset() {
	*x = DeleteStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteStoryRequest) ProtoMessage() {}

func (x *DeleteStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteStoryRequest.ProtoReflect.Descriptor instead.
func (*DeleteStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type ListCommonStoryConditionTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryPlayType v1.StoryPlayType       `protobuf:"varint,1,opt,name=story_play_type,json=storyPlayType,proto3,enum=api.items.story.types.v1.StoryPlayType" json:"story_play_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCommonStoryConditionTemplatesRequest) Reset() {
	*x = ListCommonStoryConditionTemplatesRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCommonStoryConditionTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCommonStoryConditionTemplatesRequest) ProtoMessage() {}

func (x *ListCommonStoryConditionTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCommonStoryConditionTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListCommonStoryConditionTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{4}
}

func (x *ListCommonStoryConditionTemplatesRequest) GetStoryPlayType() v1.StoryPlayType {
	if x != nil {
		return x.StoryPlayType
	}
	return v1.StoryPlayType(0)
}

type ListCommonStoryConditionTemplatesResponse struct {
	state              protoimpl.MessageState                 `protogen:"open.v1"`
	ConditionTemplates []*v1.CommonStoryPlayConditionTemplate `protobuf:"bytes,1,rep,name=condition_templates,json=conditionTemplates,proto3" json:"condition_templates,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListCommonStoryConditionTemplatesResponse) Reset() {
	*x = ListCommonStoryConditionTemplatesResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCommonStoryConditionTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCommonStoryConditionTemplatesResponse) ProtoMessage() {}

func (x *ListCommonStoryConditionTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCommonStoryConditionTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListCommonStoryConditionTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{5}
}

func (x *ListCommonStoryConditionTemplatesResponse) GetConditionTemplates() []*v1.CommonStoryPlayConditionTemplate {
	if x != nil {
		return x.ConditionTemplates
	}
	return nil
}

type ListTurtleSoupStoryConditionTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTurtleSoupStoryConditionTemplatesRequest) Reset() {
	*x = ListTurtleSoupStoryConditionTemplatesRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTurtleSoupStoryConditionTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTurtleSoupStoryConditionTemplatesRequest) ProtoMessage() {}

func (x *ListTurtleSoupStoryConditionTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTurtleSoupStoryConditionTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListTurtleSoupStoryConditionTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{6}
}

type ListTurtleSoupStoryConditionTemplatesResponse struct {
	state              protoimpl.MessageState                 `protogen:"open.v1"`
	ConditionTemplates []*v1.StoryPlayTurtleConditionTemplate `protobuf:"bytes,1,rep,name=condition_templates,json=conditionTemplates,proto3" json:"condition_templates,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListTurtleSoupStoryConditionTemplatesResponse) Reset() {
	*x = ListTurtleSoupStoryConditionTemplatesResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTurtleSoupStoryConditionTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTurtleSoupStoryConditionTemplatesResponse) ProtoMessage() {}

func (x *ListTurtleSoupStoryConditionTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTurtleSoupStoryConditionTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListTurtleSoupStoryConditionTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{7}
}

func (x *ListTurtleSoupStoryConditionTemplatesResponse) GetConditionTemplates() []*v1.StoryPlayTurtleConditionTemplate {
	if x != nil {
		return x.ConditionTemplates
	}
	return nil
}

type ListExchangeImageStoryConditionTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListExchangeImageStoryConditionTemplatesRequest) Reset() {
	*x = ListExchangeImageStoryConditionTemplatesRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListExchangeImageStoryConditionTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExchangeImageStoryConditionTemplatesRequest) ProtoMessage() {}

func (x *ListExchangeImageStoryConditionTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExchangeImageStoryConditionTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListExchangeImageStoryConditionTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{8}
}

type ListExchangeImageStoryConditionTemplatesResponse struct {
	state              protoimpl.MessageState                    `protogen:"open.v1"`
	ConditionTemplates []*v1.StoryExchangeImageConditionTemplate `protobuf:"bytes,1,rep,name=condition_templates,json=conditionTemplates,proto3" json:"condition_templates,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListExchangeImageStoryConditionTemplatesResponse) Reset() {
	*x = ListExchangeImageStoryConditionTemplatesResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListExchangeImageStoryConditionTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExchangeImageStoryConditionTemplatesResponse) ProtoMessage() {}

func (x *ListExchangeImageStoryConditionTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExchangeImageStoryConditionTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListExchangeImageStoryConditionTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{9}
}

func (x *ListExchangeImageStoryConditionTemplatesResponse) GetConditionTemplates() []*v1.StoryExchangeImageConditionTemplate {
	if x != nil {
		return x.ConditionTemplates
	}
	return nil
}

type ListUnmuteStoryConditionTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUnmuteStoryConditionTemplatesRequest) Reset() {
	*x = ListUnmuteStoryConditionTemplatesRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUnmuteStoryConditionTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnmuteStoryConditionTemplatesRequest) ProtoMessage() {}

func (x *ListUnmuteStoryConditionTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnmuteStoryConditionTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListUnmuteStoryConditionTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{10}
}

type ListUnmuteStoryConditionTemplatesResponse struct {
	state              protoimpl.MessageState                 `protogen:"open.v1"`
	ConditionTemplates []*v1.StoryPlayUnmuteConditionTemplate `protobuf:"bytes,1,rep,name=condition_templates,json=conditionTemplates,proto3" json:"condition_templates,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListUnmuteStoryConditionTemplatesResponse) Reset() {
	*x = ListUnmuteStoryConditionTemplatesResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUnmuteStoryConditionTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnmuteStoryConditionTemplatesResponse) ProtoMessage() {}

func (x *ListUnmuteStoryConditionTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnmuteStoryConditionTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListUnmuteStoryConditionTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{11}
}

func (x *ListUnmuteStoryConditionTemplatesResponse) GetConditionTemplates() []*v1.StoryPlayUnmuteConditionTemplate {
	if x != nil {
		return x.ConditionTemplates
	}
	return nil
}

type ListTurtleSoupConditionTemplatesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTurtleSoupConditionTemplatesRequest) Reset() {
	*x = ListTurtleSoupConditionTemplatesRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTurtleSoupConditionTemplatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTurtleSoupConditionTemplatesRequest) ProtoMessage() {}

func (x *ListTurtleSoupConditionTemplatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTurtleSoupConditionTemplatesRequest.ProtoReflect.Descriptor instead.
func (*ListTurtleSoupConditionTemplatesRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{12}
}

type ListTurtleSoupConditionTemplatesResponse struct {
	state              protoimpl.MessageState                 `protogen:"open.v1"`
	ConditionTemplates []*v1.StoryPlayTurtleConditionTemplate `protobuf:"bytes,1,rep,name=condition_templates,json=conditionTemplates,proto3" json:"condition_templates,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ListTurtleSoupConditionTemplatesResponse) Reset() {
	*x = ListTurtleSoupConditionTemplatesResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTurtleSoupConditionTemplatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTurtleSoupConditionTemplatesResponse) ProtoMessage() {}

func (x *ListTurtleSoupConditionTemplatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTurtleSoupConditionTemplatesResponse.ProtoReflect.Descriptor instead.
func (*ListTurtleSoupConditionTemplatesResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{13}
}

func (x *ListTurtleSoupConditionTemplatesResponse) GetConditionTemplates() []*v1.StoryPlayTurtleConditionTemplate {
	if x != nil {
		return x.ConditionTemplates
	}
	return nil
}

type ListExchangeImageStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stories       []*v1.StoryDetail      `protobuf:"bytes,1,rep,name=stories,proto3" json:"stories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListExchangeImageStoryResponse) Reset() {
	*x = ListExchangeImageStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListExchangeImageStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExchangeImageStoryResponse) ProtoMessage() {}

func (x *ListExchangeImageStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExchangeImageStoryResponse.ProtoReflect.Descriptor instead.
func (*ListExchangeImageStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{14}
}

func (x *ListExchangeImageStoryResponse) GetStories() []*v1.StoryDetail {
	if x != nil {
		return x.Stories
	}
	return nil
}

// 发布 story 的请求
type CreateExchangeImageStoryRequest struct {
	state      protoimpl.MessageState           `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayExchangeImageConfig `protobuf:"bytes,2,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateExchangeImageStoryRequest) Reset() {
	*x = CreateExchangeImageStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateExchangeImageStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangeImageStoryRequest) ProtoMessage() {}

func (x *CreateExchangeImageStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangeImageStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateExchangeImageStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{15}
}

func (x *CreateExchangeImageStoryRequest) GetPlayConfig() *v1.StoryPlayExchangeImageConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateExchangeImageStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

type CreateExchangeImageStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateExchangeImageStoryResponse) Reset() {
	*x = CreateExchangeImageStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateExchangeImageStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangeImageStoryResponse) ProtoMessage() {}

func (x *CreateExchangeImageStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangeImageStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateExchangeImageStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{16}
}

func (x *CreateExchangeImageStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布海龟汤的 story 请求
type CreateTurtleSoupStoryRequest struct {
	state      protoimpl.MessageState        `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayTurtleSoupConfig `protobuf:"bytes,1,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 是否是马赛克玩法
	IsMass bool `protobuf:"varint,2,opt,name=is_mass,json=isMass,proto3" json:"is_mass,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateTurtleSoupStoryRequest) Reset() {
	*x = CreateTurtleSoupStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTurtleSoupStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTurtleSoupStoryRequest) ProtoMessage() {}

func (x *CreateTurtleSoupStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTurtleSoupStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateTurtleSoupStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{17}
}

func (x *CreateTurtleSoupStoryRequest) GetPlayConfig() *v1.StoryPlayTurtleSoupConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateTurtleSoupStoryRequest) GetIsMass() bool {
	if x != nil {
		return x.IsMass
	}
	return false
}

func (x *CreateTurtleSoupStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

// 发布海龟汤的 story 响应
type CreateTurtleSoupStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTurtleSoupStoryResponse) Reset() {
	*x = CreateTurtleSoupStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTurtleSoupStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTurtleSoupStoryResponse) ProtoMessage() {}

func (x *CreateTurtleSoupStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTurtleSoupStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateTurtleSoupStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{18}
}

func (x *CreateTurtleSoupStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布unmute的 story 请求
type CreateUnmuteStoryRequest struct {
	state      protoimpl.MessageState    `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayUnmuteConfig `protobuf:"bytes,1,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateUnmuteStoryRequest) Reset() {
	*x = CreateUnmuteStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUnmuteStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUnmuteStoryRequest) ProtoMessage() {}

func (x *CreateUnmuteStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUnmuteStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateUnmuteStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{19}
}

func (x *CreateUnmuteStoryRequest) GetPlayConfig() *v1.StoryPlayUnmuteConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateUnmuteStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

// 发布unmute的 story 响应
type CreateUnmuteStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUnmuteStoryResponse) Reset() {
	*x = CreateUnmuteStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUnmuteStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUnmuteStoryResponse) ProtoMessage() {}

func (x *CreateUnmuteStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUnmuteStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateUnmuteStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{20}
}

func (x *CreateUnmuteStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布baseplay的 story 请求
type CreateBasePlayStoryRequest struct {
	state      protoimpl.MessageState      `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayBasePlayConfig `protobuf:"bytes,1,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateBasePlayStoryRequest) Reset() {
	*x = CreateBasePlayStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBasePlayStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBasePlayStoryRequest) ProtoMessage() {}

func (x *CreateBasePlayStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBasePlayStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateBasePlayStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{21}
}

func (x *CreateBasePlayStoryRequest) GetPlayConfig() *v1.StoryPlayBasePlayConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateBasePlayStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

// 发布baseplay的 story 响应
type CreateBasePlayStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateBasePlayStoryResponse) Reset() {
	*x = CreateBasePlayStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateBasePlayStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBasePlayStoryResponse) ProtoMessage() {}

func (x *CreateBasePlayStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBasePlayStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateBasePlayStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{22}
}

func (x *CreateBasePlayStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布 nowshot 的 story 请求
type CreateNowShotStoryRequest struct {
	state      protoimpl.MessageState     `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayNowShotConfig `protobuf:"bytes,1,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateNowShotStoryRequest) Reset() {
	*x = CreateNowShotStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNowShotStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNowShotStoryRequest) ProtoMessage() {}

func (x *CreateNowShotStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNowShotStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateNowShotStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{23}
}

func (x *CreateNowShotStoryRequest) GetPlayConfig() *v1.StoryPlayNowShotConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateNowShotStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

// 发布 nowshot 的 story 响应
type CreateNowShotStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNowShotStoryResponse) Reset() {
	*x = CreateNowShotStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNowShotStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNowShotStoryResponse) ProtoMessage() {}

func (x *CreateNowShotStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNowShotStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateNowShotStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{24}
}

func (x *CreateNowShotStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type GetStoryDetailRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStoryDetailRequest) Reset() {
	*x = GetStoryDetailRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStoryDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStoryDetailRequest) ProtoMessage() {}

func (x *GetStoryDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStoryDetailRequest.ProtoReflect.Descriptor instead.
func (*GetStoryDetailRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{25}
}

func (x *GetStoryDetailRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetStoryDetailResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStoryDetailResponse) Reset() {
	*x = GetStoryDetailResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStoryDetailResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStoryDetailResponse) ProtoMessage() {}

func (x *GetStoryDetailResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStoryDetailResponse.ProtoReflect.Descriptor instead.
func (*GetStoryDetailResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{26}
}

func (x *GetStoryDetailResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ListCreatorStoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatorId     string                 `protobuf:"bytes,1,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	ListRequest   *v11.ListRequest       `protobuf:"bytes,2,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCreatorStoryRequest) Reset() {
	*x = ListCreatorStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCreatorStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreatorStoryRequest) ProtoMessage() {}

func (x *ListCreatorStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreatorStoryRequest.ProtoReflect.Descriptor instead.
func (*ListCreatorStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{27}
}

func (x *ListCreatorStoryRequest) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *ListCreatorStoryRequest) GetListRequest() *v11.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListCreatorStoryResponse struct {
	state          protoimpl.MessageState                   `protogen:"open.v1"`
	CreatedStories []*ListCreatorStoryResponse_CreatedStory `protobuf:"bytes,1,rep,name=created_stories,json=createdStories,proto3" json:"created_stories,omitempty"`
	ListResponse   *v11.ListResponse                        `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListCreatorStoryResponse) Reset() {
	*x = ListCreatorStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCreatorStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreatorStoryResponse) ProtoMessage() {}

func (x *ListCreatorStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreatorStoryResponse.ProtoReflect.Descriptor instead.
func (*ListCreatorStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{28}
}

func (x *ListCreatorStoryResponse) GetCreatedStories() []*ListCreatorStoryResponse_CreatedStory {
	if x != nil {
		return x.CreatedStories
	}
	return nil
}

func (x *ListCreatorStoryResponse) GetListResponse() *v11.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

// 获取解锁过的 story 列表
type ListUnlockedStoryRequest struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	ListRequest *v11.ListRequest       `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	// 过滤类型，如果传空，则不过滤
	FilterPlayTypes []v1.StoryPlayType `protobuf:"varint,2,rep,packed,name=filter_play_types,json=filterPlayTypes,proto3,enum=api.items.story.types.v1.StoryPlayType" json:"filter_play_types,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListUnlockedStoryRequest) Reset() {
	*x = ListUnlockedStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUnlockedStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnlockedStoryRequest) ProtoMessage() {}

func (x *ListUnlockedStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnlockedStoryRequest.ProtoReflect.Descriptor instead.
func (*ListUnlockedStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{29}
}

func (x *ListUnlockedStoryRequest) GetListRequest() *v11.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

func (x *ListUnlockedStoryRequest) GetFilterPlayTypes() []v1.StoryPlayType {
	if x != nil {
		return x.FilterPlayTypes
	}
	return nil
}

type ListUnlockedStoryResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UnlockedStories []*v1.StoryDetail      `protobuf:"bytes,1,rep,name=unlocked_stories,json=unlockedStories,proto3" json:"unlocked_stories,omitempty"`
	ListResponse    *v11.ListResponse      `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListUnlockedStoryResponse) Reset() {
	*x = ListUnlockedStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUnlockedStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUnlockedStoryResponse) ProtoMessage() {}

func (x *ListUnlockedStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUnlockedStoryResponse.ProtoReflect.Descriptor instead.
func (*ListUnlockedStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{30}
}

func (x *ListUnlockedStoryResponse) GetUnlockedStories() []*v1.StoryDetail {
	if x != nil {
		return x.UnlockedStories
	}
	return nil
}

func (x *ListUnlockedStoryResponse) GetListResponse() *v11.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ConsumeBasePlayStoryRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	StoryId string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 消费到第几个 node 了，从 0 开始
	CurrentNodeIdx uint32 `protobuf:"varint,2,opt,name=current_node_idx,json=currentNodeIdx,proto3" json:"current_node_idx,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ConsumeBasePlayStoryRequest) Reset() {
	*x = ConsumeBasePlayStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeBasePlayStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeBasePlayStoryRequest) ProtoMessage() {}

func (x *ConsumeBasePlayStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeBasePlayStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeBasePlayStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{31}
}

func (x *ConsumeBasePlayStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeBasePlayStoryRequest) GetCurrentNodeIdx() uint32 {
	if x != nil {
		return x.CurrentNodeIdx
	}
	return 0
}

type ConsumeBasePlayStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeBasePlayStoryResponse) Reset() {
	*x = ConsumeBasePlayStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeBasePlayStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeBasePlayStoryResponse) ProtoMessage() {}

func (x *ConsumeBasePlayStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeBasePlayStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeBasePlayStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{32}
}

func (x *ConsumeBasePlayStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumeExchangeImageStoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	ImageKey      string                 `protobuf:"bytes,2,opt,name=image_key,json=imageKey,proto3" json:"image_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeExchangeImageStoryRequest) Reset() {
	*x = ConsumeExchangeImageStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeExchangeImageStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeExchangeImageStoryRequest) ProtoMessage() {}

func (x *ConsumeExchangeImageStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeExchangeImageStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeExchangeImageStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{33}
}

func (x *ConsumeExchangeImageStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeExchangeImageStoryRequest) GetImageKey() string {
	if x != nil {
		return x.ImageKey
	}
	return ""
}

type ConsumeExchangeImageStoryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 匹配状态，参考 api.items.story.types.v1.ExchangeImageMatchStatus
	MatchStatus string `protobuf:"bytes,1,opt,name=match_status,json=matchStatus,proto3" json:"match_status,omitempty"`
	// ai 回复
	AiResponse string `protobuf:"bytes,2,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	// 更新后的 story 详情，可以在这里获取下一个节点的信息
	StoryDetail   *v1.StoryDetail `protobuf:"bytes,3,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeExchangeImageStoryResponse) Reset() {
	*x = ConsumeExchangeImageStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeExchangeImageStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeExchangeImageStoryResponse) ProtoMessage() {}

func (x *ConsumeExchangeImageStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeExchangeImageStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeExchangeImageStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{34}
}

func (x *ConsumeExchangeImageStoryResponse) GetMatchStatus() string {
	if x != nil {
		return x.MatchStatus
	}
	return ""
}

func (x *ConsumeExchangeImageStoryResponse) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

func (x *ConsumeExchangeImageStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumeTurtleSoupStoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UserMessage   string                 `protobuf:"bytes,2,opt,name=user_message,json=userMessage,proto3" json:"user_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeTurtleSoupStoryRequest) Reset() {
	*x = ConsumeTurtleSoupStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeTurtleSoupStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeTurtleSoupStoryRequest) ProtoMessage() {}

func (x *ConsumeTurtleSoupStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeTurtleSoupStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeTurtleSoupStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{35}
}

func (x *ConsumeTurtleSoupStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeTurtleSoupStoryRequest) GetUserMessage() string {
	if x != nil {
		return x.UserMessage
	}
	return ""
}

type ConsumeTurtleSoupStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeTurtleSoupStoryResponse) Reset() {
	*x = ConsumeTurtleSoupStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeTurtleSoupStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeTurtleSoupStoryResponse) ProtoMessage() {}

func (x *ConsumeTurtleSoupStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeTurtleSoupStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeTurtleSoupStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{36}
}

func (x *ConsumeTurtleSoupStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 获取某个 Story 同作者一定时间范围内对的其他 story
// 通过多个 ListRequest + reverse 来实现同时正反向取多个 story
type ListSameAuthorStoryWithAnchorRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 锚点 story id
	AnchorStoryId string                                              `protobuf:"bytes,1,opt,name=anchor_story_id,json=anchorStoryId,proto3" json:"anchor_story_id,omitempty"`
	ListRequests  []*ListSameAuthorStoryWithAnchorRequest_ListRequest `protobuf:"bytes,2,rep,name=list_requests,json=listRequests,proto3" json:"list_requests,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSameAuthorStoryWithAnchorRequest) Reset() {
	*x = ListSameAuthorStoryWithAnchorRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSameAuthorStoryWithAnchorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSameAuthorStoryWithAnchorRequest) ProtoMessage() {}

func (x *ListSameAuthorStoryWithAnchorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSameAuthorStoryWithAnchorRequest.ProtoReflect.Descriptor instead.
func (*ListSameAuthorStoryWithAnchorRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{37}
}

func (x *ListSameAuthorStoryWithAnchorRequest) GetAnchorStoryId() string {
	if x != nil {
		return x.AnchorStoryId
	}
	return ""
}

func (x *ListSameAuthorStoryWithAnchorRequest) GetListRequests() []*ListSameAuthorStoryWithAnchorRequest_ListRequest {
	if x != nil {
		return x.ListRequests
	}
	return nil
}

type ListSameAuthorStoryWithAnchorResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 有多少个 ListRequests 就有多少个 ListResponse
	ListResponses []*ListSameAuthorStoryWithAnchorResponse_ListResponse `protobuf:"bytes,1,rep,name=list_responses,json=listResponses,proto3" json:"list_responses,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSameAuthorStoryWithAnchorResponse) Reset() {
	*x = ListSameAuthorStoryWithAnchorResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSameAuthorStoryWithAnchorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSameAuthorStoryWithAnchorResponse) ProtoMessage() {}

func (x *ListSameAuthorStoryWithAnchorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSameAuthorStoryWithAnchorResponse.ProtoReflect.Descriptor instead.
func (*ListSameAuthorStoryWithAnchorResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{38}
}

func (x *ListSameAuthorStoryWithAnchorResponse) GetListResponses() []*ListSameAuthorStoryWithAnchorResponse_ListResponse {
	if x != nil {
		return x.ListResponses
	}
	return nil
}

type ConsumeUnmuteStoryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UserAudioKey  string                 `protobuf:"bytes,2,opt,name=user_audio_key,json=userAudioKey,proto3" json:"user_audio_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeUnmuteStoryRequest) Reset() {
	*x = ConsumeUnmuteStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeUnmuteStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUnmuteStoryRequest) ProtoMessage() {}

func (x *ConsumeUnmuteStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUnmuteStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeUnmuteStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{39}
}

func (x *ConsumeUnmuteStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeUnmuteStoryRequest) GetUserAudioKey() string {
	if x != nil {
		return x.UserAudioKey
	}
	return ""
}

type ConsumeUnmuteStoryResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 匹配状态，参考 api.items.story.types.v1.UnmuteMatchStatus
	MatchStatus string `protobuf:"bytes,1,opt,name=match_status,json=matchStatus,proto3" json:"match_status,omitempty"`
	// ai 回复
	AiResponse    string          `protobuf:"bytes,2,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	StoryDetail   *v1.StoryDetail `protobuf:"bytes,3,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeUnmuteStoryResponse) Reset() {
	*x = ConsumeUnmuteStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeUnmuteStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeUnmuteStoryResponse) ProtoMessage() {}

func (x *ConsumeUnmuteStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeUnmuteStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeUnmuteStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{40}
}

func (x *ConsumeUnmuteStoryResponse) GetMatchStatus() string {
	if x != nil {
		return x.MatchStatus
	}
	return ""
}

func (x *ConsumeUnmuteStoryResponse) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

func (x *ConsumeUnmuteStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumeNowShotStoryRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	StoryId string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 资源 url，创建时，客户端传 key
	UserResourceKey string `protobuf:"bytes,2,opt,name=user_resource_key,json=userResourceKey,proto3" json:"user_resource_key,omitempty"`
	StartTime       uint32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 封面资源类型，参考 ResourceType
	ResourceType string `protobuf:"bytes,4,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// 如果资源是视频时，需要传一个视频首帧，创作时给 key
	ThumbnailUrl  *string `protobuf:"bytes,5,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeNowShotStoryRequest) Reset() {
	*x = ConsumeNowShotStoryRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeNowShotStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeNowShotStoryRequest) ProtoMessage() {}

func (x *ConsumeNowShotStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeNowShotStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeNowShotStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{41}
}

func (x *ConsumeNowShotStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeNowShotStoryRequest) GetUserResourceKey() string {
	if x != nil {
		return x.UserResourceKey
	}
	return ""
}

func (x *ConsumeNowShotStoryRequest) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *ConsumeNowShotStoryRequest) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ConsumeNowShotStoryRequest) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

type ConsumeNowShotStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeNowShotStoryResponse) Reset() {
	*x = ConsumeNowShotStoryResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeNowShotStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeNowShotStoryResponse) ProtoMessage() {}

func (x *ConsumeNowShotStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeNowShotStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeNowShotStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{42}
}

func (x *ConsumeNowShotStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ListCreatorStoryResponse_CreatedStory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Story *v1.StorySummary       `protobuf:"bytes,1,opt,name=story,proto3" json:"story,omitempty"`
	// 是否置顶
	IsTop         bool `protobuf:"varint,2,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCreatorStoryResponse_CreatedStory) Reset() {
	*x = ListCreatorStoryResponse_CreatedStory{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCreatorStoryResponse_CreatedStory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreatorStoryResponse_CreatedStory) ProtoMessage() {}

func (x *ListCreatorStoryResponse_CreatedStory) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreatorStoryResponse_CreatedStory.ProtoReflect.Descriptor instead.
func (*ListCreatorStoryResponse_CreatedStory) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{28, 0}
}

func (x *ListCreatorStoryResponse_CreatedStory) GetStory() *v1.StorySummary {
	if x != nil {
		return x.Story
	}
	return nil
}

func (x *ListCreatorStoryResponse_CreatedStory) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

type ListSameAuthorStoryWithAnchorRequest_ListRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤游玩类型，如果传空，则不过滤
	FilterPlayTypes []v1.StoryPlayType `protobuf:"varint,1,rep,packed,name=filter_play_types,json=filterPlayTypes,proto3,enum=api.items.story.types.v1.StoryPlayType" json:"filter_play_types,omitempty"`
	// 是否时间倒叙
	Reverse bool `protobuf:"varint,2,opt,name=reverse,proto3" json:"reverse,omitempty"`
	// 取多少个
	Limit         uint32 `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSameAuthorStoryWithAnchorRequest_ListRequest) Reset() {
	*x = ListSameAuthorStoryWithAnchorRequest_ListRequest{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSameAuthorStoryWithAnchorRequest_ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSameAuthorStoryWithAnchorRequest_ListRequest) ProtoMessage() {}

func (x *ListSameAuthorStoryWithAnchorRequest_ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSameAuthorStoryWithAnchorRequest_ListRequest.ProtoReflect.Descriptor instead.
func (*ListSameAuthorStoryWithAnchorRequest_ListRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{37, 0}
}

func (x *ListSameAuthorStoryWithAnchorRequest_ListRequest) GetFilterPlayTypes() []v1.StoryPlayType {
	if x != nil {
		return x.FilterPlayTypes
	}
	return nil
}

func (x *ListSameAuthorStoryWithAnchorRequest_ListRequest) GetReverse() bool {
	if x != nil {
		return x.Reverse
	}
	return false
}

func (x *ListSameAuthorStoryWithAnchorRequest_ListRequest) GetLimit() uint32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type ListSameAuthorStoryWithAnchorResponse_ListResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否倒叙
	Reverse       bool              `protobuf:"varint,1,opt,name=reverse,proto3" json:"reverse,omitempty"`
	Stories       []*v1.StoryDetail `protobuf:"bytes,2,rep,name=stories,proto3" json:"stories,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSameAuthorStoryWithAnchorResponse_ListResponse) Reset() {
	*x = ListSameAuthorStoryWithAnchorResponse_ListResponse{}
	mi := &file_api_items_story_v1_story_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSameAuthorStoryWithAnchorResponse_ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSameAuthorStoryWithAnchorResponse_ListResponse) ProtoMessage() {}

func (x *ListSameAuthorStoryWithAnchorResponse_ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_story_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSameAuthorStoryWithAnchorResponse_ListResponse.ProtoReflect.Descriptor instead.
func (*ListSameAuthorStoryWithAnchorResponse_ListResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_story_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ListSameAuthorStoryWithAnchorResponse_ListResponse) GetReverse() bool {
	if x != nil {
		return x.Reverse
	}
	return false
}

func (x *ListSameAuthorStoryWithAnchorResponse_ListResponse) GetStories() []*v1.StoryDetail {
	if x != nil {
		return x.Stories
	}
	return nil
}

var File_api_items_story_v1_story_proto protoreflect.FileDescriptor

const file_api_items_story_v1_story_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/items/story/v1/story.proto\x12\x12api.items.story.v1\x1a$api/items/story/types/v1/types.proto\x1a0api/items/story/types/v1/turtle_soup_types.proto\x1a-api/items/story/types/v1/now_shot_types.proto\x1a\x1aapi/common/v1/common.proto\x1a\x17validate/validate.proto\x1a.api/items/story/v1/story.privacy.setting.proto\x1a4api/items/story/types/v1/base_play_story_types.proto\"\xb0\x01\n" +
	"\x12UpdateStoryRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"_\n" +
	"\x13UpdateStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"T\n" +
	"\x0fTopStoryRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12\x15\n" +
	"\x06is_top\x18\x02 \x01(\bR\x05isTop\"@\n" +
	"\x12DeleteStoryRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\"{\n" +
	"(ListCommonStoryConditionTemplatesRequest\x12O\n" +
	"\x0fstory_play_type\x18\x01 \x01(\x0e2'.api.items.story.types.v1.StoryPlayTypeR\rstoryPlayType\"\x98\x01\n" +
	")ListCommonStoryConditionTemplatesResponse\x12k\n" +
	"\x13condition_templates\x18\x01 \x03(\v2:.api.items.story.types.v1.CommonStoryPlayConditionTemplateR\x12conditionTemplates\".\n" +
	",ListTurtleSoupStoryConditionTemplatesRequest\"\x9c\x01\n" +
	"-ListTurtleSoupStoryConditionTemplatesResponse\x12k\n" +
	"\x13condition_templates\x18\x01 \x03(\v2:.api.items.story.types.v1.StoryPlayTurtleConditionTemplateR\x12conditionTemplates\"1\n" +
	"/ListExchangeImageStoryConditionTemplatesRequest\"\xa2\x01\n" +
	"0ListExchangeImageStoryConditionTemplatesResponse\x12n\n" +
	"\x13condition_templates\x18\x01 \x03(\v2=.api.items.story.types.v1.StoryExchangeImageConditionTemplateR\x12conditionTemplates\"*\n" +
	"(ListUnmuteStoryConditionTemplatesRequest\"\x98\x01\n" +
	")ListUnmuteStoryConditionTemplatesResponse\x12k\n" +
	"\x13condition_templates\x18\x01 \x03(\v2:.api.items.story.types.v1.StoryPlayUnmuteConditionTemplateR\x12conditionTemplates\")\n" +
	"'ListTurtleSoupConditionTemplatesRequest\"\x97\x01\n" +
	"(ListTurtleSoupConditionTemplatesResponse\x12k\n" +
	"\x13condition_templates\x18\x01 \x03(\v2:.api.items.story.types.v1.StoryPlayTurtleConditionTemplateR\x12conditionTemplates\"a\n" +
	"\x1eListExchangeImageStoryResponse\x12?\n" +
	"\astories\x18\x01 \x03(\v2%.api.items.story.types.v1.StoryDetailR\astories\"\xea\x01\n" +
	"\x1fCreateExchangeImageStoryRequest\x12W\n" +
	"\vplay_config\x18\x02 \x01(\v26.api.items.story.types.v1.StoryPlayExchangeImageConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"l\n" +
	" CreateExchangeImageStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xfd\x01\n" +
	"\x1cCreateTurtleSoupStoryRequest\x12T\n" +
	"\vplay_config\x18\x01 \x01(\v23.api.items.story.types.v1.StoryPlayTurtleSoupConfigR\n" +
	"playConfig\x12\x17\n" +
	"\ais_mass\x18\x02 \x01(\bR\x06isMass\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"i\n" +
	"\x1dCreateTurtleSoupStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xdc\x01\n" +
	"\x18CreateUnmuteStoryRequest\x12P\n" +
	"\vplay_config\x18\x01 \x01(\v2/.api.items.story.types.v1.StoryPlayUnmuteConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"e\n" +
	"\x19CreateUnmuteStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xe0\x01\n" +
	"\x1aCreateBasePlayStoryRequest\x12R\n" +
	"\vplay_config\x18\x01 \x01(\v21.api.items.story.types.v1.StoryPlayBasePlayConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"g\n" +
	"\x1bCreateBasePlayStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xde\x01\n" +
	"\x19CreateNowShotStoryRequest\x12Q\n" +
	"\vplay_config\x18\x01 \x01(\v20.api.items.story.types.v1.StoryPlayNowShotConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"f\n" +
	"\x1aCreateNowShotStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"'\n" +
	"\x15GetStoryDetailRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"b\n" +
	"\x16GetStoryDetailResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\x92\x01\n" +
	"\x17ListCreatorStoryRequest\x12.\n" +
	"\n" +
	"creator_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\tcreatorId\x12G\n" +
	"\flist_request\x18\x02 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xa5\x02\n" +
	"\x18ListCreatorStoryResponse\x12b\n" +
	"\x0fcreated_stories\x18\x01 \x03(\v29.api.items.story.v1.ListCreatorStoryResponse.CreatedStoryR\x0ecreatedStories\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x1ac\n" +
	"\fCreatedStory\x12<\n" +
	"\x05story\x18\x01 \x01(\v2&.api.items.story.types.v1.StorySummaryR\x05story\x12\x15\n" +
	"\x06is_top\x18\x02 \x01(\bR\x05isTop\"\xb8\x01\n" +
	"\x18ListUnlockedStoryRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\x12S\n" +
	"\x11filter_play_types\x18\x02 \x03(\x0e2'.api.items.story.types.v1.StoryPlayTypeR\x0ffilterPlayTypes\"\xaf\x01\n" +
	"\x19ListUnlockedStoryResponse\x12P\n" +
	"\x10unlocked_stories\x18\x01 \x03(\v2%.api.items.story.types.v1.StoryDetailR\x0funlockedStories\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"b\n" +
	"\x1bConsumeBasePlayStoryRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12(\n" +
	"\x10current_node_idx\x18\x02 \x01(\rR\x0ecurrentNodeIdx\"h\n" +
	"\x1cConsumeBasePlayStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"Z\n" +
	" ConsumeExchangeImageStoryRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12\x1b\n" +
	"\timage_key\x18\x02 \x01(\tR\bimageKey\"\xb1\x01\n" +
	"!ConsumeExchangeImageStoryResponse\x12!\n" +
	"\fmatch_status\x18\x01 \x01(\tR\vmatchStatus\x12\x1f\n" +
	"\vai_response\x18\x02 \x01(\tR\n" +
	"aiResponse\x12H\n" +
	"\fstory_detail\x18\x03 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"]\n" +
	"\x1dConsumeTurtleSoupStoryRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12!\n" +
	"\fuser_message\x18\x02 \x01(\tR\vuserMessage\"j\n" +
	"\x1eConsumeTurtleSoupStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xe5\x02\n" +
	"$ListSameAuthorStoryWithAnchorRequest\x12&\n" +
	"\x0fanchor_story_id\x18\x01 \x01(\tR\ranchorStoryId\x12u\n" +
	"\rlist_requests\x18\x02 \x03(\v2D.api.items.story.v1.ListSameAuthorStoryWithAnchorRequest.ListRequestB\n" +
	"\xfaB\a\x92\x01\x04\b\x01\x10\x02R\flistRequests\x1a\x9d\x01\n" +
	"\vListRequest\x12S\n" +
	"\x11filter_play_types\x18\x01 \x03(\x0e2'.api.items.story.types.v1.StoryPlayTypeR\x0ffilterPlayTypes\x12\x18\n" +
	"\areverse\x18\x02 \x01(\bR\areverse\x12\x1f\n" +
	"\x05limit\x18\x03 \x01(\rB\t\xfaB\x06*\x04\x10\n" +
	" \x00R\x05limit\"\x81\x02\n" +
	"%ListSameAuthorStoryWithAnchorResponse\x12m\n" +
	"\x0elist_responses\x18\x01 \x03(\v2F.api.items.story.v1.ListSameAuthorStoryWithAnchorResponse.ListResponseR\rlistResponses\x1ai\n" +
	"\fListResponse\x12\x18\n" +
	"\areverse\x18\x01 \x01(\bR\areverse\x12?\n" +
	"\astories\x18\x02 \x03(\v2%.api.items.story.types.v1.StoryDetailR\astories\"\\\n" +
	"\x19ConsumeUnmuteStoryRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12$\n" +
	"\x0euser_audio_key\x18\x02 \x01(\tR\fuserAudioKey\"\xaa\x01\n" +
	"\x1aConsumeUnmuteStoryResponse\x12!\n" +
	"\fmatch_status\x18\x01 \x01(\tR\vmatchStatus\x12\x1f\n" +
	"\vai_response\x18\x02 \x01(\tR\n" +
	"aiResponse\x12H\n" +
	"\fstory_detail\x18\x03 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xc4\x02\n" +
	"\x1aConsumeNowShotStoryRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12*\n" +
	"\x11user_resource_key\x18\x02 \x01(\tR\x0fuserResourceKey\x12\x1d\n" +
	"\n" +
	"start_time\x18\x03 \x01(\rR\tstartTime\x12#\n" +
	"\rresource_type\x18\x04 \x01(\tR\fresourceType\x12(\n" +
	"\rthumbnail_url\x18\x05 \x01(\tH\x00R\fthumbnailUrl\x88\x01\x01\"_\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02B\x10\n" +
	"\x0e_thumbnail_url\"g\n" +
	"\x1bConsumeNowShotStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetailB-Z+boson/api/items/story/v1;api_items_story_v1b\x06proto3"

var (
	file_api_items_story_v1_story_proto_rawDescOnce sync.Once
	file_api_items_story_v1_story_proto_rawDescData []byte
)

func file_api_items_story_v1_story_proto_rawDescGZIP() []byte {
	file_api_items_story_v1_story_proto_rawDescOnce.Do(func() {
		file_api_items_story_v1_story_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v1_story_proto_rawDesc), len(file_api_items_story_v1_story_proto_rawDesc)))
	})
	return file_api_items_story_v1_story_proto_rawDescData
}

var file_api_items_story_v1_story_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_items_story_v1_story_proto_msgTypes = make([]protoimpl.MessageInfo, 46)
var file_api_items_story_v1_story_proto_goTypes = []any{
	(ConsumeNowShotStoryRequest_ResourceType)(0),               // 0: api.items.story.v1.ConsumeNowShotStoryRequest.ResourceType
	(*UpdateStoryRequest)(nil),                                 // 1: api.items.story.v1.UpdateStoryRequest
	(*UpdateStoryResponse)(nil),                                // 2: api.items.story.v1.UpdateStoryResponse
	(*TopStoryRequest)(nil),                                    // 3: api.items.story.v1.TopStoryRequest
	(*DeleteStoryRequest)(nil),                                 // 4: api.items.story.v1.DeleteStoryRequest
	(*ListCommonStoryConditionTemplatesRequest)(nil),           // 5: api.items.story.v1.ListCommonStoryConditionTemplatesRequest
	(*ListCommonStoryConditionTemplatesResponse)(nil),          // 6: api.items.story.v1.ListCommonStoryConditionTemplatesResponse
	(*ListTurtleSoupStoryConditionTemplatesRequest)(nil),       // 7: api.items.story.v1.ListTurtleSoupStoryConditionTemplatesRequest
	(*ListTurtleSoupStoryConditionTemplatesResponse)(nil),      // 8: api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse
	(*ListExchangeImageStoryConditionTemplatesRequest)(nil),    // 9: api.items.story.v1.ListExchangeImageStoryConditionTemplatesRequest
	(*ListExchangeImageStoryConditionTemplatesResponse)(nil),   // 10: api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse
	(*ListUnmuteStoryConditionTemplatesRequest)(nil),           // 11: api.items.story.v1.ListUnmuteStoryConditionTemplatesRequest
	(*ListUnmuteStoryConditionTemplatesResponse)(nil),          // 12: api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse
	(*ListTurtleSoupConditionTemplatesRequest)(nil),            // 13: api.items.story.v1.ListTurtleSoupConditionTemplatesRequest
	(*ListTurtleSoupConditionTemplatesResponse)(nil),           // 14: api.items.story.v1.ListTurtleSoupConditionTemplatesResponse
	(*ListExchangeImageStoryResponse)(nil),                     // 15: api.items.story.v1.ListExchangeImageStoryResponse
	(*CreateExchangeImageStoryRequest)(nil),                    // 16: api.items.story.v1.CreateExchangeImageStoryRequest
	(*CreateExchangeImageStoryResponse)(nil),                   // 17: api.items.story.v1.CreateExchangeImageStoryResponse
	(*CreateTurtleSoupStoryRequest)(nil),                       // 18: api.items.story.v1.CreateTurtleSoupStoryRequest
	(*CreateTurtleSoupStoryResponse)(nil),                      // 19: api.items.story.v1.CreateTurtleSoupStoryResponse
	(*CreateUnmuteStoryRequest)(nil),                           // 20: api.items.story.v1.CreateUnmuteStoryRequest
	(*CreateUnmuteStoryResponse)(nil),                          // 21: api.items.story.v1.CreateUnmuteStoryResponse
	(*CreateBasePlayStoryRequest)(nil),                         // 22: api.items.story.v1.CreateBasePlayStoryRequest
	(*CreateBasePlayStoryResponse)(nil),                        // 23: api.items.story.v1.CreateBasePlayStoryResponse
	(*CreateNowShotStoryRequest)(nil),                          // 24: api.items.story.v1.CreateNowShotStoryRequest
	(*CreateNowShotStoryResponse)(nil),                         // 25: api.items.story.v1.CreateNowShotStoryResponse
	(*GetStoryDetailRequest)(nil),                              // 26: api.items.story.v1.GetStoryDetailRequest
	(*GetStoryDetailResponse)(nil),                             // 27: api.items.story.v1.GetStoryDetailResponse
	(*ListCreatorStoryRequest)(nil),                            // 28: api.items.story.v1.ListCreatorStoryRequest
	(*ListCreatorStoryResponse)(nil),                           // 29: api.items.story.v1.ListCreatorStoryResponse
	(*ListUnlockedStoryRequest)(nil),                           // 30: api.items.story.v1.ListUnlockedStoryRequest
	(*ListUnlockedStoryResponse)(nil),                          // 31: api.items.story.v1.ListUnlockedStoryResponse
	(*ConsumeBasePlayStoryRequest)(nil),                        // 32: api.items.story.v1.ConsumeBasePlayStoryRequest
	(*ConsumeBasePlayStoryResponse)(nil),                       // 33: api.items.story.v1.ConsumeBasePlayStoryResponse
	(*ConsumeExchangeImageStoryRequest)(nil),                   // 34: api.items.story.v1.ConsumeExchangeImageStoryRequest
	(*ConsumeExchangeImageStoryResponse)(nil),                  // 35: api.items.story.v1.ConsumeExchangeImageStoryResponse
	(*ConsumeTurtleSoupStoryRequest)(nil),                      // 36: api.items.story.v1.ConsumeTurtleSoupStoryRequest
	(*ConsumeTurtleSoupStoryResponse)(nil),                     // 37: api.items.story.v1.ConsumeTurtleSoupStoryResponse
	(*ListSameAuthorStoryWithAnchorRequest)(nil),               // 38: api.items.story.v1.ListSameAuthorStoryWithAnchorRequest
	(*ListSameAuthorStoryWithAnchorResponse)(nil),              // 39: api.items.story.v1.ListSameAuthorStoryWithAnchorResponse
	(*ConsumeUnmuteStoryRequest)(nil),                          // 40: api.items.story.v1.ConsumeUnmuteStoryRequest
	(*ConsumeUnmuteStoryResponse)(nil),                         // 41: api.items.story.v1.ConsumeUnmuteStoryResponse
	(*ConsumeNowShotStoryRequest)(nil),                         // 42: api.items.story.v1.ConsumeNowShotStoryRequest
	(*ConsumeNowShotStoryResponse)(nil),                        // 43: api.items.story.v1.ConsumeNowShotStoryResponse
	(*ListCreatorStoryResponse_CreatedStory)(nil),              // 44: api.items.story.v1.ListCreatorStoryResponse.CreatedStory
	(*ListSameAuthorStoryWithAnchorRequest_ListRequest)(nil),   // 45: api.items.story.v1.ListSameAuthorStoryWithAnchorRequest.ListRequest
	(*ListSameAuthorStoryWithAnchorResponse_ListResponse)(nil), // 46: api.items.story.v1.ListSameAuthorStoryWithAnchorResponse.ListResponse
	(*PrivacySettingUpdateAttr)(nil),                           // 47: api.items.story.v1.PrivacySettingUpdateAttr
	(*v1.StoryDetail)(nil),                                     // 48: api.items.story.types.v1.StoryDetail
	(v1.StoryPlayType)(0),                                      // 49: api.items.story.types.v1.StoryPlayType
	(*v1.CommonStoryPlayConditionTemplate)(nil),                // 50: api.items.story.types.v1.CommonStoryPlayConditionTemplate
	(*v1.StoryPlayTurtleConditionTemplate)(nil),                // 51: api.items.story.types.v1.StoryPlayTurtleConditionTemplate
	(*v1.StoryExchangeImageConditionTemplate)(nil),             // 52: api.items.story.types.v1.StoryExchangeImageConditionTemplate
	(*v1.StoryPlayUnmuteConditionTemplate)(nil),                // 53: api.items.story.types.v1.StoryPlayUnmuteConditionTemplate
	(*v1.StoryPlayExchangeImageConfig)(nil),                    // 54: api.items.story.types.v1.StoryPlayExchangeImageConfig
	(*v1.StoryPlayTurtleSoupConfig)(nil),                       // 55: api.items.story.types.v1.StoryPlayTurtleSoupConfig
	(*v1.StoryPlayUnmuteConfig)(nil),                           // 56: api.items.story.types.v1.StoryPlayUnmuteConfig
	(*v1.StoryPlayBasePlayConfig)(nil),                         // 57: api.items.story.types.v1.StoryPlayBasePlayConfig
	(*v1.StoryPlayNowShotConfig)(nil),                          // 58: api.items.story.types.v1.StoryPlayNowShotConfig
	(*v11.ListRequest)(nil),                                    // 59: api.common.v1.ListRequest
	(*v11.ListResponse)(nil),                                   // 60: api.common.v1.ListResponse
	(*v1.StorySummary)(nil),                                    // 61: api.items.story.types.v1.StorySummary
}
var file_api_items_story_v1_story_proto_depIdxs = []int32{
	47, // 0: api.items.story.v1.UpdateStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	48, // 1: api.items.story.v1.UpdateStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	49, // 2: api.items.story.v1.ListCommonStoryConditionTemplatesRequest.story_play_type:type_name -> api.items.story.types.v1.StoryPlayType
	50, // 3: api.items.story.v1.ListCommonStoryConditionTemplatesResponse.condition_templates:type_name -> api.items.story.types.v1.CommonStoryPlayConditionTemplate
	51, // 4: api.items.story.v1.ListTurtleSoupStoryConditionTemplatesResponse.condition_templates:type_name -> api.items.story.types.v1.StoryPlayTurtleConditionTemplate
	52, // 5: api.items.story.v1.ListExchangeImageStoryConditionTemplatesResponse.condition_templates:type_name -> api.items.story.types.v1.StoryExchangeImageConditionTemplate
	53, // 6: api.items.story.v1.ListUnmuteStoryConditionTemplatesResponse.condition_templates:type_name -> api.items.story.types.v1.StoryPlayUnmuteConditionTemplate
	51, // 7: api.items.story.v1.ListTurtleSoupConditionTemplatesResponse.condition_templates:type_name -> api.items.story.types.v1.StoryPlayTurtleConditionTemplate
	48, // 8: api.items.story.v1.ListExchangeImageStoryResponse.stories:type_name -> api.items.story.types.v1.StoryDetail
	54, // 9: api.items.story.v1.CreateExchangeImageStoryRequest.play_config:type_name -> api.items.story.types.v1.StoryPlayExchangeImageConfig
	47, // 10: api.items.story.v1.CreateExchangeImageStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	48, // 11: api.items.story.v1.CreateExchangeImageStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	55, // 12: api.items.story.v1.CreateTurtleSoupStoryRequest.play_config:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupConfig
	47, // 13: api.items.story.v1.CreateTurtleSoupStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	48, // 14: api.items.story.v1.CreateTurtleSoupStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	56, // 15: api.items.story.v1.CreateUnmuteStoryRequest.play_config:type_name -> api.items.story.types.v1.StoryPlayUnmuteConfig
	47, // 16: api.items.story.v1.CreateUnmuteStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	48, // 17: api.items.story.v1.CreateUnmuteStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	57, // 18: api.items.story.v1.CreateBasePlayStoryRequest.play_config:type_name -> api.items.story.types.v1.StoryPlayBasePlayConfig
	47, // 19: api.items.story.v1.CreateBasePlayStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	48, // 20: api.items.story.v1.CreateBasePlayStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	58, // 21: api.items.story.v1.CreateNowShotStoryRequest.play_config:type_name -> api.items.story.types.v1.StoryPlayNowShotConfig
	47, // 22: api.items.story.v1.CreateNowShotStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	48, // 23: api.items.story.v1.CreateNowShotStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	48, // 24: api.items.story.v1.GetStoryDetailResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	59, // 25: api.items.story.v1.ListCreatorStoryRequest.list_request:type_name -> api.common.v1.ListRequest
	44, // 26: api.items.story.v1.ListCreatorStoryResponse.created_stories:type_name -> api.items.story.v1.ListCreatorStoryResponse.CreatedStory
	60, // 27: api.items.story.v1.ListCreatorStoryResponse.list_response:type_name -> api.common.v1.ListResponse
	59, // 28: api.items.story.v1.ListUnlockedStoryRequest.list_request:type_name -> api.common.v1.ListRequest
	49, // 29: api.items.story.v1.ListUnlockedStoryRequest.filter_play_types:type_name -> api.items.story.types.v1.StoryPlayType
	48, // 30: api.items.story.v1.ListUnlockedStoryResponse.unlocked_stories:type_name -> api.items.story.types.v1.StoryDetail
	60, // 31: api.items.story.v1.ListUnlockedStoryResponse.list_response:type_name -> api.common.v1.ListResponse
	48, // 32: api.items.story.v1.ConsumeBasePlayStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	48, // 33: api.items.story.v1.ConsumeExchangeImageStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	48, // 34: api.items.story.v1.ConsumeTurtleSoupStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	45, // 35: api.items.story.v1.ListSameAuthorStoryWithAnchorRequest.list_requests:type_name -> api.items.story.v1.ListSameAuthorStoryWithAnchorRequest.ListRequest
	46, // 36: api.items.story.v1.ListSameAuthorStoryWithAnchorResponse.list_responses:type_name -> api.items.story.v1.ListSameAuthorStoryWithAnchorResponse.ListResponse
	48, // 37: api.items.story.v1.ConsumeUnmuteStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	48, // 38: api.items.story.v1.ConsumeNowShotStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	61, // 39: api.items.story.v1.ListCreatorStoryResponse.CreatedStory.story:type_name -> api.items.story.types.v1.StorySummary
	49, // 40: api.items.story.v1.ListSameAuthorStoryWithAnchorRequest.ListRequest.filter_play_types:type_name -> api.items.story.types.v1.StoryPlayType
	48, // 41: api.items.story.v1.ListSameAuthorStoryWithAnchorResponse.ListResponse.stories:type_name -> api.items.story.types.v1.StoryDetail
	42, // [42:42] is the sub-list for method output_type
	42, // [42:42] is the sub-list for method input_type
	42, // [42:42] is the sub-list for extension type_name
	42, // [42:42] is the sub-list for extension extendee
	0,  // [0:42] is the sub-list for field type_name
}

func init() { file_api_items_story_v1_story_proto_init() }
func file_api_items_story_v1_story_proto_init() {
	if File_api_items_story_v1_story_proto != nil {
		return
	}
	file_api_items_story_v1_story_privacy_setting_proto_init()
	file_api_items_story_v1_story_proto_msgTypes[0].OneofWrappers = []any{}
	file_api_items_story_v1_story_proto_msgTypes[15].OneofWrappers = []any{}
	file_api_items_story_v1_story_proto_msgTypes[17].OneofWrappers = []any{}
	file_api_items_story_v1_story_proto_msgTypes[19].OneofWrappers = []any{}
	file_api_items_story_v1_story_proto_msgTypes[21].OneofWrappers = []any{}
	file_api_items_story_v1_story_proto_msgTypes[23].OneofWrappers = []any{}
	file_api_items_story_v1_story_proto_msgTypes[41].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v1_story_proto_rawDesc), len(file_api_items_story_v1_story_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   46,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v1_story_proto_goTypes,
		DependencyIndexes: file_api_items_story_v1_story_proto_depIdxs,
		EnumInfos:         file_api_items_story_v1_story_proto_enumTypes,
		MessageInfos:      file_api_items_story_v1_story_proto_msgTypes,
	}.Build()
	File_api_items_story_v1_story_proto = out.File
	file_api_items_story_v1_story_proto_goTypes = nil
	file_api_items_story_v1_story_proto_depIdxs = nil
}
