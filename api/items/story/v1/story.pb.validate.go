// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v1/story.proto

package api_items_story_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = api_items_story_types_v1.StoryPlayType(0)
)

// Validate checks the field values on UpdateStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateStoryRequestMultiError, or nil if none found.
func (m *UpdateStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_UpdateStoryRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := UpdateStoryRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateStoryRequestMultiError(errors)
	}

	return nil
}

// UpdateStoryRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateStoryRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateStoryRequestMultiError) AllErrors() []error { return m }

// UpdateStoryRequestValidationError is the validation error returned by
// UpdateStoryRequest.Validate if the designated constraints aren't met.
type UpdateStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateStoryRequestValidationError) ErrorName() string {
	return "UpdateStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateStoryRequestValidationError{}

var _UpdateStoryRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on UpdateStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateStoryResponseMultiError, or nil if none found.
func (m *UpdateStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateStoryResponseMultiError(errors)
	}

	return nil
}

// UpdateStoryResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateStoryResponseMultiError) AllErrors() []error { return m }

// UpdateStoryResponseValidationError is the validation error returned by
// UpdateStoryResponse.Validate if the designated constraints aren't met.
type UpdateStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateStoryResponseValidationError) ErrorName() string {
	return "UpdateStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateStoryResponseValidationError{}

// Validate checks the field values on TopStoryRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TopStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TopStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TopStoryRequestMultiError, or nil if none found.
func (m *TopStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TopStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_TopStoryRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := TopStoryRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IsTop

	if len(errors) > 0 {
		return TopStoryRequestMultiError(errors)
	}

	return nil
}

// TopStoryRequestMultiError is an error wrapping multiple validation errors
// returned by TopStoryRequest.ValidateAll() if the designated constraints
// aren't met.
type TopStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TopStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TopStoryRequestMultiError) AllErrors() []error { return m }

// TopStoryRequestValidationError is the validation error returned by
// TopStoryRequest.Validate if the designated constraints aren't met.
type TopStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TopStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TopStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TopStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TopStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TopStoryRequestValidationError) ErrorName() string { return "TopStoryRequestValidationError" }

// Error satisfies the builtin error interface
func (e TopStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTopStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TopStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TopStoryRequestValidationError{}

var _TopStoryRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on DeleteStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteStoryRequestMultiError, or nil if none found.
func (m *DeleteStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_DeleteStoryRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := DeleteStoryRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteStoryRequestMultiError(errors)
	}

	return nil
}

// DeleteStoryRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteStoryRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteStoryRequestMultiError) AllErrors() []error { return m }

// DeleteStoryRequestValidationError is the validation error returned by
// DeleteStoryRequest.Validate if the designated constraints aren't met.
type DeleteStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteStoryRequestValidationError) ErrorName() string {
	return "DeleteStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteStoryRequestValidationError{}

var _DeleteStoryRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ListCommonStoryConditionTemplatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCommonStoryConditionTemplatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListCommonStoryConditionTemplatesRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListCommonStoryConditionTemplatesRequestMultiError, or nil if none found.
func (m *ListCommonStoryConditionTemplatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommonStoryConditionTemplatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryPlayType

	if len(errors) > 0 {
		return ListCommonStoryConditionTemplatesRequestMultiError(errors)
	}

	return nil
}

// ListCommonStoryConditionTemplatesRequestMultiError is an error wrapping
// multiple validation errors returned by
// ListCommonStoryConditionTemplatesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCommonStoryConditionTemplatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommonStoryConditionTemplatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommonStoryConditionTemplatesRequestMultiError) AllErrors() []error { return m }

// ListCommonStoryConditionTemplatesRequestValidationError is the validation
// error returned by ListCommonStoryConditionTemplatesRequest.Validate if the
// designated constraints aren't met.
type ListCommonStoryConditionTemplatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommonStoryConditionTemplatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommonStoryConditionTemplatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommonStoryConditionTemplatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommonStoryConditionTemplatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommonStoryConditionTemplatesRequestValidationError) ErrorName() string {
	return "ListCommonStoryConditionTemplatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCommonStoryConditionTemplatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommonStoryConditionTemplatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommonStoryConditionTemplatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommonStoryConditionTemplatesRequestValidationError{}

// Validate checks the field values on
// ListCommonStoryConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListCommonStoryConditionTemplatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListCommonStoryConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListCommonStoryConditionTemplatesResponseMultiError, or nil if none found.
func (m *ListCommonStoryConditionTemplatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCommonStoryConditionTemplatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConditionTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCommonStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCommonStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCommonStoryConditionTemplatesResponseValidationError{
					field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListCommonStoryConditionTemplatesResponseMultiError(errors)
	}

	return nil
}

// ListCommonStoryConditionTemplatesResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListCommonStoryConditionTemplatesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCommonStoryConditionTemplatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCommonStoryConditionTemplatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCommonStoryConditionTemplatesResponseMultiError) AllErrors() []error { return m }

// ListCommonStoryConditionTemplatesResponseValidationError is the validation
// error returned by ListCommonStoryConditionTemplatesResponse.Validate if the
// designated constraints aren't met.
type ListCommonStoryConditionTemplatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCommonStoryConditionTemplatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCommonStoryConditionTemplatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCommonStoryConditionTemplatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCommonStoryConditionTemplatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCommonStoryConditionTemplatesResponseValidationError) ErrorName() string {
	return "ListCommonStoryConditionTemplatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCommonStoryConditionTemplatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCommonStoryConditionTemplatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCommonStoryConditionTemplatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCommonStoryConditionTemplatesResponseValidationError{}

// Validate checks the field values on
// ListTurtleSoupStoryConditionTemplatesRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListTurtleSoupStoryConditionTemplatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListTurtleSoupStoryConditionTemplatesRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListTurtleSoupStoryConditionTemplatesRequestMultiError, or nil if none found.
func (m *ListTurtleSoupStoryConditionTemplatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTurtleSoupStoryConditionTemplatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListTurtleSoupStoryConditionTemplatesRequestMultiError(errors)
	}

	return nil
}

// ListTurtleSoupStoryConditionTemplatesRequestMultiError is an error wrapping
// multiple validation errors returned by
// ListTurtleSoupStoryConditionTemplatesRequest.ValidateAll() if the
// designated constraints aren't met.
type ListTurtleSoupStoryConditionTemplatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTurtleSoupStoryConditionTemplatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTurtleSoupStoryConditionTemplatesRequestMultiError) AllErrors() []error { return m }

// ListTurtleSoupStoryConditionTemplatesRequestValidationError is the
// validation error returned by
// ListTurtleSoupStoryConditionTemplatesRequest.Validate if the designated
// constraints aren't met.
type ListTurtleSoupStoryConditionTemplatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTurtleSoupStoryConditionTemplatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTurtleSoupStoryConditionTemplatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTurtleSoupStoryConditionTemplatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTurtleSoupStoryConditionTemplatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTurtleSoupStoryConditionTemplatesRequestValidationError) ErrorName() string {
	return "ListTurtleSoupStoryConditionTemplatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTurtleSoupStoryConditionTemplatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTurtleSoupStoryConditionTemplatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTurtleSoupStoryConditionTemplatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTurtleSoupStoryConditionTemplatesRequestValidationError{}

// Validate checks the field values on
// ListTurtleSoupStoryConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListTurtleSoupStoryConditionTemplatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListTurtleSoupStoryConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListTurtleSoupStoryConditionTemplatesResponseMultiError, or nil if none found.
func (m *ListTurtleSoupStoryConditionTemplatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTurtleSoupStoryConditionTemplatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConditionTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTurtleSoupStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTurtleSoupStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTurtleSoupStoryConditionTemplatesResponseValidationError{
					field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTurtleSoupStoryConditionTemplatesResponseMultiError(errors)
	}

	return nil
}

// ListTurtleSoupStoryConditionTemplatesResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListTurtleSoupStoryConditionTemplatesResponse.ValidateAll() if the
// designated constraints aren't met.
type ListTurtleSoupStoryConditionTemplatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTurtleSoupStoryConditionTemplatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTurtleSoupStoryConditionTemplatesResponseMultiError) AllErrors() []error { return m }

// ListTurtleSoupStoryConditionTemplatesResponseValidationError is the
// validation error returned by
// ListTurtleSoupStoryConditionTemplatesResponse.Validate if the designated
// constraints aren't met.
type ListTurtleSoupStoryConditionTemplatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTurtleSoupStoryConditionTemplatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTurtleSoupStoryConditionTemplatesResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListTurtleSoupStoryConditionTemplatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTurtleSoupStoryConditionTemplatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTurtleSoupStoryConditionTemplatesResponseValidationError) ErrorName() string {
	return "ListTurtleSoupStoryConditionTemplatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTurtleSoupStoryConditionTemplatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTurtleSoupStoryConditionTemplatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTurtleSoupStoryConditionTemplatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTurtleSoupStoryConditionTemplatesResponseValidationError{}

// Validate checks the field values on
// ListExchangeImageStoryConditionTemplatesRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListExchangeImageStoryConditionTemplatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListExchangeImageStoryConditionTemplatesRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ListExchangeImageStoryConditionTemplatesRequestMultiError, or nil if none found.
func (m *ListExchangeImageStoryConditionTemplatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExchangeImageStoryConditionTemplatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListExchangeImageStoryConditionTemplatesRequestMultiError(errors)
	}

	return nil
}

// ListExchangeImageStoryConditionTemplatesRequestMultiError is an error
// wrapping multiple validation errors returned by
// ListExchangeImageStoryConditionTemplatesRequest.ValidateAll() if the
// designated constraints aren't met.
type ListExchangeImageStoryConditionTemplatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExchangeImageStoryConditionTemplatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExchangeImageStoryConditionTemplatesRequestMultiError) AllErrors() []error { return m }

// ListExchangeImageStoryConditionTemplatesRequestValidationError is the
// validation error returned by
// ListExchangeImageStoryConditionTemplatesRequest.Validate if the designated
// constraints aren't met.
type ListExchangeImageStoryConditionTemplatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExchangeImageStoryConditionTemplatesRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListExchangeImageStoryConditionTemplatesRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListExchangeImageStoryConditionTemplatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExchangeImageStoryConditionTemplatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExchangeImageStoryConditionTemplatesRequestValidationError) ErrorName() string {
	return "ListExchangeImageStoryConditionTemplatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListExchangeImageStoryConditionTemplatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExchangeImageStoryConditionTemplatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExchangeImageStoryConditionTemplatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExchangeImageStoryConditionTemplatesRequestValidationError{}

// Validate checks the field values on
// ListExchangeImageStoryConditionTemplatesResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListExchangeImageStoryConditionTemplatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListExchangeImageStoryConditionTemplatesResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ListExchangeImageStoryConditionTemplatesResponseMultiError, or nil if none found.
func (m *ListExchangeImageStoryConditionTemplatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExchangeImageStoryConditionTemplatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConditionTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExchangeImageStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExchangeImageStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExchangeImageStoryConditionTemplatesResponseValidationError{
					field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExchangeImageStoryConditionTemplatesResponseMultiError(errors)
	}

	return nil
}

// ListExchangeImageStoryConditionTemplatesResponseMultiError is an error
// wrapping multiple validation errors returned by
// ListExchangeImageStoryConditionTemplatesResponse.ValidateAll() if the
// designated constraints aren't met.
type ListExchangeImageStoryConditionTemplatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExchangeImageStoryConditionTemplatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExchangeImageStoryConditionTemplatesResponseMultiError) AllErrors() []error { return m }

// ListExchangeImageStoryConditionTemplatesResponseValidationError is the
// validation error returned by
// ListExchangeImageStoryConditionTemplatesResponse.Validate if the designated
// constraints aren't met.
type ListExchangeImageStoryConditionTemplatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExchangeImageStoryConditionTemplatesResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListExchangeImageStoryConditionTemplatesResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListExchangeImageStoryConditionTemplatesResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListExchangeImageStoryConditionTemplatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExchangeImageStoryConditionTemplatesResponseValidationError) ErrorName() string {
	return "ListExchangeImageStoryConditionTemplatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListExchangeImageStoryConditionTemplatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExchangeImageStoryConditionTemplatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExchangeImageStoryConditionTemplatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExchangeImageStoryConditionTemplatesResponseValidationError{}

// Validate checks the field values on ListUnmuteStoryConditionTemplatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListUnmuteStoryConditionTemplatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListUnmuteStoryConditionTemplatesRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListUnmuteStoryConditionTemplatesRequestMultiError, or nil if none found.
func (m *ListUnmuteStoryConditionTemplatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUnmuteStoryConditionTemplatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListUnmuteStoryConditionTemplatesRequestMultiError(errors)
	}

	return nil
}

// ListUnmuteStoryConditionTemplatesRequestMultiError is an error wrapping
// multiple validation errors returned by
// ListUnmuteStoryConditionTemplatesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUnmuteStoryConditionTemplatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUnmuteStoryConditionTemplatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUnmuteStoryConditionTemplatesRequestMultiError) AllErrors() []error { return m }

// ListUnmuteStoryConditionTemplatesRequestValidationError is the validation
// error returned by ListUnmuteStoryConditionTemplatesRequest.Validate if the
// designated constraints aren't met.
type ListUnmuteStoryConditionTemplatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUnmuteStoryConditionTemplatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUnmuteStoryConditionTemplatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUnmuteStoryConditionTemplatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUnmuteStoryConditionTemplatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUnmuteStoryConditionTemplatesRequestValidationError) ErrorName() string {
	return "ListUnmuteStoryConditionTemplatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUnmuteStoryConditionTemplatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUnmuteStoryConditionTemplatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUnmuteStoryConditionTemplatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUnmuteStoryConditionTemplatesRequestValidationError{}

// Validate checks the field values on
// ListUnmuteStoryConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListUnmuteStoryConditionTemplatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListUnmuteStoryConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListUnmuteStoryConditionTemplatesResponseMultiError, or nil if none found.
func (m *ListUnmuteStoryConditionTemplatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUnmuteStoryConditionTemplatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConditionTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUnmuteStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUnmuteStoryConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUnmuteStoryConditionTemplatesResponseValidationError{
					field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListUnmuteStoryConditionTemplatesResponseMultiError(errors)
	}

	return nil
}

// ListUnmuteStoryConditionTemplatesResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListUnmuteStoryConditionTemplatesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListUnmuteStoryConditionTemplatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUnmuteStoryConditionTemplatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUnmuteStoryConditionTemplatesResponseMultiError) AllErrors() []error { return m }

// ListUnmuteStoryConditionTemplatesResponseValidationError is the validation
// error returned by ListUnmuteStoryConditionTemplatesResponse.Validate if the
// designated constraints aren't met.
type ListUnmuteStoryConditionTemplatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUnmuteStoryConditionTemplatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUnmuteStoryConditionTemplatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUnmuteStoryConditionTemplatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUnmuteStoryConditionTemplatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUnmuteStoryConditionTemplatesResponseValidationError) ErrorName() string {
	return "ListUnmuteStoryConditionTemplatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListUnmuteStoryConditionTemplatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUnmuteStoryConditionTemplatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUnmuteStoryConditionTemplatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUnmuteStoryConditionTemplatesResponseValidationError{}

// Validate checks the field values on ListTurtleSoupConditionTemplatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListTurtleSoupConditionTemplatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListTurtleSoupConditionTemplatesRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListTurtleSoupConditionTemplatesRequestMultiError, or nil if none found.
func (m *ListTurtleSoupConditionTemplatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTurtleSoupConditionTemplatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListTurtleSoupConditionTemplatesRequestMultiError(errors)
	}

	return nil
}

// ListTurtleSoupConditionTemplatesRequestMultiError is an error wrapping
// multiple validation errors returned by
// ListTurtleSoupConditionTemplatesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListTurtleSoupConditionTemplatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTurtleSoupConditionTemplatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTurtleSoupConditionTemplatesRequestMultiError) AllErrors() []error { return m }

// ListTurtleSoupConditionTemplatesRequestValidationError is the validation
// error returned by ListTurtleSoupConditionTemplatesRequest.Validate if the
// designated constraints aren't met.
type ListTurtleSoupConditionTemplatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTurtleSoupConditionTemplatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTurtleSoupConditionTemplatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTurtleSoupConditionTemplatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTurtleSoupConditionTemplatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTurtleSoupConditionTemplatesRequestValidationError) ErrorName() string {
	return "ListTurtleSoupConditionTemplatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTurtleSoupConditionTemplatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTurtleSoupConditionTemplatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTurtleSoupConditionTemplatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTurtleSoupConditionTemplatesRequestValidationError{}

// Validate checks the field values on ListTurtleSoupConditionTemplatesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListTurtleSoupConditionTemplatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListTurtleSoupConditionTemplatesResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ListTurtleSoupConditionTemplatesResponseMultiError, or nil if none found.
func (m *ListTurtleSoupConditionTemplatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTurtleSoupConditionTemplatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetConditionTemplates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTurtleSoupConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTurtleSoupConditionTemplatesResponseValidationError{
						field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTurtleSoupConditionTemplatesResponseValidationError{
					field:  fmt.Sprintf("ConditionTemplates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTurtleSoupConditionTemplatesResponseMultiError(errors)
	}

	return nil
}

// ListTurtleSoupConditionTemplatesResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListTurtleSoupConditionTemplatesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListTurtleSoupConditionTemplatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTurtleSoupConditionTemplatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTurtleSoupConditionTemplatesResponseMultiError) AllErrors() []error { return m }

// ListTurtleSoupConditionTemplatesResponseValidationError is the validation
// error returned by ListTurtleSoupConditionTemplatesResponse.Validate if the
// designated constraints aren't met.
type ListTurtleSoupConditionTemplatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTurtleSoupConditionTemplatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTurtleSoupConditionTemplatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTurtleSoupConditionTemplatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTurtleSoupConditionTemplatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTurtleSoupConditionTemplatesResponseValidationError) ErrorName() string {
	return "ListTurtleSoupConditionTemplatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTurtleSoupConditionTemplatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTurtleSoupConditionTemplatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTurtleSoupConditionTemplatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTurtleSoupConditionTemplatesResponseValidationError{}

// Validate checks the field values on ListExchangeImageStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExchangeImageStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExchangeImageStoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListExchangeImageStoryResponseMultiError, or nil if none found.
func (m *ListExchangeImageStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExchangeImageStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExchangeImageStoryResponseValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExchangeImageStoryResponseValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExchangeImageStoryResponseValidationError{
					field:  fmt.Sprintf("Stories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExchangeImageStoryResponseMultiError(errors)
	}

	return nil
}

// ListExchangeImageStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ListExchangeImageStoryResponse.ValidateAll()
// if the designated constraints aren't met.
type ListExchangeImageStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExchangeImageStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExchangeImageStoryResponseMultiError) AllErrors() []error { return m }

// ListExchangeImageStoryResponseValidationError is the validation error
// returned by ListExchangeImageStoryResponse.Validate if the designated
// constraints aren't met.
type ListExchangeImageStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExchangeImageStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExchangeImageStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExchangeImageStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExchangeImageStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExchangeImageStoryResponseValidationError) ErrorName() string {
	return "ListExchangeImageStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListExchangeImageStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExchangeImageStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExchangeImageStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExchangeImageStoryResponseValidationError{}

// Validate checks the field values on CreateExchangeImageStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateExchangeImageStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangeImageStoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateExchangeImageStoryRequestMultiError, or nil if none found.
func (m *CreateExchangeImageStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangeImageStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangeImageStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangeImageStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangeImageStoryRequestValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateExchangeImageStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateExchangeImageStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateExchangeImageStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateExchangeImageStoryRequestMultiError(errors)
	}

	return nil
}

// CreateExchangeImageStoryRequestMultiError is an error wrapping multiple
// validation errors returned by CreateExchangeImageStoryRequest.ValidateAll()
// if the designated constraints aren't met.
type CreateExchangeImageStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangeImageStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangeImageStoryRequestMultiError) AllErrors() []error { return m }

// CreateExchangeImageStoryRequestValidationError is the validation error
// returned by CreateExchangeImageStoryRequest.Validate if the designated
// constraints aren't met.
type CreateExchangeImageStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangeImageStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangeImageStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangeImageStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangeImageStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangeImageStoryRequestValidationError) ErrorName() string {
	return "CreateExchangeImageStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangeImageStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangeImageStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangeImageStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangeImageStoryRequestValidationError{}

// Validate checks the field values on CreateExchangeImageStoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangeImageStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangeImageStoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateExchangeImageStoryResponseMultiError, or nil if none found.
func (m *CreateExchangeImageStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangeImageStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangeImageStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangeImageStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangeImageStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangeImageStoryResponseMultiError(errors)
	}

	return nil
}

// CreateExchangeImageStoryResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangeImageStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangeImageStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangeImageStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangeImageStoryResponseMultiError) AllErrors() []error { return m }

// CreateExchangeImageStoryResponseValidationError is the validation error
// returned by CreateExchangeImageStoryResponse.Validate if the designated
// constraints aren't met.
type CreateExchangeImageStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangeImageStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangeImageStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangeImageStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangeImageStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangeImageStoryResponseValidationError) ErrorName() string {
	return "CreateExchangeImageStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangeImageStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangeImageStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangeImageStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangeImageStoryResponseValidationError{}

// Validate checks the field values on CreateTurtleSoupStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTurtleSoupStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTurtleSoupStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTurtleSoupStoryRequestMultiError, or nil if none found.
func (m *CreateTurtleSoupStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTurtleSoupStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTurtleSoupStoryRequestValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsMass

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateTurtleSoupStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateTurtleSoupStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateTurtleSoupStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateTurtleSoupStoryRequestMultiError(errors)
	}

	return nil
}

// CreateTurtleSoupStoryRequestMultiError is an error wrapping multiple
// validation errors returned by CreateTurtleSoupStoryRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateTurtleSoupStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTurtleSoupStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTurtleSoupStoryRequestMultiError) AllErrors() []error { return m }

// CreateTurtleSoupStoryRequestValidationError is the validation error returned
// by CreateTurtleSoupStoryRequest.Validate if the designated constraints
// aren't met.
type CreateTurtleSoupStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTurtleSoupStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTurtleSoupStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTurtleSoupStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTurtleSoupStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTurtleSoupStoryRequestValidationError) ErrorName() string {
	return "CreateTurtleSoupStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTurtleSoupStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTurtleSoupStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTurtleSoupStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTurtleSoupStoryRequestValidationError{}

// Validate checks the field values on CreateTurtleSoupStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTurtleSoupStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTurtleSoupStoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateTurtleSoupStoryResponseMultiError, or nil if none found.
func (m *CreateTurtleSoupStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTurtleSoupStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTurtleSoupStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTurtleSoupStoryResponseMultiError(errors)
	}

	return nil
}

// CreateTurtleSoupStoryResponseMultiError is an error wrapping multiple
// validation errors returned by CreateTurtleSoupStoryResponse.ValidateAll()
// if the designated constraints aren't met.
type CreateTurtleSoupStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTurtleSoupStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTurtleSoupStoryResponseMultiError) AllErrors() []error { return m }

// CreateTurtleSoupStoryResponseValidationError is the validation error
// returned by CreateTurtleSoupStoryResponse.Validate if the designated
// constraints aren't met.
type CreateTurtleSoupStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTurtleSoupStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTurtleSoupStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTurtleSoupStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTurtleSoupStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTurtleSoupStoryResponseValidationError) ErrorName() string {
	return "CreateTurtleSoupStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTurtleSoupStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTurtleSoupStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTurtleSoupStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTurtleSoupStoryResponseValidationError{}

// Validate checks the field values on CreateUnmuteStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUnmuteStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUnmuteStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUnmuteStoryRequestMultiError, or nil if none found.
func (m *CreateUnmuteStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUnmuteStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUnmuteStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUnmuteStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUnmuteStoryRequestValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateUnmuteStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateUnmuteStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateUnmuteStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateUnmuteStoryRequestMultiError(errors)
	}

	return nil
}

// CreateUnmuteStoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreateUnmuteStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateUnmuteStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUnmuteStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUnmuteStoryRequestMultiError) AllErrors() []error { return m }

// CreateUnmuteStoryRequestValidationError is the validation error returned by
// CreateUnmuteStoryRequest.Validate if the designated constraints aren't met.
type CreateUnmuteStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUnmuteStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUnmuteStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUnmuteStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUnmuteStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUnmuteStoryRequestValidationError) ErrorName() string {
	return "CreateUnmuteStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUnmuteStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUnmuteStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUnmuteStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUnmuteStoryRequestValidationError{}

// Validate checks the field values on CreateUnmuteStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUnmuteStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUnmuteStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUnmuteStoryResponseMultiError, or nil if none found.
func (m *CreateUnmuteStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUnmuteStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUnmuteStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUnmuteStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUnmuteStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUnmuteStoryResponseMultiError(errors)
	}

	return nil
}

// CreateUnmuteStoryResponseMultiError is an error wrapping multiple validation
// errors returned by CreateUnmuteStoryResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateUnmuteStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUnmuteStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUnmuteStoryResponseMultiError) AllErrors() []error { return m }

// CreateUnmuteStoryResponseValidationError is the validation error returned by
// CreateUnmuteStoryResponse.Validate if the designated constraints aren't met.
type CreateUnmuteStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUnmuteStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUnmuteStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUnmuteStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUnmuteStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUnmuteStoryResponseValidationError) ErrorName() string {
	return "CreateUnmuteStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUnmuteStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUnmuteStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUnmuteStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUnmuteStoryResponseValidationError{}

// Validate checks the field values on CreateBasePlayStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBasePlayStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBasePlayStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBasePlayStoryRequestMultiError, or nil if none found.
func (m *CreateBasePlayStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBasePlayStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBasePlayStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBasePlayStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBasePlayStoryRequestValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateBasePlayStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateBasePlayStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateBasePlayStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateBasePlayStoryRequestMultiError(errors)
	}

	return nil
}

// CreateBasePlayStoryRequestMultiError is an error wrapping multiple
// validation errors returned by CreateBasePlayStoryRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateBasePlayStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBasePlayStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBasePlayStoryRequestMultiError) AllErrors() []error { return m }

// CreateBasePlayStoryRequestValidationError is the validation error returned
// by CreateBasePlayStoryRequest.Validate if the designated constraints aren't met.
type CreateBasePlayStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBasePlayStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBasePlayStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBasePlayStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBasePlayStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBasePlayStoryRequestValidationError) ErrorName() string {
	return "CreateBasePlayStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBasePlayStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBasePlayStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBasePlayStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBasePlayStoryRequestValidationError{}

// Validate checks the field values on CreateBasePlayStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateBasePlayStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateBasePlayStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateBasePlayStoryResponseMultiError, or nil if none found.
func (m *CreateBasePlayStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateBasePlayStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateBasePlayStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateBasePlayStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateBasePlayStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateBasePlayStoryResponseMultiError(errors)
	}

	return nil
}

// CreateBasePlayStoryResponseMultiError is an error wrapping multiple
// validation errors returned by CreateBasePlayStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateBasePlayStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateBasePlayStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateBasePlayStoryResponseMultiError) AllErrors() []error { return m }

// CreateBasePlayStoryResponseValidationError is the validation error returned
// by CreateBasePlayStoryResponse.Validate if the designated constraints
// aren't met.
type CreateBasePlayStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateBasePlayStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateBasePlayStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateBasePlayStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateBasePlayStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateBasePlayStoryResponseValidationError) ErrorName() string {
	return "CreateBasePlayStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateBasePlayStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateBasePlayStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateBasePlayStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateBasePlayStoryResponseValidationError{}

// Validate checks the field values on CreateNowShotStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNowShotStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNowShotStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNowShotStoryRequestMultiError, or nil if none found.
func (m *CreateNowShotStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNowShotStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNowShotStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNowShotStoryRequestValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNowShotStoryRequestValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateNowShotStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateNowShotStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateNowShotStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateNowShotStoryRequestMultiError(errors)
	}

	return nil
}

// CreateNowShotStoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreateNowShotStoryRequest.ValidateAll() if the
// designated constraints aren't met.
type CreateNowShotStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNowShotStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNowShotStoryRequestMultiError) AllErrors() []error { return m }

// CreateNowShotStoryRequestValidationError is the validation error returned by
// CreateNowShotStoryRequest.Validate if the designated constraints aren't met.
type CreateNowShotStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNowShotStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNowShotStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNowShotStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNowShotStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNowShotStoryRequestValidationError) ErrorName() string {
	return "CreateNowShotStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNowShotStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNowShotStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNowShotStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNowShotStoryRequestValidationError{}

// Validate checks the field values on CreateNowShotStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNowShotStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNowShotStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNowShotStoryResponseMultiError, or nil if none found.
func (m *CreateNowShotStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNowShotStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNowShotStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNowShotStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNowShotStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNowShotStoryResponseMultiError(errors)
	}

	return nil
}

// CreateNowShotStoryResponseMultiError is an error wrapping multiple
// validation errors returned by CreateNowShotStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateNowShotStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNowShotStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNowShotStoryResponseMultiError) AllErrors() []error { return m }

// CreateNowShotStoryResponseValidationError is the validation error returned
// by CreateNowShotStoryResponse.Validate if the designated constraints aren't met.
type CreateNowShotStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNowShotStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNowShotStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNowShotStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNowShotStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNowShotStoryResponseValidationError) ErrorName() string {
	return "CreateNowShotStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNowShotStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNowShotStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNowShotStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNowShotStoryResponseValidationError{}

// Validate checks the field values on GetStoryDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetStoryDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetStoryDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetStoryDetailRequestMultiError, or nil if none found.
func (m *GetStoryDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetStoryDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetStoryDetailRequestMultiError(errors)
	}

	return nil
}

// GetStoryDetailRequestMultiError is an error wrapping multiple validation
// errors returned by GetStoryDetailRequest.ValidateAll() if the designated
// constraints aren't met.
type GetStoryDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetStoryDetailRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetStoryDetailRequestMultiError) AllErrors() []error { return m }

// GetStoryDetailRequestValidationError is the validation error returned by
// GetStoryDetailRequest.Validate if the designated constraints aren't met.
type GetStoryDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetStoryDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetStoryDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetStoryDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetStoryDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetStoryDetailRequestValidationError) ErrorName() string {
	return "GetStoryDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetStoryDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetStoryDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetStoryDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetStoryDetailRequestValidationError{}

// Validate checks the field values on GetStoryDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetStoryDetailResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetStoryDetailResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetStoryDetailResponseMultiError, or nil if none found.
func (m *GetStoryDetailResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetStoryDetailResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetStoryDetailResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetStoryDetailResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetStoryDetailResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetStoryDetailResponseMultiError(errors)
	}

	return nil
}

// GetStoryDetailResponseMultiError is an error wrapping multiple validation
// errors returned by GetStoryDetailResponse.ValidateAll() if the designated
// constraints aren't met.
type GetStoryDetailResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetStoryDetailResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetStoryDetailResponseMultiError) AllErrors() []error { return m }

// GetStoryDetailResponseValidationError is the validation error returned by
// GetStoryDetailResponse.Validate if the designated constraints aren't met.
type GetStoryDetailResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetStoryDetailResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetStoryDetailResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetStoryDetailResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetStoryDetailResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetStoryDetailResponseValidationError) ErrorName() string {
	return "GetStoryDetailResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetStoryDetailResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetStoryDetailResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetStoryDetailResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetStoryDetailResponseValidationError{}

// Validate checks the field values on ListCreatorStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCreatorStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCreatorStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCreatorStoryRequestMultiError, or nil if none found.
func (m *ListCreatorStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCreatorStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ListCreatorStoryRequest_CreatorId_Pattern.MatchString(m.GetCreatorId()) {
		err := ListCreatorStoryRequestValidationError{
			field:  "CreatorId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetListRequest() == nil {
		err := ListCreatorStoryRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCreatorStoryRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCreatorStoryRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCreatorStoryRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCreatorStoryRequestMultiError(errors)
	}

	return nil
}

// ListCreatorStoryRequestMultiError is an error wrapping multiple validation
// errors returned by ListCreatorStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type ListCreatorStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCreatorStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCreatorStoryRequestMultiError) AllErrors() []error { return m }

// ListCreatorStoryRequestValidationError is the validation error returned by
// ListCreatorStoryRequest.Validate if the designated constraints aren't met.
type ListCreatorStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCreatorStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCreatorStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCreatorStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCreatorStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCreatorStoryRequestValidationError) ErrorName() string {
	return "ListCreatorStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListCreatorStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCreatorStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCreatorStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCreatorStoryRequestValidationError{}

var _ListCreatorStoryRequest_CreatorId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ListCreatorStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCreatorStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCreatorStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCreatorStoryResponseMultiError, or nil if none found.
func (m *ListCreatorStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCreatorStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCreatedStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCreatorStoryResponseValidationError{
						field:  fmt.Sprintf("CreatedStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCreatorStoryResponseValidationError{
						field:  fmt.Sprintf("CreatedStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCreatorStoryResponseValidationError{
					field:  fmt.Sprintf("CreatedStories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCreatorStoryResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCreatorStoryResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCreatorStoryResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCreatorStoryResponseMultiError(errors)
	}

	return nil
}

// ListCreatorStoryResponseMultiError is an error wrapping multiple validation
// errors returned by ListCreatorStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type ListCreatorStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCreatorStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCreatorStoryResponseMultiError) AllErrors() []error { return m }

// ListCreatorStoryResponseValidationError is the validation error returned by
// ListCreatorStoryResponse.Validate if the designated constraints aren't met.
type ListCreatorStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCreatorStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCreatorStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCreatorStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCreatorStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCreatorStoryResponseValidationError) ErrorName() string {
	return "ListCreatorStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListCreatorStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCreatorStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCreatorStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCreatorStoryResponseValidationError{}

// Validate checks the field values on ListUnlockedStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUnlockedStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUnlockedStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUnlockedStoryRequestMultiError, or nil if none found.
func (m *ListUnlockedStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUnlockedStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListUnlockedStoryRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUnlockedStoryRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUnlockedStoryRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUnlockedStoryRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListUnlockedStoryRequestMultiError(errors)
	}

	return nil
}

// ListUnlockedStoryRequestMultiError is an error wrapping multiple validation
// errors returned by ListUnlockedStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type ListUnlockedStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUnlockedStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUnlockedStoryRequestMultiError) AllErrors() []error { return m }

// ListUnlockedStoryRequestValidationError is the validation error returned by
// ListUnlockedStoryRequest.Validate if the designated constraints aren't met.
type ListUnlockedStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUnlockedStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUnlockedStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUnlockedStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUnlockedStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUnlockedStoryRequestValidationError) ErrorName() string {
	return "ListUnlockedStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListUnlockedStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUnlockedStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUnlockedStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUnlockedStoryRequestValidationError{}

// Validate checks the field values on ListUnlockedStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListUnlockedStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListUnlockedStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListUnlockedStoryResponseMultiError, or nil if none found.
func (m *ListUnlockedStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListUnlockedStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUnlockedStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListUnlockedStoryResponseValidationError{
						field:  fmt.Sprintf("UnlockedStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListUnlockedStoryResponseValidationError{
						field:  fmt.Sprintf("UnlockedStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListUnlockedStoryResponseValidationError{
					field:  fmt.Sprintf("UnlockedStories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListUnlockedStoryResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListUnlockedStoryResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListUnlockedStoryResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListUnlockedStoryResponseMultiError(errors)
	}

	return nil
}

// ListUnlockedStoryResponseMultiError is an error wrapping multiple validation
// errors returned by ListUnlockedStoryResponse.ValidateAll() if the
// designated constraints aren't met.
type ListUnlockedStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListUnlockedStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListUnlockedStoryResponseMultiError) AllErrors() []error { return m }

// ListUnlockedStoryResponseValidationError is the validation error returned by
// ListUnlockedStoryResponse.Validate if the designated constraints aren't met.
type ListUnlockedStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListUnlockedStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListUnlockedStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListUnlockedStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListUnlockedStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListUnlockedStoryResponseValidationError) ErrorName() string {
	return "ListUnlockedStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListUnlockedStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListUnlockedStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListUnlockedStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListUnlockedStoryResponseValidationError{}

// Validate checks the field values on ConsumeBasePlayStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeBasePlayStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeBasePlayStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeBasePlayStoryRequestMultiError, or nil if none found.
func (m *ConsumeBasePlayStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeBasePlayStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for CurrentNodeIdx

	if len(errors) > 0 {
		return ConsumeBasePlayStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeBasePlayStoryRequestMultiError is an error wrapping multiple
// validation errors returned by ConsumeBasePlayStoryRequest.ValidateAll() if
// the designated constraints aren't met.
type ConsumeBasePlayStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeBasePlayStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeBasePlayStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeBasePlayStoryRequestValidationError is the validation error returned
// by ConsumeBasePlayStoryRequest.Validate if the designated constraints
// aren't met.
type ConsumeBasePlayStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeBasePlayStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeBasePlayStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeBasePlayStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeBasePlayStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeBasePlayStoryRequestValidationError) ErrorName() string {
	return "ConsumeBasePlayStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeBasePlayStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeBasePlayStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeBasePlayStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeBasePlayStoryRequestValidationError{}

// Validate checks the field values on ConsumeBasePlayStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeBasePlayStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeBasePlayStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeBasePlayStoryResponseMultiError, or nil if none found.
func (m *ConsumeBasePlayStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeBasePlayStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeBasePlayStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeBasePlayStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeBasePlayStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeBasePlayStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeBasePlayStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeBasePlayStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type ConsumeBasePlayStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeBasePlayStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeBasePlayStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeBasePlayStoryResponseValidationError is the validation error returned
// by ConsumeBasePlayStoryResponse.Validate if the designated constraints
// aren't met.
type ConsumeBasePlayStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeBasePlayStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeBasePlayStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeBasePlayStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeBasePlayStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeBasePlayStoryResponseValidationError) ErrorName() string {
	return "ConsumeBasePlayStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeBasePlayStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeBasePlayStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeBasePlayStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeBasePlayStoryResponseValidationError{}

// Validate checks the field values on ConsumeExchangeImageStoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConsumeExchangeImageStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeExchangeImageStoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConsumeExchangeImageStoryRequestMultiError, or nil if none found.
func (m *ConsumeExchangeImageStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeExchangeImageStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for ImageKey

	if len(errors) > 0 {
		return ConsumeExchangeImageStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeExchangeImageStoryRequestMultiError is an error wrapping multiple
// validation errors returned by
// ConsumeExchangeImageStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type ConsumeExchangeImageStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeExchangeImageStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeExchangeImageStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeExchangeImageStoryRequestValidationError is the validation error
// returned by ConsumeExchangeImageStoryRequest.Validate if the designated
// constraints aren't met.
type ConsumeExchangeImageStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeExchangeImageStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeExchangeImageStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeExchangeImageStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeExchangeImageStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeExchangeImageStoryRequestValidationError) ErrorName() string {
	return "ConsumeExchangeImageStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeExchangeImageStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeExchangeImageStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeExchangeImageStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeExchangeImageStoryRequestValidationError{}

// Validate checks the field values on ConsumeExchangeImageStoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ConsumeExchangeImageStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeExchangeImageStoryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ConsumeExchangeImageStoryResponseMultiError, or nil if none found.
func (m *ConsumeExchangeImageStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeExchangeImageStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MatchStatus

	// no validation rules for AiResponse

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeExchangeImageStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeExchangeImageStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeExchangeImageStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeExchangeImageStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeExchangeImageStoryResponseMultiError is an error wrapping multiple
// validation errors returned by
// ConsumeExchangeImageStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type ConsumeExchangeImageStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeExchangeImageStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeExchangeImageStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeExchangeImageStoryResponseValidationError is the validation error
// returned by ConsumeExchangeImageStoryResponse.Validate if the designated
// constraints aren't met.
type ConsumeExchangeImageStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeExchangeImageStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeExchangeImageStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeExchangeImageStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeExchangeImageStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeExchangeImageStoryResponseValidationError) ErrorName() string {
	return "ConsumeExchangeImageStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeExchangeImageStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeExchangeImageStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeExchangeImageStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeExchangeImageStoryResponseValidationError{}

// Validate checks the field values on ConsumeTurtleSoupStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeTurtleSoupStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeTurtleSoupStoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConsumeTurtleSoupStoryRequestMultiError, or nil if none found.
func (m *ConsumeTurtleSoupStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeTurtleSoupStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for UserMessage

	if len(errors) > 0 {
		return ConsumeTurtleSoupStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeTurtleSoupStoryRequestMultiError is an error wrapping multiple
// validation errors returned by ConsumeTurtleSoupStoryRequest.ValidateAll()
// if the designated constraints aren't met.
type ConsumeTurtleSoupStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeTurtleSoupStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeTurtleSoupStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeTurtleSoupStoryRequestValidationError is the validation error
// returned by ConsumeTurtleSoupStoryRequest.Validate if the designated
// constraints aren't met.
type ConsumeTurtleSoupStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeTurtleSoupStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeTurtleSoupStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeTurtleSoupStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeTurtleSoupStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeTurtleSoupStoryRequestValidationError) ErrorName() string {
	return "ConsumeTurtleSoupStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeTurtleSoupStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeTurtleSoupStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeTurtleSoupStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeTurtleSoupStoryRequestValidationError{}

// Validate checks the field values on ConsumeTurtleSoupStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeTurtleSoupStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeTurtleSoupStoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConsumeTurtleSoupStoryResponseMultiError, or nil if none found.
func (m *ConsumeTurtleSoupStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeTurtleSoupStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeTurtleSoupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeTurtleSoupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeTurtleSoupStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeTurtleSoupStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeTurtleSoupStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeTurtleSoupStoryResponse.ValidateAll()
// if the designated constraints aren't met.
type ConsumeTurtleSoupStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeTurtleSoupStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeTurtleSoupStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeTurtleSoupStoryResponseValidationError is the validation error
// returned by ConsumeTurtleSoupStoryResponse.Validate if the designated
// constraints aren't met.
type ConsumeTurtleSoupStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeTurtleSoupStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeTurtleSoupStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeTurtleSoupStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeTurtleSoupStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeTurtleSoupStoryResponseValidationError) ErrorName() string {
	return "ConsumeTurtleSoupStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeTurtleSoupStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeTurtleSoupStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeTurtleSoupStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeTurtleSoupStoryResponseValidationError{}

// Validate checks the field values on ListSameAuthorStoryWithAnchorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListSameAuthorStoryWithAnchorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSameAuthorStoryWithAnchorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListSameAuthorStoryWithAnchorRequestMultiError, or nil if none found.
func (m *ListSameAuthorStoryWithAnchorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSameAuthorStoryWithAnchorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AnchorStoryId

	if l := len(m.GetListRequests()); l < 1 || l > 2 {
		err := ListSameAuthorStoryWithAnchorRequestValidationError{
			field:  "ListRequests",
			reason: "value must contain between 1 and 2 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetListRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSameAuthorStoryWithAnchorRequestValidationError{
						field:  fmt.Sprintf("ListRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSameAuthorStoryWithAnchorRequestValidationError{
						field:  fmt.Sprintf("ListRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSameAuthorStoryWithAnchorRequestValidationError{
					field:  fmt.Sprintf("ListRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListSameAuthorStoryWithAnchorRequestMultiError(errors)
	}

	return nil
}

// ListSameAuthorStoryWithAnchorRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListSameAuthorStoryWithAnchorRequest.ValidateAll() if the designated
// constraints aren't met.
type ListSameAuthorStoryWithAnchorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSameAuthorStoryWithAnchorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSameAuthorStoryWithAnchorRequestMultiError) AllErrors() []error { return m }

// ListSameAuthorStoryWithAnchorRequestValidationError is the validation error
// returned by ListSameAuthorStoryWithAnchorRequest.Validate if the designated
// constraints aren't met.
type ListSameAuthorStoryWithAnchorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSameAuthorStoryWithAnchorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSameAuthorStoryWithAnchorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSameAuthorStoryWithAnchorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSameAuthorStoryWithAnchorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSameAuthorStoryWithAnchorRequestValidationError) ErrorName() string {
	return "ListSameAuthorStoryWithAnchorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSameAuthorStoryWithAnchorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSameAuthorStoryWithAnchorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSameAuthorStoryWithAnchorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSameAuthorStoryWithAnchorRequestValidationError{}

// Validate checks the field values on ListSameAuthorStoryWithAnchorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListSameAuthorStoryWithAnchorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSameAuthorStoryWithAnchorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListSameAuthorStoryWithAnchorResponseMultiError, or nil if none found.
func (m *ListSameAuthorStoryWithAnchorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSameAuthorStoryWithAnchorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetListResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSameAuthorStoryWithAnchorResponseValidationError{
						field:  fmt.Sprintf("ListResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSameAuthorStoryWithAnchorResponseValidationError{
						field:  fmt.Sprintf("ListResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSameAuthorStoryWithAnchorResponseValidationError{
					field:  fmt.Sprintf("ListResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListSameAuthorStoryWithAnchorResponseMultiError(errors)
	}

	return nil
}

// ListSameAuthorStoryWithAnchorResponseMultiError is an error wrapping
// multiple validation errors returned by
// ListSameAuthorStoryWithAnchorResponse.ValidateAll() if the designated
// constraints aren't met.
type ListSameAuthorStoryWithAnchorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSameAuthorStoryWithAnchorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSameAuthorStoryWithAnchorResponseMultiError) AllErrors() []error { return m }

// ListSameAuthorStoryWithAnchorResponseValidationError is the validation error
// returned by ListSameAuthorStoryWithAnchorResponse.Validate if the
// designated constraints aren't met.
type ListSameAuthorStoryWithAnchorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSameAuthorStoryWithAnchorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSameAuthorStoryWithAnchorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSameAuthorStoryWithAnchorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSameAuthorStoryWithAnchorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSameAuthorStoryWithAnchorResponseValidationError) ErrorName() string {
	return "ListSameAuthorStoryWithAnchorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSameAuthorStoryWithAnchorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSameAuthorStoryWithAnchorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSameAuthorStoryWithAnchorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSameAuthorStoryWithAnchorResponseValidationError{}

// Validate checks the field values on ConsumeUnmuteStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeUnmuteStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeUnmuteStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeUnmuteStoryRequestMultiError, or nil if none found.
func (m *ConsumeUnmuteStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeUnmuteStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for UserAudioKey

	if len(errors) > 0 {
		return ConsumeUnmuteStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeUnmuteStoryRequestMultiError is an error wrapping multiple validation
// errors returned by ConsumeUnmuteStoryRequest.ValidateAll() if the
// designated constraints aren't met.
type ConsumeUnmuteStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeUnmuteStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeUnmuteStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeUnmuteStoryRequestValidationError is the validation error returned by
// ConsumeUnmuteStoryRequest.Validate if the designated constraints aren't met.
type ConsumeUnmuteStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeUnmuteStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeUnmuteStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeUnmuteStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeUnmuteStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeUnmuteStoryRequestValidationError) ErrorName() string {
	return "ConsumeUnmuteStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeUnmuteStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeUnmuteStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeUnmuteStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeUnmuteStoryRequestValidationError{}

// Validate checks the field values on ConsumeUnmuteStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeUnmuteStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeUnmuteStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeUnmuteStoryResponseMultiError, or nil if none found.
func (m *ConsumeUnmuteStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeUnmuteStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MatchStatus

	// no validation rules for AiResponse

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeUnmuteStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeUnmuteStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeUnmuteStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeUnmuteStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeUnmuteStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeUnmuteStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type ConsumeUnmuteStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeUnmuteStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeUnmuteStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeUnmuteStoryResponseValidationError is the validation error returned
// by ConsumeUnmuteStoryResponse.Validate if the designated constraints aren't met.
type ConsumeUnmuteStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeUnmuteStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeUnmuteStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeUnmuteStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeUnmuteStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeUnmuteStoryResponseValidationError) ErrorName() string {
	return "ConsumeUnmuteStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeUnmuteStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeUnmuteStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeUnmuteStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeUnmuteStoryResponseValidationError{}

// Validate checks the field values on ConsumeNowShotStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeNowShotStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeNowShotStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeNowShotStoryRequestMultiError, or nil if none found.
func (m *ConsumeNowShotStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeNowShotStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for UserResourceKey

	// no validation rules for StartTime

	// no validation rules for ResourceType

	if m.ThumbnailUrl != nil {
		// no validation rules for ThumbnailUrl
	}

	if len(errors) > 0 {
		return ConsumeNowShotStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeNowShotStoryRequestMultiError is an error wrapping multiple
// validation errors returned by ConsumeNowShotStoryRequest.ValidateAll() if
// the designated constraints aren't met.
type ConsumeNowShotStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeNowShotStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeNowShotStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeNowShotStoryRequestValidationError is the validation error returned
// by ConsumeNowShotStoryRequest.Validate if the designated constraints aren't met.
type ConsumeNowShotStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeNowShotStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeNowShotStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeNowShotStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeNowShotStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeNowShotStoryRequestValidationError) ErrorName() string {
	return "ConsumeNowShotStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeNowShotStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeNowShotStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeNowShotStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeNowShotStoryRequestValidationError{}

// Validate checks the field values on ConsumeNowShotStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeNowShotStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeNowShotStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeNowShotStoryResponseMultiError, or nil if none found.
func (m *ConsumeNowShotStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeNowShotStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeNowShotStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeNowShotStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeNowShotStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeNowShotStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeNowShotStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeNowShotStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type ConsumeNowShotStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeNowShotStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeNowShotStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeNowShotStoryResponseValidationError is the validation error returned
// by ConsumeNowShotStoryResponse.Validate if the designated constraints
// aren't met.
type ConsumeNowShotStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeNowShotStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeNowShotStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeNowShotStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeNowShotStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeNowShotStoryResponseValidationError) ErrorName() string {
	return "ConsumeNowShotStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeNowShotStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeNowShotStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeNowShotStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeNowShotStoryResponseValidationError{}

// Validate checks the field values on ListCreatorStoryResponse_CreatedStory
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCreatorStoryResponse_CreatedStory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCreatorStoryResponse_CreatedStory
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListCreatorStoryResponse_CreatedStoryMultiError, or nil if none found.
func (m *ListCreatorStoryResponse_CreatedStory) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCreatorStoryResponse_CreatedStory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCreatorStoryResponse_CreatedStoryValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCreatorStoryResponse_CreatedStoryValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCreatorStoryResponse_CreatedStoryValidationError{
				field:  "Story",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsTop

	if len(errors) > 0 {
		return ListCreatorStoryResponse_CreatedStoryMultiError(errors)
	}

	return nil
}

// ListCreatorStoryResponse_CreatedStoryMultiError is an error wrapping
// multiple validation errors returned by
// ListCreatorStoryResponse_CreatedStory.ValidateAll() if the designated
// constraints aren't met.
type ListCreatorStoryResponse_CreatedStoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCreatorStoryResponse_CreatedStoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCreatorStoryResponse_CreatedStoryMultiError) AllErrors() []error { return m }

// ListCreatorStoryResponse_CreatedStoryValidationError is the validation error
// returned by ListCreatorStoryResponse_CreatedStory.Validate if the
// designated constraints aren't met.
type ListCreatorStoryResponse_CreatedStoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCreatorStoryResponse_CreatedStoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCreatorStoryResponse_CreatedStoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCreatorStoryResponse_CreatedStoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCreatorStoryResponse_CreatedStoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCreatorStoryResponse_CreatedStoryValidationError) ErrorName() string {
	return "ListCreatorStoryResponse_CreatedStoryValidationError"
}

// Error satisfies the builtin error interface
func (e ListCreatorStoryResponse_CreatedStoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCreatorStoryResponse_CreatedStory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCreatorStoryResponse_CreatedStoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCreatorStoryResponse_CreatedStoryValidationError{}

// Validate checks the field values on
// ListSameAuthorStoryWithAnchorRequest_ListRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListSameAuthorStoryWithAnchorRequest_ListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListSameAuthorStoryWithAnchorRequest_ListRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ListSameAuthorStoryWithAnchorRequest_ListRequestMultiError, or nil if none found.
func (m *ListSameAuthorStoryWithAnchorRequest_ListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSameAuthorStoryWithAnchorRequest_ListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reverse

	if val := m.GetLimit(); val <= 0 || val >= 10 {
		err := ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError{
			field:  "Limit",
			reason: "value must be inside range (0, 10)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListSameAuthorStoryWithAnchorRequest_ListRequestMultiError(errors)
	}

	return nil
}

// ListSameAuthorStoryWithAnchorRequest_ListRequestMultiError is an error
// wrapping multiple validation errors returned by
// ListSameAuthorStoryWithAnchorRequest_ListRequest.ValidateAll() if the
// designated constraints aren't met.
type ListSameAuthorStoryWithAnchorRequest_ListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSameAuthorStoryWithAnchorRequest_ListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSameAuthorStoryWithAnchorRequest_ListRequestMultiError) AllErrors() []error { return m }

// ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError is the
// validation error returned by
// ListSameAuthorStoryWithAnchorRequest_ListRequest.Validate if the designated
// constraints aren't met.
type ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError) ErrorName() string {
	return "ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSameAuthorStoryWithAnchorRequest_ListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSameAuthorStoryWithAnchorRequest_ListRequestValidationError{}

// Validate checks the field values on
// ListSameAuthorStoryWithAnchorResponse_ListResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListSameAuthorStoryWithAnchorResponse_ListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListSameAuthorStoryWithAnchorResponse_ListResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ListSameAuthorStoryWithAnchorResponse_ListResponseMultiError, or nil if
// none found.
func (m *ListSameAuthorStoryWithAnchorResponse_ListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSameAuthorStoryWithAnchorResponse_ListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reverse

	for idx, item := range m.GetStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError{
					field:  fmt.Sprintf("Stories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListSameAuthorStoryWithAnchorResponse_ListResponseMultiError(errors)
	}

	return nil
}

// ListSameAuthorStoryWithAnchorResponse_ListResponseMultiError is an error
// wrapping multiple validation errors returned by
// ListSameAuthorStoryWithAnchorResponse_ListResponse.ValidateAll() if the
// designated constraints aren't met.
type ListSameAuthorStoryWithAnchorResponse_ListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSameAuthorStoryWithAnchorResponse_ListResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSameAuthorStoryWithAnchorResponse_ListResponseMultiError) AllErrors() []error { return m }

// ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError is the
// validation error returned by
// ListSameAuthorStoryWithAnchorResponse_ListResponse.Validate if the
// designated constraints aren't met.
type ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError) ErrorName() string {
	return "ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSameAuthorStoryWithAnchorResponse_ListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSameAuthorStoryWithAnchorResponse_ListResponseValidationError{}
