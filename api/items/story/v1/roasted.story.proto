syntax = "proto3";

package api.items.story.v1;

option go_package = "boson/api/items/story/v1;api_items_story_v1";

import "api/items/story/types/v1/types.proto";
import "api/items/story/types/v1/roasted.types.proto";
import "api/items/story/v1/story.privacy.setting.proto";
import "validate/validate.proto";

message ConsumeRoastedTopicRequest {
	api.items.story.types.v1.RoastedQuestionWithUserAnswer question_with_user_answer = 1[(validate.rules).message = {required: true}];
	// 可选型，如果传了，则视为消费了这个 story
	optional string from_story_id = 2[(validate.rules).string = {pattern: "^[0-9]+$"}];
	// 在当前节点中，这是第几个追问，如果不是追问，则传 0
	uint32 question_count = 3;
	// 目前用户总共拍摄了多久的视频，单位秒
	uint32 total_video_duration_seconds = 4;
}

message ConsumeRoastedTopicResponse {
	// 追问，可选性，如果不为空，则客户端需要继续回答追问
	// 如果为空，则表示完成了请求的 Question
	optional api.items.story.types.v1.RoastedQuestion next_question = 1;
}

message ConsumeRoastedStoryRequest {
	string story_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
	// 可选型，如果传了，会把此视频发给原作者
	optional string user_recorded_video_key = 2;
	// v2 中，客户端会将消费的视频保存成一个 story，服务端将story发给作者，并更新 story 的 privacy setting 使作者可以看到
	string user_story_id = 3;
}
message ConsumeRoastedStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}
// 获取一个 roasted 的 topics
message GetRoastedTopicsRequest {
	// 如果传了，目前会直接返回对方的 story 的 topics
	optional string copy_story_id = 1;
}
message GetRoastedTopicsResponse {
	api.items.story.types.v1.StoryPlayRoastedTopic topic = 1;
}
// 发布 roasted 的 story 请求
message CreateRoastedStoryRequest {
	api.items.story.types.v1.StoryPlayRoastedConfig play_config = 1[(validate.rules).message = {required: true}];
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 2;
	// 从哪个 story 过来创建的
	optional string from_story_id = 3[(validate.rules).string = {pattern: "^[0-9]+$"}];
}
message CreateRoastedStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}
