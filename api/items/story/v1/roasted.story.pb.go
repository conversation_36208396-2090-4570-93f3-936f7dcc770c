// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v1/roasted.story.proto

package api_items_story_v1

import (
	v1 "boson/api/items/story/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumeRoastedTopicRequest struct {
	state                  protoimpl.MessageState            `protogen:"open.v1"`
	QuestionWithUserAnswer *v1.RoastedQuestionWithUserAnswer `protobuf:"bytes,1,opt,name=question_with_user_answer,json=questionWithUserAnswer,proto3" json:"question_with_user_answer,omitempty"`
	// 可选型，如果传了，则视为消费了这个 story
	FromStoryId *string `protobuf:"bytes,2,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	// 在当前节点中，这是第几个追问，如果不是追问，则传 0
	QuestionCount uint32 `protobuf:"varint,3,opt,name=question_count,json=questionCount,proto3" json:"question_count,omitempty"`
	// 目前用户总共拍摄了多久的视频，单位秒
	TotalVideoDurationSeconds uint32 `protobuf:"varint,4,opt,name=total_video_duration_seconds,json=totalVideoDurationSeconds,proto3" json:"total_video_duration_seconds,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *ConsumeRoastedTopicRequest) Reset() {
	*x = ConsumeRoastedTopicRequest{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeRoastedTopicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRoastedTopicRequest) ProtoMessage() {}

func (x *ConsumeRoastedTopicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRoastedTopicRequest.ProtoReflect.Descriptor instead.
func (*ConsumeRoastedTopicRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{0}
}

func (x *ConsumeRoastedTopicRequest) GetQuestionWithUserAnswer() *v1.RoastedQuestionWithUserAnswer {
	if x != nil {
		return x.QuestionWithUserAnswer
	}
	return nil
}

func (x *ConsumeRoastedTopicRequest) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

func (x *ConsumeRoastedTopicRequest) GetQuestionCount() uint32 {
	if x != nil {
		return x.QuestionCount
	}
	return 0
}

func (x *ConsumeRoastedTopicRequest) GetTotalVideoDurationSeconds() uint32 {
	if x != nil {
		return x.TotalVideoDurationSeconds
	}
	return 0
}

type ConsumeRoastedTopicResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 追问，可选性，如果不为空，则客户端需要继续回答追问
	// 如果为空，则表示完成了请求的 Question
	NextQuestion  *v1.RoastedQuestion `protobuf:"bytes,1,opt,name=next_question,json=nextQuestion,proto3,oneof" json:"next_question,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeRoastedTopicResponse) Reset() {
	*x = ConsumeRoastedTopicResponse{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeRoastedTopicResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRoastedTopicResponse) ProtoMessage() {}

func (x *ConsumeRoastedTopicResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRoastedTopicResponse.ProtoReflect.Descriptor instead.
func (*ConsumeRoastedTopicResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{1}
}

func (x *ConsumeRoastedTopicResponse) GetNextQuestion() *v1.RoastedQuestion {
	if x != nil {
		return x.NextQuestion
	}
	return nil
}

type ConsumeRoastedStoryRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	StoryId string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 可选型，如果传了，会把此视频发给原作者
	UserRecordedVideoKey *string `protobuf:"bytes,2,opt,name=user_recorded_video_key,json=userRecordedVideoKey,proto3,oneof" json:"user_recorded_video_key,omitempty"`
	// v2 中，客户端会将消费的视频保存成一个 story，服务端将story发给作者，并更新 story 的 privacy setting 使作者可以看到
	UserStoryId   string `protobuf:"bytes,3,opt,name=user_story_id,json=userStoryId,proto3" json:"user_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeRoastedStoryRequest) Reset() {
	*x = ConsumeRoastedStoryRequest{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeRoastedStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRoastedStoryRequest) ProtoMessage() {}

func (x *ConsumeRoastedStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRoastedStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeRoastedStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{2}
}

func (x *ConsumeRoastedStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeRoastedStoryRequest) GetUserRecordedVideoKey() string {
	if x != nil && x.UserRecordedVideoKey != nil {
		return *x.UserRecordedVideoKey
	}
	return ""
}

func (x *ConsumeRoastedStoryRequest) GetUserStoryId() string {
	if x != nil {
		return x.UserStoryId
	}
	return ""
}

type ConsumeRoastedStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeRoastedStoryResponse) Reset() {
	*x = ConsumeRoastedStoryResponse{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeRoastedStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRoastedStoryResponse) ProtoMessage() {}

func (x *ConsumeRoastedStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRoastedStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeRoastedStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{3}
}

func (x *ConsumeRoastedStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 获取一个 roasted 的 topics
type GetRoastedTopicsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 如果传了，目前会直接返回对方的 story 的 topics
	CopyStoryId   *string `protobuf:"bytes,1,opt,name=copy_story_id,json=copyStoryId,proto3,oneof" json:"copy_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoastedTopicsRequest) Reset() {
	*x = GetRoastedTopicsRequest{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoastedTopicsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoastedTopicsRequest) ProtoMessage() {}

func (x *GetRoastedTopicsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoastedTopicsRequest.ProtoReflect.Descriptor instead.
func (*GetRoastedTopicsRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{4}
}

func (x *GetRoastedTopicsRequest) GetCopyStoryId() string {
	if x != nil && x.CopyStoryId != nil {
		return *x.CopyStoryId
	}
	return ""
}

type GetRoastedTopicsResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Topic         *v1.StoryPlayRoastedTopic `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoastedTopicsResponse) Reset() {
	*x = GetRoastedTopicsResponse{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoastedTopicsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoastedTopicsResponse) ProtoMessage() {}

func (x *GetRoastedTopicsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoastedTopicsResponse.ProtoReflect.Descriptor instead.
func (*GetRoastedTopicsResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{5}
}

func (x *GetRoastedTopicsResponse) GetTopic() *v1.StoryPlayRoastedTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

// 发布 roasted 的 story 请求
type CreateRoastedStoryRequest struct {
	state      protoimpl.MessageState     `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayRoastedConfig `protobuf:"bytes,1,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	// 从哪个 story 过来创建的
	FromStoryId   *string `protobuf:"bytes,3,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoastedStoryRequest) Reset() {
	*x = CreateRoastedStoryRequest{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoastedStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoastedStoryRequest) ProtoMessage() {}

func (x *CreateRoastedStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoastedStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateRoastedStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{6}
}

func (x *CreateRoastedStoryRequest) GetPlayConfig() *v1.StoryPlayRoastedConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateRoastedStoryRequest) GetPrivacySetting() *PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *CreateRoastedStoryRequest) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

type CreateRoastedStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoastedStoryResponse) Reset() {
	*x = CreateRoastedStoryResponse{}
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoastedStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoastedStoryResponse) ProtoMessage() {}

func (x *CreateRoastedStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v1_roasted_story_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoastedStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateRoastedStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v1_roasted_story_proto_rawDescGZIP(), []int{7}
}

func (x *CreateRoastedStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

var File_api_items_story_v1_roasted_story_proto protoreflect.FileDescriptor

const file_api_items_story_v1_roasted_story_proto_rawDesc = "" +
	"\n" +
	"&api/items/story/v1/roasted.story.proto\x12\x12api.items.story.v1\x1a$api/items/story/types/v1/types.proto\x1a,api/items/story/types/v1/roasted.types.proto\x1a.api/items/story/v1/story.privacy.setting.proto\x1a\x17validate/validate.proto\"\xce\x02\n" +
	"\x1aConsumeRoastedTopicRequest\x12|\n" +
	"\x19question_with_user_answer\x18\x01 \x01(\v27.api.items.story.types.v1.RoastedQuestionWithUserAnswerB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x16questionWithUserAnswer\x128\n" +
	"\rfrom_story_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x00R\vfromStoryId\x88\x01\x01\x12%\n" +
	"\x0equestion_count\x18\x03 \x01(\rR\rquestionCount\x12?\n" +
	"\x1ctotal_video_duration_seconds\x18\x04 \x01(\rR\x19totalVideoDurationSecondsB\x10\n" +
	"\x0e_from_story_id\"\x84\x01\n" +
	"\x1bConsumeRoastedTopicResponse\x12S\n" +
	"\rnext_question\x18\x01 \x01(\v2).api.items.story.types.v1.RoastedQuestionH\x00R\fnextQuestion\x88\x01\x01B\x10\n" +
	"\x0e_next_question\"\xc4\x01\n" +
	"\x1aConsumeRoastedStoryRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12:\n" +
	"\x17user_recorded_video_key\x18\x02 \x01(\tH\x00R\x14userRecordedVideoKey\x88\x01\x01\x12\"\n" +
	"\ruser_story_id\x18\x03 \x01(\tR\vuserStoryIdB\x1a\n" +
	"\x18_user_recorded_video_key\"g\n" +
	"\x1bConsumeRoastedStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"T\n" +
	"\x17GetRoastedTopicsRequest\x12'\n" +
	"\rcopy_story_id\x18\x01 \x01(\tH\x00R\vcopyStoryId\x88\x01\x01B\x10\n" +
	"\x0e_copy_story_id\"a\n" +
	"\x18GetRoastedTopicsResponse\x12E\n" +
	"\x05topic\x18\x01 \x01(\v2/.api.items.story.types.v1.StoryPlayRoastedTopicR\x05topic\"\xb4\x02\n" +
	"\x19CreateRoastedStoryRequest\x12[\n" +
	"\vplay_config\x18\x01 \x01(\v20.api.items.story.types.v1.StoryPlayRoastedConfigB\b\xfaB\x05\x8a\x01\x02\x10\x01R\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01\x128\n" +
	"\rfrom_story_id\x18\x03 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x01R\vfromStoryId\x88\x01\x01B\x12\n" +
	"\x10_privacy_settingB\x10\n" +
	"\x0e_from_story_id\"f\n" +
	"\x1aCreateRoastedStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetailB-Z+boson/api/items/story/v1;api_items_story_v1b\x06proto3"

var (
	file_api_items_story_v1_roasted_story_proto_rawDescOnce sync.Once
	file_api_items_story_v1_roasted_story_proto_rawDescData []byte
)

func file_api_items_story_v1_roasted_story_proto_rawDescGZIP() []byte {
	file_api_items_story_v1_roasted_story_proto_rawDescOnce.Do(func() {
		file_api_items_story_v1_roasted_story_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v1_roasted_story_proto_rawDesc), len(file_api_items_story_v1_roasted_story_proto_rawDesc)))
	})
	return file_api_items_story_v1_roasted_story_proto_rawDescData
}

var file_api_items_story_v1_roasted_story_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_items_story_v1_roasted_story_proto_goTypes = []any{
	(*ConsumeRoastedTopicRequest)(nil),       // 0: api.items.story.v1.ConsumeRoastedTopicRequest
	(*ConsumeRoastedTopicResponse)(nil),      // 1: api.items.story.v1.ConsumeRoastedTopicResponse
	(*ConsumeRoastedStoryRequest)(nil),       // 2: api.items.story.v1.ConsumeRoastedStoryRequest
	(*ConsumeRoastedStoryResponse)(nil),      // 3: api.items.story.v1.ConsumeRoastedStoryResponse
	(*GetRoastedTopicsRequest)(nil),          // 4: api.items.story.v1.GetRoastedTopicsRequest
	(*GetRoastedTopicsResponse)(nil),         // 5: api.items.story.v1.GetRoastedTopicsResponse
	(*CreateRoastedStoryRequest)(nil),        // 6: api.items.story.v1.CreateRoastedStoryRequest
	(*CreateRoastedStoryResponse)(nil),       // 7: api.items.story.v1.CreateRoastedStoryResponse
	(*v1.RoastedQuestionWithUserAnswer)(nil), // 8: api.items.story.types.v1.RoastedQuestionWithUserAnswer
	(*v1.RoastedQuestion)(nil),               // 9: api.items.story.types.v1.RoastedQuestion
	(*v1.StoryDetail)(nil),                   // 10: api.items.story.types.v1.StoryDetail
	(*v1.StoryPlayRoastedTopic)(nil),         // 11: api.items.story.types.v1.StoryPlayRoastedTopic
	(*v1.StoryPlayRoastedConfig)(nil),        // 12: api.items.story.types.v1.StoryPlayRoastedConfig
	(*PrivacySettingUpdateAttr)(nil),         // 13: api.items.story.v1.PrivacySettingUpdateAttr
}
var file_api_items_story_v1_roasted_story_proto_depIdxs = []int32{
	8,  // 0: api.items.story.v1.ConsumeRoastedTopicRequest.question_with_user_answer:type_name -> api.items.story.types.v1.RoastedQuestionWithUserAnswer
	9,  // 1: api.items.story.v1.ConsumeRoastedTopicResponse.next_question:type_name -> api.items.story.types.v1.RoastedQuestion
	10, // 2: api.items.story.v1.ConsumeRoastedStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	11, // 3: api.items.story.v1.GetRoastedTopicsResponse.topic:type_name -> api.items.story.types.v1.StoryPlayRoastedTopic
	12, // 4: api.items.story.v1.CreateRoastedStoryRequest.play_config:type_name -> api.items.story.types.v1.StoryPlayRoastedConfig
	13, // 5: api.items.story.v1.CreateRoastedStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	10, // 6: api.items.story.v1.CreateRoastedStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	7,  // [7:7] is the sub-list for method output_type
	7,  // [7:7] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_items_story_v1_roasted_story_proto_init() }
func file_api_items_story_v1_roasted_story_proto_init() {
	if File_api_items_story_v1_roasted_story_proto != nil {
		return
	}
	file_api_items_story_v1_story_privacy_setting_proto_init()
	file_api_items_story_v1_roasted_story_proto_msgTypes[0].OneofWrappers = []any{}
	file_api_items_story_v1_roasted_story_proto_msgTypes[1].OneofWrappers = []any{}
	file_api_items_story_v1_roasted_story_proto_msgTypes[2].OneofWrappers = []any{}
	file_api_items_story_v1_roasted_story_proto_msgTypes[4].OneofWrappers = []any{}
	file_api_items_story_v1_roasted_story_proto_msgTypes[6].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v1_roasted_story_proto_rawDesc), len(file_api_items_story_v1_roasted_story_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v1_roasted_story_proto_goTypes,
		DependencyIndexes: file_api_items_story_v1_roasted_story_proto_depIdxs,
		MessageInfos:      file_api_items_story_v1_roasted_story_proto_msgTypes,
	}.Build()
	File_api_items_story_v1_roasted_story_proto = out.File
	file_api_items_story_v1_roasted_story_proto_goTypes = nil
	file_api_items_story_v1_roasted_story_proto_depIdxs = nil
}
