syntax = "proto3";

package api.items.story.v1;
import "api/items/story/types/v1/types.proto";
import "api/items/story/types/v1/turtle_soup_types.proto";
import "api/items/story/types/v1/now_shot_types.proto";
import "api/common/v1/common.proto";
import "validate/validate.proto";
import "api/items/story/v1/story.privacy.setting.proto";
import "api/items/story/types/v1/base_play_story_types.proto";
option go_package = "boson/api/items/story/v1;api_items_story_v1";


message UpdateStoryRequest {
	// only number
	string story_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
	// 隐私设置，可选型，如果不传，则表示不修改
	optional PrivacySettingUpdateAttr privacy_setting = 2;
}

message UpdateStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

message TopStoryRequest {
	// only number
	string story_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
	// 是否置顶
	bool is_top = 2;
}

// 删除 story 的请求
message DeleteStoryRequest {
	// only number
	string story_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message ListCommonStoryConditionTemplatesRequest {
	api.items.story.types.v1.StoryPlayType story_play_type = 1;
}

message ListCommonStoryConditionTemplatesResponse {
	repeated api.items.story.types.v1.CommonStoryPlayConditionTemplate condition_templates = 1;
}

message ListTurtleSoupStoryConditionTemplatesRequest {
}

message ListTurtleSoupStoryConditionTemplatesResponse {
	repeated api.items.story.types.v1.StoryPlayTurtleConditionTemplate condition_templates = 1;
}

message ListExchangeImageStoryConditionTemplatesRequest {
}
message ListExchangeImageStoryConditionTemplatesResponse {
	repeated api.items.story.types.v1.StoryExchangeImageConditionTemplate condition_templates = 1;
}

message ListUnmuteStoryConditionTemplatesRequest {
}

message ListUnmuteStoryConditionTemplatesResponse {
	repeated api.items.story.types.v1.StoryPlayUnmuteConditionTemplate condition_templates = 1;
}

message ListTurtleSoupConditionTemplatesRequest {
}

message ListTurtleSoupConditionTemplatesResponse {
	repeated api.items.story.types.v1.StoryPlayTurtleConditionTemplate condition_templates = 1;
}

message ListExchangeImageStoryResponse {
	repeated api.items.story.types.v1.StoryDetail stories = 1;
}

// 发布 story 的请求
message CreateExchangeImageStoryRequest {
	api.items.story.types.v1.StoryPlayExchangeImageConfig play_config = 2;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional PrivacySettingUpdateAttr privacy_setting = 3;
}

message CreateExchangeImageStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


// 发布海龟汤的 story 请求
message CreateTurtleSoupStoryRequest {
	api.items.story.types.v1.StoryPlayTurtleSoupConfig play_config = 1;
	// 是否是马赛克玩法
	bool is_mass = 2;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional PrivacySettingUpdateAttr privacy_setting = 3;
}

// 发布海龟汤的 story 响应
message CreateTurtleSoupStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 发布unmute的 story 请求
message CreateUnmuteStoryRequest {
	api.items.story.types.v1.StoryPlayUnmuteConfig play_config = 1;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional PrivacySettingUpdateAttr privacy_setting = 2;
}

// 发布unmute的 story 响应
message CreateUnmuteStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 发布baseplay的 story 请求	
message CreateBasePlayStoryRequest {
	api.items.story.types.v1.StoryPlayBasePlayConfig play_config = 1;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional PrivacySettingUpdateAttr privacy_setting = 2;
}

// 发布baseplay的 story 响应
message CreateBasePlayStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 发布 nowshot 的 story 请求
message CreateNowShotStoryRequest {
	api.items.story.types.v1.StoryPlayNowShotConfig play_config = 1;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional PrivacySettingUpdateAttr privacy_setting = 2;
}

// 发布 nowshot 的 story 响应
message CreateNowShotStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


message GetStoryDetailRequest {
	string id = 1;
}

message GetStoryDetailResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


message ListCreatorStoryRequest {
	string creator_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
	api.common.v1.ListRequest list_request = 2[(validate.rules).message.required = true];
}

message ListCreatorStoryResponse {
	message CreatedStory {
		api.items.story.types.v1.StorySummary story = 1;
		// 是否置顶
		bool is_top = 2;
	}
	repeated CreatedStory created_stories = 1;
	api.common.v1.ListResponse list_response = 2;
}

// 获取解锁过的 story 列表
message ListUnlockedStoryRequest {
	api.common.v1.ListRequest list_request = 1[(validate.rules).message.required = true];
	// 过滤类型，如果传空，则不过滤
	repeated api.items.story.types.v1.StoryPlayType filter_play_types = 2;
}

message ListUnlockedStoryResponse {
	repeated api.items.story.types.v1.StoryDetail unlocked_stories = 1;
	api.common.v1.ListResponse list_response = 2;
}

message ConsumeBasePlayStoryRequest {
	string story_id = 1;
	// 消费到第几个 node 了，从 0 开始
	uint32 current_node_idx = 2;
}

message ConsumeBasePlayStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}
	

message ConsumeExchangeImageStoryRequest {
	string story_id = 1;
	string image_key = 2;
}

message ConsumeExchangeImageStoryResponse {
	// 匹配状态，参考 api.items.story.types.v1.ExchangeImageMatchStatus
	string match_status = 1;
	// ai 回复
	string ai_response = 2;
	// 更新后的 story 详情，可以在这里获取下一个节点的信息
	api.items.story.types.v1.StoryDetail story_detail = 3;
}

message ConsumeTurtleSoupStoryRequest {
	string story_id = 1;
	string user_message = 2;
}

message ConsumeTurtleSoupStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


// 获取某个 Story 同作者一定时间范围内对的其他 story
// 通过多个 ListRequest + reverse 来实现同时正反向取多个 story
message ListSameAuthorStoryWithAnchorRequest {
	// 锚点 story id
	string anchor_story_id = 1;
	message ListRequest {
		// 过滤游玩类型，如果传空，则不过滤
		repeated api.items.story.types.v1.StoryPlayType filter_play_types = 1;
		// 是否时间倒叙
		bool reverse = 2;
		// 取多少个
		uint32 limit = 3[(validate.rules).uint32 = {gt: 0, lt: 10}];
	}
	repeated ListRequest list_requests = 2[(validate.rules).repeated = {min_items: 1, max_items: 2}];
}

message ListSameAuthorStoryWithAnchorResponse {
	message ListResponse {
		// 是否倒叙
		bool reverse = 1;
		repeated api.items.story.types.v1.StoryDetail stories = 2;
	}
	// 有多少个 ListRequests 就有多少个 ListResponse
	repeated ListResponse list_responses = 1;
}

message ConsumeUnmuteStoryRequest {
	string story_id = 1;
	string user_audio_key = 2;
}

message ConsumeUnmuteStoryResponse {
	// 匹配状态，参考 api.items.story.types.v1.UnmuteMatchStatus
	string match_status = 1;
	// ai 回复
	string ai_response = 2;
	api.items.story.types.v1.StoryDetail story_detail = 3;
}

message ConsumeNowShotStoryRequest {
	string story_id = 1;
	// 资源 url，创建时，客户端传 key
	string user_resource_key = 2;
	uint32 start_time = 3;
	enum ResourceType {
		RESOURCE_TYPE_UNSPECIFIED = 0;
		RESOURCE_TYPE_IMAGE = 1;
		RESOURCE_TYPE_VIDEO = 2;
	}
	// 封面资源类型，参考 ResourceType
	string resource_type = 4;
	// 如果资源是视频时，需要传一个视频首帧，创作时给 key
	optional string thumbnail_url = 5;

}

message ConsumeNowShotStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}