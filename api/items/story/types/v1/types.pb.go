// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/items/story/reaction/types/v1"
	v11 "boson/api/users/info/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StoryStatus int32

const (
	StoryStatus_STORY_STATUS_UNSPECIFIED StoryStatus = 0
	StoryStatus_STORY_STATUS_PUBLISHED   StoryStatus = 1
	// 删除
	StoryStatus_STORY_STATUS_DELETED StoryStatus = 2
	// 创建中
	StoryStatus_STORY_STATUS_GENERATING StoryStatus = 3
	// 生成失败
	StoryStatus_STORY_STATUS_GENERATION_FAILED StoryStatus = 4
)

// Enum value maps for StoryStatus.
var (
	StoryStatus_name = map[int32]string{
		0: "STORY_STATUS_UNSPECIFIED",
		1: "STORY_STATUS_PUBLISHED",
		2: "STORY_STATUS_DELETED",
		3: "STORY_STATUS_GENERATING",
		4: "STORY_STATUS_GENERATION_FAILED",
	}
	StoryStatus_value = map[string]int32{
		"STORY_STATUS_UNSPECIFIED":       0,
		"STORY_STATUS_PUBLISHED":         1,
		"STORY_STATUS_DELETED":           2,
		"STORY_STATUS_GENERATING":        3,
		"STORY_STATUS_GENERATION_FAILED": 4,
	}
)

func (x StoryStatus) Enum() *StoryStatus {
	p := new(StoryStatus)
	*p = x
	return p
}

func (x StoryStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[0].Descriptor()
}

func (StoryStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[0]
}

func (x StoryStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryStatus.Descriptor instead.
func (StoryStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{0}
}

// story 游玩类型
type StoryPlayType int32

const (
	StoryPlayType_STORY_PLAY_TYPE_UNSPECIFIED StoryPlayType = 0
	// 换图玩法
	StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE StoryPlayType = 1
	// 海龟汤玩法
	StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP StoryPlayType = 2
	// Unmute玩法
	StoryPlayType_STORY_PLAY_TYPE_UNMUTE StoryPlayType = 3
	// 海龟汤 mass 玩法
	StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS StoryPlayType = 4
	// BasePlay 玩法
	StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY StoryPlayType = 5
	// NowShot 玩法
	StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT StoryPlayType = 6
	// roasted
	StoryPlayType_STORY_PLAY_TYPE_ROASTED StoryPlayType = 7
	// // wassup 玩法
	// STORY_PLAY_TYPE_WASSUP = 8;
	// capsule 玩法
	StoryPlayType_STORY_PLAY_TYPE_CAPSULE StoryPlayType = 9
	// hide 玩法
	StoryPlayType_STORY_PLAY_TYPE_HIDE StoryPlayType = 10
	// chatproxy 玩法
	StoryPlayType_STORY_PLAY_TYPE_CHATPROXY StoryPlayType = 11
	// Who 玩法
	StoryPlayType_STORY_PLAY_TYPE_WHO StoryPlayType = 12
	// wassup v2 玩法
	StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2 StoryPlayType = 13
	// Haunt 玩法
	StoryPlayType_STORY_PLAY_TYPE_HAUNT StoryPlayType = 14
	// Story Pin 玩法
	StoryPlayType_STORY_PLAY_TYPE_PIN StoryPlayType = 15
)

// Enum value maps for StoryPlayType.
var (
	StoryPlayType_name = map[int32]string{
		0:  "STORY_PLAY_TYPE_UNSPECIFIED",
		1:  "STORY_PLAY_TYPE_EXCHANGE_IMAGE",
		2:  "STORY_PLAY_TYPE_TURTLE_SOUP",
		3:  "STORY_PLAY_TYPE_UNMUTE",
		4:  "STORY_PLAY_TYPE_TURTLE_SOUP_MASS",
		5:  "STORY_PLAY_TYPE_BASE_PLAY",
		6:  "STORY_PLAY_TYPE_NOW_SHOT",
		7:  "STORY_PLAY_TYPE_ROASTED",
		9:  "STORY_PLAY_TYPE_CAPSULE",
		10: "STORY_PLAY_TYPE_HIDE",
		11: "STORY_PLAY_TYPE_CHATPROXY",
		12: "STORY_PLAY_TYPE_WHO",
		13: "STORY_PLAY_TYPE_WASSUP_V2",
		14: "STORY_PLAY_TYPE_HAUNT",
		15: "STORY_PLAY_TYPE_PIN",
	}
	StoryPlayType_value = map[string]int32{
		"STORY_PLAY_TYPE_UNSPECIFIED":      0,
		"STORY_PLAY_TYPE_EXCHANGE_IMAGE":   1,
		"STORY_PLAY_TYPE_TURTLE_SOUP":      2,
		"STORY_PLAY_TYPE_UNMUTE":           3,
		"STORY_PLAY_TYPE_TURTLE_SOUP_MASS": 4,
		"STORY_PLAY_TYPE_BASE_PLAY":        5,
		"STORY_PLAY_TYPE_NOW_SHOT":         6,
		"STORY_PLAY_TYPE_ROASTED":          7,
		"STORY_PLAY_TYPE_CAPSULE":          9,
		"STORY_PLAY_TYPE_HIDE":             10,
		"STORY_PLAY_TYPE_CHATPROXY":        11,
		"STORY_PLAY_TYPE_WHO":              12,
		"STORY_PLAY_TYPE_WASSUP_V2":        13,
		"STORY_PLAY_TYPE_HAUNT":            14,
		"STORY_PLAY_TYPE_PIN":              15,
	}
)

func (x StoryPlayType) Enum() *StoryPlayType {
	p := new(StoryPlayType)
	*p = x
	return p
}

func (x StoryPlayType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[1].Descriptor()
}

func (StoryPlayType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[1]
}

func (x StoryPlayType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayType.Descriptor instead.
func (StoryPlayType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{1}
}

type ExchangeImageMatchStatus int32

const (
	ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED ExchangeImageMatchStatus = 0
	// 未 match
	ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_NOT_MATCHED ExchangeImageMatchStatus = 1
	// 部分 match
	ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_PARTIALLY_MATCHED ExchangeImageMatchStatus = 2
	// 完全 match
	ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_MATCHED ExchangeImageMatchStatus = 3
)

// Enum value maps for ExchangeImageMatchStatus.
var (
	ExchangeImageMatchStatus_name = map[int32]string{
		0: "EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED",
		1: "EXCHANGE_IMAGE_MATCH_STATUS_NOT_MATCHED",
		2: "EXCHANGE_IMAGE_MATCH_STATUS_PARTIALLY_MATCHED",
		3: "EXCHANGE_IMAGE_MATCH_STATUS_MATCHED",
	}
	ExchangeImageMatchStatus_value = map[string]int32{
		"EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED":       0,
		"EXCHANGE_IMAGE_MATCH_STATUS_NOT_MATCHED":       1,
		"EXCHANGE_IMAGE_MATCH_STATUS_PARTIALLY_MATCHED": 2,
		"EXCHANGE_IMAGE_MATCH_STATUS_MATCHED":           3,
	}
)

func (x ExchangeImageMatchStatus) Enum() *ExchangeImageMatchStatus {
	p := new(ExchangeImageMatchStatus)
	*p = x
	return p
}

func (x ExchangeImageMatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangeImageMatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[2].Descriptor()
}

func (ExchangeImageMatchStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[2]
}

func (x ExchangeImageMatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangeImageMatchStatus.Descriptor instead.
func (ExchangeImageMatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{2}
}

type UnmuteMatchStatus int32

const (
	UnmuteMatchStatus_UNMUTE_MATCH_STATUS_UNSPECIFIED UnmuteMatchStatus = 0
	// 未 match
	UnmuteMatchStatus_UNMUTE_MATCH_STATUS_NOT_MATCHED UnmuteMatchStatus = 1
	// 部分 match
	UnmuteMatchStatus_UNMUTE_MATCH_STATUS_PARTIALLY_MATCHED UnmuteMatchStatus = 2
	// 完全 match
	UnmuteMatchStatus_UNMUTE_MATCH_STATUS_MATCHED UnmuteMatchStatus = 3
)

// Enum value maps for UnmuteMatchStatus.
var (
	UnmuteMatchStatus_name = map[int32]string{
		0: "UNMUTE_MATCH_STATUS_UNSPECIFIED",
		1: "UNMUTE_MATCH_STATUS_NOT_MATCHED",
		2: "UNMUTE_MATCH_STATUS_PARTIALLY_MATCHED",
		3: "UNMUTE_MATCH_STATUS_MATCHED",
	}
	UnmuteMatchStatus_value = map[string]int32{
		"UNMUTE_MATCH_STATUS_UNSPECIFIED":       0,
		"UNMUTE_MATCH_STATUS_NOT_MATCHED":       1,
		"UNMUTE_MATCH_STATUS_PARTIALLY_MATCHED": 2,
		"UNMUTE_MATCH_STATUS_MATCHED":           3,
	}
)

func (x UnmuteMatchStatus) Enum() *UnmuteMatchStatus {
	p := new(UnmuteMatchStatus)
	*p = x
	return p
}

func (x UnmuteMatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UnmuteMatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[3].Descriptor()
}

func (UnmuteMatchStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[3]
}

func (x UnmuteMatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UnmuteMatchStatus.Descriptor instead.
func (UnmuteMatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{3}
}

type TemplateType int32

const (
	TemplateType_TEMPLATE_TYPE_UNSPECIFIED TemplateType = 0
	// 系统自带
	TemplateType_TEMPLATE_TYPE_SYSTEM TemplateType = 1
	// 用户自定义
	TemplateType_TEMPLATE_TYPE_USER_DIY TemplateType = 2
)

// Enum value maps for TemplateType.
var (
	TemplateType_name = map[int32]string{
		0: "TEMPLATE_TYPE_UNSPECIFIED",
		1: "TEMPLATE_TYPE_SYSTEM",
		2: "TEMPLATE_TYPE_USER_DIY",
	}
	TemplateType_value = map[string]int32{
		"TEMPLATE_TYPE_UNSPECIFIED": 0,
		"TEMPLATE_TYPE_SYSTEM":      1,
		"TEMPLATE_TYPE_USER_DIY":    2,
	}
)

func (x TemplateType) Enum() *TemplateType {
	p := new(TemplateType)
	*p = x
	return p
}

func (x TemplateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TemplateType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[4].Descriptor()
}

func (TemplateType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[4]
}

func (x TemplateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TemplateType.Descriptor instead.
func (TemplateType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{4}
}

type ContextStatus int32

const (
	ContextStatus_CONTEXT_STATUS_UNSPECIFIED ContextStatus = 0
	// Played
	ContextStatus_CONTEXT_STATUS_NORMAL   ContextStatus = 1
	ContextStatus_CONTEXT_STATUS_DELETED  ContextStatus = 2
	ContextStatus_CONTEXT_STATUS_UNLOCKED ContextStatus = 3
)

// Enum value maps for ContextStatus.
var (
	ContextStatus_name = map[int32]string{
		0: "CONTEXT_STATUS_UNSPECIFIED",
		1: "CONTEXT_STATUS_NORMAL",
		2: "CONTEXT_STATUS_DELETED",
		3: "CONTEXT_STATUS_UNLOCKED",
	}
	ContextStatus_value = map[string]int32{
		"CONTEXT_STATUS_UNSPECIFIED": 0,
		"CONTEXT_STATUS_NORMAL":      1,
		"CONTEXT_STATUS_DELETED":     2,
		"CONTEXT_STATUS_UNLOCKED":    3,
	}
)

func (x ContextStatus) Enum() *ContextStatus {
	p := new(ContextStatus)
	*p = x
	return p
}

func (x ContextStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ContextStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[5].Descriptor()
}

func (ContextStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[5]
}

func (x ContextStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ContextStatus.Descriptor instead.
func (ContextStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{5}
}

type ExchangeImagePlayMode int32

const (
	ExchangeImagePlayMode_PLAY_MODE_UNSPECIFIED ExchangeImagePlayMode = 0
	// 马赛克玩法
	ExchangeImagePlayMode_PLAY_MODE_MASSE ExchangeImagePlayMode = 1
	// 线性解锁
	ExchangeImagePlayMode_PLAY_MODE_LINEAR ExchangeImagePlayMode = 2
)

// Enum value maps for ExchangeImagePlayMode.
var (
	ExchangeImagePlayMode_name = map[int32]string{
		0: "PLAY_MODE_UNSPECIFIED",
		1: "PLAY_MODE_MASSE",
		2: "PLAY_MODE_LINEAR",
	}
	ExchangeImagePlayMode_value = map[string]int32{
		"PLAY_MODE_UNSPECIFIED": 0,
		"PLAY_MODE_MASSE":       1,
		"PLAY_MODE_LINEAR":      2,
	}
)

func (x ExchangeImagePlayMode) Enum() *ExchangeImagePlayMode {
	p := new(ExchangeImagePlayMode)
	*p = x
	return p
}

func (x ExchangeImagePlayMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ExchangeImagePlayMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[6].Descriptor()
}

func (ExchangeImagePlayMode) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[6]
}

func (x ExchangeImagePlayMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ExchangeImagePlayMode.Descriptor instead.
func (ExchangeImagePlayMode) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{6}
}

type StoryPlayUnmuteConfig_ResourceType int32

const (
	StoryPlayUnmuteConfig_RESOURCE_TYPE_UNSPECIFIED StoryPlayUnmuteConfig_ResourceType = 0
	StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE       StoryPlayUnmuteConfig_ResourceType = 1
	StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO       StoryPlayUnmuteConfig_ResourceType = 2
)

// Enum value maps for StoryPlayUnmuteConfig_ResourceType.
var (
	StoryPlayUnmuteConfig_ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
	}
	StoryPlayUnmuteConfig_ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
	}
)

func (x StoryPlayUnmuteConfig_ResourceType) Enum() *StoryPlayUnmuteConfig_ResourceType {
	p := new(StoryPlayUnmuteConfig_ResourceType)
	*p = x
	return p
}

func (x StoryPlayUnmuteConfig_ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayUnmuteConfig_ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[7].Descriptor()
}

func (StoryPlayUnmuteConfig_ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[7]
}

func (x StoryPlayUnmuteConfig_ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayUnmuteConfig_ResourceType.Descriptor instead.
func (StoryPlayUnmuteConfig_ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{8, 0}
}

// V1 资源类型，已废弃，使用resource
type StoryPlayExchangeImageConfig_Node_ResourceType int32

const (
	StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_UNSPECIFIED StoryPlayExchangeImageConfig_Node_ResourceType = 0
	StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE       StoryPlayExchangeImageConfig_Node_ResourceType = 1
	StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO       StoryPlayExchangeImageConfig_Node_ResourceType = 2
)

// Enum value maps for StoryPlayExchangeImageConfig_Node_ResourceType.
var (
	StoryPlayExchangeImageConfig_Node_ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
	}
	StoryPlayExchangeImageConfig_Node_ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
	}
)

func (x StoryPlayExchangeImageConfig_Node_ResourceType) Enum() *StoryPlayExchangeImageConfig_Node_ResourceType {
	p := new(StoryPlayExchangeImageConfig_Node_ResourceType)
	*p = x
	return p
}

func (x StoryPlayExchangeImageConfig_Node_ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayExchangeImageConfig_Node_ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_types_proto_enumTypes[8].Descriptor()
}

func (StoryPlayExchangeImageConfig_Node_ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_types_proto_enumTypes[8]
}

func (x StoryPlayExchangeImageConfig_Node_ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayExchangeImageConfig_Node_ResourceType.Descriptor instead.
func (StoryPlayExchangeImageConfig_Node_ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{16, 0, 0}
}

type StoryStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 该 story 的 reaction 统计
	ReactionMadeStatSummary *v1.ReactionMadeStatSummary `protobuf:"bytes,1,opt,name=reaction_made_stat_summary,json=reactionMadeStatSummary,proto3" json:"reaction_made_stat_summary,omitempty"`
	// Share count
	ShareStat     uint32 `protobuf:"varint,2,opt,name=share_stat,json=shareStat,proto3" json:"share_stat,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryStats) Reset() {
	*x = StoryStats{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryStats) ProtoMessage() {}

func (x *StoryStats) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryStats.ProtoReflect.Descriptor instead.
func (*StoryStats) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryStats) GetReactionMadeStatSummary() *v1.ReactionMadeStatSummary {
	if x != nil {
		return x.ReactionMadeStatSummary
	}
	return nil
}

func (x *StoryStats) GetShareStat() uint32 {
	if x != nil {
		return x.ShareStat
	}
	return 0
}

type StorySummary struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创作时，不要传
	Id     string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Author *v11.UserInfoSummary `protobuf:"bytes,2,opt,name=author,proto3" json:"author,omitempty"`
	// 游玩类型，参考 StoryPlayType
	PlayType string `protobuf:"bytes,3,opt,name=play_type,json=playType,proto3" json:"play_type,omitempty"`
	// 游玩模式，换图玩法时，参考 ExchangeImagePlayMode
	// 当且仅当换图玩法时，此字段有效
	SubPlayType        string      `protobuf:"bytes,4,opt,name=sub_play_type,json=subPlayType,proto3" json:"sub_play_type,omitempty"`
	Status             StoryStatus `protobuf:"varint,24,opt,name=status,proto3,enum=api.items.story.types.v1.StoryStatus" json:"status,omitempty"`
	CreatedAtTimestamp uint32      `protobuf:"varint,5,opt,name=created_at_timestamp,json=createdAtTimestamp,proto3" json:"created_at_timestamp,omitempty"`
	// 换图玩法示例，当且仅当换图玩法时，此字段有效
	ExchangeImageExample *StoryPlayExchangeImageExample `protobuf:"bytes,6,opt,name=exchange_image_example,json=exchangeImageExample,proto3" json:"exchange_image_example,omitempty"`
	// 海龟汤玩法示例，当且仅当海龟汤玩法时，此字段有效
	TurtleSoupExample *StoryPlayTurtleSoupExample `protobuf:"bytes,7,opt,name=turtle_soup_example,json=turtleSoupExample,proto3" json:"turtle_soup_example,omitempty"`
	// Unmute 玩法示例，当且仅当Unmute 玩法时，此字段有效
	UnmuteExample *StoryPlayUnmuteExample `protobuf:"bytes,8,opt,name=unmute_example,json=unmuteExample,proto3" json:"unmute_example,omitempty"`
	// 海龟汤 mass 玩法示例，当且仅当海龟汤 mass 玩法时，此字段有效
	TurtleSoupMassExample *StoryPlayTurtleSoupMassExample `protobuf:"bytes,9,opt,name=turtle_soup_mass_example,json=turtleSoupMassExample,proto3" json:"turtle_soup_mass_example,omitempty"`
	// BasePlay 玩法示例，当且仅当BasePlay 玩法时，此字段有效
	BasePlayExample *StoryPlayBasePlayExample `protobuf:"bytes,10,opt,name=base_play_example,json=basePlayExample,proto3" json:"base_play_example,omitempty"`
	// 给站外分享用的封面图
	CoverImageUrl    string `protobuf:"bytes,11,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	CoverImageWidth  uint32 `protobuf:"varint,12,opt,name=cover_image_width,json=coverImageWidth,proto3" json:"cover_image_width,omitempty"`
	CoverImageHeight uint32 `protobuf:"varint,13,opt,name=cover_image_height,json=coverImageHeight,proto3" json:"cover_image_height,omitempty"`
	CoverType        string `protobuf:"bytes,23,opt,name=cover_type,json=coverType,proto3" json:"cover_type,omitempty"`
	// 隐私设置
	PrivacySetting *PrivacySetting `protobuf:"bytes,14,opt,name=privacy_setting,json=privacySetting,proto3" json:"privacy_setting,omitempty"`
	// NowShot 玩法示例，当且仅当 NowShot 玩法时，此字段有效
	NowShotPlayExample *StoryPlayNowShotExample `protobuf:"bytes,15,opt,name=now_shot_play_example,json=nowShotPlayExample,proto3" json:"now_shot_play_example,omitempty"`
	// roasted 玩法示例，当且仅当 roasted 玩法时，此字段有效
	RoastedExample *StoryPlayRoastedExample `protobuf:"bytes,16,opt,name=roasted_example,json=roastedExample,proto3" json:"roasted_example,omitempty"`
	// capsule 玩法示例，当且仅当 capsule 玩法时，此字段有效
	CapsuleExample *StoryPlayCapsuleExample `protobuf:"bytes,18,opt,name=capsule_example,json=capsuleExample,proto3" json:"capsule_example,omitempty"`
	// 该 story 的 stats
	Stats                  *StoryStats                     `protobuf:"bytes,19,opt,name=stats,proto3" json:"stats,omitempty"`
	LoginUserMadeReactions []*v1.Reaction                  `protobuf:"bytes,20,rep,name=login_user_made_reactions,json=loginUserMadeReactions,proto3" json:"login_user_made_reactions,omitempty"`
	UnlockedUsers          *StorySummary_UnlockedUsersInfo `protobuf:"bytes,21,opt,name=unlocked_users,json=unlockedUsers,proto3" json:"unlocked_users,omitempty"`
	HasUnlocked            bool                            `protobuf:"varint,22,opt,name=has_unlocked,json=hasUnlocked,proto3" json:"has_unlocked,omitempty"`
	// 当且仅当这个对象被推荐引擎露出时，才有此字段
	SortRequestId string `protobuf:"bytes,100,opt,name=sort_request_id,json=sortRequestId,proto3" json:"sort_request_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StorySummary) Reset() {
	*x = StorySummary{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorySummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorySummary) ProtoMessage() {}

func (x *StorySummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorySummary.ProtoReflect.Descriptor instead.
func (*StorySummary) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{1}
}

func (x *StorySummary) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StorySummary) GetAuthor() *v11.UserInfoSummary {
	if x != nil {
		return x.Author
	}
	return nil
}

func (x *StorySummary) GetPlayType() string {
	if x != nil {
		return x.PlayType
	}
	return ""
}

func (x *StorySummary) GetSubPlayType() string {
	if x != nil {
		return x.SubPlayType
	}
	return ""
}

func (x *StorySummary) GetStatus() StoryStatus {
	if x != nil {
		return x.Status
	}
	return StoryStatus_STORY_STATUS_UNSPECIFIED
}

func (x *StorySummary) GetCreatedAtTimestamp() uint32 {
	if x != nil {
		return x.CreatedAtTimestamp
	}
	return 0
}

func (x *StorySummary) GetExchangeImageExample() *StoryPlayExchangeImageExample {
	if x != nil {
		return x.ExchangeImageExample
	}
	return nil
}

func (x *StorySummary) GetTurtleSoupExample() *StoryPlayTurtleSoupExample {
	if x != nil {
		return x.TurtleSoupExample
	}
	return nil
}

func (x *StorySummary) GetUnmuteExample() *StoryPlayUnmuteExample {
	if x != nil {
		return x.UnmuteExample
	}
	return nil
}

func (x *StorySummary) GetTurtleSoupMassExample() *StoryPlayTurtleSoupMassExample {
	if x != nil {
		return x.TurtleSoupMassExample
	}
	return nil
}

func (x *StorySummary) GetBasePlayExample() *StoryPlayBasePlayExample {
	if x != nil {
		return x.BasePlayExample
	}
	return nil
}

func (x *StorySummary) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

func (x *StorySummary) GetCoverImageWidth() uint32 {
	if x != nil {
		return x.CoverImageWidth
	}
	return 0
}

func (x *StorySummary) GetCoverImageHeight() uint32 {
	if x != nil {
		return x.CoverImageHeight
	}
	return 0
}

func (x *StorySummary) GetCoverType() string {
	if x != nil {
		return x.CoverType
	}
	return ""
}

func (x *StorySummary) GetPrivacySetting() *PrivacySetting {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *StorySummary) GetNowShotPlayExample() *StoryPlayNowShotExample {
	if x != nil {
		return x.NowShotPlayExample
	}
	return nil
}

func (x *StorySummary) GetRoastedExample() *StoryPlayRoastedExample {
	if x != nil {
		return x.RoastedExample
	}
	return nil
}

func (x *StorySummary) GetCapsuleExample() *StoryPlayCapsuleExample {
	if x != nil {
		return x.CapsuleExample
	}
	return nil
}

func (x *StorySummary) GetStats() *StoryStats {
	if x != nil {
		return x.Stats
	}
	return nil
}

func (x *StorySummary) GetLoginUserMadeReactions() []*v1.Reaction {
	if x != nil {
		return x.LoginUserMadeReactions
	}
	return nil
}

func (x *StorySummary) GetUnlockedUsers() *StorySummary_UnlockedUsersInfo {
	if x != nil {
		return x.UnlockedUsers
	}
	return nil
}

func (x *StorySummary) GetHasUnlocked() bool {
	if x != nil {
		return x.HasUnlocked
	}
	return false
}

func (x *StorySummary) GetSortRequestId() string {
	if x != nil {
		return x.SortRequestId
	}
	return ""
}

type StoryPlayExchangeImageExample struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	CommonInfo *ExampleCommonInfo     `protobuf:"bytes,1,opt,name=common_info,json=commonInfo,proto3" json:"common_info,omitempty"`
	// 待交换的图片 url
	Cases         []*StoryPlayExchangeImageExample_Case `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageExample) Reset() {
	*x = StoryPlayExchangeImageExample{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageExample) ProtoMessage() {}

func (x *StoryPlayExchangeImageExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageExample.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayExchangeImageExample) GetCommonInfo() *ExampleCommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *StoryPlayExchangeImageExample) GetCases() []*StoryPlayExchangeImageExample_Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

type StoryPlayUnmuteExample struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	CommonInfo    *ExampleCommonInfo             `protobuf:"bytes,1,opt,name=common_info,json=commonInfo,proto3" json:"common_info,omitempty"`
	Cases         []*StoryPlayUnmuteExample_Case `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayUnmuteExample) Reset() {
	*x = StoryPlayUnmuteExample{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteExample) ProtoMessage() {}

func (x *StoryPlayUnmuteExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteExample.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{3}
}

func (x *StoryPlayUnmuteExample) GetCommonInfo() *ExampleCommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *StoryPlayUnmuteExample) GetCases() []*StoryPlayUnmuteExample_Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

type PortalBasicInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Portal ID
	PortalId string `protobuf:"bytes,1,opt,name=portal_id,json=portalId,proto3" json:"portal_id,omitempty"`
	// Portal 下的 moment 总数
	MomentCount   uint32 `protobuf:"varint,2,opt,name=moment_count,json=momentCount,proto3" json:"moment_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PortalBasicInfo) Reset() {
	*x = PortalBasicInfo{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PortalBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortalBasicInfo) ProtoMessage() {}

func (x *PortalBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortalBasicInfo.ProtoReflect.Descriptor instead.
func (*PortalBasicInfo) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{4}
}

func (x *PortalBasicInfo) GetPortalId() string {
	if x != nil {
		return x.PortalId
	}
	return ""
}

func (x *PortalBasicInfo) GetMomentCount() uint32 {
	if x != nil {
		return x.MomentCount
	}
	return 0
}

type StoryDetail struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Summary *StorySummary          `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	// Types that are valid to be assigned to PlayConfig:
	//
	//	*StoryDetail_ExchangeImageConfig
	//	*StoryDetail_TurtleSoupConfig
	//	*StoryDetail_UnmuteConfig
	//	*StoryDetail_TurtleSoupMassConfig
	//	*StoryDetail_BasePlayConfig
	//	*StoryDetail_NowShotConfig
	//	*StoryDetail_RoastedConfig
	//	*StoryDetail_WassupConfig
	//	*StoryDetail_CapsuleConfig
	//	*StoryDetail_HideConfig
	//	*StoryDetail_ChatproxyConfig
	//	*StoryDetail_WhoConfig
	//	*StoryDetail_HauntConfig
	//	*StoryDetail_PinConfig
	PlayConfig isStoryDetail_PlayConfig `protobuf_oneof:"play_config"`
	// Types that are valid to be assigned to PlayContext:
	//
	//	*StoryDetail_ExchangeImageContext
	//	*StoryDetail_TurtleSoupContext
	//	*StoryDetail_UnmuteContext
	//	*StoryDetail_TurtleSoupMassContext
	//	*StoryDetail_BasePlayContext
	//	*StoryDetail_NowShotContext
	//	*StoryDetail_RoastedContext
	//	*StoryDetail_WassupContext
	//	*StoryDetail_CapsuleContext
	//	*StoryDetail_HideContext
	//	*StoryDetail_ChatproxyContext
	//	*StoryDetail_WhoContext
	//	*StoryDetail_HauntContext
	//	*StoryDetail_PinContext
	PlayContext isStoryDetail_PlayContext `protobuf_oneof:"play_context"`
	// 这是个临时字段，由于开发时间太紧张了，需要在所有的
	// unlock 接口内返回加入的 portal id 字段，无法通过
	// 接口返回，所以临时加了这个字段，后续需要删除 @Larry
	EnabledPortalId *string `protobuf:"bytes,30,opt,name=enabled_portal_id,json=enabledPortalId,proto3,oneof" json:"enabled_portal_id,omitempty"`
	// 针对 haunt 的展示信息
	HauntBooShowInfo *HauntBooShowInfo `protobuf:"bytes,31,opt,name=haunt_boo_show_info,json=hauntBooShowInfo,proto3" json:"haunt_boo_show_info,omitempty"`
	// 关联的Portal基础信息
	PortalBasicInfo *PortalBasicInfo `protobuf:"bytes,32,opt,name=portal_basic_info,json=portalBasicInfo,proto3" json:"portal_basic_info,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StoryDetail) Reset() {
	*x = StoryDetail{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryDetail) ProtoMessage() {}

func (x *StoryDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryDetail.ProtoReflect.Descriptor instead.
func (*StoryDetail) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{5}
}

func (x *StoryDetail) GetSummary() *StorySummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *StoryDetail) GetPlayConfig() isStoryDetail_PlayConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *StoryDetail) GetExchangeImageConfig() *StoryPlayExchangeImageConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_ExchangeImageConfig); ok {
			return x.ExchangeImageConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetTurtleSoupConfig() *StoryPlayTurtleSoupConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_TurtleSoupConfig); ok {
			return x.TurtleSoupConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetUnmuteConfig() *StoryPlayUnmuteConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_UnmuteConfig); ok {
			return x.UnmuteConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetTurtleSoupMassConfig() *StoryPlayTurtleSoupMassConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_TurtleSoupMassConfig); ok {
			return x.TurtleSoupMassConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetBasePlayConfig() *StoryPlayBasePlayConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_BasePlayConfig); ok {
			return x.BasePlayConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetNowShotConfig() *StoryPlayNowShotConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_NowShotConfig); ok {
			return x.NowShotConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetRoastedConfig() *StoryPlayRoastedConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_RoastedConfig); ok {
			return x.RoastedConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetWassupConfig() *StoryPlayWassupConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_WassupConfig); ok {
			return x.WassupConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetCapsuleConfig() *StoryPlayCapsuleConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_CapsuleConfig); ok {
			return x.CapsuleConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetHideConfig() *StoryPlayHideConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_HideConfig); ok {
			return x.HideConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetChatproxyConfig() *StoryPlayChatProxyConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_ChatproxyConfig); ok {
			return x.ChatproxyConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetWhoConfig() *WhoStoryPlayConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_WhoConfig); ok {
			return x.WhoConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetHauntConfig() *HauntPlayConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_HauntConfig); ok {
			return x.HauntConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetPinConfig() *StoryPinConfig {
	if x != nil {
		if x, ok := x.PlayConfig.(*StoryDetail_PinConfig); ok {
			return x.PinConfig
		}
	}
	return nil
}

func (x *StoryDetail) GetPlayContext() isStoryDetail_PlayContext {
	if x != nil {
		return x.PlayContext
	}
	return nil
}

func (x *StoryDetail) GetExchangeImageContext() *StoryPlayExchangeImageContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_ExchangeImageContext); ok {
			return x.ExchangeImageContext
		}
	}
	return nil
}

func (x *StoryDetail) GetTurtleSoupContext() *StoryPlayTurtleSoupContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_TurtleSoupContext); ok {
			return x.TurtleSoupContext
		}
	}
	return nil
}

func (x *StoryDetail) GetUnmuteContext() *StoryPlayUnmuteContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_UnmuteContext); ok {
			return x.UnmuteContext
		}
	}
	return nil
}

func (x *StoryDetail) GetTurtleSoupMassContext() *StoryPlayTurtleSoupMassContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_TurtleSoupMassContext); ok {
			return x.TurtleSoupMassContext
		}
	}
	return nil
}

func (x *StoryDetail) GetBasePlayContext() *StoryPlayBasePlayContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_BasePlayContext); ok {
			return x.BasePlayContext
		}
	}
	return nil
}

func (x *StoryDetail) GetNowShotContext() *StoryPlayNowShotContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_NowShotContext); ok {
			return x.NowShotContext
		}
	}
	return nil
}

func (x *StoryDetail) GetRoastedContext() *StoryPlayRoastedContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_RoastedContext); ok {
			return x.RoastedContext
		}
	}
	return nil
}

func (x *StoryDetail) GetWassupContext() *StoryPlayWassupContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_WassupContext); ok {
			return x.WassupContext
		}
	}
	return nil
}

func (x *StoryDetail) GetCapsuleContext() *StoryPlayCapsuleContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_CapsuleContext); ok {
			return x.CapsuleContext
		}
	}
	return nil
}

func (x *StoryDetail) GetHideContext() *StoryPlayHideContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_HideContext); ok {
			return x.HideContext
		}
	}
	return nil
}

func (x *StoryDetail) GetChatproxyContext() *StoryPlayChatProxyContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_ChatproxyContext); ok {
			return x.ChatproxyContext
		}
	}
	return nil
}

func (x *StoryDetail) GetWhoContext() *WhoStoryPlayContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_WhoContext); ok {
			return x.WhoContext
		}
	}
	return nil
}

func (x *StoryDetail) GetHauntContext() *HauntPlayContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_HauntContext); ok {
			return x.HauntContext
		}
	}
	return nil
}

func (x *StoryDetail) GetPinContext() *StoryPinContext {
	if x != nil {
		if x, ok := x.PlayContext.(*StoryDetail_PinContext); ok {
			return x.PinContext
		}
	}
	return nil
}

func (x *StoryDetail) GetEnabledPortalId() string {
	if x != nil && x.EnabledPortalId != nil {
		return *x.EnabledPortalId
	}
	return ""
}

func (x *StoryDetail) GetHauntBooShowInfo() *HauntBooShowInfo {
	if x != nil {
		return x.HauntBooShowInfo
	}
	return nil
}

func (x *StoryDetail) GetPortalBasicInfo() *PortalBasicInfo {
	if x != nil {
		return x.PortalBasicInfo
	}
	return nil
}

type isStoryDetail_PlayConfig interface {
	isStoryDetail_PlayConfig()
}

type StoryDetail_ExchangeImageConfig struct {
	// 换图玩法配置
	ExchangeImageConfig *StoryPlayExchangeImageConfig `protobuf:"bytes,2,opt,name=exchange_image_config,json=exchangeImageConfig,proto3,oneof"`
}

type StoryDetail_TurtleSoupConfig struct {
	// 海龟汤玩法配置
	TurtleSoupConfig *StoryPlayTurtleSoupConfig `protobuf:"bytes,3,opt,name=turtle_soup_config,json=turtleSoupConfig,proto3,oneof"`
}

type StoryDetail_UnmuteConfig struct {
	// Unmute 玩法配置
	UnmuteConfig *StoryPlayUnmuteConfig `protobuf:"bytes,6,opt,name=unmute_config,json=unmuteConfig,proto3,oneof"`
}

type StoryDetail_TurtleSoupMassConfig struct {
	// 海龟汤 mass 玩法配置
	TurtleSoupMassConfig *StoryPlayTurtleSoupMassConfig `protobuf:"bytes,8,opt,name=turtle_soup_mass_config,json=turtleSoupMassConfig,proto3,oneof"`
}

type StoryDetail_BasePlayConfig struct {
	// BasePlay 玩法配置
	BasePlayConfig *StoryPlayBasePlayConfig `protobuf:"bytes,9,opt,name=base_play_config,json=basePlayConfig,proto3,oneof"`
}

type StoryDetail_NowShotConfig struct {
	// NowShot 玩法配置
	NowShotConfig *StoryPlayNowShotConfig `protobuf:"bytes,12,opt,name=now_shot_config,json=nowShotConfig,proto3,oneof"`
}

type StoryDetail_RoastedConfig struct {
	// roasted 玩法配置
	RoastedConfig *StoryPlayRoastedConfig `protobuf:"bytes,14,opt,name=roasted_config,json=roastedConfig,proto3,oneof"`
}

type StoryDetail_WassupConfig struct {
	// wassup 玩法配置
	WassupConfig *StoryPlayWassupConfig `protobuf:"bytes,16,opt,name=wassup_config,json=wassupConfig,proto3,oneof"`
}

type StoryDetail_CapsuleConfig struct {
	// capsule 玩法配置
	CapsuleConfig *StoryPlayCapsuleConfig `protobuf:"bytes,18,opt,name=capsule_config,json=capsuleConfig,proto3,oneof"`
}

type StoryDetail_HideConfig struct {
	// hide 玩法配置
	HideConfig *StoryPlayHideConfig `protobuf:"bytes,20,opt,name=hide_config,json=hideConfig,proto3,oneof"`
}

type StoryDetail_ChatproxyConfig struct {
	// chatproxy 玩法配置
	ChatproxyConfig *StoryPlayChatProxyConfig `protobuf:"bytes,22,opt,name=chatproxy_config,json=chatproxyConfig,proto3,oneof"`
}

type StoryDetail_WhoConfig struct {
	// Who 玩法配置
	WhoConfig *WhoStoryPlayConfig `protobuf:"bytes,24,opt,name=who_config,json=whoConfig,proto3,oneof"`
}

type StoryDetail_HauntConfig struct {
	// Haunt 玩法配置
	HauntConfig *HauntPlayConfig `protobuf:"bytes,26,opt,name=haunt_config,json=hauntConfig,proto3,oneof"`
}

type StoryDetail_PinConfig struct {
	// Pin 玩法配置
	PinConfig *StoryPinConfig `protobuf:"bytes,28,opt,name=pin_config,json=pinConfig,proto3,oneof"`
}

func (*StoryDetail_ExchangeImageConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_TurtleSoupConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_UnmuteConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_TurtleSoupMassConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_BasePlayConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_NowShotConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_RoastedConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_WassupConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_CapsuleConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_HideConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_ChatproxyConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_WhoConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_HauntConfig) isStoryDetail_PlayConfig() {}

func (*StoryDetail_PinConfig) isStoryDetail_PlayConfig() {}

type isStoryDetail_PlayContext interface {
	isStoryDetail_PlayContext()
}

type StoryDetail_ExchangeImageContext struct {
	// 换图玩法上下文
	ExchangeImageContext *StoryPlayExchangeImageContext `protobuf:"bytes,4,opt,name=exchange_image_context,json=exchangeImageContext,proto3,oneof"`
}

type StoryDetail_TurtleSoupContext struct {
	// 海龟汤玩法上下文
	TurtleSoupContext *StoryPlayTurtleSoupContext `protobuf:"bytes,5,opt,name=turtle_soup_context,json=turtleSoupContext,proto3,oneof"`
}

type StoryDetail_UnmuteContext struct {
	// Unmute 玩法上下文
	UnmuteContext *StoryPlayUnmuteContext `protobuf:"bytes,7,opt,name=unmute_context,json=unmuteContext,proto3,oneof"`
}

type StoryDetail_TurtleSoupMassContext struct {
	// 海龟汤 mass 玩法上下文
	TurtleSoupMassContext *StoryPlayTurtleSoupMassContext `protobuf:"bytes,10,opt,name=turtle_soup_mass_context,json=turtleSoupMassContext,proto3,oneof"`
}

type StoryDetail_BasePlayContext struct {
	// BasePlay 玩法上下文
	BasePlayContext *StoryPlayBasePlayContext `protobuf:"bytes,11,opt,name=base_play_context,json=basePlayContext,proto3,oneof"`
}

type StoryDetail_NowShotContext struct {
	// NowShot 玩法上下文
	NowShotContext *StoryPlayNowShotContext `protobuf:"bytes,13,opt,name=now_shot_context,json=nowShotContext,proto3,oneof"`
}

type StoryDetail_RoastedContext struct {
	// roasted 玩法上下文
	RoastedContext *StoryPlayRoastedContext `protobuf:"bytes,15,opt,name=roasted_context,json=roastedContext,proto3,oneof"`
}

type StoryDetail_WassupContext struct {
	// wassup 玩法上下文
	WassupContext *StoryPlayWassupContext `protobuf:"bytes,17,opt,name=wassup_context,json=wassupContext,proto3,oneof"`
}

type StoryDetail_CapsuleContext struct {
	// capsule 玩法上下文
	CapsuleContext *StoryPlayCapsuleContext `protobuf:"bytes,19,opt,name=capsule_context,json=capsuleContext,proto3,oneof"`
}

type StoryDetail_HideContext struct {
	// hide 玩法上下文
	HideContext *StoryPlayHideContext `protobuf:"bytes,21,opt,name=hide_context,json=hideContext,proto3,oneof"`
}

type StoryDetail_ChatproxyContext struct {
	// chatproxy 玩法上下文
	ChatproxyContext *StoryPlayChatProxyContext `protobuf:"bytes,23,opt,name=chatproxy_context,json=chatproxyContext,proto3,oneof"`
}

type StoryDetail_WhoContext struct {
	// Who 玩法上下文
	WhoContext *WhoStoryPlayContext `protobuf:"bytes,25,opt,name=who_context,json=whoContext,proto3,oneof"`
}

type StoryDetail_HauntContext struct {
	// Haunt 玩法上下文
	HauntContext *HauntPlayContext `protobuf:"bytes,27,opt,name=haunt_context,json=hauntContext,proto3,oneof"`
}

type StoryDetail_PinContext struct {
	// Pin 玩法上下文
	PinContext *StoryPinContext `protobuf:"bytes,29,opt,name=pin_context,json=pinContext,proto3,oneof"`
}

func (*StoryDetail_ExchangeImageContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_TurtleSoupContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_UnmuteContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_TurtleSoupMassContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_BasePlayContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_NowShotContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_RoastedContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_WassupContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_CapsuleContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_HideContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_ChatproxyContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_WhoContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_HauntContext) isStoryDetail_PlayContext() {}

func (*StoryDetail_PinContext) isStoryDetail_PlayContext() {}

type StoryExchangeImageConditionTemplate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 封面图片，创建时，客户端传 key
	CoverImageUrl    string                           `protobuf:"bytes,2,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	DefaultCondition *StoryPlayExchangeImageCondition `protobuf:"bytes,3,opt,name=default_condition,json=defaultCondition,proto3" json:"default_condition,omitempty"`
	Typ              TemplateType                     `protobuf:"varint,4,opt,name=typ,proto3,enum=api.items.story.types.v1.TemplateType" json:"typ,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *StoryExchangeImageConditionTemplate) Reset() {
	*x = StoryExchangeImageConditionTemplate{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryExchangeImageConditionTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryExchangeImageConditionTemplate) ProtoMessage() {}

func (x *StoryExchangeImageConditionTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryExchangeImageConditionTemplate.ProtoReflect.Descriptor instead.
func (*StoryExchangeImageConditionTemplate) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{6}
}

func (x *StoryExchangeImageConditionTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryExchangeImageConditionTemplate) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

func (x *StoryExchangeImageConditionTemplate) GetDefaultCondition() *StoryPlayExchangeImageCondition {
	if x != nil {
		return x.DefaultCondition
	}
	return nil
}

func (x *StoryExchangeImageConditionTemplate) GetTyp() TemplateType {
	if x != nil {
		return x.Typ
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

type UnmutePromptStyle struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// prompt text
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// normalized position
	X string `protobuf:"bytes,2,opt,name=x,proto3" json:"x,omitempty"`
	// normalized position
	Y string `protobuf:"bytes,3,opt,name=y,proto3" json:"y,omitempty"`
	// font size
	FontSize uint32 `protobuf:"varint,4,opt,name=font_size,json=fontSize,proto3" json:"font_size,omitempty"`
	// font name
	FontName string `protobuf:"bytes,5,opt,name=font_name,json=fontName,proto3" json:"font_name,omitempty"`
	// font color
	Color string `protobuf:"bytes,6,opt,name=color,proto3" json:"color,omitempty"`
	// prompt height
	Height string `protobuf:"bytes,7,opt,name=height,proto3" json:"height,omitempty"`
	// prompt width
	Width         string `protobuf:"bytes,8,opt,name=width,proto3" json:"width,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UnmutePromptStyle) Reset() {
	*x = UnmutePromptStyle{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnmutePromptStyle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnmutePromptStyle) ProtoMessage() {}

func (x *UnmutePromptStyle) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnmutePromptStyle.ProtoReflect.Descriptor instead.
func (*UnmutePromptStyle) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{7}
}

func (x *UnmutePromptStyle) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *UnmutePromptStyle) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *UnmutePromptStyle) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

func (x *UnmutePromptStyle) GetFontSize() uint32 {
	if x != nil {
		return x.FontSize
	}
	return 0
}

func (x *UnmutePromptStyle) GetFontName() string {
	if x != nil {
		return x.FontName
	}
	return ""
}

func (x *UnmutePromptStyle) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *UnmutePromptStyle) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *UnmutePromptStyle) GetWidth() string {
	if x != nil {
		return x.Width
	}
	return ""
}

type StoryPlayUnmuteConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// public caption 提示 for human
	Prompt *AttachmentText `protobuf:"bytes,1,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// 资源类型，参考 ResourceType
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// 资源 url,第一段（封面内容）：公开展示，用于吸引点击；给出引导;创建时，客户端传 key
	ResourceUrl string `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 结束资源类型，参考 ResourceType
	EndResourceType string `protobuf:"bytes,4,opt,name=end_resource_type,json=endResourceType,proto3" json:"end_resource_type,omitempty"`
	// 结束资源 url，第二段（隐藏内容）：默认打码，需通过语音互动完成后自动揭晓;创建时，客户端传 key
	EndResourceUrl    string                                    `protobuf:"bytes,5,opt,name=end_resource_url,json=endResourceUrl,proto3" json:"end_resource_url,omitempty"`
	CustomAiResponses []*StoryPlayUnmuteConfig_CustomAiResponse `protobuf:"bytes,6,rep,name=custom_ai_responses,json=customAiResponses,proto3" json:"custom_ai_responses,omitempty"`
	// 最大尝试次数
	MaxTryCount uint32 `protobuf:"varint,7,opt,name=max_try_count,json=maxTryCount,proto3" json:"max_try_count,omitempty"`
	// 视频首帧，当且仅当资源类型为视频时才有
	ThumbnailUrl *string `protobuf:"bytes,8,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	// 结束视频首帧，当且仅当资源类型为视频时才有
	EndThumbnailUrl *string `protobuf:"bytes,9,opt,name=end_thumbnail_url,json=endThumbnailUrl,proto3,oneof" json:"end_thumbnail_url,omitempty"`
	// intention for llm
	Intention         string              `protobuf:"bytes,10,opt,name=intention,proto3" json:"intention,omitempty"`
	CommonPlayConfig  *CommonPlayConfig   `protobuf:"bytes,11,opt,name=common_play_config,json=commonPlayConfig,proto3" json:"common_play_config,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr `protobuf:"bytes,12,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayUnmuteConfig) Reset() {
	*x = StoryPlayUnmuteConfig{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteConfig) ProtoMessage() {}

func (x *StoryPlayUnmuteConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{8}
}

func (x *StoryPlayUnmuteConfig) GetPrompt() *AttachmentText {
	if x != nil {
		return x.Prompt
	}
	return nil
}

func (x *StoryPlayUnmuteConfig) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetEndResourceType() string {
	if x != nil {
		return x.EndResourceType
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetEndResourceUrl() string {
	if x != nil {
		return x.EndResourceUrl
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetCustomAiResponses() []*StoryPlayUnmuteConfig_CustomAiResponse {
	if x != nil {
		return x.CustomAiResponses
	}
	return nil
}

func (x *StoryPlayUnmuteConfig) GetMaxTryCount() uint32 {
	if x != nil {
		return x.MaxTryCount
	}
	return 0
}

func (x *StoryPlayUnmuteConfig) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetEndThumbnailUrl() string {
	if x != nil && x.EndThumbnailUrl != nil {
		return *x.EndThumbnailUrl
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetIntention() string {
	if x != nil {
		return x.Intention
	}
	return ""
}

func (x *StoryPlayUnmuteConfig) GetCommonPlayConfig() *CommonPlayConfig {
	if x != nil {
		return x.CommonPlayConfig
	}
	return nil
}

func (x *StoryPlayUnmuteConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

type CommonStoryPlayConditionTemplate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Condition     *Condition             `protobuf:"bytes,2,opt,name=condition,proto3" json:"condition,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonStoryPlayConditionTemplate) Reset() {
	*x = CommonStoryPlayConditionTemplate{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonStoryPlayConditionTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonStoryPlayConditionTemplate) ProtoMessage() {}

func (x *CommonStoryPlayConditionTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonStoryPlayConditionTemplate.ProtoReflect.Descriptor instead.
func (*CommonStoryPlayConditionTemplate) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{9}
}

func (x *CommonStoryPlayConditionTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CommonStoryPlayConditionTemplate) GetCondition() *Condition {
	if x != nil {
		return x.Condition
	}
	return nil
}

type StoryPlayTurtleConditionTemplate struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Id      string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Summary string                 `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	// 封面图片，创建时，客户端传 key
	CoverImageUrl string       `protobuf:"bytes,3,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	Caption       string       `protobuf:"bytes,4,opt,name=caption,proto3" json:"caption,omitempty"`
	IntentPrompt  string       `protobuf:"bytes,5,opt,name=intent_prompt,json=intentPrompt,proto3" json:"intent_prompt,omitempty"`
	Typ           TemplateType `protobuf:"varint,6,opt,name=typ,proto3,enum=api.items.story.types.v1.TemplateType" json:"typ,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleConditionTemplate) Reset() {
	*x = StoryPlayTurtleConditionTemplate{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleConditionTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleConditionTemplate) ProtoMessage() {}

func (x *StoryPlayTurtleConditionTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleConditionTemplate.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleConditionTemplate) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{10}
}

func (x *StoryPlayTurtleConditionTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryPlayTurtleConditionTemplate) GetSummary() string {
	if x != nil {
		return x.Summary
	}
	return ""
}

func (x *StoryPlayTurtleConditionTemplate) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

func (x *StoryPlayTurtleConditionTemplate) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *StoryPlayTurtleConditionTemplate) GetIntentPrompt() string {
	if x != nil {
		return x.IntentPrompt
	}
	return ""
}

func (x *StoryPlayTurtleConditionTemplate) GetTyp() TemplateType {
	if x != nil {
		return x.Typ
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

type StoryPlayUnmuteConditionTemplate struct {
	state            protoimpl.MessageState    `protogen:"open.v1"`
	Id               string                    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DefaultCondition *StoryPlayUnmuteCondition `protobuf:"bytes,2,opt,name=default_condition,json=defaultCondition,proto3" json:"default_condition,omitempty"`
	Typ              TemplateType              `protobuf:"varint,3,opt,name=typ,proto3,enum=api.items.story.types.v1.TemplateType" json:"typ,omitempty"`
	// 封面图片，创建时，客户端传 key
	CoverImageUrl string `protobuf:"bytes,4,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayUnmuteConditionTemplate) Reset() {
	*x = StoryPlayUnmuteConditionTemplate{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteConditionTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteConditionTemplate) ProtoMessage() {}

func (x *StoryPlayUnmuteConditionTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteConditionTemplate.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteConditionTemplate) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{11}
}

func (x *StoryPlayUnmuteConditionTemplate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryPlayUnmuteConditionTemplate) GetDefaultCondition() *StoryPlayUnmuteCondition {
	if x != nil {
		return x.DefaultCondition
	}
	return nil
}

func (x *StoryPlayUnmuteConditionTemplate) GetTyp() TemplateType {
	if x != nil {
		return x.Typ
	}
	return TemplateType_TEMPLATE_TYPE_UNSPECIFIED
}

func (x *StoryPlayUnmuteConditionTemplate) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

type StoryPlayUnmuteCondition struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prompt        string                 `protobuf:"bytes,1,opt,name=prompt,proto3" json:"prompt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayUnmuteCondition) Reset() {
	*x = StoryPlayUnmuteCondition{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteCondition) ProtoMessage() {}

func (x *StoryPlayUnmuteCondition) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteCondition.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteCondition) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{12}
}

func (x *StoryPlayUnmuteCondition) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

// 换图玩法解锁条件
type StoryPlayExchangeImageCondition struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 提示
	Tips string `protobuf:"bytes,1,opt,name=tips,proto3" json:"tips,omitempty"`
	// 正向引导
	PositiveGuide string `protobuf:"bytes,2,opt,name=positive_guide,json=positiveGuide,proto3" json:"positive_guide,omitempty"`
	// 正向引导图片
	PositiveImageUrls []string `protobuf:"bytes,3,rep,name=positive_image_urls,json=positiveImageUrls,proto3" json:"positive_image_urls,omitempty"`
	// 正向引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
	PositiveImageKeys []string `protobuf:"bytes,4,rep,name=positive_image_keys,json=positiveImageKeys,proto3" json:"positive_image_keys,omitempty"`
	// 部分匹配引导
	PartialMatchGuide string `protobuf:"bytes,5,opt,name=partial_match_guide,json=partialMatchGuide,proto3" json:"partial_match_guide,omitempty"`
	// 部分匹配引导图片
	PartialMatchImageUrls []string `protobuf:"bytes,6,rep,name=partial_match_image_urls,json=partialMatchImageUrls,proto3" json:"partial_match_image_urls,omitempty"`
	// 部分匹配引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
	PartialMatchImageKeys []string `protobuf:"bytes,7,rep,name=partial_match_image_keys,json=partialMatchImageKeys,proto3" json:"partial_match_image_keys,omitempty"`
	// 负向引导
	NegativeGuide string `protobuf:"bytes,8,opt,name=negative_guide,json=negativeGuide,proto3" json:"negative_guide,omitempty"`
	// 负向引导图片
	NegativeImageUrls []string `protobuf:"bytes,9,rep,name=negative_image_urls,json=negativeImageUrls,proto3" json:"negative_image_urls,omitempty"`
	// 负向引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
	NegativeImageKeys []string `protobuf:"bytes,10,rep,name=negative_image_keys,json=negativeImageKeys,proto3" json:"negative_image_keys,omitempty"`
	// llm 的 prompt，即 Creator Criteria
	LlmPrompt     string `protobuf:"bytes,11,opt,name=llm_prompt,json=llmPrompt,proto3" json:"llm_prompt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageCondition) Reset() {
	*x = StoryPlayExchangeImageCondition{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageCondition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageCondition) ProtoMessage() {}

func (x *StoryPlayExchangeImageCondition) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageCondition.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageCondition) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{13}
}

func (x *StoryPlayExchangeImageCondition) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *StoryPlayExchangeImageCondition) GetPositiveGuide() string {
	if x != nil {
		return x.PositiveGuide
	}
	return ""
}

func (x *StoryPlayExchangeImageCondition) GetPositiveImageUrls() []string {
	if x != nil {
		return x.PositiveImageUrls
	}
	return nil
}

func (x *StoryPlayExchangeImageCondition) GetPositiveImageKeys() []string {
	if x != nil {
		return x.PositiveImageKeys
	}
	return nil
}

func (x *StoryPlayExchangeImageCondition) GetPartialMatchGuide() string {
	if x != nil {
		return x.PartialMatchGuide
	}
	return ""
}

func (x *StoryPlayExchangeImageCondition) GetPartialMatchImageUrls() []string {
	if x != nil {
		return x.PartialMatchImageUrls
	}
	return nil
}

func (x *StoryPlayExchangeImageCondition) GetPartialMatchImageKeys() []string {
	if x != nil {
		return x.PartialMatchImageKeys
	}
	return nil
}

func (x *StoryPlayExchangeImageCondition) GetNegativeGuide() string {
	if x != nil {
		return x.NegativeGuide
	}
	return ""
}

func (x *StoryPlayExchangeImageCondition) GetNegativeImageUrls() []string {
	if x != nil {
		return x.NegativeImageUrls
	}
	return nil
}

func (x *StoryPlayExchangeImageCondition) GetNegativeImageKeys() []string {
	if x != nil {
		return x.NegativeImageKeys
	}
	return nil
}

func (x *StoryPlayExchangeImageCondition) GetLlmPrompt() string {
	if x != nil {
		return x.LlmPrompt
	}
	return ""
}

// Unmute 玩法上下文
type StoryPlayUnmuteContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否已经完成
	IsFinished bool `protobuf:"varint,1,opt,name=is_finished,json=isFinished,proto3" json:"is_finished,omitempty"`
	// ai 的回复
	AiResponse string `protobuf:"bytes,2,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	// 尝试次数
	TryCount uint32 `protobuf:"varint,3,opt,name=try_count,json=tryCount,proto3" json:"try_count,omitempty"`
	// 历史尝试音频
	AudioKeys     []string `protobuf:"bytes,4,rep,name=audio_keys,json=audioKeys,proto3" json:"audio_keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayUnmuteContext) Reset() {
	*x = StoryPlayUnmuteContext{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteContext) ProtoMessage() {}

func (x *StoryPlayUnmuteContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteContext.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{14}
}

func (x *StoryPlayUnmuteContext) GetIsFinished() bool {
	if x != nil {
		return x.IsFinished
	}
	return false
}

func (x *StoryPlayUnmuteContext) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

func (x *StoryPlayUnmuteContext) GetTryCount() uint32 {
	if x != nil {
		return x.TryCount
	}
	return 0
}

func (x *StoryPlayUnmuteContext) GetAudioKeys() []string {
	if x != nil {
		return x.AudioKeys
	}
	return nil
}

// 换图玩法上下文
type StoryPlayExchangeImageContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户当前玩到的 node id
	// 主要是为了服务线性模式下，用户处于的状态
	// 如果马赛克模式，此字段为固定值，永远为第一个 node id
	CurrentNodeId string `protobuf:"bytes,1,opt,name=current_node_id,json=currentNodeId,proto3" json:"current_node_id,omitempty"`
	// 用户在当前 story 下的各个node尝试次数
	// key 为 node_id，value 为尝试次数
	// all values 的和应该等于当前 story 的总游玩次数
	CurrentTryCount map[string]uint32 `protobuf:"bytes,2,rep,name=current_try_count,json=currentTryCount,proto3" json:"current_try_count,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// 用户在各个 node 下的完成率
	// 如果是换图模式，用户完成了这个 node，则固定为 1，否则为 0
	// 如果是马赛克模式，就可以表达马赛克的消除程度
	CurrentSuccessProgress map[string]uint32 `protobuf:"bytes,3,rep,name=current_success_progress,json=currentSuccessProgress,proto3" json:"current_success_progress,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	// 是否已经通关
	IsFinished bool `protobuf:"varint,4,opt,name=is_finished,json=isFinished,proto3" json:"is_finished,omitempty"`
	// 用户在各个 node 下的尝试记录
	UserExchangeImageUrls []*StoryPlayExchangeImageContext_UserExchangeImageUrls `protobuf:"bytes,5,rep,name=user_exchange_image_urls,json=userExchangeImageUrls,proto3" json:"user_exchange_image_urls,omitempty"`
	// 尝试次数
	TryCount           uint32   `protobuf:"varint,6,opt,name=try_count,json=tryCount,proto3" json:"try_count,omitempty"`
	UserTrialImageUrls []string `protobuf:"bytes,7,rep,name=user_trial_image_urls,json=userTrialImageUrls,proto3" json:"user_trial_image_urls,omitempty"`
	UserTrialImageKeys []string `protobuf:"bytes,8,rep,name=user_trial_image_keys,json=userTrialImageKeys,proto3" json:"user_trial_image_keys,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageContext) Reset() {
	*x = StoryPlayExchangeImageContext{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageContext) ProtoMessage() {}

func (x *StoryPlayExchangeImageContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageContext.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{15}
}

func (x *StoryPlayExchangeImageContext) GetCurrentNodeId() string {
	if x != nil {
		return x.CurrentNodeId
	}
	return ""
}

func (x *StoryPlayExchangeImageContext) GetCurrentTryCount() map[string]uint32 {
	if x != nil {
		return x.CurrentTryCount
	}
	return nil
}

func (x *StoryPlayExchangeImageContext) GetCurrentSuccessProgress() map[string]uint32 {
	if x != nil {
		return x.CurrentSuccessProgress
	}
	return nil
}

func (x *StoryPlayExchangeImageContext) GetIsFinished() bool {
	if x != nil {
		return x.IsFinished
	}
	return false
}

func (x *StoryPlayExchangeImageContext) GetUserExchangeImageUrls() []*StoryPlayExchangeImageContext_UserExchangeImageUrls {
	if x != nil {
		return x.UserExchangeImageUrls
	}
	return nil
}

func (x *StoryPlayExchangeImageContext) GetTryCount() uint32 {
	if x != nil {
		return x.TryCount
	}
	return 0
}

func (x *StoryPlayExchangeImageContext) GetUserTrialImageUrls() []string {
	if x != nil {
		return x.UserTrialImageUrls
	}
	return nil
}

func (x *StoryPlayExchangeImageContext) GetUserTrialImageKeys() []string {
	if x != nil {
		return x.UserTrialImageKeys
	}
	return nil
}

// 换图玩法配置
type StoryPlayExchangeImageConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 换图玩法类型，参考 ExchangeImagePlayMode
	PlayMode string `protobuf:"bytes,1,opt,name=play_mode,json=playMode,proto3" json:"play_mode,omitempty"`
	// 节点列表，按照先后顺序排序，其中，节点的 next_node_id 为空时，表示该节点为终点
	// 如果 play mode 为马赛克时，此数组应该只有一个 element
	Nodes             []*StoryPlayExchangeImageConfig_Node `protobuf:"bytes,2,rep,name=nodes,proto3" json:"nodes,omitempty"`
	CommonPlayConfig  *CommonPlayConfig                    `protobuf:"bytes,3,opt,name=common_play_config,json=commonPlayConfig,proto3" json:"common_play_config,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr                  `protobuf:"bytes,4,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageConfig) Reset() {
	*x = StoryPlayExchangeImageConfig{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageConfig) ProtoMessage() {}

func (x *StoryPlayExchangeImageConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{16}
}

func (x *StoryPlayExchangeImageConfig) GetPlayMode() string {
	if x != nil {
		return x.PlayMode
	}
	return ""
}

func (x *StoryPlayExchangeImageConfig) GetNodes() []*StoryPlayExchangeImageConfig_Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *StoryPlayExchangeImageConfig) GetCommonPlayConfig() *CommonPlayConfig {
	if x != nil {
		return x.CommonPlayConfig
	}
	return nil
}

func (x *StoryPlayExchangeImageConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

type StorySummary_UnlockedUsersInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*v11.UserInfoSummary `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	TotalCount    uint32                 `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	HasMore       bool                   `protobuf:"varint,4,opt,name=has_more,json=hasMore,proto3" json:"has_more,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StorySummary_UnlockedUsersInfo) Reset() {
	*x = StorySummary_UnlockedUsersInfo{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StorySummary_UnlockedUsersInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StorySummary_UnlockedUsersInfo) ProtoMessage() {}

func (x *StorySummary_UnlockedUsersInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StorySummary_UnlockedUsersInfo.ProtoReflect.Descriptor instead.
func (*StorySummary_UnlockedUsersInfo) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{1, 0}
}

func (x *StorySummary_UnlockedUsersInfo) GetUsers() []*v11.UserInfoSummary {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *StorySummary_UnlockedUsersInfo) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *StorySummary_UnlockedUsersInfo) GetHasMore() bool {
	if x != nil {
		return x.HasMore
	}
	return false
}

type StoryPlayExchangeImageExample_Case struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 待交换的图片 url
	ExchangeImageUrl string `protobuf:"bytes,2,opt,name=exchange_image_url,json=exchangeImageUrl,proto3" json:"exchange_image_url,omitempty"`
	// ai 的回复
	AiResponse    string `protobuf:"bytes,3,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageExample_Case) Reset() {
	*x = StoryPlayExchangeImageExample_Case{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageExample_Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageExample_Case) ProtoMessage() {}

func (x *StoryPlayExchangeImageExample_Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageExample_Case.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageExample_Case) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{2, 0}
}

func (x *StoryPlayExchangeImageExample_Case) GetExchangeImageUrl() string {
	if x != nil {
		return x.ExchangeImageUrl
	}
	return ""
}

func (x *StoryPlayExchangeImageExample_Case) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

type StoryPlayUnmuteExample_Case struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prompt        string                 `protobuf:"bytes,1,opt,name=prompt,proto3" json:"prompt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayUnmuteExample_Case) Reset() {
	*x = StoryPlayUnmuteExample_Case{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteExample_Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteExample_Case) ProtoMessage() {}

func (x *StoryPlayUnmuteExample_Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteExample_Case.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteExample_Case) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{3, 0}
}

func (x *StoryPlayUnmuteExample_Case) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

// 自定义 ai 规则
type StoryPlayUnmuteConfig_CustomAiResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RuleDescription string                 `protobuf:"bytes,1,opt,name=rule_description,json=ruleDescription,proto3" json:"rule_description,omitempty"`
	RuleResult      string                 `protobuf:"bytes,2,opt,name=rule_result,json=ruleResult,proto3" json:"rule_result,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StoryPlayUnmuteConfig_CustomAiResponse) Reset() {
	*x = StoryPlayUnmuteConfig_CustomAiResponse{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayUnmuteConfig_CustomAiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayUnmuteConfig_CustomAiResponse) ProtoMessage() {}

func (x *StoryPlayUnmuteConfig_CustomAiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayUnmuteConfig_CustomAiResponse.ProtoReflect.Descriptor instead.
func (*StoryPlayUnmuteConfig_CustomAiResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{8, 0}
}

func (x *StoryPlayUnmuteConfig_CustomAiResponse) GetRuleDescription() string {
	if x != nil {
		return x.RuleDescription
	}
	return ""
}

func (x *StoryPlayUnmuteConfig_CustomAiResponse) GetRuleResult() string {
	if x != nil {
		return x.RuleResult
	}
	return ""
}

// 用户在各个 node 下的尝试记录
type StoryPlayExchangeImageContext_UserExchangeImageUrls struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeId        string                 `protobuf:"bytes,1,opt,name=node_id,json=nodeId,proto3" json:"node_id,omitempty"`
	ImageUrls     []string               `protobuf:"bytes,2,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageContext_UserExchangeImageUrls) Reset() {
	*x = StoryPlayExchangeImageContext_UserExchangeImageUrls{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageContext_UserExchangeImageUrls) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageContext_UserExchangeImageUrls) ProtoMessage() {}

func (x *StoryPlayExchangeImageContext_UserExchangeImageUrls) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageContext_UserExchangeImageUrls.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageContext_UserExchangeImageUrls) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{15, 2}
}

func (x *StoryPlayExchangeImageContext_UserExchangeImageUrls) GetNodeId() string {
	if x != nil {
		return x.NodeId
	}
	return ""
}

func (x *StoryPlayExchangeImageContext_UserExchangeImageUrls) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

// 换图玩法里的节点
type StoryPlayExchangeImageConfig_Node struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 节点 id，由端上写入时确保唯一
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// V1 视频首帧，当且仅当资源类型为视频时才有，已废弃，使用resource
	ThumbnailUrl *string `protobuf:"bytes,2,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	// V1 资源类型，参考 ResourceType，已废弃，使用resource
	ResourceType string `protobuf:"bytes,3,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// V1 资源 url，创建时，客户端传 key，已废弃，使用resource
	ResourceUrl string `protobuf:"bytes,4,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 条件，进入下一个节点的条件
	Condition *StoryPlayExchangeImageCondition `protobuf:"bytes,6,opt,name=condition,proto3" json:"condition,omitempty"`
	// 是否允许使用相册
	AllowUseAlbum bool `protobuf:"varint,7,opt,name=allow_use_album,json=allowUseAlbum,proto3" json:"allow_use_album,omitempty"`
	// 最多允许尝试多少次
	MaxTryCount           uint32 `protobuf:"varint,8,opt,name=max_try_count,json=maxTryCount,proto3" json:"max_try_count,omitempty"`
	NeedCreateDiyTemplate bool   `protobuf:"varint,9,opt,name=need_create_diy_template,json=needCreateDiyTemplate,proto3" json:"need_create_diy_template,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *StoryPlayExchangeImageConfig_Node) Reset() {
	*x = StoryPlayExchangeImageConfig_Node{}
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayExchangeImageConfig_Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayExchangeImageConfig_Node) ProtoMessage() {}

func (x *StoryPlayExchangeImageConfig_Node) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_types_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayExchangeImageConfig_Node.ProtoReflect.Descriptor instead.
func (*StoryPlayExchangeImageConfig_Node) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_types_proto_rawDescGZIP(), []int{16, 0}
}

func (x *StoryPlayExchangeImageConfig_Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryPlayExchangeImageConfig_Node) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

func (x *StoryPlayExchangeImageConfig_Node) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *StoryPlayExchangeImageConfig_Node) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *StoryPlayExchangeImageConfig_Node) GetCondition() *StoryPlayExchangeImageCondition {
	if x != nil {
		return x.Condition
	}
	return nil
}

func (x *StoryPlayExchangeImageConfig_Node) GetAllowUseAlbum() bool {
	if x != nil {
		return x.AllowUseAlbum
	}
	return false
}

func (x *StoryPlayExchangeImageConfig_Node) GetMaxTryCount() uint32 {
	if x != nil {
		return x.MaxTryCount
	}
	return 0
}

func (x *StoryPlayExchangeImageConfig_Node) GetNeedCreateDiyTemplate() bool {
	if x != nil {
		return x.NeedCreateDiyTemplate
	}
	return false
}

var File_api_items_story_types_v1_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_types_proto_rawDesc = "" +
	"\n" +
	"$api/items/story/types/v1/types.proto\x12\x18api.items.story.types.v1\x1a#api/users/info/types/v1/types.proto\x1a0api/items/story/types/v1/turtle_soup_types.proto\x1a-api/items/story/types/v1/now_shot_types.proto\x1a)api/items/story/types/v1/base_types.proto\x1a4api/items/story/types/v1/base_play_story_types.proto\x1a.api/items/story/types/v1/privacy_setting.proto\x1a,api/items/story/types/v1/roasted.types.proto\x1a+api/items/story/types/v1/wassup.types.proto\x1a,api/items/story/types/v1/capsule_types.proto\x1a-api/items/story/reaction/types/v1/types.proto\x1a)api/items/story/types/v1/hide.types.proto\x1a.api/items/story/types/v1/chatproxy.types.proto\x1a(api/items/story/types/v1/who.types.proto\x1a*api/items/story/types/v1/haunt.types.proto\x1a(api/items/story/types/v1/pin.types.proto\"\xa4\x01\n" +
	"\n" +
	"StoryStats\x12w\n" +
	"\x1areaction_made_stat_summary\x18\x01 \x01(\v2:.api.items.story.reaction.types.v1.ReactionMadeStatSummaryR\x17reactionMadeStatSummary\x12\x1d\n" +
	"\n" +
	"share_stat\x18\x02 \x01(\rR\tshareStat\"\x87\x0e\n" +
	"\fStorySummary\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12@\n" +
	"\x06author\x18\x02 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\x06author\x12\x1b\n" +
	"\tplay_type\x18\x03 \x01(\tR\bplayType\x12\"\n" +
	"\rsub_play_type\x18\x04 \x01(\tR\vsubPlayType\x12=\n" +
	"\x06status\x18\x18 \x01(\x0e2%.api.items.story.types.v1.StoryStatusR\x06status\x120\n" +
	"\x14created_at_timestamp\x18\x05 \x01(\rR\x12createdAtTimestamp\x12m\n" +
	"\x16exchange_image_example\x18\x06 \x01(\v27.api.items.story.types.v1.StoryPlayExchangeImageExampleR\x14exchangeImageExample\x12d\n" +
	"\x13turtle_soup_example\x18\a \x01(\v24.api.items.story.types.v1.StoryPlayTurtleSoupExampleR\x11turtleSoupExample\x12W\n" +
	"\x0eunmute_example\x18\b \x01(\v20.api.items.story.types.v1.StoryPlayUnmuteExampleR\runmuteExample\x12q\n" +
	"\x18turtle_soup_mass_example\x18\t \x01(\v28.api.items.story.types.v1.StoryPlayTurtleSoupMassExampleR\x15turtleSoupMassExample\x12^\n" +
	"\x11base_play_example\x18\n" +
	" \x01(\v22.api.items.story.types.v1.StoryPlayBasePlayExampleR\x0fbasePlayExample\x12&\n" +
	"\x0fcover_image_url\x18\v \x01(\tR\rcoverImageUrl\x12*\n" +
	"\x11cover_image_width\x18\f \x01(\rR\x0fcoverImageWidth\x12,\n" +
	"\x12cover_image_height\x18\r \x01(\rR\x10coverImageHeight\x12\x1d\n" +
	"\n" +
	"cover_type\x18\x17 \x01(\tR\tcoverType\x12Q\n" +
	"\x0fprivacy_setting\x18\x0e \x01(\v2(.api.items.story.types.v1.PrivacySettingR\x0eprivacySetting\x12d\n" +
	"\x15now_shot_play_example\x18\x0f \x01(\v21.api.items.story.types.v1.StoryPlayNowShotExampleR\x12nowShotPlayExample\x12Z\n" +
	"\x0froasted_example\x18\x10 \x01(\v21.api.items.story.types.v1.StoryPlayRoastedExampleR\x0eroastedExample\x12Z\n" +
	"\x0fcapsule_example\x18\x12 \x01(\v21.api.items.story.types.v1.StoryPlayCapsuleExampleR\x0ecapsuleExample\x12:\n" +
	"\x05stats\x18\x13 \x01(\v2$.api.items.story.types.v1.StoryStatsR\x05stats\x12f\n" +
	"\x19login_user_made_reactions\x18\x14 \x03(\v2+.api.items.story.reaction.types.v1.ReactionR\x16loginUserMadeReactions\x12_\n" +
	"\x0eunlocked_users\x18\x15 \x01(\v28.api.items.story.types.v1.StorySummary.UnlockedUsersInfoR\runlockedUsers\x12!\n" +
	"\fhas_unlocked\x18\x16 \x01(\bR\vhasUnlocked\x12&\n" +
	"\x0fsort_request_id\x18d \x01(\tR\rsortRequestId\x1a\x8f\x01\n" +
	"\x11UnlockedUsersInfo\x12>\n" +
	"\x05users\x18\x01 \x03(\v2(.api.users.info.types.v1.UserInfoSummaryR\x05users\x12\x1f\n" +
	"\vtotal_count\x18\x03 \x01(\rR\n" +
	"totalCount\x12\x19\n" +
	"\bhas_more\x18\x04 \x01(\bR\ahasMore\"\x98\x02\n" +
	"\x1dStoryPlayExchangeImageExample\x12L\n" +
	"\vcommon_info\x18\x01 \x01(\v2+.api.items.story.types.v1.ExampleCommonInfoR\n" +
	"commonInfo\x12R\n" +
	"\x05cases\x18\x02 \x03(\v2<.api.items.story.types.v1.StoryPlayExchangeImageExample.CaseR\x05cases\x1aU\n" +
	"\x04Case\x12,\n" +
	"\x12exchange_image_url\x18\x02 \x01(\tR\x10exchangeImageUrl\x12\x1f\n" +
	"\vai_response\x18\x03 \x01(\tR\n" +
	"aiResponse\"\xd3\x01\n" +
	"\x16StoryPlayUnmuteExample\x12L\n" +
	"\vcommon_info\x18\x01 \x01(\v2+.api.items.story.types.v1.ExampleCommonInfoR\n" +
	"commonInfo\x12K\n" +
	"\x05cases\x18\x02 \x03(\v25.api.items.story.types.v1.StoryPlayUnmuteExample.CaseR\x05cases\x1a\x1e\n" +
	"\x04Case\x12\x16\n" +
	"\x06prompt\x18\x01 \x01(\tR\x06prompt\"Q\n" +
	"\x0fPortalBasicInfo\x12\x1b\n" +
	"\tportal_id\x18\x01 \x01(\tR\bportalId\x12!\n" +
	"\fmoment_count\x18\x02 \x01(\rR\vmomentCount\"\x97\x17\n" +
	"\vStoryDetail\x12@\n" +
	"\asummary\x18\x01 \x01(\v2&.api.items.story.types.v1.StorySummaryR\asummary\x12l\n" +
	"\x15exchange_image_config\x18\x02 \x01(\v26.api.items.story.types.v1.StoryPlayExchangeImageConfigH\x00R\x13exchangeImageConfig\x12c\n" +
	"\x12turtle_soup_config\x18\x03 \x01(\v23.api.items.story.types.v1.StoryPlayTurtleSoupConfigH\x00R\x10turtleSoupConfig\x12V\n" +
	"\runmute_config\x18\x06 \x01(\v2/.api.items.story.types.v1.StoryPlayUnmuteConfigH\x00R\funmuteConfig\x12p\n" +
	"\x17turtle_soup_mass_config\x18\b \x01(\v27.api.items.story.types.v1.StoryPlayTurtleSoupMassConfigH\x00R\x14turtleSoupMassConfig\x12]\n" +
	"\x10base_play_config\x18\t \x01(\v21.api.items.story.types.v1.StoryPlayBasePlayConfigH\x00R\x0ebasePlayConfig\x12Z\n" +
	"\x0fnow_shot_config\x18\f \x01(\v20.api.items.story.types.v1.StoryPlayNowShotConfigH\x00R\rnowShotConfig\x12Y\n" +
	"\x0eroasted_config\x18\x0e \x01(\v20.api.items.story.types.v1.StoryPlayRoastedConfigH\x00R\rroastedConfig\x12V\n" +
	"\rwassup_config\x18\x10 \x01(\v2/.api.items.story.types.v1.StoryPlayWassupConfigH\x00R\fwassupConfig\x12Y\n" +
	"\x0ecapsule_config\x18\x12 \x01(\v20.api.items.story.types.v1.StoryPlayCapsuleConfigH\x00R\rcapsuleConfig\x12P\n" +
	"\vhide_config\x18\x14 \x01(\v2-.api.items.story.types.v1.StoryPlayHideConfigH\x00R\n" +
	"hideConfig\x12_\n" +
	"\x10chatproxy_config\x18\x16 \x01(\v22.api.items.story.types.v1.StoryPlayChatProxyConfigH\x00R\x0fchatproxyConfig\x12M\n" +
	"\n" +
	"who_config\x18\x18 \x01(\v2,.api.items.story.types.v1.WhoStoryPlayConfigH\x00R\twhoConfig\x12N\n" +
	"\fhaunt_config\x18\x1a \x01(\v2).api.items.story.types.v1.HauntPlayConfigH\x00R\vhauntConfig\x12I\n" +
	"\n" +
	"pin_config\x18\x1c \x01(\v2(.api.items.story.types.v1.StoryPinConfigH\x00R\tpinConfig\x12o\n" +
	"\x16exchange_image_context\x18\x04 \x01(\v27.api.items.story.types.v1.StoryPlayExchangeImageContextH\x01R\x14exchangeImageContext\x12f\n" +
	"\x13turtle_soup_context\x18\x05 \x01(\v24.api.items.story.types.v1.StoryPlayTurtleSoupContextH\x01R\x11turtleSoupContext\x12Y\n" +
	"\x0eunmute_context\x18\a \x01(\v20.api.items.story.types.v1.StoryPlayUnmuteContextH\x01R\runmuteContext\x12s\n" +
	"\x18turtle_soup_mass_context\x18\n" +
	" \x01(\v28.api.items.story.types.v1.StoryPlayTurtleSoupMassContextH\x01R\x15turtleSoupMassContext\x12`\n" +
	"\x11base_play_context\x18\v \x01(\v22.api.items.story.types.v1.StoryPlayBasePlayContextH\x01R\x0fbasePlayContext\x12]\n" +
	"\x10now_shot_context\x18\r \x01(\v21.api.items.story.types.v1.StoryPlayNowShotContextH\x01R\x0enowShotContext\x12\\\n" +
	"\x0froasted_context\x18\x0f \x01(\v21.api.items.story.types.v1.StoryPlayRoastedContextH\x01R\x0eroastedContext\x12Y\n" +
	"\x0ewassup_context\x18\x11 \x01(\v20.api.items.story.types.v1.StoryPlayWassupContextH\x01R\rwassupContext\x12\\\n" +
	"\x0fcapsule_context\x18\x13 \x01(\v21.api.items.story.types.v1.StoryPlayCapsuleContextH\x01R\x0ecapsuleContext\x12S\n" +
	"\fhide_context\x18\x15 \x01(\v2..api.items.story.types.v1.StoryPlayHideContextH\x01R\vhideContext\x12b\n" +
	"\x11chatproxy_context\x18\x17 \x01(\v23.api.items.story.types.v1.StoryPlayChatProxyContextH\x01R\x10chatproxyContext\x12P\n" +
	"\vwho_context\x18\x19 \x01(\v2-.api.items.story.types.v1.WhoStoryPlayContextH\x01R\n" +
	"whoContext\x12Q\n" +
	"\rhaunt_context\x18\x1b \x01(\v2*.api.items.story.types.v1.HauntPlayContextH\x01R\fhauntContext\x12L\n" +
	"\vpin_context\x18\x1d \x01(\v2).api.items.story.types.v1.StoryPinContextH\x01R\n" +
	"pinContext\x12/\n" +
	"\x11enabled_portal_id\x18\x1e \x01(\tH\x02R\x0fenabledPortalId\x88\x01\x01\x12Y\n" +
	"\x13haunt_boo_show_info\x18\x1f \x01(\v2*.api.items.story.types.v1.HauntBooShowInfoR\x10hauntBooShowInfo\x12U\n" +
	"\x11portal_basic_info\x18  \x01(\v2).api.items.story.types.v1.PortalBasicInfoR\x0fportalBasicInfoB\r\n" +
	"\vplay_configB\x0e\n" +
	"\fplay_contextB\x14\n" +
	"\x12_enabled_portal_id\"\xff\x01\n" +
	"#StoryExchangeImageConditionTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12&\n" +
	"\x0fcover_image_url\x18\x02 \x01(\tR\rcoverImageUrl\x12f\n" +
	"\x11default_condition\x18\x03 \x01(\v29.api.items.story.types.v1.StoryPlayExchangeImageConditionR\x10defaultCondition\x128\n" +
	"\x03typ\x18\x04 \x01(\x0e2&.api.items.story.types.v1.TemplateTypeR\x03typ\"\xc1\x01\n" +
	"\x11UnmutePromptStyle\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\f\n" +
	"\x01x\x18\x02 \x01(\tR\x01x\x12\f\n" +
	"\x01y\x18\x03 \x01(\tR\x01y\x12\x1b\n" +
	"\tfont_size\x18\x04 \x01(\rR\bfontSize\x12\x1b\n" +
	"\tfont_name\x18\x05 \x01(\tR\bfontName\x12\x14\n" +
	"\x05color\x18\x06 \x01(\tR\x05color\x12\x16\n" +
	"\x06height\x18\a \x01(\tR\x06height\x12\x14\n" +
	"\x05width\x18\b \x01(\tR\x05width\"\xa5\a\n" +
	"\x15StoryPlayUnmuteConfig\x12@\n" +
	"\x06prompt\x18\x01 \x01(\v2(.api.items.story.types.v1.AttachmentTextR\x06prompt\x12#\n" +
	"\rresource_type\x18\x02 \x01(\tR\fresourceType\x12!\n" +
	"\fresource_url\x18\x03 \x01(\tR\vresourceUrl\x12*\n" +
	"\x11end_resource_type\x18\x04 \x01(\tR\x0fendResourceType\x12(\n" +
	"\x10end_resource_url\x18\x05 \x01(\tR\x0eendResourceUrl\x12p\n" +
	"\x13custom_ai_responses\x18\x06 \x03(\<EMAIL>\x11customAiResponses\x12\"\n" +
	"\rmax_try_count\x18\a \x01(\rR\vmaxTryCount\x12(\n" +
	"\rthumbnail_url\x18\b \x01(\tH\x00R\fthumbnailUrl\x88\x01\x01\x12/\n" +
	"\x11end_thumbnail_url\x18\t \x01(\tH\x01R\x0fendThumbnailUrl\x88\x01\x01\x12\x1c\n" +
	"\tintention\x18\n" +
	" \x01(\tR\tintention\x12X\n" +
	"\x12common_play_config\x18\v \x01(\v2*.api.items.story.types.v1.CommonPlayConfigR\x10commonPlayConfig\x12Z\n" +
	"\x13moment_create_attrs\x18\f \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\x1a^\n" +
	"\x10CustomAiResponse\x12)\n" +
	"\x10rule_description\x18\x01 \x01(\tR\x0fruleDescription\x12\x1f\n" +
	"\vrule_result\x18\x02 \x01(\tR\n" +
	"ruleResult\"_\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02B\x10\n" +
	"\x0e_thumbnail_urlB\x14\n" +
	"\x12_end_thumbnail_url\"u\n" +
	" CommonStoryPlayConditionTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12A\n" +
	"\tcondition\x18\x02 \x01(\v2#.api.items.story.types.v1.ConditionR\tcondition\"\xed\x01\n" +
	" StoryPlayTurtleConditionTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x18\n" +
	"\asummary\x18\x02 \x01(\tR\asummary\x12&\n" +
	"\x0fcover_image_url\x18\x03 \x01(\tR\rcoverImageUrl\x12\x18\n" +
	"\acaption\x18\x04 \x01(\tR\acaption\x12#\n" +
	"\rintent_prompt\x18\x05 \x01(\tR\fintentPrompt\x128\n" +
	"\x03typ\x18\x06 \x01(\x0e2&.api.items.story.types.v1.TemplateTypeR\x03typ\"\xf5\x01\n" +
	" StoryPlayUnmuteConditionTemplate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12_\n" +
	"\x11default_condition\x18\x02 \x01(\v22.api.items.story.types.v1.StoryPlayUnmuteConditionR\x10defaultCondition\x128\n" +
	"\x03typ\x18\x03 \x01(\x0e2&.api.items.story.types.v1.TemplateTypeR\x03typ\x12&\n" +
	"\x0fcover_image_url\x18\x04 \x01(\tR\rcoverImageUrl\"2\n" +
	"\x18StoryPlayUnmuteCondition\x12\x16\n" +
	"\x06prompt\x18\x01 \x01(\tR\x06prompt\"\x84\x04\n" +
	"\x1fStoryPlayExchangeImageCondition\x12\x12\n" +
	"\x04tips\x18\x01 \x01(\tR\x04tips\x12%\n" +
	"\x0epositive_guide\x18\x02 \x01(\tR\rpositiveGuide\x12.\n" +
	"\x13positive_image_urls\x18\x03 \x03(\tR\x11positiveImageUrls\x12.\n" +
	"\x13positive_image_keys\x18\x04 \x03(\tR\x11positiveImageKeys\x12.\n" +
	"\x13partial_match_guide\x18\x05 \x01(\tR\x11partialMatchGuide\x127\n" +
	"\x18partial_match_image_urls\x18\x06 \x03(\tR\x15partialMatchImageUrls\x127\n" +
	"\x18partial_match_image_keys\x18\a \x03(\tR\x15partialMatchImageKeys\x12%\n" +
	"\x0enegative_guide\x18\b \x01(\tR\rnegativeGuide\x12.\n" +
	"\x13negative_image_urls\x18\t \x03(\tR\x11negativeImageUrls\x12.\n" +
	"\x13negative_image_keys\x18\n" +
	" \x03(\tR\x11negativeImageKeys\x12\x1d\n" +
	"\n" +
	"llm_prompt\x18\v \x01(\tR\tllmPrompt\"\x96\x01\n" +
	"\x16StoryPlayUnmuteContext\x12\x1f\n" +
	"\vis_finished\x18\x01 \x01(\bR\n" +
	"isFinished\x12\x1f\n" +
	"\vai_response\x18\x02 \x01(\tR\n" +
	"aiResponse\x12\x1b\n" +
	"\ttry_count\x18\x03 \x01(\rR\btryCount\x12\x1d\n" +
	"\n" +
	"audio_keys\x18\x04 \x03(\tR\taudioKeys\"\xde\x06\n" +
	"\x1dStoryPlayExchangeImageContext\x12&\n" +
	"\x0fcurrent_node_id\x18\x01 \x01(\tR\rcurrentNodeId\x12x\n" +
	"\x11current_try_count\x18\x02 \x03(\v2L.api.items.story.types.v1.StoryPlayExchangeImageContext.CurrentTryCountEntryR\x0fcurrentTryCount\x12\x8d\x01\n" +
	"\x18current_success_progress\x18\x03 \x03(\v2S.api.items.story.types.v1.StoryPlayExchangeImageContext.CurrentSuccessProgressEntryR\x16currentSuccessProgress\x12\x1f\n" +
	"\vis_finished\x18\x04 \x01(\bR\n" +
	"isFinished\x12\x86\x01\n" +
	"\x18user_exchange_image_urls\x18\x05 \x03(\v2M.api.items.story.types.v1.StoryPlayExchangeImageContext.UserExchangeImageUrlsR\x15userExchangeImageUrls\x12\x1b\n" +
	"\ttry_count\x18\x06 \x01(\rR\btryCount\x121\n" +
	"\x15user_trial_image_urls\x18\a \x03(\tR\x12userTrialImageUrls\x121\n" +
	"\x15user_trial_image_keys\x18\b \x03(\tR\x12userTrialImageKeys\x1aB\n" +
	"\x14CurrentTryCountEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\rR\x05value:\x028\x01\x1aI\n" +
	"\x1bCurrentSuccessProgressEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\rR\x05value:\x028\x01\x1aO\n" +
	"\x15UserExchangeImageUrls\x12\x17\n" +
	"\anode_id\x18\x01 \x01(\tR\x06nodeId\x12\x1d\n" +
	"\n" +
	"image_urls\x18\x02 \x03(\tR\timageUrls\"\xa0\x06\n" +
	"\x1cStoryPlayExchangeImageConfig\x12\x1b\n" +
	"\tplay_mode\x18\x01 \x01(\tR\bplayMode\x12Q\n" +
	"\x05nodes\x18\x02 \x03(\v2;.api.items.story.types.v1.StoryPlayExchangeImageConfig.NodeR\x05nodes\x12X\n" +
	"\x12common_play_config\x18\x03 \x01(\v2*.api.items.story.types.v1.CommonPlayConfigR\x10commonPlayConfig\x12Z\n" +
	"\x13moment_create_attrs\x18\x04 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\x1a\xd9\x03\n" +
	"\x04Node\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12(\n" +
	"\rthumbnail_url\x18\x02 \x01(\tH\x00R\fthumbnailUrl\x88\x01\x01\x12#\n" +
	"\rresource_type\x18\x03 \x01(\tR\fresourceType\x12!\n" +
	"\fresource_url\x18\x04 \x01(\tR\vresourceUrl\x12W\n" +
	"\tcondition\x18\x06 \x01(\v29.api.items.story.types.v1.StoryPlayExchangeImageConditionR\tcondition\x12&\n" +
	"\x0fallow_use_album\x18\a \x01(\bR\rallowUseAlbum\x12\"\n" +
	"\rmax_try_count\x18\b \x01(\rR\vmaxTryCount\x127\n" +
	"\x18need_create_diy_template\x18\t \x01(\bR\x15needCreateDiyTemplate\"_\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02B\x10\n" +
	"\x0e_thumbnail_url*\xa2\x01\n" +
	"\vStoryStatus\x12\x1c\n" +
	"\x18STORY_STATUS_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16STORY_STATUS_PUBLISHED\x10\x01\x12\x18\n" +
	"\x14STORY_STATUS_DELETED\x10\x02\x12\x1b\n" +
	"\x17STORY_STATUS_GENERATING\x10\x03\x12\"\n" +
	"\x1eSTORY_STATUS_GENERATION_FAILED\x10\x04*\xd3\x03\n" +
	"\rStoryPlayType\x12\x1f\n" +
	"\x1bSTORY_PLAY_TYPE_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eSTORY_PLAY_TYPE_EXCHANGE_IMAGE\x10\x01\x12\x1f\n" +
	"\x1bSTORY_PLAY_TYPE_TURTLE_SOUP\x10\x02\x12\x1a\n" +
	"\x16STORY_PLAY_TYPE_UNMUTE\x10\x03\x12$\n" +
	" STORY_PLAY_TYPE_TURTLE_SOUP_MASS\x10\x04\x12\x1d\n" +
	"\x19STORY_PLAY_TYPE_BASE_PLAY\x10\x05\x12\x1c\n" +
	"\x18STORY_PLAY_TYPE_NOW_SHOT\x10\x06\x12\x1b\n" +
	"\x17STORY_PLAY_TYPE_ROASTED\x10\a\x12\x1b\n" +
	"\x17STORY_PLAY_TYPE_CAPSULE\x10\t\x12\x18\n" +
	"\x14STORY_PLAY_TYPE_HIDE\x10\n" +
	"\x12\x1d\n" +
	"\x19STORY_PLAY_TYPE_CHATPROXY\x10\v\x12\x17\n" +
	"\x13STORY_PLAY_TYPE_WHO\x10\f\x12\x1d\n" +
	"\x19STORY_PLAY_TYPE_WASSUP_V2\x10\r\x12\x19\n" +
	"\x15STORY_PLAY_TYPE_HAUNT\x10\x0e\x12\x17\n" +
	"\x13STORY_PLAY_TYPE_PIN\x10\x0f*\xd0\x01\n" +
	"\x18ExchangeImageMatchStatus\x12+\n" +
	"'EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED\x10\x00\x12+\n" +
	"'EXCHANGE_IMAGE_MATCH_STATUS_NOT_MATCHED\x10\x01\x121\n" +
	"-EXCHANGE_IMAGE_MATCH_STATUS_PARTIALLY_MATCHED\x10\x02\x12'\n" +
	"#EXCHANGE_IMAGE_MATCH_STATUS_MATCHED\x10\x03*\xa9\x01\n" +
	"\x11UnmuteMatchStatus\x12#\n" +
	"\x1fUNMUTE_MATCH_STATUS_UNSPECIFIED\x10\x00\x12#\n" +
	"\x1fUNMUTE_MATCH_STATUS_NOT_MATCHED\x10\x01\x12)\n" +
	"%UNMUTE_MATCH_STATUS_PARTIALLY_MATCHED\x10\x02\x12\x1f\n" +
	"\x1bUNMUTE_MATCH_STATUS_MATCHED\x10\x03*c\n" +
	"\fTemplateType\x12\x1d\n" +
	"\x19TEMPLATE_TYPE_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14TEMPLATE_TYPE_SYSTEM\x10\x01\x12\x1a\n" +
	"\x16TEMPLATE_TYPE_USER_DIY\x10\x02*\x83\x01\n" +
	"\rContextStatus\x12\x1e\n" +
	"\x1aCONTEXT_STATUS_UNSPECIFIED\x10\x00\x12\x19\n" +
	"\x15CONTEXT_STATUS_NORMAL\x10\x01\x12\x1a\n" +
	"\x16CONTEXT_STATUS_DELETED\x10\x02\x12\x1b\n" +
	"\x17CONTEXT_STATUS_UNLOCKED\x10\x03*]\n" +
	"\x15ExchangeImagePlayMode\x12\x19\n" +
	"\x15PLAY_MODE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fPLAY_MODE_MASSE\x10\x01\x12\x14\n" +
	"\x10PLAY_MODE_LINEAR\x10\x02B9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_types_proto_rawDesc), len(file_api_items_story_types_v1_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_types_proto_rawDescData
}

var file_api_items_story_types_v1_types_proto_enumTypes = make([]protoimpl.EnumInfo, 9)
var file_api_items_story_types_v1_types_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_api_items_story_types_v1_types_proto_goTypes = []any{
	(StoryStatus)(0),                                    // 0: api.items.story.types.v1.StoryStatus
	(StoryPlayType)(0),                                  // 1: api.items.story.types.v1.StoryPlayType
	(ExchangeImageMatchStatus)(0),                       // 2: api.items.story.types.v1.ExchangeImageMatchStatus
	(UnmuteMatchStatus)(0),                              // 3: api.items.story.types.v1.UnmuteMatchStatus
	(TemplateType)(0),                                   // 4: api.items.story.types.v1.TemplateType
	(ContextStatus)(0),                                  // 5: api.items.story.types.v1.ContextStatus
	(ExchangeImagePlayMode)(0),                          // 6: api.items.story.types.v1.ExchangeImagePlayMode
	(StoryPlayUnmuteConfig_ResourceType)(0),             // 7: api.items.story.types.v1.StoryPlayUnmuteConfig.ResourceType
	(StoryPlayExchangeImageConfig_Node_ResourceType)(0), // 8: api.items.story.types.v1.StoryPlayExchangeImageConfig.Node.ResourceType
	(*StoryStats)(nil),                                  // 9: api.items.story.types.v1.StoryStats
	(*StorySummary)(nil),                                // 10: api.items.story.types.v1.StorySummary
	(*StoryPlayExchangeImageExample)(nil),               // 11: api.items.story.types.v1.StoryPlayExchangeImageExample
	(*StoryPlayUnmuteExample)(nil),                      // 12: api.items.story.types.v1.StoryPlayUnmuteExample
	(*PortalBasicInfo)(nil),                             // 13: api.items.story.types.v1.PortalBasicInfo
	(*StoryDetail)(nil),                                 // 14: api.items.story.types.v1.StoryDetail
	(*StoryExchangeImageConditionTemplate)(nil),         // 15: api.items.story.types.v1.StoryExchangeImageConditionTemplate
	(*UnmutePromptStyle)(nil),                           // 16: api.items.story.types.v1.UnmutePromptStyle
	(*StoryPlayUnmuteConfig)(nil),                       // 17: api.items.story.types.v1.StoryPlayUnmuteConfig
	(*CommonStoryPlayConditionTemplate)(nil),            // 18: api.items.story.types.v1.CommonStoryPlayConditionTemplate
	(*StoryPlayTurtleConditionTemplate)(nil),            // 19: api.items.story.types.v1.StoryPlayTurtleConditionTemplate
	(*StoryPlayUnmuteConditionTemplate)(nil),            // 20: api.items.story.types.v1.StoryPlayUnmuteConditionTemplate
	(*StoryPlayUnmuteCondition)(nil),                    // 21: api.items.story.types.v1.StoryPlayUnmuteCondition
	(*StoryPlayExchangeImageCondition)(nil),             // 22: api.items.story.types.v1.StoryPlayExchangeImageCondition
	(*StoryPlayUnmuteContext)(nil),                      // 23: api.items.story.types.v1.StoryPlayUnmuteContext
	(*StoryPlayExchangeImageContext)(nil),               // 24: api.items.story.types.v1.StoryPlayExchangeImageContext
	(*StoryPlayExchangeImageConfig)(nil),                // 25: api.items.story.types.v1.StoryPlayExchangeImageConfig
	(*StorySummary_UnlockedUsersInfo)(nil),              // 26: api.items.story.types.v1.StorySummary.UnlockedUsersInfo
	(*StoryPlayExchangeImageExample_Case)(nil),          // 27: api.items.story.types.v1.StoryPlayExchangeImageExample.Case
	(*StoryPlayUnmuteExample_Case)(nil),                 // 28: api.items.story.types.v1.StoryPlayUnmuteExample.Case
	(*StoryPlayUnmuteConfig_CustomAiResponse)(nil),      // 29: api.items.story.types.v1.StoryPlayUnmuteConfig.CustomAiResponse
	nil, // 30: api.items.story.types.v1.StoryPlayExchangeImageContext.CurrentTryCountEntry
	nil, // 31: api.items.story.types.v1.StoryPlayExchangeImageContext.CurrentSuccessProgressEntry
	(*StoryPlayExchangeImageContext_UserExchangeImageUrls)(nil), // 32: api.items.story.types.v1.StoryPlayExchangeImageContext.UserExchangeImageUrls
	(*StoryPlayExchangeImageConfig_Node)(nil),                   // 33: api.items.story.types.v1.StoryPlayExchangeImageConfig.Node
	(*v1.ReactionMadeStatSummary)(nil),                          // 34: api.items.story.reaction.types.v1.ReactionMadeStatSummary
	(*v11.UserInfoSummary)(nil),                                 // 35: api.users.info.types.v1.UserInfoSummary
	(*StoryPlayTurtleSoupExample)(nil),                          // 36: api.items.story.types.v1.StoryPlayTurtleSoupExample
	(*StoryPlayTurtleSoupMassExample)(nil),                      // 37: api.items.story.types.v1.StoryPlayTurtleSoupMassExample
	(*StoryPlayBasePlayExample)(nil),                            // 38: api.items.story.types.v1.StoryPlayBasePlayExample
	(*PrivacySetting)(nil),                                      // 39: api.items.story.types.v1.PrivacySetting
	(*StoryPlayNowShotExample)(nil),                             // 40: api.items.story.types.v1.StoryPlayNowShotExample
	(*StoryPlayRoastedExample)(nil),                             // 41: api.items.story.types.v1.StoryPlayRoastedExample
	(*StoryPlayCapsuleExample)(nil),                             // 42: api.items.story.types.v1.StoryPlayCapsuleExample
	(*v1.Reaction)(nil),                                         // 43: api.items.story.reaction.types.v1.Reaction
	(*ExampleCommonInfo)(nil),                                   // 44: api.items.story.types.v1.ExampleCommonInfo
	(*StoryPlayTurtleSoupConfig)(nil),                           // 45: api.items.story.types.v1.StoryPlayTurtleSoupConfig
	(*StoryPlayTurtleSoupMassConfig)(nil),                       // 46: api.items.story.types.v1.StoryPlayTurtleSoupMassConfig
	(*StoryPlayBasePlayConfig)(nil),                             // 47: api.items.story.types.v1.StoryPlayBasePlayConfig
	(*StoryPlayNowShotConfig)(nil),                              // 48: api.items.story.types.v1.StoryPlayNowShotConfig
	(*StoryPlayRoastedConfig)(nil),                              // 49: api.items.story.types.v1.StoryPlayRoastedConfig
	(*StoryPlayWassupConfig)(nil),                               // 50: api.items.story.types.v1.StoryPlayWassupConfig
	(*StoryPlayCapsuleConfig)(nil),                              // 51: api.items.story.types.v1.StoryPlayCapsuleConfig
	(*StoryPlayHideConfig)(nil),                                 // 52: api.items.story.types.v1.StoryPlayHideConfig
	(*StoryPlayChatProxyConfig)(nil),                            // 53: api.items.story.types.v1.StoryPlayChatProxyConfig
	(*WhoStoryPlayConfig)(nil),                                  // 54: api.items.story.types.v1.WhoStoryPlayConfig
	(*HauntPlayConfig)(nil),                                     // 55: api.items.story.types.v1.HauntPlayConfig
	(*StoryPinConfig)(nil),                                      // 56: api.items.story.types.v1.StoryPinConfig
	(*StoryPlayTurtleSoupContext)(nil),                          // 57: api.items.story.types.v1.StoryPlayTurtleSoupContext
	(*StoryPlayTurtleSoupMassContext)(nil),                      // 58: api.items.story.types.v1.StoryPlayTurtleSoupMassContext
	(*StoryPlayBasePlayContext)(nil),                            // 59: api.items.story.types.v1.StoryPlayBasePlayContext
	(*StoryPlayNowShotContext)(nil),                             // 60: api.items.story.types.v1.StoryPlayNowShotContext
	(*StoryPlayRoastedContext)(nil),                             // 61: api.items.story.types.v1.StoryPlayRoastedContext
	(*StoryPlayWassupContext)(nil),                              // 62: api.items.story.types.v1.StoryPlayWassupContext
	(*StoryPlayCapsuleContext)(nil),                             // 63: api.items.story.types.v1.StoryPlayCapsuleContext
	(*StoryPlayHideContext)(nil),                                // 64: api.items.story.types.v1.StoryPlayHideContext
	(*StoryPlayChatProxyContext)(nil),                           // 65: api.items.story.types.v1.StoryPlayChatProxyContext
	(*WhoStoryPlayContext)(nil),                                 // 66: api.items.story.types.v1.WhoStoryPlayContext
	(*HauntPlayContext)(nil),                                    // 67: api.items.story.types.v1.HauntPlayContext
	(*StoryPinContext)(nil),                                     // 68: api.items.story.types.v1.StoryPinContext
	(*HauntBooShowInfo)(nil),                                    // 69: api.items.story.types.v1.HauntBooShowInfo
	(*AttachmentText)(nil),                                      // 70: api.items.story.types.v1.AttachmentText
	(*CommonPlayConfig)(nil),                                    // 71: api.items.story.types.v1.CommonPlayConfig
	(*MomentCreateAttr)(nil),                                    // 72: api.items.story.types.v1.MomentCreateAttr
	(*Condition)(nil),                                           // 73: api.items.story.types.v1.Condition
}
var file_api_items_story_types_v1_types_proto_depIdxs = []int32{
	34, // 0: api.items.story.types.v1.StoryStats.reaction_made_stat_summary:type_name -> api.items.story.reaction.types.v1.ReactionMadeStatSummary
	35, // 1: api.items.story.types.v1.StorySummary.author:type_name -> api.users.info.types.v1.UserInfoSummary
	0,  // 2: api.items.story.types.v1.StorySummary.status:type_name -> api.items.story.types.v1.StoryStatus
	11, // 3: api.items.story.types.v1.StorySummary.exchange_image_example:type_name -> api.items.story.types.v1.StoryPlayExchangeImageExample
	36, // 4: api.items.story.types.v1.StorySummary.turtle_soup_example:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupExample
	12, // 5: api.items.story.types.v1.StorySummary.unmute_example:type_name -> api.items.story.types.v1.StoryPlayUnmuteExample
	37, // 6: api.items.story.types.v1.StorySummary.turtle_soup_mass_example:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupMassExample
	38, // 7: api.items.story.types.v1.StorySummary.base_play_example:type_name -> api.items.story.types.v1.StoryPlayBasePlayExample
	39, // 8: api.items.story.types.v1.StorySummary.privacy_setting:type_name -> api.items.story.types.v1.PrivacySetting
	40, // 9: api.items.story.types.v1.StorySummary.now_shot_play_example:type_name -> api.items.story.types.v1.StoryPlayNowShotExample
	41, // 10: api.items.story.types.v1.StorySummary.roasted_example:type_name -> api.items.story.types.v1.StoryPlayRoastedExample
	42, // 11: api.items.story.types.v1.StorySummary.capsule_example:type_name -> api.items.story.types.v1.StoryPlayCapsuleExample
	9,  // 12: api.items.story.types.v1.StorySummary.stats:type_name -> api.items.story.types.v1.StoryStats
	43, // 13: api.items.story.types.v1.StorySummary.login_user_made_reactions:type_name -> api.items.story.reaction.types.v1.Reaction
	26, // 14: api.items.story.types.v1.StorySummary.unlocked_users:type_name -> api.items.story.types.v1.StorySummary.UnlockedUsersInfo
	44, // 15: api.items.story.types.v1.StoryPlayExchangeImageExample.common_info:type_name -> api.items.story.types.v1.ExampleCommonInfo
	27, // 16: api.items.story.types.v1.StoryPlayExchangeImageExample.cases:type_name -> api.items.story.types.v1.StoryPlayExchangeImageExample.Case
	44, // 17: api.items.story.types.v1.StoryPlayUnmuteExample.common_info:type_name -> api.items.story.types.v1.ExampleCommonInfo
	28, // 18: api.items.story.types.v1.StoryPlayUnmuteExample.cases:type_name -> api.items.story.types.v1.StoryPlayUnmuteExample.Case
	10, // 19: api.items.story.types.v1.StoryDetail.summary:type_name -> api.items.story.types.v1.StorySummary
	25, // 20: api.items.story.types.v1.StoryDetail.exchange_image_config:type_name -> api.items.story.types.v1.StoryPlayExchangeImageConfig
	45, // 21: api.items.story.types.v1.StoryDetail.turtle_soup_config:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupConfig
	17, // 22: api.items.story.types.v1.StoryDetail.unmute_config:type_name -> api.items.story.types.v1.StoryPlayUnmuteConfig
	46, // 23: api.items.story.types.v1.StoryDetail.turtle_soup_mass_config:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupMassConfig
	47, // 24: api.items.story.types.v1.StoryDetail.base_play_config:type_name -> api.items.story.types.v1.StoryPlayBasePlayConfig
	48, // 25: api.items.story.types.v1.StoryDetail.now_shot_config:type_name -> api.items.story.types.v1.StoryPlayNowShotConfig
	49, // 26: api.items.story.types.v1.StoryDetail.roasted_config:type_name -> api.items.story.types.v1.StoryPlayRoastedConfig
	50, // 27: api.items.story.types.v1.StoryDetail.wassup_config:type_name -> api.items.story.types.v1.StoryPlayWassupConfig
	51, // 28: api.items.story.types.v1.StoryDetail.capsule_config:type_name -> api.items.story.types.v1.StoryPlayCapsuleConfig
	52, // 29: api.items.story.types.v1.StoryDetail.hide_config:type_name -> api.items.story.types.v1.StoryPlayHideConfig
	53, // 30: api.items.story.types.v1.StoryDetail.chatproxy_config:type_name -> api.items.story.types.v1.StoryPlayChatProxyConfig
	54, // 31: api.items.story.types.v1.StoryDetail.who_config:type_name -> api.items.story.types.v1.WhoStoryPlayConfig
	55, // 32: api.items.story.types.v1.StoryDetail.haunt_config:type_name -> api.items.story.types.v1.HauntPlayConfig
	56, // 33: api.items.story.types.v1.StoryDetail.pin_config:type_name -> api.items.story.types.v1.StoryPinConfig
	24, // 34: api.items.story.types.v1.StoryDetail.exchange_image_context:type_name -> api.items.story.types.v1.StoryPlayExchangeImageContext
	57, // 35: api.items.story.types.v1.StoryDetail.turtle_soup_context:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupContext
	23, // 36: api.items.story.types.v1.StoryDetail.unmute_context:type_name -> api.items.story.types.v1.StoryPlayUnmuteContext
	58, // 37: api.items.story.types.v1.StoryDetail.turtle_soup_mass_context:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupMassContext
	59, // 38: api.items.story.types.v1.StoryDetail.base_play_context:type_name -> api.items.story.types.v1.StoryPlayBasePlayContext
	60, // 39: api.items.story.types.v1.StoryDetail.now_shot_context:type_name -> api.items.story.types.v1.StoryPlayNowShotContext
	61, // 40: api.items.story.types.v1.StoryDetail.roasted_context:type_name -> api.items.story.types.v1.StoryPlayRoastedContext
	62, // 41: api.items.story.types.v1.StoryDetail.wassup_context:type_name -> api.items.story.types.v1.StoryPlayWassupContext
	63, // 42: api.items.story.types.v1.StoryDetail.capsule_context:type_name -> api.items.story.types.v1.StoryPlayCapsuleContext
	64, // 43: api.items.story.types.v1.StoryDetail.hide_context:type_name -> api.items.story.types.v1.StoryPlayHideContext
	65, // 44: api.items.story.types.v1.StoryDetail.chatproxy_context:type_name -> api.items.story.types.v1.StoryPlayChatProxyContext
	66, // 45: api.items.story.types.v1.StoryDetail.who_context:type_name -> api.items.story.types.v1.WhoStoryPlayContext
	67, // 46: api.items.story.types.v1.StoryDetail.haunt_context:type_name -> api.items.story.types.v1.HauntPlayContext
	68, // 47: api.items.story.types.v1.StoryDetail.pin_context:type_name -> api.items.story.types.v1.StoryPinContext
	69, // 48: api.items.story.types.v1.StoryDetail.haunt_boo_show_info:type_name -> api.items.story.types.v1.HauntBooShowInfo
	13, // 49: api.items.story.types.v1.StoryDetail.portal_basic_info:type_name -> api.items.story.types.v1.PortalBasicInfo
	22, // 50: api.items.story.types.v1.StoryExchangeImageConditionTemplate.default_condition:type_name -> api.items.story.types.v1.StoryPlayExchangeImageCondition
	4,  // 51: api.items.story.types.v1.StoryExchangeImageConditionTemplate.typ:type_name -> api.items.story.types.v1.TemplateType
	70, // 52: api.items.story.types.v1.StoryPlayUnmuteConfig.prompt:type_name -> api.items.story.types.v1.AttachmentText
	29, // 53: api.items.story.types.v1.StoryPlayUnmuteConfig.custom_ai_responses:type_name -> api.items.story.types.v1.StoryPlayUnmuteConfig.CustomAiResponse
	71, // 54: api.items.story.types.v1.StoryPlayUnmuteConfig.common_play_config:type_name -> api.items.story.types.v1.CommonPlayConfig
	72, // 55: api.items.story.types.v1.StoryPlayUnmuteConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	73, // 56: api.items.story.types.v1.CommonStoryPlayConditionTemplate.condition:type_name -> api.items.story.types.v1.Condition
	4,  // 57: api.items.story.types.v1.StoryPlayTurtleConditionTemplate.typ:type_name -> api.items.story.types.v1.TemplateType
	21, // 58: api.items.story.types.v1.StoryPlayUnmuteConditionTemplate.default_condition:type_name -> api.items.story.types.v1.StoryPlayUnmuteCondition
	4,  // 59: api.items.story.types.v1.StoryPlayUnmuteConditionTemplate.typ:type_name -> api.items.story.types.v1.TemplateType
	30, // 60: api.items.story.types.v1.StoryPlayExchangeImageContext.current_try_count:type_name -> api.items.story.types.v1.StoryPlayExchangeImageContext.CurrentTryCountEntry
	31, // 61: api.items.story.types.v1.StoryPlayExchangeImageContext.current_success_progress:type_name -> api.items.story.types.v1.StoryPlayExchangeImageContext.CurrentSuccessProgressEntry
	32, // 62: api.items.story.types.v1.StoryPlayExchangeImageContext.user_exchange_image_urls:type_name -> api.items.story.types.v1.StoryPlayExchangeImageContext.UserExchangeImageUrls
	33, // 63: api.items.story.types.v1.StoryPlayExchangeImageConfig.nodes:type_name -> api.items.story.types.v1.StoryPlayExchangeImageConfig.Node
	71, // 64: api.items.story.types.v1.StoryPlayExchangeImageConfig.common_play_config:type_name -> api.items.story.types.v1.CommonPlayConfig
	72, // 65: api.items.story.types.v1.StoryPlayExchangeImageConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	35, // 66: api.items.story.types.v1.StorySummary.UnlockedUsersInfo.users:type_name -> api.users.info.types.v1.UserInfoSummary
	22, // 67: api.items.story.types.v1.StoryPlayExchangeImageConfig.Node.condition:type_name -> api.items.story.types.v1.StoryPlayExchangeImageCondition
	68, // [68:68] is the sub-list for method output_type
	68, // [68:68] is the sub-list for method input_type
	68, // [68:68] is the sub-list for extension type_name
	68, // [68:68] is the sub-list for extension extendee
	0,  // [0:68] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_types_proto_init() }
func file_api_items_story_types_v1_types_proto_init() {
	if File_api_items_story_types_v1_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_turtle_soup_types_proto_init()
	file_api_items_story_types_v1_now_shot_types_proto_init()
	file_api_items_story_types_v1_base_types_proto_init()
	file_api_items_story_types_v1_base_play_story_types_proto_init()
	file_api_items_story_types_v1_privacy_setting_proto_init()
	file_api_items_story_types_v1_roasted_types_proto_init()
	file_api_items_story_types_v1_wassup_types_proto_init()
	file_api_items_story_types_v1_capsule_types_proto_init()
	file_api_items_story_types_v1_hide_types_proto_init()
	file_api_items_story_types_v1_chatproxy_types_proto_init()
	file_api_items_story_types_v1_who_types_proto_init()
	file_api_items_story_types_v1_haunt_types_proto_init()
	file_api_items_story_types_v1_pin_types_proto_init()
	file_api_items_story_types_v1_types_proto_msgTypes[5].OneofWrappers = []any{
		(*StoryDetail_ExchangeImageConfig)(nil),
		(*StoryDetail_TurtleSoupConfig)(nil),
		(*StoryDetail_UnmuteConfig)(nil),
		(*StoryDetail_TurtleSoupMassConfig)(nil),
		(*StoryDetail_BasePlayConfig)(nil),
		(*StoryDetail_NowShotConfig)(nil),
		(*StoryDetail_RoastedConfig)(nil),
		(*StoryDetail_WassupConfig)(nil),
		(*StoryDetail_CapsuleConfig)(nil),
		(*StoryDetail_HideConfig)(nil),
		(*StoryDetail_ChatproxyConfig)(nil),
		(*StoryDetail_WhoConfig)(nil),
		(*StoryDetail_HauntConfig)(nil),
		(*StoryDetail_PinConfig)(nil),
		(*StoryDetail_ExchangeImageContext)(nil),
		(*StoryDetail_TurtleSoupContext)(nil),
		(*StoryDetail_UnmuteContext)(nil),
		(*StoryDetail_TurtleSoupMassContext)(nil),
		(*StoryDetail_BasePlayContext)(nil),
		(*StoryDetail_NowShotContext)(nil),
		(*StoryDetail_RoastedContext)(nil),
		(*StoryDetail_WassupContext)(nil),
		(*StoryDetail_CapsuleContext)(nil),
		(*StoryDetail_HideContext)(nil),
		(*StoryDetail_ChatproxyContext)(nil),
		(*StoryDetail_WhoContext)(nil),
		(*StoryDetail_HauntContext)(nil),
		(*StoryDetail_PinContext)(nil),
	}
	file_api_items_story_types_v1_types_proto_msgTypes[8].OneofWrappers = []any{}
	file_api_items_story_types_v1_types_proto_msgTypes[24].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_types_proto_rawDesc), len(file_api_items_story_types_v1_types_proto_rawDesc)),
			NumEnums:      9,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_types_proto = out.File
	file_api_items_story_types_v1_types_proto_goTypes = nil
	file_api_items_story_types_v1_types_proto_depIdxs = nil
}
