syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/items/story/types/v1/base_types.proto";
import "validate/validate.proto";
import "api/resource/types/v1/types.proto";

message RoastedQuestion {
	string question = 1;
	// 创建的时候不需要传此值
	string tts_audio_url = 2;
	// 创建的时候必传
	string tts_audio_key = 3;
	// 此字段暂时对端上无作用，消费时带回来即可
	string thinking = 4;
	// 词粒度信息
	repeated Word words = 6;
}

message RoastedQuestionWithUserAnswer {
	RoastedQuestion question = 1;
	// 用户回答的音频资源
	string user_voice_key = 2;
}	
message StoryPlayRoastedTopic {
	// 纯 bot 播报，用于开场白和结束语
	message BotAnnouncement {
		// 播报的内容
		string content = 1;
		// 播报的音频资源, 创建的时候必须传此值
		string tts_audio_key = 2;
		// 播报的音频资源 url，创建的时候不需要传此值
		string tts_audio_url = 3;
	}
	// 纯 bot 播报，用于开场白
	BotAnnouncement greeting = 1;
	// 纯 bot 播报，用于结束语
	BotAnnouncement ending = 2;
	// 问题列表，至少有一个问题
	repeated RoastedQuestion questions = 3;
	// 最大问题数, 按60%：20%：20%返回3:4:5
	uint32 max_followup = 4;
}


message StoryPlayRoastedConfig {
	StoryPlayRoastedTopic topic = 1[(validate.rules).message = {required: true}];
	// 节点资源
	api.resource.types.v1.Resource resource = 2;
	// cover resource
	api.resource.types.v1.Resource cover = 3;
	// moment create attrs
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 4;
}

message StoryPlayRoastedContext {
	// 是否消费过此 story
	bool is_consumed = 1;
}

message StoryPlayRoastedExample {
	StoryPlayRoastedTopic topic = 1;
}