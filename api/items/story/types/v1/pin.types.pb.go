// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/pin.types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/resource/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PinEmojiArear struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             string                 `protobuf:"bytes,1,opt,name=x,proto3" json:"x,omitempty"`
	Y             string                 `protobuf:"bytes,2,opt,name=y,proto3" json:"y,omitempty"`
	Width         string                 `protobuf:"bytes,3,opt,name=width,proto3" json:"width,omitempty"`
	Height        string                 `protobuf:"bytes,4,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PinEmojiArear) Reset() {
	*x = PinEmojiArear{}
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PinEmojiArear) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinEmojiArear) ProtoMessage() {}

func (x *PinEmojiArear) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinEmojiArear.ProtoReflect.Descriptor instead.
func (*PinEmojiArear) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_pin_types_proto_rawDescGZIP(), []int{0}
}

func (x *PinEmojiArear) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *PinEmojiArear) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

func (x *PinEmojiArear) GetWidth() string {
	if x != nil {
		return x.Width
	}
	return ""
}

func (x *PinEmojiArear) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

type PinEmojiResource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Data:
	//
	//	*PinEmojiResource_GeneratedEmojiResource
	//	*PinEmojiResource_DefaultEmoji
	Data          isPinEmojiResource_Data `protobuf_oneof:"data"`
	Area          *PinEmojiArear          `protobuf:"bytes,3,opt,name=area,proto3" json:"area,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PinEmojiResource) Reset() {
	*x = PinEmojiResource{}
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PinEmojiResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinEmojiResource) ProtoMessage() {}

func (x *PinEmojiResource) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinEmojiResource.ProtoReflect.Descriptor instead.
func (*PinEmojiResource) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_pin_types_proto_rawDescGZIP(), []int{1}
}

func (x *PinEmojiResource) GetData() isPinEmojiResource_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *PinEmojiResource) GetGeneratedEmojiResource() *v1.Resource {
	if x != nil {
		if x, ok := x.Data.(*PinEmojiResource_GeneratedEmojiResource); ok {
			return x.GeneratedEmojiResource
		}
	}
	return nil
}

func (x *PinEmojiResource) GetDefaultEmoji() string {
	if x != nil {
		if x, ok := x.Data.(*PinEmojiResource_DefaultEmoji); ok {
			return x.DefaultEmoji
		}
	}
	return ""
}

func (x *PinEmojiResource) GetArea() *PinEmojiArear {
	if x != nil {
		return x.Area
	}
	return nil
}

type isPinEmojiResource_Data interface {
	isPinEmojiResource_Data()
}

type PinEmojiResource_GeneratedEmojiResource struct {
	GeneratedEmojiResource *v1.Resource `protobuf:"bytes,1,opt,name=generated_emoji_resource,json=generatedEmojiResource,proto3,oneof"`
}

type PinEmojiResource_DefaultEmoji struct {
	DefaultEmoji string `protobuf:"bytes,2,opt,name=default_emoji,json=defaultEmoji,proto3,oneof"`
}

func (*PinEmojiResource_GeneratedEmojiResource) isPinEmojiResource_Data() {}

func (*PinEmojiResource_DefaultEmoji) isPinEmojiResource_Data() {}

type StoryPinContext struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsUnlocked    bool                   `protobuf:"varint,1,opt,name=is_unlocked,json=isUnlocked,proto3" json:"is_unlocked,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPinContext) Reset() {
	*x = StoryPinContext{}
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPinContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPinContext) ProtoMessage() {}

func (x *StoryPinContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPinContext.ProtoReflect.Descriptor instead.
func (*StoryPinContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_pin_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPinContext) GetIsUnlocked() bool {
	if x != nil {
		return x.IsUnlocked
	}
	return false
}

type StoryPinConfig struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	BackgroundImage   *v1.Resource           `protobuf:"bytes,1,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	PinEmojiResources []*PinEmojiResource    `protobuf:"bytes,2,rep,name=pin_emoji_resources,json=pinEmojiResources,proto3" json:"pin_emoji_resources,omitempty"`
	Caption           *AttachmentText        `protobuf:"bytes,3,opt,name=caption,proto3" json:"caption,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr    `protobuf:"bytes,4,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPinConfig) Reset() {
	*x = StoryPinConfig{}
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPinConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPinConfig) ProtoMessage() {}

func (x *StoryPinConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_pin_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPinConfig.ProtoReflect.Descriptor instead.
func (*StoryPinConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_pin_types_proto_rawDescGZIP(), []int{3}
}

func (x *StoryPinConfig) GetBackgroundImage() *v1.Resource {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

func (x *StoryPinConfig) GetPinEmojiResources() []*PinEmojiResource {
	if x != nil {
		return x.PinEmojiResources
	}
	return nil
}

func (x *StoryPinConfig) GetCaption() *AttachmentText {
	if x != nil {
		return x.Caption
	}
	return nil
}

func (x *StoryPinConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

var File_api_items_story_types_v1_pin_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_pin_types_proto_rawDesc = "" +
	"\n" +
	"(api/items/story/types/v1/pin.types.proto\x12\x18api.items.story.types.v1\x1a!api/resource/types/v1/types.proto\x1a)api/items/story/types/v1/base_types.proto\"Y\n" +
	"\rPinEmojiArear\x12\f\n" +
	"\x01x\x18\x01 \x01(\tR\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\tR\x01y\x12\x14\n" +
	"\x05width\x18\x03 \x01(\tR\x05width\x12\x16\n" +
	"\x06height\x18\x04 \x01(\tR\x06height\"\xdb\x01\n" +
	"\x10PinEmojiResource\x12[\n" +
	"\x18generated_emoji_resource\x18\x01 \x01(\v2\x1f.api.resource.types.v1.ResourceH\x00R\x16generatedEmojiResource\x12%\n" +
	"\rdefault_emoji\x18\x02 \x01(\tH\x00R\fdefaultEmoji\x12;\n" +
	"\x04area\x18\x03 \x01(\v2'.api.items.story.types.v1.PinEmojiArearR\x04areaB\x06\n" +
	"\x04data\"2\n" +
	"\x0fStoryPinContext\x12\x1f\n" +
	"\vis_unlocked\x18\x01 \x01(\bR\n" +
	"isUnlocked\"\xd8\x02\n" +
	"\x0eStoryPinConfig\x12J\n" +
	"\x10background_image\x18\x01 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x0fbackgroundImage\x12Z\n" +
	"\x13pin_emoji_resources\x18\x02 \x03(\v2*.api.items.story.types.v1.PinEmojiResourceR\x11pinEmojiResources\x12B\n" +
	"\acaption\x18\x03 \x01(\v2(.api.items.story.types.v1.AttachmentTextR\acaption\x12Z\n" +
	"\x13moment_create_attrs\x18\x04 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrsB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_pin_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_pin_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_pin_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_pin_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_pin_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_pin_types_proto_rawDesc), len(file_api_items_story_types_v1_pin_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_pin_types_proto_rawDescData
}

var file_api_items_story_types_v1_pin_types_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_items_story_types_v1_pin_types_proto_goTypes = []any{
	(*PinEmojiArear)(nil),    // 0: api.items.story.types.v1.PinEmojiArear
	(*PinEmojiResource)(nil), // 1: api.items.story.types.v1.PinEmojiResource
	(*StoryPinContext)(nil),  // 2: api.items.story.types.v1.StoryPinContext
	(*StoryPinConfig)(nil),   // 3: api.items.story.types.v1.StoryPinConfig
	(*v1.Resource)(nil),      // 4: api.resource.types.v1.Resource
	(*AttachmentText)(nil),   // 5: api.items.story.types.v1.AttachmentText
	(*MomentCreateAttr)(nil), // 6: api.items.story.types.v1.MomentCreateAttr
}
var file_api_items_story_types_v1_pin_types_proto_depIdxs = []int32{
	4, // 0: api.items.story.types.v1.PinEmojiResource.generated_emoji_resource:type_name -> api.resource.types.v1.Resource
	0, // 1: api.items.story.types.v1.PinEmojiResource.area:type_name -> api.items.story.types.v1.PinEmojiArear
	4, // 2: api.items.story.types.v1.StoryPinConfig.background_image:type_name -> api.resource.types.v1.Resource
	1, // 3: api.items.story.types.v1.StoryPinConfig.pin_emoji_resources:type_name -> api.items.story.types.v1.PinEmojiResource
	5, // 4: api.items.story.types.v1.StoryPinConfig.caption:type_name -> api.items.story.types.v1.AttachmentText
	6, // 5: api.items.story.types.v1.StoryPinConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_pin_types_proto_init() }
func file_api_items_story_types_v1_pin_types_proto_init() {
	if File_api_items_story_types_v1_pin_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	file_api_items_story_types_v1_pin_types_proto_msgTypes[1].OneofWrappers = []any{
		(*PinEmojiResource_GeneratedEmojiResource)(nil),
		(*PinEmojiResource_DefaultEmoji)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_pin_types_proto_rawDesc), len(file_api_items_story_types_v1_pin_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_pin_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_pin_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_pin_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_pin_types_proto = out.File
	file_api_items_story_types_v1_pin_types_proto_goTypes = nil
	file_api_items_story_types_v1_pin_types_proto_depIdxs = nil
}
