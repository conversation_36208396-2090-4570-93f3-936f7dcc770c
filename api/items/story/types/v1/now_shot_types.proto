syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/items/story/types/v1/base_types.proto";

// NowShot 玩法上下文
message StoryPlayNowShotContext {
  // 消费状态
  enum ConsumeStatus {
    CONSUME_STATUS_UNSPECIFIED = 0;
    CONSUME_STATUS_LOCK = 1;
    CONSUME_STATUS_UNLOCK = 2;
    CONSUME_STATUS_FAILED = 3;
  }
  ConsumeStatus status = 1;
  // 开始倒计时时间
  uint32 start_time = 2;
  // 用户的尝试记录, deprecated @dexin
  repeated string user_submit_image_urls = 3;

  // 用户的尝试记录, v2使用这个字段 @dexin
  repeated api.items.story.types.v1.Resource resource = 4;
  
  // TTL字段，可选，单位秒，用于存储用户自定义的倒计时时间
  optional uint32 ttl = 5;
}

// NowShot 玩法配置
message StoryPlayNowShotConfig {
  // Begin Deprecated，实现完api之后删除 @dexin
  // 显示在屏幕中间的caption
  string caption = 1;
  enum ResourceType {
    RESOURCE_TYPE_UNSPECIFIED = 0;
    RESOURCE_TYPE_IMAGE = 1;
    RESOURCE_TYPE_VIDEO = 2;
  }
  // 封面资源类型，参考 ResourceType
  string resource_type = 2;
  // 封面资源 url，创建时，客户端传 key
  string resource_url = 3;
  // 如果封面资源是视频时，需要传一个视频首帧，创作时给 key
  optional string thumbnail_url = 4;
  // 需要解锁的资源类型，参考 ResourceType
  string end_resource_type = 5;
  // 需要解锁的资源 url，创建时，客户端传 key
  string end_resource_url = 6;
  // 如果需要解锁的资源是视频时，需要传一个视频首帧，创作时给 key
  optional string end_thumbnail_url = 7;
  // End Deprecated

  // 倒计时时间 time to live，单位秒，默认1分钟
  uint32 ttl = 8;
  
  // 封面和资源都在这里面
  CommonPlayConfig commonConfig = 9;
  repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 10;
}

// NowShot 玩法示例
message StoryPlayNowShotExample {
  ExampleCommonInfo common_info = 1;
  message Case {
    // 示例封面图片
    string cover_image_url = 1;
  }
  repeated Case cases = 2;
}
