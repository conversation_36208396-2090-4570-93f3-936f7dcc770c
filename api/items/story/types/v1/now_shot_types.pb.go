// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/now_shot_types.proto

package api_items_story_types_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 消费状态
type StoryPlayNowShotContext_ConsumeStatus int32

const (
	StoryPlayNowShotContext_CONSUME_STATUS_UNSPECIFIED StoryPlayNowShotContext_ConsumeStatus = 0
	StoryPlayNowShotContext_CONSUME_STATUS_LOCK        StoryPlayNowShotContext_ConsumeStatus = 1
	StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK      StoryPlayNowShotContext_ConsumeStatus = 2
	StoryPlayNowShotContext_CONSUME_STATUS_FAILED      StoryPlayNowShotContext_ConsumeStatus = 3
)

// Enum value maps for StoryPlayNowShotContext_ConsumeStatus.
var (
	StoryPlayNowShotContext_ConsumeStatus_name = map[int32]string{
		0: "CONSUME_STATUS_UNSPECIFIED",
		1: "CONSUME_STATUS_LOCK",
		2: "CONSUME_STATUS_UNLOCK",
		3: "CONSUME_STATUS_FAILED",
	}
	StoryPlayNowShotContext_ConsumeStatus_value = map[string]int32{
		"CONSUME_STATUS_UNSPECIFIED": 0,
		"CONSUME_STATUS_LOCK":        1,
		"CONSUME_STATUS_UNLOCK":      2,
		"CONSUME_STATUS_FAILED":      3,
	}
)

func (x StoryPlayNowShotContext_ConsumeStatus) Enum() *StoryPlayNowShotContext_ConsumeStatus {
	p := new(StoryPlayNowShotContext_ConsumeStatus)
	*p = x
	return p
}

func (x StoryPlayNowShotContext_ConsumeStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayNowShotContext_ConsumeStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_now_shot_types_proto_enumTypes[0].Descriptor()
}

func (StoryPlayNowShotContext_ConsumeStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_now_shot_types_proto_enumTypes[0]
}

func (x StoryPlayNowShotContext_ConsumeStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayNowShotContext_ConsumeStatus.Descriptor instead.
func (StoryPlayNowShotContext_ConsumeStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP(), []int{0, 0}
}

type StoryPlayNowShotConfig_ResourceType int32

const (
	StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED StoryPlayNowShotConfig_ResourceType = 0
	StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE       StoryPlayNowShotConfig_ResourceType = 1
	StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO       StoryPlayNowShotConfig_ResourceType = 2
)

// Enum value maps for StoryPlayNowShotConfig_ResourceType.
var (
	StoryPlayNowShotConfig_ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
	}
	StoryPlayNowShotConfig_ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
	}
)

func (x StoryPlayNowShotConfig_ResourceType) Enum() *StoryPlayNowShotConfig_ResourceType {
	p := new(StoryPlayNowShotConfig_ResourceType)
	*p = x
	return p
}

func (x StoryPlayNowShotConfig_ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayNowShotConfig_ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_now_shot_types_proto_enumTypes[1].Descriptor()
}

func (StoryPlayNowShotConfig_ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_now_shot_types_proto_enumTypes[1]
}

func (x StoryPlayNowShotConfig_ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayNowShotConfig_ResourceType.Descriptor instead.
func (StoryPlayNowShotConfig_ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP(), []int{1, 0}
}

// NowShot 玩法上下文
type StoryPlayNowShotContext struct {
	state  protoimpl.MessageState                `protogen:"open.v1"`
	Status StoryPlayNowShotContext_ConsumeStatus `protobuf:"varint,1,opt,name=status,proto3,enum=api.items.story.types.v1.StoryPlayNowShotContext_ConsumeStatus" json:"status,omitempty"`
	// 开始倒计时时间
	StartTime uint32 `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 用户的尝试记录, deprecated @dexin
	UserSubmitImageUrls []string `protobuf:"bytes,3,rep,name=user_submit_image_urls,json=userSubmitImageUrls,proto3" json:"user_submit_image_urls,omitempty"`
	// 用户的尝试记录, v2使用这个字段 @dexin
	Resource []*Resource `protobuf:"bytes,4,rep,name=resource,proto3" json:"resource,omitempty"`
	// TTL字段，可选，单位秒，用于存储用户自定义的倒计时时间
	Ttl           *uint32 `protobuf:"varint,5,opt,name=ttl,proto3,oneof" json:"ttl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayNowShotContext) Reset() {
	*x = StoryPlayNowShotContext{}
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayNowShotContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayNowShotContext) ProtoMessage() {}

func (x *StoryPlayNowShotContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayNowShotContext.ProtoReflect.Descriptor instead.
func (*StoryPlayNowShotContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryPlayNowShotContext) GetStatus() StoryPlayNowShotContext_ConsumeStatus {
	if x != nil {
		return x.Status
	}
	return StoryPlayNowShotContext_CONSUME_STATUS_UNSPECIFIED
}

func (x *StoryPlayNowShotContext) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *StoryPlayNowShotContext) GetUserSubmitImageUrls() []string {
	if x != nil {
		return x.UserSubmitImageUrls
	}
	return nil
}

func (x *StoryPlayNowShotContext) GetResource() []*Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *StoryPlayNowShotContext) GetTtl() uint32 {
	if x != nil && x.Ttl != nil {
		return *x.Ttl
	}
	return 0
}

// NowShot 玩法配置
type StoryPlayNowShotConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Begin Deprecated，实现完api之后删除 @dexin
	// 显示在屏幕中间的caption
	Caption string `protobuf:"bytes,1,opt,name=caption,proto3" json:"caption,omitempty"`
	// 封面资源类型，参考 ResourceType
	ResourceType string `protobuf:"bytes,2,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// 封面资源 url，创建时，客户端传 key
	ResourceUrl string `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 如果封面资源是视频时，需要传一个视频首帧，创作时给 key
	ThumbnailUrl *string `protobuf:"bytes,4,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	// 需要解锁的资源类型，参考 ResourceType
	EndResourceType string `protobuf:"bytes,5,opt,name=end_resource_type,json=endResourceType,proto3" json:"end_resource_type,omitempty"`
	// 需要解锁的资源 url，创建时，客户端传 key
	EndResourceUrl string `protobuf:"bytes,6,opt,name=end_resource_url,json=endResourceUrl,proto3" json:"end_resource_url,omitempty"`
	// 如果需要解锁的资源是视频时，需要传一个视频首帧，创作时给 key
	EndThumbnailUrl *string `protobuf:"bytes,7,opt,name=end_thumbnail_url,json=endThumbnailUrl,proto3,oneof" json:"end_thumbnail_url,omitempty"` // End Deprecated
	// 倒计时时间 time to live，单位秒，默认1分钟
	Ttl uint32 `protobuf:"varint,8,opt,name=ttl,proto3" json:"ttl,omitempty"`
	// 封面和资源都在这里面
	CommonConfig      *CommonPlayConfig   `protobuf:"bytes,9,opt,name=commonConfig,proto3" json:"commonConfig,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr `protobuf:"bytes,10,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayNowShotConfig) Reset() {
	*x = StoryPlayNowShotConfig{}
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayNowShotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayNowShotConfig) ProtoMessage() {}

func (x *StoryPlayNowShotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayNowShotConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayNowShotConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP(), []int{1}
}

func (x *StoryPlayNowShotConfig) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetEndResourceType() string {
	if x != nil {
		return x.EndResourceType
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetEndResourceUrl() string {
	if x != nil {
		return x.EndResourceUrl
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetEndThumbnailUrl() string {
	if x != nil && x.EndThumbnailUrl != nil {
		return *x.EndThumbnailUrl
	}
	return ""
}

func (x *StoryPlayNowShotConfig) GetTtl() uint32 {
	if x != nil {
		return x.Ttl
	}
	return 0
}

func (x *StoryPlayNowShotConfig) GetCommonConfig() *CommonPlayConfig {
	if x != nil {
		return x.CommonConfig
	}
	return nil
}

func (x *StoryPlayNowShotConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

// NowShot 玩法示例
type StoryPlayNowShotExample struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	CommonInfo    *ExampleCommonInfo              `protobuf:"bytes,1,opt,name=common_info,json=commonInfo,proto3" json:"common_info,omitempty"`
	Cases         []*StoryPlayNowShotExample_Case `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayNowShotExample) Reset() {
	*x = StoryPlayNowShotExample{}
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayNowShotExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayNowShotExample) ProtoMessage() {}

func (x *StoryPlayNowShotExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayNowShotExample.ProtoReflect.Descriptor instead.
func (*StoryPlayNowShotExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayNowShotExample) GetCommonInfo() *ExampleCommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *StoryPlayNowShotExample) GetCases() []*StoryPlayNowShotExample_Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

type StoryPlayNowShotExample_Case struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 示例封面图片
	CoverImageUrl string `protobuf:"bytes,1,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayNowShotExample_Case) Reset() {
	*x = StoryPlayNowShotExample_Case{}
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayNowShotExample_Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayNowShotExample_Case) ProtoMessage() {}

func (x *StoryPlayNowShotExample_Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_now_shot_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayNowShotExample_Case.ProtoReflect.Descriptor instead.
func (*StoryPlayNowShotExample_Case) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP(), []int{2, 0}
}

func (x *StoryPlayNowShotExample_Case) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

var File_api_items_story_types_v1_now_shot_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_now_shot_types_proto_rawDesc = "" +
	"\n" +
	"-api/items/story/types/v1/now_shot_types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\"\xa5\x03\n" +
	"\x17StoryPlayNowShotContext\x12W\n" +
	"\x06status\x18\x01 \x01(\x0e2?.api.items.story.types.v1.StoryPlayNowShotContext.ConsumeStatusR\x06status\x12\x1d\n" +
	"\n" +
	"start_time\x18\x02 \x01(\rR\tstartTime\x123\n" +
	"\x16user_submit_image_urls\x18\x03 \x03(\tR\x13userSubmitImageUrls\x12>\n" +
	"\bresource\x18\x04 \x03(\v2\".api.items.story.types.v1.ResourceR\bresource\x12\x15\n" +
	"\x03ttl\x18\x05 \x01(\rH\x00R\x03ttl\x88\x01\x01\"~\n" +
	"\rConsumeStatus\x12\x1e\n" +
	"\x1aCONSUME_STATUS_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13CONSUME_STATUS_LOCK\x10\x01\x12\x19\n" +
	"\x15CONSUME_STATUS_UNLOCK\x10\x02\x12\x19\n" +
	"\x15CONSUME_STATUS_FAILED\x10\x03B\x06\n" +
	"\x04_ttl\"\xf2\x04\n" +
	"\x16StoryPlayNowShotConfig\x12\x18\n" +
	"\acaption\x18\x01 \x01(\tR\acaption\x12#\n" +
	"\rresource_type\x18\x02 \x01(\tR\fresourceType\x12!\n" +
	"\fresource_url\x18\x03 \x01(\tR\vresourceUrl\x12(\n" +
	"\rthumbnail_url\x18\x04 \x01(\tH\x00R\fthumbnailUrl\x88\x01\x01\x12*\n" +
	"\x11end_resource_type\x18\x05 \x01(\tR\x0fendResourceType\x12(\n" +
	"\x10end_resource_url\x18\x06 \x01(\tR\x0eendResourceUrl\x12/\n" +
	"\x11end_thumbnail_url\x18\a \x01(\tH\x01R\x0fendThumbnailUrl\x88\x01\x01\x12\x10\n" +
	"\x03ttl\x18\b \x01(\rR\x03ttl\x12N\n" +
	"\fcommonConfig\x18\t \x01(\v2*.api.items.story.types.v1.CommonPlayConfigR\fcommonConfig\x12Z\n" +
	"\x13moment_create_attrs\x18\n" +
	" \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\"_\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02B\x10\n" +
	"\x0e_thumbnail_urlB\x14\n" +
	"\x12_end_thumbnail_url\"\xe5\x01\n" +
	"\x17StoryPlayNowShotExample\x12L\n" +
	"\vcommon_info\x18\x01 \x01(\v2+.api.items.story.types.v1.ExampleCommonInfoR\n" +
	"commonInfo\x12L\n" +
	"\x05cases\x18\x02 \x03(\v26.api.items.story.types.v1.StoryPlayNowShotExample.CaseR\x05cases\x1a.\n" +
	"\x04Case\x12&\n" +
	"\x0fcover_image_url\x18\x01 \x01(\tR\rcoverImageUrlB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_now_shot_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_now_shot_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_now_shot_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_now_shot_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_now_shot_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_now_shot_types_proto_rawDesc), len(file_api_items_story_types_v1_now_shot_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_now_shot_types_proto_rawDescData
}

var file_api_items_story_types_v1_now_shot_types_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_items_story_types_v1_now_shot_types_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_items_story_types_v1_now_shot_types_proto_goTypes = []any{
	(StoryPlayNowShotContext_ConsumeStatus)(0), // 0: api.items.story.types.v1.StoryPlayNowShotContext.ConsumeStatus
	(StoryPlayNowShotConfig_ResourceType)(0),   // 1: api.items.story.types.v1.StoryPlayNowShotConfig.ResourceType
	(*StoryPlayNowShotContext)(nil),            // 2: api.items.story.types.v1.StoryPlayNowShotContext
	(*StoryPlayNowShotConfig)(nil),             // 3: api.items.story.types.v1.StoryPlayNowShotConfig
	(*StoryPlayNowShotExample)(nil),            // 4: api.items.story.types.v1.StoryPlayNowShotExample
	(*StoryPlayNowShotExample_Case)(nil),       // 5: api.items.story.types.v1.StoryPlayNowShotExample.Case
	(*Resource)(nil),                           // 6: api.items.story.types.v1.Resource
	(*CommonPlayConfig)(nil),                   // 7: api.items.story.types.v1.CommonPlayConfig
	(*MomentCreateAttr)(nil),                   // 8: api.items.story.types.v1.MomentCreateAttr
	(*ExampleCommonInfo)(nil),                  // 9: api.items.story.types.v1.ExampleCommonInfo
}
var file_api_items_story_types_v1_now_shot_types_proto_depIdxs = []int32{
	0, // 0: api.items.story.types.v1.StoryPlayNowShotContext.status:type_name -> api.items.story.types.v1.StoryPlayNowShotContext.ConsumeStatus
	6, // 1: api.items.story.types.v1.StoryPlayNowShotContext.resource:type_name -> api.items.story.types.v1.Resource
	7, // 2: api.items.story.types.v1.StoryPlayNowShotConfig.commonConfig:type_name -> api.items.story.types.v1.CommonPlayConfig
	8, // 3: api.items.story.types.v1.StoryPlayNowShotConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	9, // 4: api.items.story.types.v1.StoryPlayNowShotExample.common_info:type_name -> api.items.story.types.v1.ExampleCommonInfo
	5, // 5: api.items.story.types.v1.StoryPlayNowShotExample.cases:type_name -> api.items.story.types.v1.StoryPlayNowShotExample.Case
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_now_shot_types_proto_init() }
func file_api_items_story_types_v1_now_shot_types_proto_init() {
	if File_api_items_story_types_v1_now_shot_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	file_api_items_story_types_v1_now_shot_types_proto_msgTypes[0].OneofWrappers = []any{}
	file_api_items_story_types_v1_now_shot_types_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_now_shot_types_proto_rawDesc), len(file_api_items_story_types_v1_now_shot_types_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_now_shot_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_now_shot_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_now_shot_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_now_shot_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_now_shot_types_proto = out.File
	file_api_items_story_types_v1_now_shot_types_proto_goTypes = nil
	file_api_items_story_types_v1_now_shot_types_proto_depIdxs = nil
}
