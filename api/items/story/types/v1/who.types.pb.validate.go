// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/who.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on WhoStoryPlayOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WhoStoryPlayOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhoStoryPlayOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WhoStoryPlayOptionMultiError, or nil if none found.
func (m *WhoStoryPlayOption) ValidateAll() error {
	return m.validate(true)
}

func (m *WhoStoryPlayOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Correct

	if all {
		switch v := interface{}(m.GetOption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhoStoryPlayOptionValidationError{
					field:  "Option",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhoStoryPlayOptionValidationError{
					field:  "Option",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhoStoryPlayOptionValidationError{
				field:  "Option",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WhoStoryPlayOptionMultiError(errors)
	}

	return nil
}

// WhoStoryPlayOptionMultiError is an error wrapping multiple validation errors
// returned by WhoStoryPlayOption.ValidateAll() if the designated constraints
// aren't met.
type WhoStoryPlayOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhoStoryPlayOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhoStoryPlayOptionMultiError) AllErrors() []error { return m }

// WhoStoryPlayOptionValidationError is the validation error returned by
// WhoStoryPlayOption.Validate if the designated constraints aren't met.
type WhoStoryPlayOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhoStoryPlayOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhoStoryPlayOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhoStoryPlayOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhoStoryPlayOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhoStoryPlayOptionValidationError) ErrorName() string {
	return "WhoStoryPlayOptionValidationError"
}

// Error satisfies the builtin error interface
func (e WhoStoryPlayOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhoStoryPlayOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhoStoryPlayOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhoStoryPlayOptionValidationError{}

// Validate checks the field values on WhoStoryPlayConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WhoStoryPlayConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhoStoryPlayConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WhoStoryPlayConfigMultiError, or nil if none found.
func (m *WhoStoryPlayConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *WhoStoryPlayConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUnlockResourceTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("UnlockResourceTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("UnlockResourceTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WhoStoryPlayConfigValidationError{
					field:  fmt.Sprintf("UnlockResourceTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetUnlockResource() == nil {
		err := WhoStoryPlayConfigValidationError{
			field:  "UnlockResource",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUnlockResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhoStoryPlayConfigValidationError{
					field:  "UnlockResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhoStoryPlayConfigValidationError{
					field:  "UnlockResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhoStoryPlayConfigValidationError{
				field:  "UnlockResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCoverResourceTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("CoverResourceTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("CoverResourceTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WhoStoryPlayConfigValidationError{
					field:  fmt.Sprintf("CoverResourceTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetCoverResource() == nil {
		err := WhoStoryPlayConfigValidationError{
			field:  "CoverResource",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCoverResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WhoStoryPlayConfigValidationError{
					field:  "CoverResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WhoStoryPlayConfigValidationError{
					field:  "CoverResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCoverResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WhoStoryPlayConfigValidationError{
				field:  "CoverResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("Options[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WhoStoryPlayConfigValidationError{
					field:  fmt.Sprintf("Options[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WhoStoryPlayConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WhoStoryPlayConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return WhoStoryPlayConfigMultiError(errors)
	}

	return nil
}

// WhoStoryPlayConfigMultiError is an error wrapping multiple validation errors
// returned by WhoStoryPlayConfig.ValidateAll() if the designated constraints
// aren't met.
type WhoStoryPlayConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhoStoryPlayConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhoStoryPlayConfigMultiError) AllErrors() []error { return m }

// WhoStoryPlayConfigValidationError is the validation error returned by
// WhoStoryPlayConfig.Validate if the designated constraints aren't met.
type WhoStoryPlayConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhoStoryPlayConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhoStoryPlayConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhoStoryPlayConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhoStoryPlayConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhoStoryPlayConfigValidationError) ErrorName() string {
	return "WhoStoryPlayConfigValidationError"
}

// Error satisfies the builtin error interface
func (e WhoStoryPlayConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhoStoryPlayConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhoStoryPlayConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhoStoryPlayConfigValidationError{}

// Validate checks the field values on WhoStoryTryPoint with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *WhoStoryTryPoint) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhoStoryTryPoint with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WhoStoryTryPointMultiError, or nil if none found.
func (m *WhoStoryTryPoint) ValidateAll() error {
	return m.validate(true)
}

func (m *WhoStoryTryPoint) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for X

	// no validation rules for Y

	if len(errors) > 0 {
		return WhoStoryTryPointMultiError(errors)
	}

	return nil
}

// WhoStoryTryPointMultiError is an error wrapping multiple validation errors
// returned by WhoStoryTryPoint.ValidateAll() if the designated constraints
// aren't met.
type WhoStoryTryPointMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhoStoryTryPointMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhoStoryTryPointMultiError) AllErrors() []error { return m }

// WhoStoryTryPointValidationError is the validation error returned by
// WhoStoryTryPoint.Validate if the designated constraints aren't met.
type WhoStoryTryPointValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhoStoryTryPointValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhoStoryTryPointValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhoStoryTryPointValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhoStoryTryPointValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhoStoryTryPointValidationError) ErrorName() string { return "WhoStoryTryPointValidationError" }

// Error satisfies the builtin error interface
func (e WhoStoryTryPointValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhoStoryTryPoint.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhoStoryTryPointValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhoStoryTryPointValidationError{}

// Validate checks the field values on WhoStoryPlayContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WhoStoryPlayContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WhoStoryPlayContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WhoStoryPlayContextMultiError, or nil if none found.
func (m *WhoStoryPlayContext) ValidateAll() error {
	return m.validate(true)
}

func (m *WhoStoryPlayContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTriedPoints() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WhoStoryPlayContextValidationError{
						field:  fmt.Sprintf("TriedPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WhoStoryPlayContextValidationError{
						field:  fmt.Sprintf("TriedPoints[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WhoStoryPlayContextValidationError{
					field:  fmt.Sprintf("TriedPoints[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEnabledOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WhoStoryPlayContextValidationError{
						field:  fmt.Sprintf("EnabledOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WhoStoryPlayContextValidationError{
						field:  fmt.Sprintf("EnabledOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WhoStoryPlayContextValidationError{
					field:  fmt.Sprintf("EnabledOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsUnlocked

	// no validation rules for IsConsumed

	// no validation rules for TriedCount

	// no validation rules for MaxTryCount

	if len(errors) > 0 {
		return WhoStoryPlayContextMultiError(errors)
	}

	return nil
}

// WhoStoryPlayContextMultiError is an error wrapping multiple validation
// errors returned by WhoStoryPlayContext.ValidateAll() if the designated
// constraints aren't met.
type WhoStoryPlayContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WhoStoryPlayContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WhoStoryPlayContextMultiError) AllErrors() []error { return m }

// WhoStoryPlayContextValidationError is the validation error returned by
// WhoStoryPlayContext.Validate if the designated constraints aren't met.
type WhoStoryPlayContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WhoStoryPlayContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WhoStoryPlayContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WhoStoryPlayContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WhoStoryPlayContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WhoStoryPlayContextValidationError) ErrorName() string {
	return "WhoStoryPlayContextValidationError"
}

// Error satisfies the builtin error interface
func (e WhoStoryPlayContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWhoStoryPlayContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WhoStoryPlayContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WhoStoryPlayContextValidationError{}
