syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";

import "api/resource/types/v1/types.proto";

message AttachmentText {
	// 文本内容
	string text = 1;
	// 字体名称
	string font_name = 2;
	// 字体大小
	uint32 font_size = 3;
	// 颜色
	string color = 4;
	// x centerY 相对坐标
	string x = 5;
	// y centerY 相对坐标
	string y = 6;
	// 宽度 相对屏幕宽度
	string width = 7;
	// 高度 相对屏幕高度
	string height = 8;
	// 对齐方式 center, left, right
	string alignment = 9;
	// fillStyle none, white, textcolor
	string fill_style = 10;
	// 旋转
	string rotation = 11;
	// 缩放
	string scale = 12;
	// @的 user
	message AtUser {
		string id = 1;
		// 创作时，客户端可以不传，服务端下发时会自行填充
		string nickname = 2;
	}
	repeated AtUser at_users = 13;
}

message ExampleCommonInfo {
	// 示例的提示
	string tips = 1;
	// story 的图片
	// 目前，当是换图玩法时：
	// 1. 马赛克时，数组长度固定为1， 表示封面图
	// 2. 线性解锁时，数组长度固定为2，第一个表示封面图，第二个表示下一个节点的封面图
	// 当是海龟汤玩法时，数组长度固定为1，表示封面图
	repeated string image_urls = 2;
}


enum ResourceType {
	RESOURCE_TYPE_UNSPECIFIED = 0;
	RESOURCE_TYPE_IMAGE = 1;
	RESOURCE_TYPE_VIDEO = 2;
	RESOURCE_TYPE_NONE = 3;
	RESOURCE_TYPE_GRADIENT = 4;
}

message Resource {
	ResourceType resource_type = 1;
	// 资源 key
	string resource_key = 2;
	// 资源 url，创建时可以留空，返回时服务会拼接
	string resource_url = 3;
	// 如果是视频，则需要提供封面图
	// 如果是图片，直接用 resource_key 即可
	string cover_image_key = 4;
	// 封面图 url，创建时可以留空，返回时服务会拼接
	string cover_image_url = 5;
	// 封面宽度
	uint32 cover_width = 6;
	// 封面高度
	uint32 cover_height = 7;
}

message Cover {
	// CoverType，无图 none/渐变图 gradient/自定义上传图片 image/自定义上传视频 video
	ResourceType CoverType = 1;
	// 只在 CoverType 为 image 时生效，资源 key
	string resource_key = 2;
	// 只在 CoverType 为 image 时生效，资源 url，创建时可以留空，返回时服务会拼接
	string resource_url = 3;
	// 只在 CoverType 为 gradient 时生效
	string gradient = 4;
	// 封面宽度
	uint32 cover_width = 5;
	// 封面高度
	uint32 cover_height = 6;
	// 只在 CoverType 为 video 时生效，资源 key
	string thumbnail_key = 7;
	// 只在 CoverType 为 video 时生效，资源 url，创建时可以留空，返回时服务会拼接
	string thumbnail_url = 8;
}

message Shooting {
	ShootingModel shooting_model = 1;
	// 拍摄目的，由 LLM 生成
	string purpose = 2;
}

message ShootingResource {
	ResourceType type = 1;
	// 作为上传资源时，此字段可以不填
	string resource_url = 2;
	// 作为上传资源时，此字段必填
	string resource_key = 3;
	// 视频封面图片url，如果type为RESOURCE_TYPE_VIDEO，会返回  video_cover_image_object_key 的 cdn 地址
	optional string video_cover_image_url = 4;
	// 视频封面图片object key，如果type为RESOURCE_TYPE_VIDEO，上传时必须设置
	optional string video_cover_image_object_key = 5;
	// 资源描述
	string caption = 6;
	// 拍摄模式
	ShootingModel shooting_model = 7;
}

//   - 拍摄模式（Shooting Mode），如Duo Camera、普通拍摄(前/后置)、Pano全景、延时、慢动作、电影模式(Cinematic)、边走边拍、对镜头讲话、打字Caption等
enum ShootingModel {
	SHOOTING_MODEL_UNSPECIFIED = 0;
	// 双摄
	SHOOTING_MODEL_DUO_CAMERA = 1;
	// 前置摄像头
	SHOOTING_MODEL_FRONT_CAMERA = 2;
	// 后置摄像头
	SHOOTING_MODEL_BACK_CAMERA = 3;
	// 全景
	SHOOTING_MODEL_PANO = 4;
	// 延时
	SHOOTING_MODEL_TIME_LAPSE = 5;
	// 慢动作
	SHOOTING_MODEL_SLOW_MOTION = 6;
	// 电影模式
	SHOOTING_MODEL_CINEMATIC = 7;
	// 边走边拍
	SHOOTING_MODEL_WALK_AND_SHOOT = 8;
	// 对镜头讲话
	SHOOTING_MODEL_TALK_TO_CAMERA = 9;
	// 打字Caption
	SHOOTING_MODEL_CAPTION = 10;
}

enum ShootingStyle {
	Unspecified = 0;
	Default = 1;
	Extrovert = 2;
	Introvert = 3;
	Humorist = 4;
	Aesthetics = 5;
	Socializer = 6;
	Playful = 7;
}

message Condition {
	// 展示给消费者的字幕
	AttachmentText hint = 1;
	// llm 的 prompt，即 Creator Criteria
	string prompt = 2;
}

message CommonPlayConfig {
	// V2 新的封面类型
	api.items.story.types.v1.Cover cover = 1;
	// V2 资源类型
	api.items.story.types.v1.Resource resource = 2;
	// V2 作者 at your friend 填写的文字，附着在封面资源上
	repeated AttachmentText cover_captions = 3;
	// V2 作者 condition
	Condition condition_v2 = 4;
	// V2 最多允许尝试多少次
	uint32 max_try_count = 5;
	// V2 作者 at your friend 填写的文字，附着在解锁后的资源上
	repeated AttachmentText resource_captions = 6;
}

message Word {
	// 起始时间,距离音频开始的毫秒偏移值。
	uint32 start_time = 1;
	// 结束时间,距离音频开始的毫秒偏移值。
	uint32 end_time = 2;
	// 文本
	string text = 3;
}

message MomentCreateAttr {
	optional api.resource.types.v1.Resource resource = 1;
	repeated AttachmentText attachment_texts = 2;
	repeated int64 avatar_ids = 3;
}
