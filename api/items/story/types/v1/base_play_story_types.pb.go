// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/base_play_story_types.proto

package api_items_story_types_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StoryPlayBasePlayConfig struct {
	state             protoimpl.MessageState          `protogen:"open.v1"`
	Nodes             []*StoryPlayBasePlayConfig_Node `protobuf:"bytes,1,rep,name=nodes,proto3" json:"nodes,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr             `protobuf:"bytes,2,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayBasePlayConfig) Reset() {
	*x = StoryPlayBasePlayConfig{}
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayBasePlayConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayBasePlayConfig) ProtoMessage() {}

func (x *StoryPlayBasePlayConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayBasePlayConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayBasePlayConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_play_story_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryPlayBasePlayConfig) GetNodes() []*StoryPlayBasePlayConfig_Node {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *StoryPlayBasePlayConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

type StoryPlayBasePlayContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 当前节点 id
	CurrentNodeId string `protobuf:"bytes,1,opt,name=current_node_id,json=currentNodeId,proto3" json:"current_node_id,omitempty"`
	// 当前节点 index
	CurrentNodeIndex uint32 `protobuf:"varint,2,opt,name=current_node_index,json=currentNodeIndex,proto3" json:"current_node_index,omitempty"`
	// 是否解锁
	IsUnlocked    bool `protobuf:"varint,3,opt,name=is_unlocked,json=isUnlocked,proto3" json:"is_unlocked,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayBasePlayContext) Reset() {
	*x = StoryPlayBasePlayContext{}
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayBasePlayContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayBasePlayContext) ProtoMessage() {}

func (x *StoryPlayBasePlayContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayBasePlayContext.ProtoReflect.Descriptor instead.
func (*StoryPlayBasePlayContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_play_story_types_proto_rawDescGZIP(), []int{1}
}

func (x *StoryPlayBasePlayContext) GetCurrentNodeId() string {
	if x != nil {
		return x.CurrentNodeId
	}
	return ""
}

func (x *StoryPlayBasePlayContext) GetCurrentNodeIndex() uint32 {
	if x != nil {
		return x.CurrentNodeIndex
	}
	return 0
}

func (x *StoryPlayBasePlayContext) GetIsUnlocked() bool {
	if x != nil {
		return x.IsUnlocked
	}
	return false
}

type StoryPlayBasePlayExample struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CoverImageUrl string                 `protobuf:"bytes,1,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayBasePlayExample) Reset() {
	*x = StoryPlayBasePlayExample{}
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayBasePlayExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayBasePlayExample) ProtoMessage() {}

func (x *StoryPlayBasePlayExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayBasePlayExample.ProtoReflect.Descriptor instead.
func (*StoryPlayBasePlayExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_play_story_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayBasePlayExample) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

type StoryPlayBasePlayConfig_Node struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 节点 id，客户端自定义，服务端不使用
	Id       string    `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Resource *Resource `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	// TODO，需要在视频或者图片上增加的文字，位置，大小等信息
	// 具体结构和 @fanqi 确认
	AttachmentTexts []*AttachmentText `protobuf:"bytes,3,rep,name=attachment_texts,json=attachmentTexts,proto3" json:"attachment_texts,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StoryPlayBasePlayConfig_Node) Reset() {
	*x = StoryPlayBasePlayConfig_Node{}
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayBasePlayConfig_Node) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayBasePlayConfig_Node) ProtoMessage() {}

func (x *StoryPlayBasePlayConfig_Node) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_play_story_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayBasePlayConfig_Node.ProtoReflect.Descriptor instead.
func (*StoryPlayBasePlayConfig_Node) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_play_story_types_proto_rawDescGZIP(), []int{0, 0}
}

func (x *StoryPlayBasePlayConfig_Node) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryPlayBasePlayConfig_Node) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *StoryPlayBasePlayConfig_Node) GetAttachmentTexts() []*AttachmentText {
	if x != nil {
		return x.AttachmentTexts
	}
	return nil
}

var File_api_items_story_types_v1_base_play_story_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_base_play_story_types_proto_rawDesc = "" +
	"\n" +
	"4api/items/story/types/v1/base_play_story_types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\"\xf1\x02\n" +
	"\x17StoryPlayBasePlayConfig\x12L\n" +
	"\x05nodes\x18\x01 \x03(\v26.api.items.story.types.v1.StoryPlayBasePlayConfig.NodeR\x05nodes\x12Z\n" +
	"\x13moment_create_attrs\x18\x02 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\x1a\xab\x01\n" +
	"\x04Node\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12>\n" +
	"\bresource\x18\x02 \x01(\v2\".api.items.story.types.v1.ResourceR\bresource\x12S\n" +
	"\x10attachment_texts\x18\x03 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x0fattachmentTexts\"\x91\x01\n" +
	"\x18StoryPlayBasePlayContext\x12&\n" +
	"\x0fcurrent_node_id\x18\x01 \x01(\tR\rcurrentNodeId\x12,\n" +
	"\x12current_node_index\x18\x02 \x01(\rR\x10currentNodeIndex\x12\x1f\n" +
	"\vis_unlocked\x18\x03 \x01(\bR\n" +
	"isUnlocked\"B\n" +
	"\x18StoryPlayBasePlayExample\x12&\n" +
	"\x0fcover_image_url\x18\x01 \x01(\tR\rcoverImageUrlB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_base_play_story_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_base_play_story_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_base_play_story_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_base_play_story_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_base_play_story_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_base_play_story_types_proto_rawDesc), len(file_api_items_story_types_v1_base_play_story_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_base_play_story_types_proto_rawDescData
}

var file_api_items_story_types_v1_base_play_story_types_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_items_story_types_v1_base_play_story_types_proto_goTypes = []any{
	(*StoryPlayBasePlayConfig)(nil),      // 0: api.items.story.types.v1.StoryPlayBasePlayConfig
	(*StoryPlayBasePlayContext)(nil),     // 1: api.items.story.types.v1.StoryPlayBasePlayContext
	(*StoryPlayBasePlayExample)(nil),     // 2: api.items.story.types.v1.StoryPlayBasePlayExample
	(*StoryPlayBasePlayConfig_Node)(nil), // 3: api.items.story.types.v1.StoryPlayBasePlayConfig.Node
	(*MomentCreateAttr)(nil),             // 4: api.items.story.types.v1.MomentCreateAttr
	(*Resource)(nil),                     // 5: api.items.story.types.v1.Resource
	(*AttachmentText)(nil),               // 6: api.items.story.types.v1.AttachmentText
}
var file_api_items_story_types_v1_base_play_story_types_proto_depIdxs = []int32{
	3, // 0: api.items.story.types.v1.StoryPlayBasePlayConfig.nodes:type_name -> api.items.story.types.v1.StoryPlayBasePlayConfig.Node
	4, // 1: api.items.story.types.v1.StoryPlayBasePlayConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	5, // 2: api.items.story.types.v1.StoryPlayBasePlayConfig.Node.resource:type_name -> api.items.story.types.v1.Resource
	6, // 3: api.items.story.types.v1.StoryPlayBasePlayConfig.Node.attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_base_play_story_types_proto_init() }
func file_api_items_story_types_v1_base_play_story_types_proto_init() {
	if File_api_items_story_types_v1_base_play_story_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_base_play_story_types_proto_rawDesc), len(file_api_items_story_types_v1_base_play_story_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_base_play_story_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_base_play_story_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_base_play_story_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_base_play_story_types_proto = out.File
	file_api_items_story_types_v1_base_play_story_types_proto_goTypes = nil
	file_api_items_story_types_v1_base_play_story_types_proto_depIdxs = nil
}
