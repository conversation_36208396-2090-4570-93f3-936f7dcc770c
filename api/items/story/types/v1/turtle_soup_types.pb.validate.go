// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/turtle_soup_types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StoryPlayTurtleSoupContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupContextMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsFinished

	// no validation rules for Tips

	// no validation rules for AiResponse

	// no validation rules for TryCount

	if len(errors) > 0 {
		return StoryPlayTurtleSoupContextMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupContextMultiError is an error wrapping multiple
// validation errors returned by StoryPlayTurtleSoupContext.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayTurtleSoupContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupContextMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupContextValidationError is the validation error returned
// by StoryPlayTurtleSoupContext.Validate if the designated constraints aren't met.
type StoryPlayTurtleSoupContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupContextValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupContextValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupConfigMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Caption

	// no validation rules for IntentPrompt

	// no validation rules for ResourceType

	// no validation rules for ResourceUrl

	// no validation rules for EndResourceUrl

	// no validation rules for EndResourceType

	// no validation rules for EndMessage

	// no validation rules for EndMessageFont

	for idx, item := range m.GetCustomAiResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupConfigValidationError{
						field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupConfigValidationError{
						field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayTurtleSoupConfigValidationError{
					field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NeedCreateDiyTemplate

	// no validation rules for MaxTryCount

	if all {
		switch v := interface{}(m.GetCommonPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayTurtleSoupConfigValidationError{
					field:  "CommonPlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayTurtleSoupConfigValidationError{
					field:  "CommonPlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayTurtleSoupConfigValidationError{
				field:  "CommonPlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayTurtleSoupConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ThumbnailUrl != nil {
		// no validation rules for ThumbnailUrl
	}

	if m.EndThumbnailUrl != nil {
		// no validation rules for EndThumbnailUrl
	}

	if m.TemplateId != nil {
		// no validation rules for TemplateId
	}

	if len(errors) > 0 {
		return StoryPlayTurtleSoupConfigMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayTurtleSoupConfig.ValidateAll() if the
// designated constraints aren't met.
type StoryPlayTurtleSoupConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupConfigMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupConfigValidationError is the validation error returned by
// StoryPlayTurtleSoupConfig.Validate if the designated constraints aren't met.
type StoryPlayTurtleSoupConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupConfigValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupConfigValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupExampleMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommonInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayTurtleSoupExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayTurtleSoupExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayTurtleSoupExampleValidationError{
				field:  "CommonInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EndMessage

	// no validation rules for EndMessageFont

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayTurtleSoupExampleValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayTurtleSoupExampleMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupExampleMultiError is an error wrapping multiple
// validation errors returned by StoryPlayTurtleSoupExample.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayTurtleSoupExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupExampleMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupExampleValidationError is the validation error returned
// by StoryPlayTurtleSoupExample.Validate if the designated constraints aren't met.
type StoryPlayTurtleSoupExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupExampleValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupExampleValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupMassExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupMassExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupMassExample with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupMassExampleMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupMassExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupMassExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommonInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayTurtleSoupMassExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayTurtleSoupMassExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayTurtleSoupMassExampleValidationError{
				field:  "CommonInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupMassExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupMassExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayTurtleSoupMassExampleValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayTurtleSoupMassExampleMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupMassExampleMultiError is an error wrapping multiple
// validation errors returned by StoryPlayTurtleSoupMassExample.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayTurtleSoupMassExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupMassExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupMassExampleMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupMassExampleValidationError is the validation error
// returned by StoryPlayTurtleSoupMassExample.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleSoupMassExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupMassExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupMassExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupMassExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupMassExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupMassExampleValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupMassExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupMassExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupMassExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupMassExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupMassExampleValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupMassContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupMassContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupMassContext with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupMassContextMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupMassContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupMassContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsFinished

	// no validation rules for Tips

	// no validation rules for AiResponse

	// no validation rules for TryCount

	if len(errors) > 0 {
		return StoryPlayTurtleSoupMassContextMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupMassContextMultiError is an error wrapping multiple
// validation errors returned by StoryPlayTurtleSoupMassContext.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayTurtleSoupMassContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupMassContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupMassContextMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupMassContextValidationError is the validation error
// returned by StoryPlayTurtleSoupMassContext.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleSoupMassContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupMassContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupMassContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupMassContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupMassContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupMassContextValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupMassContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupMassContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupMassContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupMassContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupMassContextValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupMassConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupMassConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupMassConfig with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupMassConfigMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupMassConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupMassConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Caption

	// no validation rules for IntentPrompt

	// no validation rules for ResourceType

	// no validation rules for ResourceUrl

	for idx, item := range m.GetCustomAiResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupMassConfigValidationError{
						field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayTurtleSoupMassConfigValidationError{
						field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayTurtleSoupMassConfigValidationError{
					field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NeedCreateDiyTemplate

	// no validation rules for MaxTryCount

	if m.ThumbnailUrl != nil {
		// no validation rules for ThumbnailUrl
	}

	if m.TemplateId != nil {
		// no validation rules for TemplateId
	}

	if len(errors) > 0 {
		return StoryPlayTurtleSoupMassConfigMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupMassConfigMultiError is an error wrapping multiple
// validation errors returned by StoryPlayTurtleSoupMassConfig.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayTurtleSoupMassConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupMassConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupMassConfigMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupMassConfigValidationError is the validation error
// returned by StoryPlayTurtleSoupMassConfig.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleSoupMassConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupMassConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupMassConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupMassConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupMassConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupMassConfigValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupMassConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupMassConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupMassConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupMassConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupMassConfigValidationError{}

// Validate checks the field values on
// StoryPlayTurtleSoupConfig_CustomAiResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupConfig_CustomAiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryPlayTurtleSoupConfig_CustomAiResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StoryPlayTurtleSoupConfig_CustomAiResponseMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupConfig_CustomAiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupConfig_CustomAiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleDescription

	// no validation rules for RuleResult

	if len(errors) > 0 {
		return StoryPlayTurtleSoupConfig_CustomAiResponseMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupConfig_CustomAiResponseMultiError is an error wrapping
// multiple validation errors returned by
// StoryPlayTurtleSoupConfig_CustomAiResponse.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayTurtleSoupConfig_CustomAiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupConfig_CustomAiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupConfig_CustomAiResponseMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupConfig_CustomAiResponseValidationError is the validation
// error returned by StoryPlayTurtleSoupConfig_CustomAiResponse.Validate if
// the designated constraints aren't met.
type StoryPlayTurtleSoupConfig_CustomAiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupConfig_CustomAiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupConfig_CustomAiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupConfig_CustomAiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupConfig_CustomAiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupConfig_CustomAiResponseValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupConfig_CustomAiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupConfig_CustomAiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupConfig_CustomAiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupConfig_CustomAiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupConfig_CustomAiResponseValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupExample_Case with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupExample_Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupExample_Case with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupExample_CaseMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupExample_Case) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupExample_Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tips

	// no validation rules for UserMessage

	// no validation rules for AiResponse

	if len(errors) > 0 {
		return StoryPlayTurtleSoupExample_CaseMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupExample_CaseMultiError is an error wrapping multiple
// validation errors returned by StoryPlayTurtleSoupExample_Case.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayTurtleSoupExample_CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupExample_CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupExample_CaseMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupExample_CaseValidationError is the validation error
// returned by StoryPlayTurtleSoupExample_Case.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleSoupExample_CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupExample_CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupExample_CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupExample_CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupExample_CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupExample_CaseValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupExample_CaseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupExample_CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupExample_Case.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupExample_CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupExample_CaseValidationError{}

// Validate checks the field values on StoryPlayTurtleSoupMassExample_Case with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryPlayTurtleSoupMassExample_Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleSoupMassExample_Case
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleSoupMassExample_CaseMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupMassExample_Case) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupMassExample_Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tips

	// no validation rules for UserMessage

	// no validation rules for AiResponse

	if len(errors) > 0 {
		return StoryPlayTurtleSoupMassExample_CaseMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupMassExample_CaseMultiError is an error wrapping multiple
// validation errors returned by
// StoryPlayTurtleSoupMassExample_Case.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayTurtleSoupMassExample_CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupMassExample_CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupMassExample_CaseMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupMassExample_CaseValidationError is the validation error
// returned by StoryPlayTurtleSoupMassExample_Case.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleSoupMassExample_CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupMassExample_CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupMassExample_CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleSoupMassExample_CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupMassExample_CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupMassExample_CaseValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupMassExample_CaseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupMassExample_CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupMassExample_Case.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupMassExample_CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupMassExample_CaseValidationError{}

// Validate checks the field values on
// StoryPlayTurtleSoupMassConfig_CustomAiResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryPlayTurtleSoupMassConfig_CustomAiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryPlayTurtleSoupMassConfig_CustomAiResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// StoryPlayTurtleSoupMassConfig_CustomAiResponseMultiError, or nil if none found.
func (m *StoryPlayTurtleSoupMassConfig_CustomAiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleSoupMassConfig_CustomAiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleDescription

	// no validation rules for RuleResult

	if len(errors) > 0 {
		return StoryPlayTurtleSoupMassConfig_CustomAiResponseMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleSoupMassConfig_CustomAiResponseMultiError is an error
// wrapping multiple validation errors returned by
// StoryPlayTurtleSoupMassConfig_CustomAiResponse.ValidateAll() if the
// designated constraints aren't met.
type StoryPlayTurtleSoupMassConfig_CustomAiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleSoupMassConfig_CustomAiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleSoupMassConfig_CustomAiResponseMultiError) AllErrors() []error { return m }

// StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError is the
// validation error returned by
// StoryPlayTurtleSoupMassConfig_CustomAiResponse.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError) ErrorName() string {
	return "StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleSoupMassConfig_CustomAiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleSoupMassConfig_CustomAiResponseValidationError{}
