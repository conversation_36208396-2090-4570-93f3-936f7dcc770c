// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/base_types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AttachmentText with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AttachmentText) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttachmentText with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AttachmentTextMultiError,
// or nil if none found.
func (m *AttachmentText) ValidateAll() error {
	return m.validate(true)
}

func (m *AttachmentText) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for FontName

	// no validation rules for FontSize

	// no validation rules for Color

	// no validation rules for X

	// no validation rules for Y

	// no validation rules for Width

	// no validation rules for Height

	// no validation rules for Alignment

	// no validation rules for FillStyle

	// no validation rules for Rotation

	// no validation rules for Scale

	for idx, item := range m.GetAtUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AttachmentTextValidationError{
						field:  fmt.Sprintf("AtUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AttachmentTextValidationError{
						field:  fmt.Sprintf("AtUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AttachmentTextValidationError{
					field:  fmt.Sprintf("AtUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AttachmentTextMultiError(errors)
	}

	return nil
}

// AttachmentTextMultiError is an error wrapping multiple validation errors
// returned by AttachmentText.ValidateAll() if the designated constraints
// aren't met.
type AttachmentTextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttachmentTextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttachmentTextMultiError) AllErrors() []error { return m }

// AttachmentTextValidationError is the validation error returned by
// AttachmentText.Validate if the designated constraints aren't met.
type AttachmentTextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttachmentTextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttachmentTextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttachmentTextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttachmentTextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttachmentTextValidationError) ErrorName() string { return "AttachmentTextValidationError" }

// Error satisfies the builtin error interface
func (e AttachmentTextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttachmentText.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttachmentTextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttachmentTextValidationError{}

// Validate checks the field values on ExampleCommonInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExampleCommonInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExampleCommonInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExampleCommonInfoMultiError, or nil if none found.
func (m *ExampleCommonInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ExampleCommonInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tips

	if len(errors) > 0 {
		return ExampleCommonInfoMultiError(errors)
	}

	return nil
}

// ExampleCommonInfoMultiError is an error wrapping multiple validation errors
// returned by ExampleCommonInfo.ValidateAll() if the designated constraints
// aren't met.
type ExampleCommonInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExampleCommonInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExampleCommonInfoMultiError) AllErrors() []error { return m }

// ExampleCommonInfoValidationError is the validation error returned by
// ExampleCommonInfo.Validate if the designated constraints aren't met.
type ExampleCommonInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExampleCommonInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExampleCommonInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExampleCommonInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExampleCommonInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExampleCommonInfoValidationError) ErrorName() string {
	return "ExampleCommonInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ExampleCommonInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExampleCommonInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExampleCommonInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExampleCommonInfoValidationError{}

// Validate checks the field values on Resource with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Resource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Resource with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourceMultiError, or nil
// if none found.
func (m *Resource) ValidateAll() error {
	return m.validate(true)
}

func (m *Resource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceType

	// no validation rules for ResourceKey

	// no validation rules for ResourceUrl

	// no validation rules for CoverImageKey

	// no validation rules for CoverImageUrl

	// no validation rules for CoverWidth

	// no validation rules for CoverHeight

	if len(errors) > 0 {
		return ResourceMultiError(errors)
	}

	return nil
}

// ResourceMultiError is an error wrapping multiple validation errors returned
// by Resource.ValidateAll() if the designated constraints aren't met.
type ResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceMultiError) AllErrors() []error { return m }

// ResourceValidationError is the validation error returned by
// Resource.Validate if the designated constraints aren't met.
type ResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceValidationError) ErrorName() string { return "ResourceValidationError" }

// Error satisfies the builtin error interface
func (e ResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceValidationError{}

// Validate checks the field values on Cover with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Cover) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cover with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CoverMultiError, or nil if none found.
func (m *Cover) ValidateAll() error {
	return m.validate(true)
}

func (m *Cover) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CoverType

	// no validation rules for ResourceKey

	// no validation rules for ResourceUrl

	// no validation rules for Gradient

	// no validation rules for CoverWidth

	// no validation rules for CoverHeight

	// no validation rules for ThumbnailKey

	// no validation rules for ThumbnailUrl

	if len(errors) > 0 {
		return CoverMultiError(errors)
	}

	return nil
}

// CoverMultiError is an error wrapping multiple validation errors returned by
// Cover.ValidateAll() if the designated constraints aren't met.
type CoverMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CoverMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CoverMultiError) AllErrors() []error { return m }

// CoverValidationError is the validation error returned by Cover.Validate if
// the designated constraints aren't met.
type CoverValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CoverValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CoverValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CoverValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CoverValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CoverValidationError) ErrorName() string { return "CoverValidationError" }

// Error satisfies the builtin error interface
func (e CoverValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCover.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CoverValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CoverValidationError{}

// Validate checks the field values on Shooting with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Shooting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Shooting with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ShootingMultiError, or nil
// if none found.
func (m *Shooting) ValidateAll() error {
	return m.validate(true)
}

func (m *Shooting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShootingModel

	// no validation rules for Purpose

	if len(errors) > 0 {
		return ShootingMultiError(errors)
	}

	return nil
}

// ShootingMultiError is an error wrapping multiple validation errors returned
// by Shooting.ValidateAll() if the designated constraints aren't met.
type ShootingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShootingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShootingMultiError) AllErrors() []error { return m }

// ShootingValidationError is the validation error returned by
// Shooting.Validate if the designated constraints aren't met.
type ShootingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShootingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShootingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShootingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShootingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShootingValidationError) ErrorName() string { return "ShootingValidationError" }

// Error satisfies the builtin error interface
func (e ShootingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShooting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShootingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShootingValidationError{}

// Validate checks the field values on ShootingResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ShootingResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ShootingResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ShootingResourceMultiError, or nil if none found.
func (m *ShootingResource) ValidateAll() error {
	return m.validate(true)
}

func (m *ShootingResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for ResourceUrl

	// no validation rules for ResourceKey

	// no validation rules for Caption

	// no validation rules for ShootingModel

	if m.VideoCoverImageUrl != nil {
		// no validation rules for VideoCoverImageUrl
	}

	if m.VideoCoverImageObjectKey != nil {
		// no validation rules for VideoCoverImageObjectKey
	}

	if len(errors) > 0 {
		return ShootingResourceMultiError(errors)
	}

	return nil
}

// ShootingResourceMultiError is an error wrapping multiple validation errors
// returned by ShootingResource.ValidateAll() if the designated constraints
// aren't met.
type ShootingResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ShootingResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ShootingResourceMultiError) AllErrors() []error { return m }

// ShootingResourceValidationError is the validation error returned by
// ShootingResource.Validate if the designated constraints aren't met.
type ShootingResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ShootingResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ShootingResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ShootingResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ShootingResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ShootingResourceValidationError) ErrorName() string { return "ShootingResourceValidationError" }

// Error satisfies the builtin error interface
func (e ShootingResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sShootingResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ShootingResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ShootingResourceValidationError{}

// Validate checks the field values on Condition with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Condition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Condition with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConditionMultiError, or nil
// if none found.
func (m *Condition) ValidateAll() error {
	return m.validate(true)
}

func (m *Condition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConditionValidationError{
					field:  "Hint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConditionValidationError{
					field:  "Hint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConditionValidationError{
				field:  "Hint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Prompt

	if len(errors) > 0 {
		return ConditionMultiError(errors)
	}

	return nil
}

// ConditionMultiError is an error wrapping multiple validation errors returned
// by Condition.ValidateAll() if the designated constraints aren't met.
type ConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConditionMultiError) AllErrors() []error { return m }

// ConditionValidationError is the validation error returned by
// Condition.Validate if the designated constraints aren't met.
type ConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConditionValidationError) ErrorName() string { return "ConditionValidationError" }

// Error satisfies the builtin error interface
func (e ConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConditionValidationError{}

// Validate checks the field values on CommonPlayConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CommonPlayConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonPlayConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CommonPlayConfigMultiError, or nil if none found.
func (m *CommonPlayConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonPlayConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCover()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommonPlayConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommonPlayConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCover()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommonPlayConfigValidationError{
				field:  "Cover",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommonPlayConfigValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommonPlayConfigValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommonPlayConfigValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCoverCaptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CommonPlayConfigValidationError{
						field:  fmt.Sprintf("CoverCaptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CommonPlayConfigValidationError{
						field:  fmt.Sprintf("CoverCaptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CommonPlayConfigValidationError{
					field:  fmt.Sprintf("CoverCaptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetConditionV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommonPlayConfigValidationError{
					field:  "ConditionV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommonPlayConfigValidationError{
					field:  "ConditionV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConditionV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommonPlayConfigValidationError{
				field:  "ConditionV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaxTryCount

	for idx, item := range m.GetResourceCaptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CommonPlayConfigValidationError{
						field:  fmt.Sprintf("ResourceCaptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CommonPlayConfigValidationError{
						field:  fmt.Sprintf("ResourceCaptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CommonPlayConfigValidationError{
					field:  fmt.Sprintf("ResourceCaptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CommonPlayConfigMultiError(errors)
	}

	return nil
}

// CommonPlayConfigMultiError is an error wrapping multiple validation errors
// returned by CommonPlayConfig.ValidateAll() if the designated constraints
// aren't met.
type CommonPlayConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonPlayConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonPlayConfigMultiError) AllErrors() []error { return m }

// CommonPlayConfigValidationError is the validation error returned by
// CommonPlayConfig.Validate if the designated constraints aren't met.
type CommonPlayConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonPlayConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonPlayConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonPlayConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonPlayConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonPlayConfigValidationError) ErrorName() string { return "CommonPlayConfigValidationError" }

// Error satisfies the builtin error interface
func (e CommonPlayConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonPlayConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonPlayConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonPlayConfigValidationError{}

// Validate checks the field values on Word with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Word) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Word with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in WordMultiError, or nil if none found.
func (m *Word) ValidateAll() error {
	return m.validate(true)
}

func (m *Word) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for Text

	if len(errors) > 0 {
		return WordMultiError(errors)
	}

	return nil
}

// WordMultiError is an error wrapping multiple validation errors returned by
// Word.ValidateAll() if the designated constraints aren't met.
type WordMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WordMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WordMultiError) AllErrors() []error { return m }

// WordValidationError is the validation error returned by Word.Validate if the
// designated constraints aren't met.
type WordValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WordValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WordValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WordValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WordValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WordValidationError) ErrorName() string { return "WordValidationError" }

// Error satisfies the builtin error interface
func (e WordValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWord.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WordValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WordValidationError{}

// Validate checks the field values on MomentCreateAttr with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *MomentCreateAttr) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MomentCreateAttr with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MomentCreateAttrMultiError, or nil if none found.
func (m *MomentCreateAttr) ValidateAll() error {
	return m.validate(true)
}

func (m *MomentCreateAttr) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MomentCreateAttrValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MomentCreateAttrValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MomentCreateAttrValidationError{
					field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Resource != nil {

		if all {
			switch v := interface{}(m.GetResource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MomentCreateAttrValidationError{
						field:  "Resource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MomentCreateAttrValidationError{
						field:  "Resource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MomentCreateAttrValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MomentCreateAttrMultiError(errors)
	}

	return nil
}

// MomentCreateAttrMultiError is an error wrapping multiple validation errors
// returned by MomentCreateAttr.ValidateAll() if the designated constraints
// aren't met.
type MomentCreateAttrMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MomentCreateAttrMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MomentCreateAttrMultiError) AllErrors() []error { return m }

// MomentCreateAttrValidationError is the validation error returned by
// MomentCreateAttr.Validate if the designated constraints aren't met.
type MomentCreateAttrValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MomentCreateAttrValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MomentCreateAttrValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MomentCreateAttrValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MomentCreateAttrValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MomentCreateAttrValidationError) ErrorName() string { return "MomentCreateAttrValidationError" }

// Error satisfies the builtin error interface
func (e MomentCreateAttrValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMomentCreateAttr.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MomentCreateAttrValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MomentCreateAttrValidationError{}

// Validate checks the field values on AttachmentText_AtUser with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AttachmentText_AtUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AttachmentText_AtUser with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AttachmentText_AtUserMultiError, or nil if none found.
func (m *AttachmentText_AtUser) ValidateAll() error {
	return m.validate(true)
}

func (m *AttachmentText_AtUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Nickname

	if len(errors) > 0 {
		return AttachmentText_AtUserMultiError(errors)
	}

	return nil
}

// AttachmentText_AtUserMultiError is an error wrapping multiple validation
// errors returned by AttachmentText_AtUser.ValidateAll() if the designated
// constraints aren't met.
type AttachmentText_AtUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AttachmentText_AtUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AttachmentText_AtUserMultiError) AllErrors() []error { return m }

// AttachmentText_AtUserValidationError is the validation error returned by
// AttachmentText_AtUser.Validate if the designated constraints aren't met.
type AttachmentText_AtUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AttachmentText_AtUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AttachmentText_AtUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AttachmentText_AtUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AttachmentText_AtUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AttachmentText_AtUserValidationError) ErrorName() string {
	return "AttachmentText_AtUserValidationError"
}

// Error satisfies the builtin error interface
func (e AttachmentText_AtUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAttachmentText_AtUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AttachmentText_AtUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AttachmentText_AtUserValidationError{}
