// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/capsule_types.proto

package api_items_story_types_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Capsule 玩法上下文
type StoryPlayCapsuleContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否消费过此 story
	IsConsumed    bool `protobuf:"varint,1,opt,name=is_consumed,json=isConsumed,proto3" json:"is_consumed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayCapsuleContext) Reset() {
	*x = StoryPlayCapsuleContext{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayCapsuleContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayCapsuleContext) ProtoMessage() {}

func (x *StoryPlayCapsuleContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayCapsuleContext.ProtoReflect.Descriptor instead.
func (*StoryPlayCapsuleContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryPlayCapsuleContext) GetIsConsumed() bool {
	if x != nil {
		return x.IsConsumed
	}
	return false
}

type CapsuleShootingResource struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Shooting         *Shooting              `protobuf:"bytes,1,opt,name=shooting,proto3" json:"shooting,omitempty"`
	ShootingResource *ShootingResource      `protobuf:"bytes,2,opt,name=shooting_resource,json=shootingResource,proto3" json:"shooting_resource,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CapsuleShootingResource) Reset() {
	*x = CapsuleShootingResource{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CapsuleShootingResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapsuleShootingResource) ProtoMessage() {}

func (x *CapsuleShootingResource) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapsuleShootingResource.ProtoReflect.Descriptor instead.
func (*CapsuleShootingResource) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{1}
}

func (x *CapsuleShootingResource) GetShooting() *Shooting {
	if x != nil {
		return x.Shooting
	}
	return nil
}

func (x *CapsuleShootingResource) GetShootingResource() *ShootingResource {
	if x != nil {
		return x.ShootingResource
	}
	return nil
}

type CapsuleAIScript struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 第几秒
	Seconds uint32 `protobuf:"varint,1,opt,name=seconds,proto3" json:"seconds,omitempty"`
	// AI 文本
	Question      *CapsuleQuestion `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CapsuleAIScript) Reset() {
	*x = CapsuleAIScript{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CapsuleAIScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapsuleAIScript) ProtoMessage() {}

func (x *CapsuleAIScript) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapsuleAIScript.ProtoReflect.Descriptor instead.
func (*CapsuleAIScript) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{2}
}

func (x *CapsuleAIScript) GetSeconds() uint32 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *CapsuleAIScript) GetQuestion() *CapsuleQuestion {
	if x != nil {
		return x.Question
	}
	return nil
}

type CapsulePhotoInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 几天内的图片
	InDays uint32 `protobuf:"varint,1,opt,name=in_days,json=inDays,proto3" json:"in_days,omitempty"`
	// 几个瞬间
	Moments       uint32 `protobuf:"varint,2,opt,name=moments,proto3" json:"moments,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CapsulePhotoInfo) Reset() {
	*x = CapsulePhotoInfo{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CapsulePhotoInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapsulePhotoInfo) ProtoMessage() {}

func (x *CapsulePhotoInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapsulePhotoInfo.ProtoReflect.Descriptor instead.
func (*CapsulePhotoInfo) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{3}
}

func (x *CapsulePhotoInfo) GetInDays() uint32 {
	if x != nil {
		return x.InDays
	}
	return 0
}

func (x *CapsulePhotoInfo) GetMoments() uint32 {
	if x != nil {
		return x.Moments
	}
	return 0
}

// Capsule 玩法配置
type StoryPlayCapsuleConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户上传的起始图片
	CoverImage *Resource `protobuf:"bytes,1,opt,name=cover_image,json=coverImage,proto3" json:"cover_image,omitempty"`
	// 用户拍摄的视频
	Video *Resource `protobuf:"bytes,2,opt,name=video,proto3" json:"video,omitempty"`
	// AI 脚本
	AiScripts []*CapsuleAIScript `protobuf:"bytes,3,rep,name=ai_scripts,json=aiScripts,proto3" json:"ai_scripts,omitempty"`
	// 客户端提供的数据
	PhotoInfo         *CapsulePhotoInfo   `protobuf:"bytes,4,opt,name=photo_info,json=photoInfo,proto3" json:"photo_info,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr `protobuf:"bytes,5,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayCapsuleConfig) Reset() {
	*x = StoryPlayCapsuleConfig{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayCapsuleConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayCapsuleConfig) ProtoMessage() {}

func (x *StoryPlayCapsuleConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayCapsuleConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayCapsuleConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{4}
}

func (x *StoryPlayCapsuleConfig) GetCoverImage() *Resource {
	if x != nil {
		return x.CoverImage
	}
	return nil
}

func (x *StoryPlayCapsuleConfig) GetVideo() *Resource {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *StoryPlayCapsuleConfig) GetAiScripts() []*CapsuleAIScript {
	if x != nil {
		return x.AiScripts
	}
	return nil
}

func (x *StoryPlayCapsuleConfig) GetPhotoInfo() *CapsulePhotoInfo {
	if x != nil {
		return x.PhotoInfo
	}
	return nil
}

func (x *StoryPlayCapsuleConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

// Capsule 玩法示例
type StoryPlayCapsuleExample struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommonInfo    *ExampleCommonInfo     `protobuf:"bytes,1,opt,name=common_info,json=commonInfo,proto3" json:"common_info,omitempty"`
	PhotoInfo     *CapsulePhotoInfo      `protobuf:"bytes,2,opt,name=photo_info,json=photoInfo,proto3" json:"photo_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayCapsuleExample) Reset() {
	*x = StoryPlayCapsuleExample{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayCapsuleExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayCapsuleExample) ProtoMessage() {}

func (x *StoryPlayCapsuleExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayCapsuleExample.ProtoReflect.Descriptor instead.
func (*StoryPlayCapsuleExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{5}
}

func (x *StoryPlayCapsuleExample) GetCommonInfo() *ExampleCommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *StoryPlayCapsuleExample) GetPhotoInfo() *CapsulePhotoInfo {
	if x != nil {
		return x.PhotoInfo
	}
	return nil
}

// ai 生成的问题和 tts
type CapsuleQuestion struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Question string                 `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	// 创建的时候不需要传此值
	TtsAudioUrl string `protobuf:"bytes,2,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	// 创建的时候必传
	TtsAudioKey string `protobuf:"bytes,3,opt,name=tts_audio_key,json=ttsAudioKey,proto3" json:"tts_audio_key,omitempty"`
	// 此字段暂时对端上无作用，消费时带回来即可
	Thinking string `protobuf:"bytes,4,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// 词粒度信息
	Words         []*Word `protobuf:"bytes,5,rep,name=words,proto3" json:"words,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CapsuleQuestion) Reset() {
	*x = CapsuleQuestion{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CapsuleQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapsuleQuestion) ProtoMessage() {}

func (x *CapsuleQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapsuleQuestion.ProtoReflect.Descriptor instead.
func (*CapsuleQuestion) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{6}
}

func (x *CapsuleQuestion) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *CapsuleQuestion) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *CapsuleQuestion) GetTtsAudioKey() string {
	if x != nil {
		return x.TtsAudioKey
	}
	return ""
}

func (x *CapsuleQuestion) GetThinking() string {
	if x != nil {
		return x.Thinking
	}
	return ""
}

func (x *CapsuleQuestion) GetWords() []*Word {
	if x != nil {
		return x.Words
	}
	return nil
}

type CapsuleQuestionWithUserAnswer struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Question *CapsuleQuestion       `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	// 用户回答的音频资源
	UserVoiceKey  string `protobuf:"bytes,2,opt,name=user_voice_key,json=userVoiceKey,proto3" json:"user_voice_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CapsuleQuestionWithUserAnswer) Reset() {
	*x = CapsuleQuestionWithUserAnswer{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CapsuleQuestionWithUserAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapsuleQuestionWithUserAnswer) ProtoMessage() {}

func (x *CapsuleQuestionWithUserAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapsuleQuestionWithUserAnswer.ProtoReflect.Descriptor instead.
func (*CapsuleQuestionWithUserAnswer) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{7}
}

func (x *CapsuleQuestionWithUserAnswer) GetQuestion() *CapsuleQuestion {
	if x != nil {
		return x.Question
	}
	return nil
}

func (x *CapsuleQuestionWithUserAnswer) GetUserVoiceKey() string {
	if x != nil {
		return x.UserVoiceKey
	}
	return ""
}

type ImageDetail struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 照片地址
	ObjectKey string `protobuf:"bytes,1,opt,name=object_key,json=objectKey,proto3" json:"object_key,omitempty"`
	// 照片拍摄纬度
	Latitude string `protobuf:"bytes,2,opt,name=latitude,proto3" json:"latitude,omitempty"`
	// 照片拍摄经度
	Longitude string `protobuf:"bytes,3,opt,name=longitude,proto3" json:"longitude,omitempty"`
	// 照片拍摄时间
	ShootingTimestamp uint32 `protobuf:"varint,4,opt,name=shooting_timestamp,json=shootingTimestamp,proto3" json:"shooting_timestamp,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ImageDetail) Reset() {
	*x = ImageDetail{}
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageDetail) ProtoMessage() {}

func (x *ImageDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_capsule_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageDetail.ProtoReflect.Descriptor instead.
func (*ImageDetail) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP(), []int{8}
}

func (x *ImageDetail) GetObjectKey() string {
	if x != nil {
		return x.ObjectKey
	}
	return ""
}

func (x *ImageDetail) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *ImageDetail) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *ImageDetail) GetShootingTimestamp() uint32 {
	if x != nil {
		return x.ShootingTimestamp
	}
	return 0
}

var File_api_items_story_types_v1_capsule_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_capsule_types_proto_rawDesc = "" +
	"\n" +
	",api/items/story/types/v1/capsule_types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\":\n" +
	"\x17StoryPlayCapsuleContext\x12\x1f\n" +
	"\vis_consumed\x18\x01 \x01(\bR\n" +
	"isConsumed\"\xb2\x01\n" +
	"\x17CapsuleShootingResource\x12>\n" +
	"\bshooting\x18\x01 \x01(\v2\".api.items.story.types.v1.ShootingR\bshooting\x12W\n" +
	"\x11shooting_resource\x18\x02 \x01(\v2*.api.items.story.types.v1.ShootingResourceR\x10shootingResource\"r\n" +
	"\x0fCapsuleAIScript\x12\x18\n" +
	"\aseconds\x18\x01 \x01(\rR\aseconds\x12E\n" +
	"\bquestion\x18\x02 \x01(\v2).api.items.story.types.v1.CapsuleQuestionR\bquestion\"E\n" +
	"\x10CapsulePhotoInfo\x12\x17\n" +
	"\ain_days\x18\x01 \x01(\rR\x06inDays\x12\x18\n" +
	"\amoments\x18\x02 \x01(\rR\amoments\"\x88\x03\n" +
	"\x16StoryPlayCapsuleConfig\x12C\n" +
	"\vcover_image\x18\x01 \x01(\v2\".api.items.story.types.v1.ResourceR\n" +
	"coverImage\x128\n" +
	"\x05video\x18\x02 \x01(\v2\".api.items.story.types.v1.ResourceR\x05video\x12H\n" +
	"\n" +
	"ai_scripts\x18\x03 \x03(\v2).api.items.story.types.v1.CapsuleAIScriptR\taiScripts\x12I\n" +
	"\n" +
	"photo_info\x18\x04 \x01(\v2*.api.items.story.types.v1.CapsulePhotoInfoR\tphotoInfo\x12Z\n" +
	"\x13moment_create_attrs\x18\x05 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\"\xb2\x01\n" +
	"\x17StoryPlayCapsuleExample\x12L\n" +
	"\vcommon_info\x18\x01 \x01(\v2+.api.items.story.types.v1.ExampleCommonInfoR\n" +
	"commonInfo\x12I\n" +
	"\n" +
	"photo_info\x18\x02 \x01(\v2*.api.items.story.types.v1.CapsulePhotoInfoR\tphotoInfo\"\xc7\x01\n" +
	"\x0fCapsuleQuestion\x12\x1a\n" +
	"\bquestion\x18\x01 \x01(\tR\bquestion\x12\"\n" +
	"\rtts_audio_url\x18\x02 \x01(\tR\vttsAudioUrl\x12\"\n" +
	"\rtts_audio_key\x18\x03 \x01(\tR\vttsAudioKey\x12\x1a\n" +
	"\bthinking\x18\x04 \x01(\tR\bthinking\x124\n" +
	"\x05words\x18\x05 \x03(\v2\x1e.api.items.story.types.v1.WordR\x05words\"\x8c\x01\n" +
	"\x1dCapsuleQuestionWithUserAnswer\x12E\n" +
	"\bquestion\x18\x01 \x01(\v2).api.items.story.types.v1.CapsuleQuestionR\bquestion\x12$\n" +
	"\x0euser_voice_key\x18\x02 \x01(\tR\fuserVoiceKey\"\x95\x01\n" +
	"\vImageDetail\x12\x1d\n" +
	"\n" +
	"object_key\x18\x01 \x01(\tR\tobjectKey\x12\x1a\n" +
	"\blatitude\x18\x02 \x01(\tR\blatitude\x12\x1c\n" +
	"\tlongitude\x18\x03 \x01(\tR\tlongitude\x12-\n" +
	"\x12shooting_timestamp\x18\x04 \x01(\rR\x11shootingTimestampB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_capsule_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_capsule_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_capsule_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_capsule_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_capsule_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_capsule_types_proto_rawDesc), len(file_api_items_story_types_v1_capsule_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_capsule_types_proto_rawDescData
}

var file_api_items_story_types_v1_capsule_types_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_items_story_types_v1_capsule_types_proto_goTypes = []any{
	(*StoryPlayCapsuleContext)(nil),       // 0: api.items.story.types.v1.StoryPlayCapsuleContext
	(*CapsuleShootingResource)(nil),       // 1: api.items.story.types.v1.CapsuleShootingResource
	(*CapsuleAIScript)(nil),               // 2: api.items.story.types.v1.CapsuleAIScript
	(*CapsulePhotoInfo)(nil),              // 3: api.items.story.types.v1.CapsulePhotoInfo
	(*StoryPlayCapsuleConfig)(nil),        // 4: api.items.story.types.v1.StoryPlayCapsuleConfig
	(*StoryPlayCapsuleExample)(nil),       // 5: api.items.story.types.v1.StoryPlayCapsuleExample
	(*CapsuleQuestion)(nil),               // 6: api.items.story.types.v1.CapsuleQuestion
	(*CapsuleQuestionWithUserAnswer)(nil), // 7: api.items.story.types.v1.CapsuleQuestionWithUserAnswer
	(*ImageDetail)(nil),                   // 8: api.items.story.types.v1.ImageDetail
	(*Shooting)(nil),                      // 9: api.items.story.types.v1.Shooting
	(*ShootingResource)(nil),              // 10: api.items.story.types.v1.ShootingResource
	(*Resource)(nil),                      // 11: api.items.story.types.v1.Resource
	(*MomentCreateAttr)(nil),              // 12: api.items.story.types.v1.MomentCreateAttr
	(*ExampleCommonInfo)(nil),             // 13: api.items.story.types.v1.ExampleCommonInfo
	(*Word)(nil),                          // 14: api.items.story.types.v1.Word
}
var file_api_items_story_types_v1_capsule_types_proto_depIdxs = []int32{
	9,  // 0: api.items.story.types.v1.CapsuleShootingResource.shooting:type_name -> api.items.story.types.v1.Shooting
	10, // 1: api.items.story.types.v1.CapsuleShootingResource.shooting_resource:type_name -> api.items.story.types.v1.ShootingResource
	6,  // 2: api.items.story.types.v1.CapsuleAIScript.question:type_name -> api.items.story.types.v1.CapsuleQuestion
	11, // 3: api.items.story.types.v1.StoryPlayCapsuleConfig.cover_image:type_name -> api.items.story.types.v1.Resource
	11, // 4: api.items.story.types.v1.StoryPlayCapsuleConfig.video:type_name -> api.items.story.types.v1.Resource
	2,  // 5: api.items.story.types.v1.StoryPlayCapsuleConfig.ai_scripts:type_name -> api.items.story.types.v1.CapsuleAIScript
	3,  // 6: api.items.story.types.v1.StoryPlayCapsuleConfig.photo_info:type_name -> api.items.story.types.v1.CapsulePhotoInfo
	12, // 7: api.items.story.types.v1.StoryPlayCapsuleConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	13, // 8: api.items.story.types.v1.StoryPlayCapsuleExample.common_info:type_name -> api.items.story.types.v1.ExampleCommonInfo
	3,  // 9: api.items.story.types.v1.StoryPlayCapsuleExample.photo_info:type_name -> api.items.story.types.v1.CapsulePhotoInfo
	14, // 10: api.items.story.types.v1.CapsuleQuestion.words:type_name -> api.items.story.types.v1.Word
	6,  // 11: api.items.story.types.v1.CapsuleQuestionWithUserAnswer.question:type_name -> api.items.story.types.v1.CapsuleQuestion
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_capsule_types_proto_init() }
func file_api_items_story_types_v1_capsule_types_proto_init() {
	if File_api_items_story_types_v1_capsule_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_capsule_types_proto_rawDesc), len(file_api_items_story_types_v1_capsule_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_capsule_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_capsule_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_capsule_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_capsule_types_proto = out.File
	file_api_items_story_types_v1_capsule_types_proto_goTypes = nil
	file_api_items_story_types_v1_capsule_types_proto_depIdxs = nil
}
