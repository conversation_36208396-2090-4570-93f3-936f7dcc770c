// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/wassup.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StoryPlayWassupConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayWassupConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayWassupConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayWassupConfigMultiError, or nil if none found.
func (m *StoryPlayWassupConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayWassupConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUnlockResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayWassupConfigValidationError{
					field:  "UnlockResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayWassupConfigValidationError{
					field:  "UnlockResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayWassupConfigValidationError{
				field:  "UnlockResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCoverImageTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayWassupConfigValidationError{
						field:  fmt.Sprintf("CoverImageTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayWassupConfigValidationError{
						field:  fmt.Sprintf("CoverImageTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayWassupConfigValidationError{
					field:  fmt.Sprintf("CoverImageTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCoverImageResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayWassupConfigValidationError{
					field:  "CoverImageResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayWassupConfigValidationError{
					field:  "CoverImageResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCoverImageResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayWassupConfigValidationError{
				field:  "CoverImageResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsMassCover

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayWassupConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayWassupConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayWassupConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayWassupConfigMultiError(errors)
	}

	return nil
}

// StoryPlayWassupConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayWassupConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayWassupConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayWassupConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayWassupConfigMultiError) AllErrors() []error { return m }

// StoryPlayWassupConfigValidationError is the validation error returned by
// StoryPlayWassupConfig.Validate if the designated constraints aren't met.
type StoryPlayWassupConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayWassupConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayWassupConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayWassupConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayWassupConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayWassupConfigValidationError) ErrorName() string {
	return "StoryPlayWassupConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayWassupConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayWassupConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayWassupConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayWassupConfigValidationError{}

// Validate checks the field values on StoryPlayWassupContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayWassupContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayWassupContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayWassupContextMultiError, or nil if none found.
func (m *StoryPlayWassupContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayWassupContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsUnlocked

	if len(errors) > 0 {
		return StoryPlayWassupContextMultiError(errors)
	}

	return nil
}

// StoryPlayWassupContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayWassupContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayWassupContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayWassupContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayWassupContextMultiError) AllErrors() []error { return m }

// StoryPlayWassupContextValidationError is the validation error returned by
// StoryPlayWassupContext.Validate if the designated constraints aren't met.
type StoryPlayWassupContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayWassupContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayWassupContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayWassupContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayWassupContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayWassupContextValidationError) ErrorName() string {
	return "StoryPlayWassupContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayWassupContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayWassupContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayWassupContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayWassupContextValidationError{}

// Validate checks the field values on StoryPlayWassupBotMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayWassupBotMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayWassupBotMessage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayWassupBotMessageMultiError, or nil if none found.
func (m *StoryPlayWassupBotMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayWassupBotMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for TtsAudioUrl

	for idx, item := range m.GetWords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayWassupBotMessageValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayWassupBotMessageValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayWassupBotMessageValidationError{
					field:  fmt.Sprintf("Words[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayWassupBotMessageMultiError(errors)
	}

	return nil
}

// StoryPlayWassupBotMessageMultiError is an error wrapping multiple validation
// errors returned by StoryPlayWassupBotMessage.ValidateAll() if the
// designated constraints aren't met.
type StoryPlayWassupBotMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayWassupBotMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayWassupBotMessageMultiError) AllErrors() []error { return m }

// StoryPlayWassupBotMessageValidationError is the validation error returned by
// StoryPlayWassupBotMessage.Validate if the designated constraints aren't met.
type StoryPlayWassupBotMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayWassupBotMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayWassupBotMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayWassupBotMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayWassupBotMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayWassupBotMessageValidationError) ErrorName() string {
	return "StoryPlayWassupBotMessageValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayWassupBotMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayWassupBotMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayWassupBotMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayWassupBotMessageValidationError{}
