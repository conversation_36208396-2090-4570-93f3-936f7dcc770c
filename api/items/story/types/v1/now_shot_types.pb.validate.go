// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/now_shot_types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StoryPlayNowShotContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayNowShotContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayNowShotContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayNowShotContextMultiError, or nil if none found.
func (m *StoryPlayNowShotContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayNowShotContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for StartTime

	for idx, item := range m.GetResource() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayNowShotContextValidationError{
						field:  fmt.Sprintf("Resource[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayNowShotContextValidationError{
						field:  fmt.Sprintf("Resource[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayNowShotContextValidationError{
					field:  fmt.Sprintf("Resource[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Ttl != nil {
		// no validation rules for Ttl
	}

	if len(errors) > 0 {
		return StoryPlayNowShotContextMultiError(errors)
	}

	return nil
}

// StoryPlayNowShotContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayNowShotContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayNowShotContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayNowShotContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayNowShotContextMultiError) AllErrors() []error { return m }

// StoryPlayNowShotContextValidationError is the validation error returned by
// StoryPlayNowShotContext.Validate if the designated constraints aren't met.
type StoryPlayNowShotContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayNowShotContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayNowShotContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayNowShotContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayNowShotContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayNowShotContextValidationError) ErrorName() string {
	return "StoryPlayNowShotContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayNowShotContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayNowShotContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayNowShotContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayNowShotContextValidationError{}

// Validate checks the field values on StoryPlayNowShotConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayNowShotConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayNowShotConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayNowShotConfigMultiError, or nil if none found.
func (m *StoryPlayNowShotConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayNowShotConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Caption

	// no validation rules for ResourceType

	// no validation rules for ResourceUrl

	// no validation rules for EndResourceType

	// no validation rules for EndResourceUrl

	// no validation rules for Ttl

	if all {
		switch v := interface{}(m.GetCommonConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayNowShotConfigValidationError{
					field:  "CommonConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayNowShotConfigValidationError{
					field:  "CommonConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayNowShotConfigValidationError{
				field:  "CommonConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayNowShotConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayNowShotConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayNowShotConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ThumbnailUrl != nil {
		// no validation rules for ThumbnailUrl
	}

	if m.EndThumbnailUrl != nil {
		// no validation rules for EndThumbnailUrl
	}

	if len(errors) > 0 {
		return StoryPlayNowShotConfigMultiError(errors)
	}

	return nil
}

// StoryPlayNowShotConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayNowShotConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayNowShotConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayNowShotConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayNowShotConfigMultiError) AllErrors() []error { return m }

// StoryPlayNowShotConfigValidationError is the validation error returned by
// StoryPlayNowShotConfig.Validate if the designated constraints aren't met.
type StoryPlayNowShotConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayNowShotConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayNowShotConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayNowShotConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayNowShotConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayNowShotConfigValidationError) ErrorName() string {
	return "StoryPlayNowShotConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayNowShotConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayNowShotConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayNowShotConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayNowShotConfigValidationError{}

// Validate checks the field values on StoryPlayNowShotExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayNowShotExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayNowShotExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayNowShotExampleMultiError, or nil if none found.
func (m *StoryPlayNowShotExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayNowShotExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommonInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayNowShotExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayNowShotExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayNowShotExampleValidationError{
				field:  "CommonInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayNowShotExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayNowShotExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayNowShotExampleValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayNowShotExampleMultiError(errors)
	}

	return nil
}

// StoryPlayNowShotExampleMultiError is an error wrapping multiple validation
// errors returned by StoryPlayNowShotExample.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayNowShotExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayNowShotExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayNowShotExampleMultiError) AllErrors() []error { return m }

// StoryPlayNowShotExampleValidationError is the validation error returned by
// StoryPlayNowShotExample.Validate if the designated constraints aren't met.
type StoryPlayNowShotExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayNowShotExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayNowShotExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayNowShotExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayNowShotExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayNowShotExampleValidationError) ErrorName() string {
	return "StoryPlayNowShotExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayNowShotExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayNowShotExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayNowShotExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayNowShotExampleValidationError{}

// Validate checks the field values on StoryPlayNowShotExample_Case with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayNowShotExample_Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayNowShotExample_Case with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayNowShotExample_CaseMultiError, or nil if none found.
func (m *StoryPlayNowShotExample_Case) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayNowShotExample_Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CoverImageUrl

	if len(errors) > 0 {
		return StoryPlayNowShotExample_CaseMultiError(errors)
	}

	return nil
}

// StoryPlayNowShotExample_CaseMultiError is an error wrapping multiple
// validation errors returned by StoryPlayNowShotExample_Case.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayNowShotExample_CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayNowShotExample_CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayNowShotExample_CaseMultiError) AllErrors() []error { return m }

// StoryPlayNowShotExample_CaseValidationError is the validation error returned
// by StoryPlayNowShotExample_Case.Validate if the designated constraints
// aren't met.
type StoryPlayNowShotExample_CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayNowShotExample_CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayNowShotExample_CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayNowShotExample_CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayNowShotExample_CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayNowShotExample_CaseValidationError) ErrorName() string {
	return "StoryPlayNowShotExample_CaseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayNowShotExample_CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayNowShotExample_Case.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayNowShotExample_CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayNowShotExample_CaseValidationError{}
