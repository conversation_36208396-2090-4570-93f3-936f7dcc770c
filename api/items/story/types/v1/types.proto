syntax = "proto3";

package api.items.story.types.v1;

import "api/users/info/types/v1/types.proto";
import "api/items/story/types/v1/turtle_soup_types.proto";
import "api/items/story/types/v1/now_shot_types.proto";
import "api/items/story/types/v1/base_types.proto";
import "api/items/story/types/v1/base_play_story_types.proto";
import "api/items/story/types/v1/privacy_setting.proto";
import "api/items/story/types/v1/roasted.types.proto";
import "api/items/story/types/v1/wassup.types.proto";
import "api/items/story/types/v1/capsule_types.proto";
import "api/items/story/reaction/types/v1/types.proto";
import "api/items/story/types/v1/hide.types.proto";
import "api/items/story/types/v1/chatproxy.types.proto";
import "api/items/story/types/v1/who.types.proto";
import "api/items/story/types/v1/haunt.types.proto";
import "api/items/story/types/v1/pin.types.proto";


option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";

enum StoryStatus {
	STORY_STATUS_UNSPECIFIED = 0;
	STORY_STATUS_PUBLISHED = 1;
	// 删除
	STORY_STATUS_DELETED = 2;
	// 创建中
	STORY_STATUS_GENERATING = 3;
	// 生成失败
	STORY_STATUS_GENERATION_FAILED = 4;
}

message StoryStats {
	// 该 story 的 reaction 统计
	api.items.story.reaction.types.v1.ReactionMadeStatSummary reaction_made_stat_summary = 1;
	// Share count
	uint32 share_stat = 2;
}

message StorySummary {
	// 创作时，不要传
	string id = 1;
	api.users.info.types.v1.UserInfoSummary author = 2;
	// 游玩类型，参考 StoryPlayType
	string play_type = 3;
	// 游玩模式，换图玩法时，参考 ExchangeImagePlayMode
	// 当且仅当换图玩法时，此字段有效
	string sub_play_type = 4;

	StoryStatus status = 24;

	uint32 created_at_timestamp = 5;

	// 换图玩法示例，当且仅当换图玩法时，此字段有效
	StoryPlayExchangeImageExample exchange_image_example = 6;
	// 海龟汤玩法示例，当且仅当海龟汤玩法时，此字段有效
	StoryPlayTurtleSoupExample turtle_soup_example = 7;
	// Unmute 玩法示例，当且仅当Unmute 玩法时，此字段有效
	StoryPlayUnmuteExample unmute_example = 8;
	// 海龟汤 mass 玩法示例，当且仅当海龟汤 mass 玩法时，此字段有效
	StoryPlayTurtleSoupMassExample turtle_soup_mass_example = 9;
	// BasePlay 玩法示例，当且仅当BasePlay 玩法时，此字段有效
	StoryPlayBasePlayExample base_play_example = 10;

	// 给站外分享用的封面图
	string cover_image_url = 11;
	uint32 cover_image_width = 12;
	uint32 cover_image_height = 13;
	string cover_type = 23;

	// 隐私设置
	PrivacySetting privacy_setting = 14;

	// NowShot 玩法示例，当且仅当 NowShot 玩法时，此字段有效
	StoryPlayNowShotExample now_shot_play_example = 15;
	// roasted 玩法示例，当且仅当 roasted 玩法时，此字段有效
	StoryPlayRoastedExample roasted_example = 16;
	// capsule 玩法示例，当且仅当 capsule 玩法时，此字段有效
	StoryPlayCapsuleExample capsule_example = 18;

	// 该 story 的 stats
	StoryStats stats = 19;

	repeated api.items.story.reaction.types.v1.Reaction login_user_made_reactions = 20;

	message UnlockedUsersInfo {
		repeated api.users.info.types.v1.UserInfoSummary users = 1;
		uint32 total_count = 3;
		bool has_more = 4;
	}
	UnlockedUsersInfo unlocked_users = 21;
	bool has_unlocked = 22;

	// 当且仅当这个对象被推荐引擎露出时，才有此字段
	string sort_request_id = 100;
}

message StoryPlayExchangeImageExample {
	ExampleCommonInfo common_info = 1;
	message Case {
		// 待交换的图片 url
		string exchange_image_url = 2;
		// ai 的回复
		string ai_response = 3;
	}
	// 待交换的图片 url
	repeated Case cases = 2;
}

message StoryPlayUnmuteExample {
	ExampleCommonInfo common_info = 1;
	message Case {
		string prompt = 1;
	}
	repeated Case cases = 2;
}

message PortalBasicInfo {
  // Portal ID
  string portal_id = 1;
  // Portal 下的 moment 总数
  uint32 moment_count = 2;
}

message StoryDetail {
	StorySummary summary = 1;
	oneof play_config {
		// 换图玩法配置
		StoryPlayExchangeImageConfig exchange_image_config = 2;
		// 海龟汤玩法配置
		StoryPlayTurtleSoupConfig turtle_soup_config = 3;
		// Unmute 玩法配置
		StoryPlayUnmuteConfig unmute_config = 6;
		// 海龟汤 mass 玩法配置
		StoryPlayTurtleSoupMassConfig turtle_soup_mass_config = 8;
		// BasePlay 玩法配置
		StoryPlayBasePlayConfig base_play_config = 9;
		// NowShot 玩法配置
		StoryPlayNowShotConfig now_shot_config = 12;
		// roasted 玩法配置
		StoryPlayRoastedConfig roasted_config = 14;
		// wassup 玩法配置
		StoryPlayWassupConfig wassup_config = 16;
		// capsule 玩法配置
		StoryPlayCapsuleConfig capsule_config = 18;
		// hide 玩法配置
		StoryPlayHideConfig hide_config = 20;
		// chatproxy 玩法配置
		StoryPlayChatProxyConfig chatproxy_config = 22;
		// Who 玩法配置
		WhoStoryPlayConfig who_config = 24;
		// Haunt 玩法配置
		HauntPlayConfig haunt_config = 26;
		// Pin 玩法配置
		StoryPinConfig pin_config = 28;
	}
	oneof play_context {
		// 换图玩法上下文
		StoryPlayExchangeImageContext exchange_image_context = 4;
		// 海龟汤玩法上下文
		StoryPlayTurtleSoupContext turtle_soup_context = 5;
		// Unmute 玩法上下文
		StoryPlayUnmuteContext unmute_context = 7;
		// 海龟汤 mass 玩法上下文
		StoryPlayTurtleSoupMassContext turtle_soup_mass_context = 10;
		// BasePlay 玩法上下文
		StoryPlayBasePlayContext base_play_context = 11;
		// NowShot 玩法上下文
		StoryPlayNowShotContext now_shot_context = 13;
		// roasted 玩法上下文
		StoryPlayRoastedContext roasted_context = 15;
		// wassup 玩法上下文
		StoryPlayWassupContext wassup_context = 17;
		// capsule 玩法上下文
		StoryPlayCapsuleContext capsule_context = 19;
		// hide 玩法上下文
		StoryPlayHideContext hide_context = 21;
		// chatproxy 玩法上下文
		StoryPlayChatProxyContext chatproxy_context = 23;
		// Who 玩法上下文
		WhoStoryPlayContext who_context = 25;
		// Haunt 玩法上下文
		HauntPlayContext haunt_context = 27;
		// Pin 玩法上下文
		StoryPinContext pin_context = 29;
	}

	// 这是个临时字段，由于开发时间太紧张了，需要在所有的
	// unlock 接口内返回加入的 portal id 字段，无法通过
	// 接口返回，所以临时加了这个字段，后续需要删除 @Larry 
	optional string enabled_portal_id = 30;

	// 针对 haunt 的展示信息
	api.items.story.types.v1.HauntBooShowInfo haunt_boo_show_info = 31;
	// 关联的Portal基础信息
	PortalBasicInfo portal_basic_info = 32;
}


// story 游玩类型
enum StoryPlayType {
	STORY_PLAY_TYPE_UNSPECIFIED = 0;
	// 换图玩法
	STORY_PLAY_TYPE_EXCHANGE_IMAGE = 1;
	// 海龟汤玩法
	STORY_PLAY_TYPE_TURTLE_SOUP = 2;
	// Unmute玩法
	STORY_PLAY_TYPE_UNMUTE = 3;
	// 海龟汤 mass 玩法
	STORY_PLAY_TYPE_TURTLE_SOUP_MASS = 4;
	// BasePlay 玩法
	STORY_PLAY_TYPE_BASE_PLAY = 5;
	// NowShot 玩法
	STORY_PLAY_TYPE_NOW_SHOT = 6;
	// roasted
	STORY_PLAY_TYPE_ROASTED = 7;
	// // wassup 玩法
	// STORY_PLAY_TYPE_WASSUP = 8;
	// capsule 玩法
	STORY_PLAY_TYPE_CAPSULE = 9;
	// hide 玩法
	STORY_PLAY_TYPE_HIDE = 10;
	// chatproxy 玩法
	STORY_PLAY_TYPE_CHATPROXY = 11;
	// Who 玩法
	STORY_PLAY_TYPE_WHO = 12;
	// wassup v2 玩法
	STORY_PLAY_TYPE_WASSUP_V2 = 13;
	// Haunt 玩法
	STORY_PLAY_TYPE_HAUNT = 14;
	// Story Pin 玩法
	STORY_PLAY_TYPE_PIN = 15;
}

enum ExchangeImageMatchStatus {
	EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED = 0;
	// 未 match
	EXCHANGE_IMAGE_MATCH_STATUS_NOT_MATCHED = 1;
	// 部分 match
	EXCHANGE_IMAGE_MATCH_STATUS_PARTIALLY_MATCHED = 2;
	// 完全 match
	EXCHANGE_IMAGE_MATCH_STATUS_MATCHED = 3;
}

enum UnmuteMatchStatus {
	UNMUTE_MATCH_STATUS_UNSPECIFIED = 0;
	// 未 match
	UNMUTE_MATCH_STATUS_NOT_MATCHED = 1;
	// 部分 match
	UNMUTE_MATCH_STATUS_PARTIALLY_MATCHED = 2;
	// 完全 match
	UNMUTE_MATCH_STATUS_MATCHED = 3;
}

enum TemplateType {
	TEMPLATE_TYPE_UNSPECIFIED = 0;
	// 系统自带
	TEMPLATE_TYPE_SYSTEM = 1;
	// 用户自定义
	TEMPLATE_TYPE_USER_DIY = 2;
}

message StoryExchangeImageConditionTemplate {
	string id = 1;
	// 封面图片，创建时，客户端传 key
	string cover_image_url = 2;
	StoryPlayExchangeImageCondition default_condition = 3;
	TemplateType typ = 4;
}

message UnmutePromptStyle {
	// prompt text
	string text = 1;
	// normalized position
	string x = 2;
	// normalized position
	string y = 3;
	// font size
	uint32 font_size = 4;
	// font name
	string font_name = 5;
	// font color
	string color = 6;
	// prompt height
	string height = 7;
	// prompt width
	string width = 8;
}

message StoryPlayUnmuteConfig {
	// public caption 提示 for human
	AttachmentText prompt = 1;
	enum ResourceType {
		RESOURCE_TYPE_UNSPECIFIED = 0;
		RESOURCE_TYPE_IMAGE = 1;
		RESOURCE_TYPE_VIDEO = 2;
	}
	// 资源类型，参考 ResourceType
	string resource_type = 2;
	// 资源 url,第一段（封面内容）：公开展示，用于吸引点击；给出引导;创建时，客户端传 key
	string resource_url = 3;
	// 结束资源类型，参考 ResourceType
	string end_resource_type = 4;
	// 结束资源 url，第二段（隐藏内容）：默认打码，需通过语音互动完成后自动揭晓;创建时，客户端传 key
	string end_resource_url = 5;
	// 自定义 ai 规则
	message CustomAiResponse {
		string rule_description = 1;
		string rule_result = 2;
	}
	repeated CustomAiResponse custom_ai_responses = 6;
	// 最大尝试次数
	uint32 max_try_count = 7;
	// 视频首帧，当且仅当资源类型为视频时才有
	optional string thumbnail_url = 8;
	// 结束视频首帧，当且仅当资源类型为视频时才有
	optional string end_thumbnail_url = 9;
	// intention for llm
	string intention = 10;
	CommonPlayConfig common_play_config = 11;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 12;
}

message CommonStoryPlayConditionTemplate {
	string id = 1;
	Condition condition = 2;
}

message StoryPlayTurtleConditionTemplate {
	string id = 1;
	string summary = 2;
	// 封面图片，创建时，客户端传 key
	string cover_image_url = 3;
	string caption = 4;
	string intent_prompt = 5;
	TemplateType typ = 6;
}

message StoryPlayUnmuteConditionTemplate {
	string id = 1;
	StoryPlayUnmuteCondition default_condition = 2;
	TemplateType typ = 3;
	// 封面图片，创建时，客户端传 key
	string cover_image_url = 4;
}

message StoryPlayUnmuteCondition {
	string prompt = 1;
}

// 换图玩法解锁条件
message StoryPlayExchangeImageCondition {
	// 提示
	string tips = 1;
	// 正向引导
	string positive_guide = 2;
	// 正向引导图片
	repeated string positive_image_urls = 3;
	// 正向引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
	repeated string positive_image_keys = 4;
	// 部分匹配引导
	string partial_match_guide = 5;
	// 部分匹配引导图片
	repeated string partial_match_image_urls = 6;
	// 部分匹配引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
	repeated string partial_match_image_keys = 7;
	// 负向引导
	string negative_guide = 8;
	// 负向引导图片
	repeated string negative_image_urls = 9;
	// 负向引导图片的 key ，这是为了使用模板时客户端可以直接使用该字段
	repeated string negative_image_keys = 10;
	// llm 的 prompt，即 Creator Criteria 
	string llm_prompt = 11;
}

enum ContextStatus {
	CONTEXT_STATUS_UNSPECIFIED = 0;
	// Played
	CONTEXT_STATUS_NORMAL = 1;
	CONTEXT_STATUS_DELETED = 2;
	CONTEXT_STATUS_UNLOCKED = 3;
}

// Unmute 玩法上下文
message StoryPlayUnmuteContext {
	// 是否已经完成
	bool is_finished = 1;
	// ai 的回复
	string ai_response = 2;
	// 尝试次数
	uint32 try_count = 3;
	// 历史尝试音频
	repeated string audio_keys = 4;
}

// 换图玩法上下文
message StoryPlayExchangeImageContext {
	// 用户当前玩到的 node id
	// 主要是为了服务线性模式下，用户处于的状态
	// 如果马赛克模式，此字段为固定值，永远为第一个 node id
	string current_node_id = 1;
	// 用户在当前 story 下的各个node尝试次数
	// key 为 node_id，value 为尝试次数
	// all values 的和应该等于当前 story 的总游玩次数
	map<string, uint32> current_try_count = 2;
	// 用户在各个 node 下的完成率
	// 如果是换图模式，用户完成了这个 node，则固定为 1，否则为 0 
	// 如果是马赛克模式，就可以表达马赛克的消除程度
	map<string, uint32> current_success_progress = 3;
	// 是否已经通关
	bool is_finished = 4;
	// 用户在各个 node 下的尝试记录
	message UserExchangeImageUrls {
		string node_id = 1;
		repeated string image_urls = 2;
	}
	// 用户在各个 node 下的尝试记录
	repeated UserExchangeImageUrls user_exchange_image_urls = 5;
	// 尝试次数
	uint32 try_count = 6;
	repeated string user_trial_image_urls = 7;
	repeated string user_trial_image_keys = 8;
}


enum ExchangeImagePlayMode {
	PLAY_MODE_UNSPECIFIED = 0;
	// 马赛克玩法
	PLAY_MODE_MASSE = 1;
	// 线性解锁
	PLAY_MODE_LINEAR = 2;
}

// 换图玩法配置
message StoryPlayExchangeImageConfig {
	// 换图玩法类型，参考 ExchangeImagePlayMode
	string play_mode = 1;
	// 换图玩法里的节点
	message Node {
		// 节点 id，由端上写入时确保唯一
		string id = 1;
		// V1 视频首帧，当且仅当资源类型为视频时才有，已废弃，使用resource
		optional string thumbnail_url = 2; 
		// V1 资源类型，已废弃，使用resource
		enum ResourceType {
			RESOURCE_TYPE_UNSPECIFIED = 0;
			RESOURCE_TYPE_IMAGE = 1;
			RESOURCE_TYPE_VIDEO = 2;
		}
		// V1 资源类型，参考 ResourceType，已废弃，使用resource
		string resource_type = 3;
		// V1 资源 url，创建时，客户端传 key，已废弃，使用resource
		string resource_url = 4;
		// 条件，进入下一个节点的条件
		StoryPlayExchangeImageCondition condition = 6;
		// 是否允许使用相册
		bool allow_use_album = 7;
		// 最多允许尝试多少次
		uint32 max_try_count = 8;
		bool need_create_diy_template = 9;
	}
	// 节点列表，按照先后顺序排序，其中，节点的 next_node_id 为空时，表示该节点为终点
	// 如果 play mode 为马赛克时，此数组应该只有一个 element
	repeated Node nodes = 2;
	CommonPlayConfig common_play_config = 3;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 4;
}
