// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/haunt.types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/users/boo/types/v1"
	v11 "boson/api/users/info/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HauntBooShowInfoSence int32

const (
	HauntBooShowInfoSence_SHOW_INFO_SENCE_UNSPECIFIED HauntBooShowInfoSence = 0
	// feed tab1 污染
	HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION HauntBooShowInfoSence = 1
	// 内容污染
	HauntBooShowInfoSence_SHOW_INFO_SENCE_CONTENT_POLLUTION HauntBooShowInfoSence = 2
)

// Enum value maps for HauntBooShowInfoSence.
var (
	HauntBooShowInfoSence_name = map[int32]string{
		0: "SHOW_INFO_SENCE_UNSPECIFIED",
		1: "SHOW_INFO_SENCE_FEED_POLLUTION",
		2: "SHOW_INFO_SENCE_CONTENT_POLLUTION",
	}
	HauntBooShowInfoSence_value = map[string]int32{
		"SHOW_INFO_SENCE_UNSPECIFIED":       0,
		"SHOW_INFO_SENCE_FEED_POLLUTION":    1,
		"SHOW_INFO_SENCE_CONTENT_POLLUTION": 2,
	}
)

func (x HauntBooShowInfoSence) Enum() *HauntBooShowInfoSence {
	p := new(HauntBooShowInfoSence)
	*p = x
	return p
}

func (x HauntBooShowInfoSence) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HauntBooShowInfoSence) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_haunt_types_proto_enumTypes[0].Descriptor()
}

func (HauntBooShowInfoSence) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_haunt_types_proto_enumTypes[0]
}

func (x HauntBooShowInfoSence) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HauntBooShowInfoSence.Descriptor instead.
func (HauntBooShowInfoSence) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{0}
}

type HauntQuestion struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Title string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 填充默认值
	Description   string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	IsRequired    bool   `protobuf:"varint,3,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HauntQuestion) Reset() {
	*x = HauntQuestion{}
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HauntQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HauntQuestion) ProtoMessage() {}

func (x *HauntQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HauntQuestion.ProtoReflect.Descriptor instead.
func (*HauntQuestion) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{0}
}

func (x *HauntQuestion) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *HauntQuestion) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *HauntQuestion) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

type HauntQuestionWithAnswer struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Question      *HauntQuestion         `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	Answer        string                 `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HauntQuestionWithAnswer) Reset() {
	*x = HauntQuestionWithAnswer{}
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HauntQuestionWithAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HauntQuestionWithAnswer) ProtoMessage() {}

func (x *HauntQuestionWithAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HauntQuestionWithAnswer.ProtoReflect.Descriptor instead.
func (*HauntQuestionWithAnswer) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{1}
}

func (x *HauntQuestionWithAnswer) GetQuestion() *HauntQuestion {
	if x != nil {
		return x.Question
	}
	return nil
}

func (x *HauntQuestionWithAnswer) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

type HauntBoo struct {
	state                protoimpl.MessageState     `protogen:"open.v1"`
	Id                   string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Avatar               *v1.Avatar                 `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Creator              *v11.UserInfoSummary       `protobuf:"bytes,3,opt,name=creator,proto3" json:"creator,omitempty"`
	GeneratedHint        string                     `protobuf:"bytes,4,opt,name=generated_hint,json=generatedHint,proto3" json:"generated_hint,omitempty"`
	VideoUrl             string                     `protobuf:"bytes,5,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty"`
	CreatorBoo           bool                       `protobuf:"varint,6,opt,name=creator_boo,json=creatorBoo,proto3" json:"creator_boo,omitempty"`
	QuestionsWithAnswers []*HauntQuestionWithAnswer `protobuf:"bytes,7,rep,name=questions_with_answers,json=questionsWithAnswers,proto3" json:"questions_with_answers,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *HauntBoo) Reset() {
	*x = HauntBoo{}
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HauntBoo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HauntBoo) ProtoMessage() {}

func (x *HauntBoo) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HauntBoo.ProtoReflect.Descriptor instead.
func (*HauntBoo) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{2}
}

func (x *HauntBoo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *HauntBoo) GetAvatar() *v1.Avatar {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *HauntBoo) GetCreator() *v11.UserInfoSummary {
	if x != nil {
		return x.Creator
	}
	return nil
}

func (x *HauntBoo) GetGeneratedHint() string {
	if x != nil {
		return x.GeneratedHint
	}
	return ""
}

func (x *HauntBoo) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *HauntBoo) GetCreatorBoo() bool {
	if x != nil {
		return x.CreatorBoo
	}
	return false
}

func (x *HauntBoo) GetQuestionsWithAnswers() []*HauntQuestionWithAnswer {
	if x != nil {
		return x.QuestionsWithAnswers
	}
	return nil
}

type HauntPlayConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 第一个永远是作者自己的 Boo
	BoosWithQuestionsAndAnswers []*HauntBoo       `protobuf:"bytes,1,rep,name=boos_with_questions_and_answers,json=boosWithQuestionsAndAnswers,proto3" json:"boos_with_questions_and_answers,omitempty"`
	Captions                    []*AttachmentText `protobuf:"bytes,4,rep,name=captions,proto3" json:"captions,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *HauntPlayConfig) Reset() {
	*x = HauntPlayConfig{}
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HauntPlayConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HauntPlayConfig) ProtoMessage() {}

func (x *HauntPlayConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HauntPlayConfig.ProtoReflect.Descriptor instead.
func (*HauntPlayConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{3}
}

func (x *HauntPlayConfig) GetBoosWithQuestionsAndAnswers() []*HauntBoo {
	if x != nil {
		return x.BoosWithQuestionsAndAnswers
	}
	return nil
}

func (x *HauntPlayConfig) GetCaptions() []*AttachmentText {
	if x != nil {
		return x.Captions
	}
	return nil
}

type HauntPlayContext struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TryCount      uint32                 `protobuf:"varint,1,opt,name=try_count,json=tryCount,proto3" json:"try_count,omitempty"`
	Unlocked      bool                   `protobuf:"varint,2,opt,name=unlocked,proto3" json:"unlocked,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HauntPlayContext) Reset() {
	*x = HauntPlayContext{}
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HauntPlayContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HauntPlayContext) ProtoMessage() {}

func (x *HauntPlayContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HauntPlayContext.ProtoReflect.Descriptor instead.
func (*HauntPlayContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{4}
}

func (x *HauntPlayContext) GetTryCount() uint32 {
	if x != nil {
		return x.TryCount
	}
	return 0
}

func (x *HauntPlayContext) GetUnlocked() bool {
	if x != nil {
		return x.Unlocked
	}
	return false
}

type HauntBooShowInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 第一个永远是 creator boo
	HauntBoos []*HauntBoo `protobuf:"bytes,1,rep,name=haunt_boos,json=hauntBoos,proto3" json:"haunt_boos,omitempty"`
	// 此次展现来源于哪个 Story
	FromHauntStoryId string `protobuf:"bytes,2,opt,name=from_haunt_story_id,json=fromHauntStoryId,proto3" json:"from_haunt_story_id,omitempty"`
	// 展示场景
	ShowInfoSence HauntBooShowInfoSence `protobuf:"varint,3,opt,name=show_info_sence,json=showInfoSence,proto3,enum=api.items.story.types.v1.HauntBooShowInfoSence" json:"show_info_sence,omitempty"`
	// 最后一次展示时间，标准 unixstamp
	LastShowAtInUnixstamp uint32 `protobuf:"varint,4,opt,name=last_show_at_in_unixstamp,json=lastShowAtInUnixstamp,proto3" json:"last_show_at_in_unixstamp,omitempty"`
	// 针对登录 session 的当日展现次数
	TodayShowCount uint32 `protobuf:"varint,5,opt,name=today_show_count,json=todayShowCount,proto3" json:"today_show_count,omitempty"`
	// 针对登录 session 这个 story 已经尝试了几次了
	TriedCount    uint32 `protobuf:"varint,6,opt,name=tried_count,json=triedCount,proto3" json:"tried_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HauntBooShowInfo) Reset() {
	*x = HauntBooShowInfo{}
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HauntBooShowInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HauntBooShowInfo) ProtoMessage() {}

func (x *HauntBooShowInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_haunt_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HauntBooShowInfo.ProtoReflect.Descriptor instead.
func (*HauntBooShowInfo) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP(), []int{5}
}

func (x *HauntBooShowInfo) GetHauntBoos() []*HauntBoo {
	if x != nil {
		return x.HauntBoos
	}
	return nil
}

func (x *HauntBooShowInfo) GetFromHauntStoryId() string {
	if x != nil {
		return x.FromHauntStoryId
	}
	return ""
}

func (x *HauntBooShowInfo) GetShowInfoSence() HauntBooShowInfoSence {
	if x != nil {
		return x.ShowInfoSence
	}
	return HauntBooShowInfoSence_SHOW_INFO_SENCE_UNSPECIFIED
}

func (x *HauntBooShowInfo) GetLastShowAtInUnixstamp() uint32 {
	if x != nil {
		return x.LastShowAtInUnixstamp
	}
	return 0
}

func (x *HauntBooShowInfo) GetTodayShowCount() uint32 {
	if x != nil {
		return x.TodayShowCount
	}
	return 0
}

func (x *HauntBooShowInfo) GetTriedCount() uint32 {
	if x != nil {
		return x.TriedCount
	}
	return 0
}

var File_api_items_story_types_v1_haunt_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_haunt_types_proto_rawDesc = "" +
	"\n" +
	"*api/items/story/types/v1/haunt.types.proto\x12\x18api.items.story.types.v1\x1a#api/users/info/types/v1/types.proto\x1a\"api/users/boo/types/v1/types.proto\x1a)api/items/story/types/v1/base_types.proto\"h\n" +
	"\rHauntQuestion\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x1f\n" +
	"\vis_required\x18\x03 \x01(\bR\n" +
	"isRequired\"v\n" +
	"\x17HauntQuestionWithAnswer\x12C\n" +
	"\bquestion\x18\x01 \x01(\v2'.api.items.story.types.v1.HauntQuestionR\bquestion\x12\x16\n" +
	"\x06answer\x18\x02 \x01(\tR\x06answer\"\xe4\x02\n" +
	"\bHauntBoo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x126\n" +
	"\x06avatar\x18\x02 \x01(\v2\x1e.api.users.boo.types.v1.AvatarR\x06avatar\x12B\n" +
	"\acreator\x18\x03 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\acreator\x12%\n" +
	"\x0egenerated_hint\x18\x04 \x01(\tR\rgeneratedHint\x12\x1b\n" +
	"\tvideo_url\x18\x05 \x01(\tR\bvideoUrl\x12\x1f\n" +
	"\vcreator_boo\x18\x06 \x01(\bR\n" +
	"creatorBoo\x12g\n" +
	"\x16questions_with_answers\x18\a \x03(\v21.api.items.story.types.v1.HauntQuestionWithAnswerR\x14questionsWithAnswers\"\xc1\x01\n" +
	"\x0fHauntPlayConfig\x12h\n" +
	"\x1fboos_with_questions_and_answers\x18\x01 \x03(\v2\".api.items.story.types.v1.HauntBooR\x1bboosWithQuestionsAndAnswers\x12D\n" +
	"\bcaptions\x18\x04 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\bcaptions\"K\n" +
	"\x10HauntPlayContext\x12\x1b\n" +
	"\ttry_count\x18\x01 \x01(\rR\btryCount\x12\x1a\n" +
	"\bunlocked\x18\x02 \x01(\bR\bunlocked\"\xe2\x02\n" +
	"\x10HauntBooShowInfo\x12A\n" +
	"\n" +
	"haunt_boos\x18\x01 \x03(\v2\".api.items.story.types.v1.HauntBooR\thauntBoos\x12-\n" +
	"\x13from_haunt_story_id\x18\x02 \x01(\tR\x10fromHauntStoryId\x12W\n" +
	"\x0fshow_info_sence\x18\x03 \x01(\x0e2/.api.items.story.types.v1.HauntBooShowInfoSenceR\rshowInfoSence\x128\n" +
	"\x19last_show_at_in_unixstamp\x18\x04 \x01(\rR\x15lastShowAtInUnixstamp\x12(\n" +
	"\x10today_show_count\x18\x05 \x01(\rR\x0etodayShowCount\x12\x1f\n" +
	"\vtried_count\x18\x06 \x01(\rR\n" +
	"triedCount*\x83\x01\n" +
	"\x15HauntBooShowInfoSence\x12\x1f\n" +
	"\x1bSHOW_INFO_SENCE_UNSPECIFIED\x10\x00\x12\"\n" +
	"\x1eSHOW_INFO_SENCE_FEED_POLLUTION\x10\x01\x12%\n" +
	"!SHOW_INFO_SENCE_CONTENT_POLLUTION\x10\x02B9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_haunt_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_haunt_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_haunt_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_haunt_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_haunt_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_haunt_types_proto_rawDesc), len(file_api_items_story_types_v1_haunt_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_haunt_types_proto_rawDescData
}

var file_api_items_story_types_v1_haunt_types_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_items_story_types_v1_haunt_types_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_items_story_types_v1_haunt_types_proto_goTypes = []any{
	(HauntBooShowInfoSence)(0),      // 0: api.items.story.types.v1.HauntBooShowInfoSence
	(*HauntQuestion)(nil),           // 1: api.items.story.types.v1.HauntQuestion
	(*HauntQuestionWithAnswer)(nil), // 2: api.items.story.types.v1.HauntQuestionWithAnswer
	(*HauntBoo)(nil),                // 3: api.items.story.types.v1.HauntBoo
	(*HauntPlayConfig)(nil),         // 4: api.items.story.types.v1.HauntPlayConfig
	(*HauntPlayContext)(nil),        // 5: api.items.story.types.v1.HauntPlayContext
	(*HauntBooShowInfo)(nil),        // 6: api.items.story.types.v1.HauntBooShowInfo
	(*v1.Avatar)(nil),               // 7: api.users.boo.types.v1.Avatar
	(*v11.UserInfoSummary)(nil),     // 8: api.users.info.types.v1.UserInfoSummary
	(*AttachmentText)(nil),          // 9: api.items.story.types.v1.AttachmentText
}
var file_api_items_story_types_v1_haunt_types_proto_depIdxs = []int32{
	1, // 0: api.items.story.types.v1.HauntQuestionWithAnswer.question:type_name -> api.items.story.types.v1.HauntQuestion
	7, // 1: api.items.story.types.v1.HauntBoo.avatar:type_name -> api.users.boo.types.v1.Avatar
	8, // 2: api.items.story.types.v1.HauntBoo.creator:type_name -> api.users.info.types.v1.UserInfoSummary
	2, // 3: api.items.story.types.v1.HauntBoo.questions_with_answers:type_name -> api.items.story.types.v1.HauntQuestionWithAnswer
	3, // 4: api.items.story.types.v1.HauntPlayConfig.boos_with_questions_and_answers:type_name -> api.items.story.types.v1.HauntBoo
	9, // 5: api.items.story.types.v1.HauntPlayConfig.captions:type_name -> api.items.story.types.v1.AttachmentText
	3, // 6: api.items.story.types.v1.HauntBooShowInfo.haunt_boos:type_name -> api.items.story.types.v1.HauntBoo
	0, // 7: api.items.story.types.v1.HauntBooShowInfo.show_info_sence:type_name -> api.items.story.types.v1.HauntBooShowInfoSence
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_haunt_types_proto_init() }
func file_api_items_story_types_v1_haunt_types_proto_init() {
	if File_api_items_story_types_v1_haunt_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_haunt_types_proto_rawDesc), len(file_api_items_story_types_v1_haunt_types_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_haunt_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_haunt_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_haunt_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_haunt_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_haunt_types_proto = out.File
	file_api_items_story_types_v1_haunt_types_proto_goTypes = nil
	file_api_items_story_types_v1_haunt_types_proto_depIdxs = nil
}
