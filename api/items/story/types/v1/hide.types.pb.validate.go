// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/hide.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StickerTransform with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StickerTransform) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerTransform with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StickerTransformMultiError, or nil if none found.
func (m *StickerTransform) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerTransform) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLocation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StickerTransformValidationError{
					field:  "Location",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StickerTransformValidationError{
					field:  "Location",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLocation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StickerTransformValidationError{
				field:  "Location",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSize()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StickerTransformValidationError{
					field:  "Size",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StickerTransformValidationError{
					field:  "Size",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSize()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StickerTransformValidationError{
				field:  "Size",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StickerTransformMultiError(errors)
	}

	return nil
}

// StickerTransformMultiError is an error wrapping multiple validation errors
// returned by StickerTransform.ValidateAll() if the designated constraints
// aren't met.
type StickerTransformMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerTransformMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerTransformMultiError) AllErrors() []error { return m }

// StickerTransformValidationError is the validation error returned by
// StickerTransform.Validate if the designated constraints aren't met.
type StickerTransformValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerTransformValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerTransformValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerTransformValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerTransformValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerTransformValidationError) ErrorName() string { return "StickerTransformValidationError" }

// Error satisfies the builtin error interface
func (e StickerTransformValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerTransform.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerTransformValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerTransformValidationError{}

// Validate checks the field values on HideSticker with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HideSticker) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HideSticker with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HideStickerMultiError, or
// nil if none found.
func (m *HideSticker) ValidateAll() error {
	return m.validate(true)
}

func (m *HideSticker) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FromStoryId

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HideStickerValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HideStickerValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HideStickerValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransform()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HideStickerValidationError{
					field:  "Transform",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HideStickerValidationError{
					field:  "Transform",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransform()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HideStickerValidationError{
				field:  "Transform",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HideStickerMultiError(errors)
	}

	return nil
}

// HideStickerMultiError is an error wrapping multiple validation errors
// returned by HideSticker.ValidateAll() if the designated constraints aren't met.
type HideStickerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HideStickerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HideStickerMultiError) AllErrors() []error { return m }

// HideStickerValidationError is the validation error returned by
// HideSticker.Validate if the designated constraints aren't met.
type HideStickerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HideStickerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HideStickerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HideStickerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HideStickerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HideStickerValidationError) ErrorName() string { return "HideStickerValidationError" }

// Error satisfies the builtin error interface
func (e HideStickerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHideSticker.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HideStickerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HideStickerValidationError{}

// Validate checks the field values on StoryPlayHideContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayHideContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayHideContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayHideContextMultiError, or nil if none found.
func (m *StoryPlayHideContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayHideContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsConsumed

	for idx, item := range m.GetUnlockedStickers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayHideContextValidationError{
						field:  fmt.Sprintf("UnlockedStickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayHideContextValidationError{
						field:  fmt.Sprintf("UnlockedStickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayHideContextValidationError{
					field:  fmt.Sprintf("UnlockedStickers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayHideContextMultiError(errors)
	}

	return nil
}

// StoryPlayHideContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayHideContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayHideContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayHideContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayHideContextMultiError) AllErrors() []error { return m }

// StoryPlayHideContextValidationError is the validation error returned by
// StoryPlayHideContext.Validate if the designated constraints aren't met.
type StoryPlayHideContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayHideContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayHideContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayHideContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayHideContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayHideContextValidationError) ErrorName() string {
	return "StoryPlayHideContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayHideContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayHideContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayHideContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayHideContextValidationError{}

// Validate checks the field values on
// StickerWithTriggerTypeContinuousClickData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StickerWithTriggerTypeContinuousClickData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StickerWithTriggerTypeContinuousClickData with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StickerWithTriggerTypeContinuousClickDataMultiError, or nil if none found.
func (m *StickerWithTriggerTypeContinuousClickData) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeContinuousClickData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStickersWithClickLocationsInCreation() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeContinuousClickDataValidationError{
						field:  fmt.Sprintf("StickersWithClickLocationsInCreation[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeContinuousClickDataValidationError{
						field:  fmt.Sprintf("StickersWithClickLocationsInCreation[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeContinuousClickDataValidationError{
					field:  fmt.Sprintf("StickersWithClickLocationsInCreation[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetStickersWithClickLocationsInConsume() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeContinuousClickDataValidationError{
						field:  fmt.Sprintf("StickersWithClickLocationsInConsume[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeContinuousClickDataValidationError{
						field:  fmt.Sprintf("StickersWithClickLocationsInConsume[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeContinuousClickDataValidationError{
					field:  fmt.Sprintf("StickersWithClickLocationsInConsume[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NeedClickCount

	if len(errors) > 0 {
		return StickerWithTriggerTypeContinuousClickDataMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeContinuousClickDataMultiError is an error wrapping
// multiple validation errors returned by
// StickerWithTriggerTypeContinuousClickData.ValidateAll() if the designated
// constraints aren't met.
type StickerWithTriggerTypeContinuousClickDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeContinuousClickDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeContinuousClickDataMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeContinuousClickDataValidationError is the validation
// error returned by StickerWithTriggerTypeContinuousClickData.Validate if the
// designated constraints aren't met.
type StickerWithTriggerTypeContinuousClickDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeContinuousClickDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeContinuousClickDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeContinuousClickDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeContinuousClickDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeContinuousClickDataValidationError) ErrorName() string {
	return "StickerWithTriggerTypeContinuousClickDataValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeContinuousClickDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeContinuousClickData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeContinuousClickDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeContinuousClickDataValidationError{}

// Validate checks the field values on StickerWithTriggerTypeDragData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StickerWithTriggerTypeDragData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerWithTriggerTypeDragData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StickerWithTriggerTypeDragDataMultiError, or nil if none found.
func (m *StickerWithTriggerTypeDragData) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeDragData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCutObjects() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeDragDataValidationError{
						field:  fmt.Sprintf("CutObjects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeDragDataValidationError{
						field:  fmt.Sprintf("CutObjects[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeDragDataValidationError{
					field:  fmt.Sprintf("CutObjects[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StickerWithTriggerTypeDragDataMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeDragDataMultiError is an error wrapping multiple
// validation errors returned by StickerWithTriggerTypeDragData.ValidateAll()
// if the designated constraints aren't met.
type StickerWithTriggerTypeDragDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeDragDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeDragDataMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeDragDataValidationError is the validation error
// returned by StickerWithTriggerTypeDragData.Validate if the designated
// constraints aren't met.
type StickerWithTriggerTypeDragDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeDragDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeDragDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeDragDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeDragDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeDragDataValidationError) ErrorName() string {
	return "StickerWithTriggerTypeDragDataValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeDragDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeDragData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeDragDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeDragDataValidationError{}

// Validate checks the field values on StickerWithTriggerTypeLikeData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StickerWithTriggerTypeLikeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerWithTriggerTypeLikeData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StickerWithTriggerTypeLikeDataMultiError, or nil if none found.
func (m *StickerWithTriggerTypeLikeData) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeLikeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStickers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeLikeDataValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeLikeDataValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeLikeDataValidationError{
					field:  fmt.Sprintf("Stickers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StickerWithTriggerTypeLikeDataMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeLikeDataMultiError is an error wrapping multiple
// validation errors returned by StickerWithTriggerTypeLikeData.ValidateAll()
// if the designated constraints aren't met.
type StickerWithTriggerTypeLikeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeLikeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeLikeDataMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeLikeDataValidationError is the validation error
// returned by StickerWithTriggerTypeLikeData.Validate if the designated
// constraints aren't met.
type StickerWithTriggerTypeLikeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeLikeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeLikeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeLikeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeLikeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeLikeDataValidationError) ErrorName() string {
	return "StickerWithTriggerTypeLikeDataValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeLikeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeLikeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeLikeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeLikeDataValidationError{}

// Validate checks the field values on StickerWithTriggerTypeShakeData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StickerWithTriggerTypeShakeData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerWithTriggerTypeShakeData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StickerWithTriggerTypeShakeDataMultiError, or nil if none found.
func (m *StickerWithTriggerTypeShakeData) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeShakeData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStickers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeShakeDataValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeShakeDataValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeShakeDataValidationError{
					field:  fmt.Sprintf("Stickers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NeedShakeCount

	// no validation rules for NeedShakeDurationSeconds

	if len(errors) > 0 {
		return StickerWithTriggerTypeShakeDataMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeShakeDataMultiError is an error wrapping multiple
// validation errors returned by StickerWithTriggerTypeShakeData.ValidateAll()
// if the designated constraints aren't met.
type StickerWithTriggerTypeShakeDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeShakeDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeShakeDataMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeShakeDataValidationError is the validation error
// returned by StickerWithTriggerTypeShakeData.Validate if the designated
// constraints aren't met.
type StickerWithTriggerTypeShakeDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeShakeDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeShakeDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeShakeDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeShakeDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeShakeDataValidationError) ErrorName() string {
	return "StickerWithTriggerTypeShakeDataValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeShakeDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeShakeData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeShakeDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeShakeDataValidationError{}

// Validate checks the field values on StickerWithTriggerTypeLongPressData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StickerWithTriggerTypeLongPressData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerWithTriggerTypeLongPressData
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StickerWithTriggerTypeLongPressDataMultiError, or nil if none found.
func (m *StickerWithTriggerTypeLongPressData) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeLongPressData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStickers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeLongPressDataValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeLongPressDataValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeLongPressDataValidationError{
					field:  fmt.Sprintf("Stickers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NeedLongPressDurationSeconds

	if len(errors) > 0 {
		return StickerWithTriggerTypeLongPressDataMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeLongPressDataMultiError is an error wrapping multiple
// validation errors returned by
// StickerWithTriggerTypeLongPressData.ValidateAll() if the designated
// constraints aren't met.
type StickerWithTriggerTypeLongPressDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeLongPressDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeLongPressDataMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeLongPressDataValidationError is the validation error
// returned by StickerWithTriggerTypeLongPressData.Validate if the designated
// constraints aren't met.
type StickerWithTriggerTypeLongPressDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeLongPressDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeLongPressDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeLongPressDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeLongPressDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeLongPressDataValidationError) ErrorName() string {
	return "StickerWithTriggerTypeLongPressDataValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeLongPressDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeLongPressData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeLongPressDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeLongPressDataValidationError{}

// Validate checks the field values on StickerWithTriggerType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StickerWithTriggerType) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerWithTriggerType with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StickerWithTriggerTypeMultiError, or nil if none found.
func (m *StickerWithTriggerType) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerType) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TriggerType

	switch v := m.Data.(type) {
	case *StickerWithTriggerType_ContinuousClickData:
		if v == nil {
			err := StickerWithTriggerTypeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetContinuousClickData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "ContinuousClickData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "ContinuousClickData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetContinuousClickData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeValidationError{
					field:  "ContinuousClickData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StickerWithTriggerType_DragData:
		if v == nil {
			err := StickerWithTriggerTypeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDragData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "DragData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "DragData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDragData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeValidationError{
					field:  "DragData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StickerWithTriggerType_LikeData:
		if v == nil {
			err := StickerWithTriggerTypeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLikeData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "LikeData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "LikeData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLikeData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeValidationError{
					field:  "LikeData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StickerWithTriggerType_ShakeData:
		if v == nil {
			err := StickerWithTriggerTypeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetShakeData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "ShakeData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "ShakeData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetShakeData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeValidationError{
					field:  "ShakeData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StickerWithTriggerType_LongPressData:
		if v == nil {
			err := StickerWithTriggerTypeValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLongPressData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "LongPressData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeValidationError{
						field:  "LongPressData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLongPressData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeValidationError{
					field:  "LongPressData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return StickerWithTriggerTypeMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeMultiError is an error wrapping multiple validation
// errors returned by StickerWithTriggerType.ValidateAll() if the designated
// constraints aren't met.
type StickerWithTriggerTypeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeValidationError is the validation error returned by
// StickerWithTriggerType.Validate if the designated constraints aren't met.
type StickerWithTriggerTypeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeValidationError) ErrorName() string {
	return "StickerWithTriggerTypeValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerType.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeValidationError{}

// Validate checks the field values on StoryPlayHideConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayHideConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayHideConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayHideConfigMultiError, or nil if none found.
func (m *StoryPlayHideConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayHideConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStickerWithTriggerTypes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("StickerWithTriggerTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("StickerWithTriggerTypes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayHideConfigValidationError{
					field:  fmt.Sprintf("StickerWithTriggerTypes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.GetBackgroundImage() == nil {
		err := StoryPlayHideConfigValidationError{
			field:  "BackgroundImage",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayHideConfigValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayHideConfigValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayHideConfigValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayHideConfigValidationError{
					field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetAllStickers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("AllStickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("AllStickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayHideConfigValidationError{
					field:  fmt.Sprintf("AllStickers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayHideConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Cover != nil {

		if all {
			switch v := interface{}(m.GetCover()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  "Cover",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayHideConfigValidationError{
						field:  "Cover",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCover()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayHideConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayHideConfigMultiError(errors)
	}

	return nil
}

// StoryPlayHideConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayHideConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayHideConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayHideConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayHideConfigMultiError) AllErrors() []error { return m }

// StoryPlayHideConfigValidationError is the validation error returned by
// StoryPlayHideConfig.Validate if the designated constraints aren't met.
type StoryPlayHideConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayHideConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayHideConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayHideConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayHideConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayHideConfigValidationError) ErrorName() string {
	return "StoryPlayHideConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayHideConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayHideConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayHideConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayHideConfigValidationError{}

// Validate checks the field values on StickerTransform_Location with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StickerTransform_Location) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerTransform_Location with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StickerTransform_LocationMultiError, or nil if none found.
func (m *StickerTransform_Location) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerTransform_Location) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for X

	// no validation rules for Y

	if len(errors) > 0 {
		return StickerTransform_LocationMultiError(errors)
	}

	return nil
}

// StickerTransform_LocationMultiError is an error wrapping multiple validation
// errors returned by StickerTransform_Location.ValidateAll() if the
// designated constraints aren't met.
type StickerTransform_LocationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerTransform_LocationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerTransform_LocationMultiError) AllErrors() []error { return m }

// StickerTransform_LocationValidationError is the validation error returned by
// StickerTransform_Location.Validate if the designated constraints aren't met.
type StickerTransform_LocationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerTransform_LocationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerTransform_LocationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerTransform_LocationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerTransform_LocationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerTransform_LocationValidationError) ErrorName() string {
	return "StickerTransform_LocationValidationError"
}

// Error satisfies the builtin error interface
func (e StickerTransform_LocationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerTransform_Location.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerTransform_LocationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerTransform_LocationValidationError{}

// Validate checks the field values on StickerTransform_Size with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StickerTransform_Size) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StickerTransform_Size with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StickerTransform_SizeMultiError, or nil if none found.
func (m *StickerTransform_Size) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerTransform_Size) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Width

	// no validation rules for Height

	if len(errors) > 0 {
		return StickerTransform_SizeMultiError(errors)
	}

	return nil
}

// StickerTransform_SizeMultiError is an error wrapping multiple validation
// errors returned by StickerTransform_Size.ValidateAll() if the designated
// constraints aren't met.
type StickerTransform_SizeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerTransform_SizeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerTransform_SizeMultiError) AllErrors() []error { return m }

// StickerTransform_SizeValidationError is the validation error returned by
// StickerTransform_Size.Validate if the designated constraints aren't met.
type StickerTransform_SizeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerTransform_SizeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerTransform_SizeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerTransform_SizeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerTransform_SizeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerTransform_SizeValidationError) ErrorName() string {
	return "StickerTransform_SizeValidationError"
}

// Error satisfies the builtin error interface
func (e StickerTransform_SizeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerTransform_Size.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerTransform_SizeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerTransform_SizeValidationError{}

// Validate checks the field values on
// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeMultiError,
// or nil if none found.
func (m *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStickers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError{
						field:  fmt.Sprintf("Stickers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError{
					field:  fmt.Sprintf("Stickers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for X

	// no validation rules for Y

	if len(errors) > 0 {
		return StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeMultiError
// is an error wrapping multiple validation errors returned by
// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume.ValidateAll()
// if the designated constraints aren't met.
type StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeMultiError) AllErrors() []error {
	return m
}

// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError
// is the validation error returned by
// StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume.Validate
// if the designated constraints aren't met.
type StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError) ErrorName() string {
	return "StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsumeValidationError{}

// Validate checks the field values on StickerWithTriggerTypeDragData_CutObject
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StickerWithTriggerTypeDragData_CutObject) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StickerWithTriggerTypeDragData_CutObject with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StickerWithTriggerTypeDragData_CutObjectMultiError, or nil if none found.
func (m *StickerWithTriggerTypeDragData_CutObject) ValidateAll() error {
	return m.validate(true)
}

func (m *StickerWithTriggerTypeDragData_CutObject) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MaskImageUrl

	// no validation rules for MaskImageKey

	if all {
		switch v := interface{}(m.GetSticker()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StickerWithTriggerTypeDragData_CutObjectValidationError{
					field:  "Sticker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StickerWithTriggerTypeDragData_CutObjectValidationError{
					field:  "Sticker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSticker()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StickerWithTriggerTypeDragData_CutObjectValidationError{
				field:  "Sticker",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StickerWithTriggerTypeDragData_CutObjectMultiError(errors)
	}

	return nil
}

// StickerWithTriggerTypeDragData_CutObjectMultiError is an error wrapping
// multiple validation errors returned by
// StickerWithTriggerTypeDragData_CutObject.ValidateAll() if the designated
// constraints aren't met.
type StickerWithTriggerTypeDragData_CutObjectMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StickerWithTriggerTypeDragData_CutObjectMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StickerWithTriggerTypeDragData_CutObjectMultiError) AllErrors() []error { return m }

// StickerWithTriggerTypeDragData_CutObjectValidationError is the validation
// error returned by StickerWithTriggerTypeDragData_CutObject.Validate if the
// designated constraints aren't met.
type StickerWithTriggerTypeDragData_CutObjectValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StickerWithTriggerTypeDragData_CutObjectValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StickerWithTriggerTypeDragData_CutObjectValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StickerWithTriggerTypeDragData_CutObjectValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StickerWithTriggerTypeDragData_CutObjectValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StickerWithTriggerTypeDragData_CutObjectValidationError) ErrorName() string {
	return "StickerWithTriggerTypeDragData_CutObjectValidationError"
}

// Error satisfies the builtin error interface
func (e StickerWithTriggerTypeDragData_CutObjectValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStickerWithTriggerTypeDragData_CutObject.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StickerWithTriggerTypeDragData_CutObjectValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StickerWithTriggerTypeDragData_CutObjectValidationError{}
