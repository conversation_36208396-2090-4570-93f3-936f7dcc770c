// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/who.types.proto

package api_items_story_types_v1

import (
	v11 "boson/api/resource/types/v1"
	v1 "boson/api/users/info/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WhoStoryPlayOption struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Correct       bool                   `protobuf:"varint,1,opt,name=correct,proto3" json:"correct,omitempty"`
	Option        *v1.UserInfoSummary    `protobuf:"bytes,2,opt,name=option,proto3" json:"option,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WhoStoryPlayOption) Reset() {
	*x = WhoStoryPlayOption{}
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhoStoryPlayOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhoStoryPlayOption) ProtoMessage() {}

func (x *WhoStoryPlayOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhoStoryPlayOption.ProtoReflect.Descriptor instead.
func (*WhoStoryPlayOption) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_who_types_proto_rawDescGZIP(), []int{0}
}

func (x *WhoStoryPlayOption) GetCorrect() bool {
	if x != nil {
		return x.Correct
	}
	return false
}

func (x *WhoStoryPlayOption) GetOption() *v1.UserInfoSummary {
	if x != nil {
		return x.Option
	}
	return nil
}

// who 玩法配置
type WhoStoryPlayConfig struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	UnlockResourceTexts []*AttachmentText      `protobuf:"bytes,1,rep,name=unlock_resource_texts,json=unlockResourceTexts,proto3" json:"unlock_resource_texts,omitempty"`
	UnlockResource      *v11.Resource          `protobuf:"bytes,2,opt,name=unlock_resource,json=unlockResource,proto3" json:"unlock_resource,omitempty"`
	CoverResourceTexts  []*AttachmentText      `protobuf:"bytes,3,rep,name=cover_resource_texts,json=coverResourceTexts,proto3" json:"cover_resource_texts,omitempty"`
	CoverResource       *v11.Resource          `protobuf:"bytes,4,opt,name=cover_resource,json=coverResource,proto3" json:"cover_resource,omitempty"`
	// 可选型，如果作者自己选择了备选，那么创作的时候，传入两个值即可；
	// 如果创作者不选，那么传空
	// 服务端会始终把创作者设为选项之一，客户端可以不传
	OptionUserIds []string `protobuf:"bytes,5,rep,name=option_user_ids,json=optionUserIds,proto3" json:"option_user_ids,omitempty"`
	// options 是服务端根据 option_user_ids 进行的渲染结构
	// 创作时，客户端不用传递此参数
	Options           []*WhoStoryPlayOption `protobuf:"bytes,6,rep,name=options,proto3" json:"options,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr   `protobuf:"bytes,7,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *WhoStoryPlayConfig) Reset() {
	*x = WhoStoryPlayConfig{}
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhoStoryPlayConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhoStoryPlayConfig) ProtoMessage() {}

func (x *WhoStoryPlayConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhoStoryPlayConfig.ProtoReflect.Descriptor instead.
func (*WhoStoryPlayConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_who_types_proto_rawDescGZIP(), []int{1}
}

func (x *WhoStoryPlayConfig) GetUnlockResourceTexts() []*AttachmentText {
	if x != nil {
		return x.UnlockResourceTexts
	}
	return nil
}

func (x *WhoStoryPlayConfig) GetUnlockResource() *v11.Resource {
	if x != nil {
		return x.UnlockResource
	}
	return nil
}

func (x *WhoStoryPlayConfig) GetCoverResourceTexts() []*AttachmentText {
	if x != nil {
		return x.CoverResourceTexts
	}
	return nil
}

func (x *WhoStoryPlayConfig) GetCoverResource() *v11.Resource {
	if x != nil {
		return x.CoverResource
	}
	return nil
}

func (x *WhoStoryPlayConfig) GetOptionUserIds() []string {
	if x != nil {
		return x.OptionUserIds
	}
	return nil
}

func (x *WhoStoryPlayConfig) GetOptions() []*WhoStoryPlayOption {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *WhoStoryPlayConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

type WhoStoryTryPoint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	X             string                 `protobuf:"bytes,1,opt,name=x,proto3" json:"x,omitempty"`
	Y             string                 `protobuf:"bytes,2,opt,name=y,proto3" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WhoStoryTryPoint) Reset() {
	*x = WhoStoryTryPoint{}
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhoStoryTryPoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhoStoryTryPoint) ProtoMessage() {}

func (x *WhoStoryTryPoint) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhoStoryTryPoint.ProtoReflect.Descriptor instead.
func (*WhoStoryTryPoint) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_who_types_proto_rawDescGZIP(), []int{2}
}

func (x *WhoStoryTryPoint) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *WhoStoryTryPoint) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

type WhoStoryPlayContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户尝试点击过的位置
	TriedPoints []*WhoStoryTryPoint `protobuf:"bytes,1,rep,name=tried_points,json=triedPoints,proto3" json:"tried_points,omitempty"`
	// 目前可用的选项
	EnabledOptions []*v1.UserInfoSummary `protobuf:"bytes,2,rep,name=enabled_options,json=enabledOptions,proto3" json:"enabled_options,omitempty"`
	// 是否已经成功了
	IsUnlocked bool `protobuf:"varint,3,opt,name=is_unlocked,json=isUnlocked,proto3" json:"is_unlocked,omitempty"`
	// 是否尝试过
	IsConsumed bool `protobuf:"varint,4,opt,name=is_consumed,json=isConsumed,proto3" json:"is_consumed,omitempty"`
	// 尝试次数
	TriedCount uint32 `protobuf:"varint,5,opt,name=tried_count,json=triedCount,proto3" json:"tried_count,omitempty"`
	// 最大尝试次数
	MaxTryCount   uint32 `protobuf:"varint,6,opt,name=max_try_count,json=maxTryCount,proto3" json:"max_try_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WhoStoryPlayContext) Reset() {
	*x = WhoStoryPlayContext{}
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhoStoryPlayContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhoStoryPlayContext) ProtoMessage() {}

func (x *WhoStoryPlayContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_who_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhoStoryPlayContext.ProtoReflect.Descriptor instead.
func (*WhoStoryPlayContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_who_types_proto_rawDescGZIP(), []int{3}
}

func (x *WhoStoryPlayContext) GetTriedPoints() []*WhoStoryTryPoint {
	if x != nil {
		return x.TriedPoints
	}
	return nil
}

func (x *WhoStoryPlayContext) GetEnabledOptions() []*v1.UserInfoSummary {
	if x != nil {
		return x.EnabledOptions
	}
	return nil
}

func (x *WhoStoryPlayContext) GetIsUnlocked() bool {
	if x != nil {
		return x.IsUnlocked
	}
	return false
}

func (x *WhoStoryPlayContext) GetIsConsumed() bool {
	if x != nil {
		return x.IsConsumed
	}
	return false
}

func (x *WhoStoryPlayContext) GetTriedCount() uint32 {
	if x != nil {
		return x.TriedCount
	}
	return 0
}

func (x *WhoStoryPlayContext) GetMaxTryCount() uint32 {
	if x != nil {
		return x.MaxTryCount
	}
	return 0
}

var File_api_items_story_types_v1_who_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_who_types_proto_rawDesc = "" +
	"\n" +
	"(api/items/story/types/v1/who.types.proto\x12\x18api.items.story.types.v1\x1a!api/resource/types/v1/types.proto\x1a\x17validate/validate.proto\x1a#api/users/info/types/v1/types.proto\x1a)api/items/story/types/v1/base_types.proto\"p\n" +
	"\x12WhoStoryPlayOption\x12\x18\n" +
	"\acorrect\x18\x01 \x01(\bR\acorrect\x12@\n" +
	"\x06option\x18\x02 \x01(\v2(.api.users.info.types.v1.UserInfoSummaryR\x06option\"\xc0\x04\n" +
	"\x12WhoStoryPlayConfig\x12\\\n" +
	"\x15unlock_resource_texts\x18\x01 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x13unlockResourceTexts\x12R\n" +
	"\x0funlock_resource\x18\x02 \x01(\v2\x1f.api.resource.types.v1.ResourceB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x0eunlockResource\x12Z\n" +
	"\x14cover_resource_texts\x18\x03 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x12coverResourceTexts\x12P\n" +
	"\x0ecover_resource\x18\x04 \x01(\v2\x1f.api.resource.types.v1.ResourceB\b\xfaB\x05\x8a\x01\x02\x10\x01R\rcoverResource\x12&\n" +
	"\x0foption_user_ids\x18\x05 \x03(\tR\roptionUserIds\x12F\n" +
	"\aoptions\x18\x06 \x03(\v2,.api.items.story.types.v1.WhoStoryPlayOptionR\aoptions\x12Z\n" +
	"\x13moment_create_attrs\x18\a \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\".\n" +
	"\x10WhoStoryTryPoint\x12\f\n" +
	"\x01x\x18\x01 \x01(\tR\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\tR\x01y\"\xbe\x02\n" +
	"\x13WhoStoryPlayContext\x12M\n" +
	"\ftried_points\x18\x01 \x03(\v2*.api.items.story.types.v1.WhoStoryTryPointR\vtriedPoints\x12Q\n" +
	"\x0fenabled_options\x18\x02 \x03(\v2(.api.users.info.types.v1.UserInfoSummaryR\x0eenabledOptions\x12\x1f\n" +
	"\vis_unlocked\x18\x03 \x01(\bR\n" +
	"isUnlocked\x12\x1f\n" +
	"\vis_consumed\x18\x04 \x01(\bR\n" +
	"isConsumed\x12\x1f\n" +
	"\vtried_count\x18\x05 \x01(\rR\n" +
	"triedCount\x12\"\n" +
	"\rmax_try_count\x18\x06 \x01(\rR\vmaxTryCountB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_who_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_who_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_who_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_who_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_who_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_who_types_proto_rawDesc), len(file_api_items_story_types_v1_who_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_who_types_proto_rawDescData
}

var file_api_items_story_types_v1_who_types_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_items_story_types_v1_who_types_proto_goTypes = []any{
	(*WhoStoryPlayOption)(nil),  // 0: api.items.story.types.v1.WhoStoryPlayOption
	(*WhoStoryPlayConfig)(nil),  // 1: api.items.story.types.v1.WhoStoryPlayConfig
	(*WhoStoryTryPoint)(nil),    // 2: api.items.story.types.v1.WhoStoryTryPoint
	(*WhoStoryPlayContext)(nil), // 3: api.items.story.types.v1.WhoStoryPlayContext
	(*v1.UserInfoSummary)(nil),  // 4: api.users.info.types.v1.UserInfoSummary
	(*AttachmentText)(nil),      // 5: api.items.story.types.v1.AttachmentText
	(*v11.Resource)(nil),        // 6: api.resource.types.v1.Resource
	(*MomentCreateAttr)(nil),    // 7: api.items.story.types.v1.MomentCreateAttr
}
var file_api_items_story_types_v1_who_types_proto_depIdxs = []int32{
	4, // 0: api.items.story.types.v1.WhoStoryPlayOption.option:type_name -> api.users.info.types.v1.UserInfoSummary
	5, // 1: api.items.story.types.v1.WhoStoryPlayConfig.unlock_resource_texts:type_name -> api.items.story.types.v1.AttachmentText
	6, // 2: api.items.story.types.v1.WhoStoryPlayConfig.unlock_resource:type_name -> api.resource.types.v1.Resource
	5, // 3: api.items.story.types.v1.WhoStoryPlayConfig.cover_resource_texts:type_name -> api.items.story.types.v1.AttachmentText
	6, // 4: api.items.story.types.v1.WhoStoryPlayConfig.cover_resource:type_name -> api.resource.types.v1.Resource
	0, // 5: api.items.story.types.v1.WhoStoryPlayConfig.options:type_name -> api.items.story.types.v1.WhoStoryPlayOption
	7, // 6: api.items.story.types.v1.WhoStoryPlayConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	2, // 7: api.items.story.types.v1.WhoStoryPlayContext.tried_points:type_name -> api.items.story.types.v1.WhoStoryTryPoint
	4, // 8: api.items.story.types.v1.WhoStoryPlayContext.enabled_options:type_name -> api.users.info.types.v1.UserInfoSummary
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_who_types_proto_init() }
func file_api_items_story_types_v1_who_types_proto_init() {
	if File_api_items_story_types_v1_who_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_who_types_proto_rawDesc), len(file_api_items_story_types_v1_who_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_who_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_who_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_who_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_who_types_proto = out.File
	file_api_items_story_types_v1_who_types_proto_goTypes = nil
	file_api_items_story_types_v1_who_types_proto_depIdxs = nil
}
