// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/pin.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on PinEmojiArear with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PinEmojiArear) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PinEmojiArear with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PinEmojiArearMultiError, or
// nil if none found.
func (m *PinEmojiArear) ValidateAll() error {
	return m.validate(true)
}

func (m *PinEmojiArear) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for X

	// no validation rules for Y

	// no validation rules for Width

	// no validation rules for Height

	if len(errors) > 0 {
		return PinEmojiArearMultiError(errors)
	}

	return nil
}

// PinEmojiArearMultiError is an error wrapping multiple validation errors
// returned by PinEmojiArear.ValidateAll() if the designated constraints
// aren't met.
type PinEmojiArearMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PinEmojiArearMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PinEmojiArearMultiError) AllErrors() []error { return m }

// PinEmojiArearValidationError is the validation error returned by
// PinEmojiArear.Validate if the designated constraints aren't met.
type PinEmojiArearValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PinEmojiArearValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PinEmojiArearValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PinEmojiArearValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PinEmojiArearValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PinEmojiArearValidationError) ErrorName() string { return "PinEmojiArearValidationError" }

// Error satisfies the builtin error interface
func (e PinEmojiArearValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPinEmojiArear.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PinEmojiArearValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PinEmojiArearValidationError{}

// Validate checks the field values on PinEmojiResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PinEmojiResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PinEmojiResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PinEmojiResourceMultiError, or nil if none found.
func (m *PinEmojiResource) ValidateAll() error {
	return m.validate(true)
}

func (m *PinEmojiResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetArea()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PinEmojiResourceValidationError{
					field:  "Area",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PinEmojiResourceValidationError{
					field:  "Area",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetArea()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PinEmojiResourceValidationError{
				field:  "Area",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Data.(type) {
	case *PinEmojiResource_GeneratedEmojiResource:
		if v == nil {
			err := PinEmojiResourceValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGeneratedEmojiResource()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PinEmojiResourceValidationError{
						field:  "GeneratedEmojiResource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PinEmojiResourceValidationError{
						field:  "GeneratedEmojiResource",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGeneratedEmojiResource()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PinEmojiResourceValidationError{
					field:  "GeneratedEmojiResource",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PinEmojiResource_DefaultEmoji:
		if v == nil {
			err := PinEmojiResourceValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DefaultEmoji
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PinEmojiResourceMultiError(errors)
	}

	return nil
}

// PinEmojiResourceMultiError is an error wrapping multiple validation errors
// returned by PinEmojiResource.ValidateAll() if the designated constraints
// aren't met.
type PinEmojiResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PinEmojiResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PinEmojiResourceMultiError) AllErrors() []error { return m }

// PinEmojiResourceValidationError is the validation error returned by
// PinEmojiResource.Validate if the designated constraints aren't met.
type PinEmojiResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PinEmojiResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PinEmojiResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PinEmojiResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PinEmojiResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PinEmojiResourceValidationError) ErrorName() string { return "PinEmojiResourceValidationError" }

// Error satisfies the builtin error interface
func (e PinEmojiResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPinEmojiResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PinEmojiResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PinEmojiResourceValidationError{}

// Validate checks the field values on StoryPinContext with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StoryPinContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPinContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPinContextMultiError, or nil if none found.
func (m *StoryPinContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPinContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsUnlocked

	if len(errors) > 0 {
		return StoryPinContextMultiError(errors)
	}

	return nil
}

// StoryPinContextMultiError is an error wrapping multiple validation errors
// returned by StoryPinContext.ValidateAll() if the designated constraints
// aren't met.
type StoryPinContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPinContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPinContextMultiError) AllErrors() []error { return m }

// StoryPinContextValidationError is the validation error returned by
// StoryPinContext.Validate if the designated constraints aren't met.
type StoryPinContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPinContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPinContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPinContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPinContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPinContextValidationError) ErrorName() string { return "StoryPinContextValidationError" }

// Error satisfies the builtin error interface
func (e StoryPinContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPinContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPinContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPinContextValidationError{}

// Validate checks the field values on StoryPinConfig with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryPinConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPinConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StoryPinConfigMultiError,
// or nil if none found.
func (m *StoryPinConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPinConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPinConfigValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPinConfigValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPinConfigValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPinEmojiResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPinConfigValidationError{
						field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPinConfigValidationError{
						field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPinConfigValidationError{
					field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCaption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPinConfigValidationError{
					field:  "Caption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPinConfigValidationError{
					field:  "Caption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPinConfigValidationError{
				field:  "Caption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPinConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPinConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPinConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPinConfigMultiError(errors)
	}

	return nil
}

// StoryPinConfigMultiError is an error wrapping multiple validation errors
// returned by StoryPinConfig.ValidateAll() if the designated constraints
// aren't met.
type StoryPinConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPinConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPinConfigMultiError) AllErrors() []error { return m }

// StoryPinConfigValidationError is the validation error returned by
// StoryPinConfig.Validate if the designated constraints aren't met.
type StoryPinConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPinConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPinConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPinConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPinConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPinConfigValidationError) ErrorName() string { return "StoryPinConfigValidationError" }

// Error satisfies the builtin error interface
func (e StoryPinConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPinConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPinConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPinConfigValidationError{}
