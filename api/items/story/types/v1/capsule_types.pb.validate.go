// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/capsule_types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StoryPlayCapsuleContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayCapsuleContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayCapsuleContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayCapsuleContextMultiError, or nil if none found.
func (m *StoryPlayCapsuleContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayCapsuleContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsConsumed

	if len(errors) > 0 {
		return StoryPlayCapsuleContextMultiError(errors)
	}

	return nil
}

// StoryPlayCapsuleContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayCapsuleContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayCapsuleContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayCapsuleContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayCapsuleContextMultiError) AllErrors() []error { return m }

// StoryPlayCapsuleContextValidationError is the validation error returned by
// StoryPlayCapsuleContext.Validate if the designated constraints aren't met.
type StoryPlayCapsuleContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayCapsuleContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayCapsuleContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayCapsuleContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayCapsuleContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayCapsuleContextValidationError) ErrorName() string {
	return "StoryPlayCapsuleContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayCapsuleContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayCapsuleContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayCapsuleContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayCapsuleContextValidationError{}

// Validate checks the field values on CapsuleShootingResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CapsuleShootingResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapsuleShootingResource with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CapsuleShootingResourceMultiError, or nil if none found.
func (m *CapsuleShootingResource) ValidateAll() error {
	return m.validate(true)
}

func (m *CapsuleShootingResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetShooting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CapsuleShootingResourceValidationError{
					field:  "Shooting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CapsuleShootingResourceValidationError{
					field:  "Shooting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShooting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CapsuleShootingResourceValidationError{
				field:  "Shooting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShootingResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CapsuleShootingResourceValidationError{
					field:  "ShootingResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CapsuleShootingResourceValidationError{
					field:  "ShootingResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShootingResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CapsuleShootingResourceValidationError{
				field:  "ShootingResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CapsuleShootingResourceMultiError(errors)
	}

	return nil
}

// CapsuleShootingResourceMultiError is an error wrapping multiple validation
// errors returned by CapsuleShootingResource.ValidateAll() if the designated
// constraints aren't met.
type CapsuleShootingResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapsuleShootingResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapsuleShootingResourceMultiError) AllErrors() []error { return m }

// CapsuleShootingResourceValidationError is the validation error returned by
// CapsuleShootingResource.Validate if the designated constraints aren't met.
type CapsuleShootingResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapsuleShootingResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapsuleShootingResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapsuleShootingResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapsuleShootingResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapsuleShootingResourceValidationError) ErrorName() string {
	return "CapsuleShootingResourceValidationError"
}

// Error satisfies the builtin error interface
func (e CapsuleShootingResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapsuleShootingResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapsuleShootingResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapsuleShootingResourceValidationError{}

// Validate checks the field values on CapsuleAIScript with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CapsuleAIScript) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapsuleAIScript with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CapsuleAIScriptMultiError, or nil if none found.
func (m *CapsuleAIScript) ValidateAll() error {
	return m.validate(true)
}

func (m *CapsuleAIScript) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Seconds

	if all {
		switch v := interface{}(m.GetQuestion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CapsuleAIScriptValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CapsuleAIScriptValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CapsuleAIScriptValidationError{
				field:  "Question",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CapsuleAIScriptMultiError(errors)
	}

	return nil
}

// CapsuleAIScriptMultiError is an error wrapping multiple validation errors
// returned by CapsuleAIScript.ValidateAll() if the designated constraints
// aren't met.
type CapsuleAIScriptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapsuleAIScriptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapsuleAIScriptMultiError) AllErrors() []error { return m }

// CapsuleAIScriptValidationError is the validation error returned by
// CapsuleAIScript.Validate if the designated constraints aren't met.
type CapsuleAIScriptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapsuleAIScriptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapsuleAIScriptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapsuleAIScriptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapsuleAIScriptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapsuleAIScriptValidationError) ErrorName() string { return "CapsuleAIScriptValidationError" }

// Error satisfies the builtin error interface
func (e CapsuleAIScriptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapsuleAIScript.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapsuleAIScriptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapsuleAIScriptValidationError{}

// Validate checks the field values on CapsulePhotoInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CapsulePhotoInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapsulePhotoInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CapsulePhotoInfoMultiError, or nil if none found.
func (m *CapsulePhotoInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CapsulePhotoInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InDays

	// no validation rules for Moments

	if len(errors) > 0 {
		return CapsulePhotoInfoMultiError(errors)
	}

	return nil
}

// CapsulePhotoInfoMultiError is an error wrapping multiple validation errors
// returned by CapsulePhotoInfo.ValidateAll() if the designated constraints
// aren't met.
type CapsulePhotoInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapsulePhotoInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapsulePhotoInfoMultiError) AllErrors() []error { return m }

// CapsulePhotoInfoValidationError is the validation error returned by
// CapsulePhotoInfo.Validate if the designated constraints aren't met.
type CapsulePhotoInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapsulePhotoInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapsulePhotoInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapsulePhotoInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapsulePhotoInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapsulePhotoInfoValidationError) ErrorName() string { return "CapsulePhotoInfoValidationError" }

// Error satisfies the builtin error interface
func (e CapsulePhotoInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapsulePhotoInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapsulePhotoInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapsulePhotoInfoValidationError{}

// Validate checks the field values on StoryPlayCapsuleConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayCapsuleConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayCapsuleConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayCapsuleConfigMultiError, or nil if none found.
func (m *StoryPlayCapsuleConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayCapsuleConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCoverImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayCapsuleConfigValidationError{
					field:  "CoverImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayCapsuleConfigValidationError{
					field:  "CoverImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCoverImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayCapsuleConfigValidationError{
				field:  "CoverImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVideo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayCapsuleConfigValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayCapsuleConfigValidationError{
					field:  "Video",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVideo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayCapsuleConfigValidationError{
				field:  "Video",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAiScripts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayCapsuleConfigValidationError{
						field:  fmt.Sprintf("AiScripts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayCapsuleConfigValidationError{
						field:  fmt.Sprintf("AiScripts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayCapsuleConfigValidationError{
					field:  fmt.Sprintf("AiScripts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPhotoInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayCapsuleConfigValidationError{
					field:  "PhotoInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayCapsuleConfigValidationError{
					field:  "PhotoInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhotoInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayCapsuleConfigValidationError{
				field:  "PhotoInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayCapsuleConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayCapsuleConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayCapsuleConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayCapsuleConfigMultiError(errors)
	}

	return nil
}

// StoryPlayCapsuleConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayCapsuleConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayCapsuleConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayCapsuleConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayCapsuleConfigMultiError) AllErrors() []error { return m }

// StoryPlayCapsuleConfigValidationError is the validation error returned by
// StoryPlayCapsuleConfig.Validate if the designated constraints aren't met.
type StoryPlayCapsuleConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayCapsuleConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayCapsuleConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayCapsuleConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayCapsuleConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayCapsuleConfigValidationError) ErrorName() string {
	return "StoryPlayCapsuleConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayCapsuleConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayCapsuleConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayCapsuleConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayCapsuleConfigValidationError{}

// Validate checks the field values on StoryPlayCapsuleExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayCapsuleExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayCapsuleExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayCapsuleExampleMultiError, or nil if none found.
func (m *StoryPlayCapsuleExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayCapsuleExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommonInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayCapsuleExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayCapsuleExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayCapsuleExampleValidationError{
				field:  "CommonInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhotoInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayCapsuleExampleValidationError{
					field:  "PhotoInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayCapsuleExampleValidationError{
					field:  "PhotoInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhotoInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayCapsuleExampleValidationError{
				field:  "PhotoInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoryPlayCapsuleExampleMultiError(errors)
	}

	return nil
}

// StoryPlayCapsuleExampleMultiError is an error wrapping multiple validation
// errors returned by StoryPlayCapsuleExample.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayCapsuleExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayCapsuleExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayCapsuleExampleMultiError) AllErrors() []error { return m }

// StoryPlayCapsuleExampleValidationError is the validation error returned by
// StoryPlayCapsuleExample.Validate if the designated constraints aren't met.
type StoryPlayCapsuleExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayCapsuleExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayCapsuleExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayCapsuleExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayCapsuleExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayCapsuleExampleValidationError) ErrorName() string {
	return "StoryPlayCapsuleExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayCapsuleExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayCapsuleExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayCapsuleExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayCapsuleExampleValidationError{}

// Validate checks the field values on CapsuleQuestion with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CapsuleQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapsuleQuestion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CapsuleQuestionMultiError, or nil if none found.
func (m *CapsuleQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *CapsuleQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	// no validation rules for TtsAudioUrl

	// no validation rules for TtsAudioKey

	// no validation rules for Thinking

	for idx, item := range m.GetWords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CapsuleQuestionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CapsuleQuestionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CapsuleQuestionValidationError{
					field:  fmt.Sprintf("Words[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CapsuleQuestionMultiError(errors)
	}

	return nil
}

// CapsuleQuestionMultiError is an error wrapping multiple validation errors
// returned by CapsuleQuestion.ValidateAll() if the designated constraints
// aren't met.
type CapsuleQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapsuleQuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapsuleQuestionMultiError) AllErrors() []error { return m }

// CapsuleQuestionValidationError is the validation error returned by
// CapsuleQuestion.Validate if the designated constraints aren't met.
type CapsuleQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapsuleQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapsuleQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapsuleQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapsuleQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapsuleQuestionValidationError) ErrorName() string { return "CapsuleQuestionValidationError" }

// Error satisfies the builtin error interface
func (e CapsuleQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapsuleQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapsuleQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapsuleQuestionValidationError{}

// Validate checks the field values on CapsuleQuestionWithUserAnswer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CapsuleQuestionWithUserAnswer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapsuleQuestionWithUserAnswer with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CapsuleQuestionWithUserAnswerMultiError, or nil if none found.
func (m *CapsuleQuestionWithUserAnswer) ValidateAll() error {
	return m.validate(true)
}

func (m *CapsuleQuestionWithUserAnswer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQuestion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CapsuleQuestionWithUserAnswerValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CapsuleQuestionWithUserAnswerValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CapsuleQuestionWithUserAnswerValidationError{
				field:  "Question",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserVoiceKey

	if len(errors) > 0 {
		return CapsuleQuestionWithUserAnswerMultiError(errors)
	}

	return nil
}

// CapsuleQuestionWithUserAnswerMultiError is an error wrapping multiple
// validation errors returned by CapsuleQuestionWithUserAnswer.ValidateAll()
// if the designated constraints aren't met.
type CapsuleQuestionWithUserAnswerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapsuleQuestionWithUserAnswerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapsuleQuestionWithUserAnswerMultiError) AllErrors() []error { return m }

// CapsuleQuestionWithUserAnswerValidationError is the validation error
// returned by CapsuleQuestionWithUserAnswer.Validate if the designated
// constraints aren't met.
type CapsuleQuestionWithUserAnswerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapsuleQuestionWithUserAnswerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapsuleQuestionWithUserAnswerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapsuleQuestionWithUserAnswerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapsuleQuestionWithUserAnswerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapsuleQuestionWithUserAnswerValidationError) ErrorName() string {
	return "CapsuleQuestionWithUserAnswerValidationError"
}

// Error satisfies the builtin error interface
func (e CapsuleQuestionWithUserAnswerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapsuleQuestionWithUserAnswer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapsuleQuestionWithUserAnswerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapsuleQuestionWithUserAnswerValidationError{}

// Validate checks the field values on ImageDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageDetailMultiError, or
// nil if none found.
func (m *ImageDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ObjectKey

	// no validation rules for Latitude

	// no validation rules for Longitude

	// no validation rules for ShootingTimestamp

	if len(errors) > 0 {
		return ImageDetailMultiError(errors)
	}

	return nil
}

// ImageDetailMultiError is an error wrapping multiple validation errors
// returned by ImageDetail.ValidateAll() if the designated constraints aren't met.
type ImageDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageDetailMultiError) AllErrors() []error { return m }

// ImageDetailValidationError is the validation error returned by
// ImageDetail.Validate if the designated constraints aren't met.
type ImageDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageDetailValidationError) ErrorName() string { return "ImageDetailValidationError" }

// Error satisfies the builtin error interface
func (e ImageDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageDetailValidationError{}
