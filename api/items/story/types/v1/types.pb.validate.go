// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StoryStats with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryStats) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryStats with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StoryStatsMultiError, or
// nil if none found.
func (m *StoryStats) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryStats) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReactionMadeStatSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryStatsValidationError{
					field:  "ReactionMadeStatSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryStatsValidationError{
					field:  "ReactionMadeStatSummary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReactionMadeStatSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryStatsValidationError{
				field:  "ReactionMadeStatSummary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShareStat

	if len(errors) > 0 {
		return StoryStatsMultiError(errors)
	}

	return nil
}

// StoryStatsMultiError is an error wrapping multiple validation errors
// returned by StoryStats.ValidateAll() if the designated constraints aren't met.
type StoryStatsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryStatsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryStatsMultiError) AllErrors() []error { return m }

// StoryStatsValidationError is the validation error returned by
// StoryStats.Validate if the designated constraints aren't met.
type StoryStatsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryStatsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryStatsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryStatsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryStatsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryStatsValidationError) ErrorName() string { return "StoryStatsValidationError" }

// Error satisfies the builtin error interface
func (e StoryStatsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryStats.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryStatsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryStatsValidationError{}

// Validate checks the field values on StorySummary with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StorySummary) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StorySummary with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StorySummaryMultiError, or
// nil if none found.
func (m *StorySummary) ValidateAll() error {
	return m.validate(true)
}

func (m *StorySummary) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetAuthor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "Author",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "Author",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuthor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "Author",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlayType

	// no validation rules for SubPlayType

	// no validation rules for Status

	// no validation rules for CreatedAtTimestamp

	if all {
		switch v := interface{}(m.GetExchangeImageExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "ExchangeImageExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "ExchangeImageExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExchangeImageExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "ExchangeImageExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTurtleSoupExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "TurtleSoupExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "TurtleSoupExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTurtleSoupExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "TurtleSoupExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnmuteExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "UnmuteExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "UnmuteExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnmuteExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "UnmuteExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTurtleSoupMassExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "TurtleSoupMassExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "TurtleSoupMassExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTurtleSoupMassExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "TurtleSoupMassExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBasePlayExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "BasePlayExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "BasePlayExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasePlayExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "BasePlayExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CoverImageUrl

	// no validation rules for CoverImageWidth

	// no validation rules for CoverImageHeight

	// no validation rules for CoverType

	if all {
		switch v := interface{}(m.GetPrivacySetting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "PrivacySetting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNowShotPlayExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "NowShotPlayExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "NowShotPlayExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNowShotPlayExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "NowShotPlayExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRoastedExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "RoastedExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "RoastedExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRoastedExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "RoastedExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCapsuleExample()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "CapsuleExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "CapsuleExample",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCapsuleExample()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "CapsuleExample",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStats()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "Stats",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "Stats",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStats()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "Stats",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetLoginUserMadeReactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StorySummaryValidationError{
						field:  fmt.Sprintf("LoginUserMadeReactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StorySummaryValidationError{
						field:  fmt.Sprintf("LoginUserMadeReactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StorySummaryValidationError{
					field:  fmt.Sprintf("LoginUserMadeReactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUnlockedUsers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "UnlockedUsers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StorySummaryValidationError{
					field:  "UnlockedUsers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockedUsers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StorySummaryValidationError{
				field:  "UnlockedUsers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HasUnlocked

	// no validation rules for SortRequestId

	if len(errors) > 0 {
		return StorySummaryMultiError(errors)
	}

	return nil
}

// StorySummaryMultiError is an error wrapping multiple validation errors
// returned by StorySummary.ValidateAll() if the designated constraints aren't met.
type StorySummaryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StorySummaryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StorySummaryMultiError) AllErrors() []error { return m }

// StorySummaryValidationError is the validation error returned by
// StorySummary.Validate if the designated constraints aren't met.
type StorySummaryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StorySummaryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StorySummaryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StorySummaryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StorySummaryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StorySummaryValidationError) ErrorName() string { return "StorySummaryValidationError" }

// Error satisfies the builtin error interface
func (e StorySummaryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStorySummary.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StorySummaryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StorySummaryValidationError{}

// Validate checks the field values on StoryPlayExchangeImageExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayExchangeImageExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayExchangeImageExample with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayExchangeImageExampleMultiError, or nil if none found.
func (m *StoryPlayExchangeImageExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommonInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayExchangeImageExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayExchangeImageExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayExchangeImageExampleValidationError{
				field:  "CommonInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayExchangeImageExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayExchangeImageExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayExchangeImageExampleValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayExchangeImageExampleMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageExampleMultiError is an error wrapping multiple
// validation errors returned by StoryPlayExchangeImageExample.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayExchangeImageExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageExampleMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageExampleValidationError is the validation error
// returned by StoryPlayExchangeImageExample.Validate if the designated
// constraints aren't met.
type StoryPlayExchangeImageExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayExchangeImageExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayExchangeImageExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayExchangeImageExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageExampleValidationError) ErrorName() string {
	return "StoryPlayExchangeImageExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageExampleValidationError{}

// Validate checks the field values on StoryPlayUnmuteExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayUnmuteExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayUnmuteExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayUnmuteExampleMultiError, or nil if none found.
func (m *StoryPlayUnmuteExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCommonInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayUnmuteExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayUnmuteExampleValidationError{
					field:  "CommonInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayUnmuteExampleValidationError{
				field:  "CommonInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayUnmuteExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayUnmuteExampleValidationError{
						field:  fmt.Sprintf("Cases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayUnmuteExampleValidationError{
					field:  fmt.Sprintf("Cases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayUnmuteExampleMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteExampleMultiError is an error wrapping multiple validation
// errors returned by StoryPlayUnmuteExample.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayUnmuteExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteExampleMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteExampleValidationError is the validation error returned by
// StoryPlayUnmuteExample.Validate if the designated constraints aren't met.
type StoryPlayUnmuteExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteExampleValidationError) ErrorName() string {
	return "StoryPlayUnmuteExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteExampleValidationError{}

// Validate checks the field values on PortalBasicInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PortalBasicInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortalBasicInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortalBasicInfoMultiError, or nil if none found.
func (m *PortalBasicInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PortalBasicInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PortalId

	// no validation rules for MomentCount

	if len(errors) > 0 {
		return PortalBasicInfoMultiError(errors)
	}

	return nil
}

// PortalBasicInfoMultiError is an error wrapping multiple validation errors
// returned by PortalBasicInfo.ValidateAll() if the designated constraints
// aren't met.
type PortalBasicInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortalBasicInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortalBasicInfoMultiError) AllErrors() []error { return m }

// PortalBasicInfoValidationError is the validation error returned by
// PortalBasicInfo.Validate if the designated constraints aren't met.
type PortalBasicInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortalBasicInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortalBasicInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortalBasicInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortalBasicInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortalBasicInfoValidationError) ErrorName() string { return "PortalBasicInfoValidationError" }

// Error satisfies the builtin error interface
func (e PortalBasicInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortalBasicInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortalBasicInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortalBasicInfoValidationError{}

// Validate checks the field values on StoryDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoryDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StoryDetailMultiError, or
// nil if none found.
func (m *StoryDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSummary()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryDetailValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryDetailValidationError{
					field:  "Summary",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSummary()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryDetailValidationError{
				field:  "Summary",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHauntBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryDetailValidationError{
					field:  "HauntBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryDetailValidationError{
					field:  "HauntBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHauntBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryDetailValidationError{
				field:  "HauntBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPortalBasicInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryDetailValidationError{
					field:  "PortalBasicInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryDetailValidationError{
					field:  "PortalBasicInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortalBasicInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryDetailValidationError{
				field:  "PortalBasicInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.PlayConfig.(type) {
	case *StoryDetail_ExchangeImageConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExchangeImageConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ExchangeImageConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ExchangeImageConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExchangeImageConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "ExchangeImageConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_TurtleSoupConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTurtleSoupConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTurtleSoupConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "TurtleSoupConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_UnmuteConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUnmuteConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "UnmuteConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "UnmuteConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUnmuteConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "UnmuteConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_TurtleSoupMassConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTurtleSoupMassConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupMassConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupMassConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTurtleSoupMassConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "TurtleSoupMassConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_BasePlayConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBasePlayConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "BasePlayConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "BasePlayConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBasePlayConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "BasePlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_NowShotConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNowShotConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "NowShotConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "NowShotConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNowShotConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "NowShotConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_RoastedConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRoastedConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "RoastedConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "RoastedConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRoastedConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "RoastedConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_WassupConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWassupConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WassupConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WassupConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWassupConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "WassupConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_CapsuleConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCapsuleConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "CapsuleConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "CapsuleConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCapsuleConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "CapsuleConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_HideConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHideConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HideConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HideConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHideConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "HideConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_ChatproxyConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetChatproxyConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ChatproxyConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ChatproxyConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetChatproxyConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "ChatproxyConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_WhoConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWhoConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WhoConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WhoConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWhoConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "WhoConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_HauntConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHauntConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HauntConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HauntConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHauntConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "HauntConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_PinConfig:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayConfig",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPinConfig()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "PinConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "PinConfig",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPinConfig()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "PinConfig",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.PlayContext.(type) {
	case *StoryDetail_ExchangeImageContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExchangeImageContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ExchangeImageContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ExchangeImageContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExchangeImageContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "ExchangeImageContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_TurtleSoupContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTurtleSoupContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTurtleSoupContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "TurtleSoupContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_UnmuteContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUnmuteContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "UnmuteContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "UnmuteContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUnmuteContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "UnmuteContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_TurtleSoupMassContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTurtleSoupMassContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupMassContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "TurtleSoupMassContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTurtleSoupMassContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "TurtleSoupMassContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_BasePlayContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBasePlayContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "BasePlayContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "BasePlayContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBasePlayContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "BasePlayContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_NowShotContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNowShotContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "NowShotContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "NowShotContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNowShotContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "NowShotContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_RoastedContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRoastedContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "RoastedContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "RoastedContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRoastedContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "RoastedContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_WassupContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWassupContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WassupContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WassupContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWassupContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "WassupContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_CapsuleContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCapsuleContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "CapsuleContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "CapsuleContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCapsuleContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "CapsuleContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_HideContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHideContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HideContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HideContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHideContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "HideContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_ChatproxyContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetChatproxyContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ChatproxyContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "ChatproxyContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetChatproxyContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "ChatproxyContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_WhoContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWhoContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WhoContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "WhoContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWhoContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "WhoContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_HauntContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetHauntContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HauntContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "HauntContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetHauntContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "HauntContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *StoryDetail_PinContext:
		if v == nil {
			err := StoryDetailValidationError{
				field:  "PlayContext",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPinContext()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "PinContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryDetailValidationError{
						field:  "PinContext",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPinContext()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryDetailValidationError{
					field:  "PinContext",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if m.EnabledPortalId != nil {
		// no validation rules for EnabledPortalId
	}

	if len(errors) > 0 {
		return StoryDetailMultiError(errors)
	}

	return nil
}

// StoryDetailMultiError is an error wrapping multiple validation errors
// returned by StoryDetail.ValidateAll() if the designated constraints aren't met.
type StoryDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryDetailMultiError) AllErrors() []error { return m }

// StoryDetailValidationError is the validation error returned by
// StoryDetail.Validate if the designated constraints aren't met.
type StoryDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryDetailValidationError) ErrorName() string { return "StoryDetailValidationError" }

// Error satisfies the builtin error interface
func (e StoryDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryDetailValidationError{}

// Validate checks the field values on StoryExchangeImageConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryExchangeImageConditionTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryExchangeImageConditionTemplate
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryExchangeImageConditionTemplateMultiError, or nil if none found.
func (m *StoryExchangeImageConditionTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryExchangeImageConditionTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CoverImageUrl

	if all {
		switch v := interface{}(m.GetDefaultCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryExchangeImageConditionTemplateValidationError{
					field:  "DefaultCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryExchangeImageConditionTemplateValidationError{
					field:  "DefaultCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryExchangeImageConditionTemplateValidationError{
				field:  "DefaultCondition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Typ

	if len(errors) > 0 {
		return StoryExchangeImageConditionTemplateMultiError(errors)
	}

	return nil
}

// StoryExchangeImageConditionTemplateMultiError is an error wrapping multiple
// validation errors returned by
// StoryExchangeImageConditionTemplate.ValidateAll() if the designated
// constraints aren't met.
type StoryExchangeImageConditionTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryExchangeImageConditionTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryExchangeImageConditionTemplateMultiError) AllErrors() []error { return m }

// StoryExchangeImageConditionTemplateValidationError is the validation error
// returned by StoryExchangeImageConditionTemplate.Validate if the designated
// constraints aren't met.
type StoryExchangeImageConditionTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryExchangeImageConditionTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryExchangeImageConditionTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryExchangeImageConditionTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryExchangeImageConditionTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryExchangeImageConditionTemplateValidationError) ErrorName() string {
	return "StoryExchangeImageConditionTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e StoryExchangeImageConditionTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryExchangeImageConditionTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryExchangeImageConditionTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryExchangeImageConditionTemplateValidationError{}

// Validate checks the field values on UnmutePromptStyle with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UnmutePromptStyle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnmutePromptStyle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnmutePromptStyleMultiError, or nil if none found.
func (m *UnmutePromptStyle) ValidateAll() error {
	return m.validate(true)
}

func (m *UnmutePromptStyle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for X

	// no validation rules for Y

	// no validation rules for FontSize

	// no validation rules for FontName

	// no validation rules for Color

	// no validation rules for Height

	// no validation rules for Width

	if len(errors) > 0 {
		return UnmutePromptStyleMultiError(errors)
	}

	return nil
}

// UnmutePromptStyleMultiError is an error wrapping multiple validation errors
// returned by UnmutePromptStyle.ValidateAll() if the designated constraints
// aren't met.
type UnmutePromptStyleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnmutePromptStyleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnmutePromptStyleMultiError) AllErrors() []error { return m }

// UnmutePromptStyleValidationError is the validation error returned by
// UnmutePromptStyle.Validate if the designated constraints aren't met.
type UnmutePromptStyleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnmutePromptStyleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnmutePromptStyleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnmutePromptStyleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnmutePromptStyleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnmutePromptStyleValidationError) ErrorName() string {
	return "UnmutePromptStyleValidationError"
}

// Error satisfies the builtin error interface
func (e UnmutePromptStyleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnmutePromptStyle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnmutePromptStyleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnmutePromptStyleValidationError{}

// Validate checks the field values on StoryPlayUnmuteConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayUnmuteConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayUnmuteConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayUnmuteConfigMultiError, or nil if none found.
func (m *StoryPlayUnmuteConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPrompt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayUnmuteConfigValidationError{
					field:  "Prompt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayUnmuteConfigValidationError{
					field:  "Prompt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrompt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayUnmuteConfigValidationError{
				field:  "Prompt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ResourceType

	// no validation rules for ResourceUrl

	// no validation rules for EndResourceType

	// no validation rules for EndResourceUrl

	for idx, item := range m.GetCustomAiResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayUnmuteConfigValidationError{
						field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayUnmuteConfigValidationError{
						field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayUnmuteConfigValidationError{
					field:  fmt.Sprintf("CustomAiResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MaxTryCount

	// no validation rules for Intention

	if all {
		switch v := interface{}(m.GetCommonPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayUnmuteConfigValidationError{
					field:  "CommonPlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayUnmuteConfigValidationError{
					field:  "CommonPlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayUnmuteConfigValidationError{
				field:  "CommonPlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayUnmuteConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayUnmuteConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayUnmuteConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.ThumbnailUrl != nil {
		// no validation rules for ThumbnailUrl
	}

	if m.EndThumbnailUrl != nil {
		// no validation rules for EndThumbnailUrl
	}

	if len(errors) > 0 {
		return StoryPlayUnmuteConfigMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayUnmuteConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayUnmuteConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteConfigMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteConfigValidationError is the validation error returned by
// StoryPlayUnmuteConfig.Validate if the designated constraints aren't met.
type StoryPlayUnmuteConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteConfigValidationError) ErrorName() string {
	return "StoryPlayUnmuteConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteConfigValidationError{}

// Validate checks the field values on CommonStoryPlayConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CommonStoryPlayConditionTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CommonStoryPlayConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CommonStoryPlayConditionTemplateMultiError, or nil if none found.
func (m *CommonStoryPlayConditionTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *CommonStoryPlayConditionTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CommonStoryPlayConditionTemplateValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CommonStoryPlayConditionTemplateValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CommonStoryPlayConditionTemplateValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CommonStoryPlayConditionTemplateMultiError(errors)
	}

	return nil
}

// CommonStoryPlayConditionTemplateMultiError is an error wrapping multiple
// validation errors returned by
// CommonStoryPlayConditionTemplate.ValidateAll() if the designated
// constraints aren't met.
type CommonStoryPlayConditionTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CommonStoryPlayConditionTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CommonStoryPlayConditionTemplateMultiError) AllErrors() []error { return m }

// CommonStoryPlayConditionTemplateValidationError is the validation error
// returned by CommonStoryPlayConditionTemplate.Validate if the designated
// constraints aren't met.
type CommonStoryPlayConditionTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CommonStoryPlayConditionTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CommonStoryPlayConditionTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CommonStoryPlayConditionTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CommonStoryPlayConditionTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CommonStoryPlayConditionTemplateValidationError) ErrorName() string {
	return "CommonStoryPlayConditionTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e CommonStoryPlayConditionTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCommonStoryPlayConditionTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CommonStoryPlayConditionTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CommonStoryPlayConditionTemplateValidationError{}

// Validate checks the field values on StoryPlayTurtleConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryPlayTurtleConditionTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayTurtleConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayTurtleConditionTemplateMultiError, or nil if none found.
func (m *StoryPlayTurtleConditionTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayTurtleConditionTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Summary

	// no validation rules for CoverImageUrl

	// no validation rules for Caption

	// no validation rules for IntentPrompt

	// no validation rules for Typ

	if len(errors) > 0 {
		return StoryPlayTurtleConditionTemplateMultiError(errors)
	}

	return nil
}

// StoryPlayTurtleConditionTemplateMultiError is an error wrapping multiple
// validation errors returned by
// StoryPlayTurtleConditionTemplate.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayTurtleConditionTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayTurtleConditionTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayTurtleConditionTemplateMultiError) AllErrors() []error { return m }

// StoryPlayTurtleConditionTemplateValidationError is the validation error
// returned by StoryPlayTurtleConditionTemplate.Validate if the designated
// constraints aren't met.
type StoryPlayTurtleConditionTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayTurtleConditionTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayTurtleConditionTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayTurtleConditionTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayTurtleConditionTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayTurtleConditionTemplateValidationError) ErrorName() string {
	return "StoryPlayTurtleConditionTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayTurtleConditionTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayTurtleConditionTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayTurtleConditionTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayTurtleConditionTemplateValidationError{}

// Validate checks the field values on StoryPlayUnmuteConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryPlayUnmuteConditionTemplate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayUnmuteConditionTemplate with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayUnmuteConditionTemplateMultiError, or nil if none found.
func (m *StoryPlayUnmuteConditionTemplate) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteConditionTemplate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetDefaultCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayUnmuteConditionTemplateValidationError{
					field:  "DefaultCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayUnmuteConditionTemplateValidationError{
					field:  "DefaultCondition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayUnmuteConditionTemplateValidationError{
				field:  "DefaultCondition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Typ

	// no validation rules for CoverImageUrl

	if len(errors) > 0 {
		return StoryPlayUnmuteConditionTemplateMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteConditionTemplateMultiError is an error wrapping multiple
// validation errors returned by
// StoryPlayUnmuteConditionTemplate.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayUnmuteConditionTemplateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteConditionTemplateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteConditionTemplateMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteConditionTemplateValidationError is the validation error
// returned by StoryPlayUnmuteConditionTemplate.Validate if the designated
// constraints aren't met.
type StoryPlayUnmuteConditionTemplateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteConditionTemplateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteConditionTemplateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteConditionTemplateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteConditionTemplateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteConditionTemplateValidationError) ErrorName() string {
	return "StoryPlayUnmuteConditionTemplateValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteConditionTemplateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteConditionTemplate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteConditionTemplateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteConditionTemplateValidationError{}

// Validate checks the field values on StoryPlayUnmuteCondition with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayUnmuteCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayUnmuteCondition with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayUnmuteConditionMultiError, or nil if none found.
func (m *StoryPlayUnmuteCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Prompt

	if len(errors) > 0 {
		return StoryPlayUnmuteConditionMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteConditionMultiError is an error wrapping multiple validation
// errors returned by StoryPlayUnmuteCondition.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayUnmuteConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteConditionMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteConditionValidationError is the validation error returned by
// StoryPlayUnmuteCondition.Validate if the designated constraints aren't met.
type StoryPlayUnmuteConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteConditionValidationError) ErrorName() string {
	return "StoryPlayUnmuteConditionValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteConditionValidationError{}

// Validate checks the field values on StoryPlayExchangeImageCondition with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayExchangeImageCondition) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayExchangeImageCondition with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayExchangeImageConditionMultiError, or nil if none found.
func (m *StoryPlayExchangeImageCondition) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageCondition) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Tips

	// no validation rules for PositiveGuide

	// no validation rules for PartialMatchGuide

	// no validation rules for NegativeGuide

	// no validation rules for LlmPrompt

	if len(errors) > 0 {
		return StoryPlayExchangeImageConditionMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageConditionMultiError is an error wrapping multiple
// validation errors returned by StoryPlayExchangeImageCondition.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayExchangeImageConditionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageConditionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageConditionMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageConditionValidationError is the validation error
// returned by StoryPlayExchangeImageCondition.Validate if the designated
// constraints aren't met.
type StoryPlayExchangeImageConditionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageConditionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayExchangeImageConditionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayExchangeImageConditionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayExchangeImageConditionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageConditionValidationError) ErrorName() string {
	return "StoryPlayExchangeImageConditionValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageConditionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageCondition.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageConditionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageConditionValidationError{}

// Validate checks the field values on StoryPlayUnmuteContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayUnmuteContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayUnmuteContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayUnmuteContextMultiError, or nil if none found.
func (m *StoryPlayUnmuteContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsFinished

	// no validation rules for AiResponse

	// no validation rules for TryCount

	if len(errors) > 0 {
		return StoryPlayUnmuteContextMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayUnmuteContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayUnmuteContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteContextMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteContextValidationError is the validation error returned by
// StoryPlayUnmuteContext.Validate if the designated constraints aren't met.
type StoryPlayUnmuteContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteContextValidationError) ErrorName() string {
	return "StoryPlayUnmuteContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteContextValidationError{}

// Validate checks the field values on StoryPlayExchangeImageContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayExchangeImageContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayExchangeImageContext with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StoryPlayExchangeImageContextMultiError, or nil if none found.
func (m *StoryPlayExchangeImageContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentNodeId

	// no validation rules for CurrentTryCount

	// no validation rules for CurrentSuccessProgress

	// no validation rules for IsFinished

	for idx, item := range m.GetUserExchangeImageUrls() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayExchangeImageContextValidationError{
						field:  fmt.Sprintf("UserExchangeImageUrls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayExchangeImageContextValidationError{
						field:  fmt.Sprintf("UserExchangeImageUrls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayExchangeImageContextValidationError{
					field:  fmt.Sprintf("UserExchangeImageUrls[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TryCount

	if len(errors) > 0 {
		return StoryPlayExchangeImageContextMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageContextMultiError is an error wrapping multiple
// validation errors returned by StoryPlayExchangeImageContext.ValidateAll()
// if the designated constraints aren't met.
type StoryPlayExchangeImageContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageContextMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageContextValidationError is the validation error
// returned by StoryPlayExchangeImageContext.Validate if the designated
// constraints aren't met.
type StoryPlayExchangeImageContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayExchangeImageContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayExchangeImageContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayExchangeImageContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageContextValidationError) ErrorName() string {
	return "StoryPlayExchangeImageContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageContextValidationError{}

// Validate checks the field values on StoryPlayExchangeImageConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayExchangeImageConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayExchangeImageConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayExchangeImageConfigMultiError, or nil if none found.
func (m *StoryPlayExchangeImageConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PlayMode

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayExchangeImageConfigValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayExchangeImageConfigValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayExchangeImageConfigValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCommonPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayExchangeImageConfigValidationError{
					field:  "CommonPlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayExchangeImageConfigValidationError{
					field:  "CommonPlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCommonPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayExchangeImageConfigValidationError{
				field:  "CommonPlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayExchangeImageConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayExchangeImageConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayExchangeImageConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayExchangeImageConfigMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageConfigMultiError is an error wrapping multiple
// validation errors returned by StoryPlayExchangeImageConfig.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayExchangeImageConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageConfigMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageConfigValidationError is the validation error returned
// by StoryPlayExchangeImageConfig.Validate if the designated constraints
// aren't met.
type StoryPlayExchangeImageConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayExchangeImageConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayExchangeImageConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayExchangeImageConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageConfigValidationError) ErrorName() string {
	return "StoryPlayExchangeImageConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageConfigValidationError{}

// Validate checks the field values on StorySummary_UnlockedUsersInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StorySummary_UnlockedUsersInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StorySummary_UnlockedUsersInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// StorySummary_UnlockedUsersInfoMultiError, or nil if none found.
func (m *StorySummary_UnlockedUsersInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StorySummary_UnlockedUsersInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StorySummary_UnlockedUsersInfoValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StorySummary_UnlockedUsersInfoValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StorySummary_UnlockedUsersInfoValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalCount

	// no validation rules for HasMore

	if len(errors) > 0 {
		return StorySummary_UnlockedUsersInfoMultiError(errors)
	}

	return nil
}

// StorySummary_UnlockedUsersInfoMultiError is an error wrapping multiple
// validation errors returned by StorySummary_UnlockedUsersInfo.ValidateAll()
// if the designated constraints aren't met.
type StorySummary_UnlockedUsersInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StorySummary_UnlockedUsersInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StorySummary_UnlockedUsersInfoMultiError) AllErrors() []error { return m }

// StorySummary_UnlockedUsersInfoValidationError is the validation error
// returned by StorySummary_UnlockedUsersInfo.Validate if the designated
// constraints aren't met.
type StorySummary_UnlockedUsersInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StorySummary_UnlockedUsersInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StorySummary_UnlockedUsersInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StorySummary_UnlockedUsersInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StorySummary_UnlockedUsersInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StorySummary_UnlockedUsersInfoValidationError) ErrorName() string {
	return "StorySummary_UnlockedUsersInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StorySummary_UnlockedUsersInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStorySummary_UnlockedUsersInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StorySummary_UnlockedUsersInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StorySummary_UnlockedUsersInfoValidationError{}

// Validate checks the field values on StoryPlayExchangeImageExample_Case with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryPlayExchangeImageExample_Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayExchangeImageExample_Case
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryPlayExchangeImageExample_CaseMultiError, or nil if none found.
func (m *StoryPlayExchangeImageExample_Case) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageExample_Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExchangeImageUrl

	// no validation rules for AiResponse

	if len(errors) > 0 {
		return StoryPlayExchangeImageExample_CaseMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageExample_CaseMultiError is an error wrapping multiple
// validation errors returned by
// StoryPlayExchangeImageExample_Case.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayExchangeImageExample_CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageExample_CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageExample_CaseMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageExample_CaseValidationError is the validation error
// returned by StoryPlayExchangeImageExample_Case.Validate if the designated
// constraints aren't met.
type StoryPlayExchangeImageExample_CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageExample_CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayExchangeImageExample_CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayExchangeImageExample_CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayExchangeImageExample_CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageExample_CaseValidationError) ErrorName() string {
	return "StoryPlayExchangeImageExample_CaseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageExample_CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageExample_Case.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageExample_CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageExample_CaseValidationError{}

// Validate checks the field values on StoryPlayUnmuteExample_Case with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayUnmuteExample_Case) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayUnmuteExample_Case with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayUnmuteExample_CaseMultiError, or nil if none found.
func (m *StoryPlayUnmuteExample_Case) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteExample_Case) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Prompt

	if len(errors) > 0 {
		return StoryPlayUnmuteExample_CaseMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteExample_CaseMultiError is an error wrapping multiple
// validation errors returned by StoryPlayUnmuteExample_Case.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayUnmuteExample_CaseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteExample_CaseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteExample_CaseMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteExample_CaseValidationError is the validation error returned
// by StoryPlayUnmuteExample_Case.Validate if the designated constraints
// aren't met.
type StoryPlayUnmuteExample_CaseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteExample_CaseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteExample_CaseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteExample_CaseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteExample_CaseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteExample_CaseValidationError) ErrorName() string {
	return "StoryPlayUnmuteExample_CaseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteExample_CaseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteExample_Case.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteExample_CaseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteExample_CaseValidationError{}

// Validate checks the field values on StoryPlayUnmuteConfig_CustomAiResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StoryPlayUnmuteConfig_CustomAiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryPlayUnmuteConfig_CustomAiResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// StoryPlayUnmuteConfig_CustomAiResponseMultiError, or nil if none found.
func (m *StoryPlayUnmuteConfig_CustomAiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayUnmuteConfig_CustomAiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RuleDescription

	// no validation rules for RuleResult

	if len(errors) > 0 {
		return StoryPlayUnmuteConfig_CustomAiResponseMultiError(errors)
	}

	return nil
}

// StoryPlayUnmuteConfig_CustomAiResponseMultiError is an error wrapping
// multiple validation errors returned by
// StoryPlayUnmuteConfig_CustomAiResponse.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayUnmuteConfig_CustomAiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayUnmuteConfig_CustomAiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayUnmuteConfig_CustomAiResponseMultiError) AllErrors() []error { return m }

// StoryPlayUnmuteConfig_CustomAiResponseValidationError is the validation
// error returned by StoryPlayUnmuteConfig_CustomAiResponse.Validate if the
// designated constraints aren't met.
type StoryPlayUnmuteConfig_CustomAiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayUnmuteConfig_CustomAiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayUnmuteConfig_CustomAiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayUnmuteConfig_CustomAiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayUnmuteConfig_CustomAiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayUnmuteConfig_CustomAiResponseValidationError) ErrorName() string {
	return "StoryPlayUnmuteConfig_CustomAiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayUnmuteConfig_CustomAiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayUnmuteConfig_CustomAiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayUnmuteConfig_CustomAiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayUnmuteConfig_CustomAiResponseValidationError{}

// Validate checks the field values on
// StoryPlayExchangeImageContext_UserExchangeImageUrls with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayExchangeImageContext_UserExchangeImageUrls) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoryPlayExchangeImageContext_UserExchangeImageUrls with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// StoryPlayExchangeImageContext_UserExchangeImageUrlsMultiError, or nil if
// none found.
func (m *StoryPlayExchangeImageContext_UserExchangeImageUrls) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageContext_UserExchangeImageUrls) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NodeId

	if len(errors) > 0 {
		return StoryPlayExchangeImageContext_UserExchangeImageUrlsMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageContext_UserExchangeImageUrlsMultiError is an error
// wrapping multiple validation errors returned by
// StoryPlayExchangeImageContext_UserExchangeImageUrls.ValidateAll() if the
// designated constraints aren't met.
type StoryPlayExchangeImageContext_UserExchangeImageUrlsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageContext_UserExchangeImageUrlsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageContext_UserExchangeImageUrlsMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError is the
// validation error returned by
// StoryPlayExchangeImageContext_UserExchangeImageUrls.Validate if the
// designated constraints aren't met.
type StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError) ErrorName() string {
	return "StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageContext_UserExchangeImageUrls.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageContext_UserExchangeImageUrlsValidationError{}

// Validate checks the field values on StoryPlayExchangeImageConfig_Node with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *StoryPlayExchangeImageConfig_Node) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayExchangeImageConfig_Node
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryPlayExchangeImageConfig_NodeMultiError, or nil if none found.
func (m *StoryPlayExchangeImageConfig_Node) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayExchangeImageConfig_Node) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ResourceType

	// no validation rules for ResourceUrl

	if all {
		switch v := interface{}(m.GetCondition()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayExchangeImageConfig_NodeValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayExchangeImageConfig_NodeValidationError{
					field:  "Condition",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCondition()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayExchangeImageConfig_NodeValidationError{
				field:  "Condition",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllowUseAlbum

	// no validation rules for MaxTryCount

	// no validation rules for NeedCreateDiyTemplate

	if m.ThumbnailUrl != nil {
		// no validation rules for ThumbnailUrl
	}

	if len(errors) > 0 {
		return StoryPlayExchangeImageConfig_NodeMultiError(errors)
	}

	return nil
}

// StoryPlayExchangeImageConfig_NodeMultiError is an error wrapping multiple
// validation errors returned by
// StoryPlayExchangeImageConfig_Node.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayExchangeImageConfig_NodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayExchangeImageConfig_NodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayExchangeImageConfig_NodeMultiError) AllErrors() []error { return m }

// StoryPlayExchangeImageConfig_NodeValidationError is the validation error
// returned by StoryPlayExchangeImageConfig_Node.Validate if the designated
// constraints aren't met.
type StoryPlayExchangeImageConfig_NodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayExchangeImageConfig_NodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayExchangeImageConfig_NodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayExchangeImageConfig_NodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayExchangeImageConfig_NodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayExchangeImageConfig_NodeValidationError) ErrorName() string {
	return "StoryPlayExchangeImageConfig_NodeValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayExchangeImageConfig_NodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayExchangeImageConfig_Node.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayExchangeImageConfig_NodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayExchangeImageConfig_NodeValidationError{}
