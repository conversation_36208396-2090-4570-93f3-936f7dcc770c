syntax = "proto3";

package api.items.story.types.v1;

import "validate/validate.proto";
import "api/items/story/types/v1/base_types.proto";
option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/resource/types/v1/types.proto";

message StickerTransform {
	message Location {
		// 浮点型，表示距离原点的比例 0 ~ 1
		string x = 1;
		// 浮点型，表示距离原点的比例 0 ~ 1
		string y = 2;
	}
	Location location = 1;
	message Size {
		// 宽缩放比例，浮点型，0 ~ 1
		string width = 1;
		// 高缩放比例，浮点型，0 ~ 1
		string height = 2;
	}
	Size size = 2;
}

message HideSticker {
	string id = 1;
	string from_story_id = 2;
	api.resource.types.v1.Resource resource = 3;
	StickerTransform transform = 4;
}

// hide 玩法上下文
message StoryPlayHideContext {
  // 是否消费过此 story
  bool is_consumed = 1;
  // 解锁获得的贴纸
  repeated HideSticker unlocked_stickers = 2;
}
 
// 获取贴纸的触发类型
enum HideStickerTriggerType {
	STICKER_TRIGGER_TYPE_UNSPECIFIED = 0;
	// 连续点击
	STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK = 1;
	// 拖动图层
	STICKER_TRIGGER_TYPE_DRAG = 2;
	// 点赞
	STICKER_TRIGGER_TYPE_LIKE = 3;
	// 摇动手机
	STICKER_TRIGGER_TYPE_SHAKE = 4;
	// 长按
	STICKER_TRIGGER_TYPE_LONG_PRESS = 5;
}

// 连续点击
message StickerWithTriggerTypeContinuousClickData {
	// 创作时需要指定的 Stickers 及其所在的点击判定位置
	// 服务端会根据 Location 及半径 聚合这些 stickers 
	repeated HideSticker stickers_with_click_locations_in_creation = 1;

	message StickersWithClickLocationInConsume {
		repeated HideSticker stickers = 1;
		string x = 2;
		string y = 3;
	}
	repeated StickersWithClickLocationInConsume stickers_with_click_locations_in_consume = 2;
	uint32 need_click_count = 3;
}

// 拖动图层
message StickerWithTriggerTypeDragData {
	// 抠图对象
	message CutObject {
		// 创作时忽略
		string mask_image_url = 1;
		// 消费时忽略
		string mask_image_key = 2;

		HideSticker sticker = 4;
	}
	repeated CutObject cut_objects = 2;
}

// 点赞
message StickerWithTriggerTypeLikeData {
	repeated HideSticker stickers = 1;
}

// 摇动手机
message StickerWithTriggerTypeShakeData {
	repeated HideSticker stickers = 1;
	uint32 need_shake_count = 2;
	uint32 need_shake_duration_seconds = 3;
}

// 长按
message StickerWithTriggerTypeLongPressData {
	repeated HideSticker stickers = 1;
	uint32 need_long_press_duration_seconds = 2;
}

message StickerWithTriggerType {
	HideStickerTriggerType trigger_type = 1;
	oneof data {
		StickerWithTriggerTypeContinuousClickData continuous_click_data = 2;
		StickerWithTriggerTypeDragData drag_data = 3;
		StickerWithTriggerTypeLikeData like_data = 4;
		StickerWithTriggerTypeShakeData shake_data = 5;
		StickerWithTriggerTypeLongPressData long_press_data = 6;
	}
}

// hide 玩法配置
message StoryPlayHideConfig {
	repeated StickerWithTriggerType sticker_with_trigger_types = 1;
	api.resource.types.v1.Resource background_image = 2[(validate.rules).message = {
		required: true		
	}];
	repeated api.items.story.types.v1.AttachmentText attachment_texts = 3;
	// 创作侧忽略，此值是消费侧使用，会根据创作 sticker_with_trigger_types 计算而来
	repeated HideSticker all_stickers = 4;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 5;
	optional api.resource.types.v1.Resource cover = 6;
}
  
