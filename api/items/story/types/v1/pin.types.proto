syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/resource/types/v1/types.proto";
import "api/items/story/types/v1/base_types.proto";

message PinEmojiArear {
	string x = 1;
	string y = 2;
	string width = 3;
	string height = 4;
}
message PinEmojiResource {
	oneof data  {
		api.resource.types.v1.Resource generated_emoji_resource = 1;
		string default_emoji = 2;
	}
	PinEmojiArear area = 3;
}


message StoryPinContext {
	bool is_unlocked = 1;
}

message StoryPinConfig {
	api.resource.types.v1.Resource background_image = 1;
	repeated PinEmojiResource pin_emoji_resources = 2;
	api.items.story.types.v1.AttachmentText caption = 3;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 4;
}