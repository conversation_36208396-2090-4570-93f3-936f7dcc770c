// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/base_types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/resource/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ResourceType int32

const (
	ResourceType_RESOURCE_TYPE_UNSPECIFIED ResourceType = 0
	ResourceType_RESOURCE_TYPE_IMAGE       ResourceType = 1
	ResourceType_RESOURCE_TYPE_VIDEO       ResourceType = 2
	ResourceType_RESOURCE_TYPE_NONE        ResourceType = 3
	ResourceType_RESOURCE_TYPE_GRADIENT    ResourceType = 4
)

// Enum value maps for ResourceType.
var (
	ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
		3: "RESOURCE_TYPE_NONE",
		4: "RESOURCE_TYPE_GRADIENT",
	}
	ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
		"RESOURCE_TYPE_NONE":        3,
		"RESOURCE_TYPE_GRADIENT":    4,
	}
)

func (x ResourceType) Enum() *ResourceType {
	p := new(ResourceType)
	*p = x
	return p
}

func (x ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_base_types_proto_enumTypes[0].Descriptor()
}

func (ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_base_types_proto_enumTypes[0]
}

func (x ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ResourceType.Descriptor instead.
func (ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{0}
}

// - 拍摄模式（Shooting Mode），如Duo Camera、普通拍摄(前/后置)、Pano全景、延时、慢动作、电影模式(Cinematic)、边走边拍、对镜头讲话、打字Caption等
type ShootingModel int32

const (
	ShootingModel_SHOOTING_MODEL_UNSPECIFIED ShootingModel = 0
	// 双摄
	ShootingModel_SHOOTING_MODEL_DUO_CAMERA ShootingModel = 1
	// 前置摄像头
	ShootingModel_SHOOTING_MODEL_FRONT_CAMERA ShootingModel = 2
	// 后置摄像头
	ShootingModel_SHOOTING_MODEL_BACK_CAMERA ShootingModel = 3
	// 全景
	ShootingModel_SHOOTING_MODEL_PANO ShootingModel = 4
	// 延时
	ShootingModel_SHOOTING_MODEL_TIME_LAPSE ShootingModel = 5
	// 慢动作
	ShootingModel_SHOOTING_MODEL_SLOW_MOTION ShootingModel = 6
	// 电影模式
	ShootingModel_SHOOTING_MODEL_CINEMATIC ShootingModel = 7
	// 边走边拍
	ShootingModel_SHOOTING_MODEL_WALK_AND_SHOOT ShootingModel = 8
	// 对镜头讲话
	ShootingModel_SHOOTING_MODEL_TALK_TO_CAMERA ShootingModel = 9
	// 打字Caption
	ShootingModel_SHOOTING_MODEL_CAPTION ShootingModel = 10
)

// Enum value maps for ShootingModel.
var (
	ShootingModel_name = map[int32]string{
		0:  "SHOOTING_MODEL_UNSPECIFIED",
		1:  "SHOOTING_MODEL_DUO_CAMERA",
		2:  "SHOOTING_MODEL_FRONT_CAMERA",
		3:  "SHOOTING_MODEL_BACK_CAMERA",
		4:  "SHOOTING_MODEL_PANO",
		5:  "SHOOTING_MODEL_TIME_LAPSE",
		6:  "SHOOTING_MODEL_SLOW_MOTION",
		7:  "SHOOTING_MODEL_CINEMATIC",
		8:  "SHOOTING_MODEL_WALK_AND_SHOOT",
		9:  "SHOOTING_MODEL_TALK_TO_CAMERA",
		10: "SHOOTING_MODEL_CAPTION",
	}
	ShootingModel_value = map[string]int32{
		"SHOOTING_MODEL_UNSPECIFIED":    0,
		"SHOOTING_MODEL_DUO_CAMERA":     1,
		"SHOOTING_MODEL_FRONT_CAMERA":   2,
		"SHOOTING_MODEL_BACK_CAMERA":    3,
		"SHOOTING_MODEL_PANO":           4,
		"SHOOTING_MODEL_TIME_LAPSE":     5,
		"SHOOTING_MODEL_SLOW_MOTION":    6,
		"SHOOTING_MODEL_CINEMATIC":      7,
		"SHOOTING_MODEL_WALK_AND_SHOOT": 8,
		"SHOOTING_MODEL_TALK_TO_CAMERA": 9,
		"SHOOTING_MODEL_CAPTION":        10,
	}
)

func (x ShootingModel) Enum() *ShootingModel {
	p := new(ShootingModel)
	*p = x
	return p
}

func (x ShootingModel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShootingModel) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_base_types_proto_enumTypes[1].Descriptor()
}

func (ShootingModel) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_base_types_proto_enumTypes[1]
}

func (x ShootingModel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShootingModel.Descriptor instead.
func (ShootingModel) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{1}
}

type ShootingStyle int32

const (
	ShootingStyle_Unspecified ShootingStyle = 0
	ShootingStyle_Default     ShootingStyle = 1
	ShootingStyle_Extrovert   ShootingStyle = 2
	ShootingStyle_Introvert   ShootingStyle = 3
	ShootingStyle_Humorist    ShootingStyle = 4
	ShootingStyle_Aesthetics  ShootingStyle = 5
	ShootingStyle_Socializer  ShootingStyle = 6
	ShootingStyle_Playful     ShootingStyle = 7
)

// Enum value maps for ShootingStyle.
var (
	ShootingStyle_name = map[int32]string{
		0: "Unspecified",
		1: "Default",
		2: "Extrovert",
		3: "Introvert",
		4: "Humorist",
		5: "Aesthetics",
		6: "Socializer",
		7: "Playful",
	}
	ShootingStyle_value = map[string]int32{
		"Unspecified": 0,
		"Default":     1,
		"Extrovert":   2,
		"Introvert":   3,
		"Humorist":    4,
		"Aesthetics":  5,
		"Socializer":  6,
		"Playful":     7,
	}
)

func (x ShootingStyle) Enum() *ShootingStyle {
	p := new(ShootingStyle)
	*p = x
	return p
}

func (x ShootingStyle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ShootingStyle) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_base_types_proto_enumTypes[2].Descriptor()
}

func (ShootingStyle) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_base_types_proto_enumTypes[2]
}

func (x ShootingStyle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ShootingStyle.Descriptor instead.
func (ShootingStyle) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{2}
}

type AttachmentText struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 文本内容
	Text string `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// 字体名称
	FontName string `protobuf:"bytes,2,opt,name=font_name,json=fontName,proto3" json:"font_name,omitempty"`
	// 字体大小
	FontSize uint32 `protobuf:"varint,3,opt,name=font_size,json=fontSize,proto3" json:"font_size,omitempty"`
	// 颜色
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
	// x centerY 相对坐标
	X string `protobuf:"bytes,5,opt,name=x,proto3" json:"x,omitempty"`
	// y centerY 相对坐标
	Y string `protobuf:"bytes,6,opt,name=y,proto3" json:"y,omitempty"`
	// 宽度 相对屏幕宽度
	Width string `protobuf:"bytes,7,opt,name=width,proto3" json:"width,omitempty"`
	// 高度 相对屏幕高度
	Height string `protobuf:"bytes,8,opt,name=height,proto3" json:"height,omitempty"`
	// 对齐方式 center, left, right
	Alignment string `protobuf:"bytes,9,opt,name=alignment,proto3" json:"alignment,omitempty"`
	// fillStyle none, white, textcolor
	FillStyle string `protobuf:"bytes,10,opt,name=fill_style,json=fillStyle,proto3" json:"fill_style,omitempty"`
	// 旋转
	Rotation string `protobuf:"bytes,11,opt,name=rotation,proto3" json:"rotation,omitempty"`
	// 缩放
	Scale         string                   `protobuf:"bytes,12,opt,name=scale,proto3" json:"scale,omitempty"`
	AtUsers       []*AttachmentText_AtUser `protobuf:"bytes,13,rep,name=at_users,json=atUsers,proto3" json:"at_users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttachmentText) Reset() {
	*x = AttachmentText{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttachmentText) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttachmentText) ProtoMessage() {}

func (x *AttachmentText) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttachmentText.ProtoReflect.Descriptor instead.
func (*AttachmentText) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{0}
}

func (x *AttachmentText) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *AttachmentText) GetFontName() string {
	if x != nil {
		return x.FontName
	}
	return ""
}

func (x *AttachmentText) GetFontSize() uint32 {
	if x != nil {
		return x.FontSize
	}
	return 0
}

func (x *AttachmentText) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

func (x *AttachmentText) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *AttachmentText) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

func (x *AttachmentText) GetWidth() string {
	if x != nil {
		return x.Width
	}
	return ""
}

func (x *AttachmentText) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

func (x *AttachmentText) GetAlignment() string {
	if x != nil {
		return x.Alignment
	}
	return ""
}

func (x *AttachmentText) GetFillStyle() string {
	if x != nil {
		return x.FillStyle
	}
	return ""
}

func (x *AttachmentText) GetRotation() string {
	if x != nil {
		return x.Rotation
	}
	return ""
}

func (x *AttachmentText) GetScale() string {
	if x != nil {
		return x.Scale
	}
	return ""
}

func (x *AttachmentText) GetAtUsers() []*AttachmentText_AtUser {
	if x != nil {
		return x.AtUsers
	}
	return nil
}

type ExampleCommonInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 示例的提示
	Tips string `protobuf:"bytes,1,opt,name=tips,proto3" json:"tips,omitempty"`
	// story 的图片
	// 目前，当是换图玩法时：
	// 1. 马赛克时，数组长度固定为1， 表示封面图
	// 2. 线性解锁时，数组长度固定为2，第一个表示封面图，第二个表示下一个节点的封面图
	// 当是海龟汤玩法时，数组长度固定为1，表示封面图
	ImageUrls     []string `protobuf:"bytes,2,rep,name=image_urls,json=imageUrls,proto3" json:"image_urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ExampleCommonInfo) Reset() {
	*x = ExampleCommonInfo{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExampleCommonInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExampleCommonInfo) ProtoMessage() {}

func (x *ExampleCommonInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExampleCommonInfo.ProtoReflect.Descriptor instead.
func (*ExampleCommonInfo) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{1}
}

func (x *ExampleCommonInfo) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *ExampleCommonInfo) GetImageUrls() []string {
	if x != nil {
		return x.ImageUrls
	}
	return nil
}

type Resource struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceType ResourceType           `protobuf:"varint,1,opt,name=resource_type,json=resourceType,proto3,enum=api.items.story.types.v1.ResourceType" json:"resource_type,omitempty"`
	// 资源 key
	ResourceKey string `protobuf:"bytes,2,opt,name=resource_key,json=resourceKey,proto3" json:"resource_key,omitempty"`
	// 资源 url，创建时可以留空，返回时服务会拼接
	ResourceUrl string `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 如果是视频，则需要提供封面图
	// 如果是图片，直接用 resource_key 即可
	CoverImageKey string `protobuf:"bytes,4,opt,name=cover_image_key,json=coverImageKey,proto3" json:"cover_image_key,omitempty"`
	// 封面图 url，创建时可以留空，返回时服务会拼接
	CoverImageUrl string `protobuf:"bytes,5,opt,name=cover_image_url,json=coverImageUrl,proto3" json:"cover_image_url,omitempty"`
	// 封面宽度
	CoverWidth uint32 `protobuf:"varint,6,opt,name=cover_width,json=coverWidth,proto3" json:"cover_width,omitempty"`
	// 封面高度
	CoverHeight   uint32 `protobuf:"varint,7,opt,name=cover_height,json=coverHeight,proto3" json:"cover_height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Resource) Reset() {
	*x = Resource{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Resource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resource) ProtoMessage() {}

func (x *Resource) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resource.ProtoReflect.Descriptor instead.
func (*Resource) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{2}
}

func (x *Resource) GetResourceType() ResourceType {
	if x != nil {
		return x.ResourceType
	}
	return ResourceType_RESOURCE_TYPE_UNSPECIFIED
}

func (x *Resource) GetResourceKey() string {
	if x != nil {
		return x.ResourceKey
	}
	return ""
}

func (x *Resource) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *Resource) GetCoverImageKey() string {
	if x != nil {
		return x.CoverImageKey
	}
	return ""
}

func (x *Resource) GetCoverImageUrl() string {
	if x != nil {
		return x.CoverImageUrl
	}
	return ""
}

func (x *Resource) GetCoverWidth() uint32 {
	if x != nil {
		return x.CoverWidth
	}
	return 0
}

func (x *Resource) GetCoverHeight() uint32 {
	if x != nil {
		return x.CoverHeight
	}
	return 0
}

type Cover struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// CoverType，无图 none/渐变图 gradient/自定义上传图片 image/自定义上传视频 video
	CoverType ResourceType `protobuf:"varint,1,opt,name=CoverType,proto3,enum=api.items.story.types.v1.ResourceType" json:"CoverType,omitempty"`
	// 只在 CoverType 为 image 时生效，资源 key
	ResourceKey string `protobuf:"bytes,2,opt,name=resource_key,json=resourceKey,proto3" json:"resource_key,omitempty"`
	// 只在 CoverType 为 image 时生效，资源 url，创建时可以留空，返回时服务会拼接
	ResourceUrl string `protobuf:"bytes,3,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 只在 CoverType 为 gradient 时生效
	Gradient string `protobuf:"bytes,4,opt,name=gradient,proto3" json:"gradient,omitempty"`
	// 封面宽度
	CoverWidth uint32 `protobuf:"varint,5,opt,name=cover_width,json=coverWidth,proto3" json:"cover_width,omitempty"`
	// 封面高度
	CoverHeight uint32 `protobuf:"varint,6,opt,name=cover_height,json=coverHeight,proto3" json:"cover_height,omitempty"`
	// 只在 CoverType 为 video 时生效，资源 key
	ThumbnailKey string `protobuf:"bytes,7,opt,name=thumbnail_key,json=thumbnailKey,proto3" json:"thumbnail_key,omitempty"`
	// 只在 CoverType 为 video 时生效，资源 url，创建时可以留空，返回时服务会拼接
	ThumbnailUrl  string `protobuf:"bytes,8,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Cover) Reset() {
	*x = Cover{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Cover) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cover) ProtoMessage() {}

func (x *Cover) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cover.ProtoReflect.Descriptor instead.
func (*Cover) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{3}
}

func (x *Cover) GetCoverType() ResourceType {
	if x != nil {
		return x.CoverType
	}
	return ResourceType_RESOURCE_TYPE_UNSPECIFIED
}

func (x *Cover) GetResourceKey() string {
	if x != nil {
		return x.ResourceKey
	}
	return ""
}

func (x *Cover) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *Cover) GetGradient() string {
	if x != nil {
		return x.Gradient
	}
	return ""
}

func (x *Cover) GetCoverWidth() uint32 {
	if x != nil {
		return x.CoverWidth
	}
	return 0
}

func (x *Cover) GetCoverHeight() uint32 {
	if x != nil {
		return x.CoverHeight
	}
	return 0
}

func (x *Cover) GetThumbnailKey() string {
	if x != nil {
		return x.ThumbnailKey
	}
	return ""
}

func (x *Cover) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

type Shooting struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ShootingModel ShootingModel          `protobuf:"varint,1,opt,name=shooting_model,json=shootingModel,proto3,enum=api.items.story.types.v1.ShootingModel" json:"shooting_model,omitempty"`
	// 拍摄目的，由 LLM 生成
	Purpose       string `protobuf:"bytes,2,opt,name=purpose,proto3" json:"purpose,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Shooting) Reset() {
	*x = Shooting{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Shooting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Shooting) ProtoMessage() {}

func (x *Shooting) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Shooting.ProtoReflect.Descriptor instead.
func (*Shooting) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{4}
}

func (x *Shooting) GetShootingModel() ShootingModel {
	if x != nil {
		return x.ShootingModel
	}
	return ShootingModel_SHOOTING_MODEL_UNSPECIFIED
}

func (x *Shooting) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

type ShootingResource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Type  ResourceType           `protobuf:"varint,1,opt,name=type,proto3,enum=api.items.story.types.v1.ResourceType" json:"type,omitempty"`
	// 作为上传资源时，此字段可以不填
	ResourceUrl string `protobuf:"bytes,2,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 作为上传资源时，此字段必填
	ResourceKey string `protobuf:"bytes,3,opt,name=resource_key,json=resourceKey,proto3" json:"resource_key,omitempty"`
	// 视频封面图片url，如果type为RESOURCE_TYPE_VIDEO，会返回  video_cover_image_object_key 的 cdn 地址
	VideoCoverImageUrl *string `protobuf:"bytes,4,opt,name=video_cover_image_url,json=videoCoverImageUrl,proto3,oneof" json:"video_cover_image_url,omitempty"`
	// 视频封面图片object key，如果type为RESOURCE_TYPE_VIDEO，上传时必须设置
	VideoCoverImageObjectKey *string `protobuf:"bytes,5,opt,name=video_cover_image_object_key,json=videoCoverImageObjectKey,proto3,oneof" json:"video_cover_image_object_key,omitempty"`
	// 资源描述
	Caption string `protobuf:"bytes,6,opt,name=caption,proto3" json:"caption,omitempty"`
	// 拍摄模式
	ShootingModel ShootingModel `protobuf:"varint,7,opt,name=shooting_model,json=shootingModel,proto3,enum=api.items.story.types.v1.ShootingModel" json:"shooting_model,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ShootingResource) Reset() {
	*x = ShootingResource{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ShootingResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ShootingResource) ProtoMessage() {}

func (x *ShootingResource) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ShootingResource.ProtoReflect.Descriptor instead.
func (*ShootingResource) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{5}
}

func (x *ShootingResource) GetType() ResourceType {
	if x != nil {
		return x.Type
	}
	return ResourceType_RESOURCE_TYPE_UNSPECIFIED
}

func (x *ShootingResource) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *ShootingResource) GetResourceKey() string {
	if x != nil {
		return x.ResourceKey
	}
	return ""
}

func (x *ShootingResource) GetVideoCoverImageUrl() string {
	if x != nil && x.VideoCoverImageUrl != nil {
		return *x.VideoCoverImageUrl
	}
	return ""
}

func (x *ShootingResource) GetVideoCoverImageObjectKey() string {
	if x != nil && x.VideoCoverImageObjectKey != nil {
		return *x.VideoCoverImageObjectKey
	}
	return ""
}

func (x *ShootingResource) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *ShootingResource) GetShootingModel() ShootingModel {
	if x != nil {
		return x.ShootingModel
	}
	return ShootingModel_SHOOTING_MODEL_UNSPECIFIED
}

type Condition struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 展示给消费者的字幕
	Hint *AttachmentText `protobuf:"bytes,1,opt,name=hint,proto3" json:"hint,omitempty"`
	// llm 的 prompt，即 Creator Criteria
	Prompt        string `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Condition) Reset() {
	*x = Condition{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Condition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Condition) ProtoMessage() {}

func (x *Condition) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Condition.ProtoReflect.Descriptor instead.
func (*Condition) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{6}
}

func (x *Condition) GetHint() *AttachmentText {
	if x != nil {
		return x.Hint
	}
	return nil
}

func (x *Condition) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

type CommonPlayConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// V2 新的封面类型
	Cover *Cover `protobuf:"bytes,1,opt,name=cover,proto3" json:"cover,omitempty"`
	// V2 资源类型
	Resource *Resource `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	// V2 作者 at your friend 填写的文字，附着在封面资源上
	CoverCaptions []*AttachmentText `protobuf:"bytes,3,rep,name=cover_captions,json=coverCaptions,proto3" json:"cover_captions,omitempty"`
	// V2 作者 condition
	ConditionV2 *Condition `protobuf:"bytes,4,opt,name=condition_v2,json=conditionV2,proto3" json:"condition_v2,omitempty"`
	// V2 最多允许尝试多少次
	MaxTryCount uint32 `protobuf:"varint,5,opt,name=max_try_count,json=maxTryCount,proto3" json:"max_try_count,omitempty"`
	// V2 作者 at your friend 填写的文字，附着在解锁后的资源上
	ResourceCaptions []*AttachmentText `protobuf:"bytes,6,rep,name=resource_captions,json=resourceCaptions,proto3" json:"resource_captions,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CommonPlayConfig) Reset() {
	*x = CommonPlayConfig{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonPlayConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonPlayConfig) ProtoMessage() {}

func (x *CommonPlayConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonPlayConfig.ProtoReflect.Descriptor instead.
func (*CommonPlayConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{7}
}

func (x *CommonPlayConfig) GetCover() *Cover {
	if x != nil {
		return x.Cover
	}
	return nil
}

func (x *CommonPlayConfig) GetResource() *Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *CommonPlayConfig) GetCoverCaptions() []*AttachmentText {
	if x != nil {
		return x.CoverCaptions
	}
	return nil
}

func (x *CommonPlayConfig) GetConditionV2() *Condition {
	if x != nil {
		return x.ConditionV2
	}
	return nil
}

func (x *CommonPlayConfig) GetMaxTryCount() uint32 {
	if x != nil {
		return x.MaxTryCount
	}
	return 0
}

func (x *CommonPlayConfig) GetResourceCaptions() []*AttachmentText {
	if x != nil {
		return x.ResourceCaptions
	}
	return nil
}

type Word struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 起始时间,距离音频开始的毫秒偏移值。
	StartTime uint32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 结束时间,距离音频开始的毫秒偏移值。
	EndTime uint32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// 文本
	Text          string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Word) Reset() {
	*x = Word{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Word) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Word) ProtoMessage() {}

func (x *Word) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Word.ProtoReflect.Descriptor instead.
func (*Word) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{8}
}

func (x *Word) GetStartTime() uint32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Word) GetEndTime() uint32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Word) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type MomentCreateAttr struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Resource        *v1.Resource           `protobuf:"bytes,1,opt,name=resource,proto3,oneof" json:"resource,omitempty"`
	AttachmentTexts []*AttachmentText      `protobuf:"bytes,2,rep,name=attachment_texts,json=attachmentTexts,proto3" json:"attachment_texts,omitempty"`
	AvatarIds       []int64                `protobuf:"varint,3,rep,packed,name=avatar_ids,json=avatarIds,proto3" json:"avatar_ids,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MomentCreateAttr) Reset() {
	*x = MomentCreateAttr{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MomentCreateAttr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MomentCreateAttr) ProtoMessage() {}

func (x *MomentCreateAttr) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MomentCreateAttr.ProtoReflect.Descriptor instead.
func (*MomentCreateAttr) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{9}
}

func (x *MomentCreateAttr) GetResource() *v1.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *MomentCreateAttr) GetAttachmentTexts() []*AttachmentText {
	if x != nil {
		return x.AttachmentTexts
	}
	return nil
}

func (x *MomentCreateAttr) GetAvatarIds() []int64 {
	if x != nil {
		return x.AvatarIds
	}
	return nil
}

// @的 user
type AttachmentText_AtUser struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Id    string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 创作时，客户端可以不传，服务端下发时会自行填充
	Nickname      string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AttachmentText_AtUser) Reset() {
	*x = AttachmentText_AtUser{}
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AttachmentText_AtUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttachmentText_AtUser) ProtoMessage() {}

func (x *AttachmentText_AtUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_base_types_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttachmentText_AtUser.ProtoReflect.Descriptor instead.
func (*AttachmentText_AtUser) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_base_types_proto_rawDescGZIP(), []int{0, 0}
}

func (x *AttachmentText_AtUser) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AttachmentText_AtUser) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

var File_api_items_story_types_v1_base_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_base_types_proto_rawDesc = "" +
	"\n" +
	")api/items/story/types/v1/base_types.proto\x12\x18api.items.story.types.v1\x1a!api/resource/types/v1/types.proto\"\xaf\x03\n" +
	"\x0eAttachmentText\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\x12\x1b\n" +
	"\tfont_name\x18\x02 \x01(\tR\bfontName\x12\x1b\n" +
	"\tfont_size\x18\x03 \x01(\rR\bfontSize\x12\x14\n" +
	"\x05color\x18\x04 \x01(\tR\x05color\x12\f\n" +
	"\x01x\x18\x05 \x01(\tR\x01x\x12\f\n" +
	"\x01y\x18\x06 \x01(\tR\x01y\x12\x14\n" +
	"\x05width\x18\a \x01(\tR\x05width\x12\x16\n" +
	"\x06height\x18\b \x01(\tR\x06height\x12\x1c\n" +
	"\talignment\x18\t \x01(\tR\talignment\x12\x1d\n" +
	"\n" +
	"fill_style\x18\n" +
	" \x01(\tR\tfillStyle\x12\x1a\n" +
	"\brotation\x18\v \x01(\tR\brotation\x12\x14\n" +
	"\x05scale\x18\f \x01(\tR\x05scale\x12J\n" +
	"\bat_users\x18\r \x03(\v2/.api.items.story.types.v1.AttachmentText.AtUserR\aatUsers\x1a4\n" +
	"\x06AtUser\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\"F\n" +
	"\x11ExampleCommonInfo\x12\x12\n" +
	"\x04tips\x18\x01 \x01(\tR\x04tips\x12\x1d\n" +
	"\n" +
	"image_urls\x18\x02 \x03(\tR\timageUrls\"\xb1\x02\n" +
	"\bResource\x12K\n" +
	"\rresource_type\x18\x01 \x01(\x0e2&.api.items.story.types.v1.ResourceTypeR\fresourceType\x12!\n" +
	"\fresource_key\x18\x02 \x01(\tR\vresourceKey\x12!\n" +
	"\fresource_url\x18\x03 \x01(\tR\vresourceUrl\x12&\n" +
	"\x0fcover_image_key\x18\x04 \x01(\tR\rcoverImageKey\x12&\n" +
	"\x0fcover_image_url\x18\x05 \x01(\tR\rcoverImageUrl\x12\x1f\n" +
	"\vcover_width\x18\x06 \x01(\rR\n" +
	"coverWidth\x12!\n" +
	"\fcover_height\x18\a \x01(\rR\vcoverHeight\"\xbd\x02\n" +
	"\x05Cover\x12D\n" +
	"\tCoverType\x18\x01 \x01(\x0e2&.api.items.story.types.v1.ResourceTypeR\tCoverType\x12!\n" +
	"\fresource_key\x18\x02 \x01(\tR\vresourceKey\x12!\n" +
	"\fresource_url\x18\x03 \x01(\tR\vresourceUrl\x12\x1a\n" +
	"\bgradient\x18\x04 \x01(\tR\bgradient\x12\x1f\n" +
	"\vcover_width\x18\x05 \x01(\rR\n" +
	"coverWidth\x12!\n" +
	"\fcover_height\x18\x06 \x01(\rR\vcoverHeight\x12#\n" +
	"\rthumbnail_key\x18\a \x01(\tR\fthumbnailKey\x12#\n" +
	"\rthumbnail_url\x18\b \x01(\tR\fthumbnailUrl\"t\n" +
	"\bShooting\x12N\n" +
	"\x0eshooting_model\x18\x01 \x01(\x0e2'.api.items.story.types.v1.ShootingModelR\rshootingModel\x12\x18\n" +
	"\apurpose\x18\x02 \x01(\tR\apurpose\"\xb6\x03\n" +
	"\x10ShootingResource\x12:\n" +
	"\x04type\x18\x01 \x01(\x0e2&.api.items.story.types.v1.ResourceTypeR\x04type\x12!\n" +
	"\fresource_url\x18\x02 \x01(\tR\vresourceUrl\x12!\n" +
	"\fresource_key\x18\x03 \x01(\tR\vresourceKey\x126\n" +
	"\x15video_cover_image_url\x18\x04 \x01(\tH\x00R\x12videoCoverImageUrl\x88\x01\x01\x12C\n" +
	"\x1cvideo_cover_image_object_key\x18\x05 \x01(\tH\x01R\x18videoCoverImageObjectKey\x88\x01\x01\x12\x18\n" +
	"\acaption\x18\x06 \x01(\tR\acaption\x12N\n" +
	"\x0eshooting_model\x18\a \x01(\x0e2'.api.items.story.types.v1.ShootingModelR\rshootingModelB\x18\n" +
	"\x16_video_cover_image_urlB\x1f\n" +
	"\x1d_video_cover_image_object_key\"a\n" +
	"\tCondition\x12<\n" +
	"\x04hint\x18\x01 \x01(\v2(.api.items.story.types.v1.AttachmentTextR\x04hint\x12\x16\n" +
	"\x06prompt\x18\x02 \x01(\tR\x06prompt\"\x9d\x03\n" +
	"\x10CommonPlayConfig\x125\n" +
	"\x05cover\x18\x01 \x01(\v2\x1f.api.items.story.types.v1.CoverR\x05cover\x12>\n" +
	"\bresource\x18\x02 \x01(\v2\".api.items.story.types.v1.ResourceR\bresource\x12O\n" +
	"\x0ecover_captions\x18\x03 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\rcoverCaptions\x12F\n" +
	"\fcondition_v2\x18\x04 \x01(\v2#.api.items.story.types.v1.ConditionR\vconditionV2\x12\"\n" +
	"\rmax_try_count\x18\x05 \x01(\rR\vmaxTryCount\x12U\n" +
	"\x11resource_captions\x18\x06 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x10resourceCaptions\"T\n" +
	"\x04Word\x12\x1d\n" +
	"\n" +
	"start_time\x18\x01 \x01(\rR\tstartTime\x12\x19\n" +
	"\bend_time\x18\x02 \x01(\rR\aendTime\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\"\xd5\x01\n" +
	"\x10MomentCreateAttr\x12@\n" +
	"\bresource\x18\x01 \x01(\v2\x1f.api.resource.types.v1.ResourceH\x00R\bresource\x88\x01\x01\x12S\n" +
	"\x10attachment_texts\x18\x02 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x0fattachmentTexts\x12\x1d\n" +
	"\n" +
	"avatar_ids\x18\x03 \x03(\x03R\tavatarIdsB\v\n" +
	"\t_resource*\x93\x01\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02\x12\x16\n" +
	"\x12RESOURCE_TYPE_NONE\x10\x03\x12\x1a\n" +
	"\x16RESOURCE_TYPE_GRADIENT\x10\x04*\xe7\x02\n" +
	"\rShootingModel\x12\x1e\n" +
	"\x1aSHOOTING_MODEL_UNSPECIFIED\x10\x00\x12\x1d\n" +
	"\x19SHOOTING_MODEL_DUO_CAMERA\x10\x01\x12\x1f\n" +
	"\x1bSHOOTING_MODEL_FRONT_CAMERA\x10\x02\x12\x1e\n" +
	"\x1aSHOOTING_MODEL_BACK_CAMERA\x10\x03\x12\x17\n" +
	"\x13SHOOTING_MODEL_PANO\x10\x04\x12\x1d\n" +
	"\x19SHOOTING_MODEL_TIME_LAPSE\x10\x05\x12\x1e\n" +
	"\x1aSHOOTING_MODEL_SLOW_MOTION\x10\x06\x12\x1c\n" +
	"\x18SHOOTING_MODEL_CINEMATIC\x10\a\x12!\n" +
	"\x1dSHOOTING_MODEL_WALK_AND_SHOOT\x10\b\x12!\n" +
	"\x1dSHOOTING_MODEL_TALK_TO_CAMERA\x10\t\x12\x1a\n" +
	"\x16SHOOTING_MODEL_CAPTION\x10\n" +
	"*\x86\x01\n" +
	"\rShootingStyle\x12\x0f\n" +
	"\vUnspecified\x10\x00\x12\v\n" +
	"\aDefault\x10\x01\x12\r\n" +
	"\tExtrovert\x10\x02\x12\r\n" +
	"\tIntrovert\x10\x03\x12\f\n" +
	"\bHumorist\x10\x04\x12\x0e\n" +
	"\n" +
	"Aesthetics\x10\x05\x12\x0e\n" +
	"\n" +
	"Socializer\x10\x06\x12\v\n" +
	"\aPlayful\x10\aB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_base_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_base_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_base_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_base_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_base_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_base_types_proto_rawDesc), len(file_api_items_story_types_v1_base_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_base_types_proto_rawDescData
}

var file_api_items_story_types_v1_base_types_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_items_story_types_v1_base_types_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_items_story_types_v1_base_types_proto_goTypes = []any{
	(ResourceType)(0),             // 0: api.items.story.types.v1.ResourceType
	(ShootingModel)(0),            // 1: api.items.story.types.v1.ShootingModel
	(ShootingStyle)(0),            // 2: api.items.story.types.v1.ShootingStyle
	(*AttachmentText)(nil),        // 3: api.items.story.types.v1.AttachmentText
	(*ExampleCommonInfo)(nil),     // 4: api.items.story.types.v1.ExampleCommonInfo
	(*Resource)(nil),              // 5: api.items.story.types.v1.Resource
	(*Cover)(nil),                 // 6: api.items.story.types.v1.Cover
	(*Shooting)(nil),              // 7: api.items.story.types.v1.Shooting
	(*ShootingResource)(nil),      // 8: api.items.story.types.v1.ShootingResource
	(*Condition)(nil),             // 9: api.items.story.types.v1.Condition
	(*CommonPlayConfig)(nil),      // 10: api.items.story.types.v1.CommonPlayConfig
	(*Word)(nil),                  // 11: api.items.story.types.v1.Word
	(*MomentCreateAttr)(nil),      // 12: api.items.story.types.v1.MomentCreateAttr
	(*AttachmentText_AtUser)(nil), // 13: api.items.story.types.v1.AttachmentText.AtUser
	(*v1.Resource)(nil),           // 14: api.resource.types.v1.Resource
}
var file_api_items_story_types_v1_base_types_proto_depIdxs = []int32{
	13, // 0: api.items.story.types.v1.AttachmentText.at_users:type_name -> api.items.story.types.v1.AttachmentText.AtUser
	0,  // 1: api.items.story.types.v1.Resource.resource_type:type_name -> api.items.story.types.v1.ResourceType
	0,  // 2: api.items.story.types.v1.Cover.CoverType:type_name -> api.items.story.types.v1.ResourceType
	1,  // 3: api.items.story.types.v1.Shooting.shooting_model:type_name -> api.items.story.types.v1.ShootingModel
	0,  // 4: api.items.story.types.v1.ShootingResource.type:type_name -> api.items.story.types.v1.ResourceType
	1,  // 5: api.items.story.types.v1.ShootingResource.shooting_model:type_name -> api.items.story.types.v1.ShootingModel
	3,  // 6: api.items.story.types.v1.Condition.hint:type_name -> api.items.story.types.v1.AttachmentText
	6,  // 7: api.items.story.types.v1.CommonPlayConfig.cover:type_name -> api.items.story.types.v1.Cover
	5,  // 8: api.items.story.types.v1.CommonPlayConfig.resource:type_name -> api.items.story.types.v1.Resource
	3,  // 9: api.items.story.types.v1.CommonPlayConfig.cover_captions:type_name -> api.items.story.types.v1.AttachmentText
	9,  // 10: api.items.story.types.v1.CommonPlayConfig.condition_v2:type_name -> api.items.story.types.v1.Condition
	3,  // 11: api.items.story.types.v1.CommonPlayConfig.resource_captions:type_name -> api.items.story.types.v1.AttachmentText
	14, // 12: api.items.story.types.v1.MomentCreateAttr.resource:type_name -> api.resource.types.v1.Resource
	3,  // 13: api.items.story.types.v1.MomentCreateAttr.attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	14, // [14:14] is the sub-list for method output_type
	14, // [14:14] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_base_types_proto_init() }
func file_api_items_story_types_v1_base_types_proto_init() {
	if File_api_items_story_types_v1_base_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_msgTypes[5].OneofWrappers = []any{}
	file_api_items_story_types_v1_base_types_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_base_types_proto_rawDesc), len(file_api_items_story_types_v1_base_types_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_base_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_base_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_base_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_base_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_base_types_proto = out.File
	file_api_items_story_types_v1_base_types_proto_goTypes = nil
	file_api_items_story_types_v1_base_types_proto_depIdxs = nil
}
