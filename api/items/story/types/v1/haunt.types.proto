syntax = "proto3";

package api.items.story.types.v1;

import "api/users/info/types/v1/types.proto";
import "api/users/boo/types/v1/types.proto";
import "api/items/story/types/v1/base_types.proto";

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";

message HauntQuestion {
	string title = 1;
	// 填充默认值
	string description = 2;
	bool is_required = 3;
}
message HauntQuestionWithAnswer {
	HauntQuestion question = 1;
	string answer = 2;
}

message HauntBoo {
	string id = 1;
	api.users.boo.types.v1.Avatar avatar = 2;
	api.users.info.types.v1.UserInfoSummary creator = 3;
	string generated_hint = 4;
	string video_url = 5;
	bool creator_boo = 6;	
	repeated HauntQuestionWithAnswer questions_with_answers = 7;
}

message HauntPlayConfig {
	// 第一个永远是作者自己的 Bo<PERSON>
	repeated HauntBoo boos_with_questions_and_answers = 1;
	repeated api.items.story.types.v1.AttachmentText captions = 4;
}
message HauntPlayContext {
	uint32 try_count = 1;
	bool unlocked = 2;
}

enum HauntBooShowInfoSence {
	SHOW_INFO_SENCE_UNSPECIFIED = 0;
	// feed tab1 污染
	SHOW_INFO_SENCE_FEED_POLLUTION = 1;
	// 内容污染
	SHOW_INFO_SENCE_CONTENT_POLLUTION = 2;
}

message HauntBooShowInfo {
	// 第一个永远是 creator boo
	repeated HauntBoo haunt_boos = 1;
	// 此次展现来源于哪个 Story
	string from_haunt_story_id = 2;
	// 展示场景
	HauntBooShowInfoSence show_info_sence = 3;
	// 最后一次展示时间，标准 unixstamp
	uint32 last_show_at_in_unixstamp = 4; 
	// 针对登录 session 的当日展现次数
	uint32 today_show_count = 5;
	// 针对登录 session 这个 story 已经尝试了几次了
	uint32 tried_count = 6;
}