syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/items/story/types/v1/base_types.proto";
import "api/resource/types/v1/types.proto";
import "validate/validate.proto";

message ChatProxyQuestion {
	string question = 1;
	string tts_audio_url = 2;
	repeated api.items.story.types.v1.Word words = 3;
}

// hide 玩法上下文
message StoryPlayChatProxyContext {
	// 是否解锁过此 story
  bool is_unlocked = 1;
}

message StoryPlayChatProxyTopic {
	string id = 1;
	string content = 2;
}

message StoryPlayChatProxyCaption {
	string content = 1;
	string descrption = 2;
	// 客户端创建时不传，服务端自动根据 content 进行生成
	string tts_audio_url = 3;
	// 客户端创建时不传，服务端自动根据 content 进行生成
	repeated api.items.story.types.v1.Word words = 4;
}
message StoryPlayChatProxyGreeting {
	string content = 1;
	// 客户端创建时不传，服务端自动根据 content 进行生成
	string tts_audio_url = 3;
	// 客户端创建时不传，服务端自动根据 content 进行生成
	repeated api.items.story.types.v1.Word words = 4;
}

// chatproxy 玩法配置
message StoryPlayChatProxyConfig {
	StoryPlayChatProxyCaption caption = 1[(validate.rules).message = {required: true}];
	// greeting 客户端不传，服务端自动生成
	StoryPlayChatProxyGreeting greeting = 2;
	repeated StoryPlayChatProxyTopic topics = 3;

	api.resource.types.v1.Resource cover = 7;
	repeated api.items.story.types.v1.AttachmentText cover_attachment_texts = 9;
	api.resource.types.v1.Resource unlock_resource = 8;
	repeated api.items.story.types.v1.AttachmentText unlock_resource_attachment_texts = 10;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 11;
}
  
