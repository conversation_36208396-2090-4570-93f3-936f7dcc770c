// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/base_play_story_types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on StoryPlayBasePlayConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayBasePlayConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayBasePlayConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayBasePlayConfigMultiError, or nil if none found.
func (m *StoryPlayBasePlayConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayBasePlayConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNodes() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayBasePlayConfigValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayBasePlayConfigValidationError{
						field:  fmt.Sprintf("Nodes[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayBasePlayConfigValidationError{
					field:  fmt.Sprintf("Nodes[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayBasePlayConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayBasePlayConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayBasePlayConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayBasePlayConfigMultiError(errors)
	}

	return nil
}

// StoryPlayBasePlayConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayBasePlayConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayBasePlayConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayBasePlayConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayBasePlayConfigMultiError) AllErrors() []error { return m }

// StoryPlayBasePlayConfigValidationError is the validation error returned by
// StoryPlayBasePlayConfig.Validate if the designated constraints aren't met.
type StoryPlayBasePlayConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayBasePlayConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayBasePlayConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayBasePlayConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayBasePlayConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayBasePlayConfigValidationError) ErrorName() string {
	return "StoryPlayBasePlayConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayBasePlayConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayBasePlayConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayBasePlayConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayBasePlayConfigValidationError{}

// Validate checks the field values on StoryPlayBasePlayContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayBasePlayContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayBasePlayContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayBasePlayContextMultiError, or nil if none found.
func (m *StoryPlayBasePlayContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayBasePlayContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentNodeId

	// no validation rules for CurrentNodeIndex

	// no validation rules for IsUnlocked

	if len(errors) > 0 {
		return StoryPlayBasePlayContextMultiError(errors)
	}

	return nil
}

// StoryPlayBasePlayContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayBasePlayContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayBasePlayContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayBasePlayContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayBasePlayContextMultiError) AllErrors() []error { return m }

// StoryPlayBasePlayContextValidationError is the validation error returned by
// StoryPlayBasePlayContext.Validate if the designated constraints aren't met.
type StoryPlayBasePlayContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayBasePlayContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayBasePlayContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayBasePlayContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayBasePlayContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayBasePlayContextValidationError) ErrorName() string {
	return "StoryPlayBasePlayContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayBasePlayContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayBasePlayContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayBasePlayContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayBasePlayContextValidationError{}

// Validate checks the field values on StoryPlayBasePlayExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayBasePlayExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayBasePlayExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayBasePlayExampleMultiError, or nil if none found.
func (m *StoryPlayBasePlayExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayBasePlayExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CoverImageUrl

	if len(errors) > 0 {
		return StoryPlayBasePlayExampleMultiError(errors)
	}

	return nil
}

// StoryPlayBasePlayExampleMultiError is an error wrapping multiple validation
// errors returned by StoryPlayBasePlayExample.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayBasePlayExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayBasePlayExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayBasePlayExampleMultiError) AllErrors() []error { return m }

// StoryPlayBasePlayExampleValidationError is the validation error returned by
// StoryPlayBasePlayExample.Validate if the designated constraints aren't met.
type StoryPlayBasePlayExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayBasePlayExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayBasePlayExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayBasePlayExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayBasePlayExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayBasePlayExampleValidationError) ErrorName() string {
	return "StoryPlayBasePlayExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayBasePlayExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayBasePlayExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayBasePlayExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayBasePlayExampleValidationError{}

// Validate checks the field values on StoryPlayBasePlayConfig_Node with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayBasePlayConfig_Node) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayBasePlayConfig_Node with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayBasePlayConfig_NodeMultiError, or nil if none found.
func (m *StoryPlayBasePlayConfig_Node) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayBasePlayConfig_Node) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayBasePlayConfig_NodeValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayBasePlayConfig_NodeValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayBasePlayConfig_NodeValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayBasePlayConfig_NodeValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayBasePlayConfig_NodeValidationError{
						field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayBasePlayConfig_NodeValidationError{
					field:  fmt.Sprintf("AttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayBasePlayConfig_NodeMultiError(errors)
	}

	return nil
}

// StoryPlayBasePlayConfig_NodeMultiError is an error wrapping multiple
// validation errors returned by StoryPlayBasePlayConfig_Node.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayBasePlayConfig_NodeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayBasePlayConfig_NodeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayBasePlayConfig_NodeMultiError) AllErrors() []error { return m }

// StoryPlayBasePlayConfig_NodeValidationError is the validation error returned
// by StoryPlayBasePlayConfig_Node.Validate if the designated constraints
// aren't met.
type StoryPlayBasePlayConfig_NodeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayBasePlayConfig_NodeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayBasePlayConfig_NodeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayBasePlayConfig_NodeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayBasePlayConfig_NodeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayBasePlayConfig_NodeValidationError) ErrorName() string {
	return "StoryPlayBasePlayConfig_NodeValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayBasePlayConfig_NodeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayBasePlayConfig_Node.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayBasePlayConfig_NodeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayBasePlayConfig_NodeValidationError{}
