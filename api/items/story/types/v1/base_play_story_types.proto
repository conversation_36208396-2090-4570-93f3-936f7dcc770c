syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";

import "api/items/story/types/v1/base_types.proto";

message StoryPlayBasePlayConfig {
	message Node {
		// 节点 id，客户端自定义，服务端不使用
		string id = 1;
		api.items.story.types.v1.Resource resource = 2;
		// TODO，需要在视频或者图片上增加的文字，位置，大小等信息
		// 具体结构和 @fanqi 确认
		repeated AttachmentText attachment_texts = 3;
	}
	repeated Node nodes = 1;
	repeated MomentCreateAttr moment_create_attrs = 2;
}

message StoryPlayBasePlayContext {
	// 当前节点 id
	string current_node_id = 1;
	// 当前节点 index
	uint32 current_node_index = 2;
	// 是否解锁
	bool is_unlocked = 3;
}

message StoryPlayBasePlayExample {
	string cover_image_url = 1;
}