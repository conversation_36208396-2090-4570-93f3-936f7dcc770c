syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/items/story/types/v1/base_types.proto";
import "api/resource/types/v1/types.proto";

message StoryPlayWassupConfig {
	api.resource.types.v1.Resource unlock_resource = 1;
	repeated api.items.story.types.v1.AttachmentText cover_image_texts = 2;
	api.resource.types.v1.Resource cover_image_resource = 3;
	bool is_mass_cover = 4;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 5;
}

message StoryPlayWassupContext {
	bool is_unlocked = 1;
}

message StoryPlayWassupBotMessage {
	string content = 1;
	// 客户端创建时不传，服务端自动根据 content 进行生成
	string tts_audio_url = 3;
	// 客户端创建时不传，服务端自动根据 content 进行生成
	repeated api.items.story.types.v1.Word words = 4;
}

