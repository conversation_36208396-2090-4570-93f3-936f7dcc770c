syntax = "proto3";

package api.items.story.types.v1;

import "api/resource/types/v1/types.proto";
import "validate/validate.proto";
import "api/users/info/types/v1/types.proto";
import "api/items/story/types/v1/base_types.proto";

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";

message WhoStoryPlayOption {
	bool correct = 1;
	api.users.info.types.v1.UserInfoSummary option = 2;
}
// who 玩法配置
message WhoStoryPlayConfig {
	repeated api.items.story.types.v1.AttachmentText unlock_resource_texts = 1;
	api.resource.types.v1.Resource unlock_resource = 2[(validate.rules).message = {
		required: true		
	}];
	repeated api.items.story.types.v1.AttachmentText cover_resource_texts = 3;
	api.resource.types.v1.Resource cover_resource = 4[(validate.rules).message = {
		required: true		
	}];
	// 可选型，如果作者自己选择了备选，那么创作的时候，传入两个值即可；
	// 如果创作者不选，那么传空
	// 服务端会始终把创作者设为选项之一，客户端可以不传
	repeated string option_user_ids = 5;
	// options 是服务端根据 option_user_ids 进行的渲染结构
	// 创作时，客户端不用传递此参数
	repeated WhoStoryPlayOption options = 6;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 7;
}

message WhoStoryTryPoint {
	string x = 1;
	string y = 2;
}

message WhoStoryPlayContext {
	// 用户尝试点击过的位置
	repeated WhoStoryTryPoint tried_points = 1;
	// 目前可用的选项
	repeated api.users.info.types.v1.UserInfoSummary enabled_options = 2;
	// 是否已经成功了
	bool is_unlocked = 3;
	// 是否尝试过
	bool is_consumed = 4;
	// 尝试次数
	uint32 tried_count = 5;
	// 最大尝试次数
	uint32 max_try_count = 6;
}