syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/items/story/types/v1/base_types.proto";

// Capsule 玩法上下文
message StoryPlayCapsuleContext {
  // 是否消费过此 story
  bool is_consumed = 1;
}

message CapsuleShootingResource {
  Shooting shooting = 1;
  ShootingResource shooting_resource = 2;
}

message CapsuleAIScript {
  // 第几秒
  uint32 seconds = 1;
  // AI 文本
  CapsuleQuestion question = 2;
}

message CapsulePhotoInfo {
  // 几天内的图片
  uint32 in_days = 1;
  // 几个瞬间
  uint32 moments = 2;
}

// Capsule 玩法配置
message StoryPlayCapsuleConfig {
  // 用户上传的起始图片
  api.items.story.types.v1.Resource cover_image = 1;
  // 用户拍摄的视频
  api.items.story.types.v1.Resource video = 2;
  // AI 脚本
  repeated CapsuleAIScript ai_scripts = 3;
  // 客户端提供的数据
  CapsulePhotoInfo photo_info = 4;
  repeated MomentCreateAttr moment_create_attrs = 5;
}

// Capsule 玩法示例
message StoryPlayCapsuleExample {
  ExampleCommonInfo common_info = 1;
  CapsulePhotoInfo photo_info = 2;
}

// ai 生成的问题和 tts
message CapsuleQuestion {
  string question = 1;
  // 创建的时候不需要传此值
  string tts_audio_url = 2;
  // 创建的时候必传
  string tts_audio_key = 3;
  // 此字段暂时对端上无作用，消费时带回来即可
  string thinking = 4;
  // 词粒度信息
  repeated Word words = 5;
}

message CapsuleQuestionWithUserAnswer {
  CapsuleQuestion question = 1;
  // 用户回答的音频资源
  string user_voice_key = 2;
}

message ImageDetail {
  // 照片地址
  string object_key = 1;
  // 照片拍摄纬度
  string latitude = 2;
  // 照片拍摄经度
  string longitude = 3;
  // 照片拍摄时间
  uint32 shooting_timestamp = 4;
}