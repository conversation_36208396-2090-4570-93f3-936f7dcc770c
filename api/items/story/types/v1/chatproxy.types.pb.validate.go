// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/chatproxy.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ChatProxyQuestion with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChatProxyQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatProxyQuestion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatProxyQuestionMultiError, or nil if none found.
func (m *ChatProxyQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatProxyQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	// no validation rules for TtsAudioUrl

	for idx, item := range m.GetWords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatProxyQuestionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatProxyQuestionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatProxyQuestionValidationError{
					field:  fmt.Sprintf("Words[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ChatProxyQuestionMultiError(errors)
	}

	return nil
}

// ChatProxyQuestionMultiError is an error wrapping multiple validation errors
// returned by ChatProxyQuestion.ValidateAll() if the designated constraints
// aren't met.
type ChatProxyQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatProxyQuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatProxyQuestionMultiError) AllErrors() []error { return m }

// ChatProxyQuestionValidationError is the validation error returned by
// ChatProxyQuestion.Validate if the designated constraints aren't met.
type ChatProxyQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatProxyQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatProxyQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatProxyQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatProxyQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatProxyQuestionValidationError) ErrorName() string {
	return "ChatProxyQuestionValidationError"
}

// Error satisfies the builtin error interface
func (e ChatProxyQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatProxyQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatProxyQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatProxyQuestionValidationError{}

// Validate checks the field values on StoryPlayChatProxyContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayChatProxyContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayChatProxyContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayChatProxyContextMultiError, or nil if none found.
func (m *StoryPlayChatProxyContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayChatProxyContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsUnlocked

	if len(errors) > 0 {
		return StoryPlayChatProxyContextMultiError(errors)
	}

	return nil
}

// StoryPlayChatProxyContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayChatProxyContext.ValidateAll() if the
// designated constraints aren't met.
type StoryPlayChatProxyContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayChatProxyContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayChatProxyContextMultiError) AllErrors() []error { return m }

// StoryPlayChatProxyContextValidationError is the validation error returned by
// StoryPlayChatProxyContext.Validate if the designated constraints aren't met.
type StoryPlayChatProxyContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayChatProxyContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayChatProxyContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayChatProxyContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayChatProxyContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayChatProxyContextValidationError) ErrorName() string {
	return "StoryPlayChatProxyContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayChatProxyContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayChatProxyContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayChatProxyContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayChatProxyContextValidationError{}

// Validate checks the field values on StoryPlayChatProxyTopic with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayChatProxyTopic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayChatProxyTopic with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayChatProxyTopicMultiError, or nil if none found.
func (m *StoryPlayChatProxyTopic) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayChatProxyTopic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Content

	if len(errors) > 0 {
		return StoryPlayChatProxyTopicMultiError(errors)
	}

	return nil
}

// StoryPlayChatProxyTopicMultiError is an error wrapping multiple validation
// errors returned by StoryPlayChatProxyTopic.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayChatProxyTopicMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayChatProxyTopicMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayChatProxyTopicMultiError) AllErrors() []error { return m }

// StoryPlayChatProxyTopicValidationError is the validation error returned by
// StoryPlayChatProxyTopic.Validate if the designated constraints aren't met.
type StoryPlayChatProxyTopicValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayChatProxyTopicValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayChatProxyTopicValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayChatProxyTopicValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayChatProxyTopicValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayChatProxyTopicValidationError) ErrorName() string {
	return "StoryPlayChatProxyTopicValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayChatProxyTopicValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayChatProxyTopic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayChatProxyTopicValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayChatProxyTopicValidationError{}

// Validate checks the field values on StoryPlayChatProxyCaption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayChatProxyCaption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayChatProxyCaption with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayChatProxyCaptionMultiError, or nil if none found.
func (m *StoryPlayChatProxyCaption) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayChatProxyCaption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for Descrption

	// no validation rules for TtsAudioUrl

	for idx, item := range m.GetWords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayChatProxyCaptionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayChatProxyCaptionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayChatProxyCaptionValidationError{
					field:  fmt.Sprintf("Words[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayChatProxyCaptionMultiError(errors)
	}

	return nil
}

// StoryPlayChatProxyCaptionMultiError is an error wrapping multiple validation
// errors returned by StoryPlayChatProxyCaption.ValidateAll() if the
// designated constraints aren't met.
type StoryPlayChatProxyCaptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayChatProxyCaptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayChatProxyCaptionMultiError) AllErrors() []error { return m }

// StoryPlayChatProxyCaptionValidationError is the validation error returned by
// StoryPlayChatProxyCaption.Validate if the designated constraints aren't met.
type StoryPlayChatProxyCaptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayChatProxyCaptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayChatProxyCaptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayChatProxyCaptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayChatProxyCaptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayChatProxyCaptionValidationError) ErrorName() string {
	return "StoryPlayChatProxyCaptionValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayChatProxyCaptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayChatProxyCaption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayChatProxyCaptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayChatProxyCaptionValidationError{}

// Validate checks the field values on StoryPlayChatProxyGreeting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayChatProxyGreeting) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayChatProxyGreeting with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayChatProxyGreetingMultiError, or nil if none found.
func (m *StoryPlayChatProxyGreeting) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayChatProxyGreeting) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for TtsAudioUrl

	for idx, item := range m.GetWords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayChatProxyGreetingValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayChatProxyGreetingValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayChatProxyGreetingValidationError{
					field:  fmt.Sprintf("Words[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayChatProxyGreetingMultiError(errors)
	}

	return nil
}

// StoryPlayChatProxyGreetingMultiError is an error wrapping multiple
// validation errors returned by StoryPlayChatProxyGreeting.ValidateAll() if
// the designated constraints aren't met.
type StoryPlayChatProxyGreetingMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayChatProxyGreetingMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayChatProxyGreetingMultiError) AllErrors() []error { return m }

// StoryPlayChatProxyGreetingValidationError is the validation error returned
// by StoryPlayChatProxyGreeting.Validate if the designated constraints aren't met.
type StoryPlayChatProxyGreetingValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayChatProxyGreetingValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayChatProxyGreetingValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayChatProxyGreetingValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayChatProxyGreetingValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayChatProxyGreetingValidationError) ErrorName() string {
	return "StoryPlayChatProxyGreetingValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayChatProxyGreetingValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayChatProxyGreeting.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayChatProxyGreetingValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayChatProxyGreetingValidationError{}

// Validate checks the field values on StoryPlayChatProxyConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayChatProxyConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayChatProxyConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayChatProxyConfigMultiError, or nil if none found.
func (m *StoryPlayChatProxyConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayChatProxyConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCaption() == nil {
		err := StoryPlayChatProxyConfigValidationError{
			field:  "Caption",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCaption()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "Caption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "Caption",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaption()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayChatProxyConfigValidationError{
				field:  "Caption",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGreeting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "Greeting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "Greeting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGreeting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayChatProxyConfigValidationError{
				field:  "Greeting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTopics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("Topics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayChatProxyConfigValidationError{
					field:  fmt.Sprintf("Topics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCover()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCover()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayChatProxyConfigValidationError{
				field:  "Cover",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCoverAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("CoverAttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("CoverAttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayChatProxyConfigValidationError{
					field:  fmt.Sprintf("CoverAttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUnlockResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "UnlockResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayChatProxyConfigValidationError{
					field:  "UnlockResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnlockResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayChatProxyConfigValidationError{
				field:  "UnlockResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetUnlockResourceAttachmentTexts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("UnlockResourceAttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("UnlockResourceAttachmentTexts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayChatProxyConfigValidationError{
					field:  fmt.Sprintf("UnlockResourceAttachmentTexts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayChatProxyConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayChatProxyConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayChatProxyConfigMultiError(errors)
	}

	return nil
}

// StoryPlayChatProxyConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayChatProxyConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayChatProxyConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayChatProxyConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayChatProxyConfigMultiError) AllErrors() []error { return m }

// StoryPlayChatProxyConfigValidationError is the validation error returned by
// StoryPlayChatProxyConfig.Validate if the designated constraints aren't met.
type StoryPlayChatProxyConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayChatProxyConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayChatProxyConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayChatProxyConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayChatProxyConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayChatProxyConfigValidationError) ErrorName() string {
	return "StoryPlayChatProxyConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayChatProxyConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayChatProxyConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayChatProxyConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayChatProxyConfigValidationError{}
