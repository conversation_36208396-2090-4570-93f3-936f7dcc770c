// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/roasted.types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/resource/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RoastedQuestion struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Question string                 `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	// 创建的时候不需要传此值
	TtsAudioUrl string `protobuf:"bytes,2,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	// 创建的时候必传
	TtsAudioKey string `protobuf:"bytes,3,opt,name=tts_audio_key,json=ttsAudioKey,proto3" json:"tts_audio_key,omitempty"`
	// 此字段暂时对端上无作用，消费时带回来即可
	Thinking string `protobuf:"bytes,4,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// 词粒度信息
	Words         []*Word `protobuf:"bytes,6,rep,name=words,proto3" json:"words,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoastedQuestion) Reset() {
	*x = RoastedQuestion{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoastedQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoastedQuestion) ProtoMessage() {}

func (x *RoastedQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoastedQuestion.ProtoReflect.Descriptor instead.
func (*RoastedQuestion) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{0}
}

func (x *RoastedQuestion) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *RoastedQuestion) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *RoastedQuestion) GetTtsAudioKey() string {
	if x != nil {
		return x.TtsAudioKey
	}
	return ""
}

func (x *RoastedQuestion) GetThinking() string {
	if x != nil {
		return x.Thinking
	}
	return ""
}

func (x *RoastedQuestion) GetWords() []*Word {
	if x != nil {
		return x.Words
	}
	return nil
}

type RoastedQuestionWithUserAnswer struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Question *RoastedQuestion       `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	// 用户回答的音频资源
	UserVoiceKey  string `protobuf:"bytes,2,opt,name=user_voice_key,json=userVoiceKey,proto3" json:"user_voice_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoastedQuestionWithUserAnswer) Reset() {
	*x = RoastedQuestionWithUserAnswer{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoastedQuestionWithUserAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoastedQuestionWithUserAnswer) ProtoMessage() {}

func (x *RoastedQuestionWithUserAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoastedQuestionWithUserAnswer.ProtoReflect.Descriptor instead.
func (*RoastedQuestionWithUserAnswer) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{1}
}

func (x *RoastedQuestionWithUserAnswer) GetQuestion() *RoastedQuestion {
	if x != nil {
		return x.Question
	}
	return nil
}

func (x *RoastedQuestionWithUserAnswer) GetUserVoiceKey() string {
	if x != nil {
		return x.UserVoiceKey
	}
	return ""
}

type StoryPlayRoastedTopic struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 纯 bot 播报，用于开场白
	Greeting *StoryPlayRoastedTopic_BotAnnouncement `protobuf:"bytes,1,opt,name=greeting,proto3" json:"greeting,omitempty"`
	// 纯 bot 播报，用于结束语
	Ending *StoryPlayRoastedTopic_BotAnnouncement `protobuf:"bytes,2,opt,name=ending,proto3" json:"ending,omitempty"`
	// 问题列表，至少有一个问题
	Questions []*RoastedQuestion `protobuf:"bytes,3,rep,name=questions,proto3" json:"questions,omitempty"`
	// 最大问题数, 按60%：20%：20%返回3:4:5
	MaxFollowup   uint32 `protobuf:"varint,4,opt,name=max_followup,json=maxFollowup,proto3" json:"max_followup,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayRoastedTopic) Reset() {
	*x = StoryPlayRoastedTopic{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayRoastedTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayRoastedTopic) ProtoMessage() {}

func (x *StoryPlayRoastedTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayRoastedTopic.ProtoReflect.Descriptor instead.
func (*StoryPlayRoastedTopic) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayRoastedTopic) GetGreeting() *StoryPlayRoastedTopic_BotAnnouncement {
	if x != nil {
		return x.Greeting
	}
	return nil
}

func (x *StoryPlayRoastedTopic) GetEnding() *StoryPlayRoastedTopic_BotAnnouncement {
	if x != nil {
		return x.Ending
	}
	return nil
}

func (x *StoryPlayRoastedTopic) GetQuestions() []*RoastedQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

func (x *StoryPlayRoastedTopic) GetMaxFollowup() uint32 {
	if x != nil {
		return x.MaxFollowup
	}
	return 0
}

type StoryPlayRoastedConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Topic *StoryPlayRoastedTopic `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	// 节点资源
	Resource *v1.Resource `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	// cover resource
	Cover *v1.Resource `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover,omitempty"`
	// moment create attrs
	MomentCreateAttrs []*MomentCreateAttr `protobuf:"bytes,4,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayRoastedConfig) Reset() {
	*x = StoryPlayRoastedConfig{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayRoastedConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayRoastedConfig) ProtoMessage() {}

func (x *StoryPlayRoastedConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayRoastedConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayRoastedConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{3}
}

func (x *StoryPlayRoastedConfig) GetTopic() *StoryPlayRoastedTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

func (x *StoryPlayRoastedConfig) GetResource() *v1.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *StoryPlayRoastedConfig) GetCover() *v1.Resource {
	if x != nil {
		return x.Cover
	}
	return nil
}

func (x *StoryPlayRoastedConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

type StoryPlayRoastedContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否消费过此 story
	IsConsumed    bool `protobuf:"varint,1,opt,name=is_consumed,json=isConsumed,proto3" json:"is_consumed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayRoastedContext) Reset() {
	*x = StoryPlayRoastedContext{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayRoastedContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayRoastedContext) ProtoMessage() {}

func (x *StoryPlayRoastedContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayRoastedContext.ProtoReflect.Descriptor instead.
func (*StoryPlayRoastedContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{4}
}

func (x *StoryPlayRoastedContext) GetIsConsumed() bool {
	if x != nil {
		return x.IsConsumed
	}
	return false
}

type StoryPlayRoastedExample struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         *StoryPlayRoastedTopic `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayRoastedExample) Reset() {
	*x = StoryPlayRoastedExample{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayRoastedExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayRoastedExample) ProtoMessage() {}

func (x *StoryPlayRoastedExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayRoastedExample.ProtoReflect.Descriptor instead.
func (*StoryPlayRoastedExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{5}
}

func (x *StoryPlayRoastedExample) GetTopic() *StoryPlayRoastedTopic {
	if x != nil {
		return x.Topic
	}
	return nil
}

// 纯 bot 播报，用于开场白和结束语
type StoryPlayRoastedTopic_BotAnnouncement struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 播报的内容
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 播报的音频资源, 创建的时候必须传此值
	TtsAudioKey string `protobuf:"bytes,2,opt,name=tts_audio_key,json=ttsAudioKey,proto3" json:"tts_audio_key,omitempty"`
	// 播报的音频资源 url，创建的时候不需要传此值
	TtsAudioUrl   string `protobuf:"bytes,3,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayRoastedTopic_BotAnnouncement) Reset() {
	*x = StoryPlayRoastedTopic_BotAnnouncement{}
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayRoastedTopic_BotAnnouncement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayRoastedTopic_BotAnnouncement) ProtoMessage() {}

func (x *StoryPlayRoastedTopic_BotAnnouncement) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_roasted_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayRoastedTopic_BotAnnouncement.ProtoReflect.Descriptor instead.
func (*StoryPlayRoastedTopic_BotAnnouncement) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP(), []int{2, 0}
}

func (x *StoryPlayRoastedTopic_BotAnnouncement) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *StoryPlayRoastedTopic_BotAnnouncement) GetTtsAudioKey() string {
	if x != nil {
		return x.TtsAudioKey
	}
	return ""
}

func (x *StoryPlayRoastedTopic_BotAnnouncement) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

var File_api_items_story_types_v1_roasted_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_roasted_types_proto_rawDesc = "" +
	"\n" +
	",api/items/story/types/v1/roasted.types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\x1a\x17validate/validate.proto\x1a!api/resource/types/v1/types.proto\"\xc7\x01\n" +
	"\x0fRoastedQuestion\x12\x1a\n" +
	"\bquestion\x18\x01 \x01(\tR\bquestion\x12\"\n" +
	"\rtts_audio_url\x18\x02 \x01(\tR\vttsAudioUrl\x12\"\n" +
	"\rtts_audio_key\x18\x03 \x01(\tR\vttsAudioKey\x12\x1a\n" +
	"\bthinking\x18\x04 \x01(\tR\bthinking\x124\n" +
	"\x05words\x18\x06 \x03(\v2\x1e.api.items.story.types.v1.WordR\x05words\"\x8c\x01\n" +
	"\x1dRoastedQuestionWithUserAnswer\x12E\n" +
	"\bquestion\x18\x01 \x01(\v2).api.items.story.types.v1.RoastedQuestionR\bquestion\x12$\n" +
	"\x0euser_voice_key\x18\x02 \x01(\tR\fuserVoiceKey\"\xae\x03\n" +
	"\x15StoryPlayRoastedTopic\x12[\n" +
	"\bgreeting\x18\x01 \x01(\v2?.api.items.story.types.v1.StoryPlayRoastedTopic.BotAnnouncementR\bgreeting\x12W\n" +
	"\x06ending\x18\x02 \x01(\v2?.api.items.story.types.v1.StoryPlayRoastedTopic.BotAnnouncementR\x06ending\x12G\n" +
	"\tquestions\x18\x03 \x03(\v2).api.items.story.types.v1.RoastedQuestionR\tquestions\x12!\n" +
	"\fmax_followup\x18\x04 \x01(\rR\vmaxFollowup\x1as\n" +
	"\x0fBotAnnouncement\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\"\n" +
	"\rtts_audio_key\x18\x02 \x01(\tR\vttsAudioKey\x12\"\n" +
	"\rtts_audio_url\x18\x03 \x01(\tR\vttsAudioUrl\"\xb9\x02\n" +
	"\x16StoryPlayRoastedConfig\x12O\n" +
	"\x05topic\x18\x01 \x01(\v2/.api.items.story.types.v1.StoryPlayRoastedTopicB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x05topic\x12;\n" +
	"\bresource\x18\x02 \x01(\v2\x1f.api.resource.types.v1.ResourceR\bresource\x125\n" +
	"\x05cover\x18\x03 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x05cover\x12Z\n" +
	"\x13moment_create_attrs\x18\x04 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\":\n" +
	"\x17StoryPlayRoastedContext\x12\x1f\n" +
	"\vis_consumed\x18\x01 \x01(\bR\n" +
	"isConsumed\"`\n" +
	"\x17StoryPlayRoastedExample\x12E\n" +
	"\x05topic\x18\x01 \x01(\v2/.api.items.story.types.v1.StoryPlayRoastedTopicR\x05topicB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_roasted_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_roasted_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_roasted_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_roasted_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_roasted_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_roasted_types_proto_rawDesc), len(file_api_items_story_types_v1_roasted_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_roasted_types_proto_rawDescData
}

var file_api_items_story_types_v1_roasted_types_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_items_story_types_v1_roasted_types_proto_goTypes = []any{
	(*RoastedQuestion)(nil),                       // 0: api.items.story.types.v1.RoastedQuestion
	(*RoastedQuestionWithUserAnswer)(nil),         // 1: api.items.story.types.v1.RoastedQuestionWithUserAnswer
	(*StoryPlayRoastedTopic)(nil),                 // 2: api.items.story.types.v1.StoryPlayRoastedTopic
	(*StoryPlayRoastedConfig)(nil),                // 3: api.items.story.types.v1.StoryPlayRoastedConfig
	(*StoryPlayRoastedContext)(nil),               // 4: api.items.story.types.v1.StoryPlayRoastedContext
	(*StoryPlayRoastedExample)(nil),               // 5: api.items.story.types.v1.StoryPlayRoastedExample
	(*StoryPlayRoastedTopic_BotAnnouncement)(nil), // 6: api.items.story.types.v1.StoryPlayRoastedTopic.BotAnnouncement
	(*Word)(nil),             // 7: api.items.story.types.v1.Word
	(*v1.Resource)(nil),      // 8: api.resource.types.v1.Resource
	(*MomentCreateAttr)(nil), // 9: api.items.story.types.v1.MomentCreateAttr
}
var file_api_items_story_types_v1_roasted_types_proto_depIdxs = []int32{
	7,  // 0: api.items.story.types.v1.RoastedQuestion.words:type_name -> api.items.story.types.v1.Word
	0,  // 1: api.items.story.types.v1.RoastedQuestionWithUserAnswer.question:type_name -> api.items.story.types.v1.RoastedQuestion
	6,  // 2: api.items.story.types.v1.StoryPlayRoastedTopic.greeting:type_name -> api.items.story.types.v1.StoryPlayRoastedTopic.BotAnnouncement
	6,  // 3: api.items.story.types.v1.StoryPlayRoastedTopic.ending:type_name -> api.items.story.types.v1.StoryPlayRoastedTopic.BotAnnouncement
	0,  // 4: api.items.story.types.v1.StoryPlayRoastedTopic.questions:type_name -> api.items.story.types.v1.RoastedQuestion
	2,  // 5: api.items.story.types.v1.StoryPlayRoastedConfig.topic:type_name -> api.items.story.types.v1.StoryPlayRoastedTopic
	8,  // 6: api.items.story.types.v1.StoryPlayRoastedConfig.resource:type_name -> api.resource.types.v1.Resource
	8,  // 7: api.items.story.types.v1.StoryPlayRoastedConfig.cover:type_name -> api.resource.types.v1.Resource
	9,  // 8: api.items.story.types.v1.StoryPlayRoastedConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	2,  // 9: api.items.story.types.v1.StoryPlayRoastedExample.topic:type_name -> api.items.story.types.v1.StoryPlayRoastedTopic
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_roasted_types_proto_init() }
func file_api_items_story_types_v1_roasted_types_proto_init() {
	if File_api_items_story_types_v1_roasted_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_roasted_types_proto_rawDesc), len(file_api_items_story_types_v1_roasted_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_roasted_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_roasted_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_roasted_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_roasted_types_proto = out.File
	file_api_items_story_types_v1_roasted_types_proto_goTypes = nil
	file_api_items_story_types_v1_roasted_types_proto_depIdxs = nil
}
