// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/management_types.proto

package api_items_story_types_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Feed 状态
type FeedStatus int32

const (
	FeedStatus_FEED_STATUS_UNSPECIFIED FeedStatus = 0
	// 正常推送
	FeedStatus_FEED_STATUS_NORMAL FeedStatus = 1
	// 禁止推送
	FeedStatus_FEED_STATUS_BANNED FeedStatus = 2
)

// Enum value maps for FeedStatus.
var (
	FeedStatus_name = map[int32]string{
		0: "FEED_STATUS_UNSPECIFIED",
		1: "FEED_STATUS_NORMAL",
		2: "FEED_STATUS_BANNED",
	}
	FeedStatus_value = map[string]int32{
		"FEED_STATUS_UNSPECIFIED": 0,
		"FEED_STATUS_NORMAL":      1,
		"FEED_STATUS_BANNED":      2,
	}
)

func (x FeedStatus) Enum() *FeedStatus {
	p := new(FeedStatus)
	*p = x
	return p
}

func (x FeedStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FeedStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_management_types_proto_enumTypes[0].Descriptor()
}

func (FeedStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_management_types_proto_enumTypes[0]
}

func (x FeedStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FeedStatus.Descriptor instead.
func (FeedStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_management_types_proto_rawDescGZIP(), []int{0}
}

// 故事质量等级
type StoryQuality int32

const (
	StoryQuality_STORY_QUALITY_UNSPECIFIED StoryQuality = 0
	// 普通质量
	StoryQuality_STORY_QUALITY_NORMAL StoryQuality = 1
	// 高质量
	StoryQuality_STORY_QUALITY_HIGH StoryQuality = 2
)

// Enum value maps for StoryQuality.
var (
	StoryQuality_name = map[int32]string{
		0: "STORY_QUALITY_UNSPECIFIED",
		1: "STORY_QUALITY_NORMAL",
		2: "STORY_QUALITY_HIGH",
	}
	StoryQuality_value = map[string]int32{
		"STORY_QUALITY_UNSPECIFIED": 0,
		"STORY_QUALITY_NORMAL":      1,
		"STORY_QUALITY_HIGH":        2,
	}
)

func (x StoryQuality) Enum() *StoryQuality {
	p := new(StoryQuality)
	*p = x
	return p
}

func (x StoryQuality) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryQuality) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_management_types_proto_enumTypes[1].Descriptor()
}

func (StoryQuality) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_management_types_proto_enumTypes[1]
}

func (x StoryQuality) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryQuality.Descriptor instead.
func (StoryQuality) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_management_types_proto_rawDescGZIP(), []int{1}
}

// 推送状态
type PushStatus int32

const (
	PushStatus_PUSH_STATUS_UNSPECIFIED PushStatus = 0
	// 正常推送
	PushStatus_PUSH_STATUS_NORMAL PushStatus = 1
	// 禁止推送
	PushStatus_PUSH_STATUS_BANNED PushStatus = 2
	// 仅关注者推送
	PushStatus_PUSH_STATUS_FOLLOWED PushStatus = 3
)

// Enum value maps for PushStatus.
var (
	PushStatus_name = map[int32]string{
		0: "PUSH_STATUS_UNSPECIFIED",
		1: "PUSH_STATUS_NORMAL",
		2: "PUSH_STATUS_BANNED",
		3: "PUSH_STATUS_FOLLOWED",
	}
	PushStatus_value = map[string]int32{
		"PUSH_STATUS_UNSPECIFIED": 0,
		"PUSH_STATUS_NORMAL":      1,
		"PUSH_STATUS_BANNED":      2,
		"PUSH_STATUS_FOLLOWED":    3,
	}
)

func (x PushStatus) Enum() *PushStatus {
	p := new(PushStatus)
	*p = x
	return p
}

func (x PushStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PushStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_management_types_proto_enumTypes[2].Descriptor()
}

func (PushStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_management_types_proto_enumTypes[2]
}

func (x PushStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PushStatus.Descriptor instead.
func (PushStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_management_types_proto_rawDescGZIP(), []int{2}
}

// 故事管理信息
type StoryManagement struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 故事ID
	StoryId string `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// Feed状态
	FeedStatus FeedStatus `protobuf:"varint,2,opt,name=feed_status,json=feedStatus,proto3,enum=api.items.story.types.v1.FeedStatus" json:"feed_status,omitempty"`
	// Feed权重
	FeedBoost float32 `protobuf:"fixed32,3,opt,name=feed_boost,json=feedBoost,proto3" json:"feed_boost,omitempty"`
	// 故事质量
	StoryQuality StoryQuality `protobuf:"varint,4,opt,name=story_quality,json=storyQuality,proto3,enum=api.items.story.types.v1.StoryQuality" json:"story_quality,omitempty"`
	// 推送状态
	PushStatus    PushStatus `protobuf:"varint,5,opt,name=push_status,json=pushStatus,proto3,enum=api.items.story.types.v1.PushStatus" json:"push_status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryManagement) Reset() {
	*x = StoryManagement{}
	mi := &file_api_items_story_types_v1_management_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryManagement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryManagement) ProtoMessage() {}

func (x *StoryManagement) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_management_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryManagement.ProtoReflect.Descriptor instead.
func (*StoryManagement) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_management_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryManagement) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *StoryManagement) GetFeedStatus() FeedStatus {
	if x != nil {
		return x.FeedStatus
	}
	return FeedStatus_FEED_STATUS_UNSPECIFIED
}

func (x *StoryManagement) GetFeedBoost() float32 {
	if x != nil {
		return x.FeedBoost
	}
	return 0
}

func (x *StoryManagement) GetStoryQuality() StoryQuality {
	if x != nil {
		return x.StoryQuality
	}
	return StoryQuality_STORY_QUALITY_UNSPECIFIED
}

func (x *StoryManagement) GetPushStatus() PushStatus {
	if x != nil {
		return x.PushStatus
	}
	return PushStatus_PUSH_STATUS_UNSPECIFIED
}

var File_api_items_story_types_v1_management_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_management_types_proto_rawDesc = "" +
	"\n" +
	"/api/items/story/types/v1/management_types.proto\x12\x18api.items.story.types.v1\"\xa6\x02\n" +
	"\x0fStoryManagement\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12E\n" +
	"\vfeed_status\x18\x02 \x01(\x0e2$.api.items.story.types.v1.FeedStatusR\n" +
	"feedStatus\x12\x1d\n" +
	"\n" +
	"feed_boost\x18\x03 \x01(\x02R\tfeedBoost\x12K\n" +
	"\rstory_quality\x18\x04 \x01(\x0e2&.api.items.story.types.v1.StoryQualityR\fstoryQuality\x12E\n" +
	"\vpush_status\x18\x05 \x01(\x0e2$.api.items.story.types.v1.PushStatusR\n" +
	"pushStatus*Y\n" +
	"\n" +
	"FeedStatus\x12\x1b\n" +
	"\x17FEED_STATUS_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12FEED_STATUS_NORMAL\x10\x01\x12\x16\n" +
	"\x12FEED_STATUS_BANNED\x10\x02*_\n" +
	"\fStoryQuality\x12\x1d\n" +
	"\x19STORY_QUALITY_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14STORY_QUALITY_NORMAL\x10\x01\x12\x16\n" +
	"\x12STORY_QUALITY_HIGH\x10\x02*s\n" +
	"\n" +
	"PushStatus\x12\x1b\n" +
	"\x17PUSH_STATUS_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12PUSH_STATUS_NORMAL\x10\x01\x12\x16\n" +
	"\x12PUSH_STATUS_BANNED\x10\x02\x12\x18\n" +
	"\x14PUSH_STATUS_FOLLOWED\x10\x03B9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_management_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_management_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_management_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_management_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_management_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_management_types_proto_rawDesc), len(file_api_items_story_types_v1_management_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_management_types_proto_rawDescData
}

var file_api_items_story_types_v1_management_types_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_items_story_types_v1_management_types_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_items_story_types_v1_management_types_proto_goTypes = []any{
	(FeedStatus)(0),         // 0: api.items.story.types.v1.FeedStatus
	(StoryQuality)(0),       // 1: api.items.story.types.v1.StoryQuality
	(PushStatus)(0),         // 2: api.items.story.types.v1.PushStatus
	(*StoryManagement)(nil), // 3: api.items.story.types.v1.StoryManagement
}
var file_api_items_story_types_v1_management_types_proto_depIdxs = []int32{
	0, // 0: api.items.story.types.v1.StoryManagement.feed_status:type_name -> api.items.story.types.v1.FeedStatus
	1, // 1: api.items.story.types.v1.StoryManagement.story_quality:type_name -> api.items.story.types.v1.StoryQuality
	2, // 2: api.items.story.types.v1.StoryManagement.push_status:type_name -> api.items.story.types.v1.PushStatus
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_management_types_proto_init() }
func file_api_items_story_types_v1_management_types_proto_init() {
	if File_api_items_story_types_v1_management_types_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_management_types_proto_rawDesc), len(file_api_items_story_types_v1_management_types_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_management_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_management_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_management_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_management_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_management_types_proto = out.File
	file_api_items_story_types_v1_management_types_proto_goTypes = nil
	file_api_items_story_types_v1_management_types_proto_depIdxs = nil
}
