// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/turtle_soup_types.proto

package api_items_story_types_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TurtleSoupMatchStatus int32

const (
	TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED TurtleSoupMatchStatus = 0
	// 未 match
	TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_NOT_MATCHED TurtleSoupMatchStatus = 1
	// 部分 match
	TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED TurtleSoupMatchStatus = 2
	// 完全 match
	TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_MATCHED TurtleSoupMatchStatus = 3
)

// Enum value maps for TurtleSoupMatchStatus.
var (
	TurtleSoupMatchStatus_name = map[int32]string{
		0: "TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED",
		1: "TURTLE_SOUP_MATCH_STATUS_NOT_MATCHED",
		2: "TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED",
		3: "TURTLE_SOUP_MATCH_STATUS_MATCHED",
	}
	TurtleSoupMatchStatus_value = map[string]int32{
		"TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED":       0,
		"TURTLE_SOUP_MATCH_STATUS_NOT_MATCHED":       1,
		"TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED": 2,
		"TURTLE_SOUP_MATCH_STATUS_MATCHED":           3,
	}
)

func (x TurtleSoupMatchStatus) Enum() *TurtleSoupMatchStatus {
	p := new(TurtleSoupMatchStatus)
	*p = x
	return p
}

func (x TurtleSoupMatchStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TurtleSoupMatchStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes[0].Descriptor()
}

func (TurtleSoupMatchStatus) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes[0]
}

func (x TurtleSoupMatchStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TurtleSoupMatchStatus.Descriptor instead.
func (TurtleSoupMatchStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{0}
}

type StoryPlayTurtleSoupConfig_ResourceType int32

const (
	StoryPlayTurtleSoupConfig_RESOURCE_TYPE_UNSPECIFIED StoryPlayTurtleSoupConfig_ResourceType = 0
	StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE       StoryPlayTurtleSoupConfig_ResourceType = 1
	StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO       StoryPlayTurtleSoupConfig_ResourceType = 2
)

// Enum value maps for StoryPlayTurtleSoupConfig_ResourceType.
var (
	StoryPlayTurtleSoupConfig_ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
	}
	StoryPlayTurtleSoupConfig_ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
	}
)

func (x StoryPlayTurtleSoupConfig_ResourceType) Enum() *StoryPlayTurtleSoupConfig_ResourceType {
	p := new(StoryPlayTurtleSoupConfig_ResourceType)
	*p = x
	return p
}

func (x StoryPlayTurtleSoupConfig_ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayTurtleSoupConfig_ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes[1].Descriptor()
}

func (StoryPlayTurtleSoupConfig_ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes[1]
}

func (x StoryPlayTurtleSoupConfig_ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayTurtleSoupConfig_ResourceType.Descriptor instead.
func (StoryPlayTurtleSoupConfig_ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{1, 0}
}

type StoryPlayTurtleSoupMassConfig_ResourceType int32

const (
	StoryPlayTurtleSoupMassConfig_RESOURCE_TYPE_UNSPECIFIED StoryPlayTurtleSoupMassConfig_ResourceType = 0
	StoryPlayTurtleSoupMassConfig_RESOURCE_TYPE_IMAGE       StoryPlayTurtleSoupMassConfig_ResourceType = 1
	StoryPlayTurtleSoupMassConfig_RESOURCE_TYPE_VIDEO       StoryPlayTurtleSoupMassConfig_ResourceType = 2
)

// Enum value maps for StoryPlayTurtleSoupMassConfig_ResourceType.
var (
	StoryPlayTurtleSoupMassConfig_ResourceType_name = map[int32]string{
		0: "RESOURCE_TYPE_UNSPECIFIED",
		1: "RESOURCE_TYPE_IMAGE",
		2: "RESOURCE_TYPE_VIDEO",
	}
	StoryPlayTurtleSoupMassConfig_ResourceType_value = map[string]int32{
		"RESOURCE_TYPE_UNSPECIFIED": 0,
		"RESOURCE_TYPE_IMAGE":       1,
		"RESOURCE_TYPE_VIDEO":       2,
	}
)

func (x StoryPlayTurtleSoupMassConfig_ResourceType) Enum() *StoryPlayTurtleSoupMassConfig_ResourceType {
	p := new(StoryPlayTurtleSoupMassConfig_ResourceType)
	*p = x
	return p
}

func (x StoryPlayTurtleSoupMassConfig_ResourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StoryPlayTurtleSoupMassConfig_ResourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes[2].Descriptor()
}

func (StoryPlayTurtleSoupMassConfig_ResourceType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes[2]
}

func (x StoryPlayTurtleSoupMassConfig_ResourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StoryPlayTurtleSoupMassConfig_ResourceType.Descriptor instead.
func (StoryPlayTurtleSoupMassConfig_ResourceType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{5, 0}
}

// 海龟汤玩法上下文
type StoryPlayTurtleSoupContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否已经完成
	IsFinished bool `protobuf:"varint,1,opt,name=is_finished,json=isFinished,proto3" json:"is_finished,omitempty"`
	// 命中的 words，当且仅当 is_finished 为 false 时，此字段有效
	HitWords []string `protobuf:"bytes,2,rep,name=hit_words,json=hitWords,proto3" json:"hit_words,omitempty"`
	// 显示在屏幕中间的 tips
	Tips string `protobuf:"bytes,3,opt,name=tips,proto3" json:"tips,omitempty"`
	// ai 的回复
	AiResponse string `protobuf:"bytes,4,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	// 尝试次数
	TryCount      uint32 `protobuf:"varint,5,opt,name=try_count,json=tryCount,proto3" json:"try_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupContext) Reset() {
	*x = StoryPlayTurtleSoupContext{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupContext) ProtoMessage() {}

func (x *StoryPlayTurtleSoupContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupContext.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryPlayTurtleSoupContext) GetIsFinished() bool {
	if x != nil {
		return x.IsFinished
	}
	return false
}

func (x *StoryPlayTurtleSoupContext) GetHitWords() []string {
	if x != nil {
		return x.HitWords
	}
	return nil
}

func (x *StoryPlayTurtleSoupContext) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *StoryPlayTurtleSoupContext) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

func (x *StoryPlayTurtleSoupContext) GetTryCount() uint32 {
	if x != nil {
		return x.TryCount
	}
	return 0
}

// 海龟汤玩法配置
type StoryPlayTurtleSoupConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 海龟汤玩法里的节点
	Caption      string `protobuf:"bytes,1,opt,name=caption,proto3" json:"caption,omitempty"`
	IntentPrompt string `protobuf:"bytes,2,opt,name=intent_prompt,json=intentPrompt,proto3" json:"intent_prompt,omitempty"`
	// 资源类型，参考 ResourceType
	ResourceType string `protobuf:"bytes,4,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// 资源 url，创建时，客户端传 key
	ResourceUrl string `protobuf:"bytes,5,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 结束资源 url，创建时，客户端传 key
	EndResourceUrl string `protobuf:"bytes,6,opt,name=end_resource_url,json=endResourceUrl,proto3" json:"end_resource_url,omitempty"`
	// 结束资源类型，参考 ResourceType
	EndResourceType string `protobuf:"bytes,7,opt,name=end_resource_type,json=endResourceType,proto3" json:"end_resource_type,omitempty"`
	// 结束资源显示的文字
	EndMessage string `protobuf:"bytes,8,opt,name=end_message,json=endMessage,proto3" json:"end_message,omitempty"`
	// 结束资源显示的文字字体
	EndMessageFont string `protobuf:"bytes,9,opt,name=end_message_font,json=endMessageFont,proto3" json:"end_message_font,omitempty"`
	// 如果资源是视频时，需要传一个视频首帧，创作时给 key
	ThumbnailUrl *string `protobuf:"bytes,10,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	// 如果结束资源是是视频时，需要传一个视频首帧，创作时给 key
	EndThumbnailUrl   *string                                       `protobuf:"bytes,11,opt,name=end_thumbnail_url,json=endThumbnailUrl,proto3,oneof" json:"end_thumbnail_url,omitempty"`
	CustomAiResponses []*StoryPlayTurtleSoupConfig_CustomAiResponse `protobuf:"bytes,12,rep,name=custom_ai_responses,json=customAiResponses,proto3" json:"custom_ai_responses,omitempty"`
	// 模板 id
	TemplateId *string `protobuf:"bytes,13,opt,name=template_id,json=templateId,proto3,oneof" json:"template_id,omitempty"`
	// 是否需要创建自定义模板
	NeedCreateDiyTemplate bool `protobuf:"varint,14,opt,name=need_create_diy_template,json=needCreateDiyTemplate,proto3" json:"need_create_diy_template,omitempty"`
	// 最大尝试次数
	MaxTryCount       uint32              `protobuf:"varint,15,opt,name=max_try_count,json=maxTryCount,proto3" json:"max_try_count,omitempty"`
	CommonPlayConfig  *CommonPlayConfig   `protobuf:"bytes,16,opt,name=common_play_config,json=commonPlayConfig,proto3" json:"common_play_config,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr `protobuf:"bytes,17,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupConfig) Reset() {
	*x = StoryPlayTurtleSoupConfig{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupConfig) ProtoMessage() {}

func (x *StoryPlayTurtleSoupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{1}
}

func (x *StoryPlayTurtleSoupConfig) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetIntentPrompt() string {
	if x != nil {
		return x.IntentPrompt
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetEndResourceUrl() string {
	if x != nil {
		return x.EndResourceUrl
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetEndResourceType() string {
	if x != nil {
		return x.EndResourceType
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetEndMessage() string {
	if x != nil {
		return x.EndMessage
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetEndMessageFont() string {
	if x != nil {
		return x.EndMessageFont
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetEndThumbnailUrl() string {
	if x != nil && x.EndThumbnailUrl != nil {
		return *x.EndThumbnailUrl
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetCustomAiResponses() []*StoryPlayTurtleSoupConfig_CustomAiResponse {
	if x != nil {
		return x.CustomAiResponses
	}
	return nil
}

func (x *StoryPlayTurtleSoupConfig) GetTemplateId() string {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig) GetNeedCreateDiyTemplate() bool {
	if x != nil {
		return x.NeedCreateDiyTemplate
	}
	return false
}

func (x *StoryPlayTurtleSoupConfig) GetMaxTryCount() uint32 {
	if x != nil {
		return x.MaxTryCount
	}
	return 0
}

func (x *StoryPlayTurtleSoupConfig) GetCommonPlayConfig() *CommonPlayConfig {
	if x != nil {
		return x.CommonPlayConfig
	}
	return nil
}

func (x *StoryPlayTurtleSoupConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

// 海龟汤玩法示例
type StoryPlayTurtleSoupExample struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	CommonInfo *ExampleCommonInfo     `protobuf:"bytes,1,opt,name=common_info,json=commonInfo,proto3" json:"common_info,omitempty"`
	// 隐藏意图，用于帮助 hit_words 命中
	EndMessage string `protobuf:"bytes,2,opt,name=end_message,json=endMessage,proto3" json:"end_message,omitempty"`
	// 隐藏意图字体
	EndMessageFont string `protobuf:"bytes,3,opt,name=end_message_font,json=endMessageFont,proto3" json:"end_message_font,omitempty"`
	// 用户发送的消息示例
	Cases         []*StoryPlayTurtleSoupExample_Case `protobuf:"bytes,4,rep,name=cases,proto3" json:"cases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupExample) Reset() {
	*x = StoryPlayTurtleSoupExample{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupExample) ProtoMessage() {}

func (x *StoryPlayTurtleSoupExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupExample.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayTurtleSoupExample) GetCommonInfo() *ExampleCommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *StoryPlayTurtleSoupExample) GetEndMessage() string {
	if x != nil {
		return x.EndMessage
	}
	return ""
}

func (x *StoryPlayTurtleSoupExample) GetEndMessageFont() string {
	if x != nil {
		return x.EndMessageFont
	}
	return ""
}

func (x *StoryPlayTurtleSoupExample) GetCases() []*StoryPlayTurtleSoupExample_Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

type StoryPlayTurtleSoupMassExample struct {
	state         protoimpl.MessageState                 `protogen:"open.v1"`
	CommonInfo    *ExampleCommonInfo                     `protobuf:"bytes,1,opt,name=common_info,json=commonInfo,proto3" json:"common_info,omitempty"`
	Cases         []*StoryPlayTurtleSoupMassExample_Case `protobuf:"bytes,2,rep,name=cases,proto3" json:"cases,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupMassExample) Reset() {
	*x = StoryPlayTurtleSoupMassExample{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupMassExample) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupMassExample) ProtoMessage() {}

func (x *StoryPlayTurtleSoupMassExample) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupMassExample.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupMassExample) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{3}
}

func (x *StoryPlayTurtleSoupMassExample) GetCommonInfo() *ExampleCommonInfo {
	if x != nil {
		return x.CommonInfo
	}
	return nil
}

func (x *StoryPlayTurtleSoupMassExample) GetCases() []*StoryPlayTurtleSoupMassExample_Case {
	if x != nil {
		return x.Cases
	}
	return nil
}

type StoryPlayTurtleSoupMassContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否已经完成
	IsFinished bool `protobuf:"varint,1,opt,name=is_finished,json=isFinished,proto3" json:"is_finished,omitempty"`
	// 显示在屏幕中间的 tips
	Tips string `protobuf:"bytes,2,opt,name=tips,proto3" json:"tips,omitempty"`
	// ai 的回复
	AiResponse string `protobuf:"bytes,3,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	// 尝试次数
	TryCount      uint32 `protobuf:"varint,4,opt,name=try_count,json=tryCount,proto3" json:"try_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupMassContext) Reset() {
	*x = StoryPlayTurtleSoupMassContext{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupMassContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupMassContext) ProtoMessage() {}

func (x *StoryPlayTurtleSoupMassContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupMassContext.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupMassContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{4}
}

func (x *StoryPlayTurtleSoupMassContext) GetIsFinished() bool {
	if x != nil {
		return x.IsFinished
	}
	return false
}

func (x *StoryPlayTurtleSoupMassContext) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassContext) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassContext) GetTryCount() uint32 {
	if x != nil {
		return x.TryCount
	}
	return 0
}

// 与海龟汤类似，只是没有 end_resource_url 和 end_resource_type，为了不混淆旧的数据结构，新开了类型
type StoryPlayTurtleSoupMassConfig struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 海龟汤玩法里的节点
	Caption      string `protobuf:"bytes,1,opt,name=caption,proto3" json:"caption,omitempty"`
	IntentPrompt string `protobuf:"bytes,2,opt,name=intent_prompt,json=intentPrompt,proto3" json:"intent_prompt,omitempty"`
	// 资源类型，参考 ResourceType
	ResourceType string `protobuf:"bytes,4,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// 资源 url，创建时，客户端传 key
	ResourceUrl string `protobuf:"bytes,5,opt,name=resource_url,json=resourceUrl,proto3" json:"resource_url,omitempty"`
	// 马赛克玩法的海龟汤，只有一个资源
	// 如果资源是视频时，需要传一个视频首帧，创作时给 key
	ThumbnailUrl      *string                                           `protobuf:"bytes,8,opt,name=thumbnail_url,json=thumbnailUrl,proto3,oneof" json:"thumbnail_url,omitempty"`
	CustomAiResponses []*StoryPlayTurtleSoupMassConfig_CustomAiResponse `protobuf:"bytes,12,rep,name=custom_ai_responses,json=customAiResponses,proto3" json:"custom_ai_responses,omitempty"`
	// 模板 id
	TemplateId *string `protobuf:"bytes,13,opt,name=template_id,json=templateId,proto3,oneof" json:"template_id,omitempty"`
	// 是否需要创建自定义模板
	NeedCreateDiyTemplate bool `protobuf:"varint,14,opt,name=need_create_diy_template,json=needCreateDiyTemplate,proto3" json:"need_create_diy_template,omitempty"`
	// 最大尝试次数
	MaxTryCount   uint32 `protobuf:"varint,15,opt,name=max_try_count,json=maxTryCount,proto3" json:"max_try_count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupMassConfig) Reset() {
	*x = StoryPlayTurtleSoupMassConfig{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupMassConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupMassConfig) ProtoMessage() {}

func (x *StoryPlayTurtleSoupMassConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupMassConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupMassConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{5}
}

func (x *StoryPlayTurtleSoupMassConfig) GetCaption() string {
	if x != nil {
		return x.Caption
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig) GetIntentPrompt() string {
	if x != nil {
		return x.IntentPrompt
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig) GetResourceUrl() string {
	if x != nil {
		return x.ResourceUrl
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig) GetThumbnailUrl() string {
	if x != nil && x.ThumbnailUrl != nil {
		return *x.ThumbnailUrl
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig) GetCustomAiResponses() []*StoryPlayTurtleSoupMassConfig_CustomAiResponse {
	if x != nil {
		return x.CustomAiResponses
	}
	return nil
}

func (x *StoryPlayTurtleSoupMassConfig) GetTemplateId() string {
	if x != nil && x.TemplateId != nil {
		return *x.TemplateId
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig) GetNeedCreateDiyTemplate() bool {
	if x != nil {
		return x.NeedCreateDiyTemplate
	}
	return false
}

func (x *StoryPlayTurtleSoupMassConfig) GetMaxTryCount() uint32 {
	if x != nil {
		return x.MaxTryCount
	}
	return 0
}

// 自定义 ai 规则
type StoryPlayTurtleSoupConfig_CustomAiResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RuleDescription string                 `protobuf:"bytes,1,opt,name=rule_description,json=ruleDescription,proto3" json:"rule_description,omitempty"`
	RuleResult      string                 `protobuf:"bytes,2,opt,name=rule_result,json=ruleResult,proto3" json:"rule_result,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupConfig_CustomAiResponse) Reset() {
	*x = StoryPlayTurtleSoupConfig_CustomAiResponse{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupConfig_CustomAiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupConfig_CustomAiResponse) ProtoMessage() {}

func (x *StoryPlayTurtleSoupConfig_CustomAiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupConfig_CustomAiResponse.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupConfig_CustomAiResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{1, 0}
}

func (x *StoryPlayTurtleSoupConfig_CustomAiResponse) GetRuleDescription() string {
	if x != nil {
		return x.RuleDescription
	}
	return ""
}

func (x *StoryPlayTurtleSoupConfig_CustomAiResponse) GetRuleResult() string {
	if x != nil {
		return x.RuleResult
	}
	return ""
}

type StoryPlayTurtleSoupExample_Case struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Tips  string                 `protobuf:"bytes,1,opt,name=tips,proto3" json:"tips,omitempty"`
	// 用户发送的消息示例
	UserMessage string `protobuf:"bytes,2,opt,name=user_message,json=userMessage,proto3" json:"user_message,omitempty"`
	// 示例的命中词
	HitWords []string `protobuf:"bytes,3,rep,name=hit_words,json=hitWords,proto3" json:"hit_words,omitempty"`
	// ai 的回复
	AiResponse    string `protobuf:"bytes,4,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupExample_Case) Reset() {
	*x = StoryPlayTurtleSoupExample_Case{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupExample_Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupExample_Case) ProtoMessage() {}

func (x *StoryPlayTurtleSoupExample_Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupExample_Case.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupExample_Case) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{2, 0}
}

func (x *StoryPlayTurtleSoupExample_Case) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *StoryPlayTurtleSoupExample_Case) GetUserMessage() string {
	if x != nil {
		return x.UserMessage
	}
	return ""
}

func (x *StoryPlayTurtleSoupExample_Case) GetHitWords() []string {
	if x != nil {
		return x.HitWords
	}
	return nil
}

func (x *StoryPlayTurtleSoupExample_Case) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

type StoryPlayTurtleSoupMassExample_Case struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tips          string                 `protobuf:"bytes,1,opt,name=tips,proto3" json:"tips,omitempty"`
	UserMessage   string                 `protobuf:"bytes,2,opt,name=user_message,json=userMessage,proto3" json:"user_message,omitempty"`
	AiResponse    string                 `protobuf:"bytes,3,opt,name=ai_response,json=aiResponse,proto3" json:"ai_response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupMassExample_Case) Reset() {
	*x = StoryPlayTurtleSoupMassExample_Case{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupMassExample_Case) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupMassExample_Case) ProtoMessage() {}

func (x *StoryPlayTurtleSoupMassExample_Case) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupMassExample_Case.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupMassExample_Case) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{3, 0}
}

func (x *StoryPlayTurtleSoupMassExample_Case) GetTips() string {
	if x != nil {
		return x.Tips
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassExample_Case) GetUserMessage() string {
	if x != nil {
		return x.UserMessage
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassExample_Case) GetAiResponse() string {
	if x != nil {
		return x.AiResponse
	}
	return ""
}

// 自定义 ai 规则
type StoryPlayTurtleSoupMassConfig_CustomAiResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RuleDescription string                 `protobuf:"bytes,1,opt,name=rule_description,json=ruleDescription,proto3" json:"rule_description,omitempty"`
	RuleResult      string                 `protobuf:"bytes,2,opt,name=rule_result,json=ruleResult,proto3" json:"rule_result,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StoryPlayTurtleSoupMassConfig_CustomAiResponse) Reset() {
	*x = StoryPlayTurtleSoupMassConfig_CustomAiResponse{}
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayTurtleSoupMassConfig_CustomAiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayTurtleSoupMassConfig_CustomAiResponse) ProtoMessage() {}

func (x *StoryPlayTurtleSoupMassConfig_CustomAiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayTurtleSoupMassConfig_CustomAiResponse.ProtoReflect.Descriptor instead.
func (*StoryPlayTurtleSoupMassConfig_CustomAiResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP(), []int{5, 0}
}

func (x *StoryPlayTurtleSoupMassConfig_CustomAiResponse) GetRuleDescription() string {
	if x != nil {
		return x.RuleDescription
	}
	return ""
}

func (x *StoryPlayTurtleSoupMassConfig_CustomAiResponse) GetRuleResult() string {
	if x != nil {
		return x.RuleResult
	}
	return ""
}

var File_api_items_story_types_v1_turtle_soup_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_turtle_soup_types_proto_rawDesc = "" +
	"\n" +
	"0api/items/story/types/v1/turtle_soup_types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\"\xac\x01\n" +
	"\x1aStoryPlayTurtleSoupContext\x12\x1f\n" +
	"\vis_finished\x18\x01 \x01(\bR\n" +
	"isFinished\x12\x1b\n" +
	"\thit_words\x18\x02 \x03(\tR\bhitWords\x12\x12\n" +
	"\x04tips\x18\x03 \x01(\tR\x04tips\x12\x1f\n" +
	"\vai_response\x18\x04 \x01(\tR\n" +
	"aiResponse\x12\x1b\n" +
	"\ttry_count\x18\x05 \x01(\rR\btryCount\"\xc6\b\n" +
	"\x19StoryPlayTurtleSoupConfig\x12\x18\n" +
	"\acaption\x18\x01 \x01(\tR\acaption\x12#\n" +
	"\rintent_prompt\x18\x02 \x01(\tR\fintentPrompt\x12#\n" +
	"\rresource_type\x18\x04 \x01(\tR\fresourceType\x12!\n" +
	"\fresource_url\x18\x05 \x01(\tR\vresourceUrl\x12(\n" +
	"\x10end_resource_url\x18\x06 \x01(\tR\x0eendResourceUrl\x12*\n" +
	"\x11end_resource_type\x18\a \x01(\tR\x0fendResourceType\x12\x1f\n" +
	"\vend_message\x18\b \x01(\tR\n" +
	"endMessage\x12(\n" +
	"\x10end_message_font\x18\t \x01(\tR\x0eendMessageFont\x12(\n" +
	"\rthumbnail_url\x18\n" +
	" \x01(\tH\x00R\fthumbnailUrl\x88\x01\x01\x12/\n" +
	"\x11end_thumbnail_url\x18\v \x01(\tH\x01R\x0fendThumbnailUrl\x88\x01\x01\x12t\n" +
	"\x13custom_ai_responses\x18\f \x03(\v2D.api.items.story.types.v1.StoryPlayTurtleSoupConfig.CustomAiResponseR\x11customAiResponses\x12$\n" +
	"\vtemplate_id\x18\r \x01(\tH\x02R\n" +
	"templateId\x88\x01\x01\x127\n" +
	"\x18need_create_diy_template\x18\x0e \x01(\bR\x15needCreateDiyTemplate\x12\"\n" +
	"\rmax_try_count\x18\x0f \x01(\rR\vmaxTryCount\x12X\n" +
	"\x12common_play_config\x18\x10 \x01(\v2*.api.items.story.types.v1.CommonPlayConfigR\x10commonPlayConfig\x12Z\n" +
	"\x13moment_create_attrs\x18\x11 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\x1a^\n" +
	"\x10CustomAiResponse\x12)\n" +
	"\x10rule_description\x18\x01 \x01(\tR\x0fruleDescription\x12\x1f\n" +
	"\vrule_result\x18\x02 \x01(\tR\n" +
	"ruleResult\"_\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02B\x10\n" +
	"\x0e_thumbnail_urlB\x14\n" +
	"\x12_end_thumbnail_urlB\x0e\n" +
	"\f_template_id\"\x83\x03\n" +
	"\x1aStoryPlayTurtleSoupExample\x12L\n" +
	"\vcommon_info\x18\x01 \x01(\v2+.api.items.story.types.v1.ExampleCommonInfoR\n" +
	"commonInfo\x12\x1f\n" +
	"\vend_message\x18\x02 \x01(\tR\n" +
	"endMessage\x12(\n" +
	"\x10end_message_font\x18\x03 \x01(\tR\x0eendMessageFont\x12O\n" +
	"\x05cases\x18\x04 \x03(\v29.api.items.story.types.v1.StoryPlayTurtleSoupExample.CaseR\x05cases\x1a{\n" +
	"\x04Case\x12\x12\n" +
	"\x04tips\x18\x01 \x01(\tR\x04tips\x12!\n" +
	"\fuser_message\x18\x02 \x01(\tR\vuserMessage\x12\x1b\n" +
	"\thit_words\x18\x03 \x03(\tR\bhitWords\x12\x1f\n" +
	"\vai_response\x18\x04 \x01(\tR\n" +
	"aiResponse\"\xa3\x02\n" +
	"\x1eStoryPlayTurtleSoupMassExample\x12L\n" +
	"\vcommon_info\x18\x01 \x01(\v2+.api.items.story.types.v1.ExampleCommonInfoR\n" +
	"commonInfo\x12S\n" +
	"\x05cases\x18\x02 \x03(\v2=.api.items.story.types.v1.StoryPlayTurtleSoupMassExample.CaseR\x05cases\x1a^\n" +
	"\x04Case\x12\x12\n" +
	"\x04tips\x18\x01 \x01(\tR\x04tips\x12!\n" +
	"\fuser_message\x18\x02 \x01(\tR\vuserMessage\x12\x1f\n" +
	"\vai_response\x18\x03 \x01(\tR\n" +
	"aiResponse\"\x93\x01\n" +
	"\x1eStoryPlayTurtleSoupMassContext\x12\x1f\n" +
	"\vis_finished\x18\x01 \x01(\bR\n" +
	"isFinished\x12\x12\n" +
	"\x04tips\x18\x02 \x01(\tR\x04tips\x12\x1f\n" +
	"\vai_response\x18\x03 \x01(\tR\n" +
	"aiResponse\x12\x1b\n" +
	"\ttry_count\x18\x04 \x01(\rR\btryCount\"\xb0\x05\n" +
	"\x1dStoryPlayTurtleSoupMassConfig\x12\x18\n" +
	"\acaption\x18\x01 \x01(\tR\acaption\x12#\n" +
	"\rintent_prompt\x18\x02 \x01(\tR\fintentPrompt\x12#\n" +
	"\rresource_type\x18\x04 \x01(\tR\fresourceType\x12!\n" +
	"\fresource_url\x18\x05 \x01(\tR\vresourceUrl\x12(\n" +
	"\rthumbnail_url\x18\b \x01(\tH\x00R\fthumbnailUrl\x88\x01\x01\x12x\n" +
	"\x13custom_ai_responses\x18\f \x03(\v2H.api.items.story.types.v1.StoryPlayTurtleSoupMassConfig.CustomAiResponseR\x11customAiResponses\x12$\n" +
	"\vtemplate_id\x18\r \x01(\tH\x01R\n" +
	"templateId\x88\x01\x01\x127\n" +
	"\x18need_create_diy_template\x18\x0e \x01(\bR\x15needCreateDiyTemplate\x12\"\n" +
	"\rmax_try_count\x18\x0f \x01(\rR\vmaxTryCount\x1a^\n" +
	"\x10CustomAiResponse\x12)\n" +
	"\x10rule_description\x18\x01 \x01(\tR\x0fruleDescription\x12\x1f\n" +
	"\vrule_result\x18\x02 \x01(\tR\n" +
	"ruleResult\"_\n" +
	"\fResourceType\x12\x1d\n" +
	"\x19RESOURCE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13RESOURCE_TYPE_IMAGE\x10\x01\x12\x17\n" +
	"\x13RESOURCE_TYPE_VIDEO\x10\x02B\x10\n" +
	"\x0e_thumbnail_urlB\x0e\n" +
	"\f_template_id*\xc1\x01\n" +
	"\x15TurtleSoupMatchStatus\x12(\n" +
	"$TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED\x10\x00\x12(\n" +
	"$TURTLE_SOUP_MATCH_STATUS_NOT_MATCHED\x10\x01\x12.\n" +
	"*TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED\x10\x02\x12$\n" +
	" TURTLE_SOUP_MATCH_STATUS_MATCHED\x10\x03B9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_turtle_soup_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_turtle_soup_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_turtle_soup_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_turtle_soup_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_turtle_soup_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_turtle_soup_types_proto_rawDesc), len(file_api_items_story_types_v1_turtle_soup_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_turtle_soup_types_proto_rawDescData
}

var file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_items_story_types_v1_turtle_soup_types_proto_goTypes = []any{
	(TurtleSoupMatchStatus)(0),                             // 0: api.items.story.types.v1.TurtleSoupMatchStatus
	(StoryPlayTurtleSoupConfig_ResourceType)(0),            // 1: api.items.story.types.v1.StoryPlayTurtleSoupConfig.ResourceType
	(StoryPlayTurtleSoupMassConfig_ResourceType)(0),        // 2: api.items.story.types.v1.StoryPlayTurtleSoupMassConfig.ResourceType
	(*StoryPlayTurtleSoupContext)(nil),                     // 3: api.items.story.types.v1.StoryPlayTurtleSoupContext
	(*StoryPlayTurtleSoupConfig)(nil),                      // 4: api.items.story.types.v1.StoryPlayTurtleSoupConfig
	(*StoryPlayTurtleSoupExample)(nil),                     // 5: api.items.story.types.v1.StoryPlayTurtleSoupExample
	(*StoryPlayTurtleSoupMassExample)(nil),                 // 6: api.items.story.types.v1.StoryPlayTurtleSoupMassExample
	(*StoryPlayTurtleSoupMassContext)(nil),                 // 7: api.items.story.types.v1.StoryPlayTurtleSoupMassContext
	(*StoryPlayTurtleSoupMassConfig)(nil),                  // 8: api.items.story.types.v1.StoryPlayTurtleSoupMassConfig
	(*StoryPlayTurtleSoupConfig_CustomAiResponse)(nil),     // 9: api.items.story.types.v1.StoryPlayTurtleSoupConfig.CustomAiResponse
	(*StoryPlayTurtleSoupExample_Case)(nil),                // 10: api.items.story.types.v1.StoryPlayTurtleSoupExample.Case
	(*StoryPlayTurtleSoupMassExample_Case)(nil),            // 11: api.items.story.types.v1.StoryPlayTurtleSoupMassExample.Case
	(*StoryPlayTurtleSoupMassConfig_CustomAiResponse)(nil), // 12: api.items.story.types.v1.StoryPlayTurtleSoupMassConfig.CustomAiResponse
	(*CommonPlayConfig)(nil),                               // 13: api.items.story.types.v1.CommonPlayConfig
	(*MomentCreateAttr)(nil),                               // 14: api.items.story.types.v1.MomentCreateAttr
	(*ExampleCommonInfo)(nil),                              // 15: api.items.story.types.v1.ExampleCommonInfo
}
var file_api_items_story_types_v1_turtle_soup_types_proto_depIdxs = []int32{
	9,  // 0: api.items.story.types.v1.StoryPlayTurtleSoupConfig.custom_ai_responses:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupConfig.CustomAiResponse
	13, // 1: api.items.story.types.v1.StoryPlayTurtleSoupConfig.common_play_config:type_name -> api.items.story.types.v1.CommonPlayConfig
	14, // 2: api.items.story.types.v1.StoryPlayTurtleSoupConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	15, // 3: api.items.story.types.v1.StoryPlayTurtleSoupExample.common_info:type_name -> api.items.story.types.v1.ExampleCommonInfo
	10, // 4: api.items.story.types.v1.StoryPlayTurtleSoupExample.cases:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupExample.Case
	15, // 5: api.items.story.types.v1.StoryPlayTurtleSoupMassExample.common_info:type_name -> api.items.story.types.v1.ExampleCommonInfo
	11, // 6: api.items.story.types.v1.StoryPlayTurtleSoupMassExample.cases:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupMassExample.Case
	12, // 7: api.items.story.types.v1.StoryPlayTurtleSoupMassConfig.custom_ai_responses:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupMassConfig.CustomAiResponse
	8,  // [8:8] is the sub-list for method output_type
	8,  // [8:8] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_turtle_soup_types_proto_init() }
func file_api_items_story_types_v1_turtle_soup_types_proto_init() {
	if File_api_items_story_types_v1_turtle_soup_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[1].OneofWrappers = []any{}
	file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_turtle_soup_types_proto_rawDesc), len(file_api_items_story_types_v1_turtle_soup_types_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_turtle_soup_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_turtle_soup_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_turtle_soup_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_turtle_soup_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_turtle_soup_types_proto = out.File
	file_api_items_story_types_v1_turtle_soup_types_proto_goTypes = nil
	file_api_items_story_types_v1_turtle_soup_types_proto_depIdxs = nil
}
