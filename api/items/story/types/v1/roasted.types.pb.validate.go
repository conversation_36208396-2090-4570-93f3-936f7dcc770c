// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/roasted.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on RoastedQuestion with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RoastedQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RoastedQuestion with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RoastedQuestionMultiError, or nil if none found.
func (m *RoastedQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *RoastedQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	// no validation rules for TtsAudioUrl

	// no validation rules for TtsAudioKey

	// no validation rules for Thinking

	for idx, item := range m.GetWords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RoastedQuestionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RoastedQuestionValidationError{
						field:  fmt.Sprintf("Words[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RoastedQuestionValidationError{
					field:  fmt.Sprintf("Words[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RoastedQuestionMultiError(errors)
	}

	return nil
}

// RoastedQuestionMultiError is an error wrapping multiple validation errors
// returned by RoastedQuestion.ValidateAll() if the designated constraints
// aren't met.
type RoastedQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RoastedQuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RoastedQuestionMultiError) AllErrors() []error { return m }

// RoastedQuestionValidationError is the validation error returned by
// RoastedQuestion.Validate if the designated constraints aren't met.
type RoastedQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RoastedQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RoastedQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RoastedQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RoastedQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RoastedQuestionValidationError) ErrorName() string { return "RoastedQuestionValidationError" }

// Error satisfies the builtin error interface
func (e RoastedQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRoastedQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RoastedQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RoastedQuestionValidationError{}

// Validate checks the field values on RoastedQuestionWithUserAnswer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RoastedQuestionWithUserAnswer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RoastedQuestionWithUserAnswer with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// RoastedQuestionWithUserAnswerMultiError, or nil if none found.
func (m *RoastedQuestionWithUserAnswer) ValidateAll() error {
	return m.validate(true)
}

func (m *RoastedQuestionWithUserAnswer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQuestion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RoastedQuestionWithUserAnswerValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RoastedQuestionWithUserAnswerValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RoastedQuestionWithUserAnswerValidationError{
				field:  "Question",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserVoiceKey

	if len(errors) > 0 {
		return RoastedQuestionWithUserAnswerMultiError(errors)
	}

	return nil
}

// RoastedQuestionWithUserAnswerMultiError is an error wrapping multiple
// validation errors returned by RoastedQuestionWithUserAnswer.ValidateAll()
// if the designated constraints aren't met.
type RoastedQuestionWithUserAnswerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RoastedQuestionWithUserAnswerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RoastedQuestionWithUserAnswerMultiError) AllErrors() []error { return m }

// RoastedQuestionWithUserAnswerValidationError is the validation error
// returned by RoastedQuestionWithUserAnswer.Validate if the designated
// constraints aren't met.
type RoastedQuestionWithUserAnswerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RoastedQuestionWithUserAnswerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RoastedQuestionWithUserAnswerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RoastedQuestionWithUserAnswerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RoastedQuestionWithUserAnswerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RoastedQuestionWithUserAnswerValidationError) ErrorName() string {
	return "RoastedQuestionWithUserAnswerValidationError"
}

// Error satisfies the builtin error interface
func (e RoastedQuestionWithUserAnswerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRoastedQuestionWithUserAnswer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RoastedQuestionWithUserAnswerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RoastedQuestionWithUserAnswerValidationError{}

// Validate checks the field values on StoryPlayRoastedTopic with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayRoastedTopic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayRoastedTopic with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayRoastedTopicMultiError, or nil if none found.
func (m *StoryPlayRoastedTopic) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayRoastedTopic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetGreeting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayRoastedTopicValidationError{
					field:  "Greeting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayRoastedTopicValidationError{
					field:  "Greeting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGreeting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayRoastedTopicValidationError{
				field:  "Greeting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEnding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayRoastedTopicValidationError{
					field:  "Ending",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayRoastedTopicValidationError{
					field:  "Ending",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEnding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayRoastedTopicValidationError{
				field:  "Ending",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayRoastedTopicValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayRoastedTopicValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayRoastedTopicValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for MaxFollowup

	if len(errors) > 0 {
		return StoryPlayRoastedTopicMultiError(errors)
	}

	return nil
}

// StoryPlayRoastedTopicMultiError is an error wrapping multiple validation
// errors returned by StoryPlayRoastedTopic.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayRoastedTopicMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayRoastedTopicMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayRoastedTopicMultiError) AllErrors() []error { return m }

// StoryPlayRoastedTopicValidationError is the validation error returned by
// StoryPlayRoastedTopic.Validate if the designated constraints aren't met.
type StoryPlayRoastedTopicValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayRoastedTopicValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayRoastedTopicValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayRoastedTopicValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayRoastedTopicValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayRoastedTopicValidationError) ErrorName() string {
	return "StoryPlayRoastedTopicValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayRoastedTopicValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayRoastedTopic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayRoastedTopicValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayRoastedTopicValidationError{}

// Validate checks the field values on StoryPlayRoastedConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayRoastedConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayRoastedConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayRoastedConfigMultiError, or nil if none found.
func (m *StoryPlayRoastedConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayRoastedConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetTopic() == nil {
		err := StoryPlayRoastedConfigValidationError{
			field:  "Topic",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTopic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayRoastedConfigValidationError{
					field:  "Topic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayRoastedConfigValidationError{
					field:  "Topic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayRoastedConfigValidationError{
				field:  "Topic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayRoastedConfigValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayRoastedConfigValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayRoastedConfigValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCover()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayRoastedConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayRoastedConfigValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCover()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayRoastedConfigValidationError{
				field:  "Cover",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StoryPlayRoastedConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StoryPlayRoastedConfigValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StoryPlayRoastedConfigValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return StoryPlayRoastedConfigMultiError(errors)
	}

	return nil
}

// StoryPlayRoastedConfigMultiError is an error wrapping multiple validation
// errors returned by StoryPlayRoastedConfig.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayRoastedConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayRoastedConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayRoastedConfigMultiError) AllErrors() []error { return m }

// StoryPlayRoastedConfigValidationError is the validation error returned by
// StoryPlayRoastedConfig.Validate if the designated constraints aren't met.
type StoryPlayRoastedConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayRoastedConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayRoastedConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayRoastedConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayRoastedConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayRoastedConfigValidationError) ErrorName() string {
	return "StoryPlayRoastedConfigValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayRoastedConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayRoastedConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayRoastedConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayRoastedConfigValidationError{}

// Validate checks the field values on StoryPlayRoastedContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayRoastedContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayRoastedContext with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayRoastedContextMultiError, or nil if none found.
func (m *StoryPlayRoastedContext) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayRoastedContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsConsumed

	if len(errors) > 0 {
		return StoryPlayRoastedContextMultiError(errors)
	}

	return nil
}

// StoryPlayRoastedContextMultiError is an error wrapping multiple validation
// errors returned by StoryPlayRoastedContext.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayRoastedContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayRoastedContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayRoastedContextMultiError) AllErrors() []error { return m }

// StoryPlayRoastedContextValidationError is the validation error returned by
// StoryPlayRoastedContext.Validate if the designated constraints aren't met.
type StoryPlayRoastedContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayRoastedContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayRoastedContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayRoastedContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayRoastedContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayRoastedContextValidationError) ErrorName() string {
	return "StoryPlayRoastedContextValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayRoastedContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayRoastedContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayRoastedContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayRoastedContextValidationError{}

// Validate checks the field values on StoryPlayRoastedExample with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryPlayRoastedExample) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayRoastedExample with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryPlayRoastedExampleMultiError, or nil if none found.
func (m *StoryPlayRoastedExample) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayRoastedExample) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryPlayRoastedExampleValidationError{
					field:  "Topic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryPlayRoastedExampleValidationError{
					field:  "Topic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryPlayRoastedExampleValidationError{
				field:  "Topic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoryPlayRoastedExampleMultiError(errors)
	}

	return nil
}

// StoryPlayRoastedExampleMultiError is an error wrapping multiple validation
// errors returned by StoryPlayRoastedExample.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayRoastedExampleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayRoastedExampleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayRoastedExampleMultiError) AllErrors() []error { return m }

// StoryPlayRoastedExampleValidationError is the validation error returned by
// StoryPlayRoastedExample.Validate if the designated constraints aren't met.
type StoryPlayRoastedExampleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayRoastedExampleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayRoastedExampleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayRoastedExampleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayRoastedExampleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayRoastedExampleValidationError) ErrorName() string {
	return "StoryPlayRoastedExampleValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayRoastedExampleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayRoastedExample.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayRoastedExampleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayRoastedExampleValidationError{}

// Validate checks the field values on StoryPlayRoastedTopic_BotAnnouncement
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StoryPlayRoastedTopic_BotAnnouncement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryPlayRoastedTopic_BotAnnouncement
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// StoryPlayRoastedTopic_BotAnnouncementMultiError, or nil if none found.
func (m *StoryPlayRoastedTopic_BotAnnouncement) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryPlayRoastedTopic_BotAnnouncement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Content

	// no validation rules for TtsAudioKey

	// no validation rules for TtsAudioUrl

	if len(errors) > 0 {
		return StoryPlayRoastedTopic_BotAnnouncementMultiError(errors)
	}

	return nil
}

// StoryPlayRoastedTopic_BotAnnouncementMultiError is an error wrapping
// multiple validation errors returned by
// StoryPlayRoastedTopic_BotAnnouncement.ValidateAll() if the designated
// constraints aren't met.
type StoryPlayRoastedTopic_BotAnnouncementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryPlayRoastedTopic_BotAnnouncementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryPlayRoastedTopic_BotAnnouncementMultiError) AllErrors() []error { return m }

// StoryPlayRoastedTopic_BotAnnouncementValidationError is the validation error
// returned by StoryPlayRoastedTopic_BotAnnouncement.Validate if the
// designated constraints aren't met.
type StoryPlayRoastedTopic_BotAnnouncementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryPlayRoastedTopic_BotAnnouncementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryPlayRoastedTopic_BotAnnouncementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryPlayRoastedTopic_BotAnnouncementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryPlayRoastedTopic_BotAnnouncementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryPlayRoastedTopic_BotAnnouncementValidationError) ErrorName() string {
	return "StoryPlayRoastedTopic_BotAnnouncementValidationError"
}

// Error satisfies the builtin error interface
func (e StoryPlayRoastedTopic_BotAnnouncementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryPlayRoastedTopic_BotAnnouncement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryPlayRoastedTopic_BotAnnouncementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryPlayRoastedTopic_BotAnnouncementValidationError{}
