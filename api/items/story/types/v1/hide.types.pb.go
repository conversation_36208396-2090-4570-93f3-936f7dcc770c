// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/hide.types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/resource/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 获取贴纸的触发类型
type HideStickerTriggerType int32

const (
	HideStickerTriggerType_STICKER_TRIGGER_TYPE_UNSPECIFIED HideStickerTriggerType = 0
	// 连续点击
	HideStickerTriggerType_STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK HideStickerTriggerType = 1
	// 拖动图层
	HideStickerTriggerType_STICKER_TRIGGER_TYPE_DRAG HideStickerTriggerType = 2
	// 点赞
	HideStickerTriggerType_STICKER_TRIGGER_TYPE_LIKE HideStickerTriggerType = 3
	// 摇动手机
	HideStickerTriggerType_STICKER_TRIGGER_TYPE_SHAKE HideStickerTriggerType = 4
	// 长按
	HideStickerTriggerType_STICKER_TRIGGER_TYPE_LONG_PRESS HideStickerTriggerType = 5
)

// Enum value maps for HideStickerTriggerType.
var (
	HideStickerTriggerType_name = map[int32]string{
		0: "STICKER_TRIGGER_TYPE_UNSPECIFIED",
		1: "STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK",
		2: "STICKER_TRIGGER_TYPE_DRAG",
		3: "STICKER_TRIGGER_TYPE_LIKE",
		4: "STICKER_TRIGGER_TYPE_SHAKE",
		5: "STICKER_TRIGGER_TYPE_LONG_PRESS",
	}
	HideStickerTriggerType_value = map[string]int32{
		"STICKER_TRIGGER_TYPE_UNSPECIFIED":        0,
		"STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK": 1,
		"STICKER_TRIGGER_TYPE_DRAG":               2,
		"STICKER_TRIGGER_TYPE_LIKE":               3,
		"STICKER_TRIGGER_TYPE_SHAKE":              4,
		"STICKER_TRIGGER_TYPE_LONG_PRESS":         5,
	}
)

func (x HideStickerTriggerType) Enum() *HideStickerTriggerType {
	p := new(HideStickerTriggerType)
	*p = x
	return p
}

func (x HideStickerTriggerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HideStickerTriggerType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_types_v1_hide_types_proto_enumTypes[0].Descriptor()
}

func (HideStickerTriggerType) Type() protoreflect.EnumType {
	return &file_api_items_story_types_v1_hide_types_proto_enumTypes[0]
}

func (x HideStickerTriggerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HideStickerTriggerType.Descriptor instead.
func (HideStickerTriggerType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{0}
}

type StickerTransform struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Location      *StickerTransform_Location `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	Size          *StickerTransform_Size     `protobuf:"bytes,2,opt,name=size,proto3" json:"size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerTransform) Reset() {
	*x = StickerTransform{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerTransform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerTransform) ProtoMessage() {}

func (x *StickerTransform) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerTransform.ProtoReflect.Descriptor instead.
func (*StickerTransform) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{0}
}

func (x *StickerTransform) GetLocation() *StickerTransform_Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *StickerTransform) GetSize() *StickerTransform_Size {
	if x != nil {
		return x.Size
	}
	return nil
}

type HideSticker struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	FromStoryId   string                 `protobuf:"bytes,2,opt,name=from_story_id,json=fromStoryId,proto3" json:"from_story_id,omitempty"`
	Resource      *v1.Resource           `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
	Transform     *StickerTransform      `protobuf:"bytes,4,opt,name=transform,proto3" json:"transform,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HideSticker) Reset() {
	*x = HideSticker{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HideSticker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HideSticker) ProtoMessage() {}

func (x *HideSticker) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HideSticker.ProtoReflect.Descriptor instead.
func (*HideSticker) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{1}
}

func (x *HideSticker) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *HideSticker) GetFromStoryId() string {
	if x != nil {
		return x.FromStoryId
	}
	return ""
}

func (x *HideSticker) GetResource() *v1.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *HideSticker) GetTransform() *StickerTransform {
	if x != nil {
		return x.Transform
	}
	return nil
}

// hide 玩法上下文
type StoryPlayHideContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否消费过此 story
	IsConsumed bool `protobuf:"varint,1,opt,name=is_consumed,json=isConsumed,proto3" json:"is_consumed,omitempty"`
	// 解锁获得的贴纸
	UnlockedStickers []*HideSticker `protobuf:"bytes,2,rep,name=unlocked_stickers,json=unlockedStickers,proto3" json:"unlocked_stickers,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *StoryPlayHideContext) Reset() {
	*x = StoryPlayHideContext{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayHideContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayHideContext) ProtoMessage() {}

func (x *StoryPlayHideContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayHideContext.ProtoReflect.Descriptor instead.
func (*StoryPlayHideContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayHideContext) GetIsConsumed() bool {
	if x != nil {
		return x.IsConsumed
	}
	return false
}

func (x *StoryPlayHideContext) GetUnlockedStickers() []*HideSticker {
	if x != nil {
		return x.UnlockedStickers
	}
	return nil
}

// 连续点击
type StickerWithTriggerTypeContinuousClickData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创作时需要指定的 Stickers 及其所在的点击判定位置
	// 服务端会根据 Location 及半径 聚合这些 stickers
	StickersWithClickLocationsInCreation []*HideSticker                                                                  `protobuf:"bytes,1,rep,name=stickers_with_click_locations_in_creation,json=stickersWithClickLocationsInCreation,proto3" json:"stickers_with_click_locations_in_creation,omitempty"`
	StickersWithClickLocationsInConsume  []*StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume `protobuf:"bytes,2,rep,name=stickers_with_click_locations_in_consume,json=stickersWithClickLocationsInConsume,proto3" json:"stickers_with_click_locations_in_consume,omitempty"`
	NeedClickCount                       uint32                                                                          `protobuf:"varint,3,opt,name=need_click_count,json=needClickCount,proto3" json:"need_click_count,omitempty"`
	unknownFields                        protoimpl.UnknownFields
	sizeCache                            protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeContinuousClickData) Reset() {
	*x = StickerWithTriggerTypeContinuousClickData{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeContinuousClickData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeContinuousClickData) ProtoMessage() {}

func (x *StickerWithTriggerTypeContinuousClickData) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeContinuousClickData.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeContinuousClickData) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{3}
}

func (x *StickerWithTriggerTypeContinuousClickData) GetStickersWithClickLocationsInCreation() []*HideSticker {
	if x != nil {
		return x.StickersWithClickLocationsInCreation
	}
	return nil
}

func (x *StickerWithTriggerTypeContinuousClickData) GetStickersWithClickLocationsInConsume() []*StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume {
	if x != nil {
		return x.StickersWithClickLocationsInConsume
	}
	return nil
}

func (x *StickerWithTriggerTypeContinuousClickData) GetNeedClickCount() uint32 {
	if x != nil {
		return x.NeedClickCount
	}
	return 0
}

// 拖动图层
type StickerWithTriggerTypeDragData struct {
	state         protoimpl.MessageState                      `protogen:"open.v1"`
	CutObjects    []*StickerWithTriggerTypeDragData_CutObject `protobuf:"bytes,2,rep,name=cut_objects,json=cutObjects,proto3" json:"cut_objects,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeDragData) Reset() {
	*x = StickerWithTriggerTypeDragData{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeDragData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeDragData) ProtoMessage() {}

func (x *StickerWithTriggerTypeDragData) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeDragData.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeDragData) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{4}
}

func (x *StickerWithTriggerTypeDragData) GetCutObjects() []*StickerWithTriggerTypeDragData_CutObject {
	if x != nil {
		return x.CutObjects
	}
	return nil
}

// 点赞
type StickerWithTriggerTypeLikeData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stickers      []*HideSticker         `protobuf:"bytes,1,rep,name=stickers,proto3" json:"stickers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeLikeData) Reset() {
	*x = StickerWithTriggerTypeLikeData{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeLikeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeLikeData) ProtoMessage() {}

func (x *StickerWithTriggerTypeLikeData) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeLikeData.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeLikeData) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{5}
}

func (x *StickerWithTriggerTypeLikeData) GetStickers() []*HideSticker {
	if x != nil {
		return x.Stickers
	}
	return nil
}

// 摇动手机
type StickerWithTriggerTypeShakeData struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	Stickers                 []*HideSticker         `protobuf:"bytes,1,rep,name=stickers,proto3" json:"stickers,omitempty"`
	NeedShakeCount           uint32                 `protobuf:"varint,2,opt,name=need_shake_count,json=needShakeCount,proto3" json:"need_shake_count,omitempty"`
	NeedShakeDurationSeconds uint32                 `protobuf:"varint,3,opt,name=need_shake_duration_seconds,json=needShakeDurationSeconds,proto3" json:"need_shake_duration_seconds,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeShakeData) Reset() {
	*x = StickerWithTriggerTypeShakeData{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeShakeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeShakeData) ProtoMessage() {}

func (x *StickerWithTriggerTypeShakeData) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeShakeData.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeShakeData) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{6}
}

func (x *StickerWithTriggerTypeShakeData) GetStickers() []*HideSticker {
	if x != nil {
		return x.Stickers
	}
	return nil
}

func (x *StickerWithTriggerTypeShakeData) GetNeedShakeCount() uint32 {
	if x != nil {
		return x.NeedShakeCount
	}
	return 0
}

func (x *StickerWithTriggerTypeShakeData) GetNeedShakeDurationSeconds() uint32 {
	if x != nil {
		return x.NeedShakeDurationSeconds
	}
	return 0
}

// 长按
type StickerWithTriggerTypeLongPressData struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	Stickers                     []*HideSticker         `protobuf:"bytes,1,rep,name=stickers,proto3" json:"stickers,omitempty"`
	NeedLongPressDurationSeconds uint32                 `protobuf:"varint,2,opt,name=need_long_press_duration_seconds,json=needLongPressDurationSeconds,proto3" json:"need_long_press_duration_seconds,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeLongPressData) Reset() {
	*x = StickerWithTriggerTypeLongPressData{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeLongPressData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeLongPressData) ProtoMessage() {}

func (x *StickerWithTriggerTypeLongPressData) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeLongPressData.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeLongPressData) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{7}
}

func (x *StickerWithTriggerTypeLongPressData) GetStickers() []*HideSticker {
	if x != nil {
		return x.Stickers
	}
	return nil
}

func (x *StickerWithTriggerTypeLongPressData) GetNeedLongPressDurationSeconds() uint32 {
	if x != nil {
		return x.NeedLongPressDurationSeconds
	}
	return 0
}

type StickerWithTriggerType struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	TriggerType HideStickerTriggerType `protobuf:"varint,1,opt,name=trigger_type,json=triggerType,proto3,enum=api.items.story.types.v1.HideStickerTriggerType" json:"trigger_type,omitempty"`
	// Types that are valid to be assigned to Data:
	//
	//	*StickerWithTriggerType_ContinuousClickData
	//	*StickerWithTriggerType_DragData
	//	*StickerWithTriggerType_LikeData
	//	*StickerWithTriggerType_ShakeData
	//	*StickerWithTriggerType_LongPressData
	Data          isStickerWithTriggerType_Data `protobuf_oneof:"data"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerWithTriggerType) Reset() {
	*x = StickerWithTriggerType{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerType) ProtoMessage() {}

func (x *StickerWithTriggerType) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerType.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerType) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{8}
}

func (x *StickerWithTriggerType) GetTriggerType() HideStickerTriggerType {
	if x != nil {
		return x.TriggerType
	}
	return HideStickerTriggerType_STICKER_TRIGGER_TYPE_UNSPECIFIED
}

func (x *StickerWithTriggerType) GetData() isStickerWithTriggerType_Data {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *StickerWithTriggerType) GetContinuousClickData() *StickerWithTriggerTypeContinuousClickData {
	if x != nil {
		if x, ok := x.Data.(*StickerWithTriggerType_ContinuousClickData); ok {
			return x.ContinuousClickData
		}
	}
	return nil
}

func (x *StickerWithTriggerType) GetDragData() *StickerWithTriggerTypeDragData {
	if x != nil {
		if x, ok := x.Data.(*StickerWithTriggerType_DragData); ok {
			return x.DragData
		}
	}
	return nil
}

func (x *StickerWithTriggerType) GetLikeData() *StickerWithTriggerTypeLikeData {
	if x != nil {
		if x, ok := x.Data.(*StickerWithTriggerType_LikeData); ok {
			return x.LikeData
		}
	}
	return nil
}

func (x *StickerWithTriggerType) GetShakeData() *StickerWithTriggerTypeShakeData {
	if x != nil {
		if x, ok := x.Data.(*StickerWithTriggerType_ShakeData); ok {
			return x.ShakeData
		}
	}
	return nil
}

func (x *StickerWithTriggerType) GetLongPressData() *StickerWithTriggerTypeLongPressData {
	if x != nil {
		if x, ok := x.Data.(*StickerWithTriggerType_LongPressData); ok {
			return x.LongPressData
		}
	}
	return nil
}

type isStickerWithTriggerType_Data interface {
	isStickerWithTriggerType_Data()
}

type StickerWithTriggerType_ContinuousClickData struct {
	ContinuousClickData *StickerWithTriggerTypeContinuousClickData `protobuf:"bytes,2,opt,name=continuous_click_data,json=continuousClickData,proto3,oneof"`
}

type StickerWithTriggerType_DragData struct {
	DragData *StickerWithTriggerTypeDragData `protobuf:"bytes,3,opt,name=drag_data,json=dragData,proto3,oneof"`
}

type StickerWithTriggerType_LikeData struct {
	LikeData *StickerWithTriggerTypeLikeData `protobuf:"bytes,4,opt,name=like_data,json=likeData,proto3,oneof"`
}

type StickerWithTriggerType_ShakeData struct {
	ShakeData *StickerWithTriggerTypeShakeData `protobuf:"bytes,5,opt,name=shake_data,json=shakeData,proto3,oneof"`
}

type StickerWithTriggerType_LongPressData struct {
	LongPressData *StickerWithTriggerTypeLongPressData `protobuf:"bytes,6,opt,name=long_press_data,json=longPressData,proto3,oneof"`
}

func (*StickerWithTriggerType_ContinuousClickData) isStickerWithTriggerType_Data() {}

func (*StickerWithTriggerType_DragData) isStickerWithTriggerType_Data() {}

func (*StickerWithTriggerType_LikeData) isStickerWithTriggerType_Data() {}

func (*StickerWithTriggerType_ShakeData) isStickerWithTriggerType_Data() {}

func (*StickerWithTriggerType_LongPressData) isStickerWithTriggerType_Data() {}

// hide 玩法配置
type StoryPlayHideConfig struct {
	state                   protoimpl.MessageState    `protogen:"open.v1"`
	StickerWithTriggerTypes []*StickerWithTriggerType `protobuf:"bytes,1,rep,name=sticker_with_trigger_types,json=stickerWithTriggerTypes,proto3" json:"sticker_with_trigger_types,omitempty"`
	BackgroundImage         *v1.Resource              `protobuf:"bytes,2,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	AttachmentTexts         []*AttachmentText         `protobuf:"bytes,3,rep,name=attachment_texts,json=attachmentTexts,proto3" json:"attachment_texts,omitempty"`
	// 创作侧忽略，此值是消费侧使用，会根据创作 sticker_with_trigger_types 计算而来
	AllStickers       []*HideSticker      `protobuf:"bytes,4,rep,name=all_stickers,json=allStickers,proto3" json:"all_stickers,omitempty"`
	MomentCreateAttrs []*MomentCreateAttr `protobuf:"bytes,5,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	Cover             *v1.Resource        `protobuf:"bytes,6,opt,name=cover,proto3,oneof" json:"cover,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *StoryPlayHideConfig) Reset() {
	*x = StoryPlayHideConfig{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayHideConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayHideConfig) ProtoMessage() {}

func (x *StoryPlayHideConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayHideConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayHideConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{9}
}

func (x *StoryPlayHideConfig) GetStickerWithTriggerTypes() []*StickerWithTriggerType {
	if x != nil {
		return x.StickerWithTriggerTypes
	}
	return nil
}

func (x *StoryPlayHideConfig) GetBackgroundImage() *v1.Resource {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

func (x *StoryPlayHideConfig) GetAttachmentTexts() []*AttachmentText {
	if x != nil {
		return x.AttachmentTexts
	}
	return nil
}

func (x *StoryPlayHideConfig) GetAllStickers() []*HideSticker {
	if x != nil {
		return x.AllStickers
	}
	return nil
}

func (x *StoryPlayHideConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

func (x *StoryPlayHideConfig) GetCover() *v1.Resource {
	if x != nil {
		return x.Cover
	}
	return nil
}

type StickerTransform_Location struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 浮点型，表示距离原点的比例 0 ~ 1
	X string `protobuf:"bytes,1,opt,name=x,proto3" json:"x,omitempty"`
	// 浮点型，表示距离原点的比例 0 ~ 1
	Y             string `protobuf:"bytes,2,opt,name=y,proto3" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerTransform_Location) Reset() {
	*x = StickerTransform_Location{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerTransform_Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerTransform_Location) ProtoMessage() {}

func (x *StickerTransform_Location) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerTransform_Location.ProtoReflect.Descriptor instead.
func (*StickerTransform_Location) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{0, 0}
}

func (x *StickerTransform_Location) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *StickerTransform_Location) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

type StickerTransform_Size struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宽缩放比例，浮点型，0 ~ 1
	Width string `protobuf:"bytes,1,opt,name=width,proto3" json:"width,omitempty"`
	// 高缩放比例，浮点型，0 ~ 1
	Height        string `protobuf:"bytes,2,opt,name=height,proto3" json:"height,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerTransform_Size) Reset() {
	*x = StickerTransform_Size{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerTransform_Size) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerTransform_Size) ProtoMessage() {}

func (x *StickerTransform_Size) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerTransform_Size.ProtoReflect.Descriptor instead.
func (*StickerTransform_Size) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{0, 1}
}

func (x *StickerTransform_Size) GetWidth() string {
	if x != nil {
		return x.Width
	}
	return ""
}

func (x *StickerTransform_Size) GetHeight() string {
	if x != nil {
		return x.Height
	}
	return ""
}

type StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stickers      []*HideSticker         `protobuf:"bytes,1,rep,name=stickers,proto3" json:"stickers,omitempty"`
	X             string                 `protobuf:"bytes,2,opt,name=x,proto3" json:"x,omitempty"`
	Y             string                 `protobuf:"bytes,3,opt,name=y,proto3" json:"y,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) Reset() {
	*x = StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) ProtoMessage() {}

func (x *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{3, 0}
}

func (x *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) GetStickers() []*HideSticker {
	if x != nil {
		return x.Stickers
	}
	return nil
}

func (x *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) GetX() string {
	if x != nil {
		return x.X
	}
	return ""
}

func (x *StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume) GetY() string {
	if x != nil {
		return x.Y
	}
	return ""
}

// 抠图对象
type StickerWithTriggerTypeDragData_CutObject struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 创作时忽略
	MaskImageUrl string `protobuf:"bytes,1,opt,name=mask_image_url,json=maskImageUrl,proto3" json:"mask_image_url,omitempty"`
	// 消费时忽略
	MaskImageKey  string       `protobuf:"bytes,2,opt,name=mask_image_key,json=maskImageKey,proto3" json:"mask_image_key,omitempty"`
	Sticker       *HideSticker `protobuf:"bytes,4,opt,name=sticker,proto3" json:"sticker,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StickerWithTriggerTypeDragData_CutObject) Reset() {
	*x = StickerWithTriggerTypeDragData_CutObject{}
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StickerWithTriggerTypeDragData_CutObject) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StickerWithTriggerTypeDragData_CutObject) ProtoMessage() {}

func (x *StickerWithTriggerTypeDragData_CutObject) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_hide_types_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StickerWithTriggerTypeDragData_CutObject.ProtoReflect.Descriptor instead.
func (*StickerWithTriggerTypeDragData_CutObject) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_hide_types_proto_rawDescGZIP(), []int{4, 0}
}

func (x *StickerWithTriggerTypeDragData_CutObject) GetMaskImageUrl() string {
	if x != nil {
		return x.MaskImageUrl
	}
	return ""
}

func (x *StickerWithTriggerTypeDragData_CutObject) GetMaskImageKey() string {
	if x != nil {
		return x.MaskImageKey
	}
	return ""
}

func (x *StickerWithTriggerTypeDragData_CutObject) GetSticker() *HideSticker {
	if x != nil {
		return x.Sticker
	}
	return nil
}

var File_api_items_story_types_v1_hide_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_hide_types_proto_rawDesc = "" +
	"\n" +
	")api/items/story/types/v1/hide.types.proto\x12\x18api.items.story.types.v1\x1a\x17validate/validate.proto\x1a)api/items/story/types/v1/base_types.proto\x1a!api/resource/types/v1/types.proto\"\x86\x02\n" +
	"\x10StickerTransform\x12O\n" +
	"\blocation\x18\x01 \x01(\v23.api.items.story.types.v1.StickerTransform.LocationR\blocation\x12C\n" +
	"\x04size\x18\x02 \x01(\v2/.api.items.story.types.v1.StickerTransform.SizeR\x04size\x1a&\n" +
	"\bLocation\x12\f\n" +
	"\x01x\x18\x01 \x01(\tR\x01x\x12\f\n" +
	"\x01y\x18\x02 \x01(\tR\x01y\x1a4\n" +
	"\x04Size\x12\x14\n" +
	"\x05width\x18\x01 \x01(\tR\x05width\x12\x16\n" +
	"\x06height\x18\x02 \x01(\tR\x06height\"\xc8\x01\n" +
	"\vHideSticker\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\"\n" +
	"\rfrom_story_id\x18\x02 \x01(\tR\vfromStoryId\x12;\n" +
	"\bresource\x18\x03 \x01(\v2\x1f.api.resource.types.v1.ResourceR\bresource\x12H\n" +
	"\ttransform\x18\x04 \x01(\v2*.api.items.story.types.v1.StickerTransformR\ttransform\"\x8b\x01\n" +
	"\x14StoryPlayHideContext\x12\x1f\n" +
	"\vis_consumed\x18\x01 \x01(\bR\n" +
	"isConsumed\x12R\n" +
	"\x11unlocked_stickers\x18\x02 \x03(\v2%.api.items.story.types.v1.HideStickerR\x10unlockedStickers\"\x9b\x04\n" +
	")StickerWithTriggerTypeContinuousClickData\x12~\n" +
	")stickers_with_click_locations_in_creation\x18\x01 \x03(\v2%.api.items.story.types.v1.HideStickerR$stickersWithClickLocationsInCreation\x12\xbd\x01\n" +
	"(stickers_with_click_locations_in_consume\x18\x02 \x03(\v2f.api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData.StickersWithClickLocationInConsumeR#stickersWithClickLocationsInConsume\x12(\n" +
	"\x10need_click_count\x18\x03 \x01(\rR\x0eneedClickCount\x1a\x83\x01\n" +
	"\"StickersWithClickLocationInConsume\x12A\n" +
	"\bstickers\x18\x01 \x03(\v2%.api.items.story.types.v1.HideStickerR\bstickers\x12\f\n" +
	"\x01x\x18\x02 \x01(\tR\x01x\x12\f\n" +
	"\x01y\x18\x03 \x01(\tR\x01y\"\xa0\x02\n" +
	"\x1eStickerWithTriggerTypeDragData\x12c\n" +
	"\vcut_objects\x18\x02 \x03(\v2B.api.items.story.types.v1.StickerWithTriggerTypeDragData.CutObjectR\n" +
	"cutObjects\x1a\x98\x01\n" +
	"\tCutObject\x12$\n" +
	"\x0emask_image_url\x18\x01 \x01(\tR\fmaskImageUrl\x12$\n" +
	"\x0emask_image_key\x18\x02 \x01(\tR\fmaskImageKey\x12?\n" +
	"\asticker\x18\x04 \x01(\v2%.api.items.story.types.v1.HideStickerR\asticker\"c\n" +
	"\x1eStickerWithTriggerTypeLikeData\x12A\n" +
	"\bstickers\x18\x01 \x03(\v2%.api.items.story.types.v1.HideStickerR\bstickers\"\xcd\x01\n" +
	"\x1fStickerWithTriggerTypeShakeData\x12A\n" +
	"\bstickers\x18\x01 \x03(\v2%.api.items.story.types.v1.HideStickerR\bstickers\x12(\n" +
	"\x10need_shake_count\x18\x02 \x01(\rR\x0eneedShakeCount\x12=\n" +
	"\x1bneed_shake_duration_seconds\x18\x03 \x01(\rR\x18needShakeDurationSeconds\"\xb0\x01\n" +
	"#StickerWithTriggerTypeLongPressData\x12A\n" +
	"\bstickers\x18\x01 \x03(\v2%.api.items.story.types.v1.HideStickerR\bstickers\x12F\n" +
	" need_long_press_duration_seconds\x18\x02 \x01(\rR\x1cneedLongPressDurationSeconds\"\xe7\x04\n" +
	"\x16StickerWithTriggerType\x12S\n" +
	"\ftrigger_type\x18\x01 \x01(\x0e20.api.items.story.types.v1.HideStickerTriggerTypeR\vtriggerType\x12y\n" +
	"\x15continuous_click_data\x18\x02 \x01(\v2C.api.items.story.types.v1.StickerWithTriggerTypeContinuousClickDataH\x00R\x13continuousClickData\x12W\n" +
	"\tdrag_data\x18\x03 \x01(\v28.api.items.story.types.v1.StickerWithTriggerTypeDragDataH\x00R\bdragData\x12W\n" +
	"\tlike_data\x18\x04 \x01(\v28.api.items.story.types.v1.StickerWithTriggerTypeLikeDataH\x00R\blikeData\x12Z\n" +
	"\n" +
	"shake_data\x18\x05 \x01(\v29.api.items.story.types.v1.StickerWithTriggerTypeShakeDataH\x00R\tshakeData\x12g\n" +
	"\x0flong_press_data\x18\x06 \x01(\v2=.api.items.story.types.v1.StickerWithTriggerTypeLongPressDataH\x00R\rlongPressDataB\x06\n" +
	"\x04data\"\x9b\x04\n" +
	"\x13StoryPlayHideConfig\x12m\n" +
	"\x1asticker_with_trigger_types\x18\x01 \x03(\v20.api.items.story.types.v1.StickerWithTriggerTypeR\x17stickerWithTriggerTypes\x12T\n" +
	"\x10background_image\x18\x02 \x01(\v2\x1f.api.resource.types.v1.ResourceB\b\xfaB\x05\x8a\x01\x02\x10\x01R\x0fbackgroundImage\x12S\n" +
	"\x10attachment_texts\x18\x03 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x0fattachmentTexts\x12H\n" +
	"\fall_stickers\x18\x04 \x03(\v2%.api.items.story.types.v1.HideStickerR\vallStickers\x12Z\n" +
	"\x13moment_create_attrs\x18\x05 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\x12:\n" +
	"\x05cover\x18\x06 \x01(\v2\x1f.api.resource.types.v1.ResourceH\x00R\x05cover\x88\x01\x01B\b\n" +
	"\x06_cover*\xee\x01\n" +
	"\x16HideStickerTriggerType\x12$\n" +
	" STICKER_TRIGGER_TYPE_UNSPECIFIED\x10\x00\x12+\n" +
	"'STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK\x10\x01\x12\x1d\n" +
	"\x19STICKER_TRIGGER_TYPE_DRAG\x10\x02\x12\x1d\n" +
	"\x19STICKER_TRIGGER_TYPE_LIKE\x10\x03\x12\x1e\n" +
	"\x1aSTICKER_TRIGGER_TYPE_SHAKE\x10\x04\x12#\n" +
	"\x1fSTICKER_TRIGGER_TYPE_LONG_PRESS\x10\x05B9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_hide_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_hide_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_hide_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_hide_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_hide_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_hide_types_proto_rawDesc), len(file_api_items_story_types_v1_hide_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_hide_types_proto_rawDescData
}

var file_api_items_story_types_v1_hide_types_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_items_story_types_v1_hide_types_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_items_story_types_v1_hide_types_proto_goTypes = []any{
	(HideStickerTriggerType)(0),                       // 0: api.items.story.types.v1.HideStickerTriggerType
	(*StickerTransform)(nil),                          // 1: api.items.story.types.v1.StickerTransform
	(*HideSticker)(nil),                               // 2: api.items.story.types.v1.HideSticker
	(*StoryPlayHideContext)(nil),                      // 3: api.items.story.types.v1.StoryPlayHideContext
	(*StickerWithTriggerTypeContinuousClickData)(nil), // 4: api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData
	(*StickerWithTriggerTypeDragData)(nil),            // 5: api.items.story.types.v1.StickerWithTriggerTypeDragData
	(*StickerWithTriggerTypeLikeData)(nil),            // 6: api.items.story.types.v1.StickerWithTriggerTypeLikeData
	(*StickerWithTriggerTypeShakeData)(nil),           // 7: api.items.story.types.v1.StickerWithTriggerTypeShakeData
	(*StickerWithTriggerTypeLongPressData)(nil),       // 8: api.items.story.types.v1.StickerWithTriggerTypeLongPressData
	(*StickerWithTriggerType)(nil),                    // 9: api.items.story.types.v1.StickerWithTriggerType
	(*StoryPlayHideConfig)(nil),                       // 10: api.items.story.types.v1.StoryPlayHideConfig
	(*StickerTransform_Location)(nil),                 // 11: api.items.story.types.v1.StickerTransform.Location
	(*StickerTransform_Size)(nil),                     // 12: api.items.story.types.v1.StickerTransform.Size
	(*StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume)(nil), // 13: api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData.StickersWithClickLocationInConsume
	(*StickerWithTriggerTypeDragData_CutObject)(nil),                                     // 14: api.items.story.types.v1.StickerWithTriggerTypeDragData.CutObject
	(*v1.Resource)(nil),      // 15: api.resource.types.v1.Resource
	(*AttachmentText)(nil),   // 16: api.items.story.types.v1.AttachmentText
	(*MomentCreateAttr)(nil), // 17: api.items.story.types.v1.MomentCreateAttr
}
var file_api_items_story_types_v1_hide_types_proto_depIdxs = []int32{
	11, // 0: api.items.story.types.v1.StickerTransform.location:type_name -> api.items.story.types.v1.StickerTransform.Location
	12, // 1: api.items.story.types.v1.StickerTransform.size:type_name -> api.items.story.types.v1.StickerTransform.Size
	15, // 2: api.items.story.types.v1.HideSticker.resource:type_name -> api.resource.types.v1.Resource
	1,  // 3: api.items.story.types.v1.HideSticker.transform:type_name -> api.items.story.types.v1.StickerTransform
	2,  // 4: api.items.story.types.v1.StoryPlayHideContext.unlocked_stickers:type_name -> api.items.story.types.v1.HideSticker
	2,  // 5: api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData.stickers_with_click_locations_in_creation:type_name -> api.items.story.types.v1.HideSticker
	13, // 6: api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData.stickers_with_click_locations_in_consume:type_name -> api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData.StickersWithClickLocationInConsume
	14, // 7: api.items.story.types.v1.StickerWithTriggerTypeDragData.cut_objects:type_name -> api.items.story.types.v1.StickerWithTriggerTypeDragData.CutObject
	2,  // 8: api.items.story.types.v1.StickerWithTriggerTypeLikeData.stickers:type_name -> api.items.story.types.v1.HideSticker
	2,  // 9: api.items.story.types.v1.StickerWithTriggerTypeShakeData.stickers:type_name -> api.items.story.types.v1.HideSticker
	2,  // 10: api.items.story.types.v1.StickerWithTriggerTypeLongPressData.stickers:type_name -> api.items.story.types.v1.HideSticker
	0,  // 11: api.items.story.types.v1.StickerWithTriggerType.trigger_type:type_name -> api.items.story.types.v1.HideStickerTriggerType
	4,  // 12: api.items.story.types.v1.StickerWithTriggerType.continuous_click_data:type_name -> api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData
	5,  // 13: api.items.story.types.v1.StickerWithTriggerType.drag_data:type_name -> api.items.story.types.v1.StickerWithTriggerTypeDragData
	6,  // 14: api.items.story.types.v1.StickerWithTriggerType.like_data:type_name -> api.items.story.types.v1.StickerWithTriggerTypeLikeData
	7,  // 15: api.items.story.types.v1.StickerWithTriggerType.shake_data:type_name -> api.items.story.types.v1.StickerWithTriggerTypeShakeData
	8,  // 16: api.items.story.types.v1.StickerWithTriggerType.long_press_data:type_name -> api.items.story.types.v1.StickerWithTriggerTypeLongPressData
	9,  // 17: api.items.story.types.v1.StoryPlayHideConfig.sticker_with_trigger_types:type_name -> api.items.story.types.v1.StickerWithTriggerType
	15, // 18: api.items.story.types.v1.StoryPlayHideConfig.background_image:type_name -> api.resource.types.v1.Resource
	16, // 19: api.items.story.types.v1.StoryPlayHideConfig.attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	2,  // 20: api.items.story.types.v1.StoryPlayHideConfig.all_stickers:type_name -> api.items.story.types.v1.HideSticker
	17, // 21: api.items.story.types.v1.StoryPlayHideConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	15, // 22: api.items.story.types.v1.StoryPlayHideConfig.cover:type_name -> api.resource.types.v1.Resource
	2,  // 23: api.items.story.types.v1.StickerWithTriggerTypeContinuousClickData.StickersWithClickLocationInConsume.stickers:type_name -> api.items.story.types.v1.HideSticker
	2,  // 24: api.items.story.types.v1.StickerWithTriggerTypeDragData.CutObject.sticker:type_name -> api.items.story.types.v1.HideSticker
	25, // [25:25] is the sub-list for method output_type
	25, // [25:25] is the sub-list for method input_type
	25, // [25:25] is the sub-list for extension type_name
	25, // [25:25] is the sub-list for extension extendee
	0,  // [0:25] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_hide_types_proto_init() }
func file_api_items_story_types_v1_hide_types_proto_init() {
	if File_api_items_story_types_v1_hide_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	file_api_items_story_types_v1_hide_types_proto_msgTypes[8].OneofWrappers = []any{
		(*StickerWithTriggerType_ContinuousClickData)(nil),
		(*StickerWithTriggerType_DragData)(nil),
		(*StickerWithTriggerType_LikeData)(nil),
		(*StickerWithTriggerType_ShakeData)(nil),
		(*StickerWithTriggerType_LongPressData)(nil),
	}
	file_api_items_story_types_v1_hide_types_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_hide_types_proto_rawDesc), len(file_api_items_story_types_v1_hide_types_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_hide_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_hide_types_proto_depIdxs,
		EnumInfos:         file_api_items_story_types_v1_hide_types_proto_enumTypes,
		MessageInfos:      file_api_items_story_types_v1_hide_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_hide_types_proto = out.File
	file_api_items_story_types_v1_hide_types_proto_goTypes = nil
	file_api_items_story_types_v1_hide_types_proto_depIdxs = nil
}
