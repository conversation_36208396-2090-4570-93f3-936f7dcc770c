// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/types/v1/haunt.types.proto

package api_items_story_types_v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on HauntQuestion with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HauntQuestion) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HauntQuestion with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HauntQuestionMultiError, or
// nil if none found.
func (m *HauntQuestion) ValidateAll() error {
	return m.validate(true)
}

func (m *HauntQuestion) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Description

	// no validation rules for IsRequired

	if len(errors) > 0 {
		return HauntQuestionMultiError(errors)
	}

	return nil
}

// HauntQuestionMultiError is an error wrapping multiple validation errors
// returned by HauntQuestion.ValidateAll() if the designated constraints
// aren't met.
type HauntQuestionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HauntQuestionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HauntQuestionMultiError) AllErrors() []error { return m }

// HauntQuestionValidationError is the validation error returned by
// HauntQuestion.Validate if the designated constraints aren't met.
type HauntQuestionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HauntQuestionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HauntQuestionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HauntQuestionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HauntQuestionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HauntQuestionValidationError) ErrorName() string { return "HauntQuestionValidationError" }

// Error satisfies the builtin error interface
func (e HauntQuestionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHauntQuestion.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HauntQuestionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HauntQuestionValidationError{}

// Validate checks the field values on HauntQuestionWithAnswer with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HauntQuestionWithAnswer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HauntQuestionWithAnswer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HauntQuestionWithAnswerMultiError, or nil if none found.
func (m *HauntQuestionWithAnswer) ValidateAll() error {
	return m.validate(true)
}

func (m *HauntQuestionWithAnswer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQuestion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HauntQuestionWithAnswerValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HauntQuestionWithAnswerValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HauntQuestionWithAnswerValidationError{
				field:  "Question",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Answer

	if len(errors) > 0 {
		return HauntQuestionWithAnswerMultiError(errors)
	}

	return nil
}

// HauntQuestionWithAnswerMultiError is an error wrapping multiple validation
// errors returned by HauntQuestionWithAnswer.ValidateAll() if the designated
// constraints aren't met.
type HauntQuestionWithAnswerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HauntQuestionWithAnswerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HauntQuestionWithAnswerMultiError) AllErrors() []error { return m }

// HauntQuestionWithAnswerValidationError is the validation error returned by
// HauntQuestionWithAnswer.Validate if the designated constraints aren't met.
type HauntQuestionWithAnswerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HauntQuestionWithAnswerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HauntQuestionWithAnswerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HauntQuestionWithAnswerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HauntQuestionWithAnswerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HauntQuestionWithAnswerValidationError) ErrorName() string {
	return "HauntQuestionWithAnswerValidationError"
}

// Error satisfies the builtin error interface
func (e HauntQuestionWithAnswerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHauntQuestionWithAnswer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HauntQuestionWithAnswerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HauntQuestionWithAnswerValidationError{}

// Validate checks the field values on HauntBoo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HauntBoo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HauntBoo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HauntBooMultiError, or nil
// if none found.
func (m *HauntBoo) ValidateAll() error {
	return m.validate(true)
}

func (m *HauntBoo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetAvatar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HauntBooValidationError{
					field:  "Avatar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HauntBooValidationError{
					field:  "Avatar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvatar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HauntBooValidationError{
				field:  "Avatar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HauntBooValidationError{
					field:  "Creator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HauntBooValidationError{
					field:  "Creator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HauntBooValidationError{
				field:  "Creator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GeneratedHint

	// no validation rules for VideoUrl

	// no validation rules for CreatorBoo

	for idx, item := range m.GetQuestionsWithAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HauntBooValidationError{
						field:  fmt.Sprintf("QuestionsWithAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HauntBooValidationError{
						field:  fmt.Sprintf("QuestionsWithAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HauntBooValidationError{
					field:  fmt.Sprintf("QuestionsWithAnswers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HauntBooMultiError(errors)
	}

	return nil
}

// HauntBooMultiError is an error wrapping multiple validation errors returned
// by HauntBoo.ValidateAll() if the designated constraints aren't met.
type HauntBooMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HauntBooMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HauntBooMultiError) AllErrors() []error { return m }

// HauntBooValidationError is the validation error returned by
// HauntBoo.Validate if the designated constraints aren't met.
type HauntBooValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HauntBooValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HauntBooValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HauntBooValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HauntBooValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HauntBooValidationError) ErrorName() string { return "HauntBooValidationError" }

// Error satisfies the builtin error interface
func (e HauntBooValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHauntBoo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HauntBooValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HauntBooValidationError{}

// Validate checks the field values on HauntPlayConfig with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HauntPlayConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HauntPlayConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HauntPlayConfigMultiError, or nil if none found.
func (m *HauntPlayConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *HauntPlayConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBoosWithQuestionsAndAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HauntPlayConfigValidationError{
						field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HauntPlayConfigValidationError{
						field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HauntPlayConfigValidationError{
					field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCaptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HauntPlayConfigValidationError{
						field:  fmt.Sprintf("Captions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HauntPlayConfigValidationError{
						field:  fmt.Sprintf("Captions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HauntPlayConfigValidationError{
					field:  fmt.Sprintf("Captions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return HauntPlayConfigMultiError(errors)
	}

	return nil
}

// HauntPlayConfigMultiError is an error wrapping multiple validation errors
// returned by HauntPlayConfig.ValidateAll() if the designated constraints
// aren't met.
type HauntPlayConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HauntPlayConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HauntPlayConfigMultiError) AllErrors() []error { return m }

// HauntPlayConfigValidationError is the validation error returned by
// HauntPlayConfig.Validate if the designated constraints aren't met.
type HauntPlayConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HauntPlayConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HauntPlayConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HauntPlayConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HauntPlayConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HauntPlayConfigValidationError) ErrorName() string { return "HauntPlayConfigValidationError" }

// Error satisfies the builtin error interface
func (e HauntPlayConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHauntPlayConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HauntPlayConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HauntPlayConfigValidationError{}

// Validate checks the field values on HauntPlayContext with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HauntPlayContext) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HauntPlayContext with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HauntPlayContextMultiError, or nil if none found.
func (m *HauntPlayContext) ValidateAll() error {
	return m.validate(true)
}

func (m *HauntPlayContext) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TryCount

	// no validation rules for Unlocked

	if len(errors) > 0 {
		return HauntPlayContextMultiError(errors)
	}

	return nil
}

// HauntPlayContextMultiError is an error wrapping multiple validation errors
// returned by HauntPlayContext.ValidateAll() if the designated constraints
// aren't met.
type HauntPlayContextMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HauntPlayContextMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HauntPlayContextMultiError) AllErrors() []error { return m }

// HauntPlayContextValidationError is the validation error returned by
// HauntPlayContext.Validate if the designated constraints aren't met.
type HauntPlayContextValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HauntPlayContextValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HauntPlayContextValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HauntPlayContextValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HauntPlayContextValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HauntPlayContextValidationError) ErrorName() string { return "HauntPlayContextValidationError" }

// Error satisfies the builtin error interface
func (e HauntPlayContextValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHauntPlayContext.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HauntPlayContextValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HauntPlayContextValidationError{}

// Validate checks the field values on HauntBooShowInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *HauntBooShowInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HauntBooShowInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HauntBooShowInfoMultiError, or nil if none found.
func (m *HauntBooShowInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *HauntBooShowInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetHauntBoos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, HauntBooShowInfoValidationError{
						field:  fmt.Sprintf("HauntBoos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, HauntBooShowInfoValidationError{
						field:  fmt.Sprintf("HauntBoos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return HauntBooShowInfoValidationError{
					field:  fmt.Sprintf("HauntBoos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FromHauntStoryId

	// no validation rules for ShowInfoSence

	// no validation rules for LastShowAtInUnixstamp

	// no validation rules for TodayShowCount

	// no validation rules for TriedCount

	if len(errors) > 0 {
		return HauntBooShowInfoMultiError(errors)
	}

	return nil
}

// HauntBooShowInfoMultiError is an error wrapping multiple validation errors
// returned by HauntBooShowInfo.ValidateAll() if the designated constraints
// aren't met.
type HauntBooShowInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HauntBooShowInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HauntBooShowInfoMultiError) AllErrors() []error { return m }

// HauntBooShowInfoValidationError is the validation error returned by
// HauntBooShowInfo.Validate if the designated constraints aren't met.
type HauntBooShowInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HauntBooShowInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HauntBooShowInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HauntBooShowInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HauntBooShowInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HauntBooShowInfoValidationError) ErrorName() string { return "HauntBooShowInfoValidationError" }

// Error satisfies the builtin error interface
func (e HauntBooShowInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHauntBooShowInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HauntBooShowInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HauntBooShowInfoValidationError{}
