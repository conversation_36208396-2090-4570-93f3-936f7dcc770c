// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/chatproxy.types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/resource/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChatProxyQuestion struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Question      string                 `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	TtsAudioUrl   string                 `protobuf:"bytes,2,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	Words         []*Word                `protobuf:"bytes,3,rep,name=words,proto3" json:"words,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatProxyQuestion) Reset() {
	*x = ChatProxyQuestion{}
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatProxyQuestion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatProxyQuestion) ProtoMessage() {}

func (x *ChatProxyQuestion) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatProxyQuestion.ProtoReflect.Descriptor instead.
func (*ChatProxyQuestion) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP(), []int{0}
}

func (x *ChatProxyQuestion) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *ChatProxyQuestion) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *ChatProxyQuestion) GetWords() []*Word {
	if x != nil {
		return x.Words
	}
	return nil
}

// hide 玩法上下文
type StoryPlayChatProxyContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 是否解锁过此 story
	IsUnlocked    bool `protobuf:"varint,1,opt,name=is_unlocked,json=isUnlocked,proto3" json:"is_unlocked,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayChatProxyContext) Reset() {
	*x = StoryPlayChatProxyContext{}
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayChatProxyContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayChatProxyContext) ProtoMessage() {}

func (x *StoryPlayChatProxyContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayChatProxyContext.ProtoReflect.Descriptor instead.
func (*StoryPlayChatProxyContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP(), []int{1}
}

func (x *StoryPlayChatProxyContext) GetIsUnlocked() bool {
	if x != nil {
		return x.IsUnlocked
	}
	return false
}

type StoryPlayChatProxyTopic struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Content       string                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayChatProxyTopic) Reset() {
	*x = StoryPlayChatProxyTopic{}
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayChatProxyTopic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayChatProxyTopic) ProtoMessage() {}

func (x *StoryPlayChatProxyTopic) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayChatProxyTopic.ProtoReflect.Descriptor instead.
func (*StoryPlayChatProxyTopic) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayChatProxyTopic) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StoryPlayChatProxyTopic) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

type StoryPlayChatProxyCaption struct {
	state      protoimpl.MessageState `protogen:"open.v1"`
	Content    string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Descrption string                 `protobuf:"bytes,2,opt,name=descrption,proto3" json:"descrption,omitempty"`
	// 客户端创建时不传，服务端自动根据 content 进行生成
	TtsAudioUrl string `protobuf:"bytes,3,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	// 客户端创建时不传，服务端自动根据 content 进行生成
	Words         []*Word `protobuf:"bytes,4,rep,name=words,proto3" json:"words,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayChatProxyCaption) Reset() {
	*x = StoryPlayChatProxyCaption{}
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayChatProxyCaption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayChatProxyCaption) ProtoMessage() {}

func (x *StoryPlayChatProxyCaption) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayChatProxyCaption.ProtoReflect.Descriptor instead.
func (*StoryPlayChatProxyCaption) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP(), []int{3}
}

func (x *StoryPlayChatProxyCaption) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *StoryPlayChatProxyCaption) GetDescrption() string {
	if x != nil {
		return x.Descrption
	}
	return ""
}

func (x *StoryPlayChatProxyCaption) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *StoryPlayChatProxyCaption) GetWords() []*Word {
	if x != nil {
		return x.Words
	}
	return nil
}

type StoryPlayChatProxyGreeting struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Content string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 客户端创建时不传，服务端自动根据 content 进行生成
	TtsAudioUrl string `protobuf:"bytes,3,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	// 客户端创建时不传，服务端自动根据 content 进行生成
	Words         []*Word `protobuf:"bytes,4,rep,name=words,proto3" json:"words,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayChatProxyGreeting) Reset() {
	*x = StoryPlayChatProxyGreeting{}
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayChatProxyGreeting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayChatProxyGreeting) ProtoMessage() {}

func (x *StoryPlayChatProxyGreeting) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayChatProxyGreeting.ProtoReflect.Descriptor instead.
func (*StoryPlayChatProxyGreeting) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP(), []int{4}
}

func (x *StoryPlayChatProxyGreeting) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *StoryPlayChatProxyGreeting) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *StoryPlayChatProxyGreeting) GetWords() []*Word {
	if x != nil {
		return x.Words
	}
	return nil
}

// chatproxy 玩法配置
type StoryPlayChatProxyConfig struct {
	state   protoimpl.MessageState     `protogen:"open.v1"`
	Caption *StoryPlayChatProxyCaption `protobuf:"bytes,1,opt,name=caption,proto3" json:"caption,omitempty"`
	// greeting 客户端不传，服务端自动生成
	Greeting                      *StoryPlayChatProxyGreeting `protobuf:"bytes,2,opt,name=greeting,proto3" json:"greeting,omitempty"`
	Topics                        []*StoryPlayChatProxyTopic  `protobuf:"bytes,3,rep,name=topics,proto3" json:"topics,omitempty"`
	Cover                         *v1.Resource                `protobuf:"bytes,7,opt,name=cover,proto3" json:"cover,omitempty"`
	CoverAttachmentTexts          []*AttachmentText           `protobuf:"bytes,9,rep,name=cover_attachment_texts,json=coverAttachmentTexts,proto3" json:"cover_attachment_texts,omitempty"`
	UnlockResource                *v1.Resource                `protobuf:"bytes,8,opt,name=unlock_resource,json=unlockResource,proto3" json:"unlock_resource,omitempty"`
	UnlockResourceAttachmentTexts []*AttachmentText           `protobuf:"bytes,10,rep,name=unlock_resource_attachment_texts,json=unlockResourceAttachmentTexts,proto3" json:"unlock_resource_attachment_texts,omitempty"`
	MomentCreateAttrs             []*MomentCreateAttr         `protobuf:"bytes,11,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields                 protoimpl.UnknownFields
	sizeCache                     protoimpl.SizeCache
}

func (x *StoryPlayChatProxyConfig) Reset() {
	*x = StoryPlayChatProxyConfig{}
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayChatProxyConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayChatProxyConfig) ProtoMessage() {}

func (x *StoryPlayChatProxyConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_chatproxy_types_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayChatProxyConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayChatProxyConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP(), []int{5}
}

func (x *StoryPlayChatProxyConfig) GetCaption() *StoryPlayChatProxyCaption {
	if x != nil {
		return x.Caption
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetGreeting() *StoryPlayChatProxyGreeting {
	if x != nil {
		return x.Greeting
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetTopics() []*StoryPlayChatProxyTopic {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetCover() *v1.Resource {
	if x != nil {
		return x.Cover
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetCoverAttachmentTexts() []*AttachmentText {
	if x != nil {
		return x.CoverAttachmentTexts
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetUnlockResource() *v1.Resource {
	if x != nil {
		return x.UnlockResource
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetUnlockResourceAttachmentTexts() []*AttachmentText {
	if x != nil {
		return x.UnlockResourceAttachmentTexts
	}
	return nil
}

func (x *StoryPlayChatProxyConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

var File_api_items_story_types_v1_chatproxy_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_chatproxy_types_proto_rawDesc = "" +
	"\n" +
	".api/items/story/types/v1/chatproxy.types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\x1a!api/resource/types/v1/types.proto\x1a\x17validate/validate.proto\"\x89\x01\n" +
	"\x11ChatProxyQuestion\x12\x1a\n" +
	"\bquestion\x18\x01 \x01(\tR\bquestion\x12\"\n" +
	"\rtts_audio_url\x18\x02 \x01(\tR\vttsAudioUrl\x124\n" +
	"\x05words\x18\x03 \x03(\v2\x1e.api.items.story.types.v1.WordR\x05words\"<\n" +
	"\x19StoryPlayChatProxyContext\x12\x1f\n" +
	"\vis_unlocked\x18\x01 \x01(\bR\n" +
	"isUnlocked\"C\n" +
	"\x17StoryPlayChatProxyTopic\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x18\n" +
	"\acontent\x18\x02 \x01(\tR\acontent\"\xaf\x01\n" +
	"\x19StoryPlayChatProxyCaption\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\x1e\n" +
	"\n" +
	"descrption\x18\x02 \x01(\tR\n" +
	"descrption\x12\"\n" +
	"\rtts_audio_url\x18\x03 \x01(\tR\vttsAudioUrl\x124\n" +
	"\x05words\x18\x04 \x03(\v2\x1e.api.items.story.types.v1.WordR\x05words\"\x90\x01\n" +
	"\x1aStoryPlayChatProxyGreeting\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\"\n" +
	"\rtts_audio_url\x18\x03 \x01(\tR\vttsAudioUrl\x124\n" +
	"\x05words\x18\x04 \x03(\v2\x1e.api.items.story.types.v1.WordR\x05words\"\xc0\x05\n" +
	"\x18StoryPlayChatProxyConfig\x12W\n" +
	"\acaption\x18\x01 \x01(\v23.api.items.story.types.v1.StoryPlayChatProxyCaptionB\b\xfaB\x05\x8a\x01\x02\x10\x01R\acaption\x12P\n" +
	"\bgreeting\x18\x02 \x01(\v24.api.items.story.types.v1.StoryPlayChatProxyGreetingR\bgreeting\x12I\n" +
	"\x06topics\x18\x03 \x03(\v21.api.items.story.types.v1.StoryPlayChatProxyTopicR\x06topics\x125\n" +
	"\x05cover\x18\a \x01(\v2\x1f.api.resource.types.v1.ResourceR\x05cover\x12^\n" +
	"\x16cover_attachment_texts\x18\t \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x14coverAttachmentTexts\x12H\n" +
	"\x0funlock_resource\x18\b \x01(\v2\x1f.api.resource.types.v1.ResourceR\x0eunlockResource\x12q\n" +
	" unlock_resource_attachment_texts\x18\n" +
	" \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x1dunlockResourceAttachmentTexts\x12Z\n" +
	"\x13moment_create_attrs\x18\v \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrsB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_chatproxy_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_chatproxy_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_chatproxy_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_chatproxy_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_chatproxy_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_chatproxy_types_proto_rawDesc), len(file_api_items_story_types_v1_chatproxy_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_chatproxy_types_proto_rawDescData
}

var file_api_items_story_types_v1_chatproxy_types_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_items_story_types_v1_chatproxy_types_proto_goTypes = []any{
	(*ChatProxyQuestion)(nil),          // 0: api.items.story.types.v1.ChatProxyQuestion
	(*StoryPlayChatProxyContext)(nil),  // 1: api.items.story.types.v1.StoryPlayChatProxyContext
	(*StoryPlayChatProxyTopic)(nil),    // 2: api.items.story.types.v1.StoryPlayChatProxyTopic
	(*StoryPlayChatProxyCaption)(nil),  // 3: api.items.story.types.v1.StoryPlayChatProxyCaption
	(*StoryPlayChatProxyGreeting)(nil), // 4: api.items.story.types.v1.StoryPlayChatProxyGreeting
	(*StoryPlayChatProxyConfig)(nil),   // 5: api.items.story.types.v1.StoryPlayChatProxyConfig
	(*Word)(nil),                       // 6: api.items.story.types.v1.Word
	(*v1.Resource)(nil),                // 7: api.resource.types.v1.Resource
	(*AttachmentText)(nil),             // 8: api.items.story.types.v1.AttachmentText
	(*MomentCreateAttr)(nil),           // 9: api.items.story.types.v1.MomentCreateAttr
}
var file_api_items_story_types_v1_chatproxy_types_proto_depIdxs = []int32{
	6,  // 0: api.items.story.types.v1.ChatProxyQuestion.words:type_name -> api.items.story.types.v1.Word
	6,  // 1: api.items.story.types.v1.StoryPlayChatProxyCaption.words:type_name -> api.items.story.types.v1.Word
	6,  // 2: api.items.story.types.v1.StoryPlayChatProxyGreeting.words:type_name -> api.items.story.types.v1.Word
	3,  // 3: api.items.story.types.v1.StoryPlayChatProxyConfig.caption:type_name -> api.items.story.types.v1.StoryPlayChatProxyCaption
	4,  // 4: api.items.story.types.v1.StoryPlayChatProxyConfig.greeting:type_name -> api.items.story.types.v1.StoryPlayChatProxyGreeting
	2,  // 5: api.items.story.types.v1.StoryPlayChatProxyConfig.topics:type_name -> api.items.story.types.v1.StoryPlayChatProxyTopic
	7,  // 6: api.items.story.types.v1.StoryPlayChatProxyConfig.cover:type_name -> api.resource.types.v1.Resource
	8,  // 7: api.items.story.types.v1.StoryPlayChatProxyConfig.cover_attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	7,  // 8: api.items.story.types.v1.StoryPlayChatProxyConfig.unlock_resource:type_name -> api.resource.types.v1.Resource
	8,  // 9: api.items.story.types.v1.StoryPlayChatProxyConfig.unlock_resource_attachment_texts:type_name -> api.items.story.types.v1.AttachmentText
	9,  // 10: api.items.story.types.v1.StoryPlayChatProxyConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_chatproxy_types_proto_init() }
func file_api_items_story_types_v1_chatproxy_types_proto_init() {
	if File_api_items_story_types_v1_chatproxy_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_chatproxy_types_proto_rawDesc), len(file_api_items_story_types_v1_chatproxy_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_chatproxy_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_chatproxy_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_chatproxy_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_chatproxy_types_proto = out.File
	file_api_items_story_types_v1_chatproxy_types_proto_goTypes = nil
	file_api_items_story_types_v1_chatproxy_types_proto_depIdxs = nil
}
