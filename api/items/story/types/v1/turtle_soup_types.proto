syntax = "proto3";

package api.items.story.types.v1;

option go_package = "boson/api/items/story/types/v1;api_items_story_types_v1";
import "api/items/story/types/v1/base_types.proto";

enum TurtleSoupMatchStatus {
	TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED = 0;
	// 未 match
	TURTLE_SOUP_MATCH_STATUS_NOT_MATCHED = 1;
	// 部分 match
	TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED = 2;
	// 完全 match
	TURTLE_SOUP_MATCH_STATUS_MATCHED = 3;
}
// 海龟汤玩法上下文
message StoryPlayTurtleSoupContext {
	// 是否已经完成
	bool is_finished = 1;
	// 命中的 words，当且仅当 is_finished 为 false 时，此字段有效
	repeated string hit_words = 2;
	// 显示在屏幕中间的 tips
	string tips = 3;
	// ai 的回复
	string ai_response = 4;
	// 尝试次数
	uint32 try_count = 5;
}
// 海龟汤玩法配置
message StoryPlayTurtleSoupConfig {
	// 海龟汤玩法里的节点
	string caption = 1;
	string intent_prompt = 2;
	enum ResourceType {
		RESOURCE_TYPE_UNSPECIFIED = 0;
		RESOURCE_TYPE_IMAGE = 1;
		RESOURCE_TYPE_VIDEO = 2;
	}
	// 资源类型，参考 ResourceType
	string resource_type = 4;
	// 资源 url，创建时，客户端传 key
	string resource_url = 5;
	// 结束资源 url，创建时，客户端传 key
	string end_resource_url = 6;
	// 结束资源类型，参考 ResourceType
	string end_resource_type = 7;
	// 结束资源显示的文字
	string end_message = 8;
	// 结束资源显示的文字字体
	string end_message_font = 9;
	// 如果资源是视频时，需要传一个视频首帧，创作时给 key
	optional string thumbnail_url = 10;
	// 如果结束资源是是视频时，需要传一个视频首帧，创作时给 key
	optional string end_thumbnail_url = 11;
	// 自定义 ai 规则
	message CustomAiResponse {
		string rule_description = 1;
		string rule_result = 2;
	}
	repeated CustomAiResponse custom_ai_responses = 12;
	// 模板 id
	optional string template_id = 13;
	// 是否需要创建自定义模板
	bool need_create_diy_template = 14;
	// 最大尝试次数
	uint32 max_try_count = 15;
	CommonPlayConfig common_play_config = 16;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 17;
}

// 海龟汤玩法示例
message StoryPlayTurtleSoupExample {
	ExampleCommonInfo common_info = 1;
	message Case {
		string tips = 1;
		// 用户发送的消息示例
		string user_message= 2;
		// 示例的命中词
		repeated string hit_words = 3;
		// ai 的回复
		string ai_response = 4;
	}
	// 隐藏意图，用于帮助 hit_words 命中
	string end_message = 2;
	// 隐藏意图字体
	string end_message_font = 3;
	// 用户发送的消息示例
	repeated Case cases = 4;
}


message StoryPlayTurtleSoupMassExample {
	ExampleCommonInfo common_info = 1;
	message Case {
		string tips = 1;
		string user_message= 2;
		string ai_response = 3;
	}
	repeated Case cases = 2;
}

message StoryPlayTurtleSoupMassContext {
	// 是否已经完成
    bool is_finished = 1;
    // 显示在屏幕中间的 tips
    string tips = 2;
    // ai 的回复
    string ai_response = 3;
    // 尝试次数
    uint32 try_count = 4;
}

// 与海龟汤类似，只是没有 end_resource_url 和 end_resource_type，为了不混淆旧的数据结构，新开了类型
message StoryPlayTurtleSoupMassConfig {
    // 海龟汤玩法里的节点
	string caption = 1;
	string intent_prompt = 2;
	enum ResourceType {
		RESOURCE_TYPE_UNSPECIFIED = 0;
		RESOURCE_TYPE_IMAGE = 1;
		RESOURCE_TYPE_VIDEO = 2;
	}
	// 资源类型，参考 ResourceType
	string resource_type = 4;
	// 资源 url，创建时，客户端传 key
	string resource_url = 5;
	// 马赛克玩法的海龟汤，只有一个资源
	// 如果资源是视频时，需要传一个视频首帧，创作时给 key
	optional string thumbnail_url = 8;
	// 自定义 ai 规则
	message CustomAiResponse {
		string rule_description = 1;
		string rule_result = 2;
	}
	repeated CustomAiResponse custom_ai_responses = 12;
	// 模板 id
	optional string template_id = 13;
	// 是否需要创建自定义模板
	bool need_create_diy_template = 14;
	// 最大尝试次数
	uint32 max_try_count = 15;
}