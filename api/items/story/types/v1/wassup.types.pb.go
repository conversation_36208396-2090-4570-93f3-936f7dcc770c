// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/types/v1/wassup.types.proto

package api_items_story_types_v1

import (
	v1 "boson/api/resource/types/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StoryPlayWassupConfig struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	UnlockResource     *v1.Resource           `protobuf:"bytes,1,opt,name=unlock_resource,json=unlockResource,proto3" json:"unlock_resource,omitempty"`
	CoverImageTexts    []*AttachmentText      `protobuf:"bytes,2,rep,name=cover_image_texts,json=coverImageTexts,proto3" json:"cover_image_texts,omitempty"`
	CoverImageResource *v1.Resource           `protobuf:"bytes,3,opt,name=cover_image_resource,json=coverImageResource,proto3" json:"cover_image_resource,omitempty"`
	IsMassCover        bool                   `protobuf:"varint,4,opt,name=is_mass_cover,json=isMassCover,proto3" json:"is_mass_cover,omitempty"`
	MomentCreateAttrs  []*MomentCreateAttr    `protobuf:"bytes,5,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *StoryPlayWassupConfig) Reset() {
	*x = StoryPlayWassupConfig{}
	mi := &file_api_items_story_types_v1_wassup_types_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayWassupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayWassupConfig) ProtoMessage() {}

func (x *StoryPlayWassupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_wassup_types_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayWassupConfig.ProtoReflect.Descriptor instead.
func (*StoryPlayWassupConfig) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_wassup_types_proto_rawDescGZIP(), []int{0}
}

func (x *StoryPlayWassupConfig) GetUnlockResource() *v1.Resource {
	if x != nil {
		return x.UnlockResource
	}
	return nil
}

func (x *StoryPlayWassupConfig) GetCoverImageTexts() []*AttachmentText {
	if x != nil {
		return x.CoverImageTexts
	}
	return nil
}

func (x *StoryPlayWassupConfig) GetCoverImageResource() *v1.Resource {
	if x != nil {
		return x.CoverImageResource
	}
	return nil
}

func (x *StoryPlayWassupConfig) GetIsMassCover() bool {
	if x != nil {
		return x.IsMassCover
	}
	return false
}

func (x *StoryPlayWassupConfig) GetMomentCreateAttrs() []*MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

type StoryPlayWassupContext struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsUnlocked    bool                   `protobuf:"varint,1,opt,name=is_unlocked,json=isUnlocked,proto3" json:"is_unlocked,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayWassupContext) Reset() {
	*x = StoryPlayWassupContext{}
	mi := &file_api_items_story_types_v1_wassup_types_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayWassupContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayWassupContext) ProtoMessage() {}

func (x *StoryPlayWassupContext) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_wassup_types_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayWassupContext.ProtoReflect.Descriptor instead.
func (*StoryPlayWassupContext) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_wassup_types_proto_rawDescGZIP(), []int{1}
}

func (x *StoryPlayWassupContext) GetIsUnlocked() bool {
	if x != nil {
		return x.IsUnlocked
	}
	return false
}

type StoryPlayWassupBotMessage struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	Content string                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	// 客户端创建时不传，服务端自动根据 content 进行生成
	TtsAudioUrl string `protobuf:"bytes,3,opt,name=tts_audio_url,json=ttsAudioUrl,proto3" json:"tts_audio_url,omitempty"`
	// 客户端创建时不传，服务端自动根据 content 进行生成
	Words         []*Word `protobuf:"bytes,4,rep,name=words,proto3" json:"words,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryPlayWassupBotMessage) Reset() {
	*x = StoryPlayWassupBotMessage{}
	mi := &file_api_items_story_types_v1_wassup_types_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryPlayWassupBotMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryPlayWassupBotMessage) ProtoMessage() {}

func (x *StoryPlayWassupBotMessage) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_types_v1_wassup_types_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryPlayWassupBotMessage.ProtoReflect.Descriptor instead.
func (*StoryPlayWassupBotMessage) Descriptor() ([]byte, []int) {
	return file_api_items_story_types_v1_wassup_types_proto_rawDescGZIP(), []int{2}
}

func (x *StoryPlayWassupBotMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *StoryPlayWassupBotMessage) GetTtsAudioUrl() string {
	if x != nil {
		return x.TtsAudioUrl
	}
	return ""
}

func (x *StoryPlayWassupBotMessage) GetWords() []*Word {
	if x != nil {
		return x.Words
	}
	return nil
}

var File_api_items_story_types_v1_wassup_types_proto protoreflect.FileDescriptor

const file_api_items_story_types_v1_wassup_types_proto_rawDesc = "" +
	"\n" +
	"+api/items/story/types/v1/wassup.types.proto\x12\x18api.items.story.types.v1\x1a)api/items/story/types/v1/base_types.proto\x1a!api/resource/types/v1/types.proto\"\x8a\x03\n" +
	"\x15StoryPlayWassupConfig\x12H\n" +
	"\x0funlock_resource\x18\x01 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x0eunlockResource\x12T\n" +
	"\x11cover_image_texts\x18\x02 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\x0fcoverImageTexts\x12Q\n" +
	"\x14cover_image_resource\x18\x03 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x12coverImageResource\x12\"\n" +
	"\ris_mass_cover\x18\x04 \x01(\bR\visMassCover\x12Z\n" +
	"\x13moment_create_attrs\x18\x05 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\"9\n" +
	"\x16StoryPlayWassupContext\x12\x1f\n" +
	"\vis_unlocked\x18\x01 \x01(\bR\n" +
	"isUnlocked\"\x8f\x01\n" +
	"\x19StoryPlayWassupBotMessage\x12\x18\n" +
	"\acontent\x18\x01 \x01(\tR\acontent\x12\"\n" +
	"\rtts_audio_url\x18\x03 \x01(\tR\vttsAudioUrl\x124\n" +
	"\x05words\x18\x04 \x03(\v2\x1e.api.items.story.types.v1.WordR\x05wordsB9Z7boson/api/items/story/types/v1;api_items_story_types_v1b\x06proto3"

var (
	file_api_items_story_types_v1_wassup_types_proto_rawDescOnce sync.Once
	file_api_items_story_types_v1_wassup_types_proto_rawDescData []byte
)

func file_api_items_story_types_v1_wassup_types_proto_rawDescGZIP() []byte {
	file_api_items_story_types_v1_wassup_types_proto_rawDescOnce.Do(func() {
		file_api_items_story_types_v1_wassup_types_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_wassup_types_proto_rawDesc), len(file_api_items_story_types_v1_wassup_types_proto_rawDesc)))
	})
	return file_api_items_story_types_v1_wassup_types_proto_rawDescData
}

var file_api_items_story_types_v1_wassup_types_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_items_story_types_v1_wassup_types_proto_goTypes = []any{
	(*StoryPlayWassupConfig)(nil),     // 0: api.items.story.types.v1.StoryPlayWassupConfig
	(*StoryPlayWassupContext)(nil),    // 1: api.items.story.types.v1.StoryPlayWassupContext
	(*StoryPlayWassupBotMessage)(nil), // 2: api.items.story.types.v1.StoryPlayWassupBotMessage
	(*v1.Resource)(nil),               // 3: api.resource.types.v1.Resource
	(*AttachmentText)(nil),            // 4: api.items.story.types.v1.AttachmentText
	(*MomentCreateAttr)(nil),          // 5: api.items.story.types.v1.MomentCreateAttr
	(*Word)(nil),                      // 6: api.items.story.types.v1.Word
}
var file_api_items_story_types_v1_wassup_types_proto_depIdxs = []int32{
	3, // 0: api.items.story.types.v1.StoryPlayWassupConfig.unlock_resource:type_name -> api.resource.types.v1.Resource
	4, // 1: api.items.story.types.v1.StoryPlayWassupConfig.cover_image_texts:type_name -> api.items.story.types.v1.AttachmentText
	3, // 2: api.items.story.types.v1.StoryPlayWassupConfig.cover_image_resource:type_name -> api.resource.types.v1.Resource
	5, // 3: api.items.story.types.v1.StoryPlayWassupConfig.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	6, // 4: api.items.story.types.v1.StoryPlayWassupBotMessage.words:type_name -> api.items.story.types.v1.Word
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_items_story_types_v1_wassup_types_proto_init() }
func file_api_items_story_types_v1_wassup_types_proto_init() {
	if File_api_items_story_types_v1_wassup_types_proto != nil {
		return
	}
	file_api_items_story_types_v1_base_types_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_types_v1_wassup_types_proto_rawDesc), len(file_api_items_story_types_v1_wassup_types_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_types_v1_wassup_types_proto_goTypes,
		DependencyIndexes: file_api_items_story_types_v1_wassup_types_proto_depIdxs,
		MessageInfos:      file_api_items_story_types_v1_wassup_types_proto_msgTypes,
	}.Build()
	File_api_items_story_types_v1_wassup_types_proto = out.File
	file_api_items_story_types_v1_wassup_types_proto_goTypes = nil
	file_api_items_story_types_v1_wassup_types_proto_depIdxs = nil
}
