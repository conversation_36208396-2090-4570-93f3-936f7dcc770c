// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v2/chatproxy.proto

package api_items_story_v2

import (
	v1 "boson/api/items/story/types/v1"
	v11 "boson/api/items/story/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetChatProxyNextTopicRequestV2 struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	StoryId      string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UserAudioKey string                 `protobuf:"bytes,2,opt,name=user_audio_key,json=userAudioKey,proto3" json:"user_audio_key,omitempty"`
	// 如果是刚开始，则传0
	RoundIndex    uint32 `protobuf:"varint,3,opt,name=round_index,json=roundIndex,proto3" json:"round_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatProxyNextTopicRequestV2) Reset() {
	*x = GetChatProxyNextTopicRequestV2{}
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatProxyNextTopicRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatProxyNextTopicRequestV2) ProtoMessage() {}

func (x *GetChatProxyNextTopicRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatProxyNextTopicRequestV2.ProtoReflect.Descriptor instead.
func (*GetChatProxyNextTopicRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_chatproxy_proto_rawDescGZIP(), []int{0}
}

func (x *GetChatProxyNextTopicRequestV2) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *GetChatProxyNextTopicRequestV2) GetUserAudioKey() string {
	if x != nil {
		return x.UserAudioKey
	}
	return ""
}

func (x *GetChatProxyNextTopicRequestV2) GetRoundIndex() uint32 {
	if x != nil {
		return x.RoundIndex
	}
	return 0
}

type GetChatProxyNextTopicResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Question      *v1.ChatProxyQuestion  `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	End           bool                   `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,3,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChatProxyNextTopicResponseV2) Reset() {
	*x = GetChatProxyNextTopicResponseV2{}
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChatProxyNextTopicResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatProxyNextTopicResponseV2) ProtoMessage() {}

func (x *GetChatProxyNextTopicResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatProxyNextTopicResponseV2.ProtoReflect.Descriptor instead.
func (*GetChatProxyNextTopicResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_chatproxy_proto_rawDescGZIP(), []int{1}
}

func (x *GetChatProxyNextTopicResponseV2) GetQuestion() *v1.ChatProxyQuestion {
	if x != nil {
		return x.Question
	}
	return nil
}

func (x *GetChatProxyNextTopicResponseV2) GetEnd() bool {
	if x != nil {
		return x.End
	}
	return false
}

func (x *GetChatProxyNextTopicResponseV2) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumeChatProxyRequestV2 struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	StoryId           string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UserVideoKey      string                 `protobuf:"bytes,2,opt,name=user_video_key,json=userVideoKey,proto3" json:"user_video_key,omitempty"`
	UserVideoCoverKey string                 `protobuf:"bytes,3,opt,name=user_video_cover_key,json=userVideoCoverKey,proto3" json:"user_video_cover_key,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ConsumeChatProxyRequestV2) Reset() {
	*x = ConsumeChatProxyRequestV2{}
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeChatProxyRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeChatProxyRequestV2) ProtoMessage() {}

func (x *ConsumeChatProxyRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeChatProxyRequestV2.ProtoReflect.Descriptor instead.
func (*ConsumeChatProxyRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_chatproxy_proto_rawDescGZIP(), []int{2}
}

func (x *ConsumeChatProxyRequestV2) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeChatProxyRequestV2) GetUserVideoKey() string {
	if x != nil {
		return x.UserVideoKey
	}
	return ""
}

func (x *ConsumeChatProxyRequestV2) GetUserVideoCoverKey() string {
	if x != nil {
		return x.UserVideoCoverKey
	}
	return ""
}

type ConsumeChatProxyResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeChatProxyResponseV2) Reset() {
	*x = ConsumeChatProxyResponseV2{}
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeChatProxyResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeChatProxyResponseV2) ProtoMessage() {}

func (x *ConsumeChatProxyResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeChatProxyResponseV2.ProtoReflect.Descriptor instead.
func (*ConsumeChatProxyResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_chatproxy_proto_rawDescGZIP(), []int{3}
}

func (x *ConsumeChatProxyResponseV2) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type CreateChatProxyStoryRequestV2 struct {
	state      protoimpl.MessageState       `protogen:"open.v1"`
	PlayConfig *v1.StoryPlayChatProxyConfig `protobuf:"bytes,1,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v11.PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	// 从哪个 story 过来创建的
	FromStoryId   *string `protobuf:"bytes,3,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatProxyStoryRequestV2) Reset() {
	*x = CreateChatProxyStoryRequestV2{}
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatProxyStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatProxyStoryRequestV2) ProtoMessage() {}

func (x *CreateChatProxyStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatProxyStoryRequestV2.ProtoReflect.Descriptor instead.
func (*CreateChatProxyStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_chatproxy_proto_rawDescGZIP(), []int{4}
}

func (x *CreateChatProxyStoryRequestV2) GetPlayConfig() *v1.StoryPlayChatProxyConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateChatProxyStoryRequestV2) GetPrivacySetting() *v11.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *CreateChatProxyStoryRequestV2) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

type CreateChatProxyStoryResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateChatProxyStoryResponseV2) Reset() {
	*x = CreateChatProxyStoryResponseV2{}
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatProxyStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatProxyStoryResponseV2) ProtoMessage() {}

func (x *CreateChatProxyStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_chatproxy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatProxyStoryResponseV2.ProtoReflect.Descriptor instead.
func (*CreateChatProxyStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_chatproxy_proto_rawDescGZIP(), []int{5}
}

func (x *CreateChatProxyStoryResponseV2) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

var File_api_items_story_v2_chatproxy_proto protoreflect.FileDescriptor

const file_api_items_story_v2_chatproxy_proto_rawDesc = "" +
	"\n" +
	"\"api/items/story/v2/chatproxy.proto\x12\x12api.items.story.v2\x1a.api/items/story/types/v1/chatproxy.types.proto\x1a.api/items/story/v1/story.privacy.setting.proto\x1a$api/items/story/types/v1/types.proto\x1a google/protobuf/descriptor.proto\x1a\x17validate/validate.proto\"\x93\x01\n" +
	"\x1eGetChatProxyNextTopicRequestV2\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12$\n" +
	"\x0euser_audio_key\x18\x02 \x01(\tR\fuserAudioKey\x12\x1f\n" +
	"\vround_index\x18\x03 \x01(\rR\n" +
	"roundIndex\"\xc6\x01\n" +
	"\x1fGetChatProxyNextTopicResponseV2\x12G\n" +
	"\bquestion\x18\x01 \x01(\v2+.api.items.story.types.v1.ChatProxyQuestionR\bquestion\x12\x10\n" +
	"\x03end\x18\x02 \x01(\bR\x03end\x12H\n" +
	"\fstory_detail\x18\x03 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\x9e\x01\n" +
	"\x19ConsumeChatProxyRequestV2\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12$\n" +
	"\x0euser_video_key\x18\x02 \x01(\tR\fuserVideoKey\x12/\n" +
	"\x14user_video_cover_key\x18\x03 \x01(\tR\x11userVideoCoverKey\"f\n" +
	"\x1aConsumeChatProxyResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xba\x02\n" +
	"\x1dCreateChatProxyStoryRequestV2\x12]\n" +
	"\vplay_config\x18\x01 \x01(\v22.api.items.story.types.v1.StoryPlayChatProxyConfigB\b\xfaB\x05\x8a\x01\x02\x10\x01R\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01\x128\n" +
	"\rfrom_story_id\x18\x03 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x01R\vfromStoryId\x88\x01\x01B\x12\n" +
	"\x10_privacy_settingB\x10\n" +
	"\x0e_from_story_id\"j\n" +
	"\x1eCreateChatProxyStoryResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetailB-Z+boson/api/items/story/v2;api_items_story_v2b\x06proto3"

var (
	file_api_items_story_v2_chatproxy_proto_rawDescOnce sync.Once
	file_api_items_story_v2_chatproxy_proto_rawDescData []byte
)

func file_api_items_story_v2_chatproxy_proto_rawDescGZIP() []byte {
	file_api_items_story_v2_chatproxy_proto_rawDescOnce.Do(func() {
		file_api_items_story_v2_chatproxy_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v2_chatproxy_proto_rawDesc), len(file_api_items_story_v2_chatproxy_proto_rawDesc)))
	})
	return file_api_items_story_v2_chatproxy_proto_rawDescData
}

var file_api_items_story_v2_chatproxy_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_items_story_v2_chatproxy_proto_goTypes = []any{
	(*GetChatProxyNextTopicRequestV2)(nil),  // 0: api.items.story.v2.GetChatProxyNextTopicRequestV2
	(*GetChatProxyNextTopicResponseV2)(nil), // 1: api.items.story.v2.GetChatProxyNextTopicResponseV2
	(*ConsumeChatProxyRequestV2)(nil),       // 2: api.items.story.v2.ConsumeChatProxyRequestV2
	(*ConsumeChatProxyResponseV2)(nil),      // 3: api.items.story.v2.ConsumeChatProxyResponseV2
	(*CreateChatProxyStoryRequestV2)(nil),   // 4: api.items.story.v2.CreateChatProxyStoryRequestV2
	(*CreateChatProxyStoryResponseV2)(nil),  // 5: api.items.story.v2.CreateChatProxyStoryResponseV2
	(*v1.ChatProxyQuestion)(nil),            // 6: api.items.story.types.v1.ChatProxyQuestion
	(*v1.StoryDetail)(nil),                  // 7: api.items.story.types.v1.StoryDetail
	(*v1.StoryPlayChatProxyConfig)(nil),     // 8: api.items.story.types.v1.StoryPlayChatProxyConfig
	(*v11.PrivacySettingUpdateAttr)(nil),    // 9: api.items.story.v1.PrivacySettingUpdateAttr
}
var file_api_items_story_v2_chatproxy_proto_depIdxs = []int32{
	6, // 0: api.items.story.v2.GetChatProxyNextTopicResponseV2.question:type_name -> api.items.story.types.v1.ChatProxyQuestion
	7, // 1: api.items.story.v2.GetChatProxyNextTopicResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	7, // 2: api.items.story.v2.ConsumeChatProxyResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	8, // 3: api.items.story.v2.CreateChatProxyStoryRequestV2.play_config:type_name -> api.items.story.types.v1.StoryPlayChatProxyConfig
	9, // 4: api.items.story.v2.CreateChatProxyStoryRequestV2.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	7, // 5: api.items.story.v2.CreateChatProxyStoryResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_items_story_v2_chatproxy_proto_init() }
func file_api_items_story_v2_chatproxy_proto_init() {
	if File_api_items_story_v2_chatproxy_proto != nil {
		return
	}
	file_api_items_story_v2_chatproxy_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v2_chatproxy_proto_rawDesc), len(file_api_items_story_v2_chatproxy_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v2_chatproxy_proto_goTypes,
		DependencyIndexes: file_api_items_story_v2_chatproxy_proto_depIdxs,
		MessageInfos:      file_api_items_story_v2_chatproxy_proto_msgTypes,
	}.Build()
	File_api_items_story_v2_chatproxy_proto = out.File
	file_api_items_story_v2_chatproxy_proto_goTypes = nil
	file_api_items_story_v2_chatproxy_proto_depIdxs = nil
}
