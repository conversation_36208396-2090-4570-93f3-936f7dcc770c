// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v2/haunt.proto

package api_items_story_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = api_items_story_types_v1.HauntBooShowInfoSence(0)
)

// Validate checks the field values on ImageCheckRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ImageCheckRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCheckRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImageCheckRequestMultiError, or nil if none found.
func (m *ImageCheckRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCheckRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ImageKey

	// no validation rules for TextCondition

	if len(errors) > 0 {
		return ImageCheckRequestMultiError(errors)
	}

	return nil
}

// ImageCheckRequestMultiError is an error wrapping multiple validation errors
// returned by ImageCheckRequest.ValidateAll() if the designated constraints
// aren't met.
type ImageCheckRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageCheckRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageCheckRequestMultiError) AllErrors() []error { return m }

// ImageCheckRequestValidationError is the validation error returned by
// ImageCheckRequest.Validate if the designated constraints aren't met.
type ImageCheckRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageCheckRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageCheckRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageCheckRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageCheckRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageCheckRequestValidationError) ErrorName() string {
	return "ImageCheckRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ImageCheckRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCheckRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageCheckRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageCheckRequestValidationError{}

// Validate checks the field values on ImageCheckResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ImageCheckResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageCheckResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ImageCheckResponseMultiError, or nil if none found.
func (m *ImageCheckResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageCheckResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Pass

	// no validation rules for Feedback

	if len(errors) > 0 {
		return ImageCheckResponseMultiError(errors)
	}

	return nil
}

// ImageCheckResponseMultiError is an error wrapping multiple validation errors
// returned by ImageCheckResponse.ValidateAll() if the designated constraints
// aren't met.
type ImageCheckResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageCheckResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageCheckResponseMultiError) AllErrors() []error { return m }

// ImageCheckResponseValidationError is the validation error returned by
// ImageCheckResponse.Validate if the designated constraints aren't met.
type ImageCheckResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageCheckResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageCheckResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageCheckResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageCheckResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageCheckResponseValidationError) ErrorName() string {
	return "ImageCheckResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ImageCheckResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageCheckResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageCheckResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageCheckResponseValidationError{}

// Validate checks the field values on ListHauntRandomAvatarsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHauntRandomAvatarsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHauntRandomAvatarsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListHauntRandomAvatarsRequestMultiError, or nil if none found.
func (m *ListHauntRandomAvatarsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHauntRandomAvatarsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListHauntRandomAvatarsRequestMultiError(errors)
	}

	return nil
}

// ListHauntRandomAvatarsRequestMultiError is an error wrapping multiple
// validation errors returned by ListHauntRandomAvatarsRequest.ValidateAll()
// if the designated constraints aren't met.
type ListHauntRandomAvatarsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHauntRandomAvatarsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHauntRandomAvatarsRequestMultiError) AllErrors() []error { return m }

// ListHauntRandomAvatarsRequestValidationError is the validation error
// returned by ListHauntRandomAvatarsRequest.Validate if the designated
// constraints aren't met.
type ListHauntRandomAvatarsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHauntRandomAvatarsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHauntRandomAvatarsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHauntRandomAvatarsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHauntRandomAvatarsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHauntRandomAvatarsRequestValidationError) ErrorName() string {
	return "ListHauntRandomAvatarsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListHauntRandomAvatarsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHauntRandomAvatarsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHauntRandomAvatarsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHauntRandomAvatarsRequestValidationError{}

// Validate checks the field values on ListHauntRandomAvatarsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHauntRandomAvatarsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHauntRandomAvatarsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListHauntRandomAvatarsResponseMultiError, or nil if none found.
func (m *ListHauntRandomAvatarsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHauntRandomAvatarsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListHauntRandomAvatarsResponseMultiError(errors)
	}

	return nil
}

// ListHauntRandomAvatarsResponseMultiError is an error wrapping multiple
// validation errors returned by ListHauntRandomAvatarsResponse.ValidateAll()
// if the designated constraints aren't met.
type ListHauntRandomAvatarsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHauntRandomAvatarsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHauntRandomAvatarsResponseMultiError) AllErrors() []error { return m }

// ListHauntRandomAvatarsResponseValidationError is the validation error
// returned by ListHauntRandomAvatarsResponse.Validate if the designated
// constraints aren't met.
type ListHauntRandomAvatarsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHauntRandomAvatarsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHauntRandomAvatarsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHauntRandomAvatarsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHauntRandomAvatarsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHauntRandomAvatarsResponseValidationError) ErrorName() string {
	return "ListHauntRandomAvatarsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListHauntRandomAvatarsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHauntRandomAvatarsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHauntRandomAvatarsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHauntRandomAvatarsResponseValidationError{}

// Validate checks the field values on ListHauntQuestionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHauntQuestionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHauntQuestionsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListHauntQuestionsRequestMultiError, or nil if none found.
func (m *ListHauntQuestionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHauntQuestionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListHauntQuestionsRequestMultiError(errors)
	}

	return nil
}

// ListHauntQuestionsRequestMultiError is an error wrapping multiple validation
// errors returned by ListHauntQuestionsRequest.ValidateAll() if the
// designated constraints aren't met.
type ListHauntQuestionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHauntQuestionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHauntQuestionsRequestMultiError) AllErrors() []error { return m }

// ListHauntQuestionsRequestValidationError is the validation error returned by
// ListHauntQuestionsRequest.Validate if the designated constraints aren't met.
type ListHauntQuestionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHauntQuestionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHauntQuestionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHauntQuestionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHauntQuestionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHauntQuestionsRequestValidationError) ErrorName() string {
	return "ListHauntQuestionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListHauntQuestionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHauntQuestionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHauntQuestionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHauntQuestionsRequestValidationError{}

// Validate checks the field values on ListHauntQuestionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHauntQuestionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHauntQuestionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListHauntQuestionsResponseMultiError, or nil if none found.
func (m *ListHauntQuestionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHauntQuestionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListHauntQuestionsResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListHauntQuestionsResponseValidationError{
						field:  fmt.Sprintf("Questions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListHauntQuestionsResponseValidationError{
					field:  fmt.Sprintf("Questions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListHauntQuestionsResponseMultiError(errors)
	}

	return nil
}

// ListHauntQuestionsResponseMultiError is an error wrapping multiple
// validation errors returned by ListHauntQuestionsResponse.ValidateAll() if
// the designated constraints aren't met.
type ListHauntQuestionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHauntQuestionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHauntQuestionsResponseMultiError) AllErrors() []error { return m }

// ListHauntQuestionsResponseValidationError is the validation error returned
// by ListHauntQuestionsResponse.Validate if the designated constraints aren't met.
type ListHauntQuestionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHauntQuestionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHauntQuestionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHauntQuestionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHauntQuestionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHauntQuestionsResponseValidationError) ErrorName() string {
	return "ListHauntQuestionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListHauntQuestionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHauntQuestionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHauntQuestionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHauntQuestionsResponseValidationError{}

// Validate checks the field values on ListHauntBooAssistRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHauntBooAssistRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHauntBooAssistRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListHauntBooAssistRequestMultiError, or nil if none found.
func (m *ListHauntBooAssistRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHauntBooAssistRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListHauntBooAssistRequestValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListHauntBooAssistRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListHauntBooAssistRequestValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListHauntBooAssistRequestValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListHauntBooAssistRequestMultiError(errors)
	}

	return nil
}

// ListHauntBooAssistRequestMultiError is an error wrapping multiple validation
// errors returned by ListHauntBooAssistRequest.ValidateAll() if the
// designated constraints aren't met.
type ListHauntBooAssistRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHauntBooAssistRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHauntBooAssistRequestMultiError) AllErrors() []error { return m }

// ListHauntBooAssistRequestValidationError is the validation error returned by
// ListHauntBooAssistRequest.Validate if the designated constraints aren't met.
type ListHauntBooAssistRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHauntBooAssistRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHauntBooAssistRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHauntBooAssistRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHauntBooAssistRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHauntBooAssistRequestValidationError) ErrorName() string {
	return "ListHauntBooAssistRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListHauntBooAssistRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHauntBooAssistRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHauntBooAssistRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHauntBooAssistRequestValidationError{}

// Validate checks the field values on ListHauntBooAssistResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHauntBooAssistResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHauntBooAssistResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListHauntBooAssistResponseMultiError, or nil if none found.
func (m *ListHauntBooAssistResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHauntBooAssistResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBoosWithQuestionsAndAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListHauntBooAssistResponseValidationError{
						field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListHauntBooAssistResponseValidationError{
						field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListHauntBooAssistResponseValidationError{
					field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListHauntBooAssistResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListHauntBooAssistResponseValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListHauntBooAssistResponseValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListHauntBooAssistResponseMultiError(errors)
	}

	return nil
}

// ListHauntBooAssistResponseMultiError is an error wrapping multiple
// validation errors returned by ListHauntBooAssistResponse.ValidateAll() if
// the designated constraints aren't met.
type ListHauntBooAssistResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHauntBooAssistResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHauntBooAssistResponseMultiError) AllErrors() []error { return m }

// ListHauntBooAssistResponseValidationError is the validation error returned
// by ListHauntBooAssistResponse.Validate if the designated constraints aren't met.
type ListHauntBooAssistResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHauntBooAssistResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHauntBooAssistResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHauntBooAssistResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHauntBooAssistResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHauntBooAssistResponseValidationError) ErrorName() string {
	return "ListHauntBooAssistResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListHauntBooAssistResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHauntBooAssistResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHauntBooAssistResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHauntBooAssistResponseValidationError{}

// Validate checks the field values on ConsumeHauntStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeHauntStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeHauntStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeHauntStoryRequestMultiError, or nil if none found.
func (m *ConsumeHauntStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeHauntStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for Unlocked

	// no validation rules for IsFromFeedOrFriends

	if len(errors) > 0 {
		return ConsumeHauntStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeHauntStoryRequestMultiError is an error wrapping multiple validation
// errors returned by ConsumeHauntStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type ConsumeHauntStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeHauntStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeHauntStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeHauntStoryRequestValidationError is the validation error returned by
// ConsumeHauntStoryRequest.Validate if the designated constraints aren't met.
type ConsumeHauntStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeHauntStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeHauntStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeHauntStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeHauntStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeHauntStoryRequestValidationError) ErrorName() string {
	return "ConsumeHauntStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeHauntStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeHauntStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeHauntStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeHauntStoryRequestValidationError{}

// Validate checks the field values on ConsumeHauntStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeHauntStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeHauntStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeHauntStoryResponseMultiError, or nil if none found.
func (m *ConsumeHauntStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeHauntStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeHauntStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeHauntStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeHauntStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeHauntStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeHauntStoryResponseMultiError is an error wrapping multiple validation
// errors returned by ConsumeHauntStoryResponse.ValidateAll() if the
// designated constraints aren't met.
type ConsumeHauntStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeHauntStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeHauntStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeHauntStoryResponseValidationError is the validation error returned by
// ConsumeHauntStoryResponse.Validate if the designated constraints aren't met.
type ConsumeHauntStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeHauntStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeHauntStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeHauntStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeHauntStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeHauntStoryResponseValidationError) ErrorName() string {
	return "ConsumeHauntStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeHauntStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeHauntStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeHauntStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeHauntStoryResponseValidationError{}

// Validate checks the field values on SendHauntCaptureVideoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendHauntCaptureVideoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendHauntCaptureVideoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendHauntCaptureVideoRequestMultiError, or nil if none found.
func (m *SendHauntCaptureVideoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendHauntCaptureVideoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for VideoKey

	// no validation rules for VideoCoverKey

	if len(errors) > 0 {
		return SendHauntCaptureVideoRequestMultiError(errors)
	}

	return nil
}

// SendHauntCaptureVideoRequestMultiError is an error wrapping multiple
// validation errors returned by SendHauntCaptureVideoRequest.ValidateAll() if
// the designated constraints aren't met.
type SendHauntCaptureVideoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendHauntCaptureVideoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendHauntCaptureVideoRequestMultiError) AllErrors() []error { return m }

// SendHauntCaptureVideoRequestValidationError is the validation error returned
// by SendHauntCaptureVideoRequest.Validate if the designated constraints
// aren't met.
type SendHauntCaptureVideoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendHauntCaptureVideoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendHauntCaptureVideoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendHauntCaptureVideoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendHauntCaptureVideoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendHauntCaptureVideoRequestValidationError) ErrorName() string {
	return "SendHauntCaptureVideoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendHauntCaptureVideoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendHauntCaptureVideoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendHauntCaptureVideoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendHauntCaptureVideoRequestValidationError{}

// Validate checks the field values on CreateHauntStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHauntStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHauntStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHauntStoryRequestMultiError, or nil if none found.
func (m *CreateHauntStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHauntStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBoosWithQuestionsAndAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHauntStoryRequestValidationError{
					field:  fmt.Sprintf("BoosWithQuestionsAndAnswers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCaptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  fmt.Sprintf("Captions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  fmt.Sprintf("Captions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHauntStoryRequestValidationError{
					field:  fmt.Sprintf("Captions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetMomentCreateAttrs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHauntStoryRequestValidationError{
					field:  fmt.Sprintf("MomentCreateAttrs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHauntStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.Cover != nil {

		if all {
			switch v := interface{}(m.GetCover()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  "Cover",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHauntStoryRequestValidationError{
						field:  "Cover",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCover()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHauntStoryRequestValidationError{
					field:  "Cover",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateHauntStoryRequestMultiError(errors)
	}

	return nil
}

// CreateHauntStoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreateHauntStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateHauntStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHauntStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHauntStoryRequestMultiError) AllErrors() []error { return m }

// CreateHauntStoryRequestValidationError is the validation error returned by
// CreateHauntStoryRequest.Validate if the designated constraints aren't met.
type CreateHauntStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHauntStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHauntStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHauntStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHauntStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHauntStoryRequestValidationError) ErrorName() string {
	return "CreateHauntStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHauntStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHauntStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHauntStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHauntStoryRequestValidationError{}

// Validate checks the field values on CreateHauntStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHauntStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHauntStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHauntStoryResponseMultiError, or nil if none found.
func (m *CreateHauntStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHauntStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateHauntStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateHauntStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateHauntStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateHauntStoryResponseMultiError(errors)
	}

	return nil
}

// CreateHauntStoryResponseMultiError is an error wrapping multiple validation
// errors returned by CreateHauntStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateHauntStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHauntStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHauntStoryResponseMultiError) AllErrors() []error { return m }

// CreateHauntStoryResponseValidationError is the validation error returned by
// CreateHauntStoryResponse.Validate if the designated constraints aren't met.
type CreateHauntStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHauntStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHauntStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHauntStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHauntStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHauntStoryResponseValidationError) ErrorName() string {
	return "CreateHauntStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHauntStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHauntStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHauntStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHauntStoryResponseValidationError{}

// Validate checks the field values on
// AddCapturedBooInToCollectedStickersRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddCapturedBooInToCollectedStickersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AddCapturedBooInToCollectedStickersRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AddCapturedBooInToCollectedStickersRequestMultiError, or nil if none found.
func (m *AddCapturedBooInToCollectedStickersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCapturedBooInToCollectedStickersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_AddCapturedBooInToCollectedStickersRequest_BooId_Pattern.MatchString(m.GetBooId()) {
		err := AddCapturedBooInToCollectedStickersRequestValidationError{
			field:  "BooId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddCapturedBooInToCollectedStickersRequestMultiError(errors)
	}

	return nil
}

// AddCapturedBooInToCollectedStickersRequestMultiError is an error wrapping
// multiple validation errors returned by
// AddCapturedBooInToCollectedStickersRequest.ValidateAll() if the designated
// constraints aren't met.
type AddCapturedBooInToCollectedStickersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCapturedBooInToCollectedStickersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCapturedBooInToCollectedStickersRequestMultiError) AllErrors() []error { return m }

// AddCapturedBooInToCollectedStickersRequestValidationError is the validation
// error returned by AddCapturedBooInToCollectedStickersRequest.Validate if
// the designated constraints aren't met.
type AddCapturedBooInToCollectedStickersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCapturedBooInToCollectedStickersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCapturedBooInToCollectedStickersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCapturedBooInToCollectedStickersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCapturedBooInToCollectedStickersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCapturedBooInToCollectedStickersRequestValidationError) ErrorName() string {
	return "AddCapturedBooInToCollectedStickersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddCapturedBooInToCollectedStickersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCapturedBooInToCollectedStickersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCapturedBooInToCollectedStickersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCapturedBooInToCollectedStickersRequestValidationError{}

var _AddCapturedBooInToCollectedStickersRequest_BooId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on
// AddCapturedBooInToCollectedStickersResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddCapturedBooInToCollectedStickersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AddCapturedBooInToCollectedStickersResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// AddCapturedBooInToCollectedStickersResponseMultiError, or nil if none found.
func (m *AddCapturedBooInToCollectedStickersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCapturedBooInToCollectedStickersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAddedCollectedSticker()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddCapturedBooInToCollectedStickersResponseValidationError{
					field:  "AddedCollectedSticker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddCapturedBooInToCollectedStickersResponseValidationError{
					field:  "AddedCollectedSticker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddedCollectedSticker()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddCapturedBooInToCollectedStickersResponseValidationError{
				field:  "AddedCollectedSticker",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddCapturedBooInToCollectedStickersResponseMultiError(errors)
	}

	return nil
}

// AddCapturedBooInToCollectedStickersResponseMultiError is an error wrapping
// multiple validation errors returned by
// AddCapturedBooInToCollectedStickersResponse.ValidateAll() if the designated
// constraints aren't met.
type AddCapturedBooInToCollectedStickersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCapturedBooInToCollectedStickersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCapturedBooInToCollectedStickersResponseMultiError) AllErrors() []error { return m }

// AddCapturedBooInToCollectedStickersResponseValidationError is the validation
// error returned by AddCapturedBooInToCollectedStickersResponse.Validate if
// the designated constraints aren't met.
type AddCapturedBooInToCollectedStickersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCapturedBooInToCollectedStickersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCapturedBooInToCollectedStickersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCapturedBooInToCollectedStickersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCapturedBooInToCollectedStickersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCapturedBooInToCollectedStickersResponseValidationError) ErrorName() string {
	return "AddCapturedBooInToCollectedStickersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddCapturedBooInToCollectedStickersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCapturedBooInToCollectedStickersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCapturedBooInToCollectedStickersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCapturedBooInToCollectedStickersResponseValidationError{}

// Validate checks the field values on AddCaptureBooIntoMyAssistRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AddCaptureBooIntoMyAssistRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddCaptureBooIntoMyAssistRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AddCaptureBooIntoMyAssistRequestMultiError, or nil if none found.
func (m *AddCaptureBooIntoMyAssistRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCaptureBooIntoMyAssistRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_AddCaptureBooIntoMyAssistRequest_BooId_Pattern.MatchString(m.GetBooId()) {
		err := AddCaptureBooIntoMyAssistRequestValidationError{
			field:  "BooId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_AddCaptureBooIntoMyAssistRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := AddCaptureBooIntoMyAssistRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddCaptureBooIntoMyAssistRequestMultiError(errors)
	}

	return nil
}

// AddCaptureBooIntoMyAssistRequestMultiError is an error wrapping multiple
// validation errors returned by
// AddCaptureBooIntoMyAssistRequest.ValidateAll() if the designated
// constraints aren't met.
type AddCaptureBooIntoMyAssistRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCaptureBooIntoMyAssistRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCaptureBooIntoMyAssistRequestMultiError) AllErrors() []error { return m }

// AddCaptureBooIntoMyAssistRequestValidationError is the validation error
// returned by AddCaptureBooIntoMyAssistRequest.Validate if the designated
// constraints aren't met.
type AddCaptureBooIntoMyAssistRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCaptureBooIntoMyAssistRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCaptureBooIntoMyAssistRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCaptureBooIntoMyAssistRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCaptureBooIntoMyAssistRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCaptureBooIntoMyAssistRequestValidationError) ErrorName() string {
	return "AddCaptureBooIntoMyAssistRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddCaptureBooIntoMyAssistRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCaptureBooIntoMyAssistRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCaptureBooIntoMyAssistRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCaptureBooIntoMyAssistRequestValidationError{}

var _AddCaptureBooIntoMyAssistRequest_BooId_Pattern = regexp.MustCompile("^[0-9]+$")

var _AddCaptureBooIntoMyAssistRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on AddCaptureBooIntoMyAssistResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AddCaptureBooIntoMyAssistResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddCaptureBooIntoMyAssistResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AddCaptureBooIntoMyAssistResponseMultiError, or nil if none found.
func (m *AddCaptureBooIntoMyAssistResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCaptureBooIntoMyAssistResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddCaptureBooIntoMyAssistResponseMultiError(errors)
	}

	return nil
}

// AddCaptureBooIntoMyAssistResponseMultiError is an error wrapping multiple
// validation errors returned by
// AddCaptureBooIntoMyAssistResponse.ValidateAll() if the designated
// constraints aren't met.
type AddCaptureBooIntoMyAssistResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCaptureBooIntoMyAssistResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCaptureBooIntoMyAssistResponseMultiError) AllErrors() []error { return m }

// AddCaptureBooIntoMyAssistResponseValidationError is the validation error
// returned by AddCaptureBooIntoMyAssistResponse.Validate if the designated
// constraints aren't met.
type AddCaptureBooIntoMyAssistResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCaptureBooIntoMyAssistResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCaptureBooIntoMyAssistResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCaptureBooIntoMyAssistResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCaptureBooIntoMyAssistResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCaptureBooIntoMyAssistResponseValidationError) ErrorName() string {
	return "AddCaptureBooIntoMyAssistResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddCaptureBooIntoMyAssistResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCaptureBooIntoMyAssistResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCaptureBooIntoMyAssistResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCaptureBooIntoMyAssistResponseValidationError{}

// Validate checks the field values on ReportHauntShowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportHauntShowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportHauntShowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportHauntShowRequestMultiError, or nil if none found.
func (m *ReportHauntShowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportHauntShowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ReportHauntShowRequest_BooId_Pattern.MatchString(m.GetBooId()) {
		err := ReportHauntShowRequestValidationError{
			field:  "BooId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ShowInfoSence

	if len(errors) > 0 {
		return ReportHauntShowRequestMultiError(errors)
	}

	return nil
}

// ReportHauntShowRequestMultiError is an error wrapping multiple validation
// errors returned by ReportHauntShowRequest.ValidateAll() if the designated
// constraints aren't met.
type ReportHauntShowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportHauntShowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportHauntShowRequestMultiError) AllErrors() []error { return m }

// ReportHauntShowRequestValidationError is the validation error returned by
// ReportHauntShowRequest.Validate if the designated constraints aren't met.
type ReportHauntShowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportHauntShowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportHauntShowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportHauntShowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportHauntShowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportHauntShowRequestValidationError) ErrorName() string {
	return "ReportHauntShowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReportHauntShowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportHauntShowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportHauntShowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportHauntShowRequestValidationError{}

var _ReportHauntShowRequest_BooId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ReportHauntShowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReportHauntShowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReportHauntShowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReportHauntShowResponseMultiError, or nil if none found.
func (m *ReportHauntShowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReportHauntShowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetUpdatedHauntBooShowInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReportHauntShowResponseValidationError{
					field:  "UpdatedHauntBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReportHauntShowResponseValidationError{
					field:  "UpdatedHauntBooShowInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedHauntBooShowInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReportHauntShowResponseValidationError{
				field:  "UpdatedHauntBooShowInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReportHauntShowResponseMultiError(errors)
	}

	return nil
}

// ReportHauntShowResponseMultiError is an error wrapping multiple validation
// errors returned by ReportHauntShowResponse.ValidateAll() if the designated
// constraints aren't met.
type ReportHauntShowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReportHauntShowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReportHauntShowResponseMultiError) AllErrors() []error { return m }

// ReportHauntShowResponseValidationError is the validation error returned by
// ReportHauntShowResponse.Validate if the designated constraints aren't met.
type ReportHauntShowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReportHauntShowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReportHauntShowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReportHauntShowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReportHauntShowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReportHauntShowResponseValidationError) ErrorName() string {
	return "ReportHauntShowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReportHauntShowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReportHauntShowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReportHauntShowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReportHauntShowResponseValidationError{}

// Validate checks the field values on
// CreateHauntStoryRequest_BooWithQuestionAndAnswer with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreateHauntStoryRequest_BooWithQuestionAndAnswer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreateHauntStoryRequest_BooWithQuestionAndAnswer with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CreateHauntStoryRequest_BooWithQuestionAndAnswerMultiError, or nil if none found.
func (m *CreateHauntStoryRequest_BooWithQuestionAndAnswer) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHauntStoryRequest_BooWithQuestionAndAnswer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuestionsWithAnswers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{
						field:  fmt.Sprintf("QuestionsWithAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{
						field:  fmt.Sprintf("QuestionsWithAnswers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{
					field:  fmt.Sprintf("QuestionsWithAnswers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	switch v := m.ObjectId.(type) {
	case *CreateHauntStoryRequest_BooWithQuestionAndAnswer_BooId:
		if v == nil {
			err := CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{
				field:  "ObjectId",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for BooId
	case *CreateHauntStoryRequest_BooWithQuestionAndAnswer_UserAvatarId:
		if v == nil {
			err := CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{
				field:  "ObjectId",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for UserAvatarId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CreateHauntStoryRequest_BooWithQuestionAndAnswerMultiError(errors)
	}

	return nil
}

// CreateHauntStoryRequest_BooWithQuestionAndAnswerMultiError is an error
// wrapping multiple validation errors returned by
// CreateHauntStoryRequest_BooWithQuestionAndAnswer.ValidateAll() if the
// designated constraints aren't met.
type CreateHauntStoryRequest_BooWithQuestionAndAnswerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHauntStoryRequest_BooWithQuestionAndAnswerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHauntStoryRequest_BooWithQuestionAndAnswerMultiError) AllErrors() []error { return m }

// CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError is the
// validation error returned by
// CreateHauntStoryRequest_BooWithQuestionAndAnswer.Validate if the designated
// constraints aren't met.
type CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError) ErrorName() string {
	return "CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHauntStoryRequest_BooWithQuestionAndAnswer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHauntStoryRequest_BooWithQuestionAndAnswerValidationError{}

// Validate checks the field values on
// AddCapturedBooInToCollectedStickersResponse_CollectedSticker with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// AddCapturedBooInToCollectedStickersResponse_CollectedSticker with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddCapturedBooInToCollectedStickersResponse_CollectedStickerMultiError, or
// nil if none found.
func (m *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetSticker() == nil {
		err := AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError{
			field:  "Sticker",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSticker()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError{
					field:  "Sticker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError{
					field:  "Sticker",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSticker()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError{
				field:  "Sticker",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CollectedAtUnixTimestamp

	// no validation rules for IsTop

	if len(errors) > 0 {
		return AddCapturedBooInToCollectedStickersResponse_CollectedStickerMultiError(errors)
	}

	return nil
}

// AddCapturedBooInToCollectedStickersResponse_CollectedStickerMultiError is an
// error wrapping multiple validation errors returned by
// AddCapturedBooInToCollectedStickersResponse_CollectedSticker.ValidateAll()
// if the designated constraints aren't met.
type AddCapturedBooInToCollectedStickersResponse_CollectedStickerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCapturedBooInToCollectedStickersResponse_CollectedStickerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCapturedBooInToCollectedStickersResponse_CollectedStickerMultiError) AllErrors() []error {
	return m
}

// AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError
// is the validation error returned by
// AddCapturedBooInToCollectedStickersResponse_CollectedSticker.Validate if
// the designated constraints aren't met.
type AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError) ErrorName() string {
	return "AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError"
}

// Error satisfies the builtin error interface
func (e AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCapturedBooInToCollectedStickersResponse_CollectedSticker.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCapturedBooInToCollectedStickersResponse_CollectedStickerValidationError{}
