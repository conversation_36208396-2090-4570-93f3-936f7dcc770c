// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v2/story.proto

package api_items_story_v2

import (
	v1 "boson/api/common/v1"
	v14 "boson/api/im/message/types/v1"
	v12 "boson/api/items/portal/types/v1"
	v11 "boson/api/items/story/types/v1"
	v13 "boson/api/items/story/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 消息类型枚举
type MessageType int32

const (
	MessageType_MESSAGE_TYPE_UNSPECIFIED MessageType = 0
	MessageType_MESSAGE_TYPE_WASSUP      MessageType = 1
	MessageType_MESSAGE_TYPE_CHATPROXY   MessageType = 2
	MessageType_MESSAGE_TYPE_ROASTED     MessageType = 3
	MessageType_MESSAGE_TYPE_BASEPLAY    MessageType = 4
)

// Enum value maps for MessageType.
var (
	MessageType_name = map[int32]string{
		0: "MESSAGE_TYPE_UNSPECIFIED",
		1: "MESSAGE_TYPE_WASSUP",
		2: "MESSAGE_TYPE_CHATPROXY",
		3: "MESSAGE_TYPE_ROASTED",
		4: "MESSAGE_TYPE_BASEPLAY",
	}
	MessageType_value = map[string]int32{
		"MESSAGE_TYPE_UNSPECIFIED": 0,
		"MESSAGE_TYPE_WASSUP":      1,
		"MESSAGE_TYPE_CHATPROXY":   2,
		"MESSAGE_TYPE_ROASTED":     3,
		"MESSAGE_TYPE_BASEPLAY":    4,
	}
)

func (x MessageType) Enum() *MessageType {
	p := new(MessageType)
	*p = x
	return p
}

func (x MessageType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_items_story_v2_story_proto_enumTypes[0].Descriptor()
}

func (MessageType) Type() protoreflect.EnumType {
	return &file_api_items_story_v2_story_proto_enumTypes[0]
}

func (x MessageType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageType.Descriptor instead.
func (MessageType) EnumDescriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{0}
}

type ListHomePageStoryRequestV2 struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	ListRequest *v1.ListRequest        `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	// 是否使用推荐引擎
	UseRecommended bool `protobuf:"varint,2,opt,name=use_recommended,json=useRecommended,proto3" json:"use_recommended,omitempty"`
	// 过滤类型，如果传空，则不过滤
	// 当且仅当不走推荐引擎时起作用
	FilterPlayTypes []v11.StoryPlayType `protobuf:"varint,3,rep,packed,name=filter_play_types,json=filterPlayTypes,proto3,enum=api.items.story.types.v1.StoryPlayType" json:"filter_play_types,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListHomePageStoryRequestV2) Reset() {
	*x = ListHomePageStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHomePageStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHomePageStoryRequestV2) ProtoMessage() {}

func (x *ListHomePageStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHomePageStoryRequestV2.ProtoReflect.Descriptor instead.
func (*ListHomePageStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{0}
}

func (x *ListHomePageStoryRequestV2) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

func (x *ListHomePageStoryRequestV2) GetUseRecommended() bool {
	if x != nil {
		return x.UseRecommended
	}
	return false
}

func (x *ListHomePageStoryRequestV2) GetFilterPlayTypes() []v11.StoryPlayType {
	if x != nil {
		return x.FilterPlayTypes
	}
	return nil
}

type ListHomePageStoryResponseV2 struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	Stories      []*v11.StoryDetail     `protobuf:"bytes,1,rep,name=stories,proto3" json:"stories,omitempty"`
	ListResponse *v1.ListResponse       `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	// 推荐引擎的 sort_request_id
	SortRequestId string `protobuf:"bytes,3,opt,name=sort_request_id,json=sortRequestId,proto3" json:"sort_request_id,omitempty"`
	// 当且仅当第一页时，会返回顶部的 10个 除登录用户自己发的 其他portals
	// 注意，其中是包含 user created portals 的
	UserCreatedPortals *v12.UserCreatedPortals `protobuf:"bytes,4,opt,name=user_created_portals,json=userCreatedPortals,proto3" json:"user_created_portals,omitempty"`
	Portals            []*v12.Portal           `protobuf:"bytes,5,rep,name=portals,proto3" json:"portals,omitempty"`
	// 用于下一页的 portals 获取
	PortalListResponse *v1.ListResponse `protobuf:"bytes,6,opt,name=portal_list_response,json=portalListResponse,proto3" json:"portal_list_response,omitempty"`
	// 可能存在的 haunt boo 的展示信息，注意目前是返回数组，但是客户端应该只取第一个
	// 服务端暂时会保证数组也只有一个，后续如果需要支持数组，则需要修改
	HauntBooShowInfos []*v11.HauntBooShowInfo `protobuf:"bytes,7,rep,name=haunt_boo_show_infos,json=hauntBooShowInfos,proto3" json:"haunt_boo_show_infos,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListHomePageStoryResponseV2) Reset() {
	*x = ListHomePageStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHomePageStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHomePageStoryResponseV2) ProtoMessage() {}

func (x *ListHomePageStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHomePageStoryResponseV2.ProtoReflect.Descriptor instead.
func (*ListHomePageStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{1}
}

func (x *ListHomePageStoryResponseV2) GetStories() []*v11.StoryDetail {
	if x != nil {
		return x.Stories
	}
	return nil
}

func (x *ListHomePageStoryResponseV2) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

func (x *ListHomePageStoryResponseV2) GetSortRequestId() string {
	if x != nil {
		return x.SortRequestId
	}
	return ""
}

func (x *ListHomePageStoryResponseV2) GetUserCreatedPortals() *v12.UserCreatedPortals {
	if x != nil {
		return x.UserCreatedPortals
	}
	return nil
}

func (x *ListHomePageStoryResponseV2) GetPortals() []*v12.Portal {
	if x != nil {
		return x.Portals
	}
	return nil
}

func (x *ListHomePageStoryResponseV2) GetPortalListResponse() *v1.ListResponse {
	if x != nil {
		return x.PortalListResponse
	}
	return nil
}

func (x *ListHomePageStoryResponseV2) GetHauntBooShowInfos() []*v11.HauntBooShowInfo {
	if x != nil {
		return x.HauntBooShowInfos
	}
	return nil
}

type ListCreatorStoryRequestV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CreatorId     string                 `protobuf:"bytes,1,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	ListRequest   *v1.ListRequest        `protobuf:"bytes,2,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCreatorStoryRequestV2) Reset() {
	*x = ListCreatorStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCreatorStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreatorStoryRequestV2) ProtoMessage() {}

func (x *ListCreatorStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreatorStoryRequestV2.ProtoReflect.Descriptor instead.
func (*ListCreatorStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{2}
}

func (x *ListCreatorStoryRequestV2) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *ListCreatorStoryRequestV2) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListCreatorStoryResponseV2 struct {
	state          protoimpl.MessageState                     `protogen:"open.v1"`
	CreatedStories []*ListCreatorStoryResponseV2_CreatedStory `protobuf:"bytes,1,rep,name=created_stories,json=createdStories,proto3" json:"created_stories,omitempty"`
	ListResponse   *v1.ListResponse                           `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListCreatorStoryResponseV2) Reset() {
	*x = ListCreatorStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCreatorStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreatorStoryResponseV2) ProtoMessage() {}

func (x *ListCreatorStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreatorStoryResponseV2.ProtoReflect.Descriptor instead.
func (*ListCreatorStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{3}
}

func (x *ListCreatorStoryResponseV2) GetCreatedStories() []*ListCreatorStoryResponseV2_CreatedStory {
	if x != nil {
		return x.CreatedStories
	}
	return nil
}

func (x *ListCreatorStoryResponseV2) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

// 获取关注的创作者 story 列表
type ListFollowingCreatorStoryRequestV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v1.ListRequest        `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListFollowingCreatorStoryRequestV2) Reset() {
	*x = ListFollowingCreatorStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFollowingCreatorStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFollowingCreatorStoryRequestV2) ProtoMessage() {}

func (x *ListFollowingCreatorStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFollowingCreatorStoryRequestV2.ProtoReflect.Descriptor instead.
func (*ListFollowingCreatorStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{4}
}

func (x *ListFollowingCreatorStoryRequestV2) GetListRequest() *v1.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListFollowingCreatorStoryResponseV2 struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	FollowingCreatorStories []*v11.StoryDetail     `protobuf:"bytes,1,rep,name=following_creator_stories,json=followingCreatorStories,proto3" json:"following_creator_stories,omitempty"`
	// list response 针对的是 following_creator_stories 的数组，不会影响 recommended_unfollowed_creator_stories 的数组
	ListResponse *v1.ListResponse `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	// 如果创作者的 story 列表为空，则会返回推荐的 story，用于曝光更多的作品
	// 此数组当且仅当关注的创作者的 story 列表为空时才会返回
	// 且理论上这批推荐的内容作者都是未关注的
	RecommendedUnfollowedCreatorStories []*v11.StoryDetail `protobuf:"bytes,3,rep,name=recommended_unfollowed_creator_stories,json=recommendedUnfollowedCreatorStories,proto3" json:"recommended_unfollowed_creator_stories,omitempty"`
	// 当且仅当第一页时，会返回顶部的 10个 除登录用户自己发的 其他portals
	// 注意，其中是包含 user created portals 的
	UserCreatedPortals *v12.UserCreatedPortals `protobuf:"bytes,4,opt,name=user_created_portals,json=userCreatedPortals,proto3" json:"user_created_portals,omitempty"`
	Portals            []*v12.Portal           `protobuf:"bytes,5,rep,name=portals,proto3" json:"portals,omitempty"`
	// 用于下一页的 portals 获取
	PortalListResponse *v1.ListResponse `protobuf:"bytes,6,opt,name=portal_list_response,json=portalListResponse,proto3" json:"portal_list_response,omitempty"`
	// 可能存在的 haunt boo 的展示信息，注意目前是返回数组，但是客户端应该只取第一个
	// 服务端暂时会保证数组也只有一个，后续如果需要支持数组，则需要修改
	HauntBooShowInfos []*v11.HauntBooShowInfo `protobuf:"bytes,7,rep,name=haunt_boo_show_infos,json=hauntBooShowInfos,proto3" json:"haunt_boo_show_infos,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ListFollowingCreatorStoryResponseV2) Reset() {
	*x = ListFollowingCreatorStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListFollowingCreatorStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListFollowingCreatorStoryResponseV2) ProtoMessage() {}

func (x *ListFollowingCreatorStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListFollowingCreatorStoryResponseV2.ProtoReflect.Descriptor instead.
func (*ListFollowingCreatorStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{5}
}

func (x *ListFollowingCreatorStoryResponseV2) GetFollowingCreatorStories() []*v11.StoryDetail {
	if x != nil {
		return x.FollowingCreatorStories
	}
	return nil
}

func (x *ListFollowingCreatorStoryResponseV2) GetListResponse() *v1.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

func (x *ListFollowingCreatorStoryResponseV2) GetRecommendedUnfollowedCreatorStories() []*v11.StoryDetail {
	if x != nil {
		return x.RecommendedUnfollowedCreatorStories
	}
	return nil
}

func (x *ListFollowingCreatorStoryResponseV2) GetUserCreatedPortals() *v12.UserCreatedPortals {
	if x != nil {
		return x.UserCreatedPortals
	}
	return nil
}

func (x *ListFollowingCreatorStoryResponseV2) GetPortals() []*v12.Portal {
	if x != nil {
		return x.Portals
	}
	return nil
}

func (x *ListFollowingCreatorStoryResponseV2) GetPortalListResponse() *v1.ListResponse {
	if x != nil {
		return x.PortalListResponse
	}
	return nil
}

func (x *ListFollowingCreatorStoryResponseV2) GetHauntBooShowInfos() []*v11.HauntBooShowInfo {
	if x != nil {
		return x.HauntBooShowInfos
	}
	return nil
}

// 发布 reveal story 的请求
type CreateExchangeImageStoryRequestV2 struct {
	state      protoimpl.MessageState            `protogen:"open.v1"`
	PlayConfig *v11.StoryPlayExchangeImageConfig `protobuf:"bytes,2,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v13.PrivacySettingUpdateAttr `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	// 从哪个 story 过来创建的
	FromStoryId   *string `protobuf:"bytes,4,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateExchangeImageStoryRequestV2) Reset() {
	*x = CreateExchangeImageStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateExchangeImageStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangeImageStoryRequestV2) ProtoMessage() {}

func (x *CreateExchangeImageStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangeImageStoryRequestV2.ProtoReflect.Descriptor instead.
func (*CreateExchangeImageStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{6}
}

func (x *CreateExchangeImageStoryRequestV2) GetPlayConfig() *v11.StoryPlayExchangeImageConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateExchangeImageStoryRequestV2) GetPrivacySetting() *v13.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *CreateExchangeImageStoryRequestV2) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

type CreateExchangeImageStoryResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateExchangeImageStoryResponseV2) Reset() {
	*x = CreateExchangeImageStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateExchangeImageStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExchangeImageStoryResponseV2) ProtoMessage() {}

func (x *CreateExchangeImageStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExchangeImageStoryResponseV2.ProtoReflect.Descriptor instead.
func (*CreateExchangeImageStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{7}
}

func (x *CreateExchangeImageStoryResponseV2) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布 type story 的请求
type CreateTurtleSoupStoryRequestV2 struct {
	state      protoimpl.MessageState         `protogen:"open.v1"`
	PlayConfig *v11.StoryPlayTurtleSoupConfig `protobuf:"bytes,2,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v13.PrivacySettingUpdateAttr `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateTurtleSoupStoryRequestV2) Reset() {
	*x = CreateTurtleSoupStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTurtleSoupStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTurtleSoupStoryRequestV2) ProtoMessage() {}

func (x *CreateTurtleSoupStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTurtleSoupStoryRequestV2.ProtoReflect.Descriptor instead.
func (*CreateTurtleSoupStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{8}
}

func (x *CreateTurtleSoupStoryRequestV2) GetPlayConfig() *v11.StoryPlayTurtleSoupConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateTurtleSoupStoryRequestV2) GetPrivacySetting() *v13.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

type CreateTurtleSoupStoryResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTurtleSoupStoryResponseV2) Reset() {
	*x = CreateTurtleSoupStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTurtleSoupStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTurtleSoupStoryResponseV2) ProtoMessage() {}

func (x *CreateTurtleSoupStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTurtleSoupStoryResponseV2.ProtoReflect.Descriptor instead.
func (*CreateTurtleSoupStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{9}
}

func (x *CreateTurtleSoupStoryResponseV2) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布 reveal story 的请求
type CreateUnmuteStoryRequestV2 struct {
	state      protoimpl.MessageState            `protogen:"open.v1"`
	PlayConfig *v11.StoryPlayExchangeImageConfig `protobuf:"bytes,2,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v13.PrivacySettingUpdateAttr `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateUnmuteStoryRequestV2) Reset() {
	*x = CreateUnmuteStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUnmuteStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUnmuteStoryRequestV2) ProtoMessage() {}

func (x *CreateUnmuteStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUnmuteStoryRequestV2.ProtoReflect.Descriptor instead.
func (*CreateUnmuteStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{10}
}

func (x *CreateUnmuteStoryRequestV2) GetPlayConfig() *v11.StoryPlayExchangeImageConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateUnmuteStoryRequestV2) GetPrivacySetting() *v13.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

type CreateUnmuteStoryResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUnmuteStoryResponseV2) Reset() {
	*x = CreateUnmuteStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUnmuteStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUnmuteStoryResponseV2) ProtoMessage() {}

func (x *CreateUnmuteStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUnmuteStoryResponseV2.ProtoReflect.Descriptor instead.
func (*CreateUnmuteStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{11}
}

func (x *CreateUnmuteStoryResponseV2) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发布now story
type CreateNowShotStoryRequestV2 struct {
	state      protoimpl.MessageState      `protogen:"open.v1"`
	PlayConfig *v11.StoryPlayNowShotConfig `protobuf:"bytes,2,opt,name=play_config,json=playConfig,proto3" json:"play_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v13.PrivacySettingUpdateAttr `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	// 从哪个 story 过来创建的
	FromStoryId   *string `protobuf:"bytes,4,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNowShotStoryRequestV2) Reset() {
	*x = CreateNowShotStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNowShotStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNowShotStoryRequestV2) ProtoMessage() {}

func (x *CreateNowShotStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNowShotStoryRequestV2.ProtoReflect.Descriptor instead.
func (*CreateNowShotStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{12}
}

func (x *CreateNowShotStoryRequestV2) GetPlayConfig() *v11.StoryPlayNowShotConfig {
	if x != nil {
		return x.PlayConfig
	}
	return nil
}

func (x *CreateNowShotStoryRequestV2) GetPrivacySetting() *v13.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *CreateNowShotStoryRequestV2) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

type CreateNowShotStoryResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNowShotStoryResponseV2) Reset() {
	*x = CreateNowShotStoryResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNowShotStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNowShotStoryResponseV2) ProtoMessage() {}

func (x *CreateNowShotStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNowShotStoryResponseV2.ProtoReflect.Descriptor instead.
func (*CreateNowShotStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{13}
}

func (x *CreateNowShotStoryResponseV2) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumeNowShotStoryRequestV2 struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	StoryId   string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	Succeeded bool                   `protobuf:"varint,2,opt,name=succeeded,proto3" json:"succeeded,omitempty"`
	// 如果succeeded为false则为空
	Resource *v11.Resource `protobuf:"bytes,3,opt,name=resource,proto3" json:"resource,omitempty"`
	// TTL字段，可选，单位秒，如果不传则不存储
	Ttl           *uint32 `protobuf:"varint,4,opt,name=ttl,proto3,oneof" json:"ttl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeNowShotStoryRequestV2) Reset() {
	*x = ConsumeNowShotStoryRequestV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeNowShotStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeNowShotStoryRequestV2) ProtoMessage() {}

func (x *ConsumeNowShotStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeNowShotStoryRequestV2.ProtoReflect.Descriptor instead.
func (*ConsumeNowShotStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{14}
}

func (x *ConsumeNowShotStoryRequestV2) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeNowShotStoryRequestV2) GetSucceeded() bool {
	if x != nil {
		return x.Succeeded
	}
	return false
}

func (x *ConsumeNowShotStoryRequestV2) GetResource() *v11.Resource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *ConsumeNowShotStoryRequestV2) GetTtl() uint32 {
	if x != nil && x.Ttl != nil {
		return *x.Ttl
	}
	return 0
}

type StoryDetailResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StoryDetailResponseV2) Reset() {
	*x = StoryDetailResponseV2{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StoryDetailResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StoryDetailResponseV2) ProtoMessage() {}

func (x *StoryDetailResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StoryDetailResponseV2.ProtoReflect.Descriptor instead.
func (*StoryDetailResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{15}
}

func (x *StoryDetailResponseV2) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

// 发送消息请求
type SendMessageRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	StoryId string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	// 消息类型，用于区分不同的故事类型; deprecated(该字段不被使用，story类型通过story_id获取)
	MessageType       MessageType `protobuf:"varint,2,opt,name=message_type,json=messageType,proto3,enum=api.items.story.v2.MessageType" json:"message_type,omitempty"`
	UserVideoKey      *string     `protobuf:"bytes,3,opt,name=user_video_key,json=userVideoKey,proto3,oneof" json:"user_video_key,omitempty"`
	UserVideoCoverKey *string     `protobuf:"bytes,4,opt,name=user_video_cover_key,json=userVideoCoverKey,proto3,oneof" json:"user_video_cover_key,omitempty"`
	// 消费者故事封面URL
	ConsumerStoryCoverKey *string            `protobuf:"bytes,5,opt,name=consumer_story_cover_key,json=consumerStoryCoverKey,proto3,oneof" json:"consumer_story_cover_key,omitempty"`
	ConsumeStatus         *v14.ConsumeStatus `protobuf:"varint,6,opt,name=consume_status,json=consumeStatus,proto3,enum=api.im.message.types.v1.ConsumeStatus,oneof" json:"consume_status,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{16}
}

func (x *SendMessageRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *SendMessageRequest) GetMessageType() MessageType {
	if x != nil {
		return x.MessageType
	}
	return MessageType_MESSAGE_TYPE_UNSPECIFIED
}

func (x *SendMessageRequest) GetUserVideoKey() string {
	if x != nil && x.UserVideoKey != nil {
		return *x.UserVideoKey
	}
	return ""
}

func (x *SendMessageRequest) GetUserVideoCoverKey() string {
	if x != nil && x.UserVideoCoverKey != nil {
		return *x.UserVideoCoverKey
	}
	return ""
}

func (x *SendMessageRequest) GetConsumerStoryCoverKey() string {
	if x != nil && x.ConsumerStoryCoverKey != nil {
		return *x.ConsumerStoryCoverKey
	}
	return ""
}

func (x *SendMessageRequest) GetConsumeStatus() v14.ConsumeStatus {
	if x != nil && x.ConsumeStatus != nil {
		return *x.ConsumeStatus
	}
	return v14.ConsumeStatus(0)
}

type SendMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{17}
}

func (x *SendMessageResponse) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ListCreatorStoryResponseV2_CreatedStory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Story *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story,proto3" json:"story,omitempty"`
	// 是否置顶
	IsTop         bool `protobuf:"varint,2,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCreatorStoryResponseV2_CreatedStory) Reset() {
	*x = ListCreatorStoryResponseV2_CreatedStory{}
	mi := &file_api_items_story_v2_story_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCreatorStoryResponseV2_CreatedStory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCreatorStoryResponseV2_CreatedStory) ProtoMessage() {}

func (x *ListCreatorStoryResponseV2_CreatedStory) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_story_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCreatorStoryResponseV2_CreatedStory.ProtoReflect.Descriptor instead.
func (*ListCreatorStoryResponseV2_CreatedStory) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_story_proto_rawDescGZIP(), []int{3, 0}
}

func (x *ListCreatorStoryResponseV2_CreatedStory) GetStory() *v11.StoryDetail {
	if x != nil {
		return x.Story
	}
	return nil
}

func (x *ListCreatorStoryResponseV2_CreatedStory) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

var File_api_items_story_v2_story_proto protoreflect.FileDescriptor

const file_api_items_story_v2_story_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/items/story/v2/story.proto\x12\x12api.items.story.v2\x1a\x1aapi/common/v1/common.proto\x1a$api/items/story/types/v1/types.proto\x1a0api/items/story/types/v1/turtle_soup_types.proto\x1a-api/items/story/types/v1/now_shot_types.proto\x1a)api/items/story/types/v1/base_types.proto\x1a.api/items/story/v1/story.privacy.setting.proto\x1a%api/items/portal/types/v1/types.proto\x1a*api/items/story/types/v1/haunt.types.proto\x1a\x17validate/validate.proto\x1a#api/im/message/types/v1/types.proto\"\xe3\x01\n" +
	"\x1aListHomePageStoryRequestV2\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\x12'\n" +
	"\x0fuse_recommended\x18\x02 \x01(\bR\x0euseRecommended\x12S\n" +
	"\x11filter_play_types\x18\x03 \x03(\x0e2'.api.items.story.types.v1.StoryPlayTypeR\x0ffilterPlayTypes\"\x92\x04\n" +
	"\x1bListHomePageStoryResponseV2\x12?\n" +
	"\astories\x18\x01 \x03(\v2%.api.items.story.types.v1.StoryDetailR\astories\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x12&\n" +
	"\x0fsort_request_id\x18\x03 \x01(\tR\rsortRequestId\x12_\n" +
	"\x14user_created_portals\x18\x04 \x01(\v2-.api.items.portal.types.v1.UserCreatedPortalsR\x12userCreatedPortals\x12;\n" +
	"\aportals\x18\x05 \x03(\v2!.api.items.portal.types.v1.PortalR\aportals\x12M\n" +
	"\x14portal_list_response\x18\x06 \x01(\v2\x1b.api.common.v1.ListResponseR\x12portalListResponse\x12[\n" +
	"\x14haunt_boo_show_infos\x18\a \x03(\v2*.api.items.story.types.v1.HauntBooShowInfoR\x11hauntBooShowInfos\"\x94\x01\n" +
	"\x19ListCreatorStoryRequestV2\x12.\n" +
	"\n" +
	"creator_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\tcreatorId\x12G\n" +
	"\flist_request\x18\x02 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xa8\x02\n" +
	"\x1aListCreatorStoryResponseV2\x12d\n" +
	"\x0fcreated_stories\x18\x01 \x03(\v2;.api.items.story.v2.ListCreatorStoryResponseV2.CreatedStoryR\x0ecreatedStories\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x1ab\n" +
	"\fCreatedStory\x12;\n" +
	"\x05story\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\x05story\x12\x15\n" +
	"\x06is_top\x18\x02 \x01(\bR\x05isTop\"m\n" +
	"\"ListFollowingCreatorStoryRequestV2\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\x90\x05\n" +
	"#ListFollowingCreatorStoryResponseV2\x12a\n" +
	"\x19following_creator_stories\x18\x01 \x03(\v2%.api.items.story.types.v1.StoryDetailR\x17followingCreatorStories\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\x12z\n" +
	"&recommended_unfollowed_creator_stories\x18\x03 \x03(\v2%.api.items.story.types.v1.StoryDetailR#recommendedUnfollowedCreatorStories\x12_\n" +
	"\x14user_created_portals\x18\x04 \x01(\v2-.api.items.portal.types.v1.UserCreatedPortalsR\x12userCreatedPortals\x12;\n" +
	"\aportals\x18\x05 \x03(\v2!.api.items.portal.types.v1.PortalR\aportals\x12M\n" +
	"\x14portal_list_response\x18\x06 \x01(\v2\x1b.api.common.v1.ListResponseR\x12portalListResponse\x12[\n" +
	"\x14haunt_boo_show_infos\x18\a \x03(\v2*.api.items.story.types.v1.HauntBooShowInfoR\x11hauntBooShowInfos\"\xb8\x02\n" +
	"!CreateExchangeImageStoryRequestV2\x12W\n" +
	"\vplay_config\x18\x02 \x01(\v26.api.items.story.types.v1.StoryPlayExchangeImageConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01\x128\n" +
	"\rfrom_story_id\x18\x04 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x01R\vfromStoryId\x88\x01\x01B\x12\n" +
	"\x10_privacy_settingB\x10\n" +
	"\x0e_from_story_id\"n\n" +
	"\"CreateExchangeImageStoryResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xe6\x01\n" +
	"\x1eCreateTurtleSoupStoryRequestV2\x12T\n" +
	"\vplay_config\x18\x02 \x01(\v23.api.items.story.types.v1.StoryPlayTurtleSoupConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"k\n" +
	"\x1fCreateTurtleSoupStoryResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xe5\x01\n" +
	"\x1aCreateUnmuteStoryRequestV2\x12W\n" +
	"\vplay_config\x18\x02 \x01(\v26.api.items.story.types.v1.StoryPlayExchangeImageConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"g\n" +
	"\x1bCreateUnmuteStoryResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xac\x02\n" +
	"\x1bCreateNowShotStoryRequestV2\x12Q\n" +
	"\vplay_config\x18\x02 \x01(\v20.api.items.story.types.v1.StoryPlayNowShotConfigR\n" +
	"playConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01\x128\n" +
	"\rfrom_story_id\x18\x04 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x01R\vfromStoryId\x88\x01\x01B\x12\n" +
	"\x10_privacy_settingB\x10\n" +
	"\x0e_from_story_id\"h\n" +
	"\x1cCreateNowShotStoryResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xb6\x01\n" +
	"\x1cConsumeNowShotStoryRequestV2\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12\x1c\n" +
	"\tsucceeded\x18\x02 \x01(\bR\tsucceeded\x12>\n" +
	"\bresource\x18\x03 \x01(\v2\".api.items.story.types.v1.ResourceR\bresource\x12\x15\n" +
	"\x03ttl\x18\x04 \x01(\rH\x00R\x03ttl\x88\x01\x01B\x06\n" +
	"\x04_ttl\"a\n" +
	"\x15StoryDetailResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xd3\x03\n" +
	"\x12SendMessageRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12B\n" +
	"\fmessage_type\x18\x02 \x01(\x0e2\x1f.api.items.story.v2.MessageTypeR\vmessageType\x12)\n" +
	"\x0euser_video_key\x18\x03 \x01(\tH\x00R\fuserVideoKey\x88\x01\x01\x124\n" +
	"\x14user_video_cover_key\x18\x04 \x01(\tH\x01R\x11userVideoCoverKey\x88\x01\x01\x12<\n" +
	"\x18consumer_story_cover_key\x18\x05 \x01(\tH\x02R\x15consumerStoryCoverKey\x88\x01\x01\x12R\n" +
	"\x0econsume_status\x18\x06 \x01(\x0e2&.api.im.message.types.v1.ConsumeStatusH\x03R\rconsumeStatus\x88\x01\x01B\x11\n" +
	"\x0f_user_video_keyB\x17\n" +
	"\x15_user_video_cover_keyB\x1b\n" +
	"\x19_consumer_story_cover_keyB\x11\n" +
	"\x0f_consume_status\"_\n" +
	"\x13SendMessageResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail*\x95\x01\n" +
	"\vMessageType\x12\x1c\n" +
	"\x18MESSAGE_TYPE_UNSPECIFIED\x10\x00\x12\x17\n" +
	"\x13MESSAGE_TYPE_WASSUP\x10\x01\x12\x1a\n" +
	"\x16MESSAGE_TYPE_CHATPROXY\x10\x02\x12\x18\n" +
	"\x14MESSAGE_TYPE_ROASTED\x10\x03\x12\x19\n" +
	"\x15MESSAGE_TYPE_BASEPLAY\x10\x04B-Z+boson/api/items/story/v2;api_items_story_v2b\x06proto3"

var (
	file_api_items_story_v2_story_proto_rawDescOnce sync.Once
	file_api_items_story_v2_story_proto_rawDescData []byte
)

func file_api_items_story_v2_story_proto_rawDescGZIP() []byte {
	file_api_items_story_v2_story_proto_rawDescOnce.Do(func() {
		file_api_items_story_v2_story_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v2_story_proto_rawDesc), len(file_api_items_story_v2_story_proto_rawDesc)))
	})
	return file_api_items_story_v2_story_proto_rawDescData
}

var file_api_items_story_v2_story_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_items_story_v2_story_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_items_story_v2_story_proto_goTypes = []any{
	(MessageType)(0),                                // 0: api.items.story.v2.MessageType
	(*ListHomePageStoryRequestV2)(nil),              // 1: api.items.story.v2.ListHomePageStoryRequestV2
	(*ListHomePageStoryResponseV2)(nil),             // 2: api.items.story.v2.ListHomePageStoryResponseV2
	(*ListCreatorStoryRequestV2)(nil),               // 3: api.items.story.v2.ListCreatorStoryRequestV2
	(*ListCreatorStoryResponseV2)(nil),              // 4: api.items.story.v2.ListCreatorStoryResponseV2
	(*ListFollowingCreatorStoryRequestV2)(nil),      // 5: api.items.story.v2.ListFollowingCreatorStoryRequestV2
	(*ListFollowingCreatorStoryResponseV2)(nil),     // 6: api.items.story.v2.ListFollowingCreatorStoryResponseV2
	(*CreateExchangeImageStoryRequestV2)(nil),       // 7: api.items.story.v2.CreateExchangeImageStoryRequestV2
	(*CreateExchangeImageStoryResponseV2)(nil),      // 8: api.items.story.v2.CreateExchangeImageStoryResponseV2
	(*CreateTurtleSoupStoryRequestV2)(nil),          // 9: api.items.story.v2.CreateTurtleSoupStoryRequestV2
	(*CreateTurtleSoupStoryResponseV2)(nil),         // 10: api.items.story.v2.CreateTurtleSoupStoryResponseV2
	(*CreateUnmuteStoryRequestV2)(nil),              // 11: api.items.story.v2.CreateUnmuteStoryRequestV2
	(*CreateUnmuteStoryResponseV2)(nil),             // 12: api.items.story.v2.CreateUnmuteStoryResponseV2
	(*CreateNowShotStoryRequestV2)(nil),             // 13: api.items.story.v2.CreateNowShotStoryRequestV2
	(*CreateNowShotStoryResponseV2)(nil),            // 14: api.items.story.v2.CreateNowShotStoryResponseV2
	(*ConsumeNowShotStoryRequestV2)(nil),            // 15: api.items.story.v2.ConsumeNowShotStoryRequestV2
	(*StoryDetailResponseV2)(nil),                   // 16: api.items.story.v2.StoryDetailResponseV2
	(*SendMessageRequest)(nil),                      // 17: api.items.story.v2.SendMessageRequest
	(*SendMessageResponse)(nil),                     // 18: api.items.story.v2.SendMessageResponse
	(*ListCreatorStoryResponseV2_CreatedStory)(nil), // 19: api.items.story.v2.ListCreatorStoryResponseV2.CreatedStory
	(*v1.ListRequest)(nil),                          // 20: api.common.v1.ListRequest
	(v11.StoryPlayType)(0),                          // 21: api.items.story.types.v1.StoryPlayType
	(*v11.StoryDetail)(nil),                         // 22: api.items.story.types.v1.StoryDetail
	(*v1.ListResponse)(nil),                         // 23: api.common.v1.ListResponse
	(*v12.UserCreatedPortals)(nil),                  // 24: api.items.portal.types.v1.UserCreatedPortals
	(*v12.Portal)(nil),                              // 25: api.items.portal.types.v1.Portal
	(*v11.HauntBooShowInfo)(nil),                    // 26: api.items.story.types.v1.HauntBooShowInfo
	(*v11.StoryPlayExchangeImageConfig)(nil),        // 27: api.items.story.types.v1.StoryPlayExchangeImageConfig
	(*v13.PrivacySettingUpdateAttr)(nil),            // 28: api.items.story.v1.PrivacySettingUpdateAttr
	(*v11.StoryPlayTurtleSoupConfig)(nil),           // 29: api.items.story.types.v1.StoryPlayTurtleSoupConfig
	(*v11.StoryPlayNowShotConfig)(nil),              // 30: api.items.story.types.v1.StoryPlayNowShotConfig
	(*v11.Resource)(nil),                            // 31: api.items.story.types.v1.Resource
	(v14.ConsumeStatus)(0),                          // 32: api.im.message.types.v1.ConsumeStatus
}
var file_api_items_story_v2_story_proto_depIdxs = []int32{
	20, // 0: api.items.story.v2.ListHomePageStoryRequestV2.list_request:type_name -> api.common.v1.ListRequest
	21, // 1: api.items.story.v2.ListHomePageStoryRequestV2.filter_play_types:type_name -> api.items.story.types.v1.StoryPlayType
	22, // 2: api.items.story.v2.ListHomePageStoryResponseV2.stories:type_name -> api.items.story.types.v1.StoryDetail
	23, // 3: api.items.story.v2.ListHomePageStoryResponseV2.list_response:type_name -> api.common.v1.ListResponse
	24, // 4: api.items.story.v2.ListHomePageStoryResponseV2.user_created_portals:type_name -> api.items.portal.types.v1.UserCreatedPortals
	25, // 5: api.items.story.v2.ListHomePageStoryResponseV2.portals:type_name -> api.items.portal.types.v1.Portal
	23, // 6: api.items.story.v2.ListHomePageStoryResponseV2.portal_list_response:type_name -> api.common.v1.ListResponse
	26, // 7: api.items.story.v2.ListHomePageStoryResponseV2.haunt_boo_show_infos:type_name -> api.items.story.types.v1.HauntBooShowInfo
	20, // 8: api.items.story.v2.ListCreatorStoryRequestV2.list_request:type_name -> api.common.v1.ListRequest
	19, // 9: api.items.story.v2.ListCreatorStoryResponseV2.created_stories:type_name -> api.items.story.v2.ListCreatorStoryResponseV2.CreatedStory
	23, // 10: api.items.story.v2.ListCreatorStoryResponseV2.list_response:type_name -> api.common.v1.ListResponse
	20, // 11: api.items.story.v2.ListFollowingCreatorStoryRequestV2.list_request:type_name -> api.common.v1.ListRequest
	22, // 12: api.items.story.v2.ListFollowingCreatorStoryResponseV2.following_creator_stories:type_name -> api.items.story.types.v1.StoryDetail
	23, // 13: api.items.story.v2.ListFollowingCreatorStoryResponseV2.list_response:type_name -> api.common.v1.ListResponse
	22, // 14: api.items.story.v2.ListFollowingCreatorStoryResponseV2.recommended_unfollowed_creator_stories:type_name -> api.items.story.types.v1.StoryDetail
	24, // 15: api.items.story.v2.ListFollowingCreatorStoryResponseV2.user_created_portals:type_name -> api.items.portal.types.v1.UserCreatedPortals
	25, // 16: api.items.story.v2.ListFollowingCreatorStoryResponseV2.portals:type_name -> api.items.portal.types.v1.Portal
	23, // 17: api.items.story.v2.ListFollowingCreatorStoryResponseV2.portal_list_response:type_name -> api.common.v1.ListResponse
	26, // 18: api.items.story.v2.ListFollowingCreatorStoryResponseV2.haunt_boo_show_infos:type_name -> api.items.story.types.v1.HauntBooShowInfo
	27, // 19: api.items.story.v2.CreateExchangeImageStoryRequestV2.play_config:type_name -> api.items.story.types.v1.StoryPlayExchangeImageConfig
	28, // 20: api.items.story.v2.CreateExchangeImageStoryRequestV2.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	22, // 21: api.items.story.v2.CreateExchangeImageStoryResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	29, // 22: api.items.story.v2.CreateTurtleSoupStoryRequestV2.play_config:type_name -> api.items.story.types.v1.StoryPlayTurtleSoupConfig
	28, // 23: api.items.story.v2.CreateTurtleSoupStoryRequestV2.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	22, // 24: api.items.story.v2.CreateTurtleSoupStoryResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	27, // 25: api.items.story.v2.CreateUnmuteStoryRequestV2.play_config:type_name -> api.items.story.types.v1.StoryPlayExchangeImageConfig
	28, // 26: api.items.story.v2.CreateUnmuteStoryRequestV2.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	22, // 27: api.items.story.v2.CreateUnmuteStoryResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	30, // 28: api.items.story.v2.CreateNowShotStoryRequestV2.play_config:type_name -> api.items.story.types.v1.StoryPlayNowShotConfig
	28, // 29: api.items.story.v2.CreateNowShotStoryRequestV2.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	22, // 30: api.items.story.v2.CreateNowShotStoryResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	31, // 31: api.items.story.v2.ConsumeNowShotStoryRequestV2.resource:type_name -> api.items.story.types.v1.Resource
	22, // 32: api.items.story.v2.StoryDetailResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	0,  // 33: api.items.story.v2.SendMessageRequest.message_type:type_name -> api.items.story.v2.MessageType
	32, // 34: api.items.story.v2.SendMessageRequest.consume_status:type_name -> api.im.message.types.v1.ConsumeStatus
	22, // 35: api.items.story.v2.SendMessageResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	22, // 36: api.items.story.v2.ListCreatorStoryResponseV2.CreatedStory.story:type_name -> api.items.story.types.v1.StoryDetail
	37, // [37:37] is the sub-list for method output_type
	37, // [37:37] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_api_items_story_v2_story_proto_init() }
func file_api_items_story_v2_story_proto_init() {
	if File_api_items_story_v2_story_proto != nil {
		return
	}
	file_api_items_story_v2_story_proto_msgTypes[6].OneofWrappers = []any{}
	file_api_items_story_v2_story_proto_msgTypes[8].OneofWrappers = []any{}
	file_api_items_story_v2_story_proto_msgTypes[10].OneofWrappers = []any{}
	file_api_items_story_v2_story_proto_msgTypes[12].OneofWrappers = []any{}
	file_api_items_story_v2_story_proto_msgTypes[14].OneofWrappers = []any{}
	file_api_items_story_v2_story_proto_msgTypes[16].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v2_story_proto_rawDesc), len(file_api_items_story_v2_story_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v2_story_proto_goTypes,
		DependencyIndexes: file_api_items_story_v2_story_proto_depIdxs,
		EnumInfos:         file_api_items_story_v2_story_proto_enumTypes,
		MessageInfos:      file_api_items_story_v2_story_proto_msgTypes,
	}.Build()
	File_api_items_story_v2_story_proto = out.File
	file_api_items_story_v2_story_proto_goTypes = nil
	file_api_items_story_v2_story_proto_depIdxs = nil
}
