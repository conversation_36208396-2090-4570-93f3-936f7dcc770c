// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v2/wassup.proto

package api_items_story_v2

import (
	v1 "boson/api/items/story/types/v1"
	v11 "boson/api/items/story/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetWassupGreetingsRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ImageKeys []string               `protobuf:"bytes,1,rep,name=image_keys,json=imageKeys,proto3" json:"image_keys,omitempty"`
	// 如果视频，那么 image_keys 长度将大于1，且客户端需要把视频音频 asr 结果给到服务端
	// 此值可能为空
	AudioAsrResult *string `protobuf:"bytes,2,opt,name=audio_asr_result,json=audioAsrResult,proto3,oneof" json:"audio_asr_result,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetWassupGreetingsRequest) Reset() {
	*x = GetWassupGreetingsRequest{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWassupGreetingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWassupGreetingsRequest) ProtoMessage() {}

func (x *GetWassupGreetingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWassupGreetingsRequest.ProtoReflect.Descriptor instead.
func (*GetWassupGreetingsRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{0}
}

func (x *GetWassupGreetingsRequest) GetImageKeys() []string {
	if x != nil {
		return x.ImageKeys
	}
	return nil
}

func (x *GetWassupGreetingsRequest) GetAudioAsrResult() string {
	if x != nil && x.AudioAsrResult != nil {
		return *x.AudioAsrResult
	}
	return ""
}

type GetWassupGreetingsResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Greeting      *v1.StoryPlayWassupBotMessage `protobuf:"bytes,1,opt,name=greeting,proto3" json:"greeting,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWassupGreetingsResponse) Reset() {
	*x = GetWassupGreetingsResponse{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWassupGreetingsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWassupGreetingsResponse) ProtoMessage() {}

func (x *GetWassupGreetingsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWassupGreetingsResponse.ProtoReflect.Descriptor instead.
func (*GetWassupGreetingsResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{1}
}

func (x *GetWassupGreetingsResponse) GetGreeting() *v1.StoryPlayWassupBotMessage {
	if x != nil {
		return x.Greeting
	}
	return nil
}

type GetWassupNextQuestionRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserAudioKey  string                 `protobuf:"bytes,1,opt,name=user_audio_key,json=userAudioKey,proto3" json:"user_audio_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWassupNextQuestionRequest) Reset() {
	*x = GetWassupNextQuestionRequest{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWassupNextQuestionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWassupNextQuestionRequest) ProtoMessage() {}

func (x *GetWassupNextQuestionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWassupNextQuestionRequest.ProtoReflect.Descriptor instead.
func (*GetWassupNextQuestionRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{2}
}

func (x *GetWassupNextQuestionRequest) GetUserAudioKey() string {
	if x != nil {
		return x.UserAudioKey
	}
	return ""
}

type GetWassupNextQuestionResponse struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Message       *v1.StoryPlayWassupBotMessage `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	IsEnd         bool                          `protobuf:"varint,2,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetWassupNextQuestionResponse) Reset() {
	*x = GetWassupNextQuestionResponse{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetWassupNextQuestionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWassupNextQuestionResponse) ProtoMessage() {}

func (x *GetWassupNextQuestionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWassupNextQuestionResponse.ProtoReflect.Descriptor instead.
func (*GetWassupNextQuestionResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{3}
}

func (x *GetWassupNextQuestionResponse) GetMessage() *v1.StoryPlayWassupBotMessage {
	if x != nil {
		return x.Message
	}
	return nil
}

func (x *GetWassupNextQuestionResponse) GetIsEnd() bool {
	if x != nil {
		return x.IsEnd
	}
	return false
}

type CreateWassupStoryRequest struct {
	state  protoimpl.MessageState    `protogen:"open.v1"`
	Config *v1.StoryPlayWassupConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v11.PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	// 从哪个 story 过来创建的
	FromStoryId   *string `protobuf:"bytes,3,opt,name=from_story_id,json=fromStoryId,proto3,oneof" json:"from_story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWassupStoryRequest) Reset() {
	*x = CreateWassupStoryRequest{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWassupStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWassupStoryRequest) ProtoMessage() {}

func (x *CreateWassupStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWassupStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateWassupStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{4}
}

func (x *CreateWassupStoryRequest) GetConfig() *v1.StoryPlayWassupConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *CreateWassupStoryRequest) GetPrivacySetting() *v11.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *CreateWassupStoryRequest) GetFromStoryId() string {
	if x != nil && x.FromStoryId != nil {
		return *x.FromStoryId
	}
	return ""
}

type CreateWassupStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateWassupStoryResponse) Reset() {
	*x = CreateWassupStoryResponse{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateWassupStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateWassupStoryResponse) ProtoMessage() {}

func (x *CreateWassupStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateWassupStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateWassupStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{5}
}

func (x *CreateWassupStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumeWassupStoryRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	StoryId           string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	UserVideoKey      string                 `protobuf:"bytes,2,opt,name=user_video_key,json=userVideoKey,proto3" json:"user_video_key,omitempty"`
	UserVideoCoverKey string                 `protobuf:"bytes,3,opt,name=user_video_cover_key,json=userVideoCoverKey,proto3" json:"user_video_cover_key,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ConsumeWassupStoryRequest) Reset() {
	*x = ConsumeWassupStoryRequest{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeWassupStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeWassupStoryRequest) ProtoMessage() {}

func (x *ConsumeWassupStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeWassupStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeWassupStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{6}
}

func (x *ConsumeWassupStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeWassupStoryRequest) GetUserVideoKey() string {
	if x != nil {
		return x.UserVideoKey
	}
	return ""
}

func (x *ConsumeWassupStoryRequest) GetUserVideoCoverKey() string {
	if x != nil {
		return x.UserVideoCoverKey
	}
	return ""
}

type ConsumeWassupStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeWassupStoryResponse) Reset() {
	*x = ConsumeWassupStoryResponse{}
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeWassupStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeWassupStoryResponse) ProtoMessage() {}

func (x *ConsumeWassupStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_wassup_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeWassupStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeWassupStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_wassup_proto_rawDescGZIP(), []int{7}
}

func (x *ConsumeWassupStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

var File_api_items_story_v2_wassup_proto protoreflect.FileDescriptor

const file_api_items_story_v2_wassup_proto_rawDesc = "" +
	"\n" +
	"\x1fapi/items/story/v2/wassup.proto\x12\x12api.items.story.v2\x1a+api/items/story/types/v1/wassup.types.proto\x1a\x17validate/validate.proto\x1a$api/items/story/types/v1/types.proto\x1a.api/items/story/v1/story.privacy.setting.proto\"~\n" +
	"\x19GetWassupGreetingsRequest\x12\x1d\n" +
	"\n" +
	"image_keys\x18\x01 \x03(\tR\timageKeys\x12-\n" +
	"\x10audio_asr_result\x18\x02 \x01(\tH\x00R\x0eaudioAsrResult\x88\x01\x01B\x13\n" +
	"\x11_audio_asr_result\"m\n" +
	"\x1aGetWassupGreetingsResponse\x12O\n" +
	"\bgreeting\x18\x01 \x01(\v23.api.items.story.types.v1.StoryPlayWassupBotMessageR\bgreeting\"D\n" +
	"\x1cGetWassupNextQuestionRequest\x12$\n" +
	"\x0euser_audio_key\x18\x01 \x01(\tR\fuserAudioKey\"\x85\x01\n" +
	"\x1dGetWassupNextQuestionResponse\x12M\n" +
	"\amessage\x18\x01 \x01(\v23.api.items.story.types.v1.StoryPlayWassupBotMessageR\amessage\x12\x15\n" +
	"\x06is_end\x18\x02 \x01(\bR\x05isEnd\"\x9f\x02\n" +
	"\x18CreateWassupStoryRequest\x12G\n" +
	"\x06config\x18\x01 \x01(\v2/.api.items.story.types.v1.StoryPlayWassupConfigR\x06config\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01\x128\n" +
	"\rfrom_story_id\x18\x03 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$H\x01R\vfromStoryId\x88\x01\x01B\x12\n" +
	"\x10_privacy_settingB\x10\n" +
	"\x0e_from_story_id\"e\n" +
	"\x19CreateWassupStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\x9e\x01\n" +
	"\x19ConsumeWassupStoryRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12$\n" +
	"\x0euser_video_key\x18\x02 \x01(\tR\fuserVideoKey\x12/\n" +
	"\x14user_video_cover_key\x18\x03 \x01(\tR\x11userVideoCoverKey\"f\n" +
	"\x1aConsumeWassupStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetailB-Z+boson/api/items/story/v2;api_items_story_v2b\x06proto3"

var (
	file_api_items_story_v2_wassup_proto_rawDescOnce sync.Once
	file_api_items_story_v2_wassup_proto_rawDescData []byte
)

func file_api_items_story_v2_wassup_proto_rawDescGZIP() []byte {
	file_api_items_story_v2_wassup_proto_rawDescOnce.Do(func() {
		file_api_items_story_v2_wassup_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v2_wassup_proto_rawDesc), len(file_api_items_story_v2_wassup_proto_rawDesc)))
	})
	return file_api_items_story_v2_wassup_proto_rawDescData
}

var file_api_items_story_v2_wassup_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_items_story_v2_wassup_proto_goTypes = []any{
	(*GetWassupGreetingsRequest)(nil),     // 0: api.items.story.v2.GetWassupGreetingsRequest
	(*GetWassupGreetingsResponse)(nil),    // 1: api.items.story.v2.GetWassupGreetingsResponse
	(*GetWassupNextQuestionRequest)(nil),  // 2: api.items.story.v2.GetWassupNextQuestionRequest
	(*GetWassupNextQuestionResponse)(nil), // 3: api.items.story.v2.GetWassupNextQuestionResponse
	(*CreateWassupStoryRequest)(nil),      // 4: api.items.story.v2.CreateWassupStoryRequest
	(*CreateWassupStoryResponse)(nil),     // 5: api.items.story.v2.CreateWassupStoryResponse
	(*ConsumeWassupStoryRequest)(nil),     // 6: api.items.story.v2.ConsumeWassupStoryRequest
	(*ConsumeWassupStoryResponse)(nil),    // 7: api.items.story.v2.ConsumeWassupStoryResponse
	(*v1.StoryPlayWassupBotMessage)(nil),  // 8: api.items.story.types.v1.StoryPlayWassupBotMessage
	(*v1.StoryPlayWassupConfig)(nil),      // 9: api.items.story.types.v1.StoryPlayWassupConfig
	(*v11.PrivacySettingUpdateAttr)(nil),  // 10: api.items.story.v1.PrivacySettingUpdateAttr
	(*v1.StoryDetail)(nil),                // 11: api.items.story.types.v1.StoryDetail
}
var file_api_items_story_v2_wassup_proto_depIdxs = []int32{
	8,  // 0: api.items.story.v2.GetWassupGreetingsResponse.greeting:type_name -> api.items.story.types.v1.StoryPlayWassupBotMessage
	8,  // 1: api.items.story.v2.GetWassupNextQuestionResponse.message:type_name -> api.items.story.types.v1.StoryPlayWassupBotMessage
	9,  // 2: api.items.story.v2.CreateWassupStoryRequest.config:type_name -> api.items.story.types.v1.StoryPlayWassupConfig
	10, // 3: api.items.story.v2.CreateWassupStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	11, // 4: api.items.story.v2.CreateWassupStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	11, // 5: api.items.story.v2.ConsumeWassupStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	6,  // [6:6] is the sub-list for method output_type
	6,  // [6:6] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_api_items_story_v2_wassup_proto_init() }
func file_api_items_story_v2_wassup_proto_init() {
	if File_api_items_story_v2_wassup_proto != nil {
		return
	}
	file_api_items_story_v2_wassup_proto_msgTypes[0].OneofWrappers = []any{}
	file_api_items_story_v2_wassup_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v2_wassup_proto_rawDesc), len(file_api_items_story_v2_wassup_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v2_wassup_proto_goTypes,
		DependencyIndexes: file_api_items_story_v2_wassup_proto_depIdxs,
		MessageInfos:      file_api_items_story_v2_wassup_proto_msgTypes,
	}.Build()
	File_api_items_story_v2_wassup_proto = out.File
	file_api_items_story_v2_wassup_proto_goTypes = nil
	file_api_items_story_v2_wassup_proto_depIdxs = nil
}
