// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v2/wassup.proto

package api_items_story_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetWassupGreetingsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWassupGreetingsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWassupGreetingsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWassupGreetingsRequestMultiError, or nil if none found.
func (m *GetWassupGreetingsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWassupGreetingsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.AudioAsrResult != nil {
		// no validation rules for AudioAsrResult
	}

	if len(errors) > 0 {
		return GetWassupGreetingsRequestMultiError(errors)
	}

	return nil
}

// GetWassupGreetingsRequestMultiError is an error wrapping multiple validation
// errors returned by GetWassupGreetingsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetWassupGreetingsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWassupGreetingsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWassupGreetingsRequestMultiError) AllErrors() []error { return m }

// GetWassupGreetingsRequestValidationError is the validation error returned by
// GetWassupGreetingsRequest.Validate if the designated constraints aren't met.
type GetWassupGreetingsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWassupGreetingsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWassupGreetingsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWassupGreetingsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWassupGreetingsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWassupGreetingsRequestValidationError) ErrorName() string {
	return "GetWassupGreetingsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWassupGreetingsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWassupGreetingsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWassupGreetingsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWassupGreetingsRequestValidationError{}

// Validate checks the field values on GetWassupGreetingsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWassupGreetingsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWassupGreetingsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWassupGreetingsResponseMultiError, or nil if none found.
func (m *GetWassupGreetingsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWassupGreetingsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetGreeting()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWassupGreetingsResponseValidationError{
					field:  "Greeting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWassupGreetingsResponseValidationError{
					field:  "Greeting",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGreeting()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWassupGreetingsResponseValidationError{
				field:  "Greeting",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetWassupGreetingsResponseMultiError(errors)
	}

	return nil
}

// GetWassupGreetingsResponseMultiError is an error wrapping multiple
// validation errors returned by GetWassupGreetingsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetWassupGreetingsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWassupGreetingsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWassupGreetingsResponseMultiError) AllErrors() []error { return m }

// GetWassupGreetingsResponseValidationError is the validation error returned
// by GetWassupGreetingsResponse.Validate if the designated constraints aren't met.
type GetWassupGreetingsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWassupGreetingsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWassupGreetingsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWassupGreetingsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWassupGreetingsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWassupGreetingsResponseValidationError) ErrorName() string {
	return "GetWassupGreetingsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWassupGreetingsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWassupGreetingsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWassupGreetingsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWassupGreetingsResponseValidationError{}

// Validate checks the field values on GetWassupNextQuestionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWassupNextQuestionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWassupNextQuestionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetWassupNextQuestionRequestMultiError, or nil if none found.
func (m *GetWassupNextQuestionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWassupNextQuestionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserAudioKey

	if len(errors) > 0 {
		return GetWassupNextQuestionRequestMultiError(errors)
	}

	return nil
}

// GetWassupNextQuestionRequestMultiError is an error wrapping multiple
// validation errors returned by GetWassupNextQuestionRequest.ValidateAll() if
// the designated constraints aren't met.
type GetWassupNextQuestionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWassupNextQuestionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWassupNextQuestionRequestMultiError) AllErrors() []error { return m }

// GetWassupNextQuestionRequestValidationError is the validation error returned
// by GetWassupNextQuestionRequest.Validate if the designated constraints
// aren't met.
type GetWassupNextQuestionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWassupNextQuestionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWassupNextQuestionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWassupNextQuestionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWassupNextQuestionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWassupNextQuestionRequestValidationError) ErrorName() string {
	return "GetWassupNextQuestionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetWassupNextQuestionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWassupNextQuestionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWassupNextQuestionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWassupNextQuestionRequestValidationError{}

// Validate checks the field values on GetWassupNextQuestionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetWassupNextQuestionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetWassupNextQuestionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetWassupNextQuestionResponseMultiError, or nil if none found.
func (m *GetWassupNextQuestionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetWassupNextQuestionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetWassupNextQuestionResponseValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetWassupNextQuestionResponseValidationError{
					field:  "Message",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetWassupNextQuestionResponseValidationError{
				field:  "Message",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEnd

	if len(errors) > 0 {
		return GetWassupNextQuestionResponseMultiError(errors)
	}

	return nil
}

// GetWassupNextQuestionResponseMultiError is an error wrapping multiple
// validation errors returned by GetWassupNextQuestionResponse.ValidateAll()
// if the designated constraints aren't met.
type GetWassupNextQuestionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetWassupNextQuestionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetWassupNextQuestionResponseMultiError) AllErrors() []error { return m }

// GetWassupNextQuestionResponseValidationError is the validation error
// returned by GetWassupNextQuestionResponse.Validate if the designated
// constraints aren't met.
type GetWassupNextQuestionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetWassupNextQuestionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetWassupNextQuestionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetWassupNextQuestionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetWassupNextQuestionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetWassupNextQuestionResponseValidationError) ErrorName() string {
	return "GetWassupNextQuestionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetWassupNextQuestionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetWassupNextQuestionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetWassupNextQuestionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetWassupNextQuestionResponseValidationError{}

// Validate checks the field values on CreateWassupStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateWassupStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateWassupStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateWassupStoryRequestMultiError, or nil if none found.
func (m *CreateWassupStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateWassupStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateWassupStoryRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateWassupStoryRequestValidationError{
					field:  "Config",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateWassupStoryRequestValidationError{
				field:  "Config",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateWassupStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateWassupStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateWassupStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.FromStoryId != nil {

		if !_CreateWassupStoryRequest_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := CreateWassupStoryRequestValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateWassupStoryRequestMultiError(errors)
	}

	return nil
}

// CreateWassupStoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreateWassupStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateWassupStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateWassupStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateWassupStoryRequestMultiError) AllErrors() []error { return m }

// CreateWassupStoryRequestValidationError is the validation error returned by
// CreateWassupStoryRequest.Validate if the designated constraints aren't met.
type CreateWassupStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateWassupStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateWassupStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateWassupStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateWassupStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateWassupStoryRequestValidationError) ErrorName() string {
	return "CreateWassupStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateWassupStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateWassupStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateWassupStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateWassupStoryRequestValidationError{}

var _CreateWassupStoryRequest_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateWassupStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateWassupStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateWassupStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateWassupStoryResponseMultiError, or nil if none found.
func (m *CreateWassupStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateWassupStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateWassupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateWassupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateWassupStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateWassupStoryResponseMultiError(errors)
	}

	return nil
}

// CreateWassupStoryResponseMultiError is an error wrapping multiple validation
// errors returned by CreateWassupStoryResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateWassupStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateWassupStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateWassupStoryResponseMultiError) AllErrors() []error { return m }

// CreateWassupStoryResponseValidationError is the validation error returned by
// CreateWassupStoryResponse.Validate if the designated constraints aren't met.
type CreateWassupStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateWassupStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateWassupStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateWassupStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateWassupStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateWassupStoryResponseValidationError) ErrorName() string {
	return "CreateWassupStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateWassupStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateWassupStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateWassupStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateWassupStoryResponseValidationError{}

// Validate checks the field values on ConsumeWassupStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeWassupStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeWassupStoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeWassupStoryRequestMultiError, or nil if none found.
func (m *ConsumeWassupStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeWassupStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ConsumeWassupStoryRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := ConsumeWassupStoryRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UserVideoKey

	// no validation rules for UserVideoCoverKey

	if len(errors) > 0 {
		return ConsumeWassupStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumeWassupStoryRequestMultiError is an error wrapping multiple validation
// errors returned by ConsumeWassupStoryRequest.ValidateAll() if the
// designated constraints aren't met.
type ConsumeWassupStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeWassupStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeWassupStoryRequestMultiError) AllErrors() []error { return m }

// ConsumeWassupStoryRequestValidationError is the validation error returned by
// ConsumeWassupStoryRequest.Validate if the designated constraints aren't met.
type ConsumeWassupStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeWassupStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeWassupStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeWassupStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeWassupStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeWassupStoryRequestValidationError) ErrorName() string {
	return "ConsumeWassupStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeWassupStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeWassupStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeWassupStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeWassupStoryRequestValidationError{}

var _ConsumeWassupStoryRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ConsumeWassupStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeWassupStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeWassupStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeWassupStoryResponseMultiError, or nil if none found.
func (m *ConsumeWassupStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeWassupStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeWassupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeWassupStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeWassupStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeWassupStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumeWassupStoryResponseMultiError is an error wrapping multiple
// validation errors returned by ConsumeWassupStoryResponse.ValidateAll() if
// the designated constraints aren't met.
type ConsumeWassupStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeWassupStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeWassupStoryResponseMultiError) AllErrors() []error { return m }

// ConsumeWassupStoryResponseValidationError is the validation error returned
// by ConsumeWassupStoryResponse.Validate if the designated constraints aren't met.
type ConsumeWassupStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeWassupStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeWassupStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeWassupStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeWassupStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeWassupStoryResponseValidationError) ErrorName() string {
	return "ConsumeWassupStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeWassupStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeWassupStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeWassupStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeWassupStoryResponseValidationError{}
