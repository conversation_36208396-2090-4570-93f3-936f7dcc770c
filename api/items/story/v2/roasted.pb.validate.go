// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v2/roasted.proto

package api_items_story_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ConsumeRoastedStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeRoastedStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeRoastedStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeRoastedStoryRequestV2MultiError, or nil if none found.
func (m *ConsumeRoastedStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeRoastedStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ConsumeRoastedStoryRequestV2_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := ConsumeRoastedStoryRequestV2ValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ConsumeRoastedStoryRequestV2MultiError(errors)
	}

	return nil
}

// ConsumeRoastedStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by ConsumeRoastedStoryRequestV2.ValidateAll() if
// the designated constraints aren't met.
type ConsumeRoastedStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeRoastedStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeRoastedStoryRequestV2MultiError) AllErrors() []error { return m }

// ConsumeRoastedStoryRequestV2ValidationError is the validation error returned
// by ConsumeRoastedStoryRequestV2.Validate if the designated constraints
// aren't met.
type ConsumeRoastedStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeRoastedStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeRoastedStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeRoastedStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeRoastedStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeRoastedStoryRequestV2ValidationError) ErrorName() string {
	return "ConsumeRoastedStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeRoastedStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeRoastedStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeRoastedStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeRoastedStoryRequestV2ValidationError{}

var _ConsumeRoastedStoryRequestV2_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ConsumeRoastedStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeRoastedStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeRoastedStoryResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ConsumeRoastedStoryResponseV2MultiError, or nil if none found.
func (m *ConsumeRoastedStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeRoastedStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeRoastedStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeRoastedStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeRoastedStoryResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeRoastedStoryResponseV2MultiError(errors)
	}

	return nil
}

// ConsumeRoastedStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by ConsumeRoastedStoryResponseV2.ValidateAll()
// if the designated constraints aren't met.
type ConsumeRoastedStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeRoastedStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeRoastedStoryResponseV2MultiError) AllErrors() []error { return m }

// ConsumeRoastedStoryResponseV2ValidationError is the validation error
// returned by ConsumeRoastedStoryResponseV2.Validate if the designated
// constraints aren't met.
type ConsumeRoastedStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeRoastedStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeRoastedStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeRoastedStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeRoastedStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeRoastedStoryResponseV2ValidationError) ErrorName() string {
	return "ConsumeRoastedStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeRoastedStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeRoastedStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeRoastedStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeRoastedStoryResponseV2ValidationError{}
