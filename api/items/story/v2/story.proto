syntax = "proto3";

package api.items.story.v2;

option go_package = "boson/api/items/story/v2;api_items_story_v2";
import "api/common/v1/common.proto";
import "api/items/story/types/v1/types.proto";
import "api/items/story/types/v1/turtle_soup_types.proto";
import "api/items/story/types/v1/now_shot_types.proto";
import "api/items/story/types/v1/base_types.proto";
import "api/items/story/v1/story.privacy.setting.proto";
import "api/items/portal/types/v1/types.proto";
import "api/items/story/types/v1/haunt.types.proto";
import "validate/validate.proto";
import "api/im/message/types/v1/types.proto";

message ListHomePageStoryRequestV2 {
	api.common.v1.ListRequest list_request = 1[(validate.rules).message.required = true];
	// 是否使用推荐引擎
	bool use_recommended = 2;
	// 过滤类型，如果传空，则不过滤
	// 当且仅当不走推荐引擎时起作用
	repeated api.items.story.types.v1.StoryPlayType filter_play_types = 3;
}

message ListHomePageStoryResponseV2 {
	repeated api.items.story.types.v1.StoryDetail stories = 1;
	api.common.v1.ListResponse list_response = 2;
	// 推荐引擎的 sort_request_id
	string sort_request_id = 3;
	// 当且仅当第一页时，会返回顶部的 10个 除登录用户自己发的 其他portals
	// 注意，其中是包含 user created portals 的
	api.items.portal.types.v1.UserCreatedPortals user_created_portals = 4;
	repeated api.items.portal.types.v1.Portal portals = 5;
	// 用于下一页的 portals 获取
	api.common.v1.ListResponse portal_list_response = 6;

	// 可能存在的 haunt boo 的展示信息，注意目前是返回数组，但是客户端应该只取第一个
	// 服务端暂时会保证数组也只有一个，后续如果需要支持数组，则需要修改
	repeated api.items.story.types.v1.HauntBooShowInfo haunt_boo_show_infos = 7;
}

message ListCreatorStoryRequestV2 {
	string creator_id = 1[(validate.rules).string = {pattern: "^[0-9]+$"}];
	api.common.v1.ListRequest list_request = 2[(validate.rules).message.required = true];
}

message ListCreatorStoryResponseV2 {
	message CreatedStory {
		api.items.story.types.v1.StoryDetail story = 1;
		// 是否置顶
		bool is_top = 2;
	}
	repeated CreatedStory created_stories = 1;
	api.common.v1.ListResponse list_response = 2;
}

// 获取关注的创作者 story 列表
message ListFollowingCreatorStoryRequestV2 {
	api.common.v1.ListRequest list_request = 1[(validate.rules).message.required = true];
}

message ListFollowingCreatorStoryResponseV2 {
	repeated api.items.story.types.v1.StoryDetail following_creator_stories = 1;
	// list response 针对的是 following_creator_stories 的数组，不会影响 recommended_unfollowed_creator_stories 的数组
	api.common.v1.ListResponse list_response = 2;
	// 如果创作者的 story 列表为空，则会返回推荐的 story，用于曝光更多的作品
	// 此数组当且仅当关注的创作者的 story 列表为空时才会返回
	// 且理论上这批推荐的内容作者都是未关注的
	repeated api.items.story.types.v1.StoryDetail recommended_unfollowed_creator_stories = 3;
	// 当且仅当第一页时，会返回顶部的 10个 除登录用户自己发的 其他portals
	// 注意，其中是包含 user created portals 的
	api.items.portal.types.v1.UserCreatedPortals user_created_portals = 4;
	repeated api.items.portal.types.v1.Portal portals = 5;
	// 用于下一页的 portals 获取
	api.common.v1.ListResponse portal_list_response = 6;
	
	
	// 可能存在的 haunt boo 的展示信息，注意目前是返回数组，但是客户端应该只取第一个
	// 服务端暂时会保证数组也只有一个，后续如果需要支持数组，则需要修改
	repeated api.items.story.types.v1.HauntBooShowInfo haunt_boo_show_infos = 7;
}

// 发布 reveal story 的请求
message CreateExchangeImageStoryRequestV2 {
	api.items.story.types.v1.StoryPlayExchangeImageConfig play_config = 2;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 3;
	// 从哪个 story 过来创建的
	optional string from_story_id = 4[(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message CreateExchangeImageStoryResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


// 发布 type story 的请求
message CreateTurtleSoupStoryRequestV2 {
	api.items.story.types.v1.StoryPlayTurtleSoupConfig play_config = 2;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 3;
}

message CreateTurtleSoupStoryResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 发布 reveal story 的请求
message CreateUnmuteStoryRequestV2 {
	api.items.story.types.v1.StoryPlayExchangeImageConfig play_config = 2;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 3;
}

message CreateUnmuteStoryResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 发布now story
message CreateNowShotStoryRequestV2 {
	api.items.story.types.v1.StoryPlayNowShotConfig play_config = 2;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 3;
	// 从哪个 story 过来创建的
	optional string from_story_id = 4[(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message CreateNowShotStoryResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

message ConsumeNowShotStoryRequestV2 {
	string story_id = 1;
	bool succeeded = 2;
	// 如果succeeded为false则为空
	api.items.story.types.v1.Resource resource = 3;
	// TTL字段，可选，单位秒，如果不传则不存储
	optional uint32 ttl = 4;
}

message StoryDetailResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 发送消息请求
message SendMessageRequest {
	string story_id = 1 [(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	// 消息类型，用于区分不同的故事类型; deprecated(该字段不被使用，story类型通过story_id获取)
	MessageType message_type = 2;
	optional string user_video_key = 3;
	optional string user_video_cover_key = 4;
	// 消费者故事封面URL
	optional string consumer_story_cover_key = 5;
	optional api.im.message.types.v1.ConsumeStatus consume_status = 6;
}

message SendMessageResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

// 消息类型枚举
enum MessageType {
	MESSAGE_TYPE_UNSPECIFIED = 0;
	MESSAGE_TYPE_WASSUP = 1;
	MESSAGE_TYPE_CHATPROXY = 2;
	MESSAGE_TYPE_ROASTED = 3;
	MESSAGE_TYPE_BASEPLAY = 4;
}
