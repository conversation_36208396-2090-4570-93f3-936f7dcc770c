syntax = "proto3";

package api.items.story.v2;

option go_package = "boson/api/items/story/v2;api_items_story_v2";
import "api/items/story/types/v1/wassup.types.proto";
import "validate/validate.proto";
import "api/items/story/types/v1/types.proto";
import "api/items/story/v1/story.privacy.setting.proto";

message GetWassupGreetingsRequest {
	repeated string image_keys = 1;
	// 如果视频，那么 image_keys 长度将大于1，且客户端需要把视频音频 asr 结果给到服务端
	// 此值可能为空
	optional string audio_asr_result = 2;
}

message GetWassupGreetingsResponse {
	api.items.story.types.v1.StoryPlayWassupBotMessage greeting = 1;
}

message GetWassupNextQuestionRequest {
	string user_audio_key = 1;
}
message GetWassupNextQuestionResponse {
	api.items.story.types.v1.StoryPlayWassupBotMessage message = 1;
	bool is_end = 2;
}

message CreateWassupStoryRequest {
	api.items.story.types.v1.StoryPlayWassupConfig config = 1;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 2;
	// 从哪个 story 过来创建的
	optional string from_story_id = 3[(validate.rules).string = {pattern: "^[0-9]+$"}];
}
message CreateWassupStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


message ConsumeWassupStoryRequest {
	string story_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	string user_video_key = 2;
	string user_video_cover_key = 3;
}
message ConsumeWassupStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}