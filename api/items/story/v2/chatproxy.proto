syntax = "proto3";

package api.items.story.v2;

option go_package = "boson/api/items/story/v2;api_items_story_v2";
import "api/items/story/types/v1/chatproxy.types.proto";
import "api/items/story/v1/story.privacy.setting.proto";
import "api/items/story/types/v1/types.proto";
import "google/protobuf/descriptor.proto";
import "validate/validate.proto";

message GetChatProxyNextTopicRequestV2 {
	string story_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	string user_audio_key = 2;
	// 如果是刚开始，则传0
	uint32 round_index = 3;
}
message GetChatProxyNextTopicResponseV2 {
	api.items.story.types.v1.ChatProxyQuestion question = 1;
	bool end = 2;
	api.items.story.types.v1.StoryDetail story_detail = 3;
}

message ConsumeChatProxyRequestV2 {
	string story_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	string user_video_key = 2;
	string user_video_cover_key = 3;
}
message ConsumeChatProxyResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

message CreateChatProxyStoryRequestV2 {
	api.items.story.types.v1.StoryPlayChatProxyConfig play_config = 1[(validate.rules).message = {
		required: true
	}];
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 2;
	// 从哪个 story 过来创建的
	optional string from_story_id = 3[(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message CreateChatProxyStoryResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}