// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v2/pin.proto

package api_items_story_v2

import (
	v11 "boson/api/items/story/types/v1"
	v12 "boson/api/items/story/v1"
	v1 "boson/api/resource/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AutoGenerateAreaEmojiRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BackgroundImage *v1.Resource           `protobuf:"bytes,1,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AutoGenerateAreaEmojiRequest) Reset() {
	*x = AutoGenerateAreaEmojiRequest{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoGenerateAreaEmojiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoGenerateAreaEmojiRequest) ProtoMessage() {}

func (x *AutoGenerateAreaEmojiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoGenerateAreaEmojiRequest.ProtoReflect.Descriptor instead.
func (*AutoGenerateAreaEmojiRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{0}
}

func (x *AutoGenerateAreaEmojiRequest) GetBackgroundImage() *v1.Resource {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

type AutoGenerateAreaEmojiResponse struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	PinEmojiResources []*v11.PinEmojiResource `protobuf:"bytes,1,rep,name=pin_emoji_resources,json=pinEmojiResources,proto3" json:"pin_emoji_resources,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AutoGenerateAreaEmojiResponse) Reset() {
	*x = AutoGenerateAreaEmojiResponse{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoGenerateAreaEmojiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoGenerateAreaEmojiResponse) ProtoMessage() {}

func (x *AutoGenerateAreaEmojiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoGenerateAreaEmojiResponse.ProtoReflect.Descriptor instead.
func (*AutoGenerateAreaEmojiResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{1}
}

func (x *AutoGenerateAreaEmojiResponse) GetPinEmojiResources() []*v11.PinEmojiResource {
	if x != nil {
		return x.PinEmojiResources
	}
	return nil
}

type ManualGenerateAreaEmojiRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Areas           []*v11.PinEmojiArear   `protobuf:"bytes,1,rep,name=areas,proto3" json:"areas,omitempty"`
	BackgroundImage *v1.Resource           `protobuf:"bytes,2,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ManualGenerateAreaEmojiRequest) Reset() {
	*x = ManualGenerateAreaEmojiRequest{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManualGenerateAreaEmojiRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualGenerateAreaEmojiRequest) ProtoMessage() {}

func (x *ManualGenerateAreaEmojiRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualGenerateAreaEmojiRequest.ProtoReflect.Descriptor instead.
func (*ManualGenerateAreaEmojiRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{2}
}

func (x *ManualGenerateAreaEmojiRequest) GetAreas() []*v11.PinEmojiArear {
	if x != nil {
		return x.Areas
	}
	return nil
}

func (x *ManualGenerateAreaEmojiRequest) GetBackgroundImage() *v1.Resource {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

type ManualGenerateAreaEmojiResponse struct {
	state             protoimpl.MessageState  `protogen:"open.v1"`
	PinEmojiResources []*v11.PinEmojiResource `protobuf:"bytes,1,rep,name=pin_emoji_resources,json=pinEmojiResources,proto3" json:"pin_emoji_resources,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *ManualGenerateAreaEmojiResponse) Reset() {
	*x = ManualGenerateAreaEmojiResponse{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ManualGenerateAreaEmojiResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualGenerateAreaEmojiResponse) ProtoMessage() {}

func (x *ManualGenerateAreaEmojiResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualGenerateAreaEmojiResponse.ProtoReflect.Descriptor instead.
func (*ManualGenerateAreaEmojiResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{3}
}

func (x *ManualGenerateAreaEmojiResponse) GetPinEmojiResources() []*v11.PinEmojiResource {
	if x != nil {
		return x.PinEmojiResources
	}
	return nil
}

type CreatePinStoryRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	PinConfig *v11.StoryPinConfig    `protobuf:"bytes,1,opt,name=pin_config,json=pinConfig,proto3" json:"pin_config,omitempty"`
	// 隐私设置，可选型，如果不传则自动使用默认值
	PrivacySetting *v12.PrivacySettingUpdateAttr `protobuf:"bytes,2,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreatePinStoryRequest) Reset() {
	*x = CreatePinStoryRequest{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePinStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePinStoryRequest) ProtoMessage() {}

func (x *CreatePinStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePinStoryRequest.ProtoReflect.Descriptor instead.
func (*CreatePinStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePinStoryRequest) GetPinConfig() *v11.StoryPinConfig {
	if x != nil {
		return x.PinConfig
	}
	return nil
}

func (x *CreatePinStoryRequest) GetPrivacySetting() *v12.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

type CreatePinStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePinStoryResponse) Reset() {
	*x = CreatePinStoryResponse{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePinStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePinStoryResponse) ProtoMessage() {}

func (x *CreatePinStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePinStoryResponse.ProtoReflect.Descriptor instead.
func (*CreatePinStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePinStoryResponse) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type ConsumePinStoryRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	StoryId string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	Success bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	// Types that are valid to be assigned to ConsumeData:
	//
	//	*ConsumePinStoryRequest_FailedImage
	//	*ConsumePinStoryRequest_SuccessCostSeconds
	ConsumeData   isConsumePinStoryRequest_ConsumeData `protobuf_oneof:"consume_data"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumePinStoryRequest) Reset() {
	*x = ConsumePinStoryRequest{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumePinStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumePinStoryRequest) ProtoMessage() {}

func (x *ConsumePinStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumePinStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumePinStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{6}
}

func (x *ConsumePinStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumePinStoryRequest) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ConsumePinStoryRequest) GetConsumeData() isConsumePinStoryRequest_ConsumeData {
	if x != nil {
		return x.ConsumeData
	}
	return nil
}

func (x *ConsumePinStoryRequest) GetFailedImage() *v1.Resource {
	if x != nil {
		if x, ok := x.ConsumeData.(*ConsumePinStoryRequest_FailedImage); ok {
			return x.FailedImage
		}
	}
	return nil
}

func (x *ConsumePinStoryRequest) GetSuccessCostSeconds() uint32 {
	if x != nil {
		if x, ok := x.ConsumeData.(*ConsumePinStoryRequest_SuccessCostSeconds); ok {
			return x.SuccessCostSeconds
		}
	}
	return 0
}

type isConsumePinStoryRequest_ConsumeData interface {
	isConsumePinStoryRequest_ConsumeData()
}

type ConsumePinStoryRequest_FailedImage struct {
	FailedImage *v1.Resource `protobuf:"bytes,3,opt,name=failed_image,json=failedImage,proto3,oneof"`
}

type ConsumePinStoryRequest_SuccessCostSeconds struct {
	SuccessCostSeconds uint32 `protobuf:"varint,4,opt,name=success_cost_seconds,json=successCostSeconds,proto3,oneof"`
}

func (*ConsumePinStoryRequest_FailedImage) isConsumePinStoryRequest_ConsumeData() {}

func (*ConsumePinStoryRequest_SuccessCostSeconds) isConsumePinStoryRequest_ConsumeData() {}

type ConsumePinStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v11.StoryDetail       `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumePinStoryResponse) Reset() {
	*x = ConsumePinStoryResponse{}
	mi := &file_api_items_story_v2_pin_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumePinStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumePinStoryResponse) ProtoMessage() {}

func (x *ConsumePinStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_pin_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumePinStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumePinStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_pin_proto_rawDescGZIP(), []int{7}
}

func (x *ConsumePinStoryResponse) GetStoryDetail() *v11.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

var File_api_items_story_v2_pin_proto protoreflect.FileDescriptor

const file_api_items_story_v2_pin_proto_rawDesc = "" +
	"\n" +
	"\x1capi/items/story/v2/pin.proto\x12\x12api.items.story.v2\x1a$api/items/story/types/v1/types.proto\x1a(api/items/story/types/v1/pin.types.proto\x1a.api/items/story/v1/story.privacy.setting.proto\x1a!api/resource/types/v1/types.proto\x1a\x17validate/validate.proto\"j\n" +
	"\x1cAutoGenerateAreaEmojiRequest\x12J\n" +
	"\x10background_image\x18\x01 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x0fbackgroundImage\"{\n" +
	"\x1dAutoGenerateAreaEmojiResponse\x12Z\n" +
	"\x13pin_emoji_resources\x18\x01 \x03(\v2*.api.items.story.types.v1.PinEmojiResourceR\x11pinEmojiResources\"\xab\x01\n" +
	"\x1eManualGenerateAreaEmojiRequest\x12=\n" +
	"\x05areas\x18\x01 \x03(\v2'.api.items.story.types.v1.PinEmojiArearR\x05areas\x12J\n" +
	"\x10background_image\x18\x02 \x01(\v2\x1f.api.resource.types.v1.ResourceR\x0fbackgroundImage\"}\n" +
	"\x1fManualGenerateAreaEmojiResponse\x12Z\n" +
	"\x13pin_emoji_resources\x18\x01 \x03(\v2*.api.items.story.types.v1.PinEmojiResourceR\x11pinEmojiResources\"\xd0\x01\n" +
	"\x15CreatePinStoryRequest\x12G\n" +
	"\n" +
	"pin_config\x18\x01 \x01(\v2(.api.items.story.types.v1.StoryPinConfigR\tpinConfig\x12Z\n" +
	"\x0fprivacy_setting\x18\x02 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01B\x12\n" +
	"\x10_privacy_setting\"b\n" +
	"\x16CreatePinStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"\xe8\x01\n" +
	"\x16ConsumePinStoryRequest\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12D\n" +
	"\ffailed_image\x18\x03 \x01(\v2\x1f.api.resource.types.v1.ResourceH\x00R\vfailedImage\x122\n" +
	"\x14success_cost_seconds\x18\x04 \x01(\rH\x00R\x12successCostSecondsB\x0e\n" +
	"\fconsume_data\"c\n" +
	"\x17ConsumePinStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetailB-Z+boson/api/items/story/v2;api_items_story_v2b\x06proto3"

var (
	file_api_items_story_v2_pin_proto_rawDescOnce sync.Once
	file_api_items_story_v2_pin_proto_rawDescData []byte
)

func file_api_items_story_v2_pin_proto_rawDescGZIP() []byte {
	file_api_items_story_v2_pin_proto_rawDescOnce.Do(func() {
		file_api_items_story_v2_pin_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v2_pin_proto_rawDesc), len(file_api_items_story_v2_pin_proto_rawDesc)))
	})
	return file_api_items_story_v2_pin_proto_rawDescData
}

var file_api_items_story_v2_pin_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_items_story_v2_pin_proto_goTypes = []any{
	(*AutoGenerateAreaEmojiRequest)(nil),    // 0: api.items.story.v2.AutoGenerateAreaEmojiRequest
	(*AutoGenerateAreaEmojiResponse)(nil),   // 1: api.items.story.v2.AutoGenerateAreaEmojiResponse
	(*ManualGenerateAreaEmojiRequest)(nil),  // 2: api.items.story.v2.ManualGenerateAreaEmojiRequest
	(*ManualGenerateAreaEmojiResponse)(nil), // 3: api.items.story.v2.ManualGenerateAreaEmojiResponse
	(*CreatePinStoryRequest)(nil),           // 4: api.items.story.v2.CreatePinStoryRequest
	(*CreatePinStoryResponse)(nil),          // 5: api.items.story.v2.CreatePinStoryResponse
	(*ConsumePinStoryRequest)(nil),          // 6: api.items.story.v2.ConsumePinStoryRequest
	(*ConsumePinStoryResponse)(nil),         // 7: api.items.story.v2.ConsumePinStoryResponse
	(*v1.Resource)(nil),                     // 8: api.resource.types.v1.Resource
	(*v11.PinEmojiResource)(nil),            // 9: api.items.story.types.v1.PinEmojiResource
	(*v11.PinEmojiArear)(nil),               // 10: api.items.story.types.v1.PinEmojiArear
	(*v11.StoryPinConfig)(nil),              // 11: api.items.story.types.v1.StoryPinConfig
	(*v12.PrivacySettingUpdateAttr)(nil),    // 12: api.items.story.v1.PrivacySettingUpdateAttr
	(*v11.StoryDetail)(nil),                 // 13: api.items.story.types.v1.StoryDetail
}
var file_api_items_story_v2_pin_proto_depIdxs = []int32{
	8,  // 0: api.items.story.v2.AutoGenerateAreaEmojiRequest.background_image:type_name -> api.resource.types.v1.Resource
	9,  // 1: api.items.story.v2.AutoGenerateAreaEmojiResponse.pin_emoji_resources:type_name -> api.items.story.types.v1.PinEmojiResource
	10, // 2: api.items.story.v2.ManualGenerateAreaEmojiRequest.areas:type_name -> api.items.story.types.v1.PinEmojiArear
	8,  // 3: api.items.story.v2.ManualGenerateAreaEmojiRequest.background_image:type_name -> api.resource.types.v1.Resource
	9,  // 4: api.items.story.v2.ManualGenerateAreaEmojiResponse.pin_emoji_resources:type_name -> api.items.story.types.v1.PinEmojiResource
	11, // 5: api.items.story.v2.CreatePinStoryRequest.pin_config:type_name -> api.items.story.types.v1.StoryPinConfig
	12, // 6: api.items.story.v2.CreatePinStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	13, // 7: api.items.story.v2.CreatePinStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	8,  // 8: api.items.story.v2.ConsumePinStoryRequest.failed_image:type_name -> api.resource.types.v1.Resource
	13, // 9: api.items.story.v2.ConsumePinStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_items_story_v2_pin_proto_init() }
func file_api_items_story_v2_pin_proto_init() {
	if File_api_items_story_v2_pin_proto != nil {
		return
	}
	file_api_items_story_v2_pin_proto_msgTypes[4].OneofWrappers = []any{}
	file_api_items_story_v2_pin_proto_msgTypes[6].OneofWrappers = []any{
		(*ConsumePinStoryRequest_FailedImage)(nil),
		(*ConsumePinStoryRequest_SuccessCostSeconds)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v2_pin_proto_rawDesc), len(file_api_items_story_v2_pin_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v2_pin_proto_goTypes,
		DependencyIndexes: file_api_items_story_v2_pin_proto_depIdxs,
		MessageInfos:      file_api_items_story_v2_pin_proto_msgTypes,
	}.Build()
	File_api_items_story_v2_pin_proto = out.File
	file_api_items_story_v2_pin_proto_goTypes = nil
	file_api_items_story_v2_pin_proto_depIdxs = nil
}
