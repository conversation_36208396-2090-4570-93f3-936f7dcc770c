// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v2/pin.proto

package api_items_story_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AutoGenerateAreaEmojiRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AutoGenerateAreaEmojiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AutoGenerateAreaEmojiRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AutoGenerateAreaEmojiRequestMultiError, or nil if none found.
func (m *AutoGenerateAreaEmojiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoGenerateAreaEmojiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AutoGenerateAreaEmojiRequestValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AutoGenerateAreaEmojiRequestValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AutoGenerateAreaEmojiRequestValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AutoGenerateAreaEmojiRequestMultiError(errors)
	}

	return nil
}

// AutoGenerateAreaEmojiRequestMultiError is an error wrapping multiple
// validation errors returned by AutoGenerateAreaEmojiRequest.ValidateAll() if
// the designated constraints aren't met.
type AutoGenerateAreaEmojiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoGenerateAreaEmojiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoGenerateAreaEmojiRequestMultiError) AllErrors() []error { return m }

// AutoGenerateAreaEmojiRequestValidationError is the validation error returned
// by AutoGenerateAreaEmojiRequest.Validate if the designated constraints
// aren't met.
type AutoGenerateAreaEmojiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoGenerateAreaEmojiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AutoGenerateAreaEmojiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AutoGenerateAreaEmojiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AutoGenerateAreaEmojiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AutoGenerateAreaEmojiRequestValidationError) ErrorName() string {
	return "AutoGenerateAreaEmojiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AutoGenerateAreaEmojiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoGenerateAreaEmojiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoGenerateAreaEmojiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoGenerateAreaEmojiRequestValidationError{}

// Validate checks the field values on AutoGenerateAreaEmojiResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AutoGenerateAreaEmojiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AutoGenerateAreaEmojiResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// AutoGenerateAreaEmojiResponseMultiError, or nil if none found.
func (m *AutoGenerateAreaEmojiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AutoGenerateAreaEmojiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPinEmojiResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AutoGenerateAreaEmojiResponseValidationError{
						field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AutoGenerateAreaEmojiResponseValidationError{
						field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AutoGenerateAreaEmojiResponseValidationError{
					field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AutoGenerateAreaEmojiResponseMultiError(errors)
	}

	return nil
}

// AutoGenerateAreaEmojiResponseMultiError is an error wrapping multiple
// validation errors returned by AutoGenerateAreaEmojiResponse.ValidateAll()
// if the designated constraints aren't met.
type AutoGenerateAreaEmojiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AutoGenerateAreaEmojiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AutoGenerateAreaEmojiResponseMultiError) AllErrors() []error { return m }

// AutoGenerateAreaEmojiResponseValidationError is the validation error
// returned by AutoGenerateAreaEmojiResponse.Validate if the designated
// constraints aren't met.
type AutoGenerateAreaEmojiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AutoGenerateAreaEmojiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AutoGenerateAreaEmojiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AutoGenerateAreaEmojiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AutoGenerateAreaEmojiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AutoGenerateAreaEmojiResponseValidationError) ErrorName() string {
	return "AutoGenerateAreaEmojiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AutoGenerateAreaEmojiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAutoGenerateAreaEmojiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AutoGenerateAreaEmojiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AutoGenerateAreaEmojiResponseValidationError{}

// Validate checks the field values on ManualGenerateAreaEmojiRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualGenerateAreaEmojiRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualGenerateAreaEmojiRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ManualGenerateAreaEmojiRequestMultiError, or nil if none found.
func (m *ManualGenerateAreaEmojiRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualGenerateAreaEmojiRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAreas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualGenerateAreaEmojiRequestValidationError{
						field:  fmt.Sprintf("Areas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualGenerateAreaEmojiRequestValidationError{
						field:  fmt.Sprintf("Areas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualGenerateAreaEmojiRequestValidationError{
					field:  fmt.Sprintf("Areas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetBackgroundImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ManualGenerateAreaEmojiRequestValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ManualGenerateAreaEmojiRequestValidationError{
					field:  "BackgroundImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ManualGenerateAreaEmojiRequestValidationError{
				field:  "BackgroundImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ManualGenerateAreaEmojiRequestMultiError(errors)
	}

	return nil
}

// ManualGenerateAreaEmojiRequestMultiError is an error wrapping multiple
// validation errors returned by ManualGenerateAreaEmojiRequest.ValidateAll()
// if the designated constraints aren't met.
type ManualGenerateAreaEmojiRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualGenerateAreaEmojiRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualGenerateAreaEmojiRequestMultiError) AllErrors() []error { return m }

// ManualGenerateAreaEmojiRequestValidationError is the validation error
// returned by ManualGenerateAreaEmojiRequest.Validate if the designated
// constraints aren't met.
type ManualGenerateAreaEmojiRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualGenerateAreaEmojiRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualGenerateAreaEmojiRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualGenerateAreaEmojiRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualGenerateAreaEmojiRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualGenerateAreaEmojiRequestValidationError) ErrorName() string {
	return "ManualGenerateAreaEmojiRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ManualGenerateAreaEmojiRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualGenerateAreaEmojiRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualGenerateAreaEmojiRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualGenerateAreaEmojiRequestValidationError{}

// Validate checks the field values on ManualGenerateAreaEmojiResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualGenerateAreaEmojiResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualGenerateAreaEmojiResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ManualGenerateAreaEmojiResponseMultiError, or nil if none found.
func (m *ManualGenerateAreaEmojiResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualGenerateAreaEmojiResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPinEmojiResources() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualGenerateAreaEmojiResponseValidationError{
						field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualGenerateAreaEmojiResponseValidationError{
						field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualGenerateAreaEmojiResponseValidationError{
					field:  fmt.Sprintf("PinEmojiResources[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ManualGenerateAreaEmojiResponseMultiError(errors)
	}

	return nil
}

// ManualGenerateAreaEmojiResponseMultiError is an error wrapping multiple
// validation errors returned by ManualGenerateAreaEmojiResponse.ValidateAll()
// if the designated constraints aren't met.
type ManualGenerateAreaEmojiResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualGenerateAreaEmojiResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualGenerateAreaEmojiResponseMultiError) AllErrors() []error { return m }

// ManualGenerateAreaEmojiResponseValidationError is the validation error
// returned by ManualGenerateAreaEmojiResponse.Validate if the designated
// constraints aren't met.
type ManualGenerateAreaEmojiResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualGenerateAreaEmojiResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualGenerateAreaEmojiResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualGenerateAreaEmojiResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualGenerateAreaEmojiResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualGenerateAreaEmojiResponseValidationError) ErrorName() string {
	return "ManualGenerateAreaEmojiResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ManualGenerateAreaEmojiResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualGenerateAreaEmojiResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualGenerateAreaEmojiResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualGenerateAreaEmojiResponseValidationError{}

// Validate checks the field values on CreatePinStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePinStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePinStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePinStoryRequestMultiError, or nil if none found.
func (m *CreatePinStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePinStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPinConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePinStoryRequestValidationError{
					field:  "PinConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePinStoryRequestValidationError{
					field:  "PinConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPinConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePinStoryRequestValidationError{
				field:  "PinConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreatePinStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreatePinStoryRequestValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreatePinStoryRequestValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreatePinStoryRequestMultiError(errors)
	}

	return nil
}

// CreatePinStoryRequestMultiError is an error wrapping multiple validation
// errors returned by CreatePinStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type CreatePinStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePinStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePinStoryRequestMultiError) AllErrors() []error { return m }

// CreatePinStoryRequestValidationError is the validation error returned by
// CreatePinStoryRequest.Validate if the designated constraints aren't met.
type CreatePinStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePinStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePinStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePinStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePinStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePinStoryRequestValidationError) ErrorName() string {
	return "CreatePinStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePinStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePinStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePinStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePinStoryRequestValidationError{}

// Validate checks the field values on CreatePinStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreatePinStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreatePinStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreatePinStoryResponseMultiError, or nil if none found.
func (m *CreatePinStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreatePinStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreatePinStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreatePinStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreatePinStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreatePinStoryResponseMultiError(errors)
	}

	return nil
}

// CreatePinStoryResponseMultiError is an error wrapping multiple validation
// errors returned by CreatePinStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type CreatePinStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreatePinStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreatePinStoryResponseMultiError) AllErrors() []error { return m }

// CreatePinStoryResponseValidationError is the validation error returned by
// CreatePinStoryResponse.Validate if the designated constraints aren't met.
type CreatePinStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreatePinStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreatePinStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreatePinStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreatePinStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreatePinStoryResponseValidationError) ErrorName() string {
	return "CreatePinStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreatePinStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreatePinStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreatePinStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreatePinStoryResponseValidationError{}

// Validate checks the field values on ConsumePinStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumePinStoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumePinStoryRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumePinStoryRequestMultiError, or nil if none found.
func (m *ConsumePinStoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumePinStoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ConsumePinStoryRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := ConsumePinStoryRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Success

	switch v := m.ConsumeData.(type) {
	case *ConsumePinStoryRequest_FailedImage:
		if v == nil {
			err := ConsumePinStoryRequestValidationError{
				field:  "ConsumeData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFailedImage()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ConsumePinStoryRequestValidationError{
						field:  "FailedImage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ConsumePinStoryRequestValidationError{
						field:  "FailedImage",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFailedImage()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ConsumePinStoryRequestValidationError{
					field:  "FailedImage",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ConsumePinStoryRequest_SuccessCostSeconds:
		if v == nil {
			err := ConsumePinStoryRequestValidationError{
				field:  "ConsumeData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for SuccessCostSeconds
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ConsumePinStoryRequestMultiError(errors)
	}

	return nil
}

// ConsumePinStoryRequestMultiError is an error wrapping multiple validation
// errors returned by ConsumePinStoryRequest.ValidateAll() if the designated
// constraints aren't met.
type ConsumePinStoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumePinStoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumePinStoryRequestMultiError) AllErrors() []error { return m }

// ConsumePinStoryRequestValidationError is the validation error returned by
// ConsumePinStoryRequest.Validate if the designated constraints aren't met.
type ConsumePinStoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumePinStoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumePinStoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumePinStoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumePinStoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumePinStoryRequestValidationError) ErrorName() string {
	return "ConsumePinStoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumePinStoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumePinStoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumePinStoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumePinStoryRequestValidationError{}

var _ConsumePinStoryRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ConsumePinStoryResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumePinStoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumePinStoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumePinStoryResponseMultiError, or nil if none found.
func (m *ConsumePinStoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumePinStoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumePinStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumePinStoryResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumePinStoryResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumePinStoryResponseMultiError(errors)
	}

	return nil
}

// ConsumePinStoryResponseMultiError is an error wrapping multiple validation
// errors returned by ConsumePinStoryResponse.ValidateAll() if the designated
// constraints aren't met.
type ConsumePinStoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumePinStoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumePinStoryResponseMultiError) AllErrors() []error { return m }

// ConsumePinStoryResponseValidationError is the validation error returned by
// ConsumePinStoryResponse.Validate if the designated constraints aren't met.
type ConsumePinStoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumePinStoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumePinStoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumePinStoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumePinStoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumePinStoryResponseValidationError) ErrorName() string {
	return "ConsumePinStoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumePinStoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumePinStoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumePinStoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumePinStoryResponseValidationError{}
