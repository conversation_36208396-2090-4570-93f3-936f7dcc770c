syntax = "proto3";

package api.items.story.v2;

option go_package = "boson/api/items/story/v2;api_items_story_v2";
import "api/items/story/types/v1/types.proto";
import "validate/validate.proto";

message ConsumeRoastedStoryRequestV2 {
	string story_id = 1 [(validate.rules).string = {pattern: "^[0-9]+$"}];
}

message ConsumeRoastedStoryResponseV2 {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}
