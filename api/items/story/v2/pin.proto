syntax = "proto3";

package api.items.story.v2;

option go_package = "boson/api/items/story/v2;api_items_story_v2";

import "api/items/story/types/v1/types.proto";
import "api/items/story/types/v1/pin.types.proto";
import "api/items/story/v1/story.privacy.setting.proto";
import "api/resource/types/v1/types.proto";
import "validate/validate.proto";

message AutoGenerateAreaEmojiRequest {
	api.resource.types.v1.Resource background_image = 1;
}
message AutoGenerateAreaEmojiResponse {
	repeated api.items.story.types.v1.PinEmojiResource pin_emoji_resources = 1;
}

message ManualGenerateAreaEmojiRequest {
	repeated api.items.story.types.v1.PinEmojiArear areas = 1;
	api.resource.types.v1.Resource background_image = 2;
}
message ManualGenerateAreaEmojiResponse {
	repeated api.items.story.types.v1.PinEmojiResource pin_emoji_resources = 1;
}

message CreatePinStoryRequest {
	api.items.story.types.v1.StoryPinConfig pin_config = 1;
	// 隐私设置，可选型，如果不传则自动使用默认值
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 2;
}
message CreatePinStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

message ConsumePinStoryRequest {
	string story_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$"
	}];
	bool success = 2;
	oneof consume_data {
		api.resource.types.v1.Resource failed_image = 3;
		uint32 success_cost_seconds = 4;
	}
}
message ConsumePinStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}