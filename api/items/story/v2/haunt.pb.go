// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v2/haunt.proto

package api_items_story_v2

import (
	v11 "boson/api/common/v1"
	v1 "boson/api/items/story/types/v1"
	v12 "boson/api/items/story/v1"
	v13 "boson/api/resource/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ImageCheckRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ImageKey      string                 `protobuf:"bytes,1,opt,name=image_key,json=imageKey,proto3" json:"image_key,omitempty"`
	TextCondition string                 `protobuf:"bytes,2,opt,name=text_condition,json=textCondition,proto3" json:"text_condition,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageCheckRequest) Reset() {
	*x = ImageCheckRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCheckRequest) ProtoMessage() {}

func (x *ImageCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCheckRequest.ProtoReflect.Descriptor instead.
func (*ImageCheckRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{0}
}

func (x *ImageCheckRequest) GetImageKey() string {
	if x != nil {
		return x.ImageKey
	}
	return ""
}

func (x *ImageCheckRequest) GetTextCondition() string {
	if x != nil {
		return x.TextCondition
	}
	return ""
}

type ImageCheckResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Pass  bool                   `protobuf:"varint,1,opt,name=pass,proto3" json:"pass,omitempty"`
	// float64 格式
	Point         []string `protobuf:"bytes,2,rep,name=point,proto3" json:"point,omitempty"`
	Feedback      string   `protobuf:"bytes,3,opt,name=feedback,proto3" json:"feedback,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImageCheckResponse) Reset() {
	*x = ImageCheckResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImageCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageCheckResponse) ProtoMessage() {}

func (x *ImageCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageCheckResponse.ProtoReflect.Descriptor instead.
func (*ImageCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{1}
}

func (x *ImageCheckResponse) GetPass() bool {
	if x != nil {
		return x.Pass
	}
	return false
}

func (x *ImageCheckResponse) GetPoint() []string {
	if x != nil {
		return x.Point
	}
	return nil
}

func (x *ImageCheckResponse) GetFeedback() string {
	if x != nil {
		return x.Feedback
	}
	return ""
}

type ListHauntRandomAvatarsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHauntRandomAvatarsRequest) Reset() {
	*x = ListHauntRandomAvatarsRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHauntRandomAvatarsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHauntRandomAvatarsRequest) ProtoMessage() {}

func (x *ListHauntRandomAvatarsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHauntRandomAvatarsRequest.ProtoReflect.Descriptor instead.
func (*ListHauntRandomAvatarsRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{2}
}

type ListHauntRandomAvatarsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AvatarUrls    []string               `protobuf:"bytes,1,rep,name=avatar_urls,json=avatarUrls,proto3" json:"avatar_urls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHauntRandomAvatarsResponse) Reset() {
	*x = ListHauntRandomAvatarsResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHauntRandomAvatarsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHauntRandomAvatarsResponse) ProtoMessage() {}

func (x *ListHauntRandomAvatarsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHauntRandomAvatarsResponse.ProtoReflect.Descriptor instead.
func (*ListHauntRandomAvatarsResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{3}
}

func (x *ListHauntRandomAvatarsResponse) GetAvatarUrls() []string {
	if x != nil {
		return x.AvatarUrls
	}
	return nil
}

type ListHauntQuestionsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHauntQuestionsRequest) Reset() {
	*x = ListHauntQuestionsRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHauntQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHauntQuestionsRequest) ProtoMessage() {}

func (x *ListHauntQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHauntQuestionsRequest.ProtoReflect.Descriptor instead.
func (*ListHauntQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{4}
}

type ListHauntQuestionsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Questions     []*v1.HauntQuestion    `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHauntQuestionsResponse) Reset() {
	*x = ListHauntQuestionsResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHauntQuestionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHauntQuestionsResponse) ProtoMessage() {}

func (x *ListHauntQuestionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHauntQuestionsResponse.ProtoReflect.Descriptor instead.
func (*ListHauntQuestionsResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{5}
}

func (x *ListHauntQuestionsResponse) GetQuestions() []*v1.HauntQuestion {
	if x != nil {
		return x.Questions
	}
	return nil
}

type ListHauntBooAssistRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ListRequest   *v11.ListRequest       `protobuf:"bytes,1,opt,name=list_request,json=listRequest,proto3" json:"list_request,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListHauntBooAssistRequest) Reset() {
	*x = ListHauntBooAssistRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHauntBooAssistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHauntBooAssistRequest) ProtoMessage() {}

func (x *ListHauntBooAssistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHauntBooAssistRequest.ProtoReflect.Descriptor instead.
func (*ListHauntBooAssistRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{6}
}

func (x *ListHauntBooAssistRequest) GetListRequest() *v11.ListRequest {
	if x != nil {
		return x.ListRequest
	}
	return nil
}

type ListHauntBooAssistResponse struct {
	state                       protoimpl.MessageState `protogen:"open.v1"`
	BoosWithQuestionsAndAnswers []*v1.HauntBoo         `protobuf:"bytes,1,rep,name=boos_with_questions_and_answers,json=boosWithQuestionsAndAnswers,proto3" json:"boos_with_questions_and_answers,omitempty"`
	ListResponse                *v11.ListResponse      `protobuf:"bytes,2,opt,name=list_response,json=listResponse,proto3" json:"list_response,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *ListHauntBooAssistResponse) Reset() {
	*x = ListHauntBooAssistResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListHauntBooAssistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListHauntBooAssistResponse) ProtoMessage() {}

func (x *ListHauntBooAssistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListHauntBooAssistResponse.ProtoReflect.Descriptor instead.
func (*ListHauntBooAssistResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{7}
}

func (x *ListHauntBooAssistResponse) GetBoosWithQuestionsAndAnswers() []*v1.HauntBoo {
	if x != nil {
		return x.BoosWithQuestionsAndAnswers
	}
	return nil
}

func (x *ListHauntBooAssistResponse) GetListResponse() *v11.ListResponse {
	if x != nil {
		return x.ListResponse
	}
	return nil
}

type ConsumeHauntStoryRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	StoryId             string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	Unlocked            bool                   `protobuf:"varint,2,opt,name=unlocked,proto3" json:"unlocked,omitempty"`
	IsFromFeedOrFriends bool                   `protobuf:"varint,3,opt,name=is_from_feed_or_friends,json=isFromFeedOrFriends,proto3" json:"is_from_feed_or_friends,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ConsumeHauntStoryRequest) Reset() {
	*x = ConsumeHauntStoryRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeHauntStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeHauntStoryRequest) ProtoMessage() {}

func (x *ConsumeHauntStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeHauntStoryRequest.ProtoReflect.Descriptor instead.
func (*ConsumeHauntStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{8}
}

func (x *ConsumeHauntStoryRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *ConsumeHauntStoryRequest) GetUnlocked() bool {
	if x != nil {
		return x.Unlocked
	}
	return false
}

func (x *ConsumeHauntStoryRequest) GetIsFromFeedOrFriends() bool {
	if x != nil {
		return x.IsFromFeedOrFriends
	}
	return false
}

type ConsumeHauntStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeHauntStoryResponse) Reset() {
	*x = ConsumeHauntStoryResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeHauntStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeHauntStoryResponse) ProtoMessage() {}

func (x *ConsumeHauntStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeHauntStoryResponse.ProtoReflect.Descriptor instead.
func (*ConsumeHauntStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{9}
}

func (x *ConsumeHauntStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type SendHauntCaptureVideoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	VideoKey      string                 `protobuf:"bytes,2,opt,name=video_key,json=videoKey,proto3" json:"video_key,omitempty"`
	VideoCoverKey string                 `protobuf:"bytes,3,opt,name=video_cover_key,json=videoCoverKey,proto3" json:"video_cover_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendHauntCaptureVideoRequest) Reset() {
	*x = SendHauntCaptureVideoRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendHauntCaptureVideoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendHauntCaptureVideoRequest) ProtoMessage() {}

func (x *SendHauntCaptureVideoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendHauntCaptureVideoRequest.ProtoReflect.Descriptor instead.
func (*SendHauntCaptureVideoRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{10}
}

func (x *SendHauntCaptureVideoRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

func (x *SendHauntCaptureVideoRequest) GetVideoKey() string {
	if x != nil {
		return x.VideoKey
	}
	return ""
}

func (x *SendHauntCaptureVideoRequest) GetVideoCoverKey() string {
	if x != nil {
		return x.VideoCoverKey
	}
	return ""
}

type CreateHauntStoryRequest struct {
	state                       protoimpl.MessageState                              `protogen:"open.v1"`
	BoosWithQuestionsAndAnswers []*CreateHauntStoryRequest_BooWithQuestionAndAnswer `protobuf:"bytes,1,rep,name=boos_with_questions_and_answers,json=boosWithQuestionsAndAnswers,proto3" json:"boos_with_questions_and_answers,omitempty"`
	Captions                    []*v1.AttachmentText                                `protobuf:"bytes,2,rep,name=captions,proto3" json:"captions,omitempty"`
	PrivacySetting              *v12.PrivacySettingUpdateAttr                       `protobuf:"bytes,3,opt,name=privacy_setting,json=privacySetting,proto3,oneof" json:"privacy_setting,omitempty"`
	MomentCreateAttrs           []*v1.MomentCreateAttr                              `protobuf:"bytes,4,rep,name=moment_create_attrs,json=momentCreateAttrs,proto3" json:"moment_create_attrs,omitempty"`
	Cover                       *v13.Resource                                       `protobuf:"bytes,5,opt,name=cover,proto3,oneof" json:"cover,omitempty"`
	unknownFields               protoimpl.UnknownFields
	sizeCache                   protoimpl.SizeCache
}

func (x *CreateHauntStoryRequest) Reset() {
	*x = CreateHauntStoryRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateHauntStoryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHauntStoryRequest) ProtoMessage() {}

func (x *CreateHauntStoryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHauntStoryRequest.ProtoReflect.Descriptor instead.
func (*CreateHauntStoryRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{11}
}

func (x *CreateHauntStoryRequest) GetBoosWithQuestionsAndAnswers() []*CreateHauntStoryRequest_BooWithQuestionAndAnswer {
	if x != nil {
		return x.BoosWithQuestionsAndAnswers
	}
	return nil
}

func (x *CreateHauntStoryRequest) GetCaptions() []*v1.AttachmentText {
	if x != nil {
		return x.Captions
	}
	return nil
}

func (x *CreateHauntStoryRequest) GetPrivacySetting() *v12.PrivacySettingUpdateAttr {
	if x != nil {
		return x.PrivacySetting
	}
	return nil
}

func (x *CreateHauntStoryRequest) GetMomentCreateAttrs() []*v1.MomentCreateAttr {
	if x != nil {
		return x.MomentCreateAttrs
	}
	return nil
}

func (x *CreateHauntStoryRequest) GetCover() *v13.Resource {
	if x != nil {
		return x.Cover
	}
	return nil
}

type CreateHauntStoryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateHauntStoryResponse) Reset() {
	*x = CreateHauntStoryResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateHauntStoryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHauntStoryResponse) ProtoMessage() {}

func (x *CreateHauntStoryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHauntStoryResponse.ProtoReflect.Descriptor instead.
func (*CreateHauntStoryResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{12}
}

func (x *CreateHauntStoryResponse) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

type AddCapturedBooInToCollectedStickersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BooId         string                 `protobuf:"bytes,1,opt,name=boo_id,json=booId,proto3" json:"boo_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCapturedBooInToCollectedStickersRequest) Reset() {
	*x = AddCapturedBooInToCollectedStickersRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCapturedBooInToCollectedStickersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCapturedBooInToCollectedStickersRequest) ProtoMessage() {}

func (x *AddCapturedBooInToCollectedStickersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCapturedBooInToCollectedStickersRequest.ProtoReflect.Descriptor instead.
func (*AddCapturedBooInToCollectedStickersRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{13}
}

func (x *AddCapturedBooInToCollectedStickersRequest) GetBooId() string {
	if x != nil {
		return x.BooId
	}
	return ""
}

type AddCapturedBooInToCollectedStickersResponse struct {
	state                 protoimpl.MessageState                                        `protogen:"open.v1"`
	AddedCollectedSticker *AddCapturedBooInToCollectedStickersResponse_CollectedSticker `protobuf:"bytes,1,opt,name=added_collected_sticker,json=addedCollectedSticker,proto3" json:"added_collected_sticker,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AddCapturedBooInToCollectedStickersResponse) Reset() {
	*x = AddCapturedBooInToCollectedStickersResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCapturedBooInToCollectedStickersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCapturedBooInToCollectedStickersResponse) ProtoMessage() {}

func (x *AddCapturedBooInToCollectedStickersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCapturedBooInToCollectedStickersResponse.ProtoReflect.Descriptor instead.
func (*AddCapturedBooInToCollectedStickersResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{14}
}

func (x *AddCapturedBooInToCollectedStickersResponse) GetAddedCollectedSticker() *AddCapturedBooInToCollectedStickersResponse_CollectedSticker {
	if x != nil {
		return x.AddedCollectedSticker
	}
	return nil
}

type AddCaptureBooIntoMyAssistRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BooId         string                 `protobuf:"bytes,1,opt,name=boo_id,json=booId,proto3" json:"boo_id,omitempty"`
	StoryId       string                 `protobuf:"bytes,2,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCaptureBooIntoMyAssistRequest) Reset() {
	*x = AddCaptureBooIntoMyAssistRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCaptureBooIntoMyAssistRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCaptureBooIntoMyAssistRequest) ProtoMessage() {}

func (x *AddCaptureBooIntoMyAssistRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCaptureBooIntoMyAssistRequest.ProtoReflect.Descriptor instead.
func (*AddCaptureBooIntoMyAssistRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{15}
}

func (x *AddCaptureBooIntoMyAssistRequest) GetBooId() string {
	if x != nil {
		return x.BooId
	}
	return ""
}

func (x *AddCaptureBooIntoMyAssistRequest) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type AddCaptureBooIntoMyAssistResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddCaptureBooIntoMyAssistResponse) Reset() {
	*x = AddCaptureBooIntoMyAssistResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCaptureBooIntoMyAssistResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCaptureBooIntoMyAssistResponse) ProtoMessage() {}

func (x *AddCaptureBooIntoMyAssistResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCaptureBooIntoMyAssistResponse.ProtoReflect.Descriptor instead.
func (*AddCaptureBooIntoMyAssistResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{16}
}

// 上报一次 haunt boo 的展现
type ReportHauntShowRequest struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	BooId         string                   `protobuf:"bytes,1,opt,name=boo_id,json=booId,proto3" json:"boo_id,omitempty"`
	ShowInfoSence v1.HauntBooShowInfoSence `protobuf:"varint,2,opt,name=show_info_sence,json=showInfoSence,proto3,enum=api.items.story.types.v1.HauntBooShowInfoSence" json:"show_info_sence,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReportHauntShowRequest) Reset() {
	*x = ReportHauntShowRequest{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportHauntShowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportHauntShowRequest) ProtoMessage() {}

func (x *ReportHauntShowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportHauntShowRequest.ProtoReflect.Descriptor instead.
func (*ReportHauntShowRequest) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{17}
}

func (x *ReportHauntShowRequest) GetBooId() string {
	if x != nil {
		return x.BooId
	}
	return ""
}

func (x *ReportHauntShowRequest) GetShowInfoSence() v1.HauntBooShowInfoSence {
	if x != nil {
		return x.ShowInfoSence
	}
	return v1.HauntBooShowInfoSence(0)
}

type ReportHauntShowResponse struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	UpdatedHauntBooShowInfo *v1.HauntBooShowInfo   `protobuf:"bytes,1,opt,name=updated_haunt_boo_show_info,json=updatedHauntBooShowInfo,proto3" json:"updated_haunt_boo_show_info,omitempty"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *ReportHauntShowResponse) Reset() {
	*x = ReportHauntShowResponse{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReportHauntShowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportHauntShowResponse) ProtoMessage() {}

func (x *ReportHauntShowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportHauntShowResponse.ProtoReflect.Descriptor instead.
func (*ReportHauntShowResponse) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{18}
}

func (x *ReportHauntShowResponse) GetUpdatedHauntBooShowInfo() *v1.HauntBooShowInfo {
	if x != nil {
		return x.UpdatedHauntBooShowInfo
	}
	return nil
}

type CreateHauntStoryRequest_BooWithQuestionAndAnswer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to ObjectId:
	//
	//	*CreateHauntStoryRequest_BooWithQuestionAndAnswer_BooId
	//	*CreateHauntStoryRequest_BooWithQuestionAndAnswer_UserAvatarId
	ObjectId             isCreateHauntStoryRequest_BooWithQuestionAndAnswer_ObjectId `protobuf_oneof:"object_id"`
	QuestionsWithAnswers []*v1.HauntQuestionWithAnswer                               `protobuf:"bytes,3,rep,name=questions_with_answers,json=questionsWithAnswers,proto3" json:"questions_with_answers,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) Reset() {
	*x = CreateHauntStoryRequest_BooWithQuestionAndAnswer{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHauntStoryRequest_BooWithQuestionAndAnswer) ProtoMessage() {}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHauntStoryRequest_BooWithQuestionAndAnswer.ProtoReflect.Descriptor instead.
func (*CreateHauntStoryRequest_BooWithQuestionAndAnswer) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) GetObjectId() isCreateHauntStoryRequest_BooWithQuestionAndAnswer_ObjectId {
	if x != nil {
		return x.ObjectId
	}
	return nil
}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) GetBooId() string {
	if x != nil {
		if x, ok := x.ObjectId.(*CreateHauntStoryRequest_BooWithQuestionAndAnswer_BooId); ok {
			return x.BooId
		}
	}
	return ""
}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) GetUserAvatarId() string {
	if x != nil {
		if x, ok := x.ObjectId.(*CreateHauntStoryRequest_BooWithQuestionAndAnswer_UserAvatarId); ok {
			return x.UserAvatarId
		}
	}
	return ""
}

func (x *CreateHauntStoryRequest_BooWithQuestionAndAnswer) GetQuestionsWithAnswers() []*v1.HauntQuestionWithAnswer {
	if x != nil {
		return x.QuestionsWithAnswers
	}
	return nil
}

type isCreateHauntStoryRequest_BooWithQuestionAndAnswer_ObjectId interface {
	isCreateHauntStoryRequest_BooWithQuestionAndAnswer_ObjectId()
}

type CreateHauntStoryRequest_BooWithQuestionAndAnswer_BooId struct {
	BooId string `protobuf:"bytes,1,opt,name=boo_id,json=booId,proto3,oneof"`
}

type CreateHauntStoryRequest_BooWithQuestionAndAnswer_UserAvatarId struct {
	UserAvatarId string `protobuf:"bytes,2,opt,name=user_avatar_id,json=userAvatarId,proto3,oneof"`
}

func (*CreateHauntStoryRequest_BooWithQuestionAndAnswer_BooId) isCreateHauntStoryRequest_BooWithQuestionAndAnswer_ObjectId() {
}

func (*CreateHauntStoryRequest_BooWithQuestionAndAnswer_UserAvatarId) isCreateHauntStoryRequest_BooWithQuestionAndAnswer_ObjectId() {
}

type AddCapturedBooInToCollectedStickersResponse_CollectedSticker struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	Sticker                  *v1.HideSticker        `protobuf:"bytes,1,opt,name=sticker,proto3" json:"sticker,omitempty"`
	CollectedAtUnixTimestamp uint32                 `protobuf:"varint,2,opt,name=collected_at_unix_timestamp,json=collectedAtUnixTimestamp,proto3" json:"collected_at_unix_timestamp,omitempty"`
	IsTop                    bool                   `protobuf:"varint,3,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) Reset() {
	*x = AddCapturedBooInToCollectedStickersResponse_CollectedSticker{}
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCapturedBooInToCollectedStickersResponse_CollectedSticker) ProtoMessage() {}

func (x *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_haunt_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCapturedBooInToCollectedStickersResponse_CollectedSticker.ProtoReflect.Descriptor instead.
func (*AddCapturedBooInToCollectedStickersResponse_CollectedSticker) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_haunt_proto_rawDescGZIP(), []int{14, 0}
}

func (x *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) GetSticker() *v1.HideSticker {
	if x != nil {
		return x.Sticker
	}
	return nil
}

func (x *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) GetCollectedAtUnixTimestamp() uint32 {
	if x != nil {
		return x.CollectedAtUnixTimestamp
	}
	return 0
}

func (x *AddCapturedBooInToCollectedStickersResponse_CollectedSticker) GetIsTop() bool {
	if x != nil {
		return x.IsTop
	}
	return false
}

var File_api_items_story_v2_haunt_proto protoreflect.FileDescriptor

const file_api_items_story_v2_haunt_proto_rawDesc = "" +
	"\n" +
	"\x1eapi/items/story/v2/haunt.proto\x12\x12api.items.story.v2\x1a*api/items/story/types/v1/haunt.types.proto\x1a.api/items/story/v1/story.privacy.setting.proto\x1a$api/items/story/types/v1/types.proto\x1a)api/items/story/types/v1/base_types.proto\x1a)api/items/story/types/v1/hide.types.proto\x1a\x1aapi/common/v1/common.proto\x1a\x17validate/validate.proto\x1a!api/resource/types/v1/types.proto\"W\n" +
	"\x11ImageCheckRequest\x12\x1b\n" +
	"\timage_key\x18\x01 \x01(\tR\bimageKey\x12%\n" +
	"\x0etext_condition\x18\x02 \x01(\tR\rtextCondition\"Z\n" +
	"\x12ImageCheckResponse\x12\x12\n" +
	"\x04pass\x18\x01 \x01(\bR\x04pass\x12\x14\n" +
	"\x05point\x18\x02 \x03(\tR\x05point\x12\x1a\n" +
	"\bfeedback\x18\x03 \x01(\tR\bfeedback\"\x1f\n" +
	"\x1dListHauntRandomAvatarsRequest\"A\n" +
	"\x1eListHauntRandomAvatarsResponse\x12\x1f\n" +
	"\vavatar_urls\x18\x01 \x03(\tR\n" +
	"avatarUrls\"\x1b\n" +
	"\x19ListHauntQuestionsRequest\"c\n" +
	"\x1aListHauntQuestionsResponse\x12E\n" +
	"\tquestions\x18\x01 \x03(\v2'.api.items.story.types.v1.HauntQuestionR\tquestions\"d\n" +
	"\x19ListHauntBooAssistRequest\x12G\n" +
	"\flist_request\x18\x01 \x01(\v2\x1a.api.common.v1.ListRequestB\b\xfaB\x05\x8a\x01\x02\x10\x01R\vlistRequest\"\xc8\x01\n" +
	"\x1aListHauntBooAssistResponse\x12h\n" +
	"\x1fboos_with_questions_and_answers\x18\x01 \x03(\v2\".api.items.story.types.v1.HauntBooR\x1bboosWithQuestionsAndAnswers\x12@\n" +
	"\rlist_response\x18\x02 \x01(\v2\x1b.api.common.v1.ListResponseR\flistResponse\"\x87\x01\n" +
	"\x18ConsumeHauntStoryRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12\x1a\n" +
	"\bunlocked\x18\x02 \x01(\bR\bunlocked\x124\n" +
	"\x17is_from_feed_or_friends\x18\x03 \x01(\bR\x13isFromFeedOrFriends\"e\n" +
	"\x19ConsumeHauntStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"~\n" +
	"\x1cSendHauntCaptureVideoRequest\x12\x19\n" +
	"\bstory_id\x18\x01 \x01(\tR\astoryId\x12\x1b\n" +
	"\tvideo_key\x18\x02 \x01(\tR\bvideoKey\x12&\n" +
	"\x0fvideo_cover_key\x18\x03 \x01(\tR\rvideoCoverKey\"\xd2\x05\n" +
	"\x17CreateHauntStoryRequest\x12\x8a\x01\n" +
	"\x1fboos_with_questions_and_answers\x18\x01 \x03(\v2D.api.items.story.v2.CreateHauntStoryRequest.BooWithQuestionAndAnswerR\x1bboosWithQuestionsAndAnswers\x12D\n" +
	"\bcaptions\x18\x02 \x03(\v2(.api.items.story.types.v1.AttachmentTextR\bcaptions\x12Z\n" +
	"\x0fprivacy_setting\x18\x03 \x01(\v2,.api.items.story.v1.PrivacySettingUpdateAttrH\x00R\x0eprivacySetting\x88\x01\x01\x12Z\n" +
	"\x13moment_create_attrs\x18\x04 \x03(\v2*.api.items.story.types.v1.MomentCreateAttrR\x11momentCreateAttrs\x12:\n" +
	"\x05cover\x18\x05 \x01(\v2\x1f.api.resource.types.v1.ResourceH\x01R\x05cover\x88\x01\x01\x1a\xd1\x01\n" +
	"\x18BooWithQuestionAndAnswer\x12\x17\n" +
	"\x06boo_id\x18\x01 \x01(\tH\x00R\x05booId\x12&\n" +
	"\x0euser_avatar_id\x18\x02 \x01(\tH\x00R\fuserAvatarId\x12g\n" +
	"\x16questions_with_answers\x18\x03 \x03(\v21.api.items.story.types.v1.HauntQuestionWithAnswerR\x14questionsWithAnswersB\v\n" +
	"\tobject_idB\x12\n" +
	"\x10_privacy_settingB\b\n" +
	"\x06_cover\"d\n" +
	"\x18CreateHauntStoryResponse\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetail\"T\n" +
	"*AddCapturedBooInToCollectedStickersRequest\x12&\n" +
	"\x06boo_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\x05booId\"\xee\x02\n" +
	"+AddCapturedBooInToCollectedStickersResponse\x12\x88\x01\n" +
	"\x17added_collected_sticker\x18\x01 \x01(\v2P.api.items.story.v2.AddCapturedBooInToCollectedStickersResponse.CollectedStickerR\x15addedCollectedSticker\x1a\xb3\x01\n" +
	"\x10CollectedSticker\x12I\n" +
	"\asticker\x18\x01 \x01(\v2%.api.items.story.types.v1.HideStickerB\b\xfaB\x05\x8a\x01\x02\x10\x01R\asticker\x12=\n" +
	"\x1bcollected_at_unix_timestamp\x18\x02 \x01(\rR\x18collectedAtUnixTimestamp\x12\x15\n" +
	"\x06is_top\x18\x03 \x01(\bR\x05isTop\"v\n" +
	" AddCaptureBooIntoMyAssistRequest\x12&\n" +
	"\x06boo_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\x05booId\x12*\n" +
	"\bstory_id\x18\x02 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\"#\n" +
	"!AddCaptureBooIntoMyAssistResponse\"\x99\x01\n" +
	"\x16ReportHauntShowRequest\x12&\n" +
	"\x06boo_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\x05booId\x12W\n" +
	"\x0fshow_info_sence\x18\x02 \x01(\x0e2/.api.items.story.types.v1.HauntBooShowInfoSenceR\rshowInfoSence\"\x83\x01\n" +
	"\x17ReportHauntShowResponse\x12h\n" +
	"\x1bupdated_haunt_boo_show_info\x18\x01 \x01(\v2*.api.items.story.types.v1.HauntBooShowInfoR\x17updatedHauntBooShowInfoB-Z+boson/api/items/story/v2;api_items_story_v2b\x06proto3"

var (
	file_api_items_story_v2_haunt_proto_rawDescOnce sync.Once
	file_api_items_story_v2_haunt_proto_rawDescData []byte
)

func file_api_items_story_v2_haunt_proto_rawDescGZIP() []byte {
	file_api_items_story_v2_haunt_proto_rawDescOnce.Do(func() {
		file_api_items_story_v2_haunt_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v2_haunt_proto_rawDesc), len(file_api_items_story_v2_haunt_proto_rawDesc)))
	})
	return file_api_items_story_v2_haunt_proto_rawDescData
}

var file_api_items_story_v2_haunt_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_api_items_story_v2_haunt_proto_goTypes = []any{
	(*ImageCheckRequest)(nil),                                            // 0: api.items.story.v2.ImageCheckRequest
	(*ImageCheckResponse)(nil),                                           // 1: api.items.story.v2.ImageCheckResponse
	(*ListHauntRandomAvatarsRequest)(nil),                                // 2: api.items.story.v2.ListHauntRandomAvatarsRequest
	(*ListHauntRandomAvatarsResponse)(nil),                               // 3: api.items.story.v2.ListHauntRandomAvatarsResponse
	(*ListHauntQuestionsRequest)(nil),                                    // 4: api.items.story.v2.ListHauntQuestionsRequest
	(*ListHauntQuestionsResponse)(nil),                                   // 5: api.items.story.v2.ListHauntQuestionsResponse
	(*ListHauntBooAssistRequest)(nil),                                    // 6: api.items.story.v2.ListHauntBooAssistRequest
	(*ListHauntBooAssistResponse)(nil),                                   // 7: api.items.story.v2.ListHauntBooAssistResponse
	(*ConsumeHauntStoryRequest)(nil),                                     // 8: api.items.story.v2.ConsumeHauntStoryRequest
	(*ConsumeHauntStoryResponse)(nil),                                    // 9: api.items.story.v2.ConsumeHauntStoryResponse
	(*SendHauntCaptureVideoRequest)(nil),                                 // 10: api.items.story.v2.SendHauntCaptureVideoRequest
	(*CreateHauntStoryRequest)(nil),                                      // 11: api.items.story.v2.CreateHauntStoryRequest
	(*CreateHauntStoryResponse)(nil),                                     // 12: api.items.story.v2.CreateHauntStoryResponse
	(*AddCapturedBooInToCollectedStickersRequest)(nil),                   // 13: api.items.story.v2.AddCapturedBooInToCollectedStickersRequest
	(*AddCapturedBooInToCollectedStickersResponse)(nil),                  // 14: api.items.story.v2.AddCapturedBooInToCollectedStickersResponse
	(*AddCaptureBooIntoMyAssistRequest)(nil),                             // 15: api.items.story.v2.AddCaptureBooIntoMyAssistRequest
	(*AddCaptureBooIntoMyAssistResponse)(nil),                            // 16: api.items.story.v2.AddCaptureBooIntoMyAssistResponse
	(*ReportHauntShowRequest)(nil),                                       // 17: api.items.story.v2.ReportHauntShowRequest
	(*ReportHauntShowResponse)(nil),                                      // 18: api.items.story.v2.ReportHauntShowResponse
	(*CreateHauntStoryRequest_BooWithQuestionAndAnswer)(nil),             // 19: api.items.story.v2.CreateHauntStoryRequest.BooWithQuestionAndAnswer
	(*AddCapturedBooInToCollectedStickersResponse_CollectedSticker)(nil), // 20: api.items.story.v2.AddCapturedBooInToCollectedStickersResponse.CollectedSticker
	(*v1.HauntQuestion)(nil),                                             // 21: api.items.story.types.v1.HauntQuestion
	(*v11.ListRequest)(nil),                                              // 22: api.common.v1.ListRequest
	(*v1.HauntBoo)(nil),                                                  // 23: api.items.story.types.v1.HauntBoo
	(*v11.ListResponse)(nil),                                             // 24: api.common.v1.ListResponse
	(*v1.StoryDetail)(nil),                                               // 25: api.items.story.types.v1.StoryDetail
	(*v1.AttachmentText)(nil),                                            // 26: api.items.story.types.v1.AttachmentText
	(*v12.PrivacySettingUpdateAttr)(nil),                                 // 27: api.items.story.v1.PrivacySettingUpdateAttr
	(*v1.MomentCreateAttr)(nil),                                          // 28: api.items.story.types.v1.MomentCreateAttr
	(*v13.Resource)(nil),                                                 // 29: api.resource.types.v1.Resource
	(v1.HauntBooShowInfoSence)(0),                                        // 30: api.items.story.types.v1.HauntBooShowInfoSence
	(*v1.HauntBooShowInfo)(nil),                                          // 31: api.items.story.types.v1.HauntBooShowInfo
	(*v1.HauntQuestionWithAnswer)(nil),                                   // 32: api.items.story.types.v1.HauntQuestionWithAnswer
	(*v1.HideSticker)(nil),                                               // 33: api.items.story.types.v1.HideSticker
}
var file_api_items_story_v2_haunt_proto_depIdxs = []int32{
	21, // 0: api.items.story.v2.ListHauntQuestionsResponse.questions:type_name -> api.items.story.types.v1.HauntQuestion
	22, // 1: api.items.story.v2.ListHauntBooAssistRequest.list_request:type_name -> api.common.v1.ListRequest
	23, // 2: api.items.story.v2.ListHauntBooAssistResponse.boos_with_questions_and_answers:type_name -> api.items.story.types.v1.HauntBoo
	24, // 3: api.items.story.v2.ListHauntBooAssistResponse.list_response:type_name -> api.common.v1.ListResponse
	25, // 4: api.items.story.v2.ConsumeHauntStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	19, // 5: api.items.story.v2.CreateHauntStoryRequest.boos_with_questions_and_answers:type_name -> api.items.story.v2.CreateHauntStoryRequest.BooWithQuestionAndAnswer
	26, // 6: api.items.story.v2.CreateHauntStoryRequest.captions:type_name -> api.items.story.types.v1.AttachmentText
	27, // 7: api.items.story.v2.CreateHauntStoryRequest.privacy_setting:type_name -> api.items.story.v1.PrivacySettingUpdateAttr
	28, // 8: api.items.story.v2.CreateHauntStoryRequest.moment_create_attrs:type_name -> api.items.story.types.v1.MomentCreateAttr
	29, // 9: api.items.story.v2.CreateHauntStoryRequest.cover:type_name -> api.resource.types.v1.Resource
	25, // 10: api.items.story.v2.CreateHauntStoryResponse.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	20, // 11: api.items.story.v2.AddCapturedBooInToCollectedStickersResponse.added_collected_sticker:type_name -> api.items.story.v2.AddCapturedBooInToCollectedStickersResponse.CollectedSticker
	30, // 12: api.items.story.v2.ReportHauntShowRequest.show_info_sence:type_name -> api.items.story.types.v1.HauntBooShowInfoSence
	31, // 13: api.items.story.v2.ReportHauntShowResponse.updated_haunt_boo_show_info:type_name -> api.items.story.types.v1.HauntBooShowInfo
	32, // 14: api.items.story.v2.CreateHauntStoryRequest.BooWithQuestionAndAnswer.questions_with_answers:type_name -> api.items.story.types.v1.HauntQuestionWithAnswer
	33, // 15: api.items.story.v2.AddCapturedBooInToCollectedStickersResponse.CollectedSticker.sticker:type_name -> api.items.story.types.v1.HideSticker
	16, // [16:16] is the sub-list for method output_type
	16, // [16:16] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_api_items_story_v2_haunt_proto_init() }
func file_api_items_story_v2_haunt_proto_init() {
	if File_api_items_story_v2_haunt_proto != nil {
		return
	}
	file_api_items_story_v2_haunt_proto_msgTypes[11].OneofWrappers = []any{}
	file_api_items_story_v2_haunt_proto_msgTypes[19].OneofWrappers = []any{
		(*CreateHauntStoryRequest_BooWithQuestionAndAnswer_BooId)(nil),
		(*CreateHauntStoryRequest_BooWithQuestionAndAnswer_UserAvatarId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v2_haunt_proto_rawDesc), len(file_api_items_story_v2_haunt_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v2_haunt_proto_goTypes,
		DependencyIndexes: file_api_items_story_v2_haunt_proto_depIdxs,
		MessageInfos:      file_api_items_story_v2_haunt_proto_msgTypes,
	}.Build()
	File_api_items_story_v2_haunt_proto = out.File
	file_api_items_story_v2_haunt_proto_goTypes = nil
	file_api_items_story_v2_haunt_proto_depIdxs = nil
}
