// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v2/story.proto

package api_items_story_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	api_im_message_types_v1 "boson/api/im/message/types/v1"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = api_im_message_types_v1.ConsumeStatus(0)

	_ = api_items_story_types_v1.StoryPlayType(0)
)

// Validate checks the field values on ListHomePageStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHomePageStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHomePageStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListHomePageStoryRequestV2MultiError, or nil if none found.
func (m *ListHomePageStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHomePageStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListHomePageStoryRequestV2ValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListHomePageStoryRequestV2ValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListHomePageStoryRequestV2ValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListHomePageStoryRequestV2ValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UseRecommended

	if len(errors) > 0 {
		return ListHomePageStoryRequestV2MultiError(errors)
	}

	return nil
}

// ListHomePageStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by ListHomePageStoryRequestV2.ValidateAll() if
// the designated constraints aren't met.
type ListHomePageStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHomePageStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHomePageStoryRequestV2MultiError) AllErrors() []error { return m }

// ListHomePageStoryRequestV2ValidationError is the validation error returned
// by ListHomePageStoryRequestV2.Validate if the designated constraints aren't met.
type ListHomePageStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHomePageStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHomePageStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHomePageStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHomePageStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHomePageStoryRequestV2ValidationError) ErrorName() string {
	return "ListHomePageStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ListHomePageStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHomePageStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHomePageStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHomePageStoryRequestV2ValidationError{}

// Validate checks the field values on ListHomePageStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListHomePageStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListHomePageStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListHomePageStoryResponseV2MultiError, or nil if none found.
func (m *ListHomePageStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ListHomePageStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListHomePageStoryResponseV2ValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListHomePageStoryResponseV2ValidationError{
						field:  fmt.Sprintf("Stories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListHomePageStoryResponseV2ValidationError{
					field:  fmt.Sprintf("Stories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListHomePageStoryResponseV2ValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListHomePageStoryResponseV2ValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListHomePageStoryResponseV2ValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortRequestId

	if all {
		switch v := interface{}(m.GetUserCreatedPortals()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListHomePageStoryResponseV2ValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListHomePageStoryResponseV2ValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserCreatedPortals()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListHomePageStoryResponseV2ValidationError{
				field:  "UserCreatedPortals",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPortals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListHomePageStoryResponseV2ValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListHomePageStoryResponseV2ValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListHomePageStoryResponseV2ValidationError{
					field:  fmt.Sprintf("Portals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPortalListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListHomePageStoryResponseV2ValidationError{
					field:  "PortalListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListHomePageStoryResponseV2ValidationError{
					field:  "PortalListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortalListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListHomePageStoryResponseV2ValidationError{
				field:  "PortalListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHauntBooShowInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListHomePageStoryResponseV2ValidationError{
						field:  fmt.Sprintf("HauntBooShowInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListHomePageStoryResponseV2ValidationError{
						field:  fmt.Sprintf("HauntBooShowInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListHomePageStoryResponseV2ValidationError{
					field:  fmt.Sprintf("HauntBooShowInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListHomePageStoryResponseV2MultiError(errors)
	}

	return nil
}

// ListHomePageStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by ListHomePageStoryResponseV2.ValidateAll() if
// the designated constraints aren't met.
type ListHomePageStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListHomePageStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListHomePageStoryResponseV2MultiError) AllErrors() []error { return m }

// ListHomePageStoryResponseV2ValidationError is the validation error returned
// by ListHomePageStoryResponseV2.Validate if the designated constraints
// aren't met.
type ListHomePageStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListHomePageStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListHomePageStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListHomePageStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListHomePageStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListHomePageStoryResponseV2ValidationError) ErrorName() string {
	return "ListHomePageStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ListHomePageStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListHomePageStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListHomePageStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListHomePageStoryResponseV2ValidationError{}

// Validate checks the field values on ListCreatorStoryRequestV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCreatorStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCreatorStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCreatorStoryRequestV2MultiError, or nil if none found.
func (m *ListCreatorStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCreatorStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ListCreatorStoryRequestV2_CreatorId_Pattern.MatchString(m.GetCreatorId()) {
		err := ListCreatorStoryRequestV2ValidationError{
			field:  "CreatorId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetListRequest() == nil {
		err := ListCreatorStoryRequestV2ValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCreatorStoryRequestV2ValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCreatorStoryRequestV2ValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCreatorStoryRequestV2ValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCreatorStoryRequestV2MultiError(errors)
	}

	return nil
}

// ListCreatorStoryRequestV2MultiError is an error wrapping multiple validation
// errors returned by ListCreatorStoryRequestV2.ValidateAll() if the
// designated constraints aren't met.
type ListCreatorStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCreatorStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCreatorStoryRequestV2MultiError) AllErrors() []error { return m }

// ListCreatorStoryRequestV2ValidationError is the validation error returned by
// ListCreatorStoryRequestV2.Validate if the designated constraints aren't met.
type ListCreatorStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCreatorStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCreatorStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCreatorStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCreatorStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCreatorStoryRequestV2ValidationError) ErrorName() string {
	return "ListCreatorStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ListCreatorStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCreatorStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCreatorStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCreatorStoryRequestV2ValidationError{}

var _ListCreatorStoryRequestV2_CreatorId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ListCreatorStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListCreatorStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListCreatorStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListCreatorStoryResponseV2MultiError, or nil if none found.
func (m *ListCreatorStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCreatorStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCreatedStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("CreatedStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("CreatedStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListCreatorStoryResponseV2ValidationError{
					field:  fmt.Sprintf("CreatedStories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCreatorStoryResponseV2ValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCreatorStoryResponseV2ValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCreatorStoryResponseV2ValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListCreatorStoryResponseV2MultiError(errors)
	}

	return nil
}

// ListCreatorStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by ListCreatorStoryResponseV2.ValidateAll() if
// the designated constraints aren't met.
type ListCreatorStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCreatorStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCreatorStoryResponseV2MultiError) AllErrors() []error { return m }

// ListCreatorStoryResponseV2ValidationError is the validation error returned
// by ListCreatorStoryResponseV2.Validate if the designated constraints aren't met.
type ListCreatorStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCreatorStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCreatorStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCreatorStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCreatorStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCreatorStoryResponseV2ValidationError) ErrorName() string {
	return "ListCreatorStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ListCreatorStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCreatorStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCreatorStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCreatorStoryResponseV2ValidationError{}

// Validate checks the field values on ListFollowingCreatorStoryRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListFollowingCreatorStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFollowingCreatorStoryRequestV2
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListFollowingCreatorStoryRequestV2MultiError, or nil if none found.
func (m *ListFollowingCreatorStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFollowingCreatorStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetListRequest() == nil {
		err := ListFollowingCreatorStoryRequestV2ValidationError{
			field:  "ListRequest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryRequestV2ValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryRequestV2ValidationError{
					field:  "ListRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFollowingCreatorStoryRequestV2ValidationError{
				field:  "ListRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListFollowingCreatorStoryRequestV2MultiError(errors)
	}

	return nil
}

// ListFollowingCreatorStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by
// ListFollowingCreatorStoryRequestV2.ValidateAll() if the designated
// constraints aren't met.
type ListFollowingCreatorStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFollowingCreatorStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFollowingCreatorStoryRequestV2MultiError) AllErrors() []error { return m }

// ListFollowingCreatorStoryRequestV2ValidationError is the validation error
// returned by ListFollowingCreatorStoryRequestV2.Validate if the designated
// constraints aren't met.
type ListFollowingCreatorStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFollowingCreatorStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFollowingCreatorStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFollowingCreatorStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFollowingCreatorStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFollowingCreatorStoryRequestV2ValidationError) ErrorName() string {
	return "ListFollowingCreatorStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ListFollowingCreatorStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFollowingCreatorStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFollowingCreatorStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFollowingCreatorStoryRequestV2ValidationError{}

// Validate checks the field values on ListFollowingCreatorStoryResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListFollowingCreatorStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListFollowingCreatorStoryResponseV2
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListFollowingCreatorStoryResponseV2MultiError, or nil if none found.
func (m *ListFollowingCreatorStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ListFollowingCreatorStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFollowingCreatorStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("FollowingCreatorStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("FollowingCreatorStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFollowingCreatorStoryResponseV2ValidationError{
					field:  fmt.Sprintf("FollowingCreatorStories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
					field:  "ListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFollowingCreatorStoryResponseV2ValidationError{
				field:  "ListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecommendedUnfollowedCreatorStories() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("RecommendedUnfollowedCreatorStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("RecommendedUnfollowedCreatorStories[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFollowingCreatorStoryResponseV2ValidationError{
					field:  fmt.Sprintf("RecommendedUnfollowedCreatorStories[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUserCreatedPortals()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
					field:  "UserCreatedPortals",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserCreatedPortals()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFollowingCreatorStoryResponseV2ValidationError{
				field:  "UserCreatedPortals",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPortals() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("Portals[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFollowingCreatorStoryResponseV2ValidationError{
					field:  fmt.Sprintf("Portals[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPortalListResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
					field:  "PortalListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
					field:  "PortalListResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPortalListResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListFollowingCreatorStoryResponseV2ValidationError{
				field:  "PortalListResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetHauntBooShowInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("HauntBooShowInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListFollowingCreatorStoryResponseV2ValidationError{
						field:  fmt.Sprintf("HauntBooShowInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListFollowingCreatorStoryResponseV2ValidationError{
					field:  fmt.Sprintf("HauntBooShowInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListFollowingCreatorStoryResponseV2MultiError(errors)
	}

	return nil
}

// ListFollowingCreatorStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by
// ListFollowingCreatorStoryResponseV2.ValidateAll() if the designated
// constraints aren't met.
type ListFollowingCreatorStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListFollowingCreatorStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListFollowingCreatorStoryResponseV2MultiError) AllErrors() []error { return m }

// ListFollowingCreatorStoryResponseV2ValidationError is the validation error
// returned by ListFollowingCreatorStoryResponseV2.Validate if the designated
// constraints aren't met.
type ListFollowingCreatorStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListFollowingCreatorStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListFollowingCreatorStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListFollowingCreatorStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListFollowingCreatorStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListFollowingCreatorStoryResponseV2ValidationError) ErrorName() string {
	return "ListFollowingCreatorStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ListFollowingCreatorStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListFollowingCreatorStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListFollowingCreatorStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListFollowingCreatorStoryResponseV2ValidationError{}

// Validate checks the field values on CreateExchangeImageStoryRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangeImageStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangeImageStoryRequestV2
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangeImageStoryRequestV2MultiError, or nil if none found.
func (m *CreateExchangeImageStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangeImageStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangeImageStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangeImageStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangeImageStoryRequestV2ValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateExchangeImageStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateExchangeImageStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateExchangeImageStoryRequestV2ValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.FromStoryId != nil {

		if !_CreateExchangeImageStoryRequestV2_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := CreateExchangeImageStoryRequestV2ValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateExchangeImageStoryRequestV2MultiError(errors)
	}

	return nil
}

// CreateExchangeImageStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangeImageStoryRequestV2.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangeImageStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangeImageStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangeImageStoryRequestV2MultiError) AllErrors() []error { return m }

// CreateExchangeImageStoryRequestV2ValidationError is the validation error
// returned by CreateExchangeImageStoryRequestV2.Validate if the designated
// constraints aren't met.
type CreateExchangeImageStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangeImageStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangeImageStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangeImageStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangeImageStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangeImageStoryRequestV2ValidationError) ErrorName() string {
	return "CreateExchangeImageStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangeImageStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangeImageStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangeImageStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangeImageStoryRequestV2ValidationError{}

var _CreateExchangeImageStoryRequestV2_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateExchangeImageStoryResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateExchangeImageStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateExchangeImageStoryResponseV2
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateExchangeImageStoryResponseV2MultiError, or nil if none found.
func (m *CreateExchangeImageStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateExchangeImageStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateExchangeImageStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateExchangeImageStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateExchangeImageStoryResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateExchangeImageStoryResponseV2MultiError(errors)
	}

	return nil
}

// CreateExchangeImageStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by
// CreateExchangeImageStoryResponseV2.ValidateAll() if the designated
// constraints aren't met.
type CreateExchangeImageStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateExchangeImageStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateExchangeImageStoryResponseV2MultiError) AllErrors() []error { return m }

// CreateExchangeImageStoryResponseV2ValidationError is the validation error
// returned by CreateExchangeImageStoryResponseV2.Validate if the designated
// constraints aren't met.
type CreateExchangeImageStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateExchangeImageStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateExchangeImageStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateExchangeImageStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateExchangeImageStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateExchangeImageStoryResponseV2ValidationError) ErrorName() string {
	return "CreateExchangeImageStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateExchangeImageStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateExchangeImageStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateExchangeImageStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateExchangeImageStoryResponseV2ValidationError{}

// Validate checks the field values on CreateTurtleSoupStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTurtleSoupStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTurtleSoupStoryRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateTurtleSoupStoryRequestV2MultiError, or nil if none found.
func (m *CreateTurtleSoupStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTurtleSoupStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTurtleSoupStoryRequestV2ValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateTurtleSoupStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateTurtleSoupStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateTurtleSoupStoryRequestV2ValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateTurtleSoupStoryRequestV2MultiError(errors)
	}

	return nil
}

// CreateTurtleSoupStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by CreateTurtleSoupStoryRequestV2.ValidateAll()
// if the designated constraints aren't met.
type CreateTurtleSoupStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTurtleSoupStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTurtleSoupStoryRequestV2MultiError) AllErrors() []error { return m }

// CreateTurtleSoupStoryRequestV2ValidationError is the validation error
// returned by CreateTurtleSoupStoryRequestV2.Validate if the designated
// constraints aren't met.
type CreateTurtleSoupStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTurtleSoupStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTurtleSoupStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTurtleSoupStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTurtleSoupStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTurtleSoupStoryRequestV2ValidationError) ErrorName() string {
	return "CreateTurtleSoupStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTurtleSoupStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTurtleSoupStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTurtleSoupStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTurtleSoupStoryRequestV2ValidationError{}

// Validate checks the field values on CreateTurtleSoupStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTurtleSoupStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTurtleSoupStoryResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateTurtleSoupStoryResponseV2MultiError, or nil if none found.
func (m *CreateTurtleSoupStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTurtleSoupStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTurtleSoupStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTurtleSoupStoryResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTurtleSoupStoryResponseV2MultiError(errors)
	}

	return nil
}

// CreateTurtleSoupStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by CreateTurtleSoupStoryResponseV2.ValidateAll()
// if the designated constraints aren't met.
type CreateTurtleSoupStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTurtleSoupStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTurtleSoupStoryResponseV2MultiError) AllErrors() []error { return m }

// CreateTurtleSoupStoryResponseV2ValidationError is the validation error
// returned by CreateTurtleSoupStoryResponseV2.Validate if the designated
// constraints aren't met.
type CreateTurtleSoupStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTurtleSoupStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTurtleSoupStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTurtleSoupStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTurtleSoupStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTurtleSoupStoryResponseV2ValidationError) ErrorName() string {
	return "CreateTurtleSoupStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTurtleSoupStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTurtleSoupStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTurtleSoupStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTurtleSoupStoryResponseV2ValidationError{}

// Validate checks the field values on CreateUnmuteStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUnmuteStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUnmuteStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUnmuteStoryRequestV2MultiError, or nil if none found.
func (m *CreateUnmuteStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUnmuteStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUnmuteStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUnmuteStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUnmuteStoryRequestV2ValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateUnmuteStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateUnmuteStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateUnmuteStoryRequestV2ValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateUnmuteStoryRequestV2MultiError(errors)
	}

	return nil
}

// CreateUnmuteStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by CreateUnmuteStoryRequestV2.ValidateAll() if
// the designated constraints aren't met.
type CreateUnmuteStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUnmuteStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUnmuteStoryRequestV2MultiError) AllErrors() []error { return m }

// CreateUnmuteStoryRequestV2ValidationError is the validation error returned
// by CreateUnmuteStoryRequestV2.Validate if the designated constraints aren't met.
type CreateUnmuteStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUnmuteStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUnmuteStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUnmuteStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUnmuteStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUnmuteStoryRequestV2ValidationError) ErrorName() string {
	return "CreateUnmuteStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUnmuteStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUnmuteStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUnmuteStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUnmuteStoryRequestV2ValidationError{}

// Validate checks the field values on CreateUnmuteStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUnmuteStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUnmuteStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUnmuteStoryResponseV2MultiError, or nil if none found.
func (m *CreateUnmuteStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUnmuteStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUnmuteStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUnmuteStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUnmuteStoryResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateUnmuteStoryResponseV2MultiError(errors)
	}

	return nil
}

// CreateUnmuteStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by CreateUnmuteStoryResponseV2.ValidateAll() if
// the designated constraints aren't met.
type CreateUnmuteStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUnmuteStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUnmuteStoryResponseV2MultiError) AllErrors() []error { return m }

// CreateUnmuteStoryResponseV2ValidationError is the validation error returned
// by CreateUnmuteStoryResponseV2.Validate if the designated constraints
// aren't met.
type CreateUnmuteStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUnmuteStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUnmuteStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUnmuteStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUnmuteStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUnmuteStoryResponseV2ValidationError) ErrorName() string {
	return "CreateUnmuteStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUnmuteStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUnmuteStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUnmuteStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUnmuteStoryResponseV2ValidationError{}

// Validate checks the field values on CreateNowShotStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNowShotStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNowShotStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNowShotStoryRequestV2MultiError, or nil if none found.
func (m *CreateNowShotStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNowShotStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNowShotStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNowShotStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNowShotStoryRequestV2ValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateNowShotStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateNowShotStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateNowShotStoryRequestV2ValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.FromStoryId != nil {

		if !_CreateNowShotStoryRequestV2_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := CreateNowShotStoryRequestV2ValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateNowShotStoryRequestV2MultiError(errors)
	}

	return nil
}

// CreateNowShotStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by CreateNowShotStoryRequestV2.ValidateAll() if
// the designated constraints aren't met.
type CreateNowShotStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNowShotStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNowShotStoryRequestV2MultiError) AllErrors() []error { return m }

// CreateNowShotStoryRequestV2ValidationError is the validation error returned
// by CreateNowShotStoryRequestV2.Validate if the designated constraints
// aren't met.
type CreateNowShotStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNowShotStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNowShotStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNowShotStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNowShotStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNowShotStoryRequestV2ValidationError) ErrorName() string {
	return "CreateNowShotStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNowShotStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNowShotStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNowShotStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNowShotStoryRequestV2ValidationError{}

var _CreateNowShotStoryRequestV2_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateNowShotStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNowShotStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNowShotStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNowShotStoryResponseV2MultiError, or nil if none found.
func (m *CreateNowShotStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNowShotStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNowShotStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNowShotStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNowShotStoryResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNowShotStoryResponseV2MultiError(errors)
	}

	return nil
}

// CreateNowShotStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by CreateNowShotStoryResponseV2.ValidateAll() if
// the designated constraints aren't met.
type CreateNowShotStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNowShotStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNowShotStoryResponseV2MultiError) AllErrors() []error { return m }

// CreateNowShotStoryResponseV2ValidationError is the validation error returned
// by CreateNowShotStoryResponseV2.Validate if the designated constraints
// aren't met.
type CreateNowShotStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNowShotStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNowShotStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNowShotStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNowShotStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNowShotStoryResponseV2ValidationError) ErrorName() string {
	return "CreateNowShotStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNowShotStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNowShotStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNowShotStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNowShotStoryResponseV2ValidationError{}

// Validate checks the field values on ConsumeNowShotStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeNowShotStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeNowShotStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeNowShotStoryRequestV2MultiError, or nil if none found.
func (m *ConsumeNowShotStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeNowShotStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StoryId

	// no validation rules for Succeeded

	if all {
		switch v := interface{}(m.GetResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeNowShotStoryRequestV2ValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeNowShotStoryRequestV2ValidationError{
					field:  "Resource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeNowShotStoryRequestV2ValidationError{
				field:  "Resource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Ttl != nil {
		// no validation rules for Ttl
	}

	if len(errors) > 0 {
		return ConsumeNowShotStoryRequestV2MultiError(errors)
	}

	return nil
}

// ConsumeNowShotStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by ConsumeNowShotStoryRequestV2.ValidateAll() if
// the designated constraints aren't met.
type ConsumeNowShotStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeNowShotStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeNowShotStoryRequestV2MultiError) AllErrors() []error { return m }

// ConsumeNowShotStoryRequestV2ValidationError is the validation error returned
// by ConsumeNowShotStoryRequestV2.Validate if the designated constraints
// aren't met.
type ConsumeNowShotStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeNowShotStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeNowShotStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeNowShotStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeNowShotStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeNowShotStoryRequestV2ValidationError) ErrorName() string {
	return "ConsumeNowShotStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeNowShotStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeNowShotStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeNowShotStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeNowShotStoryRequestV2ValidationError{}

// Validate checks the field values on StoryDetailResponseV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoryDetailResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StoryDetailResponseV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoryDetailResponseV2MultiError, or nil if none found.
func (m *StoryDetailResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *StoryDetailResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoryDetailResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoryDetailResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoryDetailResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoryDetailResponseV2MultiError(errors)
	}

	return nil
}

// StoryDetailResponseV2MultiError is an error wrapping multiple validation
// errors returned by StoryDetailResponseV2.ValidateAll() if the designated
// constraints aren't met.
type StoryDetailResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoryDetailResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoryDetailResponseV2MultiError) AllErrors() []error { return m }

// StoryDetailResponseV2ValidationError is the validation error returned by
// StoryDetailResponseV2.Validate if the designated constraints aren't met.
type StoryDetailResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoryDetailResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoryDetailResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoryDetailResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoryDetailResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoryDetailResponseV2ValidationError) ErrorName() string {
	return "StoryDetailResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e StoryDetailResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoryDetailResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoryDetailResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoryDetailResponseV2ValidationError{}

// Validate checks the field values on SendMessageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendMessageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMessageRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendMessageRequestMultiError, or nil if none found.
func (m *SendMessageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMessageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_SendMessageRequest_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := SendMessageRequestValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for MessageType

	if m.UserVideoKey != nil {
		// no validation rules for UserVideoKey
	}

	if m.UserVideoCoverKey != nil {
		// no validation rules for UserVideoCoverKey
	}

	if m.ConsumerStoryCoverKey != nil {
		// no validation rules for ConsumerStoryCoverKey
	}

	if m.ConsumeStatus != nil {
		// no validation rules for ConsumeStatus
	}

	if len(errors) > 0 {
		return SendMessageRequestMultiError(errors)
	}

	return nil
}

// SendMessageRequestMultiError is an error wrapping multiple validation errors
// returned by SendMessageRequest.ValidateAll() if the designated constraints
// aren't met.
type SendMessageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMessageRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMessageRequestMultiError) AllErrors() []error { return m }

// SendMessageRequestValidationError is the validation error returned by
// SendMessageRequest.Validate if the designated constraints aren't met.
type SendMessageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMessageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMessageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMessageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMessageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMessageRequestValidationError) ErrorName() string {
	return "SendMessageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendMessageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMessageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMessageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMessageRequestValidationError{}

var _SendMessageRequest_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on SendMessageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendMessageResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendMessageResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendMessageResponseMultiError, or nil if none found.
func (m *SendMessageResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendMessageResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendMessageResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendMessageResponseValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendMessageResponseValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendMessageResponseMultiError(errors)
	}

	return nil
}

// SendMessageResponseMultiError is an error wrapping multiple validation
// errors returned by SendMessageResponse.ValidateAll() if the designated
// constraints aren't met.
type SendMessageResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendMessageResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendMessageResponseMultiError) AllErrors() []error { return m }

// SendMessageResponseValidationError is the validation error returned by
// SendMessageResponse.Validate if the designated constraints aren't met.
type SendMessageResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendMessageResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendMessageResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendMessageResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendMessageResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendMessageResponseValidationError) ErrorName() string {
	return "SendMessageResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendMessageResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendMessageResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendMessageResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendMessageResponseValidationError{}

// Validate checks the field values on ListCreatorStoryResponseV2_CreatedStory
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ListCreatorStoryResponseV2_CreatedStory) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListCreatorStoryResponseV2_CreatedStory with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// ListCreatorStoryResponseV2_CreatedStoryMultiError, or nil if none found.
func (m *ListCreatorStoryResponseV2_CreatedStory) ValidateAll() error {
	return m.validate(true)
}

func (m *ListCreatorStoryResponseV2_CreatedStory) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStory()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListCreatorStoryResponseV2_CreatedStoryValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListCreatorStoryResponseV2_CreatedStoryValidationError{
					field:  "Story",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStory()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListCreatorStoryResponseV2_CreatedStoryValidationError{
				field:  "Story",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsTop

	if len(errors) > 0 {
		return ListCreatorStoryResponseV2_CreatedStoryMultiError(errors)
	}

	return nil
}

// ListCreatorStoryResponseV2_CreatedStoryMultiError is an error wrapping
// multiple validation errors returned by
// ListCreatorStoryResponseV2_CreatedStory.ValidateAll() if the designated
// constraints aren't met.
type ListCreatorStoryResponseV2_CreatedStoryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListCreatorStoryResponseV2_CreatedStoryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListCreatorStoryResponseV2_CreatedStoryMultiError) AllErrors() []error { return m }

// ListCreatorStoryResponseV2_CreatedStoryValidationError is the validation
// error returned by ListCreatorStoryResponseV2_CreatedStory.Validate if the
// designated constraints aren't met.
type ListCreatorStoryResponseV2_CreatedStoryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListCreatorStoryResponseV2_CreatedStoryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListCreatorStoryResponseV2_CreatedStoryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListCreatorStoryResponseV2_CreatedStoryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListCreatorStoryResponseV2_CreatedStoryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListCreatorStoryResponseV2_CreatedStoryValidationError) ErrorName() string {
	return "ListCreatorStoryResponseV2_CreatedStoryValidationError"
}

// Error satisfies the builtin error interface
func (e ListCreatorStoryResponseV2_CreatedStoryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListCreatorStoryResponseV2_CreatedStory.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListCreatorStoryResponseV2_CreatedStoryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListCreatorStoryResponseV2_CreatedStoryValidationError{}
