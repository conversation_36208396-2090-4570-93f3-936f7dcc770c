syntax = "proto3";

package api.items.story.v2;

import "api/items/story/types/v1/haunt.types.proto";
import "api/items/story/v1/story.privacy.setting.proto";
import "api/items/story/types/v1/types.proto";
import "api/items/story/types/v1/base_types.proto";
import "api/items/story/types/v1/hide.types.proto";
import "api/common/v1/common.proto";
import "validate/validate.proto";
import "api/resource/types/v1/types.proto";


option go_package = "boson/api/items/story/v2;api_items_story_v2";


message ImageCheckRequest {
	string image_key = 1;
	string text_condition = 2;
}

message ImageCheckResponse {
	bool pass = 1;
	// float64 格式
	repeated string point = 2;
	string feedback = 3;
}

message ListHauntRandomAvatarsRequest {}
message ListHauntRandomAvatarsResponse {
	repeated string avatar_urls = 1;
}

message ListHauntQuestionsRequest {}
message ListHauntQuestionsResponse {
	repeated api.items.story.types.v1.HauntQuestion questions = 1;
}

message ListHauntBooAssistRequest {
	api.common.v1.ListRequest list_request = 1[(validate.rules).message = {required: true}];
}
message ListHauntBooAssistResponse {
	repeated api.items.story.types.v1.HauntBoo boos_with_questions_and_answers = 1;
	api.common.v1.ListResponse list_response = 2;
}

message ConsumeHauntStoryRequest {
	string story_id = 1;
	bool unlocked = 2;
	bool is_from_feed_or_friends = 3;
}
message ConsumeHauntStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}

message SendHauntCaptureVideoRequest {
	string story_id = 1;
	string video_key = 2;
	string video_cover_key = 3;
}

message CreateHauntStoryRequest {
	message BooWithQuestionAndAnswer {
		oneof object_id {
			string boo_id = 1;
			string user_avatar_id = 2;
		}
		repeated api.items.story.types.v1.HauntQuestionWithAnswer questions_with_answers = 3;
	}
	repeated BooWithQuestionAndAnswer boos_with_questions_and_answers = 1;
	repeated api.items.story.types.v1.AttachmentText captions = 2;
	optional api.items.story.v1.PrivacySettingUpdateAttr privacy_setting = 3;
	repeated api.items.story.types.v1.MomentCreateAttr moment_create_attrs = 4;
	optional api.resource.types.v1.Resource cover = 5;
}
message CreateHauntStoryResponse {
	api.items.story.types.v1.StoryDetail story_detail = 1;
}


message AddCapturedBooInToCollectedStickersRequest {
	string boo_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}
message AddCapturedBooInToCollectedStickersResponse {
	message CollectedSticker {
		api.items.story.types.v1.HideSticker sticker = 1[(validate.rules).message.required = true];
		uint32 collected_at_unix_timestamp = 2;
		bool is_top = 3;
	}
	CollectedSticker added_collected_sticker = 1;
}



message AddCaptureBooIntoMyAssistRequest {
	string boo_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	string story_id = 2[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
}
message AddCaptureBooIntoMyAssistResponse {}

// 上报一次 haunt boo 的展现
message ReportHauntShowRequest {
	string boo_id = 1[(validate.rules).string = {
		pattern: "^[0-9]+$",
	}];
	api.items.story.types.v1.HauntBooShowInfoSence show_info_sence = 2;
}
message ReportHauntShowResponse {
	api.items.story.types.v1.HauntBooShowInfo updated_haunt_boo_show_info = 1;
}