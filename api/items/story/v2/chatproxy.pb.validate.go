// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/items/story/v2/chatproxy.proto

package api_items_story_v2

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetChatProxyNextTopicRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChatProxyNextTopicRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatProxyNextTopicRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetChatProxyNextTopicRequestV2MultiError, or nil if none found.
func (m *GetChatProxyNextTopicRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatProxyNextTopicRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_GetChatProxyNextTopicRequestV2_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := GetChatProxyNextTopicRequestV2ValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UserAudioKey

	// no validation rules for RoundIndex

	if len(errors) > 0 {
		return GetChatProxyNextTopicRequestV2MultiError(errors)
	}

	return nil
}

// GetChatProxyNextTopicRequestV2MultiError is an error wrapping multiple
// validation errors returned by GetChatProxyNextTopicRequestV2.ValidateAll()
// if the designated constraints aren't met.
type GetChatProxyNextTopicRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatProxyNextTopicRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatProxyNextTopicRequestV2MultiError) AllErrors() []error { return m }

// GetChatProxyNextTopicRequestV2ValidationError is the validation error
// returned by GetChatProxyNextTopicRequestV2.Validate if the designated
// constraints aren't met.
type GetChatProxyNextTopicRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatProxyNextTopicRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatProxyNextTopicRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatProxyNextTopicRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatProxyNextTopicRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatProxyNextTopicRequestV2ValidationError) ErrorName() string {
	return "GetChatProxyNextTopicRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatProxyNextTopicRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatProxyNextTopicRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatProxyNextTopicRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatProxyNextTopicRequestV2ValidationError{}

var _GetChatProxyNextTopicRequestV2_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on GetChatProxyNextTopicResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetChatProxyNextTopicResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatProxyNextTopicResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetChatProxyNextTopicResponseV2MultiError, or nil if none found.
func (m *GetChatProxyNextTopicResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatProxyNextTopicResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetQuestion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatProxyNextTopicResponseV2ValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatProxyNextTopicResponseV2ValidationError{
					field:  "Question",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatProxyNextTopicResponseV2ValidationError{
				field:  "Question",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for End

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatProxyNextTopicResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatProxyNextTopicResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatProxyNextTopicResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetChatProxyNextTopicResponseV2MultiError(errors)
	}

	return nil
}

// GetChatProxyNextTopicResponseV2MultiError is an error wrapping multiple
// validation errors returned by GetChatProxyNextTopicResponseV2.ValidateAll()
// if the designated constraints aren't met.
type GetChatProxyNextTopicResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatProxyNextTopicResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatProxyNextTopicResponseV2MultiError) AllErrors() []error { return m }

// GetChatProxyNextTopicResponseV2ValidationError is the validation error
// returned by GetChatProxyNextTopicResponseV2.Validate if the designated
// constraints aren't met.
type GetChatProxyNextTopicResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatProxyNextTopicResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatProxyNextTopicResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatProxyNextTopicResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatProxyNextTopicResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatProxyNextTopicResponseV2ValidationError) ErrorName() string {
	return "GetChatProxyNextTopicResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatProxyNextTopicResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatProxyNextTopicResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatProxyNextTopicResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatProxyNextTopicResponseV2ValidationError{}

// Validate checks the field values on ConsumeChatProxyRequestV2 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeChatProxyRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeChatProxyRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeChatProxyRequestV2MultiError, or nil if none found.
func (m *ConsumeChatProxyRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeChatProxyRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if !_ConsumeChatProxyRequestV2_StoryId_Pattern.MatchString(m.GetStoryId()) {
		err := ConsumeChatProxyRequestV2ValidationError{
			field:  "StoryId",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UserVideoKey

	// no validation rules for UserVideoCoverKey

	if len(errors) > 0 {
		return ConsumeChatProxyRequestV2MultiError(errors)
	}

	return nil
}

// ConsumeChatProxyRequestV2MultiError is an error wrapping multiple validation
// errors returned by ConsumeChatProxyRequestV2.ValidateAll() if the
// designated constraints aren't met.
type ConsumeChatProxyRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeChatProxyRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeChatProxyRequestV2MultiError) AllErrors() []error { return m }

// ConsumeChatProxyRequestV2ValidationError is the validation error returned by
// ConsumeChatProxyRequestV2.Validate if the designated constraints aren't met.
type ConsumeChatProxyRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeChatProxyRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeChatProxyRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeChatProxyRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeChatProxyRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeChatProxyRequestV2ValidationError) ErrorName() string {
	return "ConsumeChatProxyRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeChatProxyRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeChatProxyRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeChatProxyRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeChatProxyRequestV2ValidationError{}

var _ConsumeChatProxyRequestV2_StoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ConsumeChatProxyResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConsumeChatProxyResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsumeChatProxyResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConsumeChatProxyResponseV2MultiError, or nil if none found.
func (m *ConsumeChatProxyResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsumeChatProxyResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsumeChatProxyResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsumeChatProxyResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsumeChatProxyResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsumeChatProxyResponseV2MultiError(errors)
	}

	return nil
}

// ConsumeChatProxyResponseV2MultiError is an error wrapping multiple
// validation errors returned by ConsumeChatProxyResponseV2.ValidateAll() if
// the designated constraints aren't met.
type ConsumeChatProxyResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsumeChatProxyResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsumeChatProxyResponseV2MultiError) AllErrors() []error { return m }

// ConsumeChatProxyResponseV2ValidationError is the validation error returned
// by ConsumeChatProxyResponseV2.Validate if the designated constraints aren't met.
type ConsumeChatProxyResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsumeChatProxyResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsumeChatProxyResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsumeChatProxyResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsumeChatProxyResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsumeChatProxyResponseV2ValidationError) ErrorName() string {
	return "ConsumeChatProxyResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e ConsumeChatProxyResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsumeChatProxyResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsumeChatProxyResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsumeChatProxyResponseV2ValidationError{}

// Validate checks the field values on CreateChatProxyStoryRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatProxyStoryRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatProxyStoryRequestV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateChatProxyStoryRequestV2MultiError, or nil if none found.
func (m *CreateChatProxyStoryRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatProxyStoryRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPlayConfig() == nil {
		err := CreateChatProxyStoryRequestV2ValidationError{
			field:  "PlayConfig",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPlayConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChatProxyStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChatProxyStoryRequestV2ValidationError{
					field:  "PlayConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlayConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChatProxyStoryRequestV2ValidationError{
				field:  "PlayConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.PrivacySetting != nil {

		if all {
			switch v := interface{}(m.GetPrivacySetting()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateChatProxyStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateChatProxyStoryRequestV2ValidationError{
						field:  "PrivacySetting",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivacySetting()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateChatProxyStoryRequestV2ValidationError{
					field:  "PrivacySetting",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if m.FromStoryId != nil {

		if !_CreateChatProxyStoryRequestV2_FromStoryId_Pattern.MatchString(m.GetFromStoryId()) {
			err := CreateChatProxyStoryRequestV2ValidationError{
				field:  "FromStoryId",
				reason: "value does not match regex pattern \"^[0-9]+$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return CreateChatProxyStoryRequestV2MultiError(errors)
	}

	return nil
}

// CreateChatProxyStoryRequestV2MultiError is an error wrapping multiple
// validation errors returned by CreateChatProxyStoryRequestV2.ValidateAll()
// if the designated constraints aren't met.
type CreateChatProxyStoryRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatProxyStoryRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatProxyStoryRequestV2MultiError) AllErrors() []error { return m }

// CreateChatProxyStoryRequestV2ValidationError is the validation error
// returned by CreateChatProxyStoryRequestV2.Validate if the designated
// constraints aren't met.
type CreateChatProxyStoryRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatProxyStoryRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatProxyStoryRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatProxyStoryRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatProxyStoryRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatProxyStoryRequestV2ValidationError) ErrorName() string {
	return "CreateChatProxyStoryRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatProxyStoryRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatProxyStoryRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatProxyStoryRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatProxyStoryRequestV2ValidationError{}

var _CreateChatProxyStoryRequestV2_FromStoryId_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on CreateChatProxyStoryResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateChatProxyStoryResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatProxyStoryResponseV2 with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreateChatProxyStoryResponseV2MultiError, or nil if none found.
func (m *CreateChatProxyStoryResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatProxyStoryResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStoryDetail()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateChatProxyStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateChatProxyStoryResponseV2ValidationError{
					field:  "StoryDetail",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStoryDetail()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateChatProxyStoryResponseV2ValidationError{
				field:  "StoryDetail",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateChatProxyStoryResponseV2MultiError(errors)
	}

	return nil
}

// CreateChatProxyStoryResponseV2MultiError is an error wrapping multiple
// validation errors returned by CreateChatProxyStoryResponseV2.ValidateAll()
// if the designated constraints aren't met.
type CreateChatProxyStoryResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatProxyStoryResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatProxyStoryResponseV2MultiError) AllErrors() []error { return m }

// CreateChatProxyStoryResponseV2ValidationError is the validation error
// returned by CreateChatProxyStoryResponseV2.Validate if the designated
// constraints aren't met.
type CreateChatProxyStoryResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatProxyStoryResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatProxyStoryResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatProxyStoryResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatProxyStoryResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatProxyStoryResponseV2ValidationError) ErrorName() string {
	return "CreateChatProxyStoryResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatProxyStoryResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatProxyStoryResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatProxyStoryResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatProxyStoryResponseV2ValidationError{}
