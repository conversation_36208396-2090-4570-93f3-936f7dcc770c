// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: api/items/story/v2/roasted.proto

package api_items_story_v2

import (
	v1 "boson/api/items/story/types/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ConsumeRoastedStoryRequestV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryId       string                 `protobuf:"bytes,1,opt,name=story_id,json=storyId,proto3" json:"story_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeRoastedStoryRequestV2) Reset() {
	*x = ConsumeRoastedStoryRequestV2{}
	mi := &file_api_items_story_v2_roasted_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeRoastedStoryRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRoastedStoryRequestV2) ProtoMessage() {}

func (x *ConsumeRoastedStoryRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_roasted_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRoastedStoryRequestV2.ProtoReflect.Descriptor instead.
func (*ConsumeRoastedStoryRequestV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_roasted_proto_rawDescGZIP(), []int{0}
}

func (x *ConsumeRoastedStoryRequestV2) GetStoryId() string {
	if x != nil {
		return x.StoryId
	}
	return ""
}

type ConsumeRoastedStoryResponseV2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StoryDetail   *v1.StoryDetail        `protobuf:"bytes,1,opt,name=story_detail,json=storyDetail,proto3" json:"story_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConsumeRoastedStoryResponseV2) Reset() {
	*x = ConsumeRoastedStoryResponseV2{}
	mi := &file_api_items_story_v2_roasted_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConsumeRoastedStoryResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsumeRoastedStoryResponseV2) ProtoMessage() {}

func (x *ConsumeRoastedStoryResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_items_story_v2_roasted_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsumeRoastedStoryResponseV2.ProtoReflect.Descriptor instead.
func (*ConsumeRoastedStoryResponseV2) Descriptor() ([]byte, []int) {
	return file_api_items_story_v2_roasted_proto_rawDescGZIP(), []int{1}
}

func (x *ConsumeRoastedStoryResponseV2) GetStoryDetail() *v1.StoryDetail {
	if x != nil {
		return x.StoryDetail
	}
	return nil
}

var File_api_items_story_v2_roasted_proto protoreflect.FileDescriptor

const file_api_items_story_v2_roasted_proto_rawDesc = "" +
	"\n" +
	" api/items/story/v2/roasted.proto\x12\x12api.items.story.v2\x1a$api/items/story/types/v1/types.proto\x1a\x17validate/validate.proto\"J\n" +
	"\x1cConsumeRoastedStoryRequestV2\x12*\n" +
	"\bstory_id\x18\x01 \x01(\tB\x0f\xfaB\fr\n" +
	"2\b^[0-9]+$R\astoryId\"i\n" +
	"\x1dConsumeRoastedStoryResponseV2\x12H\n" +
	"\fstory_detail\x18\x01 \x01(\v2%.api.items.story.types.v1.StoryDetailR\vstoryDetailB-Z+boson/api/items/story/v2;api_items_story_v2b\x06proto3"

var (
	file_api_items_story_v2_roasted_proto_rawDescOnce sync.Once
	file_api_items_story_v2_roasted_proto_rawDescData []byte
)

func file_api_items_story_v2_roasted_proto_rawDescGZIP() []byte {
	file_api_items_story_v2_roasted_proto_rawDescOnce.Do(func() {
		file_api_items_story_v2_roasted_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_items_story_v2_roasted_proto_rawDesc), len(file_api_items_story_v2_roasted_proto_rawDesc)))
	})
	return file_api_items_story_v2_roasted_proto_rawDescData
}

var file_api_items_story_v2_roasted_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_items_story_v2_roasted_proto_goTypes = []any{
	(*ConsumeRoastedStoryRequestV2)(nil),  // 0: api.items.story.v2.ConsumeRoastedStoryRequestV2
	(*ConsumeRoastedStoryResponseV2)(nil), // 1: api.items.story.v2.ConsumeRoastedStoryResponseV2
	(*v1.StoryDetail)(nil),                // 2: api.items.story.types.v1.StoryDetail
}
var file_api_items_story_v2_roasted_proto_depIdxs = []int32{
	2, // 0: api.items.story.v2.ConsumeRoastedStoryResponseV2.story_detail:type_name -> api.items.story.types.v1.StoryDetail
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_items_story_v2_roasted_proto_init() }
func file_api_items_story_v2_roasted_proto_init() {
	if File_api_items_story_v2_roasted_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_items_story_v2_roasted_proto_rawDesc), len(file_api_items_story_v2_roasted_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_items_story_v2_roasted_proto_goTypes,
		DependencyIndexes: file_api_items_story_v2_roasted_proto_depIdxs,
		MessageInfos:      file_api_items_story_v2_roasted_proto_msgTypes,
	}.Build()
	File_api_items_story_v2_roasted_proto = out.File
	file_api_items_story_v2_roasted_proto_goTypes = nil
	file_api_items_story_v2_roasted_proto_depIdxs = nil
}
