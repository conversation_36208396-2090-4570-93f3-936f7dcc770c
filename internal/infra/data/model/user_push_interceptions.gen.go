// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserPushInterception = "user_push_interceptions"

// UserPushInterception push 拦截表
type UserPushInterception struct {
	ID        int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	UserID    int64     `gorm:"column:user_id;type:bigint unsigned;not null;index:idx_user_id_push_type_push_value,priority:1" json:"user_id"`
	PushType  string    `gorm:"column:push_type;type:varchar(255);not null;index:idx_user_id_push_type_push_value,priority:2" json:"push_type"`
	PushValue string    `gorm:"column:push_value;type:varchar(255);not null;index:idx_user_id_push_type_push_value,priority:3" json:"push_value"`
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName UserPushInterception's table name
func (*UserPushInterception) TableName() string {
	return TableNameUserPushInterception
}
