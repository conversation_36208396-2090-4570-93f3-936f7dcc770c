// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStoryUserReaction = "story_user_reactions"

// StoryUserReaction story user reactions
type StoryUserReaction struct {
	ID            int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	StoryID       int64     `gorm:"column:story_id;type:bigint unsigned;not null;uniqueIndex:idx_story_user_reaction,priority:1" json:"story_id"`
	StoryAuthorID int64     `gorm:"column:story_author_id;type:bigint unsigned;not null;index:idx_story_author_id_created_at,priority:1" json:"story_author_id"`
	UserID        int64     `gorm:"column:user_id;type:bigint unsigned;not null;uniqueIndex:idx_story_user_reaction,priority:2;index:idx_user_id,priority:1" json:"user_id"`
	Emoji         string    `gorm:"column:emoji;type:varchar(255);not null;uniqueIndex:idx_story_user_reaction,priority:3" json:"emoji"`
	Comment       string    `gorm:"column:comment;type:text;not null" json:"comment"`
	CreatedAt     time.Time `gorm:"column:created_at;type:timestamp;not null;index:idx_story_author_id_created_at,priority:2;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName StoryUserReaction's table name
func (*StoryUserReaction) TableName() string {
	return TableNameStoryUserReaction
}
