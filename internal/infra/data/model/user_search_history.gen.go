// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserSearchHistory = "user_search_history"

// UserSearchHistory 用户搜索历史表
type UserSearchHistory struct {
	ID         int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	UserID     int64     `gorm:"column:user_id;type:bigint unsigned;not null;index:idx_user_id,priority:1" json:"user_id"`
	SearchText string    `gorm:"column:search_text;type:varchar(255);not null" json:"search_text"`
	SearchType string    `gorm:"column:search_type;type:varchar(255);not null" json:"search_type"`
	CreatedAt  time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName UserSearchHistory's table name
func (*UserSearchHistory) TableName() string {
	return TableNameUserSearchHistory
}
