// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStoriesManagement = "stories_management"

// StoriesManagement mapped from table <stories_management>
type StoriesManagement struct {
	ID           int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	StoryID      int64     `gorm:"column:story_id;type:bigint unsigned;not null;uniqueIndex:uniq_idx_story_id,priority:1" json:"story_id"`
	FeedStatus   string    `gorm:"column:feed_status;type:varchar(255);not null;default:FEED_STATUS_NORMAL" json:"feed_status"`
	FeedBoost    float32   `gorm:"column:feed_boost;type:float;not null;default:1" json:"feed_boost"`
	StoryQuality string    `gorm:"column:story_quality;type:varchar(255);not null;default:STORY_QUALITY_NORMAL" json:"story_quality"`
	PushStatus   string    `gorm:"column:push_status;type:varchar(255);not null" json:"push_status"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName StoriesManagement's table name
func (*StoriesManagement) TableName() string {
	return TableNameStoriesManagement
}
