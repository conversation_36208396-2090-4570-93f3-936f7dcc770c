// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameHideSticker = "hide_stickers"

// HideSticker mapped from table <hide_stickers>
type HideSticker struct {
	ID           int64     `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CreatorID    int64     `gorm:"column:creator_id;type:bigint;not null;index:idx_creator_id,priority:1" json:"creator_id"`
	FromStoryID  int64     `gorm:"column:from_story_id;type:bigint;not null;index:idx_from_story_id,priority:1" json:"from_story_id"`
	FromAvatarID int64     `gorm:"column:from_avatar_id;type:bigint;not null;uniqueIndex:idx_unique_from_avatar_id,priority:1;index:idx_from_avatar_id,priority:1" json:"from_avatar_id"`
	ExtraInfo    string    `gorm:"column:extra_info;type:text;not null" json:"extra_info"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName HideSticker's table name
func (*HideSticker) TableName() string {
	return TableNameHideSticker
}
