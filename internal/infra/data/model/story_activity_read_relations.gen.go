// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameStoryActivityReadRelation = "story_activity_read_relations"

// StoryActivityReadRelation mapped from table <story_activity_read_relations>
type StoryActivityReadRelation struct {
	UserID            int64      `gorm:"column:user_id;type:bigint;primaryKey" json:"user_id"`
	LastReadLikeID    *int64     `gorm:"column:last_read_like_id;type:bigint" json:"last_read_like_id"`
	LastReadCommentID *int64     `gorm:"column:last_read_comment_id;type:bigint" json:"last_read_comment_id"`
	UpdatedAt         *time.Time `gorm:"column:updated_at;type:timestamp;index:idx_updated_at,priority:1;default:CURRENT_TIMESTAMP" json:"updated_at"`
	CreatedAt         *time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
}

// TableName StoryActivityReadRelation's table name
func (*StoryActivityReadRelation) TableName() string {
	return TableNameStoryActivityReadRelation
}
