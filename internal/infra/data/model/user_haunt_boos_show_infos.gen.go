// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameUserHauntBoosShowInfo = "user_haunt_boos_show_infos"

// UserHauntBoosShowInfo mapped from table <user_haunt_boos_show_infos>
type UserHauntBoosShowInfo struct {
	ID        int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	UserID    int64     `gorm:"column:user_id;type:bigint unsigned;not null;uniqueIndex:uniq_idx_user_id_scene,priority:1" json:"user_id"`
	Scene     string    `gorm:"column:scene;type:varchar(255);not null;uniqueIndex:uniq_idx_user_id_scene,priority:2;default:SHOW_INFO_SENCE_FEED_POLLUTION;comment:场景类型" json:"scene"` // 场景类型
	ShowInfo  string    `gorm:"column:show_info;type:longtext;not null;comment:json 格式存储" json:"show_info"`                                                                             // json 格式存储
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName UserHauntBoosShowInfo's table name
func (*UserHauntBoosShowInfo) TableName() string {
	return TableNameUserHauntBoosShowInfo
}
