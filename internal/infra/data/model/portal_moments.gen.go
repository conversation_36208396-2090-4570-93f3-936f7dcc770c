// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePortalMoment = "portal_moments"

// PortalMoment mapped from table <portal_moments>
type PortalMoment struct {
	ID         int64            `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	PortalID   int64            `gorm:"column:portal_id;type:bigint unsigned;not null;index:idx_portal_id,priority:1" json:"portal_id"`
	CreatorID  int64            `gorm:"column:creator_id;type:bigint unsigned;not null;index:idx_creator_id,priority:1" json:"creator_id"`
	Status     string           `gorm:"column:status;type:varchar(255);not null;default:STATUS_PUBLISHED" json:"status"`
	Type       string           `gorm:"column:type;type:varchar(255);not null;default:MOMENT_TYPE_NORMAL" json:"type"`
	CreateType string           `gorm:"column:create_type;type:varchar(255);not null;default:MOMENT_CREATE_TYPE_APPEND" json:"create_type"`
	ExtraInfo  string           `gorm:"column:extra_info;type:longtext;not null" json:"extra_info"`
	CreatedAt  time.Time        `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  time.Time        `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	Stat       PortalMomentStat `gorm:"foreignKey:moment_id;references:id" json:"stat"`
}

// TableName PortalMoment's table name
func (*PortalMoment) TableName() string {
	return TableNamePortalMoment
}
