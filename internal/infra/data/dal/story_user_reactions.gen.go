// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newStoryUserReaction(db *gorm.DB, opts ...gen.DOOption) storyUserReaction {
	_storyUserReaction := storyUserReaction{}

	_storyUserReaction.storyUserReactionDo.UseDB(db, opts...)
	_storyUserReaction.storyUserReactionDo.UseModel(&model.StoryUserReaction{})

	tableName := _storyUserReaction.storyUserReactionDo.TableName()
	_storyUserReaction.ALL = field.NewAsterisk(tableName)
	_storyUserReaction.ID = field.NewInt64(tableName, "id")
	_storyUserReaction.StoryID = field.NewInt64(tableName, "story_id")
	_storyUserReaction.StoryAuthorID = field.NewInt64(tableName, "story_author_id")
	_storyUserReaction.UserID = field.NewInt64(tableName, "user_id")
	_storyUserReaction.Emoji = field.NewString(tableName, "emoji")
	_storyUserReaction.Comment = field.NewString(tableName, "comment")
	_storyUserReaction.CreatedAt = field.NewTime(tableName, "created_at")
	_storyUserReaction.UpdatedAt = field.NewTime(tableName, "updated_at")

	_storyUserReaction.fillFieldMap()

	return _storyUserReaction
}

// storyUserReaction story user reactions
type storyUserReaction struct {
	storyUserReactionDo storyUserReactionDo

	ALL           field.Asterisk
	ID            field.Int64
	StoryID       field.Int64
	StoryAuthorID field.Int64
	UserID        field.Int64
	Emoji         field.String
	Comment       field.String
	CreatedAt     field.Time
	UpdatedAt     field.Time

	fieldMap map[string]field.Expr
}

func (s storyUserReaction) Table(newTableName string) *storyUserReaction {
	s.storyUserReactionDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyUserReaction) As(alias string) *storyUserReaction {
	s.storyUserReactionDo.DO = *(s.storyUserReactionDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyUserReaction) updateTableName(table string) *storyUserReaction {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.StoryID = field.NewInt64(table, "story_id")
	s.StoryAuthorID = field.NewInt64(table, "story_author_id")
	s.UserID = field.NewInt64(table, "user_id")
	s.Emoji = field.NewString(table, "emoji")
	s.Comment = field.NewString(table, "comment")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *storyUserReaction) WithContext(ctx context.Context) *storyUserReactionDo {
	return s.storyUserReactionDo.WithContext(ctx)
}

func (s storyUserReaction) TableName() string { return s.storyUserReactionDo.TableName() }

func (s storyUserReaction) Alias() string { return s.storyUserReactionDo.Alias() }

func (s storyUserReaction) Columns(cols ...field.Expr) gen.Columns {
	return s.storyUserReactionDo.Columns(cols...)
}

func (s *storyUserReaction) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyUserReaction) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["story_id"] = s.StoryID
	s.fieldMap["story_author_id"] = s.StoryAuthorID
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["emoji"] = s.Emoji
	s.fieldMap["comment"] = s.Comment
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s storyUserReaction) clone(db *gorm.DB) storyUserReaction {
	s.storyUserReactionDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyUserReaction) replaceDB(db *gorm.DB) storyUserReaction {
	s.storyUserReactionDo.ReplaceDB(db)
	return s
}

type storyUserReactionDo struct{ gen.DO }

func (s storyUserReactionDo) Debug() *storyUserReactionDo {
	return s.withDO(s.DO.Debug())
}

func (s storyUserReactionDo) WithContext(ctx context.Context) *storyUserReactionDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyUserReactionDo) ReadDB() *storyUserReactionDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyUserReactionDo) WriteDB() *storyUserReactionDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyUserReactionDo) Session(config *gorm.Session) *storyUserReactionDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyUserReactionDo) Clauses(conds ...clause.Expression) *storyUserReactionDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyUserReactionDo) Returning(value interface{}, columns ...string) *storyUserReactionDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyUserReactionDo) Not(conds ...gen.Condition) *storyUserReactionDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyUserReactionDo) Or(conds ...gen.Condition) *storyUserReactionDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyUserReactionDo) Select(conds ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyUserReactionDo) Where(conds ...gen.Condition) *storyUserReactionDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyUserReactionDo) Order(conds ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyUserReactionDo) Distinct(cols ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyUserReactionDo) Omit(cols ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyUserReactionDo) Join(table schema.Tabler, on ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyUserReactionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyUserReactionDo) RightJoin(table schema.Tabler, on ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyUserReactionDo) Group(cols ...field.Expr) *storyUserReactionDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyUserReactionDo) Having(conds ...gen.Condition) *storyUserReactionDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyUserReactionDo) Limit(limit int) *storyUserReactionDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyUserReactionDo) Offset(offset int) *storyUserReactionDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyUserReactionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *storyUserReactionDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyUserReactionDo) Unscoped() *storyUserReactionDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyUserReactionDo) Create(values ...*model.StoryUserReaction) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyUserReactionDo) CreateInBatches(values []*model.StoryUserReaction, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyUserReactionDo) Save(values ...*model.StoryUserReaction) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyUserReactionDo) First() (*model.StoryUserReaction, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryUserReaction), nil
	}
}

func (s storyUserReactionDo) Take() (*model.StoryUserReaction, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryUserReaction), nil
	}
}

func (s storyUserReactionDo) Last() (*model.StoryUserReaction, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryUserReaction), nil
	}
}

func (s storyUserReactionDo) Find() ([]*model.StoryUserReaction, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryUserReaction), err
}

func (s storyUserReactionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryUserReaction, err error) {
	buf := make([]*model.StoryUserReaction, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyUserReactionDo) FindInBatches(result *[]*model.StoryUserReaction, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyUserReactionDo) Attrs(attrs ...field.AssignExpr) *storyUserReactionDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyUserReactionDo) Assign(attrs ...field.AssignExpr) *storyUserReactionDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyUserReactionDo) Joins(fields ...field.RelationField) *storyUserReactionDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyUserReactionDo) Preload(fields ...field.RelationField) *storyUserReactionDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyUserReactionDo) FirstOrInit() (*model.StoryUserReaction, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryUserReaction), nil
	}
}

func (s storyUserReactionDo) FirstOrCreate() (*model.StoryUserReaction, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryUserReaction), nil
	}
}

func (s storyUserReactionDo) FindByPage(offset int, limit int) (result []*model.StoryUserReaction, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyUserReactionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyUserReactionDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyUserReactionDo) Delete(models ...*model.StoryUserReaction) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyUserReactionDo) withDO(do gen.Dao) *storyUserReactionDo {
	s.DO = *do.(*gen.DO)
	return s
}
