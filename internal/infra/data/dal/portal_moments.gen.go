// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newPortalMoment(db *gorm.DB, opts ...gen.DOOption) portalMoment {
	_portalMoment := portalMoment{}

	_portalMoment.portalMomentDo.UseDB(db, opts...)
	_portalMoment.portalMomentDo.UseModel(&model.PortalMoment{})

	tableName := _portalMoment.portalMomentDo.TableName()
	_portalMoment.ALL = field.NewAsterisk(tableName)
	_portalMoment.ID = field.NewInt64(tableName, "id")
	_portalMoment.PortalID = field.NewInt64(tableName, "portal_id")
	_portalMoment.CreatorID = field.NewInt64(tableName, "creator_id")
	_portalMoment.Status = field.NewString(tableName, "status")
	_portalMoment.Type = field.NewString(tableName, "type")
	_portalMoment.CreateType = field.NewString(tableName, "create_type")
	_portalMoment.ExtraInfo = field.NewString(tableName, "extra_info")
	_portalMoment.CreatedAt = field.NewTime(tableName, "created_at")
	_portalMoment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_portalMoment.Stat = portalMomentHasOneStat{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Stat", "model.PortalMomentStat"),
	}

	_portalMoment.fillFieldMap()

	return _portalMoment
}

type portalMoment struct {
	portalMomentDo portalMomentDo

	ALL        field.Asterisk
	ID         field.Int64
	PortalID   field.Int64
	CreatorID  field.Int64
	Status     field.String
	Type       field.String
	CreateType field.String
	ExtraInfo  field.String
	CreatedAt  field.Time
	UpdatedAt  field.Time
	Stat       portalMomentHasOneStat

	fieldMap map[string]field.Expr
}

func (p portalMoment) Table(newTableName string) *portalMoment {
	p.portalMomentDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p portalMoment) As(alias string) *portalMoment {
	p.portalMomentDo.DO = *(p.portalMomentDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *portalMoment) updateTableName(table string) *portalMoment {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.PortalID = field.NewInt64(table, "portal_id")
	p.CreatorID = field.NewInt64(table, "creator_id")
	p.Status = field.NewString(table, "status")
	p.Type = field.NewString(table, "type")
	p.CreateType = field.NewString(table, "create_type")
	p.ExtraInfo = field.NewString(table, "extra_info")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")

	p.fillFieldMap()

	return p
}

func (p *portalMoment) WithContext(ctx context.Context) *portalMomentDo {
	return p.portalMomentDo.WithContext(ctx)
}

func (p portalMoment) TableName() string { return p.portalMomentDo.TableName() }

func (p portalMoment) Alias() string { return p.portalMomentDo.Alias() }

func (p portalMoment) Columns(cols ...field.Expr) gen.Columns {
	return p.portalMomentDo.Columns(cols...)
}

func (p *portalMoment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *portalMoment) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 10)
	p.fieldMap["id"] = p.ID
	p.fieldMap["portal_id"] = p.PortalID
	p.fieldMap["creator_id"] = p.CreatorID
	p.fieldMap["status"] = p.Status
	p.fieldMap["type"] = p.Type
	p.fieldMap["create_type"] = p.CreateType
	p.fieldMap["extra_info"] = p.ExtraInfo
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt

}

func (p portalMoment) clone(db *gorm.DB) portalMoment {
	p.portalMomentDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p portalMoment) replaceDB(db *gorm.DB) portalMoment {
	p.portalMomentDo.ReplaceDB(db)
	return p
}

type portalMomentHasOneStat struct {
	db *gorm.DB

	field.RelationField
}

func (a portalMomentHasOneStat) Where(conds ...field.Expr) *portalMomentHasOneStat {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a portalMomentHasOneStat) WithContext(ctx context.Context) *portalMomentHasOneStat {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a portalMomentHasOneStat) Session(session *gorm.Session) *portalMomentHasOneStat {
	a.db = a.db.Session(session)
	return &a
}

func (a portalMomentHasOneStat) Model(m *model.PortalMoment) *portalMomentHasOneStatTx {
	return &portalMomentHasOneStatTx{a.db.Model(m).Association(a.Name())}
}

type portalMomentHasOneStatTx struct{ tx *gorm.Association }

func (a portalMomentHasOneStatTx) Find() (result *model.PortalMomentStat, err error) {
	return result, a.tx.Find(&result)
}

func (a portalMomentHasOneStatTx) Append(values ...*model.PortalMomentStat) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a portalMomentHasOneStatTx) Replace(values ...*model.PortalMomentStat) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a portalMomentHasOneStatTx) Delete(values ...*model.PortalMomentStat) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a portalMomentHasOneStatTx) Clear() error {
	return a.tx.Clear()
}

func (a portalMomentHasOneStatTx) Count() int64 {
	return a.tx.Count()
}

type portalMomentDo struct{ gen.DO }

func (p portalMomentDo) Debug() *portalMomentDo {
	return p.withDO(p.DO.Debug())
}

func (p portalMomentDo) WithContext(ctx context.Context) *portalMomentDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p portalMomentDo) ReadDB() *portalMomentDo {
	return p.Clauses(dbresolver.Read)
}

func (p portalMomentDo) WriteDB() *portalMomentDo {
	return p.Clauses(dbresolver.Write)
}

func (p portalMomentDo) Session(config *gorm.Session) *portalMomentDo {
	return p.withDO(p.DO.Session(config))
}

func (p portalMomentDo) Clauses(conds ...clause.Expression) *portalMomentDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p portalMomentDo) Returning(value interface{}, columns ...string) *portalMomentDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p portalMomentDo) Not(conds ...gen.Condition) *portalMomentDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p portalMomentDo) Or(conds ...gen.Condition) *portalMomentDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p portalMomentDo) Select(conds ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p portalMomentDo) Where(conds ...gen.Condition) *portalMomentDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p portalMomentDo) Order(conds ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p portalMomentDo) Distinct(cols ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p portalMomentDo) Omit(cols ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p portalMomentDo) Join(table schema.Tabler, on ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p portalMomentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p portalMomentDo) RightJoin(table schema.Tabler, on ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p portalMomentDo) Group(cols ...field.Expr) *portalMomentDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p portalMomentDo) Having(conds ...gen.Condition) *portalMomentDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p portalMomentDo) Limit(limit int) *portalMomentDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p portalMomentDo) Offset(offset int) *portalMomentDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p portalMomentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *portalMomentDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p portalMomentDo) Unscoped() *portalMomentDo {
	return p.withDO(p.DO.Unscoped())
}

func (p portalMomentDo) Create(values ...*model.PortalMoment) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p portalMomentDo) CreateInBatches(values []*model.PortalMoment, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p portalMomentDo) Save(values ...*model.PortalMoment) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p portalMomentDo) First() (*model.PortalMoment, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PortalMoment), nil
	}
}

func (p portalMomentDo) Take() (*model.PortalMoment, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PortalMoment), nil
	}
}

func (p portalMomentDo) Last() (*model.PortalMoment, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PortalMoment), nil
	}
}

func (p portalMomentDo) Find() ([]*model.PortalMoment, error) {
	result, err := p.DO.Find()
	return result.([]*model.PortalMoment), err
}

func (p portalMomentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PortalMoment, err error) {
	buf := make([]*model.PortalMoment, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p portalMomentDo) FindInBatches(result *[]*model.PortalMoment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p portalMomentDo) Attrs(attrs ...field.AssignExpr) *portalMomentDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p portalMomentDo) Assign(attrs ...field.AssignExpr) *portalMomentDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p portalMomentDo) Joins(fields ...field.RelationField) *portalMomentDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p portalMomentDo) Preload(fields ...field.RelationField) *portalMomentDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p portalMomentDo) FirstOrInit() (*model.PortalMoment, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PortalMoment), nil
	}
}

func (p portalMomentDo) FirstOrCreate() (*model.PortalMoment, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PortalMoment), nil
	}
}

func (p portalMomentDo) FindByPage(offset int, limit int) (result []*model.PortalMoment, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p portalMomentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p portalMomentDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p portalMomentDo) Delete(models ...*model.PortalMoment) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *portalMomentDo) withDO(do gen.Dao) *portalMomentDo {
	p.DO = *do.(*gen.DO)
	return p
}
