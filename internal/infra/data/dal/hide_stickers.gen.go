// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newHideSticker(db *gorm.DB, opts ...gen.DOOption) hideSticker {
	_hideSticker := hideSticker{}

	_hideSticker.hideStickerDo.UseDB(db, opts...)
	_hideSticker.hideStickerDo.UseModel(&model.HideSticker{})

	tableName := _hideSticker.hideStickerDo.TableName()
	_hideSticker.ALL = field.NewAsterisk(tableName)
	_hideSticker.ID = field.NewInt64(tableName, "id")
	_hideSticker.CreatorID = field.NewInt64(tableName, "creator_id")
	_hideSticker.FromStoryID = field.NewInt64(tableName, "from_story_id")
	_hideSticker.FromAvatarID = field.NewInt64(tableName, "from_avatar_id")
	_hideSticker.ExtraInfo = field.NewString(tableName, "extra_info")
	_hideSticker.CreatedAt = field.NewTime(tableName, "created_at")
	_hideSticker.UpdatedAt = field.NewTime(tableName, "updated_at")

	_hideSticker.fillFieldMap()

	return _hideSticker
}

type hideSticker struct {
	hideStickerDo hideStickerDo

	ALL          field.Asterisk
	ID           field.Int64
	CreatorID    field.Int64
	FromStoryID  field.Int64
	FromAvatarID field.Int64
	ExtraInfo    field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time

	fieldMap map[string]field.Expr
}

func (h hideSticker) Table(newTableName string) *hideSticker {
	h.hideStickerDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h hideSticker) As(alias string) *hideSticker {
	h.hideStickerDo.DO = *(h.hideStickerDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *hideSticker) updateTableName(table string) *hideSticker {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewInt64(table, "id")
	h.CreatorID = field.NewInt64(table, "creator_id")
	h.FromStoryID = field.NewInt64(table, "from_story_id")
	h.FromAvatarID = field.NewInt64(table, "from_avatar_id")
	h.ExtraInfo = field.NewString(table, "extra_info")
	h.CreatedAt = field.NewTime(table, "created_at")
	h.UpdatedAt = field.NewTime(table, "updated_at")

	h.fillFieldMap()

	return h
}

func (h *hideSticker) WithContext(ctx context.Context) *hideStickerDo {
	return h.hideStickerDo.WithContext(ctx)
}

func (h hideSticker) TableName() string { return h.hideStickerDo.TableName() }

func (h hideSticker) Alias() string { return h.hideStickerDo.Alias() }

func (h hideSticker) Columns(cols ...field.Expr) gen.Columns { return h.hideStickerDo.Columns(cols...) }

func (h *hideSticker) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *hideSticker) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 7)
	h.fieldMap["id"] = h.ID
	h.fieldMap["creator_id"] = h.CreatorID
	h.fieldMap["from_story_id"] = h.FromStoryID
	h.fieldMap["from_avatar_id"] = h.FromAvatarID
	h.fieldMap["extra_info"] = h.ExtraInfo
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
}

func (h hideSticker) clone(db *gorm.DB) hideSticker {
	h.hideStickerDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h hideSticker) replaceDB(db *gorm.DB) hideSticker {
	h.hideStickerDo.ReplaceDB(db)
	return h
}

type hideStickerDo struct{ gen.DO }

func (h hideStickerDo) Debug() *hideStickerDo {
	return h.withDO(h.DO.Debug())
}

func (h hideStickerDo) WithContext(ctx context.Context) *hideStickerDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h hideStickerDo) ReadDB() *hideStickerDo {
	return h.Clauses(dbresolver.Read)
}

func (h hideStickerDo) WriteDB() *hideStickerDo {
	return h.Clauses(dbresolver.Write)
}

func (h hideStickerDo) Session(config *gorm.Session) *hideStickerDo {
	return h.withDO(h.DO.Session(config))
}

func (h hideStickerDo) Clauses(conds ...clause.Expression) *hideStickerDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h hideStickerDo) Returning(value interface{}, columns ...string) *hideStickerDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h hideStickerDo) Not(conds ...gen.Condition) *hideStickerDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h hideStickerDo) Or(conds ...gen.Condition) *hideStickerDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h hideStickerDo) Select(conds ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h hideStickerDo) Where(conds ...gen.Condition) *hideStickerDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h hideStickerDo) Order(conds ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h hideStickerDo) Distinct(cols ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h hideStickerDo) Omit(cols ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h hideStickerDo) Join(table schema.Tabler, on ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h hideStickerDo) LeftJoin(table schema.Tabler, on ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h hideStickerDo) RightJoin(table schema.Tabler, on ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h hideStickerDo) Group(cols ...field.Expr) *hideStickerDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h hideStickerDo) Having(conds ...gen.Condition) *hideStickerDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h hideStickerDo) Limit(limit int) *hideStickerDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h hideStickerDo) Offset(offset int) *hideStickerDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h hideStickerDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *hideStickerDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h hideStickerDo) Unscoped() *hideStickerDo {
	return h.withDO(h.DO.Unscoped())
}

func (h hideStickerDo) Create(values ...*model.HideSticker) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h hideStickerDo) CreateInBatches(values []*model.HideSticker, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h hideStickerDo) Save(values ...*model.HideSticker) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h hideStickerDo) First() (*model.HideSticker, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.HideSticker), nil
	}
}

func (h hideStickerDo) Take() (*model.HideSticker, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.HideSticker), nil
	}
}

func (h hideStickerDo) Last() (*model.HideSticker, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.HideSticker), nil
	}
}

func (h hideStickerDo) Find() ([]*model.HideSticker, error) {
	result, err := h.DO.Find()
	return result.([]*model.HideSticker), err
}

func (h hideStickerDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.HideSticker, err error) {
	buf := make([]*model.HideSticker, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h hideStickerDo) FindInBatches(result *[]*model.HideSticker, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h hideStickerDo) Attrs(attrs ...field.AssignExpr) *hideStickerDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h hideStickerDo) Assign(attrs ...field.AssignExpr) *hideStickerDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h hideStickerDo) Joins(fields ...field.RelationField) *hideStickerDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h hideStickerDo) Preload(fields ...field.RelationField) *hideStickerDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h hideStickerDo) FirstOrInit() (*model.HideSticker, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.HideSticker), nil
	}
}

func (h hideStickerDo) FirstOrCreate() (*model.HideSticker, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.HideSticker), nil
	}
}

func (h hideStickerDo) FindByPage(offset int, limit int) (result []*model.HideSticker, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h hideStickerDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h hideStickerDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h hideStickerDo) Delete(models ...*model.HideSticker) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *hideStickerDo) withDO(do gen.Dao) *hideStickerDo {
	h.DO = *do.(*gen.DO)
	return h
}
