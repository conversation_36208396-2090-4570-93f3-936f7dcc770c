// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newUserHauntBoosShowInfo(db *gorm.DB, opts ...gen.DOOption) userHauntBoosShowInfo {
	_userHauntBoosShowInfo := userHauntBoosShowInfo{}

	_userHauntBoosShowInfo.userHauntBoosShowInfoDo.UseDB(db, opts...)
	_userHauntBoosShowInfo.userHauntBoosShowInfoDo.UseModel(&model.UserHauntBoosShowInfo{})

	tableName := _userHauntBoosShowInfo.userHauntBoosShowInfoDo.TableName()
	_userHauntBoosShowInfo.ALL = field.NewAsterisk(tableName)
	_userHauntBoosShowInfo.ID = field.NewInt64(tableName, "id")
	_userHauntBoosShowInfo.UserID = field.NewInt64(tableName, "user_id")
	_userHauntBoosShowInfo.Scene = field.NewString(tableName, "scene")
	_userHauntBoosShowInfo.ShowInfo = field.NewString(tableName, "show_info")
	_userHauntBoosShowInfo.CreatedAt = field.NewTime(tableName, "created_at")
	_userHauntBoosShowInfo.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userHauntBoosShowInfo.fillFieldMap()

	return _userHauntBoosShowInfo
}

type userHauntBoosShowInfo struct {
	userHauntBoosShowInfoDo userHauntBoosShowInfoDo

	ALL       field.Asterisk
	ID        field.Int64
	UserID    field.Int64
	Scene     field.String // 场景类型
	ShowInfo  field.String // json 格式存储
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (u userHauntBoosShowInfo) Table(newTableName string) *userHauntBoosShowInfo {
	u.userHauntBoosShowInfoDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userHauntBoosShowInfo) As(alias string) *userHauntBoosShowInfo {
	u.userHauntBoosShowInfoDo.DO = *(u.userHauntBoosShowInfoDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userHauntBoosShowInfo) updateTableName(table string) *userHauntBoosShowInfo {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewInt64(table, "user_id")
	u.Scene = field.NewString(table, "scene")
	u.ShowInfo = field.NewString(table, "show_info")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userHauntBoosShowInfo) WithContext(ctx context.Context) *userHauntBoosShowInfoDo {
	return u.userHauntBoosShowInfoDo.WithContext(ctx)
}

func (u userHauntBoosShowInfo) TableName() string { return u.userHauntBoosShowInfoDo.TableName() }

func (u userHauntBoosShowInfo) Alias() string { return u.userHauntBoosShowInfoDo.Alias() }

func (u userHauntBoosShowInfo) Columns(cols ...field.Expr) gen.Columns {
	return u.userHauntBoosShowInfoDo.Columns(cols...)
}

func (u *userHauntBoosShowInfo) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userHauntBoosShowInfo) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 6)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["scene"] = u.Scene
	u.fieldMap["show_info"] = u.ShowInfo
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userHauntBoosShowInfo) clone(db *gorm.DB) userHauntBoosShowInfo {
	u.userHauntBoosShowInfoDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userHauntBoosShowInfo) replaceDB(db *gorm.DB) userHauntBoosShowInfo {
	u.userHauntBoosShowInfoDo.ReplaceDB(db)
	return u
}

type userHauntBoosShowInfoDo struct{ gen.DO }

func (u userHauntBoosShowInfoDo) Debug() *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Debug())
}

func (u userHauntBoosShowInfoDo) WithContext(ctx context.Context) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userHauntBoosShowInfoDo) ReadDB() *userHauntBoosShowInfoDo {
	return u.Clauses(dbresolver.Read)
}

func (u userHauntBoosShowInfoDo) WriteDB() *userHauntBoosShowInfoDo {
	return u.Clauses(dbresolver.Write)
}

func (u userHauntBoosShowInfoDo) Session(config *gorm.Session) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Session(config))
}

func (u userHauntBoosShowInfoDo) Clauses(conds ...clause.Expression) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userHauntBoosShowInfoDo) Returning(value interface{}, columns ...string) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userHauntBoosShowInfoDo) Not(conds ...gen.Condition) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userHauntBoosShowInfoDo) Or(conds ...gen.Condition) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userHauntBoosShowInfoDo) Select(conds ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userHauntBoosShowInfoDo) Where(conds ...gen.Condition) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userHauntBoosShowInfoDo) Order(conds ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userHauntBoosShowInfoDo) Distinct(cols ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userHauntBoosShowInfoDo) Omit(cols ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userHauntBoosShowInfoDo) Join(table schema.Tabler, on ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userHauntBoosShowInfoDo) LeftJoin(table schema.Tabler, on ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userHauntBoosShowInfoDo) RightJoin(table schema.Tabler, on ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userHauntBoosShowInfoDo) Group(cols ...field.Expr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userHauntBoosShowInfoDo) Having(conds ...gen.Condition) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userHauntBoosShowInfoDo) Limit(limit int) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userHauntBoosShowInfoDo) Offset(offset int) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userHauntBoosShowInfoDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userHauntBoosShowInfoDo) Unscoped() *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userHauntBoosShowInfoDo) Create(values ...*model.UserHauntBoosShowInfo) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userHauntBoosShowInfoDo) CreateInBatches(values []*model.UserHauntBoosShowInfo, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userHauntBoosShowInfoDo) Save(values ...*model.UserHauntBoosShowInfo) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userHauntBoosShowInfoDo) First() (*model.UserHauntBoosShowInfo, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserHauntBoosShowInfo), nil
	}
}

func (u userHauntBoosShowInfoDo) Take() (*model.UserHauntBoosShowInfo, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserHauntBoosShowInfo), nil
	}
}

func (u userHauntBoosShowInfoDo) Last() (*model.UserHauntBoosShowInfo, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserHauntBoosShowInfo), nil
	}
}

func (u userHauntBoosShowInfoDo) Find() ([]*model.UserHauntBoosShowInfo, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserHauntBoosShowInfo), err
}

func (u userHauntBoosShowInfoDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserHauntBoosShowInfo, err error) {
	buf := make([]*model.UserHauntBoosShowInfo, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userHauntBoosShowInfoDo) FindInBatches(result *[]*model.UserHauntBoosShowInfo, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userHauntBoosShowInfoDo) Attrs(attrs ...field.AssignExpr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userHauntBoosShowInfoDo) Assign(attrs ...field.AssignExpr) *userHauntBoosShowInfoDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userHauntBoosShowInfoDo) Joins(fields ...field.RelationField) *userHauntBoosShowInfoDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userHauntBoosShowInfoDo) Preload(fields ...field.RelationField) *userHauntBoosShowInfoDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userHauntBoosShowInfoDo) FirstOrInit() (*model.UserHauntBoosShowInfo, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserHauntBoosShowInfo), nil
	}
}

func (u userHauntBoosShowInfoDo) FirstOrCreate() (*model.UserHauntBoosShowInfo, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserHauntBoosShowInfo), nil
	}
}

func (u userHauntBoosShowInfoDo) FindByPage(offset int, limit int) (result []*model.UserHauntBoosShowInfo, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userHauntBoosShowInfoDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userHauntBoosShowInfoDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userHauntBoosShowInfoDo) Delete(models ...*model.UserHauntBoosShowInfo) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userHauntBoosShowInfoDo) withDO(do gen.Dao) *userHauntBoosShowInfoDo {
	u.DO = *do.(*gen.DO)
	return u
}
