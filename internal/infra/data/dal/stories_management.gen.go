// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newStoriesManagement(db *gorm.DB, opts ...gen.DOOption) storiesManagement {
	_storiesManagement := storiesManagement{}

	_storiesManagement.storiesManagementDo.UseDB(db, opts...)
	_storiesManagement.storiesManagementDo.UseModel(&model.StoriesManagement{})

	tableName := _storiesManagement.storiesManagementDo.TableName()
	_storiesManagement.ALL = field.NewAsterisk(tableName)
	_storiesManagement.ID = field.NewInt64(tableName, "id")
	_storiesManagement.StoryID = field.NewInt64(tableName, "story_id")
	_storiesManagement.FeedStatus = field.NewString(tableName, "feed_status")
	_storiesManagement.FeedBoost = field.NewFloat32(tableName, "feed_boost")
	_storiesManagement.StoryQuality = field.NewString(tableName, "story_quality")
	_storiesManagement.PushStatus = field.NewString(tableName, "push_status")
	_storiesManagement.CreatedAt = field.NewTime(tableName, "created_at")
	_storiesManagement.UpdatedAt = field.NewTime(tableName, "updated_at")

	_storiesManagement.fillFieldMap()

	return _storiesManagement
}

type storiesManagement struct {
	storiesManagementDo storiesManagementDo

	ALL          field.Asterisk
	ID           field.Int64
	StoryID      field.Int64
	FeedStatus   field.String
	FeedBoost    field.Float32
	StoryQuality field.String
	PushStatus   field.String
	CreatedAt    field.Time
	UpdatedAt    field.Time

	fieldMap map[string]field.Expr
}

func (s storiesManagement) Table(newTableName string) *storiesManagement {
	s.storiesManagementDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storiesManagement) As(alias string) *storiesManagement {
	s.storiesManagementDo.DO = *(s.storiesManagementDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storiesManagement) updateTableName(table string) *storiesManagement {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.StoryID = field.NewInt64(table, "story_id")
	s.FeedStatus = field.NewString(table, "feed_status")
	s.FeedBoost = field.NewFloat32(table, "feed_boost")
	s.StoryQuality = field.NewString(table, "story_quality")
	s.PushStatus = field.NewString(table, "push_status")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *storiesManagement) WithContext(ctx context.Context) *storiesManagementDo {
	return s.storiesManagementDo.WithContext(ctx)
}

func (s storiesManagement) TableName() string { return s.storiesManagementDo.TableName() }

func (s storiesManagement) Alias() string { return s.storiesManagementDo.Alias() }

func (s storiesManagement) Columns(cols ...field.Expr) gen.Columns {
	return s.storiesManagementDo.Columns(cols...)
}

func (s *storiesManagement) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storiesManagement) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 8)
	s.fieldMap["id"] = s.ID
	s.fieldMap["story_id"] = s.StoryID
	s.fieldMap["feed_status"] = s.FeedStatus
	s.fieldMap["feed_boost"] = s.FeedBoost
	s.fieldMap["story_quality"] = s.StoryQuality
	s.fieldMap["push_status"] = s.PushStatus
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s storiesManagement) clone(db *gorm.DB) storiesManagement {
	s.storiesManagementDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storiesManagement) replaceDB(db *gorm.DB) storiesManagement {
	s.storiesManagementDo.ReplaceDB(db)
	return s
}

type storiesManagementDo struct{ gen.DO }

func (s storiesManagementDo) Debug() *storiesManagementDo {
	return s.withDO(s.DO.Debug())
}

func (s storiesManagementDo) WithContext(ctx context.Context) *storiesManagementDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storiesManagementDo) ReadDB() *storiesManagementDo {
	return s.Clauses(dbresolver.Read)
}

func (s storiesManagementDo) WriteDB() *storiesManagementDo {
	return s.Clauses(dbresolver.Write)
}

func (s storiesManagementDo) Session(config *gorm.Session) *storiesManagementDo {
	return s.withDO(s.DO.Session(config))
}

func (s storiesManagementDo) Clauses(conds ...clause.Expression) *storiesManagementDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storiesManagementDo) Returning(value interface{}, columns ...string) *storiesManagementDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storiesManagementDo) Not(conds ...gen.Condition) *storiesManagementDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storiesManagementDo) Or(conds ...gen.Condition) *storiesManagementDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storiesManagementDo) Select(conds ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storiesManagementDo) Where(conds ...gen.Condition) *storiesManagementDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storiesManagementDo) Order(conds ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storiesManagementDo) Distinct(cols ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storiesManagementDo) Omit(cols ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storiesManagementDo) Join(table schema.Tabler, on ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storiesManagementDo) LeftJoin(table schema.Tabler, on ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storiesManagementDo) RightJoin(table schema.Tabler, on ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storiesManagementDo) Group(cols ...field.Expr) *storiesManagementDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storiesManagementDo) Having(conds ...gen.Condition) *storiesManagementDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storiesManagementDo) Limit(limit int) *storiesManagementDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storiesManagementDo) Offset(offset int) *storiesManagementDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storiesManagementDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *storiesManagementDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storiesManagementDo) Unscoped() *storiesManagementDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storiesManagementDo) Create(values ...*model.StoriesManagement) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storiesManagementDo) CreateInBatches(values []*model.StoriesManagement, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storiesManagementDo) Save(values ...*model.StoriesManagement) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storiesManagementDo) First() (*model.StoriesManagement, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoriesManagement), nil
	}
}

func (s storiesManagementDo) Take() (*model.StoriesManagement, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoriesManagement), nil
	}
}

func (s storiesManagementDo) Last() (*model.StoriesManagement, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoriesManagement), nil
	}
}

func (s storiesManagementDo) Find() ([]*model.StoriesManagement, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoriesManagement), err
}

func (s storiesManagementDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoriesManagement, err error) {
	buf := make([]*model.StoriesManagement, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storiesManagementDo) FindInBatches(result *[]*model.StoriesManagement, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storiesManagementDo) Attrs(attrs ...field.AssignExpr) *storiesManagementDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storiesManagementDo) Assign(attrs ...field.AssignExpr) *storiesManagementDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storiesManagementDo) Joins(fields ...field.RelationField) *storiesManagementDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storiesManagementDo) Preload(fields ...field.RelationField) *storiesManagementDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storiesManagementDo) FirstOrInit() (*model.StoriesManagement, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoriesManagement), nil
	}
}

func (s storiesManagementDo) FirstOrCreate() (*model.StoriesManagement, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoriesManagement), nil
	}
}

func (s storiesManagementDo) FindByPage(offset int, limit int) (result []*model.StoriesManagement, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storiesManagementDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storiesManagementDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storiesManagementDo) Delete(models ...*model.StoriesManagement) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storiesManagementDo) withDO(do gen.Dao) *storiesManagementDo {
	s.DO = *do.(*gen.DO)
	return s
}
