// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newStoryActivityReadRelation(db *gorm.DB, opts ...gen.DOOption) storyActivityReadRelation {
	_storyActivityReadRelation := storyActivityReadRelation{}

	_storyActivityReadRelation.storyActivityReadRelationDo.UseDB(db, opts...)
	_storyActivityReadRelation.storyActivityReadRelationDo.UseModel(&model.StoryActivityReadRelation{})

	tableName := _storyActivityReadRelation.storyActivityReadRelationDo.TableName()
	_storyActivityReadRelation.ALL = field.NewAsterisk(tableName)
	_storyActivityReadRelation.UserID = field.NewInt64(tableName, "user_id")
	_storyActivityReadRelation.LastReadLikeID = field.NewInt64(tableName, "last_read_like_id")
	_storyActivityReadRelation.LastReadCommentID = field.NewInt64(tableName, "last_read_comment_id")
	_storyActivityReadRelation.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyActivityReadRelation.CreatedAt = field.NewTime(tableName, "created_at")

	_storyActivityReadRelation.fillFieldMap()

	return _storyActivityReadRelation
}

type storyActivityReadRelation struct {
	storyActivityReadRelationDo storyActivityReadRelationDo

	ALL               field.Asterisk
	UserID            field.Int64
	LastReadLikeID    field.Int64
	LastReadCommentID field.Int64
	UpdatedAt         field.Time
	CreatedAt         field.Time

	fieldMap map[string]field.Expr
}

func (s storyActivityReadRelation) Table(newTableName string) *storyActivityReadRelation {
	s.storyActivityReadRelationDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyActivityReadRelation) As(alias string) *storyActivityReadRelation {
	s.storyActivityReadRelationDo.DO = *(s.storyActivityReadRelationDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyActivityReadRelation) updateTableName(table string) *storyActivityReadRelation {
	s.ALL = field.NewAsterisk(table)
	s.UserID = field.NewInt64(table, "user_id")
	s.LastReadLikeID = field.NewInt64(table, "last_read_like_id")
	s.LastReadCommentID = field.NewInt64(table, "last_read_comment_id")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.CreatedAt = field.NewTime(table, "created_at")

	s.fillFieldMap()

	return s
}

func (s *storyActivityReadRelation) WithContext(ctx context.Context) *storyActivityReadRelationDo {
	return s.storyActivityReadRelationDo.WithContext(ctx)
}

func (s storyActivityReadRelation) TableName() string {
	return s.storyActivityReadRelationDo.TableName()
}

func (s storyActivityReadRelation) Alias() string { return s.storyActivityReadRelationDo.Alias() }

func (s storyActivityReadRelation) Columns(cols ...field.Expr) gen.Columns {
	return s.storyActivityReadRelationDo.Columns(cols...)
}

func (s *storyActivityReadRelation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyActivityReadRelation) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 5)
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["last_read_like_id"] = s.LastReadLikeID
	s.fieldMap["last_read_comment_id"] = s.LastReadCommentID
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["created_at"] = s.CreatedAt
}

func (s storyActivityReadRelation) clone(db *gorm.DB) storyActivityReadRelation {
	s.storyActivityReadRelationDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyActivityReadRelation) replaceDB(db *gorm.DB) storyActivityReadRelation {
	s.storyActivityReadRelationDo.ReplaceDB(db)
	return s
}

type storyActivityReadRelationDo struct{ gen.DO }

func (s storyActivityReadRelationDo) Debug() *storyActivityReadRelationDo {
	return s.withDO(s.DO.Debug())
}

func (s storyActivityReadRelationDo) WithContext(ctx context.Context) *storyActivityReadRelationDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyActivityReadRelationDo) ReadDB() *storyActivityReadRelationDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyActivityReadRelationDo) WriteDB() *storyActivityReadRelationDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyActivityReadRelationDo) Session(config *gorm.Session) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyActivityReadRelationDo) Clauses(conds ...clause.Expression) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyActivityReadRelationDo) Returning(value interface{}, columns ...string) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyActivityReadRelationDo) Not(conds ...gen.Condition) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyActivityReadRelationDo) Or(conds ...gen.Condition) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyActivityReadRelationDo) Select(conds ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyActivityReadRelationDo) Where(conds ...gen.Condition) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyActivityReadRelationDo) Order(conds ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyActivityReadRelationDo) Distinct(cols ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyActivityReadRelationDo) Omit(cols ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyActivityReadRelationDo) Join(table schema.Tabler, on ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyActivityReadRelationDo) LeftJoin(table schema.Tabler, on ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyActivityReadRelationDo) RightJoin(table schema.Tabler, on ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyActivityReadRelationDo) Group(cols ...field.Expr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyActivityReadRelationDo) Having(conds ...gen.Condition) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyActivityReadRelationDo) Limit(limit int) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyActivityReadRelationDo) Offset(offset int) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyActivityReadRelationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyActivityReadRelationDo) Unscoped() *storyActivityReadRelationDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyActivityReadRelationDo) Create(values ...*model.StoryActivityReadRelation) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyActivityReadRelationDo) CreateInBatches(values []*model.StoryActivityReadRelation, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyActivityReadRelationDo) Save(values ...*model.StoryActivityReadRelation) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyActivityReadRelationDo) First() (*model.StoryActivityReadRelation, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryActivityReadRelation), nil
	}
}

func (s storyActivityReadRelationDo) Take() (*model.StoryActivityReadRelation, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryActivityReadRelation), nil
	}
}

func (s storyActivityReadRelationDo) Last() (*model.StoryActivityReadRelation, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryActivityReadRelation), nil
	}
}

func (s storyActivityReadRelationDo) Find() ([]*model.StoryActivityReadRelation, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryActivityReadRelation), err
}

func (s storyActivityReadRelationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryActivityReadRelation, err error) {
	buf := make([]*model.StoryActivityReadRelation, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyActivityReadRelationDo) FindInBatches(result *[]*model.StoryActivityReadRelation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyActivityReadRelationDo) Attrs(attrs ...field.AssignExpr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyActivityReadRelationDo) Assign(attrs ...field.AssignExpr) *storyActivityReadRelationDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyActivityReadRelationDo) Joins(fields ...field.RelationField) *storyActivityReadRelationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyActivityReadRelationDo) Preload(fields ...field.RelationField) *storyActivityReadRelationDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyActivityReadRelationDo) FirstOrInit() (*model.StoryActivityReadRelation, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryActivityReadRelation), nil
	}
}

func (s storyActivityReadRelationDo) FirstOrCreate() (*model.StoryActivityReadRelation, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryActivityReadRelation), nil
	}
}

func (s storyActivityReadRelationDo) FindByPage(offset int, limit int) (result []*model.StoryActivityReadRelation, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyActivityReadRelationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyActivityReadRelationDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyActivityReadRelationDo) Delete(models ...*model.StoryActivityReadRelation) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyActivityReadRelationDo) withDO(do gen.Dao) *storyActivityReadRelationDo {
	s.DO = *do.(*gen.DO)
	return s
}
