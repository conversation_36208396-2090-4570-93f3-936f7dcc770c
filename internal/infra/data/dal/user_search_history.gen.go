// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newUserSearchHistory(db *gorm.DB, opts ...gen.DOOption) userSearchHistory {
	_userSearchHistory := userSearchHistory{}

	_userSearchHistory.userSearchHistoryDo.UseDB(db, opts...)
	_userSearchHistory.userSearchHistoryDo.UseModel(&model.UserSearchHistory{})

	tableName := _userSearchHistory.userSearchHistoryDo.TableName()
	_userSearchHistory.ALL = field.NewAsterisk(tableName)
	_userSearchHistory.ID = field.NewInt64(tableName, "id")
	_userSearchHistory.UserID = field.NewInt64(tableName, "user_id")
	_userSearchHistory.SearchText = field.NewString(tableName, "search_text")
	_userSearchHistory.SearchType = field.NewString(tableName, "search_type")
	_userSearchHistory.CreatedAt = field.NewTime(tableName, "created_at")
	_userSearchHistory.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userSearchHistory.fillFieldMap()

	return _userSearchHistory
}

// userSearchHistory 用户搜索历史表
type userSearchHistory struct {
	userSearchHistoryDo userSearchHistoryDo

	ALL        field.Asterisk
	ID         field.Int64
	UserID     field.Int64
	SearchText field.String
	SearchType field.String
	CreatedAt  field.Time
	UpdatedAt  field.Time

	fieldMap map[string]field.Expr
}

func (u userSearchHistory) Table(newTableName string) *userSearchHistory {
	u.userSearchHistoryDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userSearchHistory) As(alias string) *userSearchHistory {
	u.userSearchHistoryDo.DO = *(u.userSearchHistoryDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userSearchHistory) updateTableName(table string) *userSearchHistory {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewInt64(table, "user_id")
	u.SearchText = field.NewString(table, "search_text")
	u.SearchType = field.NewString(table, "search_type")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userSearchHistory) WithContext(ctx context.Context) *userSearchHistoryDo {
	return u.userSearchHistoryDo.WithContext(ctx)
}

func (u userSearchHistory) TableName() string { return u.userSearchHistoryDo.TableName() }

func (u userSearchHistory) Alias() string { return u.userSearchHistoryDo.Alias() }

func (u userSearchHistory) Columns(cols ...field.Expr) gen.Columns {
	return u.userSearchHistoryDo.Columns(cols...)
}

func (u *userSearchHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userSearchHistory) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 6)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["search_text"] = u.SearchText
	u.fieldMap["search_type"] = u.SearchType
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userSearchHistory) clone(db *gorm.DB) userSearchHistory {
	u.userSearchHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userSearchHistory) replaceDB(db *gorm.DB) userSearchHistory {
	u.userSearchHistoryDo.ReplaceDB(db)
	return u
}

type userSearchHistoryDo struct{ gen.DO }

func (u userSearchHistoryDo) Debug() *userSearchHistoryDo {
	return u.withDO(u.DO.Debug())
}

func (u userSearchHistoryDo) WithContext(ctx context.Context) *userSearchHistoryDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userSearchHistoryDo) ReadDB() *userSearchHistoryDo {
	return u.Clauses(dbresolver.Read)
}

func (u userSearchHistoryDo) WriteDB() *userSearchHistoryDo {
	return u.Clauses(dbresolver.Write)
}

func (u userSearchHistoryDo) Session(config *gorm.Session) *userSearchHistoryDo {
	return u.withDO(u.DO.Session(config))
}

func (u userSearchHistoryDo) Clauses(conds ...clause.Expression) *userSearchHistoryDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userSearchHistoryDo) Returning(value interface{}, columns ...string) *userSearchHistoryDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userSearchHistoryDo) Not(conds ...gen.Condition) *userSearchHistoryDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userSearchHistoryDo) Or(conds ...gen.Condition) *userSearchHistoryDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userSearchHistoryDo) Select(conds ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userSearchHistoryDo) Where(conds ...gen.Condition) *userSearchHistoryDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userSearchHistoryDo) Order(conds ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userSearchHistoryDo) Distinct(cols ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userSearchHistoryDo) Omit(cols ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userSearchHistoryDo) Join(table schema.Tabler, on ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userSearchHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userSearchHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userSearchHistoryDo) Group(cols ...field.Expr) *userSearchHistoryDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userSearchHistoryDo) Having(conds ...gen.Condition) *userSearchHistoryDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userSearchHistoryDo) Limit(limit int) *userSearchHistoryDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userSearchHistoryDo) Offset(offset int) *userSearchHistoryDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userSearchHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *userSearchHistoryDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userSearchHistoryDo) Unscoped() *userSearchHistoryDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userSearchHistoryDo) Create(values ...*model.UserSearchHistory) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userSearchHistoryDo) CreateInBatches(values []*model.UserSearchHistory, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userSearchHistoryDo) Save(values ...*model.UserSearchHistory) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userSearchHistoryDo) First() (*model.UserSearchHistory, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSearchHistory), nil
	}
}

func (u userSearchHistoryDo) Take() (*model.UserSearchHistory, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSearchHistory), nil
	}
}

func (u userSearchHistoryDo) Last() (*model.UserSearchHistory, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSearchHistory), nil
	}
}

func (u userSearchHistoryDo) Find() ([]*model.UserSearchHistory, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserSearchHistory), err
}

func (u userSearchHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserSearchHistory, err error) {
	buf := make([]*model.UserSearchHistory, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userSearchHistoryDo) FindInBatches(result *[]*model.UserSearchHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userSearchHistoryDo) Attrs(attrs ...field.AssignExpr) *userSearchHistoryDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userSearchHistoryDo) Assign(attrs ...field.AssignExpr) *userSearchHistoryDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userSearchHistoryDo) Joins(fields ...field.RelationField) *userSearchHistoryDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userSearchHistoryDo) Preload(fields ...field.RelationField) *userSearchHistoryDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userSearchHistoryDo) FirstOrInit() (*model.UserSearchHistory, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSearchHistory), nil
	}
}

func (u userSearchHistoryDo) FirstOrCreate() (*model.UserSearchHistory, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserSearchHistory), nil
	}
}

func (u userSearchHistoryDo) FindByPage(offset int, limit int) (result []*model.UserSearchHistory, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userSearchHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userSearchHistoryDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userSearchHistoryDo) Delete(models ...*model.UserSearchHistory) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userSearchHistoryDo) withDO(do gen.Dao) *userSearchHistoryDo {
	u.DO = *do.(*gen.DO)
	return u
}
