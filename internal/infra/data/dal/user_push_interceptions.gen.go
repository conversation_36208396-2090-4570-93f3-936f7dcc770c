// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"boson/internal/infra/data/model"
)

func newUserPushInterception(db *gorm.DB, opts ...gen.DOOption) userPushInterception {
	_userPushInterception := userPushInterception{}

	_userPushInterception.userPushInterceptionDo.UseDB(db, opts...)
	_userPushInterception.userPushInterceptionDo.UseModel(&model.UserPushInterception{})

	tableName := _userPushInterception.userPushInterceptionDo.TableName()
	_userPushInterception.ALL = field.NewAsterisk(tableName)
	_userPushInterception.ID = field.NewInt64(tableName, "id")
	_userPushInterception.UserID = field.NewInt64(tableName, "user_id")
	_userPushInterception.PushType = field.NewString(tableName, "push_type")
	_userPushInterception.PushValue = field.NewString(tableName, "push_value")
	_userPushInterception.CreatedAt = field.NewTime(tableName, "created_at")
	_userPushInterception.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userPushInterception.fillFieldMap()

	return _userPushInterception
}

// userPushInterception push 拦截表
type userPushInterception struct {
	userPushInterceptionDo userPushInterceptionDo

	ALL       field.Asterisk
	ID        field.Int64
	UserID    field.Int64
	PushType  field.String
	PushValue field.String
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (u userPushInterception) Table(newTableName string) *userPushInterception {
	u.userPushInterceptionDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userPushInterception) As(alias string) *userPushInterception {
	u.userPushInterceptionDo.DO = *(u.userPushInterceptionDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userPushInterception) updateTableName(table string) *userPushInterception {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewInt64(table, "id")
	u.UserID = field.NewInt64(table, "user_id")
	u.PushType = field.NewString(table, "push_type")
	u.PushValue = field.NewString(table, "push_value")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userPushInterception) WithContext(ctx context.Context) *userPushInterceptionDo {
	return u.userPushInterceptionDo.WithContext(ctx)
}

func (u userPushInterception) TableName() string { return u.userPushInterceptionDo.TableName() }

func (u userPushInterception) Alias() string { return u.userPushInterceptionDo.Alias() }

func (u userPushInterception) Columns(cols ...field.Expr) gen.Columns {
	return u.userPushInterceptionDo.Columns(cols...)
}

func (u *userPushInterception) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userPushInterception) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 6)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["push_type"] = u.PushType
	u.fieldMap["push_value"] = u.PushValue
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userPushInterception) clone(db *gorm.DB) userPushInterception {
	u.userPushInterceptionDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userPushInterception) replaceDB(db *gorm.DB) userPushInterception {
	u.userPushInterceptionDo.ReplaceDB(db)
	return u
}

type userPushInterceptionDo struct{ gen.DO }

func (u userPushInterceptionDo) Debug() *userPushInterceptionDo {
	return u.withDO(u.DO.Debug())
}

func (u userPushInterceptionDo) WithContext(ctx context.Context) *userPushInterceptionDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userPushInterceptionDo) ReadDB() *userPushInterceptionDo {
	return u.Clauses(dbresolver.Read)
}

func (u userPushInterceptionDo) WriteDB() *userPushInterceptionDo {
	return u.Clauses(dbresolver.Write)
}

func (u userPushInterceptionDo) Session(config *gorm.Session) *userPushInterceptionDo {
	return u.withDO(u.DO.Session(config))
}

func (u userPushInterceptionDo) Clauses(conds ...clause.Expression) *userPushInterceptionDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userPushInterceptionDo) Returning(value interface{}, columns ...string) *userPushInterceptionDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userPushInterceptionDo) Not(conds ...gen.Condition) *userPushInterceptionDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userPushInterceptionDo) Or(conds ...gen.Condition) *userPushInterceptionDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userPushInterceptionDo) Select(conds ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userPushInterceptionDo) Where(conds ...gen.Condition) *userPushInterceptionDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userPushInterceptionDo) Order(conds ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userPushInterceptionDo) Distinct(cols ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userPushInterceptionDo) Omit(cols ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userPushInterceptionDo) Join(table schema.Tabler, on ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userPushInterceptionDo) LeftJoin(table schema.Tabler, on ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userPushInterceptionDo) RightJoin(table schema.Tabler, on ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userPushInterceptionDo) Group(cols ...field.Expr) *userPushInterceptionDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userPushInterceptionDo) Having(conds ...gen.Condition) *userPushInterceptionDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userPushInterceptionDo) Limit(limit int) *userPushInterceptionDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userPushInterceptionDo) Offset(offset int) *userPushInterceptionDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userPushInterceptionDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *userPushInterceptionDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userPushInterceptionDo) Unscoped() *userPushInterceptionDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userPushInterceptionDo) Create(values ...*model.UserPushInterception) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userPushInterceptionDo) CreateInBatches(values []*model.UserPushInterception, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userPushInterceptionDo) Save(values ...*model.UserPushInterception) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userPushInterceptionDo) First() (*model.UserPushInterception, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPushInterception), nil
	}
}

func (u userPushInterceptionDo) Take() (*model.UserPushInterception, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPushInterception), nil
	}
}

func (u userPushInterceptionDo) Last() (*model.UserPushInterception, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPushInterception), nil
	}
}

func (u userPushInterceptionDo) Find() ([]*model.UserPushInterception, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserPushInterception), err
}

func (u userPushInterceptionDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserPushInterception, err error) {
	buf := make([]*model.UserPushInterception, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userPushInterceptionDo) FindInBatches(result *[]*model.UserPushInterception, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userPushInterceptionDo) Attrs(attrs ...field.AssignExpr) *userPushInterceptionDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userPushInterceptionDo) Assign(attrs ...field.AssignExpr) *userPushInterceptionDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userPushInterceptionDo) Joins(fields ...field.RelationField) *userPushInterceptionDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userPushInterceptionDo) Preload(fields ...field.RelationField) *userPushInterceptionDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userPushInterceptionDo) FirstOrInit() (*model.UserPushInterception, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPushInterception), nil
	}
}

func (u userPushInterceptionDo) FirstOrCreate() (*model.UserPushInterception, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPushInterception), nil
	}
}

func (u userPushInterceptionDo) FindByPage(offset int, limit int) (result []*model.UserPushInterception, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userPushInterceptionDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userPushInterceptionDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userPushInterceptionDo) Delete(models ...*model.UserPushInterception) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userPushInterceptionDo) withDO(do gen.Dao) *userPushInterceptionDo {
	u.DO = *do.(*gen.DO)
	return u
}
