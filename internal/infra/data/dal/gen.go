// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                              db,
		AppSetting:                      newAppSetting(db, opts...),
		BooWorldBooCaptureRecord:        newBooWorldBooCaptureRecord(db, opts...),
		BooWorldBooLocation:             newBooWorldBooLocation(db, opts...),
		BooWorldUserLocation:            newBooWorldUserLocation(db, opts...),
		BooWorldUserResource:            newBooWorldUserResource(db, opts...),
		BooWorldUserResourceRelation:    newBooWorldUserResourceRelation(db, opts...),
		BooWorldUserResourceStat:        newBooWorldUserResourceStat(db, opts...),
		CrawledMusicAlbum:               newCrawledMusicAlbum(db, opts...),
		CrawledMusicSinger:              newCrawledMusicSinger(db, opts...),
		CrawledMusicSong:                newCrawledMusicSong(db, opts...),
		CrawledMusicSongSingersRelation: newCrawledMusicSongSingersRelation(db, opts...),
		Fizz:                            newFizz(db, opts...),
		FizzEvent:                       newFizzEvent(db, opts...),
		FizzMedia:                       newFizzMedia(db, opts...),
		FizzMediaMessage:                newFizzMediaMessage(db, opts...),
		FizzMediaResource:               newFizzMediaResource(db, opts...),
		FizzMediaStat:                   newFizzMediaStat(db, opts...),
		FizzMediaUserRelation:           newFizzMediaUserRelation(db, opts...),
		FizzPeekMatchingJob:             newFizzPeekMatchingJob(db, opts...),
		FizzPeekMatchingJobsJoinedFizz:  newFizzPeekMatchingJobsJoinedFizz(db, opts...),
		FizzPeekUserMatchResult:         newFizzPeekUserMatchResult(db, opts...),
		FizzPlayRoom:                    newFizzPlayRoom(db, opts...),
		FizzStat:                        newFizzStat(db, opts...),
		FizzTag:                         newFizzTag(db, opts...),
		FizzTagRelation:                 newFizzTagRelation(db, opts...),
		FizzUserJoinRelation:            newFizzUserJoinRelation(db, opts...),
		HauntBoo:                        newHauntBoo(db, opts...),
		HideSticker:                     newHideSticker(db, opts...),
		Item:                            newItem(db, opts...),
		ItemComment:                     newItemComment(db, opts...),
		ItemCommentReply:                newItemCommentReply(db, opts...),
		ItemCommentsRepliesStat:         newItemCommentsRepliesStat(db, opts...),
		ItemStat:                        newItemStat(db, opts...),
		Portal:                          newPortal(db, opts...),
		PortalMoment:                    newPortalMoment(db, opts...),
		PortalMomentRelation:            newPortalMomentRelation(db, opts...),
		PortalMomentStat:                newPortalMomentStat(db, opts...),
		PortalMomentUserReadRelation:    newPortalMomentUserReadRelation(db, opts...),
		PortalReadRelation:              newPortalReadRelation(db, opts...),
		PortalUserJoinRelation:          newPortalUserJoinRelation(db, opts...),
		StoriesConditionTemplate:        newStoriesConditionTemplate(db, opts...),
		StoriesManagement:               newStoriesManagement(db, opts...),
		StoriesStat:                     newStoriesStat(db, opts...),
		StoriesUsersPlayRecord:          newStoriesUsersPlayRecord(db, opts...),
		Story:                           newStory(db, opts...),
		StoryActivityReadRelation:       newStoryActivityReadRelation(db, opts...),
		StoryUserReaction:               newStoryUserReaction(db, opts...),
		TypeConversation:                newTypeConversation(db, opts...),
		User:                            newUser(db, opts...),
		UserAlbum:                       newUserAlbum(db, opts...),
		UserAlbumsResource:              newUserAlbumsResource(db, opts...),
		UserBooGenerateJob:              newUserBooGenerateJob(db, opts...),
		UserCollectedHideSticker:        newUserCollectedHideSticker(db, opts...),
		UserCommentRepliesLikeRelation:  newUserCommentRepliesLikeRelation(db, opts...),
		UserCreatedBoo:                  newUserCreatedBoo(db, opts...),
		UserCreatedBooAvatar:            newUserCreatedBooAvatar(db, opts...),
		UserDeviceBindRelation:          newUserDeviceBindRelation(db, opts...),
		UserEmailBindRelation:           newUserEmailBindRelation(db, opts...),
		UserHauntBoosShowInfo:           newUserHauntBoosShowInfo(db, opts...),
		UserHauntCapturedBoo:            newUserHauntCapturedBoo(db, opts...),
		UserHighlight:                   newUserHighlight(db, opts...),
		UserHighlightMedia:              newUserHighlightMedia(db, opts...),
		UserHighlightStat:               newUserHighlightStat(db, opts...),
		UserImUUIDRelation:              newUserImUUIDRelation(db, opts...),
		UserPushInterception:            newUserPushInterception(db, opts...),
		UserPushToken:                   newUserPushToken(db, opts...),
		UserRelation:                    newUserRelation(db, opts...),
		UserSearchHistory:               newUserSearchHistory(db, opts...),
		UserSentBooRecord:               newUserSentBooRecord(db, opts...),
		UserShowBooInfo:                 newUserShowBooInfo(db, opts...),
		UserStat:                        newUserStat(db, opts...),
		UserSystemNotification:          newUserSystemNotification(db, opts...),
		UserThirdPartyBindRelation:      newUserThirdPartyBindRelation(db, opts...),
		VibeFizzItem:                    newVibeFizzItem(db, opts...),
		VibeFizzItemUserRelation:        newVibeFizzItemUserRelation(db, opts...),
		VibeFizzStat:                    newVibeFizzStat(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	AppSetting                      appSetting
	BooWorldBooCaptureRecord        booWorldBooCaptureRecord
	BooWorldBooLocation             booWorldBooLocation
	BooWorldUserLocation            booWorldUserLocation
	BooWorldUserResource            booWorldUserResource
	BooWorldUserResourceRelation    booWorldUserResourceRelation
	BooWorldUserResourceStat        booWorldUserResourceStat
	CrawledMusicAlbum               crawledMusicAlbum
	CrawledMusicSinger              crawledMusicSinger
	CrawledMusicSong                crawledMusicSong
	CrawledMusicSongSingersRelation crawledMusicSongSingersRelation
	Fizz                            fizz
	FizzEvent                       fizzEvent
	FizzMedia                       fizzMedia
	FizzMediaMessage                fizzMediaMessage
	FizzMediaResource               fizzMediaResource
	FizzMediaStat                   fizzMediaStat
	FizzMediaUserRelation           fizzMediaUserRelation
	FizzPeekMatchingJob             fizzPeekMatchingJob
	FizzPeekMatchingJobsJoinedFizz  fizzPeekMatchingJobsJoinedFizz
	FizzPeekUserMatchResult         fizzPeekUserMatchResult
	FizzPlayRoom                    fizzPlayRoom
	FizzStat                        fizzStat
	FizzTag                         fizzTag
	FizzTagRelation                 fizzTagRelation
	FizzUserJoinRelation            fizzUserJoinRelation
	HauntBoo                        hauntBoo
	HideSticker                     hideSticker
	Item                            item
	ItemComment                     itemComment
	ItemCommentReply                itemCommentReply
	ItemCommentsRepliesStat         itemCommentsRepliesStat
	ItemStat                        itemStat
	Portal                          portal
	PortalMoment                    portalMoment
	PortalMomentRelation            portalMomentRelation
	PortalMomentStat                portalMomentStat
	PortalMomentUserReadRelation    portalMomentUserReadRelation
	PortalReadRelation              portalReadRelation
	PortalUserJoinRelation          portalUserJoinRelation
	StoriesConditionTemplate        storiesConditionTemplate
	StoriesManagement               storiesManagement
	StoriesStat                     storiesStat
	StoriesUsersPlayRecord          storiesUsersPlayRecord
	Story                           story
	StoryActivityReadRelation       storyActivityReadRelation
	StoryUserReaction               storyUserReaction
	TypeConversation                typeConversation
	User                            user
	UserAlbum                       userAlbum
	UserAlbumsResource              userAlbumsResource
	UserBooGenerateJob              userBooGenerateJob
	UserCollectedHideSticker        userCollectedHideSticker
	UserCommentRepliesLikeRelation  userCommentRepliesLikeRelation
	UserCreatedBoo                  userCreatedBoo
	UserCreatedBooAvatar            userCreatedBooAvatar
	UserDeviceBindRelation          userDeviceBindRelation
	UserEmailBindRelation           userEmailBindRelation
	UserHauntBoosShowInfo           userHauntBoosShowInfo
	UserHauntCapturedBoo            userHauntCapturedBoo
	UserHighlight                   userHighlight
	UserHighlightMedia              userHighlightMedia
	UserHighlightStat               userHighlightStat
	UserImUUIDRelation              userImUUIDRelation
	UserPushInterception            userPushInterception
	UserPushToken                   userPushToken
	UserRelation                    userRelation
	UserSearchHistory               userSearchHistory
	UserSentBooRecord               userSentBooRecord
	UserShowBooInfo                 userShowBooInfo
	UserStat                        userStat
	UserSystemNotification          userSystemNotification
	UserThirdPartyBindRelation      userThirdPartyBindRelation
	VibeFizzItem                    vibeFizzItem
	VibeFizzItemUserRelation        vibeFizzItemUserRelation
	VibeFizzStat                    vibeFizzStat
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                              db,
		AppSetting:                      q.AppSetting.clone(db),
		BooWorldBooCaptureRecord:        q.BooWorldBooCaptureRecord.clone(db),
		BooWorldBooLocation:             q.BooWorldBooLocation.clone(db),
		BooWorldUserLocation:            q.BooWorldUserLocation.clone(db),
		BooWorldUserResource:            q.BooWorldUserResource.clone(db),
		BooWorldUserResourceRelation:    q.BooWorldUserResourceRelation.clone(db),
		BooWorldUserResourceStat:        q.BooWorldUserResourceStat.clone(db),
		CrawledMusicAlbum:               q.CrawledMusicAlbum.clone(db),
		CrawledMusicSinger:              q.CrawledMusicSinger.clone(db),
		CrawledMusicSong:                q.CrawledMusicSong.clone(db),
		CrawledMusicSongSingersRelation: q.CrawledMusicSongSingersRelation.clone(db),
		Fizz:                            q.Fizz.clone(db),
		FizzEvent:                       q.FizzEvent.clone(db),
		FizzMedia:                       q.FizzMedia.clone(db),
		FizzMediaMessage:                q.FizzMediaMessage.clone(db),
		FizzMediaResource:               q.FizzMediaResource.clone(db),
		FizzMediaStat:                   q.FizzMediaStat.clone(db),
		FizzMediaUserRelation:           q.FizzMediaUserRelation.clone(db),
		FizzPeekMatchingJob:             q.FizzPeekMatchingJob.clone(db),
		FizzPeekMatchingJobsJoinedFizz:  q.FizzPeekMatchingJobsJoinedFizz.clone(db),
		FizzPeekUserMatchResult:         q.FizzPeekUserMatchResult.clone(db),
		FizzPlayRoom:                    q.FizzPlayRoom.clone(db),
		FizzStat:                        q.FizzStat.clone(db),
		FizzTag:                         q.FizzTag.clone(db),
		FizzTagRelation:                 q.FizzTagRelation.clone(db),
		FizzUserJoinRelation:            q.FizzUserJoinRelation.clone(db),
		HauntBoo:                        q.HauntBoo.clone(db),
		HideSticker:                     q.HideSticker.clone(db),
		Item:                            q.Item.clone(db),
		ItemComment:                     q.ItemComment.clone(db),
		ItemCommentReply:                q.ItemCommentReply.clone(db),
		ItemCommentsRepliesStat:         q.ItemCommentsRepliesStat.clone(db),
		ItemStat:                        q.ItemStat.clone(db),
		Portal:                          q.Portal.clone(db),
		PortalMoment:                    q.PortalMoment.clone(db),
		PortalMomentRelation:            q.PortalMomentRelation.clone(db),
		PortalMomentStat:                q.PortalMomentStat.clone(db),
		PortalMomentUserReadRelation:    q.PortalMomentUserReadRelation.clone(db),
		PortalReadRelation:              q.PortalReadRelation.clone(db),
		PortalUserJoinRelation:          q.PortalUserJoinRelation.clone(db),
		StoriesConditionTemplate:        q.StoriesConditionTemplate.clone(db),
		StoriesManagement:               q.StoriesManagement.clone(db),
		StoriesStat:                     q.StoriesStat.clone(db),
		StoriesUsersPlayRecord:          q.StoriesUsersPlayRecord.clone(db),
		Story:                           q.Story.clone(db),
		StoryActivityReadRelation:       q.StoryActivityReadRelation.clone(db),
		StoryUserReaction:               q.StoryUserReaction.clone(db),
		TypeConversation:                q.TypeConversation.clone(db),
		User:                            q.User.clone(db),
		UserAlbum:                       q.UserAlbum.clone(db),
		UserAlbumsResource:              q.UserAlbumsResource.clone(db),
		UserBooGenerateJob:              q.UserBooGenerateJob.clone(db),
		UserCollectedHideSticker:        q.UserCollectedHideSticker.clone(db),
		UserCommentRepliesLikeRelation:  q.UserCommentRepliesLikeRelation.clone(db),
		UserCreatedBoo:                  q.UserCreatedBoo.clone(db),
		UserCreatedBooAvatar:            q.UserCreatedBooAvatar.clone(db),
		UserDeviceBindRelation:          q.UserDeviceBindRelation.clone(db),
		UserEmailBindRelation:           q.UserEmailBindRelation.clone(db),
		UserHauntBoosShowInfo:           q.UserHauntBoosShowInfo.clone(db),
		UserHauntCapturedBoo:            q.UserHauntCapturedBoo.clone(db),
		UserHighlight:                   q.UserHighlight.clone(db),
		UserHighlightMedia:              q.UserHighlightMedia.clone(db),
		UserHighlightStat:               q.UserHighlightStat.clone(db),
		UserImUUIDRelation:              q.UserImUUIDRelation.clone(db),
		UserPushInterception:            q.UserPushInterception.clone(db),
		UserPushToken:                   q.UserPushToken.clone(db),
		UserRelation:                    q.UserRelation.clone(db),
		UserSearchHistory:               q.UserSearchHistory.clone(db),
		UserSentBooRecord:               q.UserSentBooRecord.clone(db),
		UserShowBooInfo:                 q.UserShowBooInfo.clone(db),
		UserStat:                        q.UserStat.clone(db),
		UserSystemNotification:          q.UserSystemNotification.clone(db),
		UserThirdPartyBindRelation:      q.UserThirdPartyBindRelation.clone(db),
		VibeFizzItem:                    q.VibeFizzItem.clone(db),
		VibeFizzItemUserRelation:        q.VibeFizzItemUserRelation.clone(db),
		VibeFizzStat:                    q.VibeFizzStat.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                              db,
		AppSetting:                      q.AppSetting.replaceDB(db),
		BooWorldBooCaptureRecord:        q.BooWorldBooCaptureRecord.replaceDB(db),
		BooWorldBooLocation:             q.BooWorldBooLocation.replaceDB(db),
		BooWorldUserLocation:            q.BooWorldUserLocation.replaceDB(db),
		BooWorldUserResource:            q.BooWorldUserResource.replaceDB(db),
		BooWorldUserResourceRelation:    q.BooWorldUserResourceRelation.replaceDB(db),
		BooWorldUserResourceStat:        q.BooWorldUserResourceStat.replaceDB(db),
		CrawledMusicAlbum:               q.CrawledMusicAlbum.replaceDB(db),
		CrawledMusicSinger:              q.CrawledMusicSinger.replaceDB(db),
		CrawledMusicSong:                q.CrawledMusicSong.replaceDB(db),
		CrawledMusicSongSingersRelation: q.CrawledMusicSongSingersRelation.replaceDB(db),
		Fizz:                            q.Fizz.replaceDB(db),
		FizzEvent:                       q.FizzEvent.replaceDB(db),
		FizzMedia:                       q.FizzMedia.replaceDB(db),
		FizzMediaMessage:                q.FizzMediaMessage.replaceDB(db),
		FizzMediaResource:               q.FizzMediaResource.replaceDB(db),
		FizzMediaStat:                   q.FizzMediaStat.replaceDB(db),
		FizzMediaUserRelation:           q.FizzMediaUserRelation.replaceDB(db),
		FizzPeekMatchingJob:             q.FizzPeekMatchingJob.replaceDB(db),
		FizzPeekMatchingJobsJoinedFizz:  q.FizzPeekMatchingJobsJoinedFizz.replaceDB(db),
		FizzPeekUserMatchResult:         q.FizzPeekUserMatchResult.replaceDB(db),
		FizzPlayRoom:                    q.FizzPlayRoom.replaceDB(db),
		FizzStat:                        q.FizzStat.replaceDB(db),
		FizzTag:                         q.FizzTag.replaceDB(db),
		FizzTagRelation:                 q.FizzTagRelation.replaceDB(db),
		FizzUserJoinRelation:            q.FizzUserJoinRelation.replaceDB(db),
		HauntBoo:                        q.HauntBoo.replaceDB(db),
		HideSticker:                     q.HideSticker.replaceDB(db),
		Item:                            q.Item.replaceDB(db),
		ItemComment:                     q.ItemComment.replaceDB(db),
		ItemCommentReply:                q.ItemCommentReply.replaceDB(db),
		ItemCommentsRepliesStat:         q.ItemCommentsRepliesStat.replaceDB(db),
		ItemStat:                        q.ItemStat.replaceDB(db),
		Portal:                          q.Portal.replaceDB(db),
		PortalMoment:                    q.PortalMoment.replaceDB(db),
		PortalMomentRelation:            q.PortalMomentRelation.replaceDB(db),
		PortalMomentStat:                q.PortalMomentStat.replaceDB(db),
		PortalMomentUserReadRelation:    q.PortalMomentUserReadRelation.replaceDB(db),
		PortalReadRelation:              q.PortalReadRelation.replaceDB(db),
		PortalUserJoinRelation:          q.PortalUserJoinRelation.replaceDB(db),
		StoriesConditionTemplate:        q.StoriesConditionTemplate.replaceDB(db),
		StoriesManagement:               q.StoriesManagement.replaceDB(db),
		StoriesStat:                     q.StoriesStat.replaceDB(db),
		StoriesUsersPlayRecord:          q.StoriesUsersPlayRecord.replaceDB(db),
		Story:                           q.Story.replaceDB(db),
		StoryActivityReadRelation:       q.StoryActivityReadRelation.replaceDB(db),
		StoryUserReaction:               q.StoryUserReaction.replaceDB(db),
		TypeConversation:                q.TypeConversation.replaceDB(db),
		User:                            q.User.replaceDB(db),
		UserAlbum:                       q.UserAlbum.replaceDB(db),
		UserAlbumsResource:              q.UserAlbumsResource.replaceDB(db),
		UserBooGenerateJob:              q.UserBooGenerateJob.replaceDB(db),
		UserCollectedHideSticker:        q.UserCollectedHideSticker.replaceDB(db),
		UserCommentRepliesLikeRelation:  q.UserCommentRepliesLikeRelation.replaceDB(db),
		UserCreatedBoo:                  q.UserCreatedBoo.replaceDB(db),
		UserCreatedBooAvatar:            q.UserCreatedBooAvatar.replaceDB(db),
		UserDeviceBindRelation:          q.UserDeviceBindRelation.replaceDB(db),
		UserEmailBindRelation:           q.UserEmailBindRelation.replaceDB(db),
		UserHauntBoosShowInfo:           q.UserHauntBoosShowInfo.replaceDB(db),
		UserHauntCapturedBoo:            q.UserHauntCapturedBoo.replaceDB(db),
		UserHighlight:                   q.UserHighlight.replaceDB(db),
		UserHighlightMedia:              q.UserHighlightMedia.replaceDB(db),
		UserHighlightStat:               q.UserHighlightStat.replaceDB(db),
		UserImUUIDRelation:              q.UserImUUIDRelation.replaceDB(db),
		UserPushInterception:            q.UserPushInterception.replaceDB(db),
		UserPushToken:                   q.UserPushToken.replaceDB(db),
		UserRelation:                    q.UserRelation.replaceDB(db),
		UserSearchHistory:               q.UserSearchHistory.replaceDB(db),
		UserSentBooRecord:               q.UserSentBooRecord.replaceDB(db),
		UserShowBooInfo:                 q.UserShowBooInfo.replaceDB(db),
		UserStat:                        q.UserStat.replaceDB(db),
		UserSystemNotification:          q.UserSystemNotification.replaceDB(db),
		UserThirdPartyBindRelation:      q.UserThirdPartyBindRelation.replaceDB(db),
		VibeFizzItem:                    q.VibeFizzItem.replaceDB(db),
		VibeFizzItemUserRelation:        q.VibeFizzItemUserRelation.replaceDB(db),
		VibeFizzStat:                    q.VibeFizzStat.replaceDB(db),
	}
}

type queryCtx struct {
	AppSetting                      *appSettingDo
	BooWorldBooCaptureRecord        *booWorldBooCaptureRecordDo
	BooWorldBooLocation             *booWorldBooLocationDo
	BooWorldUserLocation            *booWorldUserLocationDo
	BooWorldUserResource            *booWorldUserResourceDo
	BooWorldUserResourceRelation    *booWorldUserResourceRelationDo
	BooWorldUserResourceStat        *booWorldUserResourceStatDo
	CrawledMusicAlbum               *crawledMusicAlbumDo
	CrawledMusicSinger              *crawledMusicSingerDo
	CrawledMusicSong                *crawledMusicSongDo
	CrawledMusicSongSingersRelation *crawledMusicSongSingersRelationDo
	Fizz                            *fizzDo
	FizzEvent                       *fizzEventDo
	FizzMedia                       *fizzMediaDo
	FizzMediaMessage                *fizzMediaMessageDo
	FizzMediaResource               *fizzMediaResourceDo
	FizzMediaStat                   *fizzMediaStatDo
	FizzMediaUserRelation           *fizzMediaUserRelationDo
	FizzPeekMatchingJob             *fizzPeekMatchingJobDo
	FizzPeekMatchingJobsJoinedFizz  *fizzPeekMatchingJobsJoinedFizzDo
	FizzPeekUserMatchResult         *fizzPeekUserMatchResultDo
	FizzPlayRoom                    *fizzPlayRoomDo
	FizzStat                        *fizzStatDo
	FizzTag                         *fizzTagDo
	FizzTagRelation                 *fizzTagRelationDo
	FizzUserJoinRelation            *fizzUserJoinRelationDo
	HauntBoo                        *hauntBooDo
	HideSticker                     *hideStickerDo
	Item                            *itemDo
	ItemComment                     *itemCommentDo
	ItemCommentReply                *itemCommentReplyDo
	ItemCommentsRepliesStat         *itemCommentsRepliesStatDo
	ItemStat                        *itemStatDo
	Portal                          *portalDo
	PortalMoment                    *portalMomentDo
	PortalMomentRelation            *portalMomentRelationDo
	PortalMomentStat                *portalMomentStatDo
	PortalMomentUserReadRelation    *portalMomentUserReadRelationDo
	PortalReadRelation              *portalReadRelationDo
	PortalUserJoinRelation          *portalUserJoinRelationDo
	StoriesConditionTemplate        *storiesConditionTemplateDo
	StoriesManagement               *storiesManagementDo
	StoriesStat                     *storiesStatDo
	StoriesUsersPlayRecord          *storiesUsersPlayRecordDo
	Story                           *storyDo
	StoryActivityReadRelation       *storyActivityReadRelationDo
	StoryUserReaction               *storyUserReactionDo
	TypeConversation                *typeConversationDo
	User                            *userDo
	UserAlbum                       *userAlbumDo
	UserAlbumsResource              *userAlbumsResourceDo
	UserBooGenerateJob              *userBooGenerateJobDo
	UserCollectedHideSticker        *userCollectedHideStickerDo
	UserCommentRepliesLikeRelation  *userCommentRepliesLikeRelationDo
	UserCreatedBoo                  *userCreatedBooDo
	UserCreatedBooAvatar            *userCreatedBooAvatarDo
	UserDeviceBindRelation          *userDeviceBindRelationDo
	UserEmailBindRelation           *userEmailBindRelationDo
	UserHauntBoosShowInfo           *userHauntBoosShowInfoDo
	UserHauntCapturedBoo            *userHauntCapturedBooDo
	UserHighlight                   *userHighlightDo
	UserHighlightMedia              *userHighlightMediaDo
	UserHighlightStat               *userHighlightStatDo
	UserImUUIDRelation              *userImUUIDRelationDo
	UserPushInterception            *userPushInterceptionDo
	UserPushToken                   *userPushTokenDo
	UserRelation                    *userRelationDo
	UserSearchHistory               *userSearchHistoryDo
	UserSentBooRecord               *userSentBooRecordDo
	UserShowBooInfo                 *userShowBooInfoDo
	UserStat                        *userStatDo
	UserSystemNotification          *userSystemNotificationDo
	UserThirdPartyBindRelation      *userThirdPartyBindRelationDo
	VibeFizzItem                    *vibeFizzItemDo
	VibeFizzItemUserRelation        *vibeFizzItemUserRelationDo
	VibeFizzStat                    *vibeFizzStatDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		AppSetting:                      q.AppSetting.WithContext(ctx),
		BooWorldBooCaptureRecord:        q.BooWorldBooCaptureRecord.WithContext(ctx),
		BooWorldBooLocation:             q.BooWorldBooLocation.WithContext(ctx),
		BooWorldUserLocation:            q.BooWorldUserLocation.WithContext(ctx),
		BooWorldUserResource:            q.BooWorldUserResource.WithContext(ctx),
		BooWorldUserResourceRelation:    q.BooWorldUserResourceRelation.WithContext(ctx),
		BooWorldUserResourceStat:        q.BooWorldUserResourceStat.WithContext(ctx),
		CrawledMusicAlbum:               q.CrawledMusicAlbum.WithContext(ctx),
		CrawledMusicSinger:              q.CrawledMusicSinger.WithContext(ctx),
		CrawledMusicSong:                q.CrawledMusicSong.WithContext(ctx),
		CrawledMusicSongSingersRelation: q.CrawledMusicSongSingersRelation.WithContext(ctx),
		Fizz:                            q.Fizz.WithContext(ctx),
		FizzEvent:                       q.FizzEvent.WithContext(ctx),
		FizzMedia:                       q.FizzMedia.WithContext(ctx),
		FizzMediaMessage:                q.FizzMediaMessage.WithContext(ctx),
		FizzMediaResource:               q.FizzMediaResource.WithContext(ctx),
		FizzMediaStat:                   q.FizzMediaStat.WithContext(ctx),
		FizzMediaUserRelation:           q.FizzMediaUserRelation.WithContext(ctx),
		FizzPeekMatchingJob:             q.FizzPeekMatchingJob.WithContext(ctx),
		FizzPeekMatchingJobsJoinedFizz:  q.FizzPeekMatchingJobsJoinedFizz.WithContext(ctx),
		FizzPeekUserMatchResult:         q.FizzPeekUserMatchResult.WithContext(ctx),
		FizzPlayRoom:                    q.FizzPlayRoom.WithContext(ctx),
		FizzStat:                        q.FizzStat.WithContext(ctx),
		FizzTag:                         q.FizzTag.WithContext(ctx),
		FizzTagRelation:                 q.FizzTagRelation.WithContext(ctx),
		FizzUserJoinRelation:            q.FizzUserJoinRelation.WithContext(ctx),
		HauntBoo:                        q.HauntBoo.WithContext(ctx),
		HideSticker:                     q.HideSticker.WithContext(ctx),
		Item:                            q.Item.WithContext(ctx),
		ItemComment:                     q.ItemComment.WithContext(ctx),
		ItemCommentReply:                q.ItemCommentReply.WithContext(ctx),
		ItemCommentsRepliesStat:         q.ItemCommentsRepliesStat.WithContext(ctx),
		ItemStat:                        q.ItemStat.WithContext(ctx),
		Portal:                          q.Portal.WithContext(ctx),
		PortalMoment:                    q.PortalMoment.WithContext(ctx),
		PortalMomentRelation:            q.PortalMomentRelation.WithContext(ctx),
		PortalMomentStat:                q.PortalMomentStat.WithContext(ctx),
		PortalMomentUserReadRelation:    q.PortalMomentUserReadRelation.WithContext(ctx),
		PortalReadRelation:              q.PortalReadRelation.WithContext(ctx),
		PortalUserJoinRelation:          q.PortalUserJoinRelation.WithContext(ctx),
		StoriesConditionTemplate:        q.StoriesConditionTemplate.WithContext(ctx),
		StoriesManagement:               q.StoriesManagement.WithContext(ctx),
		StoriesStat:                     q.StoriesStat.WithContext(ctx),
		StoriesUsersPlayRecord:          q.StoriesUsersPlayRecord.WithContext(ctx),
		Story:                           q.Story.WithContext(ctx),
		StoryActivityReadRelation:       q.StoryActivityReadRelation.WithContext(ctx),
		StoryUserReaction:               q.StoryUserReaction.WithContext(ctx),
		TypeConversation:                q.TypeConversation.WithContext(ctx),
		User:                            q.User.WithContext(ctx),
		UserAlbum:                       q.UserAlbum.WithContext(ctx),
		UserAlbumsResource:              q.UserAlbumsResource.WithContext(ctx),
		UserBooGenerateJob:              q.UserBooGenerateJob.WithContext(ctx),
		UserCollectedHideSticker:        q.UserCollectedHideSticker.WithContext(ctx),
		UserCommentRepliesLikeRelation:  q.UserCommentRepliesLikeRelation.WithContext(ctx),
		UserCreatedBoo:                  q.UserCreatedBoo.WithContext(ctx),
		UserCreatedBooAvatar:            q.UserCreatedBooAvatar.WithContext(ctx),
		UserDeviceBindRelation:          q.UserDeviceBindRelation.WithContext(ctx),
		UserEmailBindRelation:           q.UserEmailBindRelation.WithContext(ctx),
		UserHauntBoosShowInfo:           q.UserHauntBoosShowInfo.WithContext(ctx),
		UserHauntCapturedBoo:            q.UserHauntCapturedBoo.WithContext(ctx),
		UserHighlight:                   q.UserHighlight.WithContext(ctx),
		UserHighlightMedia:              q.UserHighlightMedia.WithContext(ctx),
		UserHighlightStat:               q.UserHighlightStat.WithContext(ctx),
		UserImUUIDRelation:              q.UserImUUIDRelation.WithContext(ctx),
		UserPushInterception:            q.UserPushInterception.WithContext(ctx),
		UserPushToken:                   q.UserPushToken.WithContext(ctx),
		UserRelation:                    q.UserRelation.WithContext(ctx),
		UserSearchHistory:               q.UserSearchHistory.WithContext(ctx),
		UserSentBooRecord:               q.UserSentBooRecord.WithContext(ctx),
		UserShowBooInfo:                 q.UserShowBooInfo.WithContext(ctx),
		UserStat:                        q.UserStat.WithContext(ctx),
		UserSystemNotification:          q.UserSystemNotification.WithContext(ctx),
		UserThirdPartyBindRelation:      q.UserThirdPartyBindRelation.WithContext(ctx),
		VibeFizzItem:                    q.VibeFizzItem.WithContext(ctx),
		VibeFizzItemUserRelation:        q.VibeFizzItemUserRelation.WithContext(ctx),
		VibeFizzStat:                    q.VibeFizzStat.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
