CREATE TABLE
  `user_haunt_boos_show_infos` (
    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `user_id` bigint unsigned NOT NULL,
    `show_info` longtext NOT NULL COMMENT 'json 格式存储',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_idx_user_id` (`user_id`)
) ENGINE = InnoDB AUTO_INCREMENT = 622 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci