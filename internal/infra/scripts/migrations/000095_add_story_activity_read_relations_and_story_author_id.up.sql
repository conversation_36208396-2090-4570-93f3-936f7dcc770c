START TRANSACTION;

-- Create story_activity_read_relations table if not exists
CREATE TABLE IF NOT EXISTS story_activity_read_relations (
    user_id BIGINT NOT NULL,
    last_read_like_id BIGINT DEFAULT 0,
    last_read_comment_id BIGINT DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id),
    INDEX idx_updated_at (updated_at)
);

-- Add story_author_id and index to story_user_reactions
ALTER TABLE `story_user_reactions` 
    ADD COLUMN `story_author_id` bigint unsigned NOT NULL AFTER `story_id`,
    ADD INDEX `idx_story_author_id_created_at` (`story_author_id`, `created_at`);

COMMIT;
