package main

import (
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func generateUserModels(g *gen.Generator, db *gorm.DB) {
	g.UseDB(db)
	userRelations := g.GenerateModel("user_relations")
	userStats := g.GenerateModel("user_stats")
	userImUuidRelations := g.GenerateModel("user_im_uuid_relations")
	userBinds := g.GenerateModel("user_third_party_bind_relations")
	userDeviceBinds := g.GenerateModel("user_device_bind_relations")
	userEmailBinds := g.GenerateModel("user_email_bind_relations")
	userPushTokens := g.GenerateModel("user_push_tokens")
	userHighlightStats := g.GenerateModel("user_highlight_stats")
	userHighlightMedias := g.GenerateModel("user_highlight_medias")
	userSystemNotifications := g.GenerateModel("user_system_notifications")
	userAlbums := g.GenerateModel("user_albums")
	userAlbumsResources := g.GenerateModel("user_albums_resources")
	// ------ boo
	userCreatedBoo := g.GenerateModel("user_created_boos")
	userSentBooRecord := g.GenerateModel("user_sent_boo_records")
	userShowBooInfo := g.GenerateModel("user_show_boo_infos")
	userBooGenerateJobs := g.GenerateModel("user_boo_generate_jobs")
	userCreatedAvatars := g.GenerateModel("user_created_boo_avatars")
	userSearchHistory := g.GenerateModel("user_search_history")
	userPushInterceptions := g.GenerateModel("user_push_interceptions")

	g.ApplyBasic(
		userBinds,
		userDeviceBinds,
		userRelations,
		userStats,
		userEmailBinds,
		userImUuidRelations,
		userPushTokens,
		userHighlightStats,
		userHighlightMedias,
		userSystemNotifications,
		userAlbums,
		userAlbumsResources,

		userCreatedBoo,
		userSentBooRecord,
		userShowBooInfo,
		userBooGenerateJobs,
		userCreatedAvatars,
		userSearchHistory,
		userPushInterceptions,

		g.GenerateModel("user_highlights", gen.FieldRelate(field.HasOne, "Stats", userHighlightStats, &field.RelateConfig{
			GORMTag: map[string][]string{
				"foreignKey": {"highlight_id"},
				"references": {"id"},
			},
		})),
		g.GenerateModel("users",
			gen.FieldRelate(field.HasMany, "Binds", userBinds, &field.RelateConfig{
				RelateSlice: true,
				GORMTag: map[string][]string{
					"foreignKey": {"user_id"},
					"references": {"id"},
				},
			}),
			gen.FieldRelate(field.HasMany, "DeviceBinds", userDeviceBinds, &field.RelateConfig{
				RelateSlice: true,
				GORMTag: map[string][]string{
					"foreignKey": {"user_id"},
					"references": {"id"},
				},
			}),
			gen.FieldRelate(field.HasOne, "Stats", userStats, &field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"user_id"},
					"references": {"id"},
				},
			}),
			gen.FieldRelate(field.HasOne, "ImUuidRelation", userImUuidRelations, &field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"user_id"},
					"references": {"id"},
				},
			}),
		),
	)
}
