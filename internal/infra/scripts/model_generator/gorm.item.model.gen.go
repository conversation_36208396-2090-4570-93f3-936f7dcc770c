package main

import (
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func generatePortalModels(g *gen.Generator, db *gorm.DB) {
	g.UseDB(db)
	portal := g.GenerateModel("portals")
	portalMomentStat := g.GenerateModel("portal_moment_stats")
	portalMomentRelation := g.Generate<PERSON>odel("portal_moment_relations")
	portalReadRelation := g.GenerateModel("portal_read_relations")
	portalMomentReadRelation := g.GenerateModel("portal_moment_user_read_relations")
	portalUserJoinRelation := g.<PERSON>rate<PERSON>odel("portal_user_join_relations")

	g.ApplyBasic(
		g.<PERSON>rateModel("portal_moments",
			gen.FieldRelate(field.HasOne, "Stat", portalMomentStat, &field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"moment_id"},
					"references": {"id"},
				},
			}),
		),
		g.Generate<PERSON>odel("portal_moments",
			gen.FieldRelate(field.HasOne, "Stat", portalMomentStat, &field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"moment_id"},
					"references": {"id"},
				},
			}),
		),
		portalMomentStat,
		portalMomentRelation,
		portal,
		portalReadRelation,
		portalMomentReadRelation,
		portalUserJoinRelation,
	)
}

func generateHideStickersModels(g *gen.Generator, db *gorm.DB) {
	g.UseDB(db)
	hideStickers := g.GenerateModel("hide_stickers")
	userCollectedHideStickers := g.GenerateModel("user_collected_hide_stickers")
	g.ApplyBasic(
		userCollectedHideStickers,
		hideStickers,
	)
}

func generateStoryModels(g *gen.Generator, db *gorm.DB) {
	g.UseDB(db)
	storyConditionTemplates := g.GenerateModel("stories_condition_templates")
	storyUserPlayRecords := g.GenerateModel("stories_users_play_records")
	storyStat := g.GenerateModel("stories_stats")
	storyUserReactions := g.GenerateModel("story_user_reactions")
	storiesManagement := g.GenerateModel("stories_management")
	typeConversations := g.GenerateModel("type_conversations")
	storyActivityReadRelation := g.GenerateModel("story_activity_read_relations")

	hauntUserCapturedBoo := g.GenerateModel("user_haunt_captured_boos")
	hauntBoo := g.GenerateModel("haunt_boos")
	hauntBooShowInfo := g.GenerateModel("user_haunt_boos_show_infos")

	stories := g.GenerateModel("stories",
		gen.FieldRelate(field.HasOne, "Stat", storyStat, &field.RelateConfig{
			GORMTag: map[string][]string{
				"foreignKey": {"story_id"},
				"references": {"id"},
			},
		}),
	)
	g.ApplyBasic(
		storyConditionTemplates,
		storyUserPlayRecords,
		stories,
		storyStat,
		storyUserReactions,
		storiesManagement,
		typeConversations,
		storyActivityReadRelation,
		hauntUserCapturedBoo,
		hauntBoo,
		hauntBooShowInfo,
	)
}

func generateItemModels(g *gen.Generator, db *gorm.DB) {
	g.UseDB(db)
	commentReplyStat := g.GenerateModel("item_comments_replies_stats")
	commentReply := g.GenerateModel("item_comment_replies",
		gen.FieldRelate(field.HasOne, "Stat", commentReplyStat, &field.RelateConfig{
			GORMTag: map[string][]string{
				"foreignKey": {"comment_reply_id"},
				"references": {"id"},
			},
		}),
	)
	itemStat := g.GenerateModel("item_stats")

	crawledMusicSingers := g.GenerateModel("crawled_music_singers")
	crawledMusicAlbums := g.GenerateModel("crawled_music_albums")
	crawledMusicSongs := g.GenerateModel("crawled_music_songs")
	crawledMusicSongSingersRelations := g.GenerateModel("crawled_music_song_singers_relations")
	userCommentReplyLikeRelation := g.GenerateModel("user_comment_replies_like_relations")
	g.ApplyBasic(
		commentReplyStat,
		userCommentReplyLikeRelation,
		g.GenerateModel("item_comments",
			gen.FieldRelate(field.HasOne, "Stat", commentReplyStat, &field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"comment_reply_id"},
					"references": {"id"},
				},
			}),
			gen.FieldRelate(field.HasMany, "TopReplies", commentReply, &field.RelateConfig{
				RelateSlicePointer: true,
				GORMTag: map[string][]string{
					"foreignKey": {"comment_id"},
					"references": {"id"},
				},
			}),
		),
		commentReply,
		crawledMusicAlbums,
		crawledMusicSingers,
		crawledMusicSongSingersRelations,
		crawledMusicSongs,
		itemStat,
		g.GenerateModel("items", gen.FieldRelate(field.HasOne, "Stat", itemStat, &field.RelateConfig{
			GORMTag: map[string][]string{
				"foreignKey": {"item_id"},
				"references": {"id"},
			},
		})),
	)
}
