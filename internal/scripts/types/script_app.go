package types

import (
	"boson/internal/conf"
	"boson/internal/infra/data"

	adapter_driving_repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	adapter_driving_services_cdn "boson/internal/adapter/driving/services/cdn"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_ai "boson/internal/domain/services/ai"
	usecases_items "boson/internal/usecases/items"

	"github.com/go-kratos/kratos/v2/log"
)

type ScriptApp struct {
	Logger          *log.Helper
	Log             *log.Helper
	Dat             *data.Data
	Conf            *conf.Bootstrap
	CdnService      *adapter_driving_services_cdn.Service
	StoryUc         *usecases_items.ItemStoryUsecase
	TTSService      *domain_services_ai.TtsService
	AppSettingsRepo adapter_driving_repos_app_settings.ConfigStorage
	UniqIdGenerator domain_interfaces.UniqIdGenerator
}

// NewScriptApp 构造函数
func NewScriptApp(
	logger *log.Helper,
	dat *data.Data,
	conf *conf.Bootstrap,
	cdnService *adapter_driving_services_cdn.Service,
	storyUc *usecases_items.ItemStoryUsecase,
	ttsService *domain_services_ai.TtsService,
	appSettingsRepo adapter_driving_repos_app_settings.ConfigStorage,
	uniqIdGenerator domain_interfaces.UniqIdGenerator,
) *ScriptApp {
	return &ScriptApp{
		Logger:          logger,
		Log:             logger,
		Dat:             dat,
		Conf:            conf,
		CdnService:      cdnService,
		StoryUc:         storyUc,
		TTSService:      ttsService,
		AppSettingsRepo: appSettingsRepo,
		UniqIdGenerator: uniqIdGenerator,
	}
}
