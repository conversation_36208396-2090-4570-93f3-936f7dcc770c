package main

import (
	"boson/internal/conf"
	"context"
	"flag"
	"os"

	_2025_08_18_larry_add_avatar_as_stickers "boson/internal/scripts/2025_08_18_larry_add_avatar_as_stickers"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
)

func main() {
	// 添加命令行参数
	configPath := flag.String("config", "../../../../configs/config.ut.yaml", "配置文件路径")
	flag.Parse()

	bootstrap, err := loadConfigFile(*configPath)
	if err != nil {
		panic(err)
	}
	app, cleanup, err := wireScriptApp(
		bootstrap,
		newLogHelper(),
	)
	if err != nil {
		panic(err)
	}

	// if err := _2025_06_23_larry_fix_stories_cover_images.Run(context.Background(), app); err != nil {
	// 	panic(err)
	// }
	// if err := _2025_07_24_larry_add_chatproxy_greeting_configs.Run(context.Background(), app); err != nil {
	// 	panic(err)
	// }
	if err := _2025_08_18_larry_add_avatar_as_stickers.Run(context.Background(), app); err != nil {
		panic(err)
	}
	defer cleanup()

}
func newLogHelper() *log.Helper {
	logFormat := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
	)
	logger := log.NewHelper(logFormat)
	return logger
}

func loadConfigFile(path string) (*conf.Bootstrap, error) {
	c := config.New(
		config.WithSource(
			file.NewSource(path),
		),
	)
	if err := c.Load(); err != nil {
		return nil, err
	}
	var bootstrap conf.Bootstrap
	if err := c.Scan(&bootstrap); err != nil {
		return nil, err
	}
	return &bootstrap, nil
}
