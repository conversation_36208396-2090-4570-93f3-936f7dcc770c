// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"boson/internal/adapter/driving/repos/app_settings"
	"boson/internal/adapter/driving/repos/items/story"
	"boson/internal/adapter/driving/repos/users/binds"
	"boson/internal/adapter/driving/repos/users/boo"
	"boson/internal/adapter/driving/repos/users/im"
	"boson/internal/adapter/driving/repos/users/info"
	"boson/internal/adapter/driving/repos/users/relations"
	"boson/internal/adapter/driving/services/agora"
	"boson/internal/adapter/driving/services/cdn"
	"boson/internal/adapter/driving/services/cdn/s3"
	"boson/internal/adapter/driving/services/kling"
	"boson/internal/adapter/driving/services/openai"
	"boson/internal/adapter/driving/services/openrouter"
	"boson/internal/adapter/driving/services/polly"
	"boson/internal/adapter/driving/services/recommend"
	"boson/internal/adapter/driving/services/sam"
	"boson/internal/adapter/driving/services/uniq_id_generator"
	"boson/internal/conf"
	"boson/internal/domain/services/ai"
	"boson/internal/domain/services/im"
	"boson/internal/domain/services/items/portal"
	"boson/internal/domain/services/items/story"
	"boson/internal/domain/services/users/info"
	"boson/internal/infra/data"
	"boson/internal/scripts/types"
	"boson/internal/usecases/items"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

// wireScriptApp 使用 Wire 注入依赖
func wireScriptApp(conf2 *conf.Bootstrap, logger *log.Helper) (*types.ScriptApp, func(), error) {
	dataData, cleanup := data.NewData(conf2, logger)
	service := s3.NewService(conf2)
	adapter_driving_services_cdnService := adapter_driving_services_cdn.NewService(service)
	uniqIdGenerator := uniq_id_generator.NewUniqIdGenerator(dataData, logger)
	repo := adapter_driving_repos_app_settings.NewRepo(dataData)
	storyTemplateRepo := adapter_driving_repos_items_story.NewStoryTemplateRepo(dataData, uniqIdGenerator, repo)
	storyTopRepo := adapter_driving_repos_items_story.NewStoryTopRepo(dataData, logger)
	storyStatRepo := adapter_driving_repos_items_story.NewStoryStatRepo(dataData)
	adapter_driving_repos_users_relationsRepo := adapter_driving_repos_users_relations.NewRepo(dataData)
	queryRepo := adapter_driving_repos_users_binds.NewQueryRepo(dataData)
	adapter_driving_repos_users_infoQueryRepo := adapter_driving_repos_users_info.NewQueryRepo(dataData, adapter_driving_repos_users_relationsRepo, queryRepo)
	adapter_driving_repos_users_booRepo := adapter_driving_repos_users_boo.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	userPortalRelationRepo := adapter_driving_repos_items_story.NewUserPortalRelationRepo(dataData)
	storyContextRepo := adapter_driving_repos_items_story.NewStoryContextRepo(dataData, userPortalRelationRepo)
	storyReactionRepo := adapter_driving_repos_items_story.NewStoryReactionRepo(dataData, storyStatRepo)
	storyQueryRepo := adapter_driving_repos_items_story.NewStoryQueryRepo(dataData, adapter_driving_repos_users_relationsRepo, adapter_driving_repos_users_infoQueryRepo, adapter_driving_repos_users_booRepo, storyContextRepo, storyReactionRepo, storyTopRepo, storyStatRepo)
	portalRepo := adapter_driving_repos_items_story.NewPortalCmdRepo(dataData, uniqIdGenerator, storyQueryRepo, adapter_driving_repos_users_infoQueryRepo, adapter_driving_repos_users_booRepo, dataData, userPortalRelationRepo, logger)
	storyCmdRepo := adapter_driving_repos_items_story.NewStoryCmdRepo(dataData, storyTemplateRepo, storyTopRepo, storyStatRepo, portalRepo, storyQueryRepo)
	openAIClient := adapter_driving_services_openai.NewOpenAIClient(adapter_driving_services_cdnService, logger)
	client := adapter_driving_services_openrouter.NewClient(conf2, logger)
	llmService := domain_services_ai.NewLlmService(conf2, logger, openAIClient, openAIClient, repo, client, dataData, adapter_driving_services_cdnService)
	pollySynthesizer := adapter_driving_services_polly.NewPollySynthesizer(conf2)
	ttsService := domain_services_ai.NewTtsService(pollySynthesizer, adapter_driving_services_cdnService, conf2, logger)
	adapter_driving_services_recommendClient := adapter_driving_services_recommend.NewClient(conf2)
	cmdRepo := adapter_driving_repos_users_info.NewCmdRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	imUuidRepo := adapter_driving_repos_users_im.NewImUuidRepo(dataData)
	driving_services_agoraClient := driving_services_agora.NewClient(conf2, imUuidRepo, logger, adapter_driving_repos_users_infoQueryRepo)
	usersInfoService := domain_services_users_info.NewUsersInfoService(adapter_driving_repos_users_infoQueryRepo, cmdRepo, driving_services_agoraClient)
	storyHauntBooShowInfoRepo := adapter_driving_repos_items_story.NewStoryHauntBooShowInfoRepo(dataData, storyQueryRepo)
	hauntBooShowInfoService := domain_services_items_story.NewHauntBooShowInfoService(storyHauntBooShowInfoRepo, dataData, dataData)
	storyQueryService := domain_services_items_story.NewStoryQueryService(storyQueryRepo, storyTemplateRepo, adapter_driving_services_recommendClient, usersInfoService, repo, hauntBooShowInfoService)
	imService := domain_services_im.NewIMService(driving_services_agoraClient)
	asrService := domain_services_ai.NewAsrService(openAIClient, conf2)
	samClient := adapter_driving_services_sam.NewSamClient(logger, service)
	hideStickersRepo := adapter_driving_repos_items_story.NewHideStickersRepo(dataData)
	hideStickerService := domain_services_items_story.NewHideStickerService(hideStickersRepo)
	storyPlayService := domain_services_items_story.NewStoryPlayService(storyContextRepo, llmService, ttsService, storyQueryService, dataData, imService, asrService, usersInfoService, repo, adapter_driving_services_cdnService, samClient, hideStickerService, storyQueryRepo, adapter_driving_repos_users_booRepo, hauntBooShowInfoService, dataData, uniqIdGenerator)
	klingClient := adapter_driving_services_kling.NewKlingClient(logger, conf2)
	booAIService := domain_services_ai.NewBooAIService(conf2, logger, klingClient, adapter_driving_services_cdnService, openAIClient, openAIClient)
	storyCmdService := domain_services_items_story.NewStoryCmdService(uniqIdGenerator, storyCmdRepo, storyPlayService, storyQueryService, logger, dataData, hideStickerService, ttsService, repo, llmService, storyQueryRepo, adapter_driving_repos_users_booRepo, booAIService)
	storyReactionService := domain_services_items_story.NewStoryReactionService(usersInfoService, storyQueryService, storyReactionRepo)
	portalService := domain_services_items_portal.NewPortalService(uniqIdGenerator, portalRepo, storyQueryService, usersInfoService, adapter_driving_services_recommendClient, hauntBooShowInfoService, imService)
	itemStoryUsecase := usecases_items.NewItemStoryUsecase(storyCmdService, storyPlayService, storyQueryService, storyReactionService, hideStickerService, portalService, hauntBooShowInfoService)
	scriptApp := types.NewScriptApp(logger, dataData, conf2, adapter_driving_services_cdnService, itemStoryUsecase, ttsService, repo, uniqIdGenerator)
	return scriptApp, func() {
		cleanup()
	}, nil
}
