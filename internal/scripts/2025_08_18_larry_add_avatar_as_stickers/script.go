package _2025_08_18_larry_add_avatar_as_stickers

import (
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"boson/internal/scripts/types"
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

func Run(ctx context.Context, app *types.ScriptApp) error {
	db := app.Dat.MasterDB(ctx)
	avatarCmd := dal.Use(db).UserCreatedBooAvatar
	stickerCmd := dal.Use(db).HideSticker

	avatars, err := avatarCmd.WithContext(ctx).Find()
	if err != nil {
		return err
	}

	type StickerWithUser struct {
		Sticker *domain_entities_items.HideSticker
		UserId  int64
	}
	var stickers []*StickerWithUser
	for _, avatar := range avatars {
		avatarObjectKey := domain_entities_resource.ImageResourcePath(avatar.AvatarObjectKey)
		resource := domain_entities_resource.NewResourceBuilder().WithImageObjectKey(avatarObjectKey, 0, 0).Build()
		stickers = append(stickers, &StickerWithUser{
			Sticker: &domain_entities_items.HideSticker{
				Id:           fmt.Sprintf("%d", app.UniqIdGenerator.Generate()),
				FromAvatarId: avatar.ID,
				Resource:     resource,
			},
			UserId: avatar.UserID,
		})
	}

	var ms []*model.HideSticker
	for _, sticker := range stickers {
		jsonInfo, err := json.Marshal(sticker.Sticker)
		if err != nil {
			return errors.Wrapf(err, "sticker: %v", sticker)
		}
		ms = append(ms, &model.HideSticker{
			ID:           cast.ToInt64(sticker.Sticker.Id),
			FromStoryID:  sticker.Sticker.FromStoryId,
			FromAvatarID: sticker.Sticker.FromAvatarId,
			CreatorID:    sticker.UserId,
			ExtraInfo:    string(jsonInfo),
		})
	}
	if err := stickerCmd.WithContext(ctx).Create(
		ms...,
	); err != nil {
		return errors.Wrapf(err, "stickers: %v", stickers)
	}
	return nil
}
