package adapter_driven_http_services_search

import (
	api_search_v1 "boson/api/search/v1"
	usecases_search "boson/internal/usecases/search"
	"boson/pkg/auth"
	"context"
)

type SearchService struct {
	api_search_v1.UnimplementedSearchServiceServer
	searchUsecase *usecases_search.SearchUsecase
	*auth.Jwt
}

func NewSearchService(
	searchUsecase *usecases_search.SearchUsecase,
	jwt *auth.Jwt,
) *SearchService {
	return &SearchService{
		searchUsecase: searchUsecase,
		Jwt:           jwt,
	}
}

func (s *SearchService) Search(ctx context.Context, req *api_search_v1.SearchRequest) (*api_search_v1.SearchResponse, error) {
	user := s.Jwt.GetAuthUser(ctx)
	searchType := api_search_v1.SearchType_value[req.SearchType]
	return s.searchUsecase.Search(ctx, user.Id, req.Keyword, api_search_v1.SearchType(searchType), req.ListRequest)
}

func (s *SearchService) GetSearchHistory(ctx context.Context, req *api_search_v1.GetSearchHistoryRequest) (*api_search_v1.GetSearchHistoryResponse, error) {
	user := s.Jwt.GetAuthUser(ctx)

	histories, err := s.searchUsecase.GetSearchHistory(ctx, user.Id, req.Count)
	if err != nil {
		return nil, err
	}

	return &api_search_v1.GetSearchHistoryResponse{
		SearchHistory: histories,
	}, nil
}

func (s *SearchService) DeleteSearchHistory(ctx context.Context, req *api_search_v1.DeleteSearchHistoryRequest) (*api_search_v1.DeleteSearchHistoryResponse, error) {
	user := s.Jwt.GetAuthUser(ctx)

	err := s.searchUsecase.DeleteSearchHistory(ctx, user.Id, req.SearchIds)
	if err != nil {
		return nil, err
	}

	return &api_search_v1.DeleteSearchHistoryResponse{
		Success: true,
	}, nil
}

func (s *SearchService) UploadSearchHistory(ctx context.Context, req *api_search_v1.UploadSearchHistoryRequest) (*api_search_v1.UploadSearchHistoryResponse, error) {
	user := s.Jwt.GetAuthUser(ctx)

	searchIDs, err := s.searchUsecase.UploadSearchHistory(ctx, user.Id, req.SearchItems)
	if err != nil {
		return nil, err
	}

	return &api_search_v1.UploadSearchHistoryResponse{
		SearchIds: searchIDs,
	}, nil
}
