package adapter_driven_http_services_items

import (
	"context"
	"slices"

	api_errors_v1 "boson/api/errors/v1"
	api_items_portal_types_v1 "boson/api/items/portal/types/v1"
	v1 "boson/api/items/portal/types/v1"
	api_items_portal_v1 "boson/api/items/portal/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/emptypb"
)

func (s *ItemsService) CreateMoment(ctx context.Context, req *api_items_portal_v1.CreateMomentRequest) (*api_items_portal_v1.CreateMomentResponse, error) {
	user := s.GetAuthUser(ctx)
	resourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.Resource)
	if err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid resource"))
	}
	moment, err := s.itemStoryUsecase.AppendMoment(ctx, user.Id, cast.ToInt64(req.StoryId), resourceEntity, req.AttachmentTexts)
	if err != nil {
		return nil, err
	}
	if req.GetFromStoryId() != "" {
		err = s.updateFromStoryPostMomentStatus(ctx, user.Id, cast.ToInt64(req.StoryId), cast.ToInt64(req.GetFromStoryId()))
		if err != nil {
			return nil, err
		}
	}
	return &api_items_portal_v1.CreateMomentResponse{
		Moment: adapter_driven_assembler.ConvertMomentToDto(moment),
	}, nil
}
func (s *ItemsService) CreateMomentRelation(ctx context.Context, req *api_items_portal_v1.CreateMomentRelationRequest) (*api_items_portal_v1.CreateMomentRelationResponse, error) {
	user := s.GetAuthUser(ctx)
	moment, err := s.itemStoryUsecase.CreateMomentRelation(
		ctx,
		user.Id,
		cast.ToInt64(req.MomentId),
		req.RelationType,
	)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.CreateMomentRelationResponse{
		Moment: adapter_driven_assembler.ConvertMomentToDto(moment),
	}, nil
}
func (s *ItemsService) DeleteMoment(ctx context.Context, req *api_items_portal_v1.DeleteMomentRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	err := s.itemStoryUsecase.DeleteMoment(ctx, user.Id, cast.ToInt64(req.MomentId))
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
func (s *ItemsService) GetUserCreatedPortalsInfo(ctx context.Context, req *api_items_portal_v1.GetUserCreatedPortalsInfoRequest) (*api_items_portal_v1.GetUserCreatedPortalsInfoResponse, error) {
	user := s.GetAuthUser(ctx)
	info, err := s.itemStoryUsecase.GetUserCreatedPortalsInfo(ctx, user.Id)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.GetUserCreatedPortalsInfoResponse{
		UserCreatedPortals: &v1.UserCreatedPortals{
			Portals: lo.Map(info.Portals, func(portal *domain_entities_items.Portal, _ int) *v1.Portal {
				return adapter_driven_assembler.ConvertPortalToDto(portal)
			}),
			UserHasPostedStories: info.HasPostedStories,
			UserLastReadPortalId: lo.IfF(info.LastReadPortalId != nil, func() *string {
				return lo.ToPtr(cast.ToString(info.LastReadPortalId))
			}).Else(nil),
		},
	}, nil
}
func (s *ItemsService) ListUserCreatedPortalsWithTimeRange(ctx context.Context, req *api_items_portal_v1.ListUserCreatedPortalsWithTimeRangeRequest) (*api_items_portal_v1.ListUserCreatedPortalsWithTimeRangeResponse, error) {
	user := s.GetAuthUser(ctx)
	reqUserId := user.Id
	if req.UserId != nil && *req.UserId != "" {
		reqUserId = cast.ToInt64(req.UserId)
	}
	portals, res, err := s.itemStoryUsecase.ListMyCreatedPortalsWithTimeRange(ctx, user.Id, reqUserId, req.ListReq)
	if err != nil {
		return nil, err
	}
	portalWithDates := []*api_items_portal_v1.ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate{}
	for date, portals := range portals {
		portalWithDates = append(portalWithDates, &api_items_portal_v1.ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate{
			DateZero: uint32(date.Unix()),
			Portals: lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) *v1.Portal {
				return adapter_driven_assembler.ConvertPortalToDto(portal)
			}),
		})
	}
	// 按照时间倒序
	slices.SortFunc(portalWithDates, func(a, b *api_items_portal_v1.ListUserCreatedPortalsWithTimeRangeResponse_PortalsWithDate) int {
		return int(b.DateZero - a.DateZero)
	})
	return &api_items_portal_v1.ListUserCreatedPortalsWithTimeRangeResponse{
		PortalsWithDates: portalWithDates,
		ListResponse:     res,
	}, nil
}
func (s *ItemsService) RemoveMomentRelation(ctx context.Context, req *api_items_portal_v1.RemoveMomentRelationRequest) (*api_items_portal_v1.RemoveMomentRelationResponse, error) {
	user := s.GetAuthUser(ctx)
	moment, err := s.itemStoryUsecase.RemoveMomentRelation(
		ctx,
		user.Id,
		cast.ToInt64(req.MomentId),
		req.RelationType,
	)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.RemoveMomentRelationResponse{
		Moment: adapter_driven_assembler.ConvertMomentToDto(moment),
	}, nil
}

func (s *ItemsService) GetPortal(ctx context.Context, req *api_items_portal_v1.GetPortalRequest) (*api_items_portal_v1.GetPortalResponse, error) {
	user := s.GetAuthUser(ctx)
	portals, err := s.itemStoryUsecase.BatchGetPortals(ctx, user.Id, []int64{cast.ToInt64(req.GetPortalId())})
	if err != nil {
		return nil, err
	}
	portal, ok := portals[cast.ToInt64(req.GetPortalId())]
	if !ok {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("portal : %s not found", req.GetPortalId()))
	}
	return &api_items_portal_v1.GetPortalResponse{
		Portal: adapter_driven_assembler.ConvertPortalToDto(portal),
	}, nil
}

func (s *ItemsService) GetMoment(ctx context.Context, req *api_items_portal_v1.GetMomentRequest) (*api_items_portal_v1.GetMomentResponse, error) {
	user := s.GetAuthUser(ctx)
	momentId := cast.ToInt64(req.MomentId)
	moments, err := s.itemStoryUsecase.BatchGetMoments(ctx, user.Id, []int64{momentId})
	if err != nil {
		return nil, err
	}
	moment, ok := moments[momentId]
	if !ok {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("moment : %s not found", req.GetMomentId()))
	}
	return &api_items_portal_v1.GetMomentResponse{
		Moment: adapter_driven_assembler.ConvertMomentToDto(moment),
	}, nil
}

func (s *ItemsService) SendMomentInvite(ctx context.Context, req *api_items_portal_v1.SendMomentInviteRequest) (*api_items_portal_v1.SendMomentInviteResponse, error) {
	user := s.GetAuthUser(ctx)
	momentId := cast.ToInt64(req.MomentId)
	storyId := cast.ToInt64(req.StoryId)
	err := s.itemStoryUsecase.SendMomentInvite(ctx, user.Id, req.Type, storyId, momentId, lo.Map(req.ReceiverIds, func(id string, _ int) int64 { return cast.ToInt64(id) }))
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.SendMomentInviteResponse{}, nil
}

func (s *ItemsService) ListMomentViewers(ctx context.Context, req *api_items_portal_v1.ListMomentViewersRequest) (*api_items_portal_v1.ListMomentViewersResponse, error) {
	user := s.GetAuthUser(ctx)
	viewers, res, err := s.itemStoryUsecase.ListMomentViewers(ctx, user.Id, cast.ToInt64(req.GetMomentId()), req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.ListMomentViewersResponse{
		Viewers:      viewers,
		ListResponse: res,
	}, nil
}

func (s *ItemsService) ReportRead(ctx context.Context, req *api_items_portal_v1.ReportReadRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	var err error
	if req.GetMomentId() != "" {
		err = s.itemStoryUsecase.ReportPortalMomentRead(ctx, user.Id, cast.ToInt64(req.GetPortalId()), cast.ToInt64(req.GetMomentId()))
	}
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *ItemsService) ListMyPortals(ctx context.Context, req *api_items_portal_v1.ListMyPortalsRequest) (*api_items_portal_v1.ListMyPortalsResponse, error) {
	user := s.GetAuthUser(ctx)
	myportals, createdPortals, res, err := s.itemStoryUsecase.ListMyPortals(ctx, user.Id, req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.ListMyPortalsResponse{
		UserCreatedPortals: &api_items_portal_types_v1.UserCreatedPortals{
			Portals: lo.Map(createdPortals.Portals, func(portal *domain_entities_items.Portal, _ int) *v1.Portal {
				return adapter_driven_assembler.ConvertPortalToDto(portal)
			}),
			UserHasPostedStories: createdPortals.HasPostedStories,
			UserLastReadPortalId: lo.IfF(createdPortals.LastReadPortalId != nil, func() *string {
				return lo.ToPtr(cast.ToString(createdPortals.LastReadPortalId))
			}).Else(nil),
		},
		Portals: lo.Map(myportals, func(portal *domain_entities_items.Portal, _ int) *v1.Portal {
			return adapter_driven_assembler.ConvertPortalToDto(portal)
		}),
		ListResponse: res,
	}, nil
}
func (s *ItemsService) ListCouldAppendMomentStories(ctx context.Context, req *api_items_portal_v1.ListCouldAppendMomentStoriesRequest) (*api_items_portal_v1.ListCouldAppendMomentStoriesResponse, error) {
	user := s.GetAuthUser(ctx)
	portals, err := s.itemStoryUsecase.ListCouldAppendMomentPortals(ctx, user.Id)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.ListCouldAppendMomentStoriesResponse{
		Stories: lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) *api_items_story_types_v1.StoryDetail {
			return adapter_driven_assembler.ConvertStoryDetailToDto(portal.Story)
		}),
	}, nil
}

func (s *ItemsService) ListTrendingPortals(ctx context.Context, req *api_items_portal_v1.ListTrendingPortalsRequest) (*api_items_portal_v1.ListTrendingPortalsResponse, error) {
	user := s.GetAuthUser(ctx)
	portals, res, err := s.itemStoryUsecase.ListTrendingPortals(ctx, user.Id, req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_portal_v1.ListTrendingPortalsResponse{
		Portals: lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) *v1.Portal {
			return adapter_driven_assembler.ConvertPortalToDto(portal)
		}),
		ListResponse: res,
	}, nil
}
