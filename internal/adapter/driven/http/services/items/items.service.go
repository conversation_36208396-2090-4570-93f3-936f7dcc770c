package adapter_driven_http_services_items

import (
	"context"

	api_common_v1 "boson/api/common/v1"
	api_items_portal_v1 "boson/api/items/portal/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_items_story_v1 "boson/api/items/story/v1"
	api_items_story_v2 "boson/api/items/story/v2"
	api_items_types_v1 "boson/api/items/types/v1"
	api_items_v1 "boson/api/items/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_interfaces "boson/internal/domain/services/items/interfaces"
	domain_services_items_story "boson/internal/domain/services/items/story"
	usecases_items "boson/internal/usecases/items"
	usecases_users "boson/internal/usecases/users"
	"boson/pkg/auth"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type ItemsService struct {
	api_items_v1.UnimplementedItemsServer
	*auth.Jwt
	itemCmdUsecase         *usecases_items.ItemCmdUsecase
	itemQueryUsecase       *usecases_items.ItemQueryUsecase
	itemAiUsecase          *usecases_items.ItemAiUsecase
	itemMusicSearchUsecase *usecases_items.ItemMusicSearchUsecase
	itemCommentUsecase     *usecases_items.ItemCommentsUsecase
	itemStoryUsecase       *usecases_items.ItemStoryUsecase
	infoUsecase            *usecases_users.UserInfoUsecase
}

func NewItemsService(
	itemCmdUsecase *usecases_items.ItemCmdUsecase,
	itemQueryUsecase *usecases_items.ItemQueryUsecase,
	itemAiUsecase *usecases_items.ItemAiUsecase,
	itemMusicSearchUsecase *usecases_items.ItemMusicSearchUsecase,
	itemCommentUsecase *usecases_items.ItemCommentsUsecase,
	itemStoryUsecase *usecases_items.ItemStoryUsecase,
	infoUsecase *usecases_users.UserInfoUsecase,
	jwt *auth.Jwt,
) *ItemsService {
	return &ItemsService{
		itemCmdUsecase:         itemCmdUsecase,
		itemQueryUsecase:       itemQueryUsecase,
		itemAiUsecase:          itemAiUsecase,
		Jwt:                    jwt,
		itemMusicSearchUsecase: itemMusicSearchUsecase,
		itemCommentUsecase:     itemCommentUsecase,
		itemStoryUsecase:       itemStoryUsecase,
		infoUsecase:            infoUsecase,
	}
}

func (s *ItemsService) ListSameAuthorStoryWithAnchor(ctx context.Context, req *api_items_story_v1.ListSameAuthorStoryWithAnchorRequest) (*api_items_story_v1.ListSameAuthorStoryWithAnchorResponse, error) {
	user := s.GetAuthUser(ctx)
	var attrs []*domain_services_items_story.ListSameAuthorStoryWithAnchorAttr
	for _, listRequest := range req.ListRequests {
		attrs = append(attrs, &domain_services_items_story.ListSameAuthorStoryWithAnchorAttr{
			IdDesc:          listRequest.Reverse,
			Limit:           listRequest.Limit,
			FilterPlayTypes: listRequest.FilterPlayTypes,
		})
	}
	stories, err := s.itemStoryUsecase.ListSameAuthorStoryWithAnchor(ctx, user.Id, cast.ToInt64(req.AnchorStoryId), attrs)
	if err != nil {
		return nil, err
	}
	res := &api_items_story_v1.ListSameAuthorStoryWithAnchorResponse{}
	for _, story := range stories {
		res.ListResponses = append(res.ListResponses, &api_items_story_v1.ListSameAuthorStoryWithAnchorResponse_ListResponse{
			Reverse: story.IdDesc,
			Stories: lo.Map(story.Details, func(item *domain_entities_items.StoryDetail, _ int) *api_items_story_types_v1.StoryDetail {
				return adapter_driven_assembler.ConvertStoryDetailToDto(item)
			}),
		})
	}
	return res, nil
}
func (s *ItemsService) ListFollowingCreatorStoryV2(ctx context.Context, req *api_items_story_v2.ListFollowingCreatorStoryRequestV2) (*api_items_story_v2.ListFollowingCreatorStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	stories, recommendedStories, listResp, hauntBooShowInfo, err := s.itemStoryUsecase.ListFollowingCreatorStory(ctx, user.Id, req.ListRequest)
	if err != nil {
		return nil, err
	}
	myPortalsRes, err := s.ListMyPortals(ctx, &api_items_portal_v1.ListMyPortalsRequest{
		ListRequest: &api_common_v1.ListRequest{
			PageToken: "", // 第一页
			PageSize:  "20",
		},
	})
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ListFollowingCreatorStoryResponseV2{
		FollowingCreatorStories: lo.Map(stories, func(item *domain_entities_items.StoryDetail, _ int) *api_items_story_types_v1.StoryDetail {
			return adapter_driven_assembler.ConvertStoryDetailToDto(item)
		}),
		ListResponse: listResp,
		RecommendedUnfollowedCreatorStories: lo.Map(recommendedStories, func(item *domain_entities_items.StoryDetail, _ int) *api_items_story_types_v1.StoryDetail {
			return adapter_driven_assembler.ConvertStoryDetailToDto(item)
		}),
		UserCreatedPortals: myPortalsRes.UserCreatedPortals,
		Portals:            myPortalsRes.Portals,
		PortalListResponse: myPortalsRes.ListResponse,
		HauntBooShowInfos:  []*api_items_story_types_v1.HauntBooShowInfo{adapter_driven_assembler.ConvertHauntBooShowInfoToDto(hauntBooShowInfo)},
	}, nil
}
func (s *ItemsService) Asr(ctx context.Context, req *api_items_v1.AsrRequest) (*api_items_v1.AsrResponse, error) {
	txt, matched, err := s.itemAiUsecase.Transcribe(ctx, req.AudioObjectKey, req.Similarity, req.ExpectedTxt)
	if err != nil {
		return nil, err
	}
	return &api_items_v1.AsrResponse{Text: txt, Matched: matched}, nil
}

func (s *ItemsService) HomePageTimeline(ctx context.Context, req *api_items_v1.HomePageTimelineRequest) (*api_items_v1.HomePageTimelineResponse, error) {
	user := s.GetAuthUser(ctx)
	items, listResp, err := s.itemQueryUsecase.ListTimeLine(ctx, user.Id, req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_v1.HomePageTimelineResponse{TimelineCards: lo.Map(items, func(item *domain_entities_items.Summary, _ int) *api_items_v1.HomePageTimelineResponse_TimelineCard {
		return &api_items_v1.HomePageTimelineResponse_TimelineCard{
			Item: adapter_driven_assembler.ConvertItemToDto(item),
			Tag:  api_items_v1.HomePageTimelineResponse_CARD_TAG_FRIEND,
		}
	}), ListResponse: listResp}, nil
}
func (s *ItemsService) CreateItem(ctx context.Context, req *api_items_v1.CreateItemRequest) (*api_items_v1.CreateItemResponse, error) {
	user := s.GetAuthUser(ctx)
	createAttr := domain_services_items_interfaces.CreateAttr{
		Title:               req.Title,
		Description:         req.Description,
		CoverImageObjectKey: domain_entities_resource.ImageResourcePath(req.CoverImageObjectKey),
		Sketchboards:        req.Sketchboards,
	}
	if req.Bgm != nil {
		createAttr.Bgm = &domain_entities_items.Bgm{
			Music: domain_entities_items.MusicSong{
				Id: cast.ToInt64(req.Bgm.Song.Id),
			},
			Audio: domain_entities_resource.AudioResourcePath(req.Bgm.AudioPlayKey),
		}
	}
	item, err := s.itemCmdUsecase.CreateItem(ctx, user.Id, createAttr)
	if err != nil {
		return nil, err
	}
	return &api_items_v1.CreateItemResponse{Item: &api_items_types_v1.ItemDetail{
		ItemSummary: adapter_driven_assembler.ConvertItemToDto(item),
	}}, nil
}

func (s *ItemsService) BatchGetItemSummaries(ctx context.Context, req *api_items_v1.BatchGetItemSummariesRequest) (*api_items_v1.BatchGetItemSummariesResponse, error) {
	user := s.GetAuthUser(ctx)
	ids := lo.Map(req.ItemIds, func(itemId string, _ int) int64 {
		return cast.ToInt64(itemId)
	})
	items, err := s.itemQueryUsecase.BatchGetItemSummaries(ctx, user.Id, ids...)
	if err != nil {
		return nil, err
	}
	return &api_items_v1.BatchGetItemSummariesResponse{Items: adapter_driven_assembler.ConverItemsToDtos(lo.Values(items))}, nil
}
