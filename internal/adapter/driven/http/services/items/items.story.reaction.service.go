package adapter_driven_http_services_items

import (
	"context"

	api_items_story_activity_types_v1 "boson/api/items/story/activity/types/v1"
	api_items_story_activity_v1 "boson/api/items/story/activity/v1"
	api_items_story_reaction_v1 "boson/api/items/story/reaction/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_users "boson/internal/domain/entities/users"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/emptypb"
)

func (s *ItemsService) CreateStoryReaction(ctx context.Context, req *api_items_story_reaction_v1.CreateReactionRequest) (*api_items_story_reaction_v1.CreateReactionResponse, error) {
	user := s.GetAuthUser(ctx)
	storyId := cast.ToInt64(req.GetStoryId())
	storyAfterReaction, err := s.itemStoryUsecase.CreateReaction(ctx, user.Id, storyId, req.Emoji, "")
	if err != nil {
		return nil, err
	}
	return &api_items_story_reaction_v1.CreateReactionResponse{
		Story: adapter_driven_assembler.ConvertStoryDetailToDto(storyAfterReaction),
	}, nil
}
func (s *ItemsService) DeleteStoryReaction(ctx context.Context, req *api_items_story_reaction_v1.DeleteReactionRequest) (*api_items_story_reaction_v1.DeleteReactionResponse, error) {
	user := s.GetAuthUser(ctx)
	storyId := cast.ToInt64(req.GetStoryId())
	storyAfterReaction, err := s.itemStoryUsecase.DeleteReaction(ctx, user.Id, storyId, req.Emoji)
	if err != nil {
		return nil, err
	}
	return &api_items_story_reaction_v1.DeleteReactionResponse{
		Story: adapter_driven_assembler.ConvertStoryDetailToDto(storyAfterReaction),
	}, nil
}
func (s *ItemsService) ListStoryReactionMadeUsers(ctx context.Context, req *api_items_story_reaction_v1.ListReactionMadeUsersRequest) (*api_items_story_reaction_v1.ListReactionMadeUsersResponse, error) {
	user := s.GetAuthUser(ctx)
	storyId := cast.ToInt64(req.GetStoryId())
	reactionMadeUsers, res, err := s.itemStoryUsecase.ListReactionMadeUsers(ctx, user.Id, storyId, req.Emojis, req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_story_reaction_v1.ListReactionMadeUsersResponse{
		Users:        adapter_driven_assembler.BatchConverUserInfosToSummaries(reactionMadeUsers),
		ListResponse: res,
	}, nil
}

func (s *ItemsService) GetActivityUnreadCount(ctx context.Context, _ *api_items_story_activity_v1.GetActivityUnreadCountRequest) (*api_items_story_activity_v1.GetActivityUnreadCountResponse, error) {
	user := s.GetAuthUser(ctx)
	count, err := s.itemStoryUsecase.GetActivityUnreadCount(ctx, user.Id)
	if err != nil {
		return nil, err
	}
	return &api_items_story_activity_v1.GetActivityUnreadCountResponse{Count: uint32(count)}, nil
}

func (s *ItemsService) ReportActivityRead(ctx context.Context, _ *api_items_story_activity_v1.ReportActivityReadRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	if err := s.itemStoryUsecase.MarkActivitiesAsRead(ctx, user.Id); err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *ItemsService) ListActivities(ctx context.Context, req *api_items_story_activity_v1.ListActivitiesRequest) (*api_items_story_activity_v1.ListActivitiesResponse, error) {
	user := s.GetAuthUser(ctx)
	activities, listResp, err := s.itemStoryUsecase.ListActivities(ctx, user.Id, req.GetListRequest())
	if err != nil {
		return nil, err
	}
	return &api_items_story_activity_v1.ListActivitiesResponse{
		Activities:   s.convertActivityItems(ctx, user.Id, activities),
		ListResponse: listResp,
	}, nil
}

func (s *ItemsService) convertActivityItems(ctx context.Context, userId int64, activities []*domain_entities_items.ActivityItem) []*api_items_story_activity_types_v1.ActivityItem {
	if len(activities) == 0 {
		return []*api_items_story_activity_types_v1.ActivityItem{}
	}

	actorIDs := lo.Uniq(lo.FilterMap(activities, func(a *domain_entities_items.ActivityItem, _ int) (int64, bool) {
		if a == nil || a.ActorUserID == 0 {
			return 0, false
		}
		return a.ActorUserID, true
	}))
	storyIDs := lo.Uniq(lo.FilterMap(activities, func(a *domain_entities_items.ActivityItem, _ int) (int64, bool) {
		if a == nil || a.StoryID == 0 {
			return 0, false
		}
		return a.StoryID, true
	}))

	userSummaries := map[int64]*domain_entities_users.UserSummaryEntity{}
	if len(actorIDs) > 0 {
		if m, err := s.infoUsecase.BatchGetUserInfo(ctx, userId, actorIDs...); err == nil {
			userSummaries = m
		}
	}
	storyDetails := map[int64]*domain_entities_items.StoryDetail{}
	if len(storyIDs) > 0 {
		if m, err := s.itemStoryUsecase.BatchGetStoryDetails(ctx, userId, storyIDs); err == nil {
			storyDetails = m
		}
	}

	result := lo.FilterMap(activities, func(a *domain_entities_items.ActivityItem, _ int) (*api_items_story_activity_types_v1.ActivityItem, bool) {
		if a == nil {
			return nil, false
		}
		actorSummary, ok := userSummaries[a.ActorUserID]
		if !ok || actorSummary == nil {
			return nil, false
		}
		storyDetail, ok := storyDetails[a.StoryID]
		if !ok || storyDetail == nil {
			return nil, false
		}

		item := &api_items_story_activity_types_v1.ActivityItem{
			Id:                 a.ID,
			Actor:              adapter_driven_assembler.ConvertUserInfoToSummary(actorSummary),
			Story:              adapter_driven_assembler.ConvertStoryDetailToDto(storyDetail),
			Type:               api_items_story_activity_types_v1.ActivityType(api_items_story_activity_types_v1.ActivityType_value[a.ActivityType]),
			CreatedAtTimestamp: a.CreatedAtTimestamp,
		}
		switch a.ActivityType {
		case domain_entities_items.ActivityType_STORY_LIKE:
			item.Detail = &api_items_story_activity_types_v1.ActivityItem_StoryLike{
				StoryLike: &api_items_story_activity_types_v1.StoryLikeActivity{Emoji: a.Emoji},
			}
		}
		return item, true
	})

	return result
}
