package adapter_driven_http_services_items

import (
	"context"
	"strconv"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_items_story_v2 "boson/api/items/story/v2"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_story "boson/internal/domain/services/items/story"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// 自动生成 area emoji
func (s *ItemsService) AutoGenerateAreaEmoji(ctx context.Context, in *api_items_story_v2.AutoGenerateAreaEmojiRequest) (*api_items_story_v2.AutoGenerateAreaEmojiResponse, error) {
	user := s.GetAuthUser(ctx)
	result, err := s.itemStoryUsecase.PinAutoGenerateEmoji(ctx, user.Id, domain_entities_resource.ImageResourcePath(in.GetBackgroundImage().ObjectKey))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.AutoGenerateAreaEmojiResponse{
		PinEmojiResources: lo.Map(result, func(item domain_services_items_story.EmojiWithArea, _ int) *api_items_story_types_v1.PinEmojiResource {
			r := &api_items_story_types_v1.PinEmojiResource{
				Data: &api_items_story_types_v1.PinEmojiResource_DefaultEmoji{
					DefaultEmoji: item.Emoji,
				},
			}
			r.Area = &api_items_story_types_v1.PinEmojiArear{
				Width:  strconv.FormatFloat(item.Area.Width, 'f', -1, 64),
				Height: strconv.FormatFloat(item.Area.Height, 'f', -1, 64),
				X:      strconv.FormatFloat(item.Area.X, 'f', -1, 64),
				Y:      strconv.FormatFloat(item.Area.Y, 'f', -1, 64),
			}
			return r
		}),
	}, nil
}

// 手动生成 area emoji
func (s *ItemsService) ManualGenerateAreaEmoji(ctx context.Context, in *api_items_story_v2.ManualGenerateAreaEmojiRequest) (*api_items_story_v2.ManualGenerateAreaEmojiResponse, error) {
	user := s.GetAuthUser(ctx)
	result, err := s.itemStoryUsecase.PinManualGenerateEmoji(ctx, user.Id, domain_entities_resource.ImageResourcePath(in.GetBackgroundImage().ObjectKey), lo.Map(in.GetAreas(), func(item *api_items_story_types_v1.PinEmojiArear, _ int) domain_entities_items.Area {
		return domain_entities_items.Area{
			X:      cast.ToFloat64(item.X),
			Y:      cast.ToFloat64(item.Y),
			Width:  cast.ToFloat64(item.Width),
			Height: cast.ToFloat64(item.Height),
		}
	}))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ManualGenerateAreaEmojiResponse{
		PinEmojiResources: lo.Map(result, func(item domain_services_items_story.ImageWithArea, _ int) *api_items_story_types_v1.PinEmojiResource {
			emojResource := domain_entities_resource.NewResourceBuilder().WithImageObjectKey(item.Image, 1, 1).Build()
			return &api_items_story_types_v1.PinEmojiResource{
				Data: &api_items_story_types_v1.PinEmojiResource_GeneratedEmojiResource{
					GeneratedEmojiResource: adapter_driven_assembler.ConvertResourceEntityToDto(emojResource),
				},
				Area: &api_items_story_types_v1.PinEmojiArear{
					Width:  strconv.FormatFloat(item.Areas.Width, 'f', -1, 64),
					Height: strconv.FormatFloat(item.Areas.Height, 'f', -1, 64),
					X:      strconv.FormatFloat(item.Areas.X, 'f', -1, 64),
					Y:      strconv.FormatFloat(item.Areas.Y, 'f', -1, 64),
				},
			}
		}),
	}, nil
}
