package adapter_driven_http_services_items

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_items_portal_v1 "boson/api/items/portal/v1"
	api_items_story_activity_v1 "boson/api/items/story/activity/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_items_story_v1 "boson/api/items/story/v1"
	api_items_story_v2 "boson/api/items/story/v2"
	api_items_v1 "boson/api/items/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_boo "boson/internal/domain/entities/boo"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_story "boson/internal/domain/services/items/story"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/emptypb"
)

func (s *ItemsService) UpdateStory(ctx context.Context, req *api_items_story_v1.UpdateStoryRequest) (*api_items_story_v1.UpdateStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	updateAttr := domain_services_items_story.UpdateStoryAttr{}
	if req.PrivacySetting != nil {
		updateAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.UpdateStory(ctx, user.Id, cast.ToInt64(req.StoryId), updateAttr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.UpdateStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) TopStory(ctx context.Context, req *api_items_story_v1.TopStoryRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	err := s.itemStoryUsecase.TopStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.IsTop)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *ItemsService) ListCreatorStory(ctx context.Context, req *api_items_story_v1.ListCreatorStoryRequest) (*api_items_story_v1.ListCreatorStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	if req.CreatorId == "" {
		req.CreatorId = fmt.Sprintf("%d", user.Id)
	}
	stories, listResp, err := s.itemStoryUsecase.ListUserCreatedStory(ctx, user.Id, cast.ToInt64(req.CreatorId), req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ListCreatorStoryResponse{
		CreatedStories: lo.Map(stories, func(item *domain_services_items_story.CreatedStory, _ int) *api_items_story_v1.ListCreatorStoryResponse_CreatedStory {
			return &api_items_story_v1.ListCreatorStoryResponse_CreatedStory{
				Story: adapter_driven_assembler.ConvertStoryToDto(item.Story)[0],
				IsTop: item.IsTop,
			}
		}),
		ListResponse: listResp,
	}, nil
}

func (s *ItemsService) ListCreatorStoryV2(ctx context.Context, req *api_items_story_v2.ListCreatorStoryRequestV2) (*api_items_story_v2.ListCreatorStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	if req.CreatorId == "" {
		req.CreatorId = fmt.Sprintf("%d", user.Id)
	}
	stories, listResp, err := s.itemStoryUsecase.ListUserCreatedStoryV2(ctx, user.Id, cast.ToInt64(req.CreatorId), req.ListRequest)
	if err != nil {
		return nil, err
	}

	return &api_items_story_v2.ListCreatorStoryResponseV2{
		CreatedStories: lo.Map(stories, func(item *domain_services_items_story.CreatedStoryV2, _ int) *api_items_story_v2.ListCreatorStoryResponseV2_CreatedStory {
			return &api_items_story_v2.ListCreatorStoryResponseV2_CreatedStory{
				Story: adapter_driven_assembler.ConvertStoryDetailToDto(item.Story),
				IsTop: item.IsTop,
			}
		}),
		ListResponse: listResp,
	}, nil
}

func (s *ItemsService) ListUnlockedStory(ctx context.Context, req *api_items_story_v1.ListUnlockedStoryRequest) (*api_items_story_v1.ListUnlockedStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	stories, listResp, err := s.itemStoryUsecase.ListUnlockedStory(ctx, user.Id, req.ListRequest, req.FilterPlayTypes)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ListUnlockedStoryResponse{
		UnlockedStories: lo.Map(stories, func(item *domain_entities_items.StoryDetail, _ int) *api_items_story_types_v1.StoryDetail {
			return adapter_driven_assembler.ConvertStoryDetailToDto(item)
		}),
		ListResponse: listResp,
	}, nil

}

func (s *ItemsService) ListHomePageStoryV2(ctx context.Context, req *api_items_story_v2.ListHomePageStoryRequestV2) (*api_items_story_v2.ListHomePageStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	stories, listResp, sortRequestId, feedHauntBooShowInfo, err := s.itemStoryUsecase.ListHomePageStory(ctx, user.Id, req.ListRequest, req.UseRecommended, req.FilterPlayTypes)
	if err != nil {
		return nil, err
	}

	myPortalsRes, err := s.ListMyPortals(ctx, &api_items_portal_v1.ListMyPortalsRequest{
		ListRequest: &api_common_v1.ListRequest{
			PageToken: "", // 第一页
			PageSize:  "20",
		},
	})
	if err != nil {
		return nil, err
	}

	return &api_items_story_v2.ListHomePageStoryResponseV2{
		Stories: lo.Map(stories, func(item *domain_entities_items.StoryDetail, _ int) *api_items_story_types_v1.StoryDetail {
			return adapter_driven_assembler.ConvertStoryDetailToDto(item)
		}),
		ListResponse:       listResp,
		SortRequestId:      sortRequestId,
		UserCreatedPortals: myPortalsRes.UserCreatedPortals,
		Portals:            myPortalsRes.Portals,
		PortalListResponse: myPortalsRes.ListResponse,
		HauntBooShowInfos:  []*api_items_story_types_v1.HauntBooShowInfo{adapter_driven_assembler.ConvertHauntBooShowInfoToDto(feedHauntBooShowInfo)},
	}, nil
}

func (s *ItemsService) ListUsersByConsumptionStatus(ctx context.Context, req *api_items_story_activity_v1.ListUsersByConsumptionStatusRequest) (*api_items_story_activity_v1.ListUsersByConsumptionStatusResponse, error) {
	user := s.GetAuthUser(ctx)
	storyId := cast.ToInt64(req.GetStoryId())
	consumptionStatus := req.GetConsumptionStatus()
	users, listResp, err := s.itemStoryUsecase.ListUsersByConsumptionStatus(ctx, user.Id, storyId, consumptionStatus, req.GetListRequest())
	if err != nil {
		return nil, err
	}
	return &api_items_story_activity_v1.ListUsersByConsumptionStatusResponse{
		Users:        adapter_driven_assembler.BatchConverUserInfosToSummaries(users),
		ListResponse: listResp,
	}, nil
}

func (s *ItemsService) ListCommonConditionTemplates(ctx context.Context, req *api_items_story_v1.ListCommonStoryConditionTemplatesRequest) (*api_items_story_v1.ListCommonStoryConditionTemplatesResponse, error) {
	user := s.GetAuthUser(ctx)
	templates, err := s.itemStoryUsecase.ListCommonStoryConditionTemplates(ctx, user.Id, req.StoryPlayType)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ListCommonStoryConditionTemplatesResponse{
		ConditionTemplates: lo.Map(templates, func(template *domain_entities_items.CommonStoryPlayConditionTemplate, _ int) *api_items_story_types_v1.CommonStoryPlayConditionTemplate {
			return &api_items_story_types_v1.CommonStoryPlayConditionTemplate{
				Id: fmt.Sprintf("%d", template.ID),
				Condition: &api_items_story_types_v1.Condition{
					Prompt: template.Condition.Prompt,
					Hint:   template.Condition.Hint,
				},
			}
		}),
	}, nil
}

func (s *ItemsService) ListTurtleSoupStoryConditionTemplates(ctx context.Context, req *api_items_story_v1.ListTurtleSoupStoryConditionTemplatesRequest) (*api_items_story_v1.ListTurtleSoupStoryConditionTemplatesResponse, error) {
	user := s.GetAuthUser(ctx)
	templates, err := s.itemStoryUsecase.ListTurtleSoupStoryConditionTemplates(ctx, user.Id)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ListTurtleSoupStoryConditionTemplatesResponse{
		ConditionTemplates: lo.Map(templates, func(template *domain_entities_items.StoryPlayTurtleSoupConditionTemplate, _ int) *api_items_story_types_v1.StoryPlayTurtleConditionTemplate {
			return &api_items_story_types_v1.StoryPlayTurtleConditionTemplate{
				Id:            fmt.Sprintf("%d", template.ID),
				CoverImageUrl: template.CoverImageObjectKey.ItemPosterInSummary(),
				Summary:       template.Condition.Summary,
				Caption:       template.Condition.Caption,
				IntentPrompt:  template.Condition.IntentPrompt,
				Typ:           template.Typ,
			}
		}),
	}, nil
}

func (s *ItemsService) ListExchangeImageStoryConditionTemplates(ctx context.Context, req *api_items_story_v1.ListExchangeImageStoryConditionTemplatesRequest) (*api_items_story_v1.ListExchangeImageStoryConditionTemplatesResponse, error) {
	user := s.GetAuthUser(ctx)
	templates, err := s.itemStoryUsecase.ListExchangeImageStoryConditionTemplates(ctx, user.Id)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ListExchangeImageStoryConditionTemplatesResponse{
		ConditionTemplates: lo.Map(templates, func(template *domain_entities_items.ExchangeImageConditionTemplate, _ int) *api_items_story_types_v1.StoryExchangeImageConditionTemplate {
			return &api_items_story_types_v1.StoryExchangeImageConditionTemplate{
				Id:            fmt.Sprintf("%d", template.ID),
				CoverImageUrl: template.CoverImageObjectKey.ItemPosterInSummary(),
				Typ:           template.Typ,
				DefaultCondition: &api_items_story_types_v1.StoryPlayExchangeImageCondition{
					PositiveGuide: template.Condition.PositiveText,
					PositiveImageUrls: lo.Map(template.Condition.PositiveImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
						return key.ItemPosterInSummary()
					}),
					NegativeGuide: template.Condition.NegativeText,
					NegativeImageUrls: lo.Map(template.Condition.NegativeImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
						return key.ItemPosterInSummary()
					}),
					PositiveImageKeys: lo.Map(template.Condition.PositiveImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
						return string(key)
					}),
					NegativeImageKeys: lo.Map(template.Condition.NegativeImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
						return string(key)
					}),
					Tips:              template.Condition.Tips,
					LlmPrompt:         template.Condition.LLMPrompt,
					PartialMatchGuide: template.Condition.PartialMatchText,
					PartialMatchImageUrls: lo.Map(template.Condition.PartialMatchImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
						return key.ItemPosterInSummary()
					}),
					PartialMatchImageKeys: lo.Map(template.Condition.PartialMatchImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
						return string(key)
					}),
				},
			}
		}),
	}, nil
}

func (s *ItemsService) ListUnmuteStoryConditionTemplates(ctx context.Context, req *api_items_story_v1.ListUnmuteStoryConditionTemplatesRequest) (*api_items_story_v1.ListUnmuteStoryConditionTemplatesResponse, error) {
	user := s.GetAuthUser(ctx)
	templates, err := s.itemStoryUsecase.ListUnmuteStoryConditionTemplates(ctx, user.Id)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ListUnmuteStoryConditionTemplatesResponse{
		ConditionTemplates: lo.Map(templates, func(template *domain_entities_items.StoryPlayUnmuteConditionTemplate, _ int) *api_items_story_types_v1.StoryPlayUnmuteConditionTemplate {
			return &api_items_story_types_v1.StoryPlayUnmuteConditionTemplate{
				Id: fmt.Sprintf("%d", template.ID),
				DefaultCondition: &api_items_story_types_v1.StoryPlayUnmuteCondition{
					Prompt: template.Condition.Prompt,
				},
				Typ: template.Typ,
			}
		}),
	}, nil
}

func (s *ItemsService) CreateWhoStoryV2(ctx context.Context, req *api_items_story_v2.CreateWhoStoryRequestV2) (*api_items_story_v2.CreateWhoStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	attr := domain_services_items_story.CreateWhoStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CreatorID: user.Id,
		},
		PlayConfig: &domain_entities_items.StoryPlayWhoConfig{
			UnlockAttachmentTexts: adapter_driven_assembler.ConvertAttachedTextDtosToEntities(req.PlayConfig.UnlockResourceTexts...),
			CoverAttachmentTexts:  adapter_driven_assembler.ConvertAttachedTextDtosToEntities(req.PlayConfig.CoverResourceTexts...),
			OptionUserIds: lo.Map(req.PlayConfig.OptionUserIds, func(option string, _ int) int64 {
				return cast.ToInt64(option)
			}),
		},
	}
	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		attr.UserMoments = moments
	}
	unlockResourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.UnlockResource)
	if err != nil {
		return nil, err
	}
	coverResourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.CoverResource)
	if err != nil {
		return nil, err
	}
	attr.PlayConfig.UnlockResource = unlockResourceEntity
	attr.PlayConfig.CoverResource = coverResourceEntity

	story, err := s.itemStoryUsecase.CreateWhoStory(ctx, user.Id, attr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.CreateWhoStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateTurtleSoupStoryV2(ctx context.Context, req *api_items_story_v2.CreateTurtleSoupStoryRequestV2) (*api_items_story_v2.CreateTurtleSoupStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	playConfig := req.PlayConfig.CommonPlayConfig
	cover := playConfig.Cover
	var internalCover *domain_entities_resource.CoverImage
	switch cover.CoverType {
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ResourceKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ThumbnailKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
			Gradient:  cover.Gradient,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_NONE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
		}
	}

	cfg := &domain_entities_items.StoryPlayTurtleSoupConfig{
		CommonConfig: &domain_entities_items.CommonConfig{
			Cover: internalCover,
			// 封面宽高移至 CoverImage
			Resource: &domain_entities_items.Resource{
				Type:             playConfig.Resource.ResourceType,
				Key:              playConfig.Resource.ResourceKey,
				CoverImageKey:    domain_entities_resource.ImageResourcePath(playConfig.Resource.CoverImageKey),
				CoverImageWidth:  int32(playConfig.Resource.CoverWidth),
				CoverImageHeight: int32(playConfig.Resource.CoverHeight),
			},
			CoverCaptions:    playConfig.CoverCaptions,
			ResourceCaptions: playConfig.ResourceCaptions,
			MaxTryCount:      playConfig.MaxTryCount,
			Condition: &domain_entities_items.Condition{
				Hint:   playConfig.ConditionV2.Hint,
				Prompt: playConfig.ConditionV2.Prompt,
			},
		},
	}

	attr := domain_services_items_story.CreateTurtleSoupStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CreatorID: user.Id,
			CoverImage: domain_entities_items.StoryCoverImage{
				Path:   domain_entities_resource.ImageResourcePath(playConfig.Cover.ThumbnailKey),
				Width:  int(playConfig.Cover.CoverWidth),
				Height: int(playConfig.Cover.CoverHeight),
			},
		},
		PlayConfig: cfg,
	}

	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		attr.UserMoments = moments
	}
	if req.PrivacySetting != nil {
		attr.PrivacySettings = buildPrivacySettingUpdateAttr(req.PrivacySetting)
	}
	story, err := s.itemStoryUsecase.CreateTurtleSoupStory(ctx, user.Id, attr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.CreateTurtleSoupStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateTurtleSoupStory(ctx context.Context, req *api_items_story_v1.CreateTurtleSoupStoryRequest) (*api_items_story_v1.CreateTurtleSoupStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	cfg := &domain_entities_items.StoryPlayTurtleSoupConfig{
		Caption:        req.PlayConfig.Caption,
		IntentPrompt:   req.PlayConfig.IntentPrompt,
		EndMessage:     req.PlayConfig.EndMessage,
		EndMessageFont: req.PlayConfig.EndMessageFont,
		CustomAiResponses: lo.Map(req.PlayConfig.CustomAiResponses, func(resp *api_items_story_types_v1.StoryPlayTurtleSoupConfig_CustomAiResponse, _ int) *domain_entities_items.StoryPlayTurtleSoupCustomAiResponse {
			return &domain_entities_items.StoryPlayTurtleSoupCustomAiResponse{
				RuleDescription: resp.RuleDescription,
				RuleResult:      resp.RuleResult,
			}
		}),
		MaxTryCount:           req.PlayConfig.MaxTryCount,
		NeedCreateDiyTemplate: req.PlayConfig.NeedCreateDiyTemplate,
	}
	resourceType := api_items_story_types_v1.StoryPlayTurtleSoupConfig_ResourceType(api_items_story_types_v1.StoryPlayTurtleSoupConfig_ResourceType_value[req.PlayConfig.ResourceType])
	endResourceType := api_items_story_types_v1.StoryPlayTurtleSoupConfig_ResourceType(api_items_story_types_v1.StoryPlayTurtleSoupConfig_ResourceType_value[req.PlayConfig.EndResourceType])
	cfg.ResourceType = resourceType
	cfg.EndResourceType = endResourceType

	switch endResourceType {
	case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
		key := domain_entities_resource.VideoResourcePath(req.PlayConfig.EndResourceUrl)
		cfg.EndResourceVideoKey = &key
	case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
		key := domain_entities_resource.ImageResourcePath(req.PlayConfig.EndResourceUrl)
		cfg.EndResourceImageKey = &key
	}
	switch resourceType {
	case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
		key := domain_entities_resource.VideoResourcePath(req.PlayConfig.ResourceUrl)
		cfg.ResourceVideoKey = &key
	case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
		key := domain_entities_resource.ImageResourcePath(req.PlayConfig.ResourceUrl)
		cfg.ResourceImageKey = &key
	}

	if req.PlayConfig.EndThumbnailUrl != nil {
		key := domain_entities_resource.ImageResourcePath(*req.PlayConfig.EndThumbnailUrl)
		cfg.EndThumbnailKey = &key
	}
	if req.PlayConfig.ThumbnailUrl != nil {
		key := domain_entities_resource.ImageResourcePath(*req.PlayConfig.ThumbnailUrl)
		cfg.ThumbnailKey = &key
	}
	if req.PlayConfig.TemplateId != nil && cast.ToInt64(*req.PlayConfig.TemplateId) > 0 {
		id := cast.ToInt64(*req.PlayConfig.TemplateId)
		cfg.TemplateId = &id
	}

	attr := domain_services_items_story.CreateTurtleSoupStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: user.Id,
		},
		PlayConfig: cfg,
		IsMass:     req.IsMass,
	}
	if req.PrivacySetting != nil {
		attr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.CreateTurtleSoupStory(ctx, user.Id, attr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CreateTurtleSoupStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) GetRoastedTopics(ctx context.Context, req *api_items_story_v1.GetRoastedTopicsRequest) (*api_items_story_v1.GetRoastedTopicsResponse, error) {
	user := s.GetAuthUser(ctx)
	var copyStoryId *int64
	if req.CopyStoryId != nil && *req.CopyStoryId != "" {
		id := cast.ToInt64(*req.CopyStoryId)
		copyStoryId = &id
	}
	topic, err := s.itemStoryUsecase.GetOneRandomTopic(ctx, user.Id, copyStoryId)
	if err != nil {
		return nil, err
	}
	// Random345 returns 3, 4, or 5 with probabilities 60%, 20%, and 20% respectively.
	random345 := func() int {
		r := rand.Float64()
		// 0.0 <= r < 1.0
		if r < 0.8 {
			return 2
		}
		return 3
	}
	return &api_items_story_v1.GetRoastedTopicsResponse{
		Topic: &api_items_story_types_v1.StoryPlayRoastedTopic{
			Questions: lo.Map(topic.Questions, func(question *domain_entities_items.StoryPlayRoastedQuestion, _ int) *api_items_story_types_v1.RoastedQuestion {
				return &api_items_story_types_v1.RoastedQuestion{
					Question:    question.Question,
					TtsAudioUrl: question.TTSAudioKey.URL(),
					Thinking:    question.Thinking,
					TtsAudioKey: string(question.TTSAudioKey),
				}
			}),
			Greeting: &api_items_story_types_v1.StoryPlayRoastedTopic_BotAnnouncement{
				Content:     topic.Greeting.Content,
				TtsAudioKey: string(topic.Greeting.TTSAudioKey),
				TtsAudioUrl: topic.Greeting.TTSAudioKey.URL(),
			},
			Ending: &api_items_story_types_v1.StoryPlayRoastedTopic_BotAnnouncement{
				Content:     topic.Ending.Content,
				TtsAudioKey: string(topic.Ending.TTSAudioKey),
				TtsAudioUrl: topic.Ending.TTSAudioKey.URL(),
			},
			MaxFollowup: uint32(random345()),
		},
	}, nil
}
func (s *ItemsService) CreateChatProxyStory(ctx context.Context, req *api_items_story_v2.CreateChatProxyStoryRequestV2) (*api_items_story_v2.CreateChatProxyStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	attr := domain_services_items_story.CreateChatProxyStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: user.Id,
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CoverImage: domain_entities_items.StoryCoverImage{
				Path:   domain_entities_resource.ImageResourcePath(req.PlayConfig.Cover.GetObjectKey()),
				Width:  int(req.PlayConfig.Cover.CoverImageWidth),
				Height: int(req.PlayConfig.Cover.CoverImageHeight),
			},
		},
		PlayConfig: &domain_entities_items.StoryPlayChatProxyConfig{
			Caption: req.PlayConfig.Caption,
			Topics: lo.Map(req.PlayConfig.Topics, func(topic *api_items_story_types_v1.StoryPlayChatProxyTopic, _ int) string {
				return topic.Content
			}),
			CoverAttachmentTexts:          req.PlayConfig.CoverAttachmentTexts,
			UnlockResourceAttachmentTexts: req.PlayConfig.UnlockResourceAttachmentTexts,
		},
	}

	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		attr.UserMoments = moments
	}

	unlockResourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.UnlockResource)
	if err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid unlock resource"))
	}
	attr.PlayConfig.UnlockResource = unlockResourceEntity
	coverResourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.Cover)
	if err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid cover resource"))
	}
	attr.PlayConfig.CoverResource = coverResourceEntity

	if req.PrivacySetting != nil {
		attr.PrivacySettings = buildPrivacySettingUpdateAttr(req.PrivacySetting)
	}

	story, err := s.itemStoryUsecase.CreateChatProxyStory(ctx, user.Id, attr)
	if err != nil {
		return nil, err
	}

	if req.GetFromStoryId() != "" {
		if err := s.updateFromStoryPostMomentStatus(ctx, user.Id, story.Summary.Id, cast.ToInt64(req.GetFromStoryId())); err != nil {
			return nil, err
		}
	}

	return &api_items_story_v2.CreateChatProxyStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateRoastedStory(ctx context.Context, req *api_items_story_v1.CreateRoastedStoryRequest) (*api_items_story_v1.CreateRoastedStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	attr := domain_services_items_story.CreateRoastedStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: user.Id,
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CoverImage: domain_entities_items.StoryCoverImage{
				Path:   domain_entities_resource.ImageResourcePath(req.PlayConfig.Cover.GetObjectKey()),
				Width:  int(req.PlayConfig.Cover.CoverImageWidth),
				Height: int(req.PlayConfig.Cover.CoverImageHeight),
			},
		},
		PlayConfig: &domain_entities_items.StoryPlayRoastedConfig{
			Topic: &domain_entities_items.StoryPlayRoastedTopic{
				Questions: lo.Map(req.PlayConfig.Topic.Questions, func(question *api_items_story_types_v1.RoastedQuestion, _ int) *domain_entities_items.StoryPlayRoastedQuestion {
					return &domain_entities_items.StoryPlayRoastedQuestion{
						Question:    question.Question,
						TTSAudioKey: domain_entities_resource.AudioResourcePath(question.TtsAudioKey),
						Thinking:    question.Thinking,
					}
				}),
				Greeting: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     req.PlayConfig.Topic.Greeting.Content,
					TTSAudioKey: domain_entities_resource.AudioResourcePath(req.PlayConfig.Topic.Greeting.TtsAudioKey),
				},
				Ending: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     req.PlayConfig.Topic.Ending.Content,
					TTSAudioKey: domain_entities_resource.AudioResourcePath(req.PlayConfig.Topic.Ending.TtsAudioKey),
				},
			},
			FromStoryID: new(int64),
		},
	}
	resourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.Resource)
	if err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid resource"))
	}
	attr.PlayConfig.BaseResource = resourceEntity

	coverResourceEntity, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.Cover)
	if err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid cover resource"))
	}
	attr.PlayConfig.Cover = coverResourceEntity

	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		attr.UserMoments = moments
	}

	if req.PrivacySetting != nil {
		attr.PrivacySettings = buildPrivacySettingUpdateAttr(req.PrivacySetting)
	}

	detail, err := s.itemStoryUsecase.CreateRoastedStory(ctx, user.Id, attr)
	if err != nil {
		return nil, err
	}

	if req.GetFromStoryId() != "" {
		if err := s.updateFromStoryPostMomentStatus(ctx, user.Id, detail.Summary.Id, cast.ToInt64(req.GetFromStoryId())); err != nil {
			return nil, err
		}
	}

	return &api_items_story_v1.CreateRoastedStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(detail),
	}, nil
}

func (s *ItemsService) CreateExchangeImageStoryV2(ctx context.Context, req *api_items_story_v2.CreateExchangeImageStoryRequestV2) (*api_items_story_v2.CreateExchangeImageStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	playConfig := req.PlayConfig.CommonPlayConfig
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
		CoverImage: domain_entities_items.StoryCoverImage{
			Path:   domain_entities_resource.ImageResourcePath(playConfig.Cover.ResourceKey),
			Width:  int(playConfig.Cover.CoverWidth),
			Height: int(playConfig.Cover.CoverHeight),
		},
		Version: lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
	}

	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		baseAttr.UserMoments = moments
	}

	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = buildPrivacySettingUpdateAttr(req.PrivacySetting)
	}
	cover := playConfig.Cover
	var internalCover *domain_entities_resource.CoverImage
	switch cover.CoverType {
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ResourceKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ThumbnailKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
			Gradient:  cover.Gradient,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_NONE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
		}
	}
	story, err := s.itemStoryUsecase.CreateImageExchangeStory(ctx, user.Id, domain_services_items_story.CreateImageExchangeStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig: &domain_entities_items.ExchanegImagePlayConfig{
			CommonConfig: &domain_entities_items.CommonConfig{
				Cover: internalCover,
				// 封面宽高移至 CoverImage
				Resource: &domain_entities_items.Resource{
					Type:             playConfig.Resource.ResourceType,
					Key:              playConfig.Resource.ResourceKey,
					CoverImageKey:    domain_entities_resource.ImageResourcePath(playConfig.Resource.CoverImageKey),
					CoverImageWidth:  int32(playConfig.Resource.CoverWidth),
					CoverImageHeight: int32(playConfig.Resource.CoverHeight),
				},
				CoverCaptions:    playConfig.CoverCaptions,
				ResourceCaptions: playConfig.ResourceCaptions,
				MaxTryCount:      playConfig.MaxTryCount,
				Condition: &domain_entities_items.Condition{
					Hint:   playConfig.ConditionV2.Hint,
					Prompt: playConfig.ConditionV2.Prompt,
				},
			},
		},
	})
	if err != nil {
		return nil, err
	}

	if req.GetFromStoryId() != "" {
		if err := s.updateFromStoryPostMomentStatus(ctx, user.Id, story.Summary.Id, cast.ToInt64(req.GetFromStoryId())); err != nil {
			return nil, err
		}
	}

	return &api_items_story_v2.CreateExchangeImageStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateExchangeImageStory(ctx context.Context, req *api_items_story_v1.CreateExchangeImageStoryRequest) (*api_items_story_v1.CreateExchangeImageStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	playMode := api_items_story_types_v1.ExchangeImagePlayMode(api_items_story_types_v1.ExchangeImagePlayMode_value[req.PlayConfig.PlayMode])
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.CreateImageExchangeStory(ctx, user.Id, domain_services_items_story.CreateImageExchangeStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig: &domain_entities_items.ExchanegImagePlayConfig{
			PlayMode: playMode,
			Nodes: lo.Map(req.PlayConfig.Nodes, func(node *api_items_story_types_v1.StoryPlayExchangeImageConfig_Node, _ int) *domain_entities_items.ExchangeImagePlayNode {
				n := &domain_entities_items.ExchangeImagePlayNode{
					ID:                    node.Id,
					AllowUseAlbum:         node.AllowUseAlbum,
					MaxTryCount:           node.MaxTryCount,
					NeedCreateDiyTemplate: node.NeedCreateDiyTemplate,
				}
				if node.Condition != nil {
					n.Condition = domain_entities_items.ExchangeImageCondition{
						Tips:         node.Condition.Tips,
						PositiveText: node.Condition.PositiveGuide,
						PositiveImageObjectKeys: lo.Map(node.Condition.PositiveImageUrls, func(key string, _ int) domain_entities_resource.ImageResourcePath {
							return domain_entities_resource.ImageResourcePath(key)
						}),
						NegativeText: node.Condition.NegativeGuide,
						NegativeImageObjectKeys: lo.Map(node.Condition.NegativeImageUrls, func(key string, _ int) domain_entities_resource.ImageResourcePath {
							return domain_entities_resource.ImageResourcePath(key)
						}),
						LLMPrompt:        node.Condition.LlmPrompt,
						PartialMatchText: node.Condition.PartialMatchGuide,
						PartialMatchImageObjectKeys: lo.Map(node.Condition.PartialMatchImageUrls, func(key string, _ int) domain_entities_resource.ImageResourcePath {
							return domain_entities_resource.ImageResourcePath(key)
						}),
					}
				}
				if node.ThumbnailUrl != nil {
					key := domain_entities_resource.ImageResourcePath(*node.ThumbnailUrl)
					n.ThumbnailObjectKey = &key
				}
				resourceType := api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_ResourceType(api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_ResourceType_value[node.ResourceType])
				n.ResourceType = resourceType
				switch resourceType {
				case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE:
					key := domain_entities_resource.ImageResourcePath(node.ResourceUrl)
					n.ImageKey = &key
				case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO:
					key := domain_entities_resource.VideoResourcePath(node.ResourceUrl)
					n.VideoKey = &key
				}
				return n
			}),
		},
	})
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CreateExchangeImageStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateUnmuteStoryV2(ctx context.Context, req *api_items_story_v2.CreateUnmuteStoryRequestV2) (*api_items_story_v2.CreateUnmuteStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	playConfig := req.PlayConfig.CommonPlayConfig
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
		CreatorID: user.Id,
		CoverImage: domain_entities_items.StoryCoverImage{
			Path:   domain_entities_resource.ImageResourcePath(playConfig.Cover.ThumbnailKey),
			Width:  int(playConfig.Cover.CoverWidth),
			Height: int(playConfig.Cover.CoverHeight),
		},
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = buildPrivacySettingUpdateAttr(req.PrivacySetting)
	}
	cover := playConfig.Cover
	var internalCover *domain_entities_resource.CoverImage
	switch cover.CoverType {
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ResourceKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ThumbnailKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
			Gradient:  cover.Gradient,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_NONE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
		}
	}
	cfg := &domain_entities_items.StoryPlayUnmuteConfig{
		CommonConfig: &domain_entities_items.CommonConfig{
			Cover: internalCover,
			// 封面宽高移至 CoverImage
			Resource: &domain_entities_items.Resource{
				Type:             playConfig.Resource.ResourceType,
				Key:              playConfig.Resource.ResourceKey,
				CoverImageKey:    domain_entities_resource.ImageResourcePath(playConfig.Resource.CoverImageKey),
				CoverImageWidth:  int32(playConfig.Resource.CoverWidth),
				CoverImageHeight: int32(playConfig.Resource.CoverHeight),
			},
			CoverCaptions:    playConfig.CoverCaptions,
			ResourceCaptions: playConfig.ResourceCaptions,
			MaxTryCount:      playConfig.MaxTryCount,
			Condition: &domain_entities_items.Condition{
				Hint:   playConfig.ConditionV2.Hint,
				Prompt: playConfig.ConditionV2.Prompt,
			},
		},
	}
	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		baseAttr.UserMoments = moments
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.CreateUnmuteStory(ctx, user.Id, domain_services_items_story.CreateUnmuteStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig:          cfg,
	})
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.CreateUnmuteStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateUnmuteStory(ctx context.Context, req *api_items_story_v1.CreateUnmuteStoryRequest) (*api_items_story_v1.CreateUnmuteStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	playConfig := req.GetPlayConfig()
	promptStyle := playConfig.GetPrompt()
	cfg := &domain_entities_items.StoryPlayUnmuteConfig{
		Prompt: domain_entities_items.UnmutePromptStyle{
			Text:     promptStyle.GetText(),
			X:        promptStyle.GetX(),
			Y:        promptStyle.GetY(),
			FontSize: promptStyle.GetFontSize(),
			FontName: promptStyle.GetFontName(),
			Color:    promptStyle.GetColor(),
			Height:   promptStyle.GetHeight(),
			Width:    promptStyle.GetWidth(),
		},
		Intention: playConfig.GetIntention(),
	}
	resourceType := api_items_story_types_v1.StoryPlayUnmuteConfig_ResourceType(api_items_story_types_v1.StoryPlayUnmuteConfig_ResourceType_value[playConfig.GetResourceType()])
	endResourceType := api_items_story_types_v1.StoryPlayUnmuteConfig_ResourceType(api_items_story_types_v1.StoryPlayUnmuteConfig_ResourceType_value[playConfig.GetEndResourceType()])
	cfg.ResourceType = resourceType
	cfg.EndResourceType = endResourceType

	switch resourceType {
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
		key := domain_entities_resource.VideoResourcePath(playConfig.GetResourceUrl())
		cfg.ResourceVideoKey = &key
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
		key := domain_entities_resource.ImageResourcePath(playConfig.GetResourceUrl())
		cfg.ResourceImageKey = &key
	}

	switch endResourceType {
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
		key := domain_entities_resource.VideoResourcePath(playConfig.GetEndResourceUrl())
		cfg.EndResourceVideoKey = &key
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
		key := domain_entities_resource.ImageResourcePath(playConfig.GetEndResourceUrl())
		cfg.EndResourceImageKey = &key
	}

	if playConfig.ThumbnailUrl != nil {
		key := domain_entities_resource.ImageResourcePath(*req.PlayConfig.ThumbnailUrl)
		cfg.ThumbnailKey = &key
	}
	if playConfig.EndThumbnailUrl != nil {
		key := domain_entities_resource.ImageResourcePath(*req.PlayConfig.EndThumbnailUrl)
		cfg.EndThumbnailKey = &key
	}

	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.CreateUnmuteStory(ctx, user.Id, domain_services_items_story.CreateUnmuteStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig:          cfg,
	})
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CreateUnmuteStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateNowShotStory(ctx context.Context, req *api_items_story_v1.CreateNowShotStoryRequest) (*api_items_story_v1.CreateNowShotStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	cfg := &domain_entities_items.StoryPlayNowShotConfig{
		Caption: req.PlayConfig.Caption,
		TTL:     req.PlayConfig.Ttl,
	}

	resourceType := api_items_story_types_v1.StoryPlayNowShotConfig_ResourceType(api_items_story_types_v1.StoryPlayNowShotConfig_ResourceType_value[req.PlayConfig.ResourceType])
	endResourceType := api_items_story_types_v1.StoryPlayNowShotConfig_ResourceType(api_items_story_types_v1.StoryPlayNowShotConfig_ResourceType_value[req.PlayConfig.EndResourceType])
	cfg.ResourceType = resourceType
	cfg.EndResourceType = endResourceType

	switch endResourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
		key := domain_entities_resource.VideoResourcePath(req.PlayConfig.EndResourceUrl)
		cfg.EndResourceVideoKey = &key
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
		key := domain_entities_resource.ImageResourcePath(req.PlayConfig.EndResourceUrl)
		cfg.EndResourceImageKey = &key
	}
	switch resourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
		key := domain_entities_resource.VideoResourcePath(req.PlayConfig.ResourceUrl)
		cfg.ResourceVideoKey = &key
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
		key := domain_entities_resource.ImageResourcePath(req.PlayConfig.ResourceUrl)
		cfg.ResourceImageKey = &key
	}

	if req.PlayConfig.EndThumbnailUrl != nil {
		key := domain_entities_resource.ImageResourcePath(*req.PlayConfig.EndThumbnailUrl)
		cfg.EndThumbnailKey = &key
	}
	if req.PlayConfig.ThumbnailUrl != nil {
		key := domain_entities_resource.ImageResourcePath(*req.PlayConfig.ThumbnailUrl)
		cfg.ThumbnailKey = &key
	}

	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.CreateNowShotStory(ctx, user.Id, domain_services_items_story.CreateNowShotStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig:          cfg,
	})
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CreateNowShotStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) GetStoryDetail(ctx context.Context, req *api_items_story_v1.GetStoryDetailRequest) (*api_items_story_v1.GetStoryDetailResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.GetStoryDetail(ctx, user.Id, cast.ToInt64(req.Id))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.GetStoryDetailResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) DeleteStory(ctx context.Context, req *api_items_story_v1.DeleteStoryRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	err := s.itemStoryUsecase.DeleteStory(ctx, user.Id, cast.ToInt64(req.StoryId))
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
func (s *ItemsService) ConsumeHauntStory(ctx context.Context, req *api_items_story_v2.ConsumeHauntStoryRequest) (*api_items_story_v2.ConsumeHauntStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.ConsumeHauntStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.Unlocked, req.IsFromFeedOrFriends)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumeHauntStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) SendHauntCaptureVideo(ctx context.Context, req *api_items_story_v2.SendHauntCaptureVideoRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	err := s.itemStoryUsecase.SendHauntCaptureVideo(
		ctx,
		user.Id,
		cast.ToInt64(req.StoryId),
		domain_entities_resource.VideoResourcePath(req.VideoKey),
		domain_entities_resource.ImageResourcePath(req.VideoCoverKey),
	)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}
func (s *ItemsService) ListHauntQuestions(ctx context.Context, req *api_items_story_v2.ListHauntQuestionsRequest) (*api_items_story_v2.ListHauntQuestionsResponse, error) {
	// user := s.GetAuthUser(ctx)
	// questions, err := s.itemStoryUsecase.ListHauntQuestions(ctx, user.Id)
	// if err != nil {
	// 	return nil, err
	// }
	// return &api_items_story_v2.ListHauntQuestionsResponse{
	// 	Questions: questions,
	// },nil
	return nil, nil
}

func (s *ItemsService) ReportHauntShow(ctx context.Context, req *api_items_story_v2.ReportHauntShowRequest) (*api_items_story_v2.ReportHauntShowResponse, error) {
	user := s.GetAuthUser(ctx)
	if err := s.itemStoryUsecase.ReportHauntBooShowEvent(ctx, user.Id, cast.ToInt64(req.BooId), req.ShowInfoSence, nil); err != nil {
		return nil, err
	}
	return &api_items_story_v2.ReportHauntShowResponse{
		UpdatedHauntBooShowInfo: nil,
	}, nil
}

func (s *ItemsService) ListHauntRandomAvatars(ctx context.Context, req *api_items_story_v2.ListHauntRandomAvatarsRequest) (*api_items_story_v2.ListHauntRandomAvatarsResponse, error) {
	user := s.GetAuthUser(ctx)
	avatars, err := s.itemStoryUsecase.ListRandomAvatars(ctx, user.Id, 10)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ListHauntRandomAvatarsResponse{
		AvatarUrls: lo.Map(avatars, func(item *domain_entities_boo.Avatar, _ int) string {
			return item.ObjectPath.ItemPosterInSummary()
		}),
	}, nil
}

func (s *ItemsService) CheckHauntImage(ctx context.Context, req *api_items_story_v2.ImageCheckRequest) (*api_items_story_v2.ImageCheckResponse, error) {
	pass, point, feedback, err := s.itemStoryUsecase.CheckHauntImage(ctx, domain_entities_resource.ImageResourcePath(req.ImageKey), req.TextCondition)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ImageCheckResponse{Pass: pass, Point: lo.Map(point, func(item float64, _ int) string {
		return fmt.Sprintf("%f", item)
	}), Feedback: feedback}, nil
}

func (s *ItemsService) ListHauntBooAssist(ctx context.Context, req *api_items_story_v2.ListHauntBooAssistRequest) (*api_items_story_v2.ListHauntBooAssistResponse, error) {
	user := s.GetAuthUser(ctx)
	boos, lisp, err := s.itemStoryUsecase.ListHauntBooAssist(ctx, user.Id, req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ListHauntBooAssistResponse{
		ListResponse:                lisp,
		BoosWithQuestionsAndAnswers: adapter_driven_assembler.ConvertHauntBooToDto(boos...),
	}, nil
}

func (s *ItemsService) AddCaptureBooIntoMyAssist(ctx context.Context, req *api_items_story_v2.AddCaptureBooIntoMyAssistRequest) (*emptypb.Empty, error) {
	user := s.GetAuthUser(ctx)
	err := s.itemStoryUsecase.AddCaptureBooIntoMyAssist(ctx, user.Id, cast.ToInt64(req.StoryId), cast.ToInt64(req.BooId))
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *ItemsService) AddCapturedBooInToCollectedStickers(ctx context.Context, req *api_items_story_v2.AddCapturedBooInToCollectedStickersRequest) (*api_items_story_v2.AddCapturedBooInToCollectedStickersResponse, error) {
	user := s.GetAuthUser(ctx)
	booId := cast.ToInt64(req.BooId)
	sticker, err := s.itemStoryUsecase.AddCapturedBooInToCollectedStickers(ctx, user.Id, booId)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.AddCapturedBooInToCollectedStickersResponse{
		AddedCollectedSticker: &api_items_story_v2.AddCapturedBooInToCollectedStickersResponse_CollectedSticker{
			Sticker:                  adapter_driven_assembler.ConvertHideStoryStickerToDto(sticker),
			CollectedAtUnixTimestamp: uint32(time.Now().Unix()),
			IsTop:                    false,
		},
	}, nil
}

func (s *ItemsService) CreateHauntStoryV2(ctx context.Context, req *api_items_story_v2.CreateHauntStoryRequest) (*api_items_story_v2.CreateHauntStoryResponse, error) {
	user := s.GetAuthUser(ctx)

	haunCreateAttr := &domain_services_items_story.CreateHauntStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CreatorID: user.Id,
		},
		PlayConfig: &domain_entities_items.HauntStoryConfig{
			Captions: req.Captions,
		},
	}

	if req.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		haunCreateAttr.StoryBaseCreateAttr.UserMoments = moments
	}
	if req.Cover != nil {
		haunCreateAttr.StoryBaseCreateAttr.CoverImage = domain_entities_items.StoryCoverImage{
			Path:   domain_entities_resource.ImageResourcePath(req.Cover.GetObjectKey()),
			Width:  int(req.Cover.CoverImageWidth),
			Height: int(req.Cover.CoverImageHeight),
		}
	}

	if req.PrivacySetting != nil {
		haunCreateAttr.StoryBaseCreateAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}

	for _, boo := range req.BoosWithQuestionsAndAnswers {
		// 取出助手的部分
		booWithQuestionAndAnswer := &domain_entities_items.HauntBoo{
			QuestionsWithAnswers: lo.Map(boo.QuestionsWithAnswers, func(question *api_items_story_types_v1.HauntQuestionWithAnswer, _ int) *domain_entities_items.HauntQuestionWithAnswer {
				return &domain_entities_items.HauntQuestionWithAnswer{
					Question: &domain_entities_items.HauntQuestion{
						Title:       question.Question.Title,
						Description: question.Question.Description,
						IsRequired:  question.Question.IsRequired,
					},
					Answer: question.Answer,
				}
			}),
		}
		if boo.GetUserAvatarId() != "" {
			booWithQuestionAndAnswer.AvatarId = cast.ToInt64(boo.GetUserAvatarId())
			haunCreateAttr.PlayConfig.CreatorBooWithQuestionsAndAnswers = booWithQuestionAndAnswer
			continue
		}
		booWithQuestionAndAnswer.Id = cast.ToInt64(boo.GetBooId())
		haunCreateAttr.PlayConfig.AssitBoosWithQuestionsAndAnswers = append(haunCreateAttr.PlayConfig.AssitBoosWithQuestionsAndAnswers, booWithQuestionAndAnswer)
	}

	story, err := s.itemStoryUsecase.CreateHauntStory(ctx, user.Id, *haunCreateAttr)

	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.CreateHauntStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateBasePlayStory(ctx context.Context, req *api_items_story_v1.CreateBasePlayStoryRequest) (*api_items_story_v1.CreateBasePlayStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
		CreatorID: user.Id,
	}
	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		baseAttr.UserMoments = moments
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	story, err := s.itemStoryUsecase.CreateBasePlayStory(ctx, user.Id, domain_services_items_story.CreateBasePlayStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig: &domain_entities_items.StoryPlayBasePlayConfig{
			Nodes: lo.Map(req.PlayConfig.Nodes, func(node *api_items_story_types_v1.StoryPlayBasePlayConfig_Node, _ int) *domain_entities_items.StotyPlayBaseConfigNode {
				n := &domain_entities_items.StotyPlayBaseConfigNode{
					ID:            node.Id,
					ResourceType:  node.Resource.ResourceType,
					ResourceKey:   node.Resource.ResourceKey,
					CoverImageKey: domain_entities_resource.ImageResourcePath(node.Resource.CoverImageKey),
					AttachedTexts: node.AttachmentTexts,
				}
				// 如果客户端没有传 cover image key 且类型是图片，则使用 resource key 作为 cover image key
				if node.Resource.CoverImageKey == "" &&
					n.ResourceType == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE {
					n.CoverImageKey = domain_entities_resource.ImageResourcePath(node.Resource.ResourceKey)
				}
				return n
			}),
		},
	})
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CreateBasePlayStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CopilotCapsuleStory(ctx context.Context, req *api_items_story_v1.CopilotCapsuleStoryRequest) (*api_items_story_v1.CopilotCapsuleStoryResponse, error) {
	user := s.Jwt.GetAuthUser(ctx)
	questionWithUserAnswer := req.GetQuestionWithUserAnswer()
	storyPlayCapsuleQuestion := &domain_entities_items.StoryPlayCapsuleQuestion{}
	if questionWithUserAnswer.GetQuestion() != nil {
		storyPlayCapsuleQuestion.Question = questionWithUserAnswer.GetQuestion().GetQuestion()
		storyPlayCapsuleQuestion.Thinking = questionWithUserAnswer.GetQuestion().GetThinking()
		storyPlayCapsuleQuestion.TTSAudioKey = domain_entities_resource.AudioResourcePath(questionWithUserAnswer.GetQuestion().GetTtsAudioKey())
	}
	question, err := s.itemStoryUsecase.CopilotCapsule(ctx, user.Id, req.GetImage(), storyPlayCapsuleQuestion, req.GetQuestionCount(), questionWithUserAnswer.GetUserVoiceKey())
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CopilotCapsuleStoryResponse{
		Question: question,
	}, nil
}
func (s *ItemsService) GetWassupGreetings(ctx context.Context, req *api_items_story_v2.GetWassupGreetingsRequest) (*api_items_story_v2.GetWassupGreetingsResponse, error) {
	user := s.GetAuthUser(ctx)
	greetings, err := s.itemStoryUsecase.GetWassupGreetings(ctx, user.Id, req.GetImageKeys(), req.GetAudioAsrResult())
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.GetWassupGreetingsResponse{
		Greeting: greetings,
	}, nil
}
func (s *ItemsService) GetWassupNextQuestion(ctx context.Context, req *api_items_story_v2.GetWassupNextQuestionRequest) (*api_items_story_v2.GetWassupNextQuestionResponse, error) {
	user := s.GetAuthUser(ctx)
	msg, end, err := s.itemStoryUsecase.GetWassupNextQuestion(ctx, user.Id, domain_entities_resource.AudioResourcePath(req.GetUserAudioKey()))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.GetWassupNextQuestionResponse{
		Message: msg,
		IsEnd:   end,
	}, nil
}
func (s *ItemsService) ConsumeWassupStoryV2(ctx context.Context, req *api_items_story_v2.ConsumeWassupStoryRequest) (*api_items_story_v2.ConsumeWassupStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.ConsumeWassupStory(
		ctx,
		user.Id,
		cast.ToInt64(req.StoryId),
	)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumeWassupStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) CreateWassupStoryV2(ctx context.Context, req *api_items_story_v2.CreateWassupStoryRequest) (*api_items_story_v2.CreateWassupStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
	}
	if req.Config.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.Config.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		baseAttr.UserMoments = moments
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	createAttr := domain_services_items_story.CreateWassupStoryAttr{StoryBaseCreateAttr: baseAttr,
		PlayConfig: &domain_entities_items.StoryPlayWassupConfig{
			IsMassCover:     req.Config.IsMassCover,
			CoverImageTexts: adapter_driven_assembler.ConvertAttachedTextDtosToEntities(req.Config.CoverImageTexts...),
		},
	}
	unlockResource, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.Config.UnlockResource)
	if err != nil {
		return nil, err
	}
	coverResource, err := adapter_driven_assembler.ConvertDtoToResourceEntity(req.Config.CoverImageResource)
	if err != nil {
		return nil, err
	}
	createAttr.PlayConfig.UnlockResource = unlockResource
	createAttr.PlayConfig.CoverResource = coverResource

	createdStory, err := s.itemStoryUsecase.CreateWassupStory(ctx, user.Id, createAttr)
	if err != nil {
		return nil, err
	}

	if req.GetFromStoryId() != "" {
		if err := s.updateFromStoryPostMomentStatus(ctx, user.Id, createdStory.Summary.Id, cast.ToInt64(req.GetFromStoryId())); err != nil {
			return nil, err
		}
	}

	return &api_items_story_v2.CreateWassupStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(createdStory),
	}, nil
}

func (s *ItemsService) SendMessage(ctx context.Context, req *api_items_story_v2.SendMessageRequest) (*api_items_story_v2.SendMessageResponse, error) {
	user := s.GetAuthUser(ctx)

	resource := domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(
		domain_entities_resource.VideoResourcePath(req.GetUserVideoKey()),
		domain_entities_resource.ImageResourcePath(req.GetUserVideoCoverKey()),
		0,
		0,
	).Build()

	story, err := s.itemStoryUsecase.UpdateUserSubmittedResource(ctx, user.Id, cast.ToInt64(req.StoryId), resource)
	if err != nil {
		return nil, err
	}

	_, err = s.itemStoryUsecase.SendStoryMessage(
		ctx,
		user.Id,
		cast.ToInt64(req.StoryId),
		req.MessageType,
		domain_entities_resource.VideoResourcePath(req.GetUserVideoKey()),
		domain_entities_resource.ImageResourcePath(req.GetUserVideoCoverKey()),
		domain_entities_resource.ImageResourcePath(req.GetConsumerStoryCoverKey()),
		req.ConsumeStatus,
	)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.SendMessageResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) CreateCapsuleStory(ctx context.Context, req *api_items_story_v1.CreateCapsuleStoryRequest) (*api_items_story_v1.CreateCapsuleStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
	}
	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}
	createAttr := domain_services_items_story.CreateCapsuleStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig:          &domain_entities_items.StoryPlayCapsuleConfig{},
	}
	coverImage := req.PlayConfig.GetCoverImage()
	if coverImage != nil {
		createAttr.PlayConfig.CoverImage = domain_entities_resource.ImageResourcePath(coverImage.ResourceKey)
	}
	video := req.PlayConfig.GetVideo()
	if video != nil {
		createAttr.PlayConfig.Video = domain_entities_resource.VideoResourcePath(video.ResourceKey)
	}
	aiScripts := req.PlayConfig.GetAiScripts()
	if aiScripts != nil {
		createAttr.PlayConfig.CapsuleAIScripts = lo.Map(aiScripts, func(aiScript *api_items_story_types_v1.CapsuleAIScript, _ int) *domain_entities_items.CapsuleAIScript {
			return &domain_entities_items.CapsuleAIScript{
				Seconds: aiScript.Seconds,
				StoryPlayCapsuleQuestion: &domain_entities_items.StoryPlayCapsuleQuestion{
					Question:    aiScript.Question.Question,
					Thinking:    aiScript.Question.Thinking,
					TTSAudioKey: domain_entities_resource.AudioResourcePath(aiScript.Question.TtsAudioKey),
					Words: lo.Map(aiScript.Question.Words, func(word *api_items_story_types_v1.Word, _ int) *domain_entities_items.Word {
						return &domain_entities_items.Word{
							StartTime: word.StartTime,
							EndTime:   word.EndTime,
							Text:      word.Text,
						}
					}),
				},
			}
		})
	}
	photoInfo := req.PlayConfig.GetPhotoInfo()
	if photoInfo != nil {
		createAttr.PlayConfig.CapsulePhotoInfo = &domain_entities_items.CapsulePhotoInfo{
			InDays:  photoInfo.InDays,
			Moments: photoInfo.Moments,
		}
	}
	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		createAttr.UserMoments = moments
	}
	story, err := s.itemStoryUsecase.CreateCapsuleStory(ctx, user.Id, createAttr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.CreateCapsuleStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) ReportShareStat(ctx context.Context, req *api_items_v1.ReportShareStatRequest) (*api_items_v1.ReportShareStatResponse, error) {
	err := s.itemStoryUsecase.IncreaseShareCount(ctx, cast.ToInt64(req.GetStoryId()))
	if err != nil {
		return nil, err
	}
	return &api_items_v1.ReportShareStatResponse{}, nil
}
func (s *ItemsService) ConsumePinStory(ctx context.Context, req *api_items_story_v2.ConsumePinStoryRequest) (*api_items_story_v2.ConsumePinStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	var failedImage *domain_entities_resource.Resource
	var err error
	if req.GetFailedImage() != nil {
		failedImage, err = adapter_driven_assembler.ConvertDtoToResourceEntity(req.GetFailedImage())
		if err != nil {
			return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid failed image"))
		}
	}
	story, err := s.itemStoryUsecase.ConsumePinStory(
		ctx,
		user.Id,
		cast.ToInt64(req.StoryId),
		req.Success,
		failedImage,
		req.GetSuccessCostSeconds(),
	)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumePinStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) CreatePinStory(ctx context.Context, req *api_items_story_v2.CreatePinStoryRequest) (*api_items_story_v2.CreatePinStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
		CreatorID: user.Id,
	}

	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}

	config := &domain_entities_items.StoryPinConfig{
		Caption: req.PinConfig.Caption,
	}

	var err error

	if config.BackgroundImage, err = adapter_driven_assembler.ConvertDtoToResourceEntity(req.PinConfig.BackgroundImage); err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid background image"))
	}

	for _, emoji := range req.PinConfig.PinEmojiResources {
		emojiEntity := &domain_entities_items.PinEmojiResource{}
		if emoji.GetGeneratedEmojiResource() != nil {
			emojiEntity.GeneratedEmojiResource, err = adapter_driven_assembler.ConvertDtoToResourceEntity(emoji.GetGeneratedEmojiResource())
			if err != nil {
				return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("invalid generated emoji resource"))
			}
		}
		if emoji.GetDefaultEmoji() != "" {
			emojiEntity.DefaultEmoji = emoji.GetDefaultEmoji()
		}
		emojiEntity.Area = &domain_entities_items.Area{
			X:      cast.ToFloat64(emoji.GetArea().GetX()),
			Y:      cast.ToFloat64(emoji.GetArea().GetY()),
			Width:  cast.ToFloat64(emoji.GetArea().GetWidth()),
			Height: cast.ToFloat64(emoji.GetArea().GetHeight()),
		}
		config.PinEmojiResources = append(config.PinEmojiResources, emojiEntity)
	}

	createAttr := domain_services_items_story.CreatePinStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig:          config,
	}

	story, err := s.itemStoryUsecase.CreatePinStory(ctx, user.Id, createAttr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.CreatePinStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) CreateHideStoryV2(ctx context.Context, req *api_items_story_v2.CreateHideStoryRequestV2) (*api_items_story_v2.CreateHideStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
	}

	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		baseAttr.UserMoments = moments
	}
	if req.PlayConfig.Cover != nil {
		baseAttr.CoverImage = domain_entities_items.StoryCoverImage{
			Path:   domain_entities_resource.ImageResourcePath(req.PlayConfig.Cover.GetObjectKey()),
			Width:  int(req.PlayConfig.Cover.CoverImageWidth),
			Height: int(req.PlayConfig.Cover.CoverImageHeight),
		}
	}

	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType:            req.PrivacySetting.PrivacyType,
			VisibleBeforeTimestamp: req.PrivacySetting.VisibleBeforeTimestamp,
		}
	}

	createAttr := domain_services_items_story.CreateHideStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig: &domain_entities_items.StoryPlayHideConfig{
			TriggerData:     make(map[domain_entities_items.StickerTriggerType]*domain_entities_items.StickerTriggerData),
			AttachmentTexts: req.PlayConfig.AttachmentTexts,
		},
	}
	var err error
	if createAttr.PlayConfig.BackgroundImage, err = adapter_driven_assembler.ConvertDtoToResourceEntity(req.PlayConfig.BackgroundImage); err != nil {
		return nil, err
	}
	for _, data := range req.PlayConfig.StickerWithTriggerTypes {
		createAttr.PlayConfig.TriggerData[data.TriggerType] = &domain_entities_items.StickerTriggerData{
			StickerTriggerType: data.TriggerType,
		}
		switch data.TriggerType {
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LONG_PRESS:
			longPressData := &domain_entities_items.StickerWithTriggerTypeLongPressData{
				PressDurationSeconds: lo.If(data.GetLongPressData().GetNeedLongPressDurationSeconds() != 0, data.GetLongPressData().GetNeedLongPressDurationSeconds()).Else(2),
			}
			for _, sticker := range data.GetLongPressData().GetStickers() {
				sticker, err := adapter_driven_assembler.ConvertStickerDtoToEntity(sticker)
				if err != nil {
					return nil, err
				}
				longPressData.Stickers = append(longPressData.Stickers, sticker)
			}
			createAttr.PlayConfig.TriggerData[data.TriggerType].LongPressData = longPressData
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LIKE:
			likeData := &domain_entities_items.StickerWithTriggerTypeLikeData{}
			for _, sticker := range data.GetLikeData().GetStickers() {
				sticker, err := adapter_driven_assembler.ConvertStickerDtoToEntity(sticker)
				if err != nil {
					return nil, err
				}
				likeData.Stickers = append(likeData.Stickers, sticker)
			}
			createAttr.PlayConfig.TriggerData[data.TriggerType].LikeData = likeData
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_DRAG:
			dragData := &domain_entities_items.StickerWithTriggerTypeDragData{}
			for _, cutObject := range data.GetDragData().GetCutObjects() {
				sticker, err := adapter_driven_assembler.ConvertStickerDtoToEntity(cutObject.Sticker)
				if err != nil {
					return nil, err
				}
				dragData.CutObjects = append(dragData.CutObjects, &domain_entities_items.StickerWithTriggerTypeDragData_CutObject{
					Sticker:      sticker,
					MaskImageKey: domain_entities_resource.ImageResourcePath(cutObject.MaskImageKey),
				})
			}
			createAttr.PlayConfig.TriggerData[data.TriggerType].DragData = dragData
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_SHAKE:
			shakeData := &domain_entities_items.StickerWithTriggerTypeShakeData{
				NeedShakeCount:           lo.If(data.GetShakeData().GetNeedShakeCount() != 0, data.GetShakeData().GetNeedShakeCount()).Else(5),
				NeedShakeDurationSeconds: lo.If(data.GetShakeData().GetNeedShakeDurationSeconds() != 0, data.GetShakeData().GetNeedShakeDurationSeconds()).Else(2),
			}
			for _, sticker := range data.GetShakeData().GetStickers() {
				sticker, err := adapter_driven_assembler.ConvertStickerDtoToEntity(sticker)
				if err != nil {
					return nil, err
				}
				shakeData.Stickers = append(shakeData.Stickers, sticker)
			}
			createAttr.PlayConfig.TriggerData[data.TriggerType].ShakeData = shakeData
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK:
			clickData := &domain_entities_items.StickerWithTriggerTypeContinuousClickData{
				NeedClickCount: lo.If(data.GetContinuousClickData().GetNeedClickCount() != 0, data.GetContinuousClickData().GetNeedClickCount()).Else(5),
			}
			for _, sticker := range data.GetContinuousClickData().StickersWithClickLocationsInCreation {
				stickerEntity, err := adapter_driven_assembler.ConvertStickerDtoToEntity(sticker)
				if err != nil {
					return nil, err
				}
				clickData.Stickers = append(clickData.Stickers, stickerEntity)
			}
			createAttr.PlayConfig.TriggerData[data.TriggerType].ContinuousClickData = clickData
		}
	}
	story, err := s.itemStoryUsecase.CreateHideStory(ctx, user.Id, createAttr)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.CreateHideStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) GenerateHideImageMask(ctx context.Context, req *api_items_story_v2.GenerateHideImageMaskRequest) (*api_items_story_v2.GenerateHideImageMaskResponse, error) {
	mask, err := s.itemStoryUsecase.GenerateImageMask(ctx, domain_entities_resource.ImageResourcePath(req.BackgroundImageKey), lo.Map(req.Points, func(item *api_items_story_v2.GenerateHideImageMaskRequest_Point, _ int) *domain_entities_items.Point {
		return &domain_entities_items.Point{
			X: item.X,
			Y: item.Y,
		}
	}))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.GenerateHideImageMaskResponse{
		MaskImageBase64: mask,
	}, nil
}

func buildPrivacySettingUpdateAttr(PrivacySetting *api_items_story_v1.PrivacySettingUpdateAttr) *domain_services_items_story.PrivacySettingUpdateAttr {
	privacySetting := &domain_services_items_story.PrivacySettingUpdateAttr{
		PrivacyType:            PrivacySetting.PrivacyType,
		VisibleBeforeTimestamp: PrivacySetting.VisibleBeforeTimestamp,
	}
	if *PrivacySetting.PrivacyType == api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_ALLOWLIST {
		privacySetting.AllowlistUserIds = lo.Map(PrivacySetting.AllowlistUserIds, func(userId string, _ int) int64 {
			return cast.ToInt64(userId)
		})
	}
	return privacySetting
}

// CreateNowShotStoryV2 创建NowShot故事的V2版本
func (s *ItemsService) CreateNowShotStoryV2(ctx context.Context, req *api_items_story_v2.CreateNowShotStoryRequestV2) (*api_items_story_v2.StoryDetailResponseV2, error) {
	user := s.GetAuthUser(ctx)
	playConfig := req.PlayConfig.CommonConfig

	baseAttr := domain_services_items_story.StoryBaseCreateAttr{
		CreatorID: user.Id,
		CoverImage: domain_entities_items.StoryCoverImage{
			Path:   domain_entities_resource.ImageResourcePath(playConfig.Cover.ResourceKey),
			Width:  int(playConfig.Cover.CoverWidth),
			Height: int(playConfig.Cover.CoverHeight),
		},
		Version: lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
	}

	if req.PlayConfig.MomentCreateAttrs != nil {
		moments := make([]*domain_entities_items.MomentExtraInfo, 0)
		for _, moment := range req.PlayConfig.MomentCreateAttrs {
			momentEntity, err := adapter_driven_assembler.ConvertDtoToMomentExtraInfoEntity(moment)
			if err != nil {
				return nil, err
			}
			moments = append(moments, momentEntity)
		}
		baseAttr.UserMoments = moments
	}

	if req.PrivacySetting != nil {
		baseAttr.PrivacySettings = buildPrivacySettingUpdateAttr(req.PrivacySetting)
	}

	// 构建内部cover结构
	cover := playConfig.Cover
	var internalCover *domain_entities_resource.CoverImage
	switch cover.CoverType {
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ResourceKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType:    cover.CoverType,
			ResourceKey:  cover.ResourceKey,
			ThumbnailKey: domain_entities_resource.ImageResourcePath(cover.ThumbnailKey),
			Height:       cover.CoverHeight,
			Width:        cover.CoverWidth,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
			Gradient:  cover.Gradient,
		}
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_NONE:
		internalCover = &domain_entities_resource.CoverImage{
			CoverType: cover.CoverType,
		}
	}

	cfg := &domain_entities_items.StoryPlayNowShotConfig{
		TTL: req.PlayConfig.Ttl,
		CommonConfig: &domain_entities_items.CommonConfig{
			Cover: internalCover,
			Resource: &domain_entities_items.Resource{
				Type:             playConfig.Resource.ResourceType,
				Key:              playConfig.Resource.ResourceKey,
				CoverImageKey:    domain_entities_resource.ImageResourcePath(playConfig.Resource.CoverImageKey),
				CoverImageWidth:  int32(playConfig.Resource.CoverWidth),
				CoverImageHeight: int32(playConfig.Resource.CoverHeight),
			},
			CoverCaptions:    playConfig.CoverCaptions,
			ResourceCaptions: playConfig.ResourceCaptions,
			MaxTryCount:      playConfig.MaxTryCount,
			Condition: &domain_entities_items.Condition{
				Hint:   playConfig.ConditionV2.Hint,
				Prompt: playConfig.ConditionV2.Prompt,
			},
		},
	}

	story, err := s.itemStoryUsecase.CreateNowShotStory(ctx, user.Id, domain_services_items_story.CreateNowShotStoryAttr{
		StoryBaseCreateAttr: baseAttr,
		PlayConfig:          cfg,
	})
	if err != nil {
		return nil, err
	}

	if req.GetFromStoryId() != "" {
		if err := s.updateFromStoryPostMomentStatus(ctx, user.Id, story.Summary.Id, cast.ToInt64(req.GetFromStoryId())); err != nil {
			return nil, err
		}
	}

	return &api_items_story_v2.StoryDetailResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) updateFromStoryPostMomentStatus(ctx context.Context, userId int64, storyId int64, fromStoryId int64) error {
	portals, err := s.itemStoryUsecase.BatchGetPortalsWithStoryIds(ctx, userId, []int64{storyId})
	if err != nil {
		return err
	}
	portal := portals[storyId]
	if portal != nil && len(portal.Moments) > 0 {
		return s.itemStoryUsecase.UpdateStoryPostMomentStatus(ctx, userId, fromStoryId)
	}
	return nil
}
