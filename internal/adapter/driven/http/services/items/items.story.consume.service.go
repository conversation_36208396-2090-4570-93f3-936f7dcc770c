package adapter_driven_http_services_items

import (
	"context"

	"github.com/samber/lo"

	api_errors_v1 "boson/api/errors/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_items_story_v1 "boson/api/items/story/v1"
	api_items_story_v2 "boson/api/items/story/v2"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

func (s *ItemsService) ConsumeWhoStoryV2(ctx context.Context, req *api_items_story_v2.ConsumeWhoStoryRequestV2) (*api_items_story_v2.ConsumeWhoStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)

	var afterDetail *domain_entities_items.StoryDetail
	var err error
	if req.GetTryPoints() != nil {
		afterDetail, err = s.itemStoryUsecase.ConsumeWhoStoryAddTryPoint(ctx, user.Id, cast.ToInt64(req.StoryId), req.GetTryPoints()...)
	} else {
		afterDetail, err = s.itemStoryUsecase.ConsumeWhoStorySelectOption(ctx, user.Id, cast.ToInt64(req.StoryId), cast.ToInt64(req.SelectedOptionUserId))
	}
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumeWhoStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(afterDetail),
	}, nil
}

func (s *ItemsService) ConsumeChatProxy(ctx context.Context, req *api_items_story_v2.ConsumeChatProxyRequestV2) (*api_items_story_v2.ConsumeChatProxyResponseV2, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.ConsumeChatProxy(
		ctx,
		user.Id,
		cast.ToInt64(req.StoryId),
	)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumeChatProxyResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) GetChatProxyNextTopic(ctx context.Context, in *api_items_story_v2.GetChatProxyNextTopicRequestV2) (*api_items_story_v2.GetChatProxyNextTopicResponseV2, error) {
	user := s.GetAuthUser(ctx)
	story, nextquestion, end, err := s.itemStoryUsecase.GetChatProxyNextTopics(ctx, user.Id, cast.ToInt64(in.StoryId), in.UserAudioKey, in.RoundIndex)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.GetChatProxyNextTopicResponseV2{
		Question:    nextquestion,
		End:         end,
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) ConsumeHideStory(ctx context.Context, req *api_items_story_v2.ConsumeHideStoryRequest) (*api_items_story_v2.ConsumeHideStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.ConsumeHideStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.StickerIds...)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumeHideStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) ConsumeRoastedTopic(ctx context.Context, req *api_items_story_v1.ConsumeRoastedTopicRequest) (*api_items_story_v1.ConsumeRoastedTopicResponse, error) {
	user := s.GetAuthUser(ctx)
	var fromStoryId *int64
	if req.FromStoryId != nil {
		fid := cast.ToInt64(req.FromStoryId)
		fromStoryId = &fid
	}
	nextQ, err := s.itemStoryUsecase.ConsumeRoastedTopic(
		ctx,
		user.Id,
		fromStoryId,
		req.QuestionWithUserAnswer.UserVoiceKey,
		req.QuestionCount,
		req.TotalVideoDurationSeconds,
		&domain_entities_items.StoryPlayRoastedQuestion{
			Question:    req.QuestionWithUserAnswer.Question.Question,
			Thinking:    req.QuestionWithUserAnswer.Question.Thinking,
			TTSAudioKey: domain_entities_resource.AudioResourcePath(req.QuestionWithUserAnswer.Question.TtsAudioKey),
		},
	)
	if err != nil {
		return nil, err
	}
	res := &api_items_story_v1.ConsumeRoastedTopicResponse{}
	if nextQ != nil {
		res.NextQuestion = &api_items_story_types_v1.RoastedQuestion{
			Question:    nextQ.Question,
			TtsAudioUrl: nextQ.TTSAudioKey.URL(),
			TtsAudioKey: string(nextQ.TTSAudioKey),
			Thinking:    nextQ.Thinking,
			Words: lo.Map(nextQ.Words, func(word domain_entities_items.Word, _ int) *api_items_story_types_v1.Word {
				return &api_items_story_types_v1.Word{
					Text:      word.Text,
					StartTime: word.StartTime,
					EndTime:   word.EndTime,
				}
			}),
		}
	}
	return res, nil
}
func (s *ItemsService) ConsumeBasePlayStory(ctx context.Context, req *api_items_story_v1.ConsumeBasePlayStoryRequest) (*api_items_story_v1.ConsumeBasePlayStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.PlayBasePlayStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.CurrentNodeIdx)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeBasePlayStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}
func (s *ItemsService) ConsumeTurtleSoupStory(ctx context.Context, req *api_items_story_v1.ConsumeTurtleSoupStoryRequest) (*api_items_story_v1.ConsumeTurtleSoupStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.PlayTurtleSoupStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.UserMessage)
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeTurtleSoupStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) ConsumeRoastedStory(ctx context.Context, req *api_items_story_v1.ConsumeRoastedStoryRequest) (*api_items_story_v1.ConsumeRoastedStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.PlayRoastedStory(ctx, user.Id, cast.ToInt64(req.StoryId))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeRoastedStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) ConsumeRoastedStoryV2(ctx context.Context, req *api_items_story_v2.ConsumeRoastedStoryRequestV2) (*api_items_story_v2.ConsumeRoastedStoryResponseV2, error) {
	user := s.GetAuthUser(ctx)
	story, err := s.itemStoryUsecase.PlayRoastedStory(ctx, user.Id, cast.ToInt64(req.StoryId))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v2.ConsumeRoastedStoryResponseV2{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) ConsumeExchangeImageStory(ctx context.Context, req *api_items_story_v1.ConsumeExchangeImageStoryRequest) (*api_items_story_v1.ConsumeExchangeImageStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, aiResponse, matchStatus, err := s.itemStoryUsecase.PlayExchangeImageStory(ctx, user.Id, cast.ToInt64(req.StoryId), domain_entities_resource.ImageResourcePath(req.ImageKey))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeExchangeImageStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
		MatchStatus: matchStatus.String(),
		AiResponse:  aiResponse,
	}, nil
}

func (s *ItemsService) ConsumeUnmuteStory(ctx context.Context, req *api_items_story_v1.ConsumeUnmuteStoryRequest) (*api_items_story_v1.ConsumeUnmuteStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	story, aiResponse, matchStatus, err := s.itemStoryUsecase.PlayUnmuteStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.GetUserAudioKey())
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeUnmuteStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
		MatchStatus: matchStatus.String(),
		AiResponse:  aiResponse,
	}, nil
}

func (s *ItemsService) ConsumeNowShotStory(ctx context.Context, req *api_items_story_v1.ConsumeNowShotStoryRequest) (*api_items_story_v1.ConsumeNowShotStoryResponse, error) {
	user := s.GetAuthUser(ctx)
	var story *domain_entities_items.StoryDetail
	var err error
	resourceType := req.GetResourceType()
	switch resourceType {
	case api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_VIDEO.String():
		story, err = s.itemStoryUsecase.PlayNowShotStory(ctx, user.Id, cast.ToInt64(req.StoryId), api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_VIDEO, domain_entities_resource.ImageResourcePath(req.GetThumbnailUrl()), domain_entities_resource.VideoResourcePath(req.GetUserResourceKey()), req.GetStartTime())
	default:
		story, err = s.itemStoryUsecase.PlayNowShotStory(ctx, user.Id, cast.ToInt64(req.StoryId), api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_IMAGE, domain_entities_resource.ImageResourcePath(req.GetUserResourceKey()), "", req.GetStartTime())
	}
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeNowShotStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

func (s *ItemsService) ConsumeCapsuleStory(ctx context.Context, req *api_items_story_v1.ConsumeCapsuleStoryRequest) (*api_items_story_v1.ConsumeCapsuleStoryResponse, error) {
	user := s.GetAuthUser(ctx)

	story, err := s.itemStoryUsecase.PlayCapsuleStory(ctx, user.Id, cast.ToInt64(req.StoryId), req.GetSendToAuthor(), cast.ToInt64(req.SavedStoryId))
	if err != nil {
		return nil, err
	}
	return &api_items_story_v1.ConsumeCapsuleStoryResponse{
		StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
	}, nil
}

// ConsumeNowShotStoryV2 消费NowShot故事的V2版本
// 在V2版本中，流程判定逻辑从服务端移到了客户端
func (s *ItemsService) ConsumeNowShotStoryV2(ctx context.Context, req *api_items_story_v2.ConsumeNowShotStoryRequestV2) (*api_items_story_v2.StoryDetailResponseV2, error) {
	user := s.GetAuthUser(ctx)

	if req.Succeeded {
		// 客户端判断成功，需要处理resource
		if req.Resource == nil {
			return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("resource is required when succeeded is true"))
		}

		resource := req.Resource
		var story *domain_entities_items.StoryDetail
		var err error

		switch resource.ResourceType {
		case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
			story, err = s.itemStoryUsecase.PlayNowShotStoryV2(
				ctx,
				user.Id,
				cast.ToInt64(req.StoryId),
				true, // succeeded
				api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_VIDEO,
				domain_entities_resource.ImageResourcePath(resource.CoverImageKey),
				domain_entities_resource.VideoResourcePath(resource.ResourceKey),
				req.Ttl,
			)
		default:
			story, err = s.itemStoryUsecase.PlayNowShotStoryV2(
				ctx,
				user.Id,
				cast.ToInt64(req.StoryId),
				true, // succeeded
				api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_IMAGE,
				domain_entities_resource.ImageResourcePath(resource.ResourceKey),
				"",
				req.Ttl,
			)
		}

		if err != nil {
			return nil, err
		}

		return &api_items_story_v2.StoryDetailResponseV2{
			StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
		}, nil
	} else {
		// 客户端判断失败，直接记录失败结果
		story, err := s.itemStoryUsecase.PlayNowShotStoryV2(
			ctx,
			user.Id,
			cast.ToInt64(req.StoryId),
			false, // succeeded
			api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_IMAGE, // 默认值，因为失败时不重要
			"",
			"",
			req.Ttl,
		)

		if err != nil {
			return nil, err
		}

		return &api_items_story_v2.StoryDetailResponseV2{
			StoryDetail: adapter_driven_assembler.ConvertStoryDetailToDto(story),
		}, nil
	}
}
