package adapter_driven_http_services_users

import (
	"context"

	api_users_recommend_v1 "boson/api/users/recommend/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_services_users_recommend "boson/internal/domain/services/users/recommend"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

func (s *UsersService) RecommendUsers(ctx context.Context, req *api_users_recommend_v1.RecommendUsersRequest) (*api_users_recommend_v1.RecommendUsersResponse, error) {
	loginUser := s.GetAuthUser(ctx)
	var profileUserId *int64
	if req.ProfileUserId != nil {
		profileUserId = lo.ToPtr(cast.ToInt64(*req.ProfileUserId))
	}
	recommendUsers, sortRequestId, res, err := s.usersRecommendUsecase.RecommendUsers(ctx, loginUser.Id, profileUserId, req.<PERSON><PERSON>, req.ListRequest)
	if err != nil {
		return nil, err
	}
	return &api_users_recommend_v1.RecommendUsersResponse{
		Users: lo.Map(recommendUsers, func(item *domain_services_users_recommend.RecommendUser, _ int) *api_users_recommend_v1.RecommendUsersResponse_RecommendUser {
			recommendUser := &api_users_recommend_v1.RecommendUsersResponse_RecommendUser{
				RecommendReason: item.RecommendReason,
				User:            adapter_driven_assembler.ConvertUserInfoToSummary(item.UserInfo),
				SortRequestId:   sortRequestId,
				RelatedStoryId:  item.RelatedStoryId,
			}
			if item.RecommendReasonExtra != nil {
				recommendUser.RecommendReasonExtra = &api_users_recommend_v1.RecommendUsersResponse_RecommendUser_RecommendReasonExtra{
					ReasonType:   item.RecommendReasonExtra.ReasonType,
					RelatedUsers: adapter_driven_assembler.BatchConverUserInfosToSummaries(item.RecommendReasonExtra.RelatedUsers),
				}
			}
			return recommendUser
		}),
		SortRequestId: sortRequestId,
		ListResponse:  res,
	}, nil
}
