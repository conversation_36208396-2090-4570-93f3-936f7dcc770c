package adapter_driven_assembler

import (
	api_items_story_reaction_types_v1 "boson/api/items/story/reaction/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_users_info_types_v1 "boson/api/users/info/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	"fmt"
	"strconv"

	"github.com/samber/lo"
)

func ConvertStoryToDto(stories ...*domain_entities_items.StorySummary) []*api_items_story_types_v1.StorySummary {
	return lo.Map(stories, func(story *domain_entities_items.StorySummary, _ int) *api_items_story_types_v1.StorySummary {
		s := &api_items_story_types_v1.StorySummary{
			Id:                    fmt.Sprintf("%d", story.Id),
			Status:                story.Status,
			Author:                ConvertUserInfoT<PERSON><PERSON><PERSON><PERSON><PERSON>(story.Author),
			PlayType:              story.PlayType.String(),
			SubPlayType:           story.SubPlayType.String(),
			CreatedAtTimestamp:    uint32(story.CreatedAt.Unix()),
			ExchangeImageExample:  story.PlayExchangeImageExample,
			TurtleSoupExample:     story.PlayTurtleSoupExample,
			TurtleSoupMassExample: story.PlayTurtleSoupMassExample,
			UnmuteExample:         story.PlayUnmuteExample,
			BasePlayExample:       story.PlayBasePlayExample,
			NowShotPlayExample:    story.PlayNowShotExample,
			RoastedExample:        story.PlayRoastedExample,
			CapsuleExample:        story.PlayCapsuleExample,
			PrivacySetting:        &api_items_story_types_v1.PrivacySetting{},
			Stats: &api_items_story_types_v1.StoryStats{
				ReactionMadeStatSummary: &api_items_story_reaction_types_v1.ReactionMadeStatSummary{
					EmojiStats: []*api_items_story_reaction_types_v1.ReactionMadeStatSummary_EmojiStat{},
				},
			},
			LoginUserMadeReactions: lo.Map(story.LoginUserMadeReactions, func(reaction *domain_entities_items.StoryReaction, _ int) *api_items_story_reaction_types_v1.Reaction {
				return &api_items_story_reaction_types_v1.Reaction{
					Emoji:              reaction.EmojiStr,
					Comment:            reaction.Comment,
					CreatedAtTimestamp: uint32(reaction.CreatedAt.Unix()),
				}
			}),
			SortRequestId: story.SortRequestId,
			HasUnlocked:   story.HasUnlocked,
		}
		// version v1 cover
		if story.StoryCoverImage != nil {
			s.CoverImageUrl = story.StoryCoverImage.Path.ItemPosterInSummary()
			s.CoverImageWidth = uint32(story.StoryCoverImage.Width)
			s.CoverImageHeight = uint32(story.StoryCoverImage.Height)
			s.CoverType = story.StoryCoverImage.Type.String()
		}
		// version v2 cover
		if story.Version == domain_entities_items.StoryVersion_V2 {
			var cover *domain_entities_resource.CoverImage
			switch story.PlayType {
			case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
				cover = story.ExchangeImagePlayConfig.CommonConfig.Cover
			case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
				cover = story.TurtleSoupPlayConfig.CommonConfig.Cover
			case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
				cover = story.UnmutePlayConfig.CommonConfig.Cover
			case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
				cover = story.NowShotConfig.CommonConfig.Cover
			}
			if cover != nil {
				s.CoverImageUrl = cover.GetThumbnailURL()
				s.CoverType = cover.CoverType.String()
			}
		}
		if story.UnlockedUsersInfo != nil {
			s.UnlockedUsers = &api_items_story_types_v1.StorySummary_UnlockedUsersInfo{
				Users: lo.Map(story.UnlockedUsersInfo.Users, func(user *domain_entities_users.UserSummaryEntity, _ int) *api_users_info_types_v1.UserInfoSummary {
					return ConvertUserInfoToSummary(user)
				}),
				TotalCount: story.UnlockedUsersInfo.Total,
				HasMore:    story.UnlockedUsersInfo.Total > uint32(len(story.UnlockedUsersInfo.Users)),
			}
		}

		if story.Stats != nil {
			s.Stats.ShareStat = story.Stats.ShareStat
			if story.Stats.ReactionEmojiStats != nil {
				for emojiStr, count := range story.Stats.ReactionEmojiStats {
					s.Stats.ReactionMadeStatSummary.EmojiStats = append(s.Stats.ReactionMadeStatSummary.EmojiStats, &api_items_story_reaction_types_v1.ReactionMadeStatSummary_EmojiStat{
						Emoji: emojiStr,
						Count: count,
					})
				}
			}
		}
		if story.PrivacySettings != nil {
			s.PrivacySetting = &api_items_story_types_v1.PrivacySetting{
				PrivacyType:            story.PrivacySettings.PrivacyType,
				VisibleBeforeTimestamp: story.PrivacySettings.VisibleBeforeTimestamp,
			}
		}
		return s
	})
}

func ConvertStoryDetailToDto(story *domain_entities_items.StoryDetail) *api_items_story_types_v1.StoryDetail {
	if story == nil {
		return nil
	}
	detail := &api_items_story_types_v1.StoryDetail{
		Summary: ConvertStoryToDto(story.Summary)[0],
		EnabledPortalId: lo.If(story.JoinedPortalId != 0, lo.ToPtr(
			fmt.Sprintf("%d", story.JoinedPortalId),
		)).Else(nil),
	}
	if story.HauntShowInfo != nil {
		detail.HauntBooShowInfo = ConvertHauntBooShowInfoToDto(story.HauntShowInfo)
	}
	injectImageExchangeConfigAndContext(story, detail)
	injectTurtleSoupConfigAndContext(story, detail)
	injectUnmuteConfigAndContext(story, detail)
	injectBasePlayConfigAndContext(story, detail)
	injectNowShotConfigAndContext(story, detail)
	injectRoastedConfigAndContext(story, detail)
	injectCapsuleConfigAndContext(story, detail)
	injectHideConfigAndContext(story, detail)
	injectChatProxyConfigAndContext(story, detail)
	injectWhoConfigAndContext(story, detail)
	injectWassupConfigAndContext(story, detail)
	injectHauntConfigAndContext(story, detail)
	injectPinConfigAndContext(story, detail)
	return detail
}

func ConvertStickerDtoToEntity(sticker *api_items_story_types_v1.HideSticker) (*domain_entities_items.HideSticker, error) {
	s := &domain_entities_items.HideSticker{
		Resource: &domain_entities_resource.Resource{},
		Transform: &domain_entities_items.StickerTransform{
			Location: &domain_entities_items.StickerTransform_Location{
				X: sticker.Transform.Location.X,
				Y: sticker.Transform.Location.Y,
			},
			Size: &domain_entities_items.StickerTransform_Size{
				Width:  sticker.Transform.Size.Width,
				Height: sticker.Transform.Size.Height,
			},
		},
	}
	if sticker.Resource != nil {
		resoruceEntity, err := ConvertDtoToResourceEntity(sticker.Resource)
		if err != nil {
			return nil, err
		}
		s.Resource = resoruceEntity
	}
	return s, nil
}

func ConvertHideStoryStickerToDtos(stickers []*domain_entities_items.HideSticker) []*api_items_story_types_v1.HideSticker {
	return lo.Map(stickers, func(sticker *domain_entities_items.HideSticker, _ int) *api_items_story_types_v1.HideSticker {
		return ConvertHideStoryStickerToDto(sticker)
	})
}

func ConvertHideStoryStickerToDto(sticker *domain_entities_items.HideSticker) *api_items_story_types_v1.HideSticker {
	dto := &api_items_story_types_v1.HideSticker{
		Id:          sticker.Id,
		FromStoryId: strconv.FormatInt(sticker.FromStoryId, 10),
		Resource:    ConvertResourceEntityToDto(sticker.Resource),
	}
	if sticker.Transform != nil {
		dto.Transform = &api_items_story_types_v1.StickerTransform{
			Location: &api_items_story_types_v1.StickerTransform_Location{
				X: sticker.Transform.Location.X,
				Y: sticker.Transform.Location.Y,
			},
		}
		if sticker.Transform.Size != nil {
			dto.Transform.Size = &api_items_story_types_v1.StickerTransform_Size{
				Width:  sticker.Transform.Size.Width,
				Height: sticker.Transform.Size.Height,
			}
		}
	}
	return dto
}

func ConvertHideStickerTriggerDataToDto(data *domain_entities_items.StickerTriggerData) *api_items_story_types_v1.StickerWithTriggerType {
	switch data.StickerTriggerType {
	case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK:
		return &api_items_story_types_v1.StickerWithTriggerType{
			TriggerType: api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK,
			Data: &api_items_story_types_v1.StickerWithTriggerType_ContinuousClickData{
				ContinuousClickData: &api_items_story_types_v1.StickerWithTriggerTypeContinuousClickData{
					NeedClickCount:                       data.ContinuousClickData.NeedClickCount,
					StickersWithClickLocationsInCreation: ConvertHideStoryStickerToDtos(data.ContinuousClickData.Stickers),
					StickersWithClickLocationsInConsume: lo.Map(data.ContinuousClickData.GetStickersWithClickLocationAggregation(), func(item *domain_entities_items.StickerWithClickLocationAggregation, _ int) *api_items_story_types_v1.StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume {
						return &api_items_story_types_v1.StickerWithTriggerTypeContinuousClickData_StickersWithClickLocationInConsume{
							Stickers: ConvertHideStoryStickerToDtos(item.Stickers),
							X:        item.Location.X,
							Y:        item.Location.Y,
						}
					}),
				},
			},
		}
	case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_DRAG:
		return &api_items_story_types_v1.StickerWithTriggerType{
			TriggerType: api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_DRAG,
			Data: &api_items_story_types_v1.StickerWithTriggerType_DragData{
				DragData: &api_items_story_types_v1.StickerWithTriggerTypeDragData{
					CutObjects: lo.Map(data.DragData.CutObjects, func(item *domain_entities_items.StickerWithTriggerTypeDragData_CutObject, _ int) *api_items_story_types_v1.StickerWithTriggerTypeDragData_CutObject {
						return &api_items_story_types_v1.StickerWithTriggerTypeDragData_CutObject{
							MaskImageKey: string(item.MaskImageKey),
							MaskImageUrl: item.MaskImageKey.ItemPosterInSummary(),
							Sticker:      ConvertHideStoryStickerToDto(item.Sticker),
						}
					}),
				},
			},
		}
	case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LIKE:
		return &api_items_story_types_v1.StickerWithTriggerType{
			TriggerType: api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LIKE,
			Data: &api_items_story_types_v1.StickerWithTriggerType_LikeData{
				LikeData: &api_items_story_types_v1.StickerWithTriggerTypeLikeData{
					Stickers: lo.Map(data.LikeData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *api_items_story_types_v1.HideSticker {
						return ConvertHideStoryStickerToDto(item)
					}),
				},
			},
		}
	case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LONG_PRESS:
		if data.LongPressData == nil {
			return nil
		}
		return &api_items_story_types_v1.StickerWithTriggerType{
			TriggerType: api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LONG_PRESS,
			Data: &api_items_story_types_v1.StickerWithTriggerType_LongPressData{
				LongPressData: &api_items_story_types_v1.StickerWithTriggerTypeLongPressData{
					Stickers: lo.Map(data.LongPressData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *api_items_story_types_v1.HideSticker {
						return ConvertHideStoryStickerToDto(item)
					}),
					NeedLongPressDurationSeconds: data.LongPressData.PressDurationSeconds,
				},
			},
		}
	case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_SHAKE:
		return &api_items_story_types_v1.StickerWithTriggerType{
			TriggerType: api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_SHAKE,
			Data: &api_items_story_types_v1.StickerWithTriggerType_ShakeData{
				ShakeData: &api_items_story_types_v1.StickerWithTriggerTypeShakeData{
					NeedShakeCount: data.ShakeData.NeedShakeCount,
					Stickers: lo.Map(data.ShakeData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *api_items_story_types_v1.HideSticker {
						return ConvertHideStoryStickerToDto(item)
					}),
					NeedShakeDurationSeconds: data.ShakeData.NeedShakeDurationSeconds,
				},
			},
		}
	}
	return nil
}
func injectHauntConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.Summary.HauntPlayConfig != nil {
		cfg := &api_items_story_types_v1.StoryDetail_HauntConfig{
			HauntConfig: &api_items_story_types_v1.HauntPlayConfig{
				Captions: story.Summary.HauntPlayConfig.Captions,
			},
		}
		if story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers != nil {
			cfg.HauntConfig.BoosWithQuestionsAndAnswers = ConvertHauntBooToDto(story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers)
		}
		cfg.HauntConfig.BoosWithQuestionsAndAnswers = append(
			cfg.HauntConfig.BoosWithQuestionsAndAnswers,
			ConvertHauntBooToDto(story.Summary.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers...)...,
		)
		detail.PlayConfig = cfg
	}
	if story.HauntPlayContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_HauntContext{
			HauntContext: &api_items_story_types_v1.HauntPlayContext{
				TryCount: story.HauntPlayContext.TryCount,
				Unlocked: story.HauntPlayContext.Unlocked,
			},
		}
	}
}

func injectWassupConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.Summary.WassupPlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_WassupConfig{
			WassupConfig: &api_items_story_types_v1.StoryPlayWassupConfig{
				UnlockResource:     ConvertResourceEntityToDto(story.Summary.WassupPlayConfig.UnlockResource),
				CoverImageTexts:    ConvertAttachmentTextsToDto(story.Summary.WassupPlayConfig.CoverImageTexts),
				CoverImageResource: ConvertResourceEntityToDto(story.Summary.WassupPlayConfig.CoverResource),
				IsMassCover:        story.Summary.WassupPlayConfig.IsMassCover,
			},
		}
		if story.WassupPlayContext != nil {
			detail.PlayContext = &api_items_story_types_v1.StoryDetail_WassupContext{
				WassupContext: &api_items_story_types_v1.StoryPlayWassupContext{
					IsUnlocked: story.WassupPlayContext.IsUnlocked,
				},
			}
		}
	}
}
func injectHideConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.HidePlayContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_HideContext{
			HideContext: &api_items_story_types_v1.StoryPlayHideContext{
				IsConsumed: story.HidePlayContext.IsConsumed,
				UnlockedStickers: ConvertHideStoryStickerToDtos(lo.Map(story.HidePlayContext.UnlockedStickers, func(item *domain_entities_items.UnlockSticker, _ int) *domain_entities_items.HideSticker {
					return item.Sticker
				})),
			},
		}
	}
	if story.Summary.HidePlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_HideConfig{
			HideConfig: &api_items_story_types_v1.StoryPlayHideConfig{
				StickerWithTriggerTypes: lo.MapToSlice(story.Summary.HidePlayConfig.TriggerData, func(
					_ domain_entities_items.StickerTriggerType,
					item *domain_entities_items.StickerTriggerData,
				) *api_items_story_types_v1.StickerWithTriggerType {
					return ConvertHideStickerTriggerDataToDto(item)
				}),
				BackgroundImage: ConvertResourceEntityToDto(story.Summary.HidePlayConfig.BackgroundImage),
				AttachmentTexts: story.Summary.HidePlayConfig.AttachmentTexts,
				AllStickers: lo.Map(story.Summary.HidePlayConfig.GetStickers(), func(item *domain_entities_items.HideSticker, _ int) *api_items_story_types_v1.HideSticker {
					return ConvertHideStoryStickerToDto(item)
				}),
			},
		}
	}
}

func injectPinConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.Summary.PinPlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_PinConfig{
			PinConfig: &api_items_story_types_v1.StoryPinConfig{
				BackgroundImage: ConvertResourceEntityToDto(story.Summary.PinPlayConfig.BackgroundImage),
				PinEmojiResources: lo.Map(story.Summary.PinPlayConfig.PinEmojiResources, func(item *domain_entities_items.PinEmojiResource, _ int) *api_items_story_types_v1.PinEmojiResource {
					dto := &api_items_story_types_v1.PinEmojiResource{
						Area: &api_items_story_types_v1.PinEmojiArear{
							X:      strconv.FormatFloat(item.Area.X, 'f', -1, 64),
							Y:      strconv.FormatFloat(item.Area.Y, 'f', -1, 64),
							Width:  strconv.FormatFloat(item.Area.Width, 'f', -1, 64),
							Height: strconv.FormatFloat(item.Area.Height, 'f', -1, 64),
						},
					}
					if item.DefaultEmoji != "" {
						dto.Data = &api_items_story_types_v1.PinEmojiResource_DefaultEmoji{
							DefaultEmoji: item.DefaultEmoji,
						}
					}
					if item.GeneratedEmojiResource != nil {
						dto.Data = &api_items_story_types_v1.PinEmojiResource_GeneratedEmojiResource{
							GeneratedEmojiResource: ConvertResourceEntityToDto(item.GeneratedEmojiResource),
						}
					}
					return dto
				}),
				Caption:           story.Summary.PinPlayConfig.Caption,
				MomentCreateAttrs: []*api_items_story_types_v1.MomentCreateAttr{},
			},
		}
		if story.PinPlayContext != nil {
			detail.PlayContext = &api_items_story_types_v1.StoryDetail_PinContext{
				PinContext: &api_items_story_types_v1.StoryPinContext{
					IsUnlocked: story.PinPlayContext.IsUnlocked,
				},
			}
		}
	}
}

func injectWhoConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.Summary.WhoPlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_WhoConfig{
			WhoConfig: &api_items_story_types_v1.WhoStoryPlayConfig{
				UnlockResourceTexts: ConvertAttachmentTextsToDto(story.Summary.WhoPlayConfig.UnlockAttachmentTexts),
				UnlockResource:      ConvertResourceEntityToDto(story.Summary.WhoPlayConfig.UnlockResource),
				CoverResourceTexts:  ConvertAttachmentTextsToDto(story.Summary.WhoPlayConfig.CoverAttachmentTexts),
				CoverResource:       ConvertResourceEntityToDto(story.Summary.WhoPlayConfig.CoverResource),
				OptionUserIds: lo.Map(story.Summary.WhoPlayConfig.OptionUserIds, func(item int64, _ int) string {
					return strconv.FormatInt(item, 10)
				}),
				Options: lo.Map(story.Summary.WhoPlayConfig.OptionUsers, func(item *domain_entities_users.UserSummaryEntity, _ int) *api_items_story_types_v1.WhoStoryPlayOption {
					return &api_items_story_types_v1.WhoStoryPlayOption{
						Correct: item.ID == story.Summary.Author.ID,
						Option:  ConvertUserInfoToSummary(item),
					}
				}),
			},
		}
		if story.WhoPlayContext != nil {
			detail.PlayContext = &api_items_story_types_v1.StoryDetail_WhoContext{
				WhoContext: &api_items_story_types_v1.WhoStoryPlayContext{
					TriedPoints: story.WhoPlayContext.TriedPoints,
					EnabledOptions: lo.Map(story.WhoPlayContext.EnabledOptionUserIds, func(item int64, _ int) *api_users_info_types_v1.UserInfoSummary {
						// 从 config 内取
						for _, option := range story.Summary.WhoPlayConfig.OptionUsers {
							if option.ID == item {
								return ConvertUserInfoToSummary(option)
							}
						}
						return nil
					}),
					IsUnlocked:  story.WhoPlayContext.IsUnlocked,
					IsConsumed:  story.WhoPlayContext.IsConsumed,
					TriedCount:  story.WhoPlayContext.TriedCount,
					MaxTryCount: story.WhoPlayContext.MaxTryCount,
				},
			}
		}
	}
}

func injectChatProxyConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.Summary.ChatProxyPlayConfig != nil {
		cfg := story.Summary.ChatProxyPlayConfig
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_ChatproxyConfig{
			ChatproxyConfig: &api_items_story_types_v1.StoryPlayChatProxyConfig{
				Caption:  cfg.Caption,
				Greeting: cfg.Greeting,
				Topics: lo.Map(cfg.Topics, func(topic string, _ int) *api_items_story_types_v1.StoryPlayChatProxyTopic {
					return &api_items_story_types_v1.StoryPlayChatProxyTopic{
						Content: topic,
					}
				}),
				UnlockResource:                ConvertResourceEntityToDto(cfg.UnlockResource),
				Cover:                         ConvertResourceEntityToDto(cfg.CoverResource),
				CoverAttachmentTexts:          cfg.CoverAttachmentTexts,
				UnlockResourceAttachmentTexts: cfg.UnlockResourceAttachmentTexts,
			},
		}
	}
	if story.ChatproxyContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_ChatproxyContext{
			ChatproxyContext: &api_items_story_types_v1.StoryPlayChatProxyContext{
				IsUnlocked: story.ChatproxyContext.Unlocked,
			},
		}
	}
}

func injectCapsuleConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.CapsulePlayContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_CapsuleContext{
			CapsuleContext: &api_items_story_types_v1.StoryPlayCapsuleContext{
				IsConsumed: story.CapsulePlayContext.IsConsumed,
			},
		}
	}
	if story.Summary.CapsulePlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_CapsuleConfig{
			CapsuleConfig: &api_items_story_types_v1.StoryPlayCapsuleConfig{
				CoverImage: &api_items_story_types_v1.Resource{
					ResourceType:  api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
					ResourceKey:   string(story.Summary.CapsulePlayConfig.CoverImage),
					ResourceUrl:   story.Summary.CapsulePlayConfig.CoverImage.ItemPosterInSummary(),
					CoverImageKey: string(story.Summary.CapsulePlayConfig.CoverImage),
					CoverImageUrl: story.Summary.CapsulePlayConfig.CoverImage.ItemPosterInSummary(),
				},
				Video: &api_items_story_types_v1.Resource{
					ResourceType:  api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO,
					ResourceKey:   string(story.Summary.CapsulePlayConfig.Video),
					ResourceUrl:   story.Summary.CapsulePlayConfig.Video.URL(),
					CoverImageKey: string(story.Summary.CapsulePlayConfig.Video.CoverImageKey()),
					CoverImageUrl: story.Summary.CapsulePlayConfig.Video.CoverImageURL(),
				},
				AiScripts: lo.Map(story.Summary.CapsulePlayConfig.CapsuleAIScripts, func(item *domain_entities_items.CapsuleAIScript, _ int) *api_items_story_types_v1.CapsuleAIScript {
					return &api_items_story_types_v1.CapsuleAIScript{
						Seconds: item.Seconds,
						Question: &api_items_story_types_v1.CapsuleQuestion{
							Question:    item.StoryPlayCapsuleQuestion.Question,
							TtsAudioUrl: item.StoryPlayCapsuleQuestion.TTSAudioKey.URL(),
							TtsAudioKey: string(item.StoryPlayCapsuleQuestion.TTSAudioKey),
							Thinking:    item.StoryPlayCapsuleQuestion.Thinking,
							Words: lo.Map(item.StoryPlayCapsuleQuestion.Words, func(word *domain_entities_items.Word, _ int) *api_items_story_types_v1.Word {
								return &api_items_story_types_v1.Word{
									StartTime: word.StartTime,
									EndTime:   word.EndTime,
									Text:      word.Text,
								}
							}),
						},
					}
				}),
			},
		}
	}
}
func injectRoastedConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.RoastedPlayContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_RoastedContext{
			RoastedContext: &api_items_story_types_v1.StoryPlayRoastedContext{
				IsConsumed: story.RoastedPlayContext.IsConsumed,
			},
		}
	}
	if story.Summary.RoastedPlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_RoastedConfig{
			RoastedConfig: &api_items_story_types_v1.StoryPlayRoastedConfig{
				Topic: &api_items_story_types_v1.StoryPlayRoastedTopic{
					Greeting: &api_items_story_types_v1.StoryPlayRoastedTopic_BotAnnouncement{
						Content:     story.Summary.RoastedPlayConfig.Topic.Greeting.Content,
						TtsAudioKey: string(story.Summary.RoastedPlayConfig.Topic.Greeting.TTSAudioKey),
						TtsAudioUrl: story.Summary.RoastedPlayConfig.Topic.Greeting.TTSAudioKey.URL(),
					},
					Ending: &api_items_story_types_v1.StoryPlayRoastedTopic_BotAnnouncement{
						Content:     story.Summary.RoastedPlayConfig.Topic.Ending.Content,
						TtsAudioKey: string(story.Summary.RoastedPlayConfig.Topic.Ending.TTSAudioKey),
						TtsAudioUrl: story.Summary.RoastedPlayConfig.Topic.Ending.TTSAudioKey.URL(),
					},
					Questions: lo.Map(story.Summary.RoastedPlayConfig.Topic.Questions, func(question *domain_entities_items.StoryPlayRoastedQuestion, _ int) *api_items_story_types_v1.RoastedQuestion {
						return &api_items_story_types_v1.RoastedQuestion{
							Question:    question.Question,
							TtsAudioUrl: question.TTSAudioKey.URL(),
							TtsAudioKey: string(question.TTSAudioKey),
							Thinking:    question.Thinking,
						}
					}),
				},
				Resource: ConvertResourceEntityToDto(story.Summary.RoastedPlayConfig.BaseResource),
				Cover:    ConvertResourceEntityToDto(story.Summary.RoastedPlayConfig.Cover),
			},
		}
	}
}

func ConvertHauntBooShowInfoToDto(showInfo *domain_entities_items.ShowInfo) *api_items_story_types_v1.HauntBooShowInfo {
	if showInfo == nil {
		return nil
	}
	dto := &api_items_story_types_v1.HauntBooShowInfo{
		HauntBoos:             ConvertHauntBooToDto(append([]*domain_entities_items.HauntBoo{showInfo.CreatorBoo}, showInfo.AssistBoos...)...),
		FromHauntStoryId:      fmt.Sprintf("%d", showInfo.FromStoryId),
		ShowInfoSence:         api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION,
		LastShowAtInUnixstamp: showInfo.LatestShowAt,
		TodayShowCount:        0,
		TriedCount:            0,
	}
	if showInfo.FromStory != nil {
		dto.TriedCount = showInfo.FromStory.HauntPlayContext.TryCount
	}
	return dto
}
func ConvertHauntBooToDto(boos ...*domain_entities_items.HauntBoo) []*api_items_story_types_v1.HauntBoo {
	return lo.Map(boos, func(boo *domain_entities_items.HauntBoo, _ int) *api_items_story_types_v1.HauntBoo {
		return &api_items_story_types_v1.HauntBoo{
			Id:            fmt.Sprintf("%d", boo.Id),
			Avatar:        ConvertAvatarToDto(boo.Avatar),
			Creator:       ConvertUserInfoToSummary(boo.Creator),
			GeneratedHint: boo.Hint,
			VideoUrl:      boo.VideoKey.URL(),
			CreatorBoo:    boo.StoryCreatorBoo,
			QuestionsWithAnswers: lo.Map(boo.QuestionsWithAnswers, func(question *domain_entities_items.HauntQuestionWithAnswer, _ int) *api_items_story_types_v1.HauntQuestionWithAnswer {
				return &api_items_story_types_v1.HauntQuestionWithAnswer{
					Question: &api_items_story_types_v1.HauntQuestion{
						Title:       question.Question.Title,
						Description: question.Question.Description,
						IsRequired:  question.Question.IsRequired,
					},
					Answer: question.Answer,
				}
			}),
		}
	})
}
func ConvertStoryResourceToDto(resource *domain_entities_items.Resource) *api_items_story_types_v1.Resource {
	if resource == nil {
		return nil
	}
	return &api_items_story_types_v1.Resource{
		ResourceType:  resource.Type,
		ResourceKey:   resource.Key,
		ResourceUrl:   resource.URL(),
		CoverImageKey: string(resource.CoverImageKey),
		CoverImageUrl: resource.CoverImageKey.ItemPosterInSummary(),
	}
}

func injectNowShotConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.NowShotPlayContext != nil {
		// 构建资源数组，v2版本使用
		// 初始化为空数组而不是nil，确保即使没有资源也返回空数组
		resources := make([]*api_items_story_types_v1.Resource, 0)

		// 添加图片资源
		for _, imageKey := range story.NowShotPlayContext.UserSubmitImageKeys {
			resources = append(resources, &api_items_story_types_v1.Resource{
				ResourceType:  api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:   string(imageKey),
				ResourceUrl:   imageKey.OriginalAccessURL(),
				CoverImageKey: string(imageKey),
				CoverImageUrl: imageKey.ItemPosterInSummary(),
			})
		}

		// 添加视频资源
		for _, videoKey := range story.NowShotPlayContext.UserSubmitVideoKeys {
			videoPath := domain_entities_resource.VideoResourcePath(videoKey)
			coverImageKey := videoPath.CoverImageKey()
			resources = append(resources, &api_items_story_types_v1.Resource{
				ResourceType:  api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO,
				ResourceKey:   string(videoKey),
				ResourceUrl:   videoPath.URL(),
				CoverImageKey: string(coverImageKey),
				CoverImageUrl: coverImageKey.ItemPosterInSummary(),
			})
		}

		detail.PlayContext = &api_items_story_types_v1.StoryDetail_NowShotContext{
			NowShotContext: &api_items_story_types_v1.StoryPlayNowShotContext{
				Status:    story.NowShotPlayContext.ConsumeStatus,
				StartTime: story.NowShotPlayContext.StartTime,
				UserSubmitImageUrls: lo.Map(story.NowShotPlayContext.UserSubmitImageKeys, func(imageKey domain_entities_resource.ImageResourcePath, _ int) string {
					return imageKey.ItemPosterInSummary()
				}),
				Resource: resources, // v2版本使用这个字段
				Ttl:      story.NowShotPlayContext.TTL,
			},
		}
	}
	if story.Summary.NowShotConfig != nil {
		cfg := &api_items_story_types_v1.StoryPlayNowShotConfig{
			Caption:         story.Summary.NowShotConfig.Caption,
			ResourceType:    story.Summary.NowShotConfig.ResourceType.String(),
			EndResourceType: story.Summary.NowShotConfig.EndResourceType.String(),
			Ttl:             story.Summary.NowShotConfig.TTL,
		}

		if story.Summary.NowShotConfig.ThumbnailKey != nil {
			url := story.Summary.NowShotConfig.ThumbnailKey.ItemPosterInSummary()
			cfg.ThumbnailUrl = &url
		}
		if story.Summary.NowShotConfig.EndThumbnailKey != nil {
			url := story.Summary.NowShotConfig.EndThumbnailKey.ItemPosterInSummary()
			cfg.EndThumbnailUrl = &url
		}

		switch story.Summary.NowShotConfig.ResourceType {
		case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
			cfg.ResourceUrl = story.Summary.NowShotConfig.ResourceVideoKey.URL()
		case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
			cfg.ResourceUrl = story.Summary.NowShotConfig.ResourceImageKey.ItemPosterInSummary()
		}
		switch story.Summary.NowShotConfig.EndResourceType {
		case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
			cfg.EndResourceUrl = story.Summary.NowShotConfig.EndResourceVideoKey.URL()
		case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
			cfg.EndResourceUrl = story.Summary.NowShotConfig.EndResourceImageKey.ItemPosterInSummary()
		}

		// 处理V2版本的CommonConfig
		if story.Summary.NowShotConfig.CommonConfig != nil {
			commonConfig := story.Summary.NowShotConfig.CommonConfig
			detail.Summary.CoverType = commonConfig.Cover.CoverType.String()
			detail.PlayConfig = &api_items_story_types_v1.StoryDetail_NowShotConfig{
				NowShotConfig: &api_items_story_types_v1.StoryPlayNowShotConfig{
					Ttl: story.Summary.NowShotConfig.TTL,
					CommonConfig: &api_items_story_types_v1.CommonPlayConfig{
						Cover: &api_items_story_types_v1.Cover{
							CoverHeight:  commonConfig.Cover.Height,
							CoverWidth:   commonConfig.Cover.Width,
							CoverType:    commonConfig.Cover.CoverType,
							ResourceUrl:  commonConfig.Cover.GetResourceURL(),
							ThumbnailUrl: commonConfig.Cover.GetThumbnailURL(),
							Gradient:     commonConfig.Cover.Gradient,
						},
						Resource: &api_items_story_types_v1.Resource{
							ResourceType:  commonConfig.Resource.Type,
							ResourceUrl:   commonConfig.Resource.URL(),
							CoverImageUrl: commonConfig.Resource.CoverImageKey.ItemPosterInSummary(),
						},
						CoverCaptions:    commonConfig.CoverCaptions,
						ResourceCaptions: commonConfig.ResourceCaptions,
						MaxTryCount:      commonConfig.MaxTryCount,
						ConditionV2: &api_items_story_types_v1.Condition{
							Hint:   commonConfig.Condition.Hint,
							Prompt: commonConfig.Condition.Prompt,
						},
					},
				},
			}
		} else {
			detail.PlayConfig = &api_items_story_types_v1.StoryDetail_NowShotConfig{
				NowShotConfig: cfg,
			}
		}
	}
}

func injectBasePlayConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.BasePlayContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_BasePlayContext{
			BasePlayContext: &api_items_story_types_v1.StoryPlayBasePlayContext{
				CurrentNodeId:    story.BasePlayContext.CurrentNodeID,
				CurrentNodeIndex: story.BasePlayContext.CurrentNodeIdx,
				IsUnlocked:       story.BasePlayContext.IsUnlocked,
			},
		}
	}
	if story.Summary.BasePlayConfig != nil {
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_BasePlayConfig{
			BasePlayConfig: &api_items_story_types_v1.StoryPlayBasePlayConfig{
				Nodes: lo.Map(story.Summary.BasePlayConfig.Nodes, func(node *domain_entities_items.StotyPlayBaseConfigNode, _ int) *api_items_story_types_v1.StoryPlayBasePlayConfig_Node {
					n := &api_items_story_types_v1.StoryPlayBasePlayConfig_Node{
						Id: node.ID,
						Resource: &api_items_story_types_v1.Resource{
							ResourceType:  node.ResourceType,
							ResourceUrl:   node.GetResourceURL(),
							ResourceKey:   node.ResourceKey,
							CoverImageKey: string(node.CoverImageKey),
							CoverImageUrl: node.CoverImageKey.ItemPosterInSummary(),
						},
						AttachmentTexts: node.AttachedTexts,
					}
					return n
				}),
			},
		}
	}
}

func injectUnmuteConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.UnmutePlayContext != nil {
		detail.PlayContext = &api_items_story_types_v1.StoryDetail_UnmuteContext{
			UnmuteContext: &api_items_story_types_v1.StoryPlayUnmuteContext{
				IsFinished: story.UnmutePlayContext.IsFinished,
				AiResponse: story.UnmutePlayContext.AiResponse,
				TryCount:   story.UnmutePlayContext.TryCount,
				AudioKeys:  story.UnmutePlayContext.AudioKeys,
			},
		}
	}
	if story.Summary.UnmutePlayConfig != nil {
		cfg := &api_items_story_types_v1.StoryPlayUnmuteConfig{
			Prompt: &api_items_story_types_v1.AttachmentText{
				Text:      story.Summary.UnmutePlayConfig.Prompt.Text,
				X:         story.Summary.UnmutePlayConfig.Prompt.X,
				Y:         story.Summary.UnmutePlayConfig.Prompt.Y,
				FontName:  story.Summary.UnmutePlayConfig.Prompt.FontName,
				FontSize:  story.Summary.UnmutePlayConfig.Prompt.FontSize,
				Color:     story.Summary.UnmutePlayConfig.Prompt.Color,
				Height:    story.Summary.UnmutePlayConfig.Prompt.Height,
				Width:     story.Summary.UnmutePlayConfig.Prompt.Width,
				Alignment: story.Summary.UnmutePlayConfig.Prompt.Alignment,
				FillStyle: story.Summary.UnmutePlayConfig.Prompt.FillStyle,
				Rotation:  story.Summary.UnmutePlayConfig.Prompt.Rotation,
				Scale:     story.Summary.UnmutePlayConfig.Prompt.Scale,
			},
			ResourceType:    story.Summary.UnmutePlayConfig.ResourceType.String(),
			EndResourceType: story.Summary.UnmutePlayConfig.EndResourceType.String(),
			CustomAiResponses: lo.Map(story.Summary.UnmutePlayConfig.CustomAiResponses, func(resp *domain_entities_items.StoryPlayUnmuteCustomAiResponse, _ int) *api_items_story_types_v1.StoryPlayUnmuteConfig_CustomAiResponse {
				return &api_items_story_types_v1.StoryPlayUnmuteConfig_CustomAiResponse{
					RuleDescription: resp.RuleDescription,
					RuleResult:      resp.RuleResult,
				}
			}),
			MaxTryCount: story.Summary.UnmutePlayConfig.MaxTryCount,
			Intention:   story.Summary.UnmutePlayConfig.Intention,
		}

		if story.Summary.UnmutePlayConfig.ThumbnailKey != nil {
			url := story.Summary.UnmutePlayConfig.ThumbnailKey.ItemPosterInSummary()
			cfg.ThumbnailUrl = &url
		}
		if story.Summary.UnmutePlayConfig.EndThumbnailKey != nil {
			url := story.Summary.UnmutePlayConfig.EndThumbnailKey.ItemPosterInSummary()
			cfg.EndThumbnailUrl = &url
		}

		switch story.Summary.UnmutePlayConfig.ResourceType {
		case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
			cfg.ResourceUrl = story.Summary.UnmutePlayConfig.ResourceVideoKey.URL()
		case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
			cfg.ResourceUrl = story.Summary.UnmutePlayConfig.ResourceImageKey.ItemPosterInSummary()
		}
		switch story.Summary.UnmutePlayConfig.EndResourceType {
		case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
			cfg.EndResourceUrl = story.Summary.UnmutePlayConfig.EndResourceVideoKey.URL()
		case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
			cfg.EndResourceUrl = story.Summary.UnmutePlayConfig.EndResourceImageKey.ItemPosterInSummary()
		}

		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_UnmuteConfig{
			UnmuteConfig: cfg,
		}
		if story.Summary.UnmutePlayConfig.CommonConfig != nil {
			commonConfig := story.Summary.UnmutePlayConfig.CommonConfig
			detail.Summary.CoverType = commonConfig.Cover.CoverType.String()
			detail.PlayConfig = &api_items_story_types_v1.StoryDetail_UnmuteConfig{
				UnmuteConfig: &api_items_story_types_v1.StoryPlayUnmuteConfig{
					CommonPlayConfig: &api_items_story_types_v1.CommonPlayConfig{
						Cover: &api_items_story_types_v1.Cover{
							CoverHeight:  commonConfig.Cover.Height,
							CoverWidth:   commonConfig.Cover.Width,
							CoverType:    commonConfig.Cover.CoverType,
							ResourceUrl:  commonConfig.Cover.GetResourceURL(),
							ThumbnailUrl: commonConfig.Cover.GetThumbnailURL(),
							Gradient:     commonConfig.Cover.Gradient,
						},
						Resource: &api_items_story_types_v1.Resource{
							ResourceType:  commonConfig.Resource.Type,
							ResourceUrl:   commonConfig.Resource.URL(),
							CoverImageUrl: commonConfig.Resource.CoverImageKey.ItemPosterInSummary(),
						},
						CoverCaptions:    commonConfig.CoverCaptions,
						ResourceCaptions: commonConfig.ResourceCaptions,
						MaxTryCount:      commonConfig.MaxTryCount,
						ConditionV2: &api_items_story_types_v1.Condition{
							Hint:   commonConfig.Condition.Hint,
							Prompt: commonConfig.Condition.Prompt,
						},
					},
				},
			}
		}
	}
}

func injectTurtleSoupConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.TurtleSoupPlayContext != nil {
		switch story.Summary.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
			detail.PlayContext = &api_items_story_types_v1.StoryDetail_TurtleSoupContext{
				TurtleSoupContext: &api_items_story_types_v1.StoryPlayTurtleSoupContext{
					IsFinished: story.TurtleSoupPlayContext.IsFinished,
					HitWords:   story.TurtleSoupPlayContext.HitWords,
					Tips:       story.TurtleSoupPlayContext.Tips,
					AiResponse: story.TurtleSoupPlayContext.AiResponse,
					TryCount:   story.TurtleSoupPlayContext.TryCount,
				},
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS:
			detail.PlayContext = &api_items_story_types_v1.StoryDetail_TurtleSoupMassContext{
				TurtleSoupMassContext: &api_items_story_types_v1.StoryPlayTurtleSoupMassContext{
					IsFinished: story.TurtleSoupPlayContext.IsFinished,
					Tips:       story.TurtleSoupPlayContext.Tips,
					AiResponse: story.TurtleSoupPlayContext.AiResponse,
					TryCount:   story.TurtleSoupPlayContext.TryCount,
				},
			}
		}
	}
	if story.Summary.TurtleSoupPlayConfig != nil {
		switch story.Summary.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
			cfg := &api_items_story_types_v1.StoryPlayTurtleSoupConfig{
				Caption:         story.Summary.TurtleSoupPlayConfig.Caption,
				IntentPrompt:    story.Summary.TurtleSoupPlayConfig.IntentPrompt,
				ResourceType:    story.Summary.TurtleSoupPlayConfig.ResourceType.String(),
				EndResourceType: story.Summary.TurtleSoupPlayConfig.EndResourceType.String(),
				EndMessage:      story.Summary.TurtleSoupPlayConfig.EndMessage,
				EndMessageFont:  story.Summary.TurtleSoupPlayConfig.EndMessageFont,
				CustomAiResponses: lo.Map(story.Summary.TurtleSoupPlayConfig.CustomAiResponses, func(resp *domain_entities_items.StoryPlayTurtleSoupCustomAiResponse, _ int) *api_items_story_types_v1.StoryPlayTurtleSoupConfig_CustomAiResponse {
					return &api_items_story_types_v1.StoryPlayTurtleSoupConfig_CustomAiResponse{
						RuleDescription: resp.RuleDescription,
						RuleResult:      resp.RuleResult,
					}
				}),
				MaxTryCount: story.Summary.TurtleSoupPlayConfig.MaxTryCount,
			}
			if story.Summary.TurtleSoupPlayConfig.EndThumbnailKey != nil {
				url := story.Summary.TurtleSoupPlayConfig.EndThumbnailKey.ItemPosterInSummary()
				cfg.EndThumbnailUrl = &url
			}
			if story.Summary.TurtleSoupPlayConfig.ThumbnailKey != nil {
				url := story.Summary.TurtleSoupPlayConfig.ThumbnailKey.ItemPosterInSummary()
				cfg.ThumbnailUrl = &url
			}
			if story.Summary.TurtleSoupPlayConfig.TemplateId != nil {
				id := fmt.Sprintf("%d", *story.Summary.TurtleSoupPlayConfig.TemplateId)
				cfg.TemplateId = &id
			}
			switch story.Summary.TurtleSoupPlayConfig.EndResourceType {
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
				cfg.EndResourceUrl = story.Summary.TurtleSoupPlayConfig.EndResourceVideoKey.URL()
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
				cfg.EndResourceUrl = story.Summary.TurtleSoupPlayConfig.EndResourceImageKey.ItemPosterInSummary()
			}
			switch story.Summary.TurtleSoupPlayConfig.ResourceType {
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
				cfg.ResourceUrl = story.Summary.TurtleSoupPlayConfig.ResourceVideoKey.URL()
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
				cfg.ResourceUrl = story.Summary.TurtleSoupPlayConfig.ResourceImageKey.ItemPosterInSummary()
			}
			detail.PlayConfig = &api_items_story_types_v1.StoryDetail_TurtleSoupConfig{
				TurtleSoupConfig: cfg,
			}
			if story.Summary.TurtleSoupPlayConfig.CommonConfig != nil {
				commonConfig := story.Summary.TurtleSoupPlayConfig.CommonConfig
				detail.Summary.CoverType = commonConfig.Cover.CoverType.String()
				detail.PlayConfig = &api_items_story_types_v1.StoryDetail_TurtleSoupConfig{
					TurtleSoupConfig: &api_items_story_types_v1.StoryPlayTurtleSoupConfig{
						CommonPlayConfig: &api_items_story_types_v1.CommonPlayConfig{
							Cover: &api_items_story_types_v1.Cover{
								CoverHeight:  commonConfig.Cover.Height,
								CoverWidth:   commonConfig.Cover.Width,
								CoverType:    commonConfig.Cover.CoverType,
								ResourceUrl:  commonConfig.Cover.GetResourceURL(),
								ThumbnailUrl: commonConfig.Cover.GetThumbnailURL(),
								Gradient:     commonConfig.Cover.Gradient,
							},
							Resource: &api_items_story_types_v1.Resource{
								ResourceType:  commonConfig.Resource.Type,
								ResourceUrl:   commonConfig.Resource.URL(),
								CoverImageUrl: commonConfig.Resource.CoverImageKey.ItemPosterInSummary(),
							},
							CoverCaptions:    commonConfig.CoverCaptions,
							ResourceCaptions: commonConfig.ResourceCaptions,
							MaxTryCount:      commonConfig.MaxTryCount,
							ConditionV2: &api_items_story_types_v1.Condition{
								Hint:   commonConfig.Condition.Hint,
								Prompt: commonConfig.Condition.Prompt,
							},
						},
					},
				}
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS:
			cfg := &api_items_story_types_v1.StoryPlayTurtleSoupMassConfig{
				Caption:      story.Summary.TurtleSoupPlayConfig.Caption,
				IntentPrompt: story.Summary.TurtleSoupPlayConfig.IntentPrompt,
				ResourceType: story.Summary.TurtleSoupPlayConfig.ResourceType.String(),
				CustomAiResponses: lo.Map(story.Summary.TurtleSoupPlayConfig.CustomAiResponses, func(resp *domain_entities_items.StoryPlayTurtleSoupCustomAiResponse, _ int) *api_items_story_types_v1.StoryPlayTurtleSoupMassConfig_CustomAiResponse {
					return &api_items_story_types_v1.StoryPlayTurtleSoupMassConfig_CustomAiResponse{
						RuleDescription: resp.RuleDescription,
						RuleResult:      resp.RuleResult,
					}
				}),
				MaxTryCount: story.Summary.TurtleSoupPlayConfig.MaxTryCount,
			}
			if story.Summary.TurtleSoupPlayConfig.ThumbnailKey != nil {
				url := story.Summary.TurtleSoupPlayConfig.ThumbnailKey.ItemPosterInSummary()
				cfg.ThumbnailUrl = &url
			}
			if story.Summary.TurtleSoupPlayConfig.TemplateId != nil {
				id := fmt.Sprintf("%d", *story.Summary.TurtleSoupPlayConfig.TemplateId)
				cfg.TemplateId = &id
			}
			switch story.Summary.TurtleSoupPlayConfig.ResourceType {
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
				cfg.ResourceUrl = story.Summary.TurtleSoupPlayConfig.ResourceVideoKey.URL()
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
				cfg.ResourceUrl = story.Summary.TurtleSoupPlayConfig.ResourceImageKey.ItemPosterInSummary()
			}
			detail.PlayConfig = &api_items_story_types_v1.StoryDetail_TurtleSoupMassConfig{
				TurtleSoupMassConfig: cfg,
			}
		}
	}
}

func injectImageExchangeConfigAndContext(story *domain_entities_items.StoryDetail, detail *api_items_story_types_v1.StoryDetail) {
	if story.ExchangeImagePlayContext != nil {
		context := &api_items_story_types_v1.StoryDetail_ExchangeImageContext{
			ExchangeImageContext: &api_items_story_types_v1.StoryPlayExchangeImageContext{
				CurrentNodeId:          story.ExchangeImagePlayContext.CurrentNodeID,
				CurrentTryCount:        story.ExchangeImagePlayContext.CurrentTryCount,
				CurrentSuccessProgress: story.ExchangeImagePlayContext.CurrentSuccessProgress,
				IsFinished:             story.ExchangeImagePlayContext.IsFinished,
				TryCount:               story.ExchangeImagePlayContext.TryCount,
				UserTrialImageKeys: lo.Map(story.ExchangeImagePlayContext.UserTrialImageUrls, func(key domain_entities_resource.ImageResourcePath, _ int) string {
					return string(key)
				}),
				UserTrialImageUrls: lo.Map(story.ExchangeImagePlayContext.UserTrialImageUrls, func(key domain_entities_resource.ImageResourcePath, _ int) string {
					return key.OriginalAccessURL()
				}),
			},
		}
		for nodeId, imageKeys := range story.ExchangeImagePlayContext.UserExchangeImageKeys {
			context.ExchangeImageContext.UserExchangeImageUrls = append(context.ExchangeImageContext.UserExchangeImageUrls, &api_items_story_types_v1.StoryPlayExchangeImageContext_UserExchangeImageUrls{
				NodeId: nodeId,
				ImageUrls: lo.Map(imageKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
					return key.ItemPosterInSummary()
				}),
			})
		}
		detail.PlayContext = context
	}
	if story.Summary.ExchangeImagePlayConfig != nil {
		detail.Summary.SubPlayType = story.Summary.ExchangeImagePlayConfig.PlayMode.String()
		detail.PlayConfig = &api_items_story_types_v1.StoryDetail_ExchangeImageConfig{
			ExchangeImageConfig: &api_items_story_types_v1.StoryPlayExchangeImageConfig{
				PlayMode: story.Summary.ExchangeImagePlayConfig.PlayMode.String(),
				Nodes: lo.Map(story.Summary.ExchangeImagePlayConfig.Nodes, func(node *domain_entities_items.ExchangeImagePlayNode, _ int) *api_items_story_types_v1.StoryPlayExchangeImageConfig_Node {
					n := &api_items_story_types_v1.StoryPlayExchangeImageConfig_Node{
						Condition: &api_items_story_types_v1.StoryPlayExchangeImageCondition{
							Tips:          node.Condition.Tips,
							PositiveGuide: node.Condition.PositiveText,
							PositiveImageUrls: lo.Map(node.Condition.PositiveImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
								return key.ItemPosterInSummary()
							}),
							PositiveImageKeys: lo.Map(node.Condition.PositiveImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
								return string(key)
							}),
							NegativeImageKeys: lo.Map(node.Condition.NegativeImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
								return string(key)
							}),
							NegativeGuide: node.Condition.NegativeText,
							NegativeImageUrls: lo.Map(node.Condition.NegativeImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
								return key.ItemPosterInSummary()
							}),
							PartialMatchImageKeys: lo.Map(node.Condition.PartialMatchImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
								return string(key)
							}),
							PartialMatchGuide: node.Condition.PartialMatchText,
							PartialMatchImageUrls: lo.Map(node.Condition.PartialMatchImageObjectKeys, func(key domain_entities_resource.ImageResourcePath, _ int) string {
								return key.ItemPosterInSummary()
							}),
							LlmPrompt: node.Condition.LLMPrompt,
						},
						AllowUseAlbum: node.AllowUseAlbum,
						MaxTryCount:   node.MaxTryCount,
						Id:            node.ID,
						ResourceType:  node.ResourceType.String(),
					}
					if node.ThumbnailObjectKey != nil {
						url := node.ThumbnailObjectKey.ItemPosterInSummary()
						n.ThumbnailUrl = &url
					}
					switch node.ResourceType {
					case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE:
						n.ResourceUrl = node.ImageKey.ItemPosterInSummary()
					case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO:
						n.ResourceUrl = node.VideoKey.URL()
					}
					return n
				}),
			},
		}
		if story.Summary.ExchangeImagePlayConfig.CommonConfig != nil {
			commonConfig := story.Summary.ExchangeImagePlayConfig.CommonConfig
			detail.Summary.CoverType = commonConfig.Cover.CoverType.String()
			detail.PlayConfig = &api_items_story_types_v1.StoryDetail_ExchangeImageConfig{
				ExchangeImageConfig: &api_items_story_types_v1.StoryPlayExchangeImageConfig{
					CommonPlayConfig: &api_items_story_types_v1.CommonPlayConfig{
						Cover: &api_items_story_types_v1.Cover{
							CoverHeight:  commonConfig.Cover.Height,
							CoverWidth:   commonConfig.Cover.Width,
							CoverType:    commonConfig.Cover.CoverType,
							ResourceUrl:  commonConfig.Cover.GetResourceURL(),
							ThumbnailUrl: commonConfig.Cover.GetThumbnailURL(),
							Gradient:     commonConfig.Cover.Gradient,
						},
						Resource: &api_items_story_types_v1.Resource{
							ResourceType:  commonConfig.Resource.Type,
							ResourceUrl:   commonConfig.Resource.URL(),
							CoverImageUrl: commonConfig.Resource.CoverImageKey.ItemPosterInSummary(),
						},
						CoverCaptions:    commonConfig.CoverCaptions,
						ResourceCaptions: commonConfig.ResourceCaptions,
						MaxTryCount:      commonConfig.MaxTryCount,
						ConditionV2: &api_items_story_types_v1.Condition{
							Hint:   commonConfig.Condition.Hint,
							Prompt: commonConfig.Condition.Prompt,
						},
					},
				},
			}
		}
	}
}
