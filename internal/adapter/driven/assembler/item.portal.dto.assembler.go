package adapter_driven_assembler

import (
	api_items_portal_moment_types_v1 "boson/api/items/portal/moments/types/v1"
	api_items_portal_types_v1 "boson/api/items/portal/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_users_boo_types_v1 "boson/api/users/boo/types/v1"
	domain_entities_boo "boson/internal/domain/entities/boo"
	domain_entities_items "boson/internal/domain/entities/items"
	"errors"
	"fmt"

	"github.com/samber/lo"
)

func ConvertAttachmentTextsToDto(attachmentTexts []*domain_entities_items.AttachedText) []*api_items_story_types_v1.AttachmentText {
	return lo.Map(attachmentTexts, func(attachmentText *domain_entities_items.AttachedText, _ int) *api_items_story_types_v1.AttachmentText {
		return &api_items_story_types_v1.AttachmentText{
			Text:      attachmentText.Text,
			X:         attachmentText.X,
			Y:         attachmentText.Y,
			FontName:  attachmentText.FontName,
			FontSize:  attachmentText.FontSize,
			Color:     attachmentText.Color,
			Height:    attachmentText.Height,
			Width:     attachmentText.Width,
			Alignment: attachmentText.Alignment,
			FillStyle: attachmentText.FillStyle,
			Rotation:  attachmentText.Rotation,
			Scale:     attachmentText.Scale,
		}
	})
}

func ConvertPortalToDtos(portals ...*domain_entities_items.Portal) []*api_items_portal_types_v1.Portal {
	return lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) *api_items_portal_types_v1.Portal {
		return ConvertPortalToDto(portal)
	})
}
func ConvertPortalToDto(portal *domain_entities_items.Portal) *api_items_portal_types_v1.Portal {
	if portal == nil {
		return nil
	}
	dto := &api_items_portal_types_v1.Portal{
		Id:     fmt.Sprintf("%d", portal.Id),
		Author: ConvertUserInfoToSummary(portal.Story.Summary.Author),
		Story:  ConvertStoryDetailToDto(portal.Story),
		AllPublishedMoments: lo.Map(portal.Moments, func(moment *domain_entities_items.Moment, _ int) *api_items_portal_moment_types_v1.Moment {
			return ConvertMomentToDto(moment)
		}),
		LastReadMomentId: lo.IfF(portal.LastReadMomentId != nil, func() *string {
			return lo.ToPtr(fmt.Sprintf("%d", *portal.LastReadMomentId))
		}).Else(nil),
		ExpiredAtTimestamp: uint32(portal.ExpiredAt.Unix()),
		Stat:               portal.Stat,
	}
	// fill unread moment ids
	// 默认全部未读
	dto.UnreadMomentIds = lo.Map(portal.Moments, func(moment *domain_entities_items.Moment, _ int) string {
		return fmt.Sprintf("%d", moment.Id)
	})
	if portal.LastReadMomentId != nil {
		allMomentIds := lo.Map(portal.Moments, func(moment *domain_entities_items.Moment, _ int) int64 {
			return moment.Id
		})
		// 找到那些 id 大于 lastReadMomentId 的 moment id，认为是未读
		unreadIds := lo.Filter(allMomentIds, func(momentId int64, _ int) bool {
			return momentId > *portal.LastReadMomentId
		})
		dto.UnreadMomentIds = lo.Map(unreadIds, func(momentId int64, _ int) string {
			return fmt.Sprintf("%d", momentId)
		})
	}
	return dto
}

func ConvertMomentToDto(moment *domain_entities_items.Moment) *api_items_portal_moment_types_v1.Moment {
	if moment == nil || moment.ExtraInfo == nil {
		return nil
	}
	return &api_items_portal_moment_types_v1.Moment{
		Id:       fmt.Sprintf("%d", moment.Id),
		Author:   ConvertUserInfoToSummary(moment.Creator),
		Resource: ConvertResourceEntityToDto(moment.ExtraInfo.Resource),
		Avatars: lo.Map(moment.ExtraInfo.Avatars, func(avatar *domain_entities_boo.Avatar, _ int) *api_users_boo_types_v1.Avatar {
			return ConvertAvatarToDto(avatar)
		}),
		MomentType:      moment.Type,
		AttachmentTexts: ConvertAttachmentTextsToDto(moment.ExtraInfo.AttachmentTexts),
		LoginUserRelations: lo.Map(
			moment.Relations,
			func(relation *domain_entities_items.MomentRelation, _ int) *api_items_portal_moment_types_v1.MomentRelation {
				return &api_items_portal_moment_types_v1.MomentRelation{
					RelationType: relation.RelationType,
				}
			},
		),
		LatestViewer: &api_items_portal_moment_types_v1.Viewer{
			User: ConvertUserInfoToSummary(moment.LatestViewer.User),
			UserRelations: lo.Map(moment.LatestViewer.Relations, func(relation *domain_entities_items.MomentRelation, _ int) *api_items_portal_moment_types_v1.MomentRelation {
				return &api_items_portal_moment_types_v1.MomentRelation{
					RelationType: relation.RelationType,
				}
			}),
		},
		CreatedAtTimestamp: uint32(moment.CreatedAt.Unix()),
		Stat:               moment.Stat,
		HauntBooShowInfo:   ConvertHauntBooShowInfoToDto(moment.HauntShowInfo),
	}
}

func ConvertDtoToMomentExtraInfoEntity(moment *api_items_story_types_v1.MomentCreateAttr) (*domain_entities_items.MomentExtraInfo, error) {
	if moment == nil {
		return nil, errors.New("moment is nil")
	}

	resourceEntity, err := ConvertDtoToResourceEntity(moment.Resource)
	if err != nil {
		return nil, err
	}

	return &domain_entities_items.MomentExtraInfo{
		AttachmentTexts: moment.AttachmentTexts,
		Resource:        resourceEntity,
		AvatarIds:       moment.AvatarIds,
	}, nil
}
