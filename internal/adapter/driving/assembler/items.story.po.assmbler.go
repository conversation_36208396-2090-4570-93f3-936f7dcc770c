package adapter_driving_assembler

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	"boson/internal/infra/data/model"
	"encoding/json"

	"github.com/pkg/errors"
)

func ConvertStoryPoToSummaryEntities(story ...*model.Story) ([]*domain_entities_items.StorySummary, error) {
	var result []*domain_entities_items.StorySummary
	for _, s := range story {
		d := &domain_entities_items.StorySummary{
			Id: s.ID,
			Author: &domain_entities_users.UserSummaryEntity{
				ID: s.CreatorID,
			},
			PlayType:  api_items_story_types_v1.StoryPlayType(api_items_story_types_v1.StoryPlayType_value[s.PlayType]),
			Status:    api_items_story_types_v1.StoryStatus(api_items_story_types_v1.StoryStatus_value[s.Status]),
			CreatedAt: s.<PERSON>,
			UpdatedAt: s.UpdatedAt,
			PrivacySettings: &domain_entities_items.StoryPrivacySettings{
				PrivacyType:            api_items_story_types_v1.PrivacyType(api_items_story_types_v1.PrivacyType_value[s.PrivacyType]),
				VisibleBeforeTimestamp: uint32(s.DisappearAt.Unix()),
			},
			Version: domain_entities_items.StoryVersion(s.Version),
		}
		d.StoryCoverImage = &domain_entities_items.StoryCoverImage{
			Path:   domain_entities_resource.ImageResourcePath(s.CoverImageObjectKey),
			Width:  int(s.CoverImageWidth),
			Height: int(s.CoverImageHeight),
		}

		if d.PlayType == api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE {
			// 需要再次判断下 sub_play_type
			playConfig, err := parseConfig[domain_entities_items.ExchanegImagePlayConfig](s)
			if err != nil {
				return nil, err
			}
			d.SubPlayType = playConfig.PlayMode
		}

		switch d.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HAUNT:
			config, err := parseConfig[domain_entities_items.HauntStoryConfig](s)
			if err != nil {
				return nil, err
			}
			d.HauntPlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO:
			config, err := parseConfig[domain_entities_items.StoryPlayWhoConfig](s)
			if err != nil {
				return nil, err
			}
			d.WhoPlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
			config, err := parseConfig[domain_entities_items.StoryPlayChatProxyConfig](s)
			if err != nil {
				return nil, err
			}
			d.ChatProxyPlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
			config, err := parseConfig[domain_entities_items.StoryPlayWassupConfig](s)
			if err != nil {
				return nil, err
			}
			d.WassupPlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			config, err := parseConfig[domain_entities_items.ExchanegImagePlayConfig](s)
			if err != nil {
				return nil, err
			}
			d.ExchangeImagePlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS:
			config, err := parseConfig[domain_entities_items.StoryPlayTurtleSoupConfig](s)
			if err != nil {
				return nil, err
			}
			d.TurtleSoupPlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			config, err := parseConfig[domain_entities_items.StoryPlayUnmuteConfig](s)
			if err != nil {
				return nil, err
			}
			d.UnmutePlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
			config, err := parseConfig[domain_entities_items.StoryPlayBasePlayConfig](s)
			if err != nil {
				return nil, err
			}
			d.BasePlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			config, err := parseConfig[domain_entities_items.StoryPlayNowShotConfig](s)
			if err != nil {
				return nil, err
			}
			d.NowShotConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
			config, err := parseConfig[domain_entities_items.StoryPlayRoastedConfig](s)
			if err != nil {
				return nil, err
			}
			d.RoastedPlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE:
			config, err := parseConfig[domain_entities_items.StoryPlayCapsuleConfig](s)
			if err != nil {
				return nil, err
			}
			d.CapsulePlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HIDE:
			config, err := parseConfig[domain_entities_items.StoryPlayHideConfig](s)
			if err != nil {
				return nil, err
			}
			d.HidePlayConfig = config
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_PIN:
			config, err := parseConfig[domain_entities_items.StoryPinConfig](s)
			if err != nil {
				return nil, err
			}
			d.PinPlayConfig = config
		}
		result = append(result, d)
	}
	return result, nil
}
func ConvertStoryPoToDetailEntities(story *model.Story) (*domain_entities_items.StoryDetail, error) {
	summary, err := ConvertStoryPoToSummaryEntities(story)
	if err != nil {
		return nil, err
	}
	d := &domain_entities_items.StoryDetail{
		Summary: summary[0],
	}
	return d, nil
}

func parseConfig[T domain_entities_items.StoryPlayNowShotConfig |
	domain_entities_items.StoryPlayBasePlayConfig |
	domain_entities_items.StoryPlayChatProxyConfig |
	domain_entities_items.StoryPlayRoastedConfig |
	domain_entities_items.StoryPlayTurtleSoupConfig |
	domain_entities_items.StoryPlayUnmuteConfig |
	domain_entities_items.StoryPlayWassupConfig |
	domain_entities_items.ExchanegImagePlayConfig |
	domain_entities_items.StoryPlayCapsuleConfig |
	domain_entities_items.StoryPlayWhoConfig |
	domain_entities_items.StoryPlayHideConfig |
	domain_entities_items.StoryPinConfig |
	domain_entities_items.HauntStoryConfig](story *model.Story) (*T, error) {
	var config T
	err := json.Unmarshal([]byte(story.PlayConfig), &config)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to unmarshal play config %s", story.PlayConfig)
	}
	return &config, nil
}
