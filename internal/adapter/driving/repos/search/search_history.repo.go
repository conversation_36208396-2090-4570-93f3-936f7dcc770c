package adapter_driving_repos_search

import (
	api_search_v1 "boson/api/search/v1"
	domain_entities_search "boson/internal/domain/entities/search"
	domain_interfaces "boson/internal/domain/interfaces"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

var _ domain_interfaces.ISearchHistoryRepository = (*SearchHistoryRepo)(nil)

type SearchHistoryRepo struct {
	data *data.Data
}

func NewSearchHistoryRepo(data *data.Data) *SearchHistoryRepo {
	return &SearchHistoryRepo{
		data: data,
	}
}

// GetSearchHistory 获取用户搜索历史
func (r *SearchHistoryRepo) GetSearchHistory(ctx context.Context, userID int64, count int32) ([]*domain_entities_search.SearchHistory, error) {
	q := dal.Use(r.data.MasterDB(ctx)).UserSearchHistory

	records, err := q.WithContext(ctx).
		Where(q.UserID.Eq(userID)).
		Order(q.CreatedAt.Desc()).
		Limit(int(count)).
		Find()

	if err != nil {
		return nil, errors.Wrapf(err, "userID: %d, count: %d", userID, count)
	}

	var histories []*domain_entities_search.SearchHistory
	for _, record := range records {
		searchType := api_search_v1.SearchType_value[record.SearchType]
		histories = append(histories, &domain_entities_search.SearchHistory{
			ID:         record.ID,
			UserID:     record.UserID,
			SearchText: record.SearchText,
			SearchType: api_search_v1.SearchType(searchType),
			CreatedAt:  record.CreatedAt,
			UpdatedAt:  record.UpdatedAt,
		})
	}

	return histories, nil
}

// DeleteSearchHistory 删除用户搜索历史
func (r *SearchHistoryRepo) DeleteSearchHistory(ctx context.Context, userID int64, searchIDs []int64) error {
	q := dal.Use(r.data.MasterDB(ctx)).UserSearchHistory

	_, err := q.WithContext(ctx).
		Where(q.UserID.Eq(userID)).
		Where(q.ID.In(searchIDs...)).
		Delete()

	if err != nil {
		return errors.Wrapf(err, "userID: %d, searchIDs: %v", userID, searchIDs)
	}

	return nil
}

// CreateSearchHistory 创建搜索历史记录
func (r *SearchHistoryRepo) CreateSearchHistory(ctx context.Context, userID int64, searchText string, searchType api_search_v1.SearchType) (int64, error) {
	q := dal.Use(r.data.MasterDB(ctx)).UserSearchHistory

	record := &model.UserSearchHistory{
		UserID:     userID,
		SearchText: searchText,
		SearchType: searchType.String(),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err := q.WithContext(ctx).Create(record)
	if err != nil {
		return 0, errors.Wrapf(err, "userID: %d, searchText: %s, searchType: %s", userID, searchText, searchType.String())
	}

	return record.ID, nil
}

// BatchCreateSearchHistory 批量创建搜索历史记录
func (r *SearchHistoryRepo) BatchCreateSearchHistory(ctx context.Context, userID int64, items []struct {
	SearchText string
	SearchType api_search_v1.SearchType
}) ([]int64, error) {
	q := dal.Use(r.data.MasterDB(ctx)).UserSearchHistory

	now := time.Now()
	records := lo.Map(items, func(item struct {
		SearchText string
		SearchType api_search_v1.SearchType
	}, _ int) *model.UserSearchHistory {
		return &model.UserSearchHistory{
			UserID:     userID,
			SearchText: item.SearchText,
			SearchType: item.SearchType.String(),
			CreatedAt:  now,
			UpdatedAt:  now,
		}
	})

	err := q.WithContext(ctx).CreateInBatches(records, 100)
	if err != nil {
		return nil, errors.Wrapf(err, "userID: %d, items: %v", userID, items)
	}

	// Extract IDs from created records
	var searchIDs []int64
	for _, record := range records {
		searchIDs = append(searchIDs, record.ID)
	}

	return searchIDs, nil
}
