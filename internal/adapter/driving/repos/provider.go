package adapter_driving_repos

import (
	adapter_driving_repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	adapter_driving_repos_fizz "boson/internal/adapter/driving/repos/fizz"
	adapter_driving_repos_items "boson/internal/adapter/driving/repos/items"
	adapter_driving_repos_push "boson/internal/adapter/driving/repos/push"
	adapter_driving_repos_search "boson/internal/adapter/driving/repos/search"
	adapter_driving_repos_users "boson/internal/adapter/driving/repos/users"

	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	adapter_driving_repos_users.ProviderSet,
	adapter_driving_repos_items.ProviderSet,
	adapter_driving_repos_app_settings.Provider,
	adapter_driving_repos_fizz.ProviderSet,
	adapter_driving_repos_push.ProviderSet,
	adapter_driving_repos_search.ProviderSet,
)
