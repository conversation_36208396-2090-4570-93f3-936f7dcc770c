package adapter_driving_repos_push

import (
	"github.com/google/wire"

	domain_services_interfaces_push "boson/internal/domain/services/interfaces/push"
	domain_services_push "boson/internal/domain/services/push"
)

var ProviderSet = wire.NewSet(
	NewRepo,
	NewPushInterceptionRepo,
	wire.Bind(new(domain_services_push.PushTokenRepository), new(*Repo)),
	wire.Bind(new(domain_services_interfaces_push.IPushInterceptionRepository), new(*PushInterceptionRepo)),
)
