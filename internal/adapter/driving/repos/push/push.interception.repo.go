package adapter_driving_repos_push

import (
	"context"
	"time"

	"github.com/pkg/errors"

	domain_entities_push "boson/internal/domain/entities/push"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
)

// PushInterceptionRepo 推送拦截仓储实现
type PushInterceptionRepo struct {
	data *data.Data
}

// NewPushInterceptionRepo 创建推送拦截仓储
func NewPushInterceptionRepo(data *data.Data) *PushInterceptionRepo {
	return &PushInterceptionRepo{
		data: data,
	}
}

// CreatePushInterception 创建推送拦截记录
func (r *PushInterceptionRepo) CreatePushInterception(ctx context.Context, attr domain_entities_push.CreatePushInterceptionAttr) error {
	cmd := dal.Use(r.data.MasterDB(ctx)).UserPushInterception

	userPushInterception := &model.UserPushInterception{
		ID:        attr.ID,
		UserID:    attr.UserID,
		PushType:  attr.PushType,
		PushValue: attr.PushValue,
		CreatedAt: attr.CreatedAt,
		UpdatedAt: attr.CreatedAt,
	}

	err := cmd.WithContext(ctx).Create(userPushInterception)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

// CheckPushInterceptionExists 检查指定时间内是否存在相同的推送拦截记录
func (r *PushInterceptionRepo) CheckPushInterceptionExists(ctx context.Context, attr domain_entities_push.CheckPushInterceptionAttr) (bool, error) {
	query := dal.Use(r.data.MasterDB(ctx)).UserPushInterception
	queryBuilder := query.WithContext(ctx).
		Where(query.UserID.Eq(attr.UserID)).
		Where(query.PushType.Eq(attr.PushType)).
		Where(query.PushValue.Eq(attr.PushValue))

	// 如果指定了时间范围，添加时间条件
	if attr.WithinHours > 0 {
		cutoffTime := time.Now().Add(-time.Duration(attr.WithinHours) * time.Hour)
		queryBuilder = queryBuilder.Where(query.CreatedAt.Gte(cutoffTime))
	}

	count, err := queryBuilder.Count()
	if err != nil {
		return false, errors.WithStack(err)
	}

	return count > 0, nil
}
