package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_items_story "boson/internal/domain/services/items/story"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var _ domain_services_items_story.IStoryReactionRepo = &StoryReactionRepo{}

type StoryReactionRepo struct {
	storyStatRepo *StoryStatRepo
	db            *data.Data
}

// ListReactionMadeUsers implements domain_services_items_story.IStoryReactionRepo.
func (s *StoryReactionRepo) ListReactionMadeUsers(ctx context.Context, userId int64, storyId int64, emojis []string, req *api_common_v1.ListRequest) ([]int64, *api_common_v1.ListResponse, error) {
	q := dal.Use(s.db.MasterDB(ctx)).StoryUserReaction
	conditions := []gen.Condition{
		q.StoryID.Eq(storyId),
	}
	if len(emojis) > 0 {
		conditions = append(conditions, q.Emoji.In(emojis...))
	}
	offset := cast.ToInt(req.PageToken)
	limit := cast.ToInt(req.PageSize)
	if limit == 0 {
		limit = 10
	}

	reactions, err := q.WithContext(ctx).Select(
		q.UserID,
	).Where(conditions...).Order(
		q.ID.Desc(),
	).Offset(offset).Limit(limit + 1).Find()
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	hasMore := false
	if len(reactions) > limit {
		hasMore = true
		reactions = reactions[:limit]
	}

	userIds := lo.Map(reactions, func(reaction *model.StoryUserReaction, _ int) int64 {
		return reaction.UserID
	})
	return userIds, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: cast.ToString(offset + len(reactions)),
	}, nil
}

func NewStoryReactionRepo(db *data.Data, storyStatRepo *StoryStatRepo) *StoryReactionRepo {
	return &StoryReactionRepo{db: db, storyStatRepo: storyStatRepo}
}

func (s *StoryReactionRepo) InjectLoginUserMadeReactions(ctx context.Context, userId int64, stories ...*domain_entities_items.StorySummary) error {
	ids := lo.Map(stories, func(story *domain_entities_items.StorySummary, _ int) int64 {
		return story.Id
	})
	reactions, err := s.batchGetLoginUserMadeReactions(ctx, userId, ids)
	if err != nil {
		return err
	}
	for _, story := range stories {
		story.LoginUserMadeReactions = reactions[story.Id]
	}
	return nil
}

func (s *StoryReactionRepo) batchGetLoginUserMadeReactions(ctx context.Context, userId int64, storyIds []int64) (
	map[int64][]*domain_entities_items.StoryReaction,
	error,
) {
	q := dal.Use(s.db.MasterDB(ctx)).StoryUserReaction
	reactions, err := q.WithContext(ctx).Where(
		q.UserID.Eq(userId),
		q.StoryID.In(storyIds...),
	).Find()
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := make(map[int64][]*domain_entities_items.StoryReaction)
	for _, reaction := range reactions {
		reactionEntity := &domain_entities_items.StoryReaction{
			StoryId:       reaction.StoryID,
			StoryAuthorId: reaction.StoryAuthorID,
			UserId:        reaction.UserID,
			EmojiStr:      reaction.Emoji,
			Comment:       reaction.Comment,
		}
		result[reaction.StoryID] = append(result[reaction.StoryID], reactionEntity)
	}
	return result, nil
}

// CreateReaction implements domain_services_items_story.IStoryReactionRepo.
func (s *StoryReactionRepo) CreateReaction(
	ctx context.Context,
	reaction *domain_entities_items.StoryReaction,
) error {
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		// 创建记录
		insert, err := s.createReactionRecord(ctx, reaction)
		if err != nil {
			return err
		}
		if insert {
			// 更新 story_stats 的对应的 emoji + 1
			if err := s.storyStatRepo.UpdateReactionStats(ctx, reaction.StoryId, true, reaction.EmojiStr); err != nil {
				return err
			}
		}
		return nil
	})
}

// DeleteReaction implements domain_services_items_story.IStoryReactionRepo.
func (s *StoryReactionRepo) DeleteReaction(
	ctx context.Context,
	storyId int64,
	userId int64,
	emoji string,
) error {
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		// 创建记录
		delete, err := s.deleteReactionRecord(ctx, storyId, userId, emoji)
		if err != nil {
			return err
		}
		if delete {
			// 更新 story_stats 的对应的 emoji - 1
			if err := s.storyStatRepo.UpdateReactionStats(ctx, storyId, false, emoji); err != nil {
				return err
			}
		}
		return nil
	})
}

func (s *StoryReactionRepo) deleteReactionRecord(ctx context.Context, storyId int64, userId int64, emoji string) (delete bool, err error) {
	cmd := dal.Use(s.db.MasterDB(ctx)).StoryUserReaction
	result, err := cmd.WithContext(ctx).Where(
		cmd.UserID.Eq(userId),
		cmd.StoryID.Eq(storyId),
		cmd.Emoji.Eq(emoji),
	).Delete()
	if err != nil {
		return false, errors.WithStack(err)
	}
	return result.RowsAffected > 0, nil
}

func (s *StoryReactionRepo) createReactionRecord(ctx context.Context, reaction *domain_entities_items.StoryReaction) (insert bool, err error) {
	db := s.db.MasterDB(ctx)
	sql := `
	INSERT IGNORE INTO story_user_reactions (story_id, story_author_id, user_id, emoji, comment)
	VALUES (?, ?, ?, ?, ?);
	`
	result := db.Exec(sql, reaction.StoryId, reaction.StoryAuthorId, reaction.UserId, reaction.EmojiStr, reaction.Comment)
	if result.Error != nil {
		return false, errors.WithStack(result.Error)
	}
	return result.RowsAffected > 0, nil
}

func (s *StoryReactionRepo) ListActivities(
	ctx context.Context,
	userId int64,
	req *api_common_v1.ListRequest,
) ([]*domain_entities_items.ActivityItem, *api_common_v1.ListResponse, error) {
	id := 0
	if req.PageToken != "" {
		id = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}

	q := dal.Use(s.db.MasterDB(ctx))

	conditions := []gen.Condition{
		q.StoryUserReaction.StoryAuthorID.Eq(userId),
		q.StoryUserReaction.UserID.Neq(userId),
		q.StoryUserReaction.Emoji.Eq("🔥"),
	}
	if id > 0 {
		conditions = append(conditions, q.StoryUserReaction.ID.Lt(int64(id)))
	}

	likeReactions, err := q.StoryUserReaction.WithContext(ctx).
		Where(conditions...).
		Order(q.StoryUserReaction.ID.Desc()).
		Limit(limit + 1).
		Find()
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	var activities []*domain_entities_items.ActivityItem

	for _, reaction := range likeReactions {
		activities = append(activities, &domain_entities_items.ActivityItem{
			ID:                 cast.ToString(reaction.ID),
			ActorUserID:        reaction.UserID,
			StoryID:            reaction.StoryID,
			ActivityType:       domain_entities_items.ActivityType_STORY_LIKE,
			CreatedAtTimestamp: uint32(reaction.CreatedAt.Unix()),
			Emoji:              reaction.Emoji,
			Comment:            reaction.Comment,
		})
	}

	if len(activities) > limit {
		activities = activities[:limit]
		return activities, &api_common_v1.ListResponse{
			HasMore:       true,
			NextPageToken: cast.ToString(likeReactions[limit-1].ID),
		}, nil
	}

	return activities, &api_common_v1.ListResponse{
		HasMore: false,
	}, nil
}

func (s *StoryReactionRepo) GetActivityUnreadCount(
	ctx context.Context,
	userId int64,
) (int64, error) {
	q := dal.Use(s.db.MasterDB(ctx))

	var readId int64
	rr, err := q.StoryActivityReadRelation.WithContext(ctx).Where(q.StoryActivityReadRelation.UserID.Eq(userId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, errors.Wrapf(err, "userId: %d", userId)
	}
	if rr != nil && rr.LastReadLikeID != nil {
		readId = *rr.LastReadLikeID
	}

	cnt, err := q.StoryUserReaction.WithContext(ctx).Where(
		q.StoryUserReaction.ID.Gt(readId),
		q.StoryUserReaction.Emoji.Eq("🔥"),
		q.StoryUserReaction.StoryAuthorID.Eq(userId),
		q.StoryUserReaction.UserID.Neq(userId),
	).Count()
	if err != nil {
		return 0, errors.Wrapf(err, "userId: %d, readId: %d", userId, readId)
	}
	return cnt, nil
}

func (s *StoryReactionRepo) MarkActivitiesAsRead(
	ctx context.Context,
	userId int64,
) error {
	q := dal.Use(s.db.MasterDB(ctx))

	// Latest like on user's stories (no join)
	maxLikeReaction, err := q.StoryUserReaction.WithContext(ctx).
		Where(
			q.StoryUserReaction.StoryAuthorID.Eq(userId),
			q.StoryUserReaction.Emoji.Eq("🔥"),
			q.StoryUserReaction.UserID.Neq(userId),
		).
		Order(q.StoryUserReaction.ID.Desc()).
		First()

	maxLikeId := int64(0)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errors.Wrapf(err, "userId: %d", userId)
	}
	if maxLikeReaction != nil {
		maxLikeId = maxLikeReaction.ID
	}

	now := time.Now()
	lastLikeID := maxLikeId

	err = q.StoryActivityReadRelation.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"last_read_like_id", "updated_at"}),
	}).Create(&model.StoryActivityReadRelation{
		UserID:         userId,
		LastReadLikeID: &lastLikeID,
		UpdatedAt:      &now,
	})
	if err != nil {
		return errors.Wrapf(err, "userId: %d, maxLikeId: %d", userId, maxLikeId)
	}

	return nil
}
