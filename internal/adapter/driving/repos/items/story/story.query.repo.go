package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_items_portal_types_v1 "boson/api/items/portal/types/v1"
	api_items_story_activity_types_v1 "boson/api/items/story/activity/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_users_relations_types_v1 "boson/api/users/relations/types/v1"
	adapter_driving_assembler "boson/internal/adapter/driving/assembler"
	adapter_driving_repos_users_boo "boson/internal/adapter/driving/repos/users/boo"
	adapter_driving_repos_users_info "boson/internal/adapter/driving/repos/users/info"
	adapter_driving_repos_users_relations "boson/internal/adapter/driving/repos/users/relations"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_services_items_story "boson/internal/domain/services/items/story"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/james-bowman/nlp"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gen"
	"gorm.io/gorm"
)

var _ domain_services_items_story.IStoryQueryRepository = &StoryQueryRepo{}

type StoryQueryRepo struct {
	db                 *data.Data
	usersInfoQueryRepo *adapter_driving_repos_users_info.QueryRepo
	avatarQueryRepo    *adapter_driving_repos_users_boo.Repo
	storyContextRepo   *StoryContextRepo
	relationsQueryRepo *adapter_driving_repos_users_relations.Repo
	topRepo            *StoryTopRepo
	storyReactionRepo  *StoryReactionRepo
	storyStatRepo      *StoryStatRepo
}

func NewStoryQueryRepo(
	db *data.Data,
	relationsQueryRepo *adapter_driving_repos_users_relations.Repo,
	usersInfoQueryRepo *adapter_driving_repos_users_info.QueryRepo,
	avatarQueryRepo *adapter_driving_repos_users_boo.Repo,
	storyContextRepo *StoryContextRepo,
	storyReactionRepo *StoryReactionRepo,
	topRepo *StoryTopRepo,
	storyStatRepo *StoryStatRepo,
) *StoryQueryRepo {
	return &StoryQueryRepo{
		db:                 db,
		usersInfoQueryRepo: usersInfoQueryRepo,
		avatarQueryRepo:    avatarQueryRepo,
		storyContextRepo:   storyContextRepo,
		relationsQueryRepo: relationsQueryRepo,
		topRepo:            topRepo,
		storyReactionRepo:  storyReactionRepo,
		storyStatRepo:      storyStatRepo,
	}
}

// ListSameAuthorStoryWithAnchor implements domain_services_items_story.IStoryQueryRepository.
func (r *StoryQueryRepo) ListSameAuthorStoryWithAnchor(
	ctx context.Context,
	loginUserId int64,
	anchorStoryId int64,
	idDesc bool,
	timeRange time.Duration,
	limit uint32,
	filterPlayTypes []api_items_story_types_v1.StoryPlayType,
	filterPrivacyTypes []api_items_story_types_v1.PrivacyType,
) ([]int64, error) {
	q := dal.Use(r.db.MasterDB(ctx)).Story
	// 先查出作者
	anchorStory, err := q.WithContext(ctx).Where(q.ID.Eq(anchorStoryId)).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		// wrapf with all params
		return nil, errors.Wrapf(err, "anchor id: %d, loginUserId: %d, idDesc: %t, timeRange: %s, limit: %d", anchorStoryId, loginUserId, idDesc, timeRange, limit)
	}
	if err != nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("story not found with id: %d", anchorStoryId))
	}
	creatorId := anchorStory.CreatorID

	where := []gen.Condition{
		q.CreatorID.Eq(creatorId),
		q.Status.Eq(api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED.String()),
		q.DisappearAt.Gt(time.Now()),
	}

	if idDesc {
		// 如果是倒序，则 id 需要小于锚点，时间需要早于锚点时间且在范围内
		where = append(where, q.ID.Lt(anchorStoryId), q.CreatedAt.Gt(anchorStory.CreatedAt.Add(-timeRange)))
	} else {
		// 如果是正序，则 id 需要大于锚点，时间需要晚于锚点时间且在范围内
		where = append(where, q.ID.Gt(anchorStoryId), q.CreatedAt.Lt(anchorStory.CreatedAt.Add(timeRange)))
	}

	if len(filterPlayTypes) > 0 {
		where = append(where, q.PlayType.In(lo.Map(filterPlayTypes, func(item api_items_story_types_v1.StoryPlayType, _ int) string {
			return item.String()
		})...))
	}
	if len(filterPrivacyTypes) > 0 {
		where = append(where, q.PrivacyType.In(lo.Map(filterPrivacyTypes, func(item api_items_story_types_v1.PrivacyType, _ int) string {
			return item.String()
		})...))
	}

	order := q.ID.Asc()
	if idDesc {
		order = q.ID.Desc()
	}
	stories, err := q.WithContext(ctx).Where(where...).Limit(int(limit)).Order(
		order,
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "anchor id: %d, loginUserId: %d, idDesc: %t, timeRange: %s, limit: %d", anchorStoryId, loginUserId, idDesc, timeRange, limit)
	}
	var result []int64
	for _, story := range stories {
		result = append(result, story.ID)
	}
	return result, nil
}

// ListFollowingCreatorStory implements domain_services_items_story.IStoryQueryRepository.
func (r *StoryQueryRepo) ListFollowingCreatorStory(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
	storyVersions []domain_entities_items.StoryVersion,
) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error) {
	// 查询所有关注的用户
	followingUserIds, _, err := r.relationsQueryRepo.ListUsersWithRelationType(
		ctx,
		loginUserId,
		api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING,
		&api_common_v1.ListRequest{
			// 限制下，最多只查500个
			PageSize:  "500",
			PageToken: "",
		},
	)
	if err != nil {
		return nil, nil, err
	}

	stories, listResp, err := r.BatchListUserCreatedStory(ctx, loginUserId, followingUserIds, req, storyVersions)
	if err != nil {
		return nil, nil, err
	}

	return stories, listResp, nil
}

// ListUserCreatedStory implements domain_services_items_story.IStoryQueryRepository.
func (r *StoryQueryRepo) BatchListUserCreatedStory(
	ctx context.Context,
	loginUserId int64,
	creatorIds []int64,
	req *api_common_v1.ListRequest,
	storyVersions []domain_entities_items.StoryVersion,
) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error) {
	q := dal.Use(r.db.MasterDB(ctx)).Story
	offset := 0
	if req.PageToken != "" {
		offset = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}

	stories, total, err := q.WithContext(ctx).Where(
		q.CreatorID.In(creatorIds...),
		q.Status.Eq(api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED.String()),
		q.DisappearAt.Gt(time.Now()),
		q.Version.In(storyVersions...),
	).Order(
		q.ID.Desc(),
	).FindByPage(
		offset,
		limit,
	)

	if err != nil {
		return nil, nil, errors.Wrapf(err, "list home page story")
	}

	ids := lo.Map(stories, func(item *model.Story, _ int) int64 {
		return item.ID
	})

	if len(ids) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}
	details, err := r.BatchGetStoryDetails(ctx, loginUserId, ids)
	if err != nil {
		return nil, nil, err
	}

	var result []*domain_entities_items.StoryDetail
	for _, story := range stories {
		result = append(result, details[story.ID])
	}

	hasMore := len(result)+offset < int(total)
	return result, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: strconv.Itoa(offset + len(result)),
	}, nil
}

// ListUserCreatedStory implements domain_services_items_story.IStoryQueryRepository.
func (r *StoryQueryRepo) ListUserCreatedStory(
	ctx context.Context,
	loginUserId int64,
	creatorId int64,
	filterPrivacyTypes []api_items_story_types_v1.PrivacyType,
	storyVersions []domain_entities_items.StoryVersion,
	req *api_common_v1.ListRequest,
) ([]*domain_services_items_story.CreatedStory, *api_common_v1.ListResponse, error) {
	q := dal.Use(r.db.MasterDB(ctx)).Story
	offset := 0
	if req.PageToken != "" {
		offset = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}

	// 如果是第一页，则需要查询置顶的 story
	var topStoryIds []int64
	if offset == 0 {
		var err error
		topStoryIds, err = r.topRepo.ListTopStories(ctx, creatorId)
		if err != nil {
			return nil, nil, err
		}
	}

	where := []gen.Condition{
		q.CreatorID.Eq(creatorId),
		q.Status.Eq(api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED.String()),
		q.ID.NotIn(topStoryIds...),
		q.DisappearAt.Gt(time.Now()),
	}
	if len(filterPrivacyTypes) > 0 {
		where = append(where, q.PrivacyType.In(lo.Map(filterPrivacyTypes, func(item api_items_story_types_v1.PrivacyType, _ int) string {
			return item.String()
		})...))
	}
	if len(storyVersions) > 0 {
		where = append(where, q.Version.In(storyVersions...))
	}
	stories, total, err := q.WithContext(ctx).Where(where...).Order(
		q.ID.Desc(),
	).FindByPage(
		offset,
		limit,
	)

	if err != nil {
		return nil, nil, errors.Wrapf(err, "list home page story")
	}

	ids := lo.Map(stories, func(item *model.Story, _ int) int64 {
		return item.ID
	})
	// top 插到 ids 的最前面
	ids = append(topStoryIds, ids...)

	if len(ids) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}
	summaries, err := r.BatchGetSummaries(ctx, loginUserId, ids)
	if err != nil {
		return nil, nil, err
	}

	var result []*domain_services_items_story.CreatedStory
	for _, story := range stories {
		summary, ok := summaries[story.ID]
		if !ok {
			continue
		}
		result = append(result, &domain_services_items_story.CreatedStory{
			Story: summary,
			IsTop: lo.Contains(topStoryIds, story.ID),
		})
	}

	hasMore := len(result)+offset < int(total)
	return result, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: strconv.Itoa(offset + len(result)),
	}, nil
}

// ListUserCreatedStoryV2 implements domain_services_items_story.IStoryQueryRepository.
func (r *StoryQueryRepo) ListUserCreatedStoryV2(
	ctx context.Context,
	loginUserId int64,
	creatorId int64,
	filterPrivacyTypes []api_items_story_types_v1.PrivacyType,
	storyVersions []domain_entities_items.StoryVersion,
	req *api_common_v1.ListRequest,
) ([]*domain_services_items_story.CreatedStoryV2, *api_common_v1.ListResponse, error) {
	q := dal.Use(r.db.MasterDB(ctx)).Story
	offset := 0
	if req.PageToken != "" {
		offset = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}

	// 如果是第一页，则需要查询置顶的 story
	var topStoryIds []int64
	if offset == 0 {
		var err error
		topStoryIds, err = r.topRepo.ListTopStories(ctx, creatorId)
		if err != nil {
			return nil, nil, err
		}
	}

	where := []gen.Condition{
		q.CreatorID.Eq(creatorId),
		q.Status.Eq(api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED.String()),
		q.ID.NotIn(topStoryIds...),
		q.DisappearAt.Gt(time.Now()),
	}
	if len(filterPrivacyTypes) > 0 {
		where = append(where, q.PrivacyType.In(lo.Map(filterPrivacyTypes, func(item api_items_story_types_v1.PrivacyType, _ int) string {
			return item.String()
		})...))
	}
	if len(storyVersions) > 0 {
		where = append(where, q.Version.In(storyVersions...))
	}
	stories, total, err := q.WithContext(ctx).Where(where...).Order(
		q.ID.Desc(),
	).FindByPage(
		offset,
		limit,
	)

	if err != nil {
		return nil, nil, errors.Wrapf(err, "list home page story")
	}

	ids := lo.Map(stories, func(item *model.Story, _ int) int64 {
		return item.ID
	})
	// top 插到 ids 的最前面
	ids = append(topStoryIds, ids...)

	if len(ids) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}
	details, err := r.BatchGetStoryDetails(ctx, loginUserId, ids)
	if err != nil {
		return nil, nil, err
	}

	var result []*domain_services_items_story.CreatedStoryV2
	for _, story := range stories {
		detail, ok := details[story.ID]
		if !ok {
			continue
		}
		result = append(result, &domain_services_items_story.CreatedStoryV2{
			Story: detail,
			IsTop: lo.Contains(topStoryIds, story.ID),
		})
	}

	hasMore := len(result)+offset < int(total)
	return result, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: strconv.Itoa(offset + len(result)),
	}, nil
}

func (r *StoryQueryRepo) ListHomePageStory(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
	filterPlayTypes []api_items_story_types_v1.StoryPlayType,
	storyVersions []domain_entities_items.StoryVersion,
) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error) {
	q := dal.Use(r.db.MasterDB(ctx)).Story
	offset := 0
	if req.PageToken != "" {
		offset = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}
	where := []gen.Condition{
		q.Status.Eq(api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED.String()),
		q.DisappearAt.Gt(time.Now()),
	}
	if len(filterPlayTypes) > 0 {
		where = append(where, q.PlayType.In(lo.Map(filterPlayTypes, func(item api_items_story_types_v1.StoryPlayType, _ int) string {
			return item.String()
		})...))
	}
	if len(storyVersions) > 0 {
		where = append(where, q.Version.In(storyVersions...))
	}
	stories, total, err := q.WithContext(ctx).
		Where(where...).
		Order(
			q.ID.Desc(),
		).
		FindByPage(
			offset,
			limit,
		)

	if err != nil {
		return nil, nil, errors.Wrapf(err, "list home page story")
	}

	ids := lo.Map(stories, func(item *model.Story, _ int) int64 {
		return item.ID
	})

	if len(ids) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}
	details, err := r.BatchGetStoryDetails(ctx, loginUserId, ids)
	if err != nil {
		return nil, nil, err
	}

	var result []*domain_entities_items.StoryDetail
	for _, story := range stories {
		result = append(result, details[story.ID])
	}

	hasMore := len(result)+offset < int(total)
	return result, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: strconv.Itoa(offset + len(result)),
	}, nil
}

func (r *StoryQueryRepo) ListUsersByConsumptionStatus(
	ctx context.Context,
	loginUserId int64,
	storyId int64,
	consumptionStatus api_items_story_activity_types_v1.ConsumptionStatus,
	req *api_common_v1.ListRequest,
) ([]*domain_entities_users.UserSummaryEntity, *api_common_v1.ListResponse, error) {
	var (
		userIds      []int64
		listResponse *api_common_v1.ListResponse
		err          error
	)
	switch consumptionStatus {
	case api_items_story_activity_types_v1.ConsumptionStatus_CONSUMPTION_STATUS_UNLOCKED:
		userIds, listResponse, err = r.storyContextRepo.ListUnlockedUserIds(ctx, storyId, req)
	case api_items_story_activity_types_v1.ConsumptionStatus_CONSUMPTION_STATUS_PLAYED:
		userIds, listResponse, err = r.storyContextRepo.ListPlayedUserIds(ctx, storyId, req)
	}
	if err != nil {
		return nil, nil, err
	}

	userIds = lo.Uniq(userIds)

	users, err := r.usersInfoQueryRepo.BatchGetUserInfo(ctx, loginUserId, userIds...)
	if err != nil {
		return nil, nil, err
	}

	results := make([]*domain_entities_users.UserSummaryEntity, 0, len(users))
	for _, userId := range userIds {
		if user, ok := users[userId]; ok {
			results = append(results, user)
		}
	}
	return results, listResponse, nil
}

func (r *StoryQueryRepo) ListUnlockedStory(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
	filterVersion []domain_entities_items.StoryVersion,
	filterPlayTypes []api_items_story_types_v1.StoryPlayType,
) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error) {
	storyIds, lisp, err := r.storyContextRepo.BatchGetUnlockedStoryIds(ctx, req, loginUserId, filterVersion, filterPlayTypes)
	if err != nil {
		return nil, nil, err
	}
	if len(storyIds) == 0 {
		return nil, lisp, nil
	}
	details, err := r.BatchGetStoryDetails(ctx, loginUserId, storyIds)
	if err != nil {
		return nil, nil, err
	}

	var result []*domain_entities_items.StoryDetail
	for _, storyId := range storyIds {
		detail, ok := details[storyId]
		if !ok {
			continue
		}
		result = append(result, detail)
	}
	return result, lisp, nil
}

func (r *StoryQueryRepo) BatchGetSummaries(
	ctx context.Context,
	userId int64,
	storyIds []int64,
) (map[int64]*domain_entities_items.StorySummary, error) {
	q := dal.Use(r.db.MasterDB(ctx)).Story
	conditions := []gen.Condition{
		q.ID.In(storyIds...),
	}
	stories, err := q.WithContext(ctx).Where(conditions...).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "batch get story summaries")
	}
	summaries, err := adapter_driving_assembler.ConvertStoryPoToSummaryEntities(stories...)
	if err != nil {
		return nil, err
	}
	if err := r.storyReactionRepo.InjectLoginUserMadeReactions(ctx, userId, summaries...); err != nil {
		return nil, err
	}
	if err := r.injectRecentUnlockedUsers(ctx, userId, summaries...); err != nil {
		return nil, err
	}
	if err := r.storyStatRepo.InjectStats(ctx, summaries); err != nil {
		return nil, err
	}
	if err := r.injectExamples(ctx, userId, summaries); err != nil {
		return nil, err
	}
	if err := r.injectCreators(ctx, userId, summaries); err != nil {
		return nil, err
	}
	if err := r.injectHauntBoos(ctx, userId, summaries); err != nil {
		return nil, err
	}
	return lo.SliceToMap(summaries, func(item *domain_entities_items.StorySummary) (int64, *domain_entities_items.StorySummary) {
		return item.Id, item
	}), nil
}

func (r *StoryQueryRepo) BatchGetStoryDetails(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryDetail, error) {
	summaries, err := r.BatchGetSummaries(ctx, loginUserId, storyIds)
	if err != nil {
		return nil, err
	}
	details := make([]*domain_entities_items.StoryDetail, 0, len(storyIds))
	for _, storyId := range storyIds {
		story, ok := summaries[storyId]
		if !ok {
			continue
		}
		detail := &domain_entities_items.StoryDetail{
			Summary: story,
		}
		details = append(details, detail)
	}
	if err := r.injectPlayContext(ctx, loginUserId, details...); err != nil {
		return nil, err
	}
	if err := r.injectJoinedPortal(ctx, loginUserId, details...); err != nil {
		return nil, err
	}

	return lo.SliceToMap(details, func(item *domain_entities_items.StoryDetail) (int64, *domain_entities_items.StoryDetail) {
		return item.Summary.Id, item
	}), nil
}
func (r *StoryQueryRepo) GetStoryDetail(ctx context.Context, loginUserId int64, storyId int64) (*domain_entities_items.StoryDetail, error) {
	details, err := r.BatchGetStoryDetails(ctx, loginUserId, []int64{storyId})
	if err != nil {
		return nil, err
	}
	detail, ok := details[storyId]
	if !ok {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("story not found with id: %d", storyId))
	}
	return detail, nil
}

func (r *StoryQueryRepo) injectJoinedPortal(ctx context.Context, loginUserId int64, stories ...*domain_entities_items.StoryDetail) error {
	storyIds := lo.Map(lo.Filter(stories, func(item *domain_entities_items.StoryDetail, _ int) bool {
		return item.Summary.Author.ID != loginUserId
	}), func(item *domain_entities_items.StoryDetail, _ int) int64 {
		return item.Summary.Id
	})
	if len(storyIds) == 0 {
		return nil
	}

	portalQ := dal.Use(r.db.MasterDB(ctx)).Portal
	portalModels, err := portalQ.WithContext(ctx).Where(
		portalQ.StoryID.In(storyIds...),
		// 没有过期，没有删除
		portalQ.ExpiredAt.Gt(time.Now()),
		portalQ.Status.Eq(api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_NORMAL.String()),
	).Select(portalQ.ID, portalQ.StoryID).Find()
	if err != nil {
		return err
	}

	storyIdPortalIdsMap := lo.SliceToMap(portalModels, func(item *model.Portal) (int64, int64) {
		return item.StoryID, item.ID
	})
	portalIdStoryIdMap := lo.SliceToMap(portalModels, func(item *model.Portal) (int64, int64) {
		return item.ID, item.StoryID
	})

	joinQ := dal.Use(r.db.MasterDB(ctx)).PortalUserJoinRelation

	joinedPortals, err := joinQ.WithContext(ctx).Where(
		joinQ.UserID.Eq(loginUserId),
		joinQ.PortalID.In(lo.Values(storyIdPortalIdsMap)...),
	).Find()

	if err != nil {
		return err
	}
	for _, story := range stories {
		for _, portal := range joinedPortals {
			portalMapStoryId := portalIdStoryIdMap[portal.PortalID]
			if portalMapStoryId == story.Summary.Id {
				story.JoinedPortalId = portal.PortalID
				break
			}
		}
	}
	return nil
}

func (r *StoryQueryRepo) injectPlayContext(ctx context.Context, loginUserId int64, stories ...*domain_entities_items.StoryDetail) error {
	storyIds := lo.Map(stories, func(item *domain_entities_items.StoryDetail, _ int) int64 {
		return item.Summary.Id
	})

	exchangeImageContexts, err := r.storyContextRepo.BatchGetImageExchangePlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	turtleSoupContexts, err := r.storyContextRepo.BatchGetTurtleSoupPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	unmuteContexts, err := r.storyContextRepo.BatchGetUnmutePlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	basePlayContexts, err := r.storyContextRepo.BatchGetBasePlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	nowShotContexts, err := r.storyContextRepo.BatchGetNowShotPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	roastedContexts, err := r.storyContextRepo.BatchGetRoastedPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	capsuleContexts, err := r.storyContextRepo.BatchGetCapsulePlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	hideContexts, err := r.storyContextRepo.BatchGetHidePlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	chatProxyContexts, err := r.storyContextRepo.BatchGetChatProxyPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	whoContexts, err := r.storyContextRepo.BatchGetWhoPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	wassupContexts, err := r.storyContextRepo.BatchGetWassupPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	hauntContexts, err := r.storyContextRepo.BatchGetHauntPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	pinContexts, err := r.storyContextRepo.BatchGetPinPlayRecords(ctx, loginUserId, storyIds)
	if err != nil {
		return err
	}
	for _, story := range stories {
		switch story.Summary.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HAUNT:
			context, ok := hauntContexts[story.Summary.Id]
			if ok {
				story.HauntPlayContext = context
				continue
			}
			story.HauntPlayContext = &domain_entities_items.HauntStoryContext{
				TryCount: 0,
				Unlocked: false,
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO:
			context, ok := whoContexts[story.Summary.Id]
			if ok {
				story.WhoPlayContext = context
				continue
			}
			story.WhoPlayContext = &domain_entities_items.StoryPlayWhoContext{
				EnabledOptionUserIds: story.Summary.WhoPlayConfig.OptionUserIds,
				MaxTryCount:          3, // default vlaue
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
			context, ok := chatProxyContexts[story.Summary.Id]
			if ok {
				story.ChatproxyContext = context
				continue
			}
			story.ChatproxyContext = &domain_entities_items.StoryPlayChatProxyContext{}

		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
			context, ok := basePlayContexts[story.Summary.Id]
			story.Summary.HasUnlocked = ok
			if ok {
				story.BasePlayContext = context
				story.Summary.HasUnlocked = context.IsUnlocked
				continue
			}
			// 构建一个初始化的 context，总是从第一个 node 开始
			firstNode := story.Summary.BasePlayConfig.Nodes[0]
			story.BasePlayContext = &domain_entities_items.StoryBasePlayContext{
				CurrentNodeID:  firstNode.ID,
				CurrentNodeIdx: 0,
				IsUnlocked:     false,
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			context, ok := exchangeImageContexts[story.Summary.Id]
			if ok {
				story.ExchangeImagePlayContext = context
				story.Summary.HasUnlocked = story.IsFinished()
				continue
			}
			// legacy v1 logic
			if len(story.Summary.ExchangeImagePlayConfig.Nodes) > 0 {
				// 构建一个初始化的 context
				firstNode := story.Summary.ExchangeImagePlayConfig.Nodes[0]
				story.ExchangeImagePlayContext = &domain_entities_items.ExchangeImagePlayContext{
					CurrentNodeID:          firstNode.ID,
					CurrentTryCount:        map[string]uint32{},
					CurrentSuccessProgress: map[string]uint32{},
					UserExchangeImageKeys:  map[string][]domain_entities_resource.ImageResourcePath{},
				}
			} else {
				story.ExchangeImagePlayContext = &domain_entities_items.ExchangeImagePlayContext{
					UserTrialImageUrls: make([]domain_entities_resource.ImageResourcePath, 0),
				}
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS:
			context, ok := turtleSoupContexts[story.Summary.Id]
			if ok {
				story.TurtleSoupPlayContext = context
				story.Summary.HasUnlocked = context.IsFinished
				continue
			}
			// 构建一个初始化的 context
			story.TurtleSoupPlayContext = &domain_entities_items.StoryPlayTurtleSoupContext{
				IsFinished: false,
				HitWords:   []string{},
				Tips:       "The content is still blurry...", // 目前是写死的
				AiResponse: "",
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			context, ok := unmuteContexts[story.Summary.Id]
			story.Summary.HasUnlocked = ok
			if ok {
				story.Summary.HasUnlocked = context.IsFinished
				story.UnmutePlayContext = context
				continue
			}
			// 构建一个初始化的 context
			story.UnmutePlayContext = &domain_entities_items.StoryPlayUnmuteContext{
				IsFinished: false,
				AiResponse: "",
				TryCount:   0,
				AudioKeys:  []string{},
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			context, ok := nowShotContexts[story.Summary.Id]
			story.Summary.HasUnlocked = ok
			if ok {
				story.Summary.HasUnlocked = context.ConsumeStatus == api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK
				story.NowShotPlayContext = context
				continue
			}
			// 构建一个初始化的 context
			story.NowShotPlayContext = &domain_entities_items.StoryPlayNowShotContext{
				ConsumeStatus:       api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_LOCK,
				StartTime:           0,
				UserSubmitImageKeys: []domain_entities_resource.ImageResourcePath{},
				UserSubmitVideoKeys: []domain_entities_resource.VideoResourcePath{},
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
			context, ok := roastedContexts[story.Summary.Id]
			story.Summary.HasUnlocked = ok
			if ok {
				story.RoastedPlayContext = context
				continue
			}
			// 构建一个初始化的 context
			story.RoastedPlayContext = &domain_entities_items.StoryPlayRoastedContext{
				IsConsumed: false,
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
			context, ok := wassupContexts[story.Summary.Id]
			story.Summary.HasUnlocked = ok
			if ok {
				story.WassupPlayContext = context
				continue
			}
			story.WassupPlayContext = &domain_entities_items.StoryPlayWassupContext{}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE:
			context, ok := capsuleContexts[story.Summary.Id]
			story.Summary.HasUnlocked = ok
			if ok {
				story.CapsulePlayContext = context
				continue
			}
			story.CapsulePlayContext = &domain_entities_items.StoryPlayCapsuleContext{
				IsConsumed: false,
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HIDE:
			context, ok := hideContexts[story.Summary.Id]
			if !ok {
				context = &domain_entities_items.StoryPlayHideContext{
					IsConsumed: false,
				}
			}
			story.HidePlayContext = context
			story.Summary.HasUnlocked = context.IsConsumed
			continue
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_PIN:
			context, ok := pinContexts[story.Summary.Id]
			if ok {
				story.PinPlayContext = context
				continue
			}
			story.PinPlayContext = &domain_entities_items.StoryPinContext{}
		}
	}

	if err := r.initWhoContext(ctx, loginUserId, stories...); err != nil {
		return err
	}

	return nil
}

func (r *StoryQueryRepo) injectExamples(ctx context.Context, loginUserId int64, stories []*domain_entities_items.StorySummary) error {
	for _, story := range stories {
		switch story.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			if err := r.injectImageChangeExamples(ctx, loginUserId, story); err != nil {
				return err
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
			if err := r.injectTurtleSoupExamples(ctx, loginUserId, story); err != nil {
				return err
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS:
			if err := r.injectTurtleSoupMassExamples(ctx, loginUserId, story); err != nil {
				return err
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			if err := r.injectUnmuteExamples(ctx, loginUserId, story); err != nil {
				return err
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
			// 手动拼接一个
			story.PlayBasePlayExample = &domain_entities_items.StoryPlayBasePlayExample{
				CoverImageUrl: story.GetStoryCoverPath().ItemPosterInSummary(),
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			if err := r.injectNowShotExamples(ctx, loginUserId, story); err != nil {
				return err
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
			if err := r.injectRoastedExamples(ctx, loginUserId, story); err != nil {
				return err
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE:
			if err := r.injectCapsuleExamples(ctx, loginUserId, story); err != nil {
			}
		}
	}
	return nil
}

func (r *StoryQueryRepo) injectRecentUnlockedUsers(ctx context.Context, loginUserId int64, stories ...*domain_entities_items.StorySummary) error {

	storyIds := lo.Map(stories, func(item *domain_entities_items.StorySummary, _ int) int64 {
		return item.Id
	})

	unlockedUserIds, err := r.storyContextRepo.BatchListUnlockedUserIds(ctx, storyIds, 10)
	if err != nil {
		return err
	}
	userIds := []int64{}
	for _, v := range unlockedUserIds {
		userIds = append(userIds, v.UserIds...)
	}
	userIds = lo.Uniq(userIds)

	users, err := r.usersInfoQueryRepo.BatchGetUserInfo(ctx, loginUserId, userIds...)
	if err != nil {
		return err
	}

	for _, story := range stories {
		story.UnlockedUsersInfo = &domain_entities_items.UnlockedUsersInfo{}
		if v, ok := unlockedUserIds[story.Id]; ok {
			story.UnlockedUsersInfo.Total = v.Total
			for _, userId := range v.UserIds {
				if user, ok := users[userId]; ok {
					story.UnlockedUsersInfo.Users = append(story.UnlockedUsersInfo.Users, user)
				}
			}
		}
	}
	return nil
}

func (r *StoryQueryRepo) injectCapsuleExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	story.PlayCapsuleExample = &domain_entities_items.StoryPlayCapsuleExample{
		CommonInfo: &domain_entities_items.StoryExampleCommonInfo{
			ImageUrls: []string{
				story.GetStoryCoverPath().ItemPosterInSummary(),
			},
		},
		PhotoInfo: &api_items_story_types_v1.CapsulePhotoInfo{
			InDays:  story.CapsulePlayConfig.CapsulePhotoInfo.InDays,
			Moments: story.CapsulePlayConfig.CapsulePhotoInfo.Moments,
		},
	}
	return nil
}

func (r *StoryQueryRepo) injectRoastedExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	// 从 config 内读取 topics
	story.PlayRoastedExample = &domain_entities_items.StoryPlayRoastedExample{
		Topic: &api_items_story_types_v1.StoryPlayRoastedTopic{
			Greeting: &api_items_story_types_v1.StoryPlayRoastedTopic_BotAnnouncement{
				Content:     story.RoastedPlayConfig.Topic.Greeting.Content,
				TtsAudioKey: string(story.RoastedPlayConfig.Topic.Greeting.TTSAudioKey),
				TtsAudioUrl: story.RoastedPlayConfig.Topic.Greeting.TTSAudioKey.URL(),
			},
			Ending: &api_items_story_types_v1.StoryPlayRoastedTopic_BotAnnouncement{
				Content:     story.RoastedPlayConfig.Topic.Ending.Content,
				TtsAudioKey: string(story.RoastedPlayConfig.Topic.Ending.TTSAudioKey),
				TtsAudioUrl: story.RoastedPlayConfig.Topic.Ending.TTSAudioKey.URL(),
			},
			Questions: lo.Map(story.RoastedPlayConfig.Topic.Questions, func(item *domain_entities_items.StoryPlayRoastedQuestion, _ int) *api_items_story_types_v1.RoastedQuestion {
				return &api_items_story_types_v1.RoastedQuestion{
					Question:    item.Question,
					TtsAudioUrl: item.TTSAudioKey.URL(),
					TtsAudioKey: string(item.TTSAudioKey),
					Thinking:    item.Thinking,
				}
			}),
		},
	}
	return nil
}
func (r *StoryQueryRepo) injectImageChangeExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	if len(story.ExchangeImagePlayConfig.Nodes) == 0 {
		return nil
	}
	randomAiResponses := []string{
		"Almost there, can you show off your abs more clearly? 🔥😉",
		"Nice pic! But let's see those abs a bit clearer 💪✨",
		"Cute selfie! But can I see you clearly in the mirror? 🪞✨",
		"Almost perfect—make sure it's a clear mirror shot! 😉",
		"Almost got it! Can you show the drink clearly? 🥤✨",
		"Love the selfie—just missing your beverage! 😉",
		"Love the selfie! Just show me those nails clearly 💅",
		"Almost there—let's see your nails a bit better! 😉",
		"Nice pic! Just missing your snack clearly 🍓😉",
		"Close! Clearly show me the snack too 🍒",
		"Nice look! Just show those shoulders clearly 😉",
		"Almost there—try showing a bit more shoulder! 🌬️✨",
		"So cute! Can I see your lipstick clearly though? 💄✨",
		"Almost perfect! Just clearly show those lips 😉",
		"Cute selfie! Can you clearly show you're chilling on your bed though? 🛏️✨",
		"Almost got it! Just clearly include your bed 😉",
		"Looking good! Just need a clearer fresh-out-the-shower vibe 🚿😉",
		"Nice selfie! Can you show clearly you've just stepped out of the shower? 🔥",
		"Almost got me! Can you make your expression a bit more flirty? 😉✨",
		"Cute selfie! Just clearly show you're on your bed with a seductive look 🛌🔥",
		"Almost seductive! Can you clearly show me that lip bite? 😉✨",
		"Cute—but make that lip bite a bit clearer next time 💋",
	}
	exchangeImagesUrls := []domain_entities_resource.ImageResourcePath{
		"flippop/image/story/template/exchange_images/01.png",
		"flippop/image/story/template/exchange_images/02.png",
		"flippop/image/story/template/exchange_images/03.png",
		"flippop/image/story/template/exchange_images/04.png",
		"flippop/image/story/template/exchange_images/05.png",
		"flippop/image/story/template/exchange_images/06.png",
		"flippop/image/story/template/exchange_images/07.png",
		"flippop/image/story/template/exchange_images/08.png",
		"flippop/image/story/template/exchange_images/09.png",
		"flippop/image/story/template/exchange_images/10.png",
		"flippop/image/story/template/exchange_images/11.png",
		"flippop/image/story/template/exchange_images/12.png",
		"flippop/image/story/template/exchange_images/13.png",
		"flippop/image/story/template/exchange_images/14.png",
		"flippop/image/story/template/exchange_images/15.png",
		"flippop/image/story/template/exchange_images/17.png",
		"flippop/image/story/template/exchange_images/18.png",
		"flippop/image/story/template/exchange_images/19.png",
		"flippop/image/story/template/exchange_images/20.png",
		"flippop/image/story/template/exchange_images/21.png",
		"flippop/image/story/template/exchange_images/22.png",
		"flippop/image/story/template/exchange_images/23.png",
		"flippop/image/story/template/exchange_images/24.png",
		"flippop/image/story/template/exchange_images/25.png",
		"flippop/image/story/template/exchange_images/26.png",
		"flippop/image/story/template/exchange_images/27.png",
		"flippop/image/story/template/exchange_images/28.png",
		"flippop/image/story/template/exchange_images/29.png",
		"flippop/image/story/template/exchange_images/30.png",
		"flippop/image/story/template/exchange_images/31.png",
		"flippop/image/story/template/exchange_images/32.png",
	}
	story.PlayExchangeImageExample = &domain_entities_items.StoryPlayExchangeImageExample{
		CommonInfo: &domain_entities_items.StoryExampleCommonInfo{
			Tips: story.ExchangeImagePlayConfig.Nodes[0].Condition.Tips,
			ImageUrls: []string{
				story.GetStoryCoverPath().ItemPosterInSummary(),
			},
		},
		Cases: []*domain_entities_items.StoryPlayExchangeImageExample_Case{},
	}
	// 随机选择2-3个案例，但不超过可用资源数量
	maxPossibleCases := min(len(randomAiResponses), len(exchangeImagesUrls))
	// 否则随机选择2-3个，但不超过可用资源数量
	numCases := min(rand.Intn(2)+2, maxPossibleCases)

	// 创建可用索引的切片，用于随机选择
	availableAiIndices := make([]int, len(randomAiResponses))
	for i := range availableAiIndices {
		availableAiIndices[i] = i
	}
	availableImageIndices := make([]int, len(exchangeImagesUrls))
	for i := range availableImageIndices {
		availableImageIndices[i] = i
	}

	// 随机打乱索引切片
	rand.Shuffle(len(availableAiIndices), func(i, j int) {
		availableAiIndices[i], availableAiIndices[j] = availableAiIndices[j], availableAiIndices[i]
	})
	rand.Shuffle(len(availableImageIndices), func(i, j int) {
		availableImageIndices[i], availableImageIndices[j] = availableImageIndices[j], availableImageIndices[i]
	})

	// 选择前numCases个索引
	for i := 0; i < numCases; i++ {
		aiIndex := availableAiIndices[i]
		imageIndex := availableImageIndices[i]

		// 添加到案例中
		story.PlayExchangeImageExample.Cases = append(story.PlayExchangeImageExample.Cases, &domain_entities_items.StoryPlayExchangeImageExample_Case{
			ExchangeImageUrl: exchangeImagesUrls[imageIndex].ItemPosterInSummary(),
			AiResponse:       randomAiResponses[aiIndex],
		})
	}

	if story.SubPlayType == api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_LINEAR {
		// 再次拼接第二个node的封面图作为 urls
		story.PlayExchangeImageExample.CommonInfo.ImageUrls = append(
			story.PlayExchangeImageExample.CommonInfo.ImageUrls,
			story.ExchangeImagePlayConfig.Nodes[1].GetCoverImagePath().ItemPosterInSummary(),
		)
	}
	return nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
func (r *StoryQueryRepo) injectTurtleSoupMassExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	userMessages := []string{
		"Bored tonight, mind some company?",
		"Need company? I have great movie taste 😉",
		"Watch 'Interstellar,' it's great!",
		"Sounds chill, need company?",
		"I'm bored, wanna chat?",
		"Mind some company? I'm up for a movie night 😉",
		"Bored tonight, mind some company?",
		"Need company? I have great movie taste 😉",
		"Watch 'Interstellar,' it's great!",
	}
	aiResponses := []string{
		"Noted! Thanks for the rec 👍",
		"Rest well! 😴✨",
		"Thanks for sharing! 🙂",
		"Cool, thanks for letting me know! 👋",
		"Got it, thanks! Have a good one 😊",
		"Thanks for the suggestion! 👍",
		"Appreciate the recommendation! 🙌",
		"Thanks, I'll check it out! ✨",
		"Cool recommendation, thanks! 😊",
		"Thanks for sharing that! 🌟",
		"Noted with thanks! 👋",
		"Thanks for the tip! 😄",
	}
	story.PlayTurtleSoupMassExample = &domain_entities_items.StoryPlayTurtleSoupMassExample{
		CommonInfo: &domain_entities_items.StoryExampleCommonInfo{
			Tips: story.TurtleSoupPlayConfig.Caption,
			ImageUrls: []string{
				story.GetStoryCoverPath().ItemPosterInSummary(),
			},
		},
		Cases: []*domain_entities_items.StoryPlayTurtleSoupMassExample_Case{},
	}
	// 使用本地随机数生成器
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < 3; i++ {
		userMessageIdx := rng.Intn(len(userMessages))
		aiResponseIdx := rng.Intn(len(aiResponses))
		userMessage := userMessages[userMessageIdx]
		aiResponse := aiResponses[aiResponseIdx]
		story.PlayTurtleSoupMassExample.Cases = append(story.PlayTurtleSoupMassExample.Cases, &domain_entities_items.StoryPlayTurtleSoupMassExample_Case{
			Tips:        "The following content is a bit blurry",
			UserMessage: userMessage,
			AiResponse:  aiResponse,
		})
	}

	return nil
}

func (r *StoryQueryRepo) injectTurtleSoupExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	userMessages := []string{
		"Bored tonight, mind some company?",
		"Need company? I have great movie taste 😉",
		"Watch 'Interstellar,' it's great!",
		"Sounds chill, need company?",
		"I'm bored, wanna chat?",
		"Mind some company? I'm up for a movie night 😉",
		"Bored tonight, mind some company?",
		"Need company? I have great movie taste 😉",
		"Watch 'Interstellar,' it's great!",
	}
	aiResponses := []string{
		"Noted! Thanks for the rec 👍",
		"Rest well! 😴✨",
		"Thanks for sharing! 🙂",
		"Cool, thanks for letting me know! 👋",
		"Got it, thanks! Have a good one 😊",
		"Thanks for the suggestion! 👍",
		"Appreciate the recommendation! 🙌",
		"Thanks, I'll check it out! ✨",
		"Cool recommendation, thanks! 😊",
		"Thanks for sharing that! 🌟",
		"Noted with thanks! 👋",
		"Thanks for the tip! 😄",
	}
	story.PlayTurtleSoupExample = &domain_entities_items.StoryPlayTurtleSoupExample{
		CommonInfo: &domain_entities_items.StoryExampleCommonInfo{
			Tips: story.TurtleSoupPlayConfig.Caption,
			ImageUrls: []string{
				story.GetStoryCoverPath().ItemPosterInSummary(),
				story.TurtleSoupPlayConfig.GetEndResourceImageKey().ItemPosterInSummary(),
			},
		},
		Cases:          []*domain_entities_items.StoryPlayTurtleSoupExample_Case{},
		EndMessage:     story.TurtleSoupPlayConfig.EndMessage,
		EndMessageFont: story.TurtleSoupPlayConfig.EndMessageFont,
	}
	// 使用本地随机数生成器
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < 3; i++ {
		userMessageIdx := rng.Intn(len(userMessages))
		aiResponseIdx := rng.Intn(len(aiResponses))
		userMessage := userMessages[userMessageIdx]
		aiResponse := aiResponses[aiResponseIdx]
		hitWords := extractHitWords(aiResponse)
		story.PlayTurtleSoupExample.Cases = append(story.PlayTurtleSoupExample.Cases, &domain_entities_items.StoryPlayTurtleSoupExample_Case{
			Tips:        "The following content is a bit blurry",
			UserMessage: userMessage,
			HitWords:    hitWords,
			AiResponse:  aiResponse,
		})
	}

	return nil
}

func (r *StoryQueryRepo) injectUnmuteExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	prompts := []string{
		"Must mention coffee, tea, cafés, or relaxing activities",
		"Must mention beauty, skincare, makeup, or fashion",
		"Must mention weekend plans or leisure activities",
		/*
			"Describe exactly what you'd do if I were with you right now.",
			"Complain about your roommate but sound sweet.",
			"Tell me something good that happened today.",
			"Say something to make me just a little jealous.",
			"Give me your honest opinion on my outfit today—no sugarcoating.",
			"Convince me to ditch homework and binge-watch our show.",
			"Spill some tea from brunch today.",
			"Give me a compliment that crosses the friendship line.",
			"You really want to see me, you can't keep silent anymore and beg me to see you.",
			"Whisper a secret fantasy you'd never admit IRL.",
			"Confess your biggest turn-on in five seconds.",
			"Tell me you miss me… as a Red Flag 🚩",
			"Say something encouraging to start the day.",
			"Wake me up with your best sleepy morning voice.",
			"Describe your crush in one subtle detail.",
		*/
	}
	imageURL := ""
	switch story.UnmutePlayConfig.ResourceType {
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
		imageURL = story.UnmutePlayConfig.ThumbnailKey.ItemPosterInSummary()
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
		imageURL = story.UnmutePlayConfig.ResourceImageKey.ItemPosterInSummary()
	}
	story.PlayUnmuteExample = &domain_entities_items.StoryPlayUnmuteExample{
		CommonInfo: &domain_entities_items.StoryExampleCommonInfo{
			Tips: story.UnmutePlayConfig.Prompt.Text,
			ImageUrls: []string{
				imageURL,
			},
		},
	}

	// 使用本地随机数生成器
	//rng := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < 3; i++ {
		promptsIdx := i // rng.Intn(len(prompts))
		prompt := prompts[promptsIdx]
		story.PlayUnmuteExample.Cases = append(story.PlayUnmuteExample.Cases, &domain_entities_items.StoryPlayUnmuteExample_Case{
			Prompt: prompt,
		})
	}
	return nil
}

func (r *StoryQueryRepo) injectNowShotExamples(ctx context.Context, loginUserId int64, story *domain_entities_items.StorySummary) error {
	nowShotCoverImagesUrls := []domain_entities_resource.ImageResourcePath{
		"flippop/image/story/template/now_shot/01.png",
		"flippop/image/story/template/now_shot/02.png",
		"flippop/image/story/template/now_shot/03.png",
	}
	imageURL := ""
	switch story.NowShotConfig.ResourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
		imageURL = story.NowShotConfig.ThumbnailKey.ItemPosterInSummary()
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
		imageURL = story.NowShotConfig.ResourceImageKey.ItemPosterInSummary()
	}

	endImageURL := ""
	switch story.NowShotConfig.EndResourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
		endImageURL = story.NowShotConfig.EndThumbnailKey.ItemPosterInSummary()
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
		endImageURL = story.NowShotConfig.EndResourceImageKey.ItemPosterInSummary()
	}

	story.PlayNowShotExample = &domain_entities_items.StoryPlayNowShotExample{
		CommonInfo: &domain_entities_items.StoryExampleCommonInfo{
			Tips:      story.NowShotConfig.Caption,
			ImageUrls: []string{imageURL, endImageURL},
		},
		Cases: []*domain_entities_items.StoryPlayNowShotExample_Case{},
	}
	for _, imgUrl := range nowShotCoverImagesUrls {
		story.PlayNowShotExample.Cases = append(story.PlayNowShotExample.Cases, &domain_entities_items.StoryPlayNowShotExample_Case{
			CoverImageUrl: imgUrl.ItemPosterInSummary(),
		})
	}
	return nil
}

// 这是个特殊的逻辑，由于 who story 内的 option user 在创作者填写时
// 可能为空，那么我们需要给登录用户初始化一些备选项，需要从登录用户的关注用户里去随机一些值
func (r *StoryQueryRepo) initWhoContext(ctx context.Context, userId int64, stories ...*domain_entities_items.StoryDetail) error {
	// 找到那些 who story 且 option 内只有一个值的 story
	whoStories := lo.Filter(stories, func(story *domain_entities_items.StoryDetail, _ int) bool {
		return story.Summary.PlayType == api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO &&
			len(story.Summary.WhoPlayConfig.OptionUserIds) == 1
	})
	if len(whoStories) == 0 {
		return nil
	}

	// 从 followingUsers 里去随机一些值
	followingUsers, _, err := r.relationsQueryRepo.ListUsersWithRelationType(ctx, userId, api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING, &api_common_v1.ListRequest{
		PageSize: "10",
	})
	if err != nil {
		return err
	}

	// 如果 followingUsers 不足2，就取创作者的 followingUsers
	if len(followingUsers) < 2 {
		followingUsers, _, err = r.relationsQueryRepo.ListUsersWithRelationType(ctx, userId, api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING, &api_common_v1.ListRequest{
			PageSize: "10",
		})
		if err != nil {
			return err
		}
	}

	// 依然不足 2，放弃
	if len(followingUsers) < 2 {
		return nil
	}

	// 创建本地随机数生成器
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))

	needSaveContexts := make(map[int64]domain_entities_items.StoryPlayWhoContext)

	// 遍历每个需要补充选项的 WHO 故事
	for _, story := range whoStories {
		// 获取当前故事已有的选项用户ID
		existingOptionId := story.Summary.WhoPlayConfig.OptionUserIds[0]

		// 从 followingUsers 中筛选出不是已有选项的用户和当前用户本身
		availableUsers := lo.Filter(followingUsers, func(followingUserId int64, _ int) bool {
			return followingUserId != existingOptionId && followingUserId != userId
		})
		if len(availableUsers) < 2 {
			continue
		}

		// 打乱可用用户列表
		rng.Shuffle(len(availableUsers), func(i, j int) {
			availableUsers[i], availableUsers[j] = availableUsers[j], availableUsers[i]
		})

		// 选择前两个用户作为新的选项
		context := story.WhoPlayContext
		context.EnabledOptionUserIds = append(context.EnabledOptionUserIds, availableUsers[0], availableUsers[1])
		needSaveContexts[story.Summary.Id] = *context
	}
	if len(needSaveContexts) > 0 {
		return r.storyContextRepo.SaveWhoPlayRecords(ctx, userId, needSaveContexts)
	}
	return nil
}

func (r *StoryQueryRepo) injectCreators(ctx context.Context, loginUserId int64, stories []*domain_entities_items.StorySummary) error {

	var creatorIds []int64
	for _, story := range stories {
		creatorIds = append(creatorIds, story.Author.ID)
		if story.PlayType == api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO {
			creatorIds = append(creatorIds, story.WhoPlayConfig.OptionUserIds...)
		}
	}

	creators, err := r.usersInfoQueryRepo.BatchGetUserInfo(ctx, loginUserId, creatorIds...)
	if err != nil {
		return err
	}

	for _, story := range stories {
		story.Author = creators[story.Author.ID]
		if story.PlayType == api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO {
			for _, optionUserId := range story.WhoPlayConfig.OptionUserIds {
				story.WhoPlayConfig.OptionUsers = append(story.WhoPlayConfig.OptionUsers, creators[optionUserId])
			}
		}
	}

	return nil
}

func extractHitWords(endMessage string) []string {
	// 创建本地随机数生成器
	rng := rand.New(rand.NewSource(time.Now().UnixNano()))

	// 创建一个新的分词器
	tokenizer := nlp.NewTokeniser()

	// 使用分词器对 endMessage 进行分词
	tokens := tokenizer.Tokenise(endMessage)

	// 随机选择3到5个关键词
	numHitWords := rng.Intn(3) + 3 // 生成3到5之间的随机数
	if numHitWords > len(tokens) {
		numHitWords = len(tokens)
	}

	// 打乱 tokens 列表
	rng.Shuffle(len(tokens), func(i, j int) {
		tokens[i], tokens[j] = tokens[j], tokens[i]
	})

	// 选择前 numHitWords 个词作为 hitwords
	hitWords := tokens[:numHitWords]

	return hitWords
}

const (
	cacheDescriptionKeyFmt = "story_llm_frames_description_%d_%s"
	cacheHintKeyFmt        = "story_llm_frames_hint_%d_%s"
)

func (r *StoryQueryRepo) CacheLLMFramesDescription(ctx context.Context, userId int64, imageKey string, description string, shootingModel string, caption string) error {
	if err := r.cacheLLMResult(ctx, userId, imageKey, description, cacheDescriptionKeyFmt); err != nil {
		return errors.Wrapf(err, "user %d, image_key %s", userId, imageKey)
	}
	val := struct {
		ShootingModel string `json:"shootingModel"`
		Caption       string `json:"caption"`
	}{
		ShootingModel: shootingModel,
		Caption:       caption,
	}
	valString, _ := json.Marshal(val)
	if err := r.cacheLLMResult(ctx, userId, imageKey, string(valString), cacheHintKeyFmt); err != nil {
		return errors.Wrapf(err, "user %d, image_key %s", userId, imageKey)
	}
	return nil
}

func (r *StoryQueryRepo) cacheLLMResult(ctx context.Context, userId int64, imageKey string, value string, keyFormat string) error {
	cacheKey := fmt.Sprintf(keyFormat, userId, imageKey)
	if err := r.db.Rdb().Set(ctx, cacheKey, value, time.Minute*10).Err(); err != nil {
		return errors.Wrapf(err, "user %d, image_key %s, keyFormat %s, value %s", userId, imageKey, cacheKey, value)
	}
	return nil
}

func (r *StoryQueryRepo) RetrieveLLMFramesDescription(ctx context.Context, userId int64, imageKeys []string) ([]string, []string, error) {
	descriptions, err := r.retrieveValues(ctx, userId, imageKeys, cacheDescriptionKeyFmt)
	if err != nil {
		return []string{}, []string{}, errors.Wrapf(err, "user %d, image_key %v", userId, imageKeys)
	}
	hints, err := r.retrieveValues(ctx, userId, imageKeys, cacheHintKeyFmt)
	if err != nil {
		return []string{}, []string{}, errors.Wrapf(err, "user %d, image_key %v", userId, imageKeys)
	}
	return descriptions, hints, nil
}

func (r *StoryQueryRepo) retrieveValues(ctx context.Context, userId int64, imageKeys []string, keyFormat string) ([]string, error) {
	// 如果 imageKeys 为空，直接返回空切片，避免 MGet 命令参数错误
	if len(imageKeys) == 0 {
		return []string{}, nil
	}

	cacheKeys := lo.Map(imageKeys, func(imageKey string, _ int) string {
		return fmt.Sprintf(keyFormat, userId, imageKey)
	})
	cacheValues, err := r.db.Rdb().MGet(ctx, cacheKeys...).Result()
	if err != nil {
		return []string{}, errors.Wrapf(err, "user %d, image_key %v, keyFormat %s, keys %v", userId, imageKeys, keyFormat, cacheKeys)
	}
	ret := make([]string, 0, len(cacheValues))
	for _, value := range cacheValues {
		if v, ok := value.(string); ok {
			ret = append(ret, v)
		} else {
			ret = append(ret, "")
		}
	}
	return ret, nil
}
