package adapter_driving_repos_items_story

import (
	adapter_driving_repos_users_boo "boson/internal/adapter/driving/repos/users/boo"
	adapter_driving_repos_users_info "boson/internal/adapter/driving/repos/users/info"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_items_portal "boson/internal/domain/services/items/portal"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"
	"fmt"
	"time"

	api_errors_v1 "boson/api/errors/v1"
	api_items_portal_moment_types_v1 "boson/api/items/portal/moments/types/v1"
	api_items_portal_types_v1 "boson/api/items/portal/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var _ domain_services_items_portal.IPortalRepository = (*PortalRepo)(nil)

type PortalRepo struct {
	db               *data.Data
	uniqIdGen        domain_interfaces.UniqIdGenerator
	joinRelationRepo *UserPortalRelationRepo
	storyQueryRepo   *StoryQueryRepo
	userQueryRepo    *adapter_driving_repos_users_info.QueryRepo
	avatarQueryRepo  *adapter_driving_repos_users_boo.Repo
	locker           domain_interfaces.Locker
	logger           *log.Helper
}

func NewPortalCmdRepo(
	db *data.Data,
	uniqIdGen domain_interfaces.UniqIdGenerator,
	storyQueryRepo *StoryQueryRepo,
	userQueryRepo *adapter_driving_repos_users_info.QueryRepo,
	avatarQueryRepo *adapter_driving_repos_users_boo.Repo,
	locker domain_interfaces.Locker,
	joinRelationRepo *UserPortalRelationRepo,
	logger *log.Helper,
) *PortalRepo {
	return &PortalRepo{db: db, uniqIdGen: uniqIdGen, storyQueryRepo: storyQueryRepo, userQueryRepo: userQueryRepo, avatarQueryRepo: avatarQueryRepo, locker: locker, joinRelationRepo: joinRelationRepo, logger: logger}
}

// BatchGetMomentsAndUsersRelations implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) BatchGetMomentAndUsersRelations(ctx context.Context, momentId int64, userIds []int64) (map[int64][]*domain_entities_items.MomentRelation, error) {
	q := dal.Use(p.db.MasterDB(ctx)).PortalMomentRelation
	relations, err := q.WithContext(ctx).Where(
		q.MomentID.Eq(momentId),
		q.UserID.In(userIds...),
	).Find()
	if err != nil {
		return nil, err
	}
	relationIdMap := make(map[int64][]*domain_entities_items.MomentRelation)
	for _, relation := range relations {
		relationIdMap[relation.UserID] = append(relationIdMap[relation.UserID], &domain_entities_items.MomentRelation{
			UserId:       cast.ToString(relation.UserID),
			RelationType: api_items_portal_moment_types_v1.RelationType(api_items_portal_moment_types_v1.RelationType_value[relation.RelationType]),
		})
	}
	return relationIdMap, nil
}

func (p *PortalRepo) BatchGetMomentsAndUsersRelations(ctx context.Context, momentIds []int64, userIds []int64) (map[int64]map[int64][]*domain_entities_items.MomentRelation, error) {
	q := dal.Use(p.db.MasterDB(ctx)).PortalMomentRelation
	relations, err := q.WithContext(ctx).Where(
		q.MomentID.In(momentIds...),
		q.UserID.In(userIds...),
	).Find()
	if err != nil {
		return nil, err
	}
	relationIdMap := make(map[int64]map[int64][]*domain_entities_items.MomentRelation)
	for _, relation := range relations {
		userRelations, ok := relationIdMap[relation.MomentID]
		if !ok {
			userRelations = make(map[int64][]*domain_entities_items.MomentRelation)
		}
		userRelations[relation.UserID] = append(userRelations[relation.UserID], &domain_entities_items.MomentRelation{
			UserId:       cast.ToString(relation.UserID),
			RelationType: api_items_portal_moment_types_v1.RelationType(api_items_portal_moment_types_v1.RelationType_value[relation.RelationType]),
		})
		relationIdMap[relation.MomentID] = userRelations
	}
	return relationIdMap, nil
}

// MakeRelation implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) MakeRelation(ctx context.Context, userId int64, momentId int64, relationType api_items_portal_moment_types_v1.RelationType) error {
	// 加锁，查询是否已经有此关系
	lockKey := fmt.Sprintf("portal_moment_relation:%d:user:%d", momentId, userId)
	return p.locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		moments, err := p.BatchGetMoments(ctx, userId, []int64{momentId})
		if err != nil {
			return err
		}
		moment := moments[momentId]
		if moment == nil {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("moment : %d not found", momentId))
		}
		if lo.ContainsBy(moment.Relations, func(relation *domain_entities_items.MomentRelation) bool {
			return relation.RelationType == relationType
		}) {
			return nil
		}

		relationCmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentRelation
		statCmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentStat
		if err := relationCmd.WithContext(ctx).Create(
			&model.PortalMomentRelation{
				UserID:       userId,
				MomentID:     momentId,
				RelationType: relationType.String(),
			},
		); err != nil {
			return errors.Wrapf(err, "momentId: %d, userId: %d, relationType: %s", momentId, userId, relationType.String())
		}

		if _, err := statCmd.WithContext(ctx).Where(
			statCmd.MomentID.Eq(momentId),
		).UpdateColumnSimple(
			statCmd.LikeCount.Add(1),
		); err != nil {
			return errors.Wrapf(err, "momentId: %d, userId: %d, relationType: %s", momentId, userId, relationType.String())
		}
		return nil
	}, time.Second*10)
}

// RemoveRelation implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) RemoveRelation(ctx context.Context, userId int64, momentId int64, relationType api_items_portal_moment_types_v1.RelationType) error {
	// 使用相同的锁键值
	lockKey := fmt.Sprintf("portal_moment_relation:%d:user:%d", momentId, userId)
	return p.locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		// 检查 moment 和关系是否存在
		moments, err := p.BatchGetMoments(ctx, userId, []int64{momentId})
		if err != nil {
			return err
		}
		moment := moments[momentId]
		if moment == nil {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("moment : %d not found", momentId))
		}
		// 如果关系不存在，直接返回
		if !lo.ContainsBy(moment.Relations, func(relation *domain_entities_items.MomentRelation) bool {
			return relation.RelationType == relationType
		}) {
			return nil
		}

		relationCmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentRelation
		statCmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentStat

		// 删除关系记录
		if _, err := relationCmd.WithContext(ctx).Where(
			relationCmd.UserID.Eq(userId),
			relationCmd.MomentID.Eq(momentId),
			relationCmd.RelationType.Eq(relationType.String()),
		).Delete(); err != nil {
			return errors.Wrapf(err, "momentId: %d, userId: %d, relationType: %s", momentId, userId, relationType.String())
		}

		// 更新统计数据
		if _, err := statCmd.WithContext(ctx).Where(
			statCmd.MomentID.Eq(momentId),
		).UpdateColumnSimple(
			statCmd.LikeCount.Sub(1),
		); err != nil {
			return errors.Wrapf(err, "momentId: %d, userId: %d, relationType: %s", momentId, userId, relationType.String())
		}

		return nil
	}, time.Second*10)
}

// AppendMoment implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) AppendMoment(
	ctx context.Context,
	userId int64,
	portal *domain_entities_items.Portal,
	moment *domain_entities_items.Moment,
	createType api_items_portal_moment_types_v1.MomentCreateType,
) error {
	return p.db.ExecTx(ctx, func(ctx context.Context) error {
		momentCmd := dal.Use(p.db.MasterDB(ctx)).PortalMoment
		momentStatCmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentStat

		extraInfo, err := json.Marshal(moment.ExtraInfo)
		if err != nil {
			return err
		}
		if moment.Id == 0 {
			moment.Id = p.uniqIdGen.Generate()
		}

		if err := momentCmd.WithContext(ctx).Create(
			&model.PortalMoment{
				ID:         moment.Id,
				PortalID:   moment.PortalId,
				CreatorID:  moment.Creator.ID,
				Type:       moment.Type.String(),
				Status:     api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED.String(),
				ExtraInfo:  string(extraInfo),
				CreateType: createType.String(),
				CreatedAt:  time.Now(),
				UpdatedAt:  time.Now(),
			},
		); err != nil {
			return err
		}
		if err := momentStatCmd.WithContext(ctx).Create(
			&model.PortalMomentStat{
				MomentID: moment.Id,
			},
		); err != nil {
			return err
		}

		// 如果 portal 剩余时间不足 24小时，则延长 portal 的过期时间
		// 延长24小时
		remainingTime := time.Until(portal.ExpiredAt)
		if remainingTime < time.Hour*24 {
			portal.ExpiredAt = time.Now().Add(time.Hour * 24)
			potalCmd := dal.Use(p.db.MasterDB(ctx)).Portal
			if _, err := potalCmd.WithContext(ctx).Where(
				potalCmd.ID.Eq(portal.Id),
			).UpdateColumnSimple(
				potalCmd.ExpiredAt.Value(portal.ExpiredAt),
			); err != nil {
				return err
			}
		}
		return nil
	})
}

func (p *PortalRepo) DeleteMoment(ctx context.Context, momentId int64) error {
	cmd := dal.Use(p.db.MasterDB(ctx)).PortalMoment
	if _, err := cmd.WithContext(ctx).Where(
		cmd.ID.Eq(momentId),
	).UpdateColumnSimple(
		cmd.Status.Value(api_items_portal_moment_types_v1.Status_STATUS_DELETED.String()),
	); err != nil {
		return errors.Wrapf(err, "momentId: %d", momentId)
	}
	return nil
}

// CreatePortal implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) CreatePortal(ctx context.Context, story *domain_entities_items.StoryDetail, userMomentInfos []*domain_entities_items.MomentExtraInfo) (*domain_entities_items.Portal, error) {
	var createdPortal *domain_entities_items.Portal
	p.db.ExecTx(ctx, func(ctx context.Context) error {
		cmd := dal.Use(p.db.MasterDB(ctx)).Portal
		portalId := p.uniqIdGen.Generate()
		if err := cmd.WithContext(ctx).Create(
			&model.Portal{
				ID:        portalId,
				UserID:    story.Summary.Author.ID,
				StoryID:   story.Summary.Id,
				ExpiredAt: time.Now().Add(time.Hour * 72), // 默认 72 小时
				Status:    api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_NORMAL.String(),
			},
		); err != nil {
			return errors.Wrapf(err, "storyId: %d", story.Summary.Id)
		}
		portals, err := p.BatchGetPortals(ctx, story.Summary.Author.ID, []int64{portalId})
		if err != nil {
			return err
		}
		portal := portals[portalId]
		moments, err := p.parseStoryTopMoments(ctx, portal, story)
		if err != nil {
			return err
		}
		for _, moment := range moments {
			if err := p.AppendMoment(ctx, story.Summary.Author.ID, portal, moment, api_items_portal_moment_types_v1.MomentCreateType_MOMENT_CREATE_TYPE_INIT); err != nil {
				return err
			}
		}
		for _, momentInfo := range userMomentInfos {
			moment := &domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Type:      api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
				Status:    api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: momentInfo,
			}
			if err := p.AppendMoment(ctx, story.Summary.Author.ID, portal, moment, api_items_portal_moment_types_v1.MomentCreateType_MOMENT_CREATE_TYPE_INIT); err != nil {
				return err
			}
		}
		createdPortal = portal
		return nil
	})
	return createdPortal, nil
}

func (p *PortalRepo) parseStoryTopMoments(ctx context.Context, portal *domain_entities_items.Portal, story *domain_entities_items.StoryDetail) ([]*domain_entities_items.Moment, error) {
	moments := []*domain_entities_items.Moment{}
	switch story.Summary.PlayType {
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HAUNT:
		moments = p.getMomentsByParseHauntConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
		moments = p.getMomentsByParseStoryCommonConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
		moments = p.getMomentsByParseStoryCommonConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
		moments = p.getMomentsByParseStoryCommonConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
		moments = p.getMomentsByParseStoryCommonConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
		moments = p.getMomentsByParseBasePlayConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_PIN:
		moments = p.getMomentsByParsePinConfig(ctx, portal, story)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
		moments = append(moments,
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.RoastedPlayConfig.Cover,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			})
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HIDE:
		moments = append(moments,
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.HidePlayConfig.BackgroundImage,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
		)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
		moments = append(moments,
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.ChatProxyPlayConfig.CoverResource,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.ChatProxyPlayConfig.UnlockResource,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
		)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO:
		moments = append(moments,
			// 封面作为第一个
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.WhoPlayConfig.CoverResource,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
			// unlock 作为第二个
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.WhoPlayConfig.UnlockResource,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
		)
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
		moments = append(moments,
			// 封面作为第一个
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.WassupPlayConfig.CoverResource,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
			// unlock 作为第二个
			&domain_entities_items.Moment{
				PortalId: portal.Id,
				Creator: &domain_entities_users.UserSummaryEntity{
					ID: story.Summary.Author.ID,
				},
				Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
				ExtraInfo: &domain_entities_items.MomentExtraInfo{
					Resource: story.Summary.WassupPlayConfig.UnlockResource,
				},
				Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
			},
		)
	default:
		return nil, errors.New("unknown story type")
	}
	return moments, nil
}
func (p *PortalRepo) getMomentsByParseHauntConfig(ctx context.Context, portal *domain_entities_items.Portal, story *domain_entities_items.StoryDetail) []*domain_entities_items.Moment {
	hauntConfig := story.Summary.HauntPlayConfig
	if hauntConfig == nil {
		return nil
	}
	avatarIds := lo.Map(hauntConfig.AssitBoosWithQuestionsAndAnswers, func(item *domain_entities_items.HauntBoo, _ int) int64 {
		return item.AvatarId
	})
	avatarIds = append(avatarIds, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.AvatarId)
	moment := &domain_entities_items.Moment{
		PortalId: portal.Id,
		Creator: &domain_entities_users.UserSummaryEntity{
			ID: story.Summary.Author.ID,
		},
		Type:   api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_HAUNT,
		Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
		ExtraInfo: &domain_entities_items.MomentExtraInfo{
			AvatarIds: avatarIds,
		},
	}
	return []*domain_entities_items.Moment{moment}
}
func (p *PortalRepo) getMomentsByParsePinConfig(ctx context.Context, portal *domain_entities_items.Portal, story *domain_entities_items.StoryDetail) []*domain_entities_items.Moment {
	return []*domain_entities_items.Moment{
		{
			PortalId: portal.Id,
			Creator: &domain_entities_users.UserSummaryEntity{
				ID: story.Summary.Author.ID,
			},
			Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
			ExtraInfo: &domain_entities_items.MomentExtraInfo{
				Resource: story.Summary.PinPlayConfig.BackgroundImage,
			},
			Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
		},
	}
}

func (p *PortalRepo) getMomentsByParseBasePlayConfig(ctx context.Context, portal *domain_entities_items.Portal, story *domain_entities_items.StoryDetail) []*domain_entities_items.Moment {
	moments := []*domain_entities_items.Moment{}

	if story.Summary.BasePlayConfig == nil || len(story.Summary.BasePlayConfig.Nodes) == 0 {
		return moments
	}

	for _, node := range story.Summary.BasePlayConfig.Nodes {
		var resource *domain_entities_resource.Resource

		switch node.ResourceType {
		case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
			resource = domain_entities_resource.NewResourceBuilder().WithImageObjectKey(domain_entities_resource.ImageResourcePath(node.ResourceKey), 0, 0).Build()
		case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
			resource = domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(domain_entities_resource.VideoResourcePath(node.ResourceKey), node.CoverImageKey, 0, 0).Build()
		case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT:
			resource = domain_entities_resource.NewResourceBuilder().WithImageObjectKey(story.Summary.StoryCoverImage.Path, uint32(story.Summary.StoryCoverImage.Width), uint32(story.Summary.StoryCoverImage.Height)).Build()
		default:
			// 如果资源类型不支持，跳过这个节点
			continue
		}

		moment := &domain_entities_items.Moment{
			PortalId: portal.Id,
			Creator: &domain_entities_users.UserSummaryEntity{
				ID: story.Summary.Author.ID,
			},
			Status: api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
			ExtraInfo: &domain_entities_items.MomentExtraInfo{
				Resource:        resource,
				AttachmentTexts: node.AttachedTexts,
			},
			Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
		}
		moments = append(moments, moment)
	}

	return moments
}

func (p *PortalRepo) getMomentsByParseStoryCommonConfig(ctx context.Context, portal *domain_entities_items.Portal, story *domain_entities_items.StoryDetail) []*domain_entities_items.Moment {
	moments := []*domain_entities_items.Moment{}

	var commonConfig *domain_entities_items.CommonConfig

	switch story.Summary.PlayType {
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
		commonConfig = story.Summary.ExchangeImagePlayConfig.CommonConfig
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
		commonConfig = story.Summary.NowShotConfig.CommonConfig
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
		commonConfig = story.Summary.UnmutePlayConfig.CommonConfig
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
		commonConfig = story.Summary.TurtleSoupPlayConfig.CommonConfig
	}

	if commonConfig == nil {
		return moments
	}

	var firstMoment *domain_entities_items.Moment
	var secondMoment *domain_entities_items.Moment
	if commonConfig.Cover != nil {
		var extraInfo *domain_entities_items.MomentExtraInfo
		var attachmentTexts []*domain_entities_items.AttachedText
		if commonConfig.Condition.Hint != nil {
			attachmentTexts = append(attachmentTexts, commonConfig.Condition.Hint)
		}
		if commonConfig.Cover.CoverType == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE {
			extraInfo = &domain_entities_items.MomentExtraInfo{
				Resource:        domain_entities_resource.NewResourceBuilder().WithImageObjectKey(domain_entities_resource.ImageResourcePath(commonConfig.Cover.ResourceKey), 0, 0).Build(),
				AttachmentTexts: attachmentTexts,
			}
		}
		if commonConfig.Cover.CoverType == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO {
			extraInfo = &domain_entities_items.MomentExtraInfo{
				Resource:        domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(domain_entities_resource.VideoResourcePath(commonConfig.Cover.ResourceKey), domain_entities_resource.ImageResourcePath(commonConfig.Cover.ThumbnailKey), 0, 0).Build(),
				AttachmentTexts: attachmentTexts,
			}
		}
		if commonConfig.Cover.CoverType == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT {
			extraInfo = &domain_entities_items.MomentExtraInfo{
				Resource:        domain_entities_resource.NewResourceBuilder().WithImageObjectKey(story.Summary.StoryCoverImage.Path, uint32(story.Summary.StoryCoverImage.Width), uint32(story.Summary.StoryCoverImage.Height)).Build(),
				AttachmentTexts: attachmentTexts,
			}
		}
		firstMoment = &domain_entities_items.Moment{
			PortalId: portal.Id,
			Creator: &domain_entities_users.UserSummaryEntity{
				ID: story.Summary.Author.ID,
			},
			Status:    api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
			ExtraInfo: extraInfo,
			Type:      api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
		}
	}
	if commonConfig.Resource != nil {
		extraInfo := &domain_entities_items.MomentExtraInfo{
			AttachmentTexts: []*domain_entities_items.AttachedText{},
			Resource:        &domain_entities_resource.Resource{},
		}
		if commonConfig.ResourceCaptions != nil {
			extraInfo.AttachmentTexts = commonConfig.ResourceCaptions
		}
		if commonConfig.Resource.Type == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE {
			extraInfo.Resource = domain_entities_resource.NewResourceBuilder().WithImageObjectKey(domain_entities_resource.ImageResourcePath(commonConfig.Resource.Key), 0, 0).Build()
		}
		if commonConfig.Resource.Type == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO {
			extraInfo.Resource = domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(
				domain_entities_resource.VideoResourcePath(commonConfig.Resource.Key),
				domain_entities_resource.ImageResourcePath(commonConfig.Resource.CoverImageKey),
				uint32(commonConfig.Resource.CoverImageWidth),
				uint32(commonConfig.Resource.CoverImageHeight),
			).Build()
		}
		secondMoment = &domain_entities_items.Moment{
			PortalId: portal.Id,
			Creator: &domain_entities_users.UserSummaryEntity{
				ID: story.Summary.Author.ID,
			},
			Status:    api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED,
			ExtraInfo: extraInfo,
			Type:      api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
		}
		if firstMoment == nil && commonConfig.Condition.Hint != nil {
			// 使用 hint 拼接
			secondMoment.ExtraInfo.AttachmentTexts = append(secondMoment.ExtraInfo.AttachmentTexts, commonConfig.Condition.Hint)
		}
	}
	if firstMoment != nil {
		moments = append(moments, firstMoment)
	}
	if secondMoment != nil {
		moments = append(moments, secondMoment)
	}
	return moments
}
