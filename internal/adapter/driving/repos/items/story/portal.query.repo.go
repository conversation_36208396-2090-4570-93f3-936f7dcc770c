package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	api_items_portal_moment_types_v1 "boson/api/items/portal/moments/types/v1"
	api_items_portal_types_v1 "boson/api/items/portal/types/v1"
	driven_http_types "boson/internal/adapter/driven/http/types"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_users "boson/internal/domain/entities/users"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"github.com/jinzhu/now"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

// GetUserHasPostedStories implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) GetUserHasPostedStories(ctx context.Context, userId int64) (bool, error) {
	storyQ := dal.Use(p.db.MasterDB(ctx)).Story
	count, err := storyQ.WithContext(ctx).Where(
		storyQ.CreatorID.Eq(userId),
	).Count()
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (p *PortalRepo) ListUserCreatedActivePortals(ctx context.Context, userId int64, reqUserId int64, req *api_common_v1.ListRequest) ([]*domain_entities_items.Portal, *api_common_v1.ListResponse, error) {
	limit := cast.ToInt(req.PageSize)
	offset := cast.ToInt(req.PageToken)

	q := dal.Use(p.db.MasterDB(ctx)).Portal
	portalModels, err := q.WithContext(ctx).Where(
		q.UserID.Eq(reqUserId),
		q.Status.Eq(api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_NORMAL.String()),
		q.ExpiredAt.Gt(time.Now()),
	).Offset(
		offset,
	).Limit(
		limit + 1,
	).Order(
		q.ID.Desc(),
	).Find()
	if err != nil {
		return nil, nil, errors.Wrapf(err, "reqUserId: %d", reqUserId)
	}

	portalIds := lo.Map(portalModels, func(portal *model.Portal, _ int) int64 { return portal.ID })
	if len(portalIds) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore: false,
		}, nil
	}

	hasMore := len(portalModels) > limit
	if hasMore {
		portalIds = portalIds[:len(portalIds)-1]
	}

	portalEntities, err := p.BatchGetPortals(ctx, userId, portalIds)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "reqUserId: %d", reqUserId)
	}

	result := make([]*domain_entities_items.Portal, 0, len(portalEntities))
	for _, portal := range portalEntities {
		result = append(result, portal)
	}
	return result, &api_common_v1.ListResponse{
		HasMore: hasMore,
	}, nil

}

func (p *PortalRepo) ListUserCreatedPortalsWithTimeRange(ctx context.Context, userId int64, reqUserId int64, req *api_common_v1.ListRequest) (map[time.Time][]*domain_entities_items.Portal, *api_common_v1.ListResponse, error) {
	userTimezone := driven_http_types.GetUserAccpetTimeZoneFromCtx(ctx)
	// 用户时区今天的0点
	nowTime := time.Now().In(userTimezone)
	if req.PageToken != "" {
		unix := cast.ToInt64(req.PageToken)
		nowTime = time.Unix(unix, 0).In(userTimezone)
	}
	beginTime := now.With(nowTime).EndOfDay()
	// 取最近8天
	endTime := beginTime.AddDate(0, 0, -8)
	sevenEndTime := beginTime.AddDate(0, 0, -7)

	portalsQ := dal.Use(p.db.MasterDB(ctx)).Portal
	portals, err := portalsQ.WithContext(ctx).Where(
		portalsQ.Status.Eq(api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_NORMAL.String()),
		portalsQ.UserID.Eq(reqUserId),
		portalsQ.CreatedAt.Lt(beginTime),
		portalsQ.CreatedAt.Gte(endTime),
	).Order(
		portalsQ.ID.Desc(),
	).Find()
	if err != nil {
		return nil, nil, errors.Wrapf(err, "reqUserId: %d", reqUserId)
	}

	// 实际上我们只要最近7天的
	// 如果有第八天的，视为 hasMore
	hasMore := false
	portalIds := lo.Map(portals, func(portal *model.Portal, _ int) int64 {
		if portal.CreatedAt.Before(sevenEndTime) {
			hasMore = true
			return 0
		}
		return portal.ID
	})
	portalIds = lo.Filter(portalIds, func(id int64, _ int) bool {
		return id != 0
	})

	portalEntities, err := p.BatchGetPortals(ctx, userId, portalIds)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "reqUserId: %d", reqUserId)
	}

	resultAfterSort := make(map[time.Time][]*domain_entities_items.Portal)
	for _, portal := range portalEntities {
		// 按照用户时区归一化
		portalDate := portal.Story.Summary.CreatedAt.In(userTimezone)
		date := now.With(portalDate).BeginningOfDay()
		resultAfterSort[date] = append(resultAfterSort[date], portal)
	}

	result := make(map[time.Time][]*domain_entities_items.Portal)
	for date, portals := range resultAfterSort {
		// 按照 id 倒叙一次
		slices.SortFunc(portals, func(a, b *domain_entities_items.Portal) int {
			return int(b.Id - a.Id)
		})
		result[date] = portals
	}
	return result, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: fmt.Sprintf("%d", beginTime.Unix()),
	}, nil
}

func (p *PortalRepo) ListMyPortals(ctx context.Context, userId int64, listRequest *api_common_v1.ListRequest) ([]*domain_entities_items.Portal, *api_common_v1.ListResponse, error) {
	ids, res, err := p.joinRelationRepo.ListJoinedPortals(ctx, userId, listRequest)
	if err != nil {
		return nil, nil, err
	}
	portals, err := p.BatchGetPortals(ctx, userId, ids)
	if err != nil {
		return nil, nil, err
	}
	var result []*domain_entities_items.Portal
	for _, id := range ids {
		if portal, ok := portals[id]; ok {
			result = append(result, portal)
		}
	}
	return result, res, nil
}

func (p *PortalRepo) BatchGetMoments(ctx context.Context, userId int64, momentIds []int64) (map[int64]*domain_entities_items.Moment, error) {
	momentsQ := dal.Use(p.db.MasterDB(ctx)).PortalMoment
	moments, err := momentsQ.WithContext(ctx).Where(
		momentsQ.ID.In(momentIds...),
	).Preload(
		momentsQ.Stat,
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "momentIds: %v", momentIds)
	}
	momentIdMap, err := p.buildMoments(ctx, userId, moments...)
	if err != nil {
		return nil, err
	}
	if err := p.injectLatestMomentViewer(ctx, userId, lo.Values(momentIdMap)...); err != nil {
		return nil, err
	}
	if err := p.injectMomentUsers(ctx, userId, lo.Values(momentIdMap)...); err != nil {
		return nil, err
	}
	if err := p.injectMomentRelations(ctx, userId, lo.Values(momentIdMap)...); err != nil {
		return nil, err
	}
	if err := p.injectMomentAvatars(ctx, userId, lo.Values(momentIdMap)...); err != nil {
		return nil, err
	}
	return momentIdMap, nil
}

func (p *PortalRepo) BatchGetPortalsWithStory(ctx context.Context, userId int64, storyIds ...int64) (map[int64]*domain_entities_items.Portal, error) {
	q := dal.Use(p.db.MasterDB(ctx)).Portal
	portals, err := q.WithContext(ctx).Where(q.StoryID.In(storyIds...)).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "storyIds: %v", storyIds)
	}
	pids := lo.Map(portals, func(portal *model.Portal, _ int) int64 {
		return portal.ID
	})
	portalIdMap, err := p.BatchGetPortals(ctx, userId, pids)
	if err != nil {
		return nil, err
	}
	result := make(map[int64]*domain_entities_items.Portal)
	for _, p := range portalIdMap {
		result[p.Story.Summary.Id] = p
	}
	return result, nil
}

// BatchGetPortalsWithStoryIds implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) BatchGetPortalsWithStoryIds(ctx context.Context, userId int64, storyIds []int64) (map[int64]*domain_entities_items.Portal, error) {
	q := dal.Use(p.db.MasterDB(ctx)).Portal
	portals, err := q.WithContext(ctx).Where(q.StoryID.In(storyIds...)).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "storyIds: %v", storyIds)
	}
	portalIds := lo.Map(portals, func(portal *model.Portal, _ int) int64 {
		return portal.ID
	})
	portalEntities, err := p.BatchGetPortals(ctx, userId, portalIds)
	if err != nil {
		return nil, err
	}
	result := make(map[int64]*domain_entities_items.Portal)
	for _, portal := range portalEntities {
		result[portal.Story.Summary.Id] = portal
	}
	return result, nil
}

// BatchGetPortals implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) BatchGetPortals(ctx context.Context, userId int64, portalIds []int64) (map[int64]*domain_entities_items.Portal, error) {
	q := dal.Use(p.db.MasterDB(ctx)).Portal
	portals, err := q.WithContext(ctx).Where(q.ID.In(portalIds...)).Find()
	if err != nil {
		return nil, err
	}
	portalEntities, err := p.buildPortal(ctx, userId, portals...)
	if err != nil {
		return nil, err
	}
	if err := p.injectPortalStories(ctx, userId, portalEntities); err != nil {
		return nil, err
	}
	if err := p.injectPortalMoments(ctx, userId, portalEntities); err != nil {
		return nil, err
	}
	if err := p.injectPortalReadRelations(ctx, userId, portalEntities); err != nil {
		return nil, err
	}

	result := make(map[int64]*domain_entities_items.Portal)
	for _, portal := range portalEntities {
		result[portal.Id] = portal
	}
	return result, nil
}

// ListCouldAppendMomentPortals implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) ListCouldAppendMomentPortals(ctx context.Context, userId int64) ([]*domain_entities_items.Portal, error) {
	q := dal.Use(p.db.MasterDB(ctx)).Portal
	portals, err := q.WithContext(ctx).Where(
		q.UserID.Eq(userId),
		// 创建时间小于 72hr 的
		q.CreatedAt.Gte(time.Now().Add(-72*time.Hour)),
		q.Status.Eq(api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_NORMAL.String()),
	).Order(
		q.ID.Desc(),
	).Find()
	if err != nil {
		return nil, err
	}
	ids := lo.Map(portals, func(portal *model.Portal, _ int) int64 {
		return portal.ID
	})
	portalEntities, err := p.BatchGetPortals(ctx, userId, ids)
	if err != nil {
		return nil, err
	}
	result := make([]*domain_entities_items.Portal, 0, len(portals))
	for _, id := range ids {
		if portal, ok := portalEntities[id]; ok {
			result = append(result, portal)
		}
	}
	return result, nil
}

// ListCreatedPortals implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) ListCreatedPortals(ctx context.Context, userId int64, expiredAfrer time.Time) ([]*domain_entities_items.Portal, error) {
	q := dal.Use(p.db.MasterDB(ctx)).Portal
	portals, err := q.WithContext(ctx).Where(
		q.UserID.Eq(userId),
		q.ExpiredAt.Gt(expiredAfrer),
		q.Status.Eq(api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_NORMAL.String()),
	).Order(
		q.ID.Desc(),
	).Find()
	if err != nil {
		return nil, err
	}
	ids := lo.Map(portals, func(portal *model.Portal, _ int) int64 {
		return portal.ID
	})
	portalEntities, err := p.BatchGetPortals(ctx, userId, ids)
	if err != nil {
		return nil, err
	}
	result := make([]*domain_entities_items.Portal, 0, len(portals))
	for _, id := range ids {
		if portal, ok := portalEntities[id]; ok {
			result = append(result, portal)
		}
	}
	return result, nil
}

func (p *PortalRepo) buildMoments(ctx context.Context, userId int64, moments ...*model.PortalMoment) (map[int64]*domain_entities_items.Moment, error) {
	momentIdMap := make(map[int64]*domain_entities_items.Moment)
	for _, moment := range moments {
		entity := &domain_entities_items.Moment{
			Id:       moment.ID,
			PortalId: moment.PortalID,
			Creator: &domain_entities_users.UserSummaryEntity{
				ID: moment.CreatorID,
			},
			Type:       api_items_portal_moment_types_v1.MomentType(api_items_portal_moment_types_v1.MomentType_value[moment.Type]),
			CreatedAt:  moment.CreatedAt,
			CreateType: api_items_portal_moment_types_v1.MomentCreateType(api_items_portal_moment_types_v1.MomentCreateType_value[moment.CreateType]),
			Status:     api_items_portal_moment_types_v1.Status(api_items_portal_moment_types_v1.Status_value[moment.Status]),
			Relations:  []*domain_entities_items.MomentRelation{},
			Stat: &domain_entities_items.MomentStat{
				LikeCount: uint32(moment.Stat.LikeCount),
			},
		}
		if moment.ExtraInfo != "" {
			if err := json.Unmarshal([]byte(moment.ExtraInfo), &entity.ExtraInfo); err != nil {
				return nil, errors.Wrapf(err, "moment.ExtraInfo: %s", moment.ExtraInfo)
			}
		}
		momentIdMap[moment.ID] = entity
	}
	return momentIdMap, nil
}

func (p *PortalRepo) buildPortal(ctx context.Context, userId int64, portals ...*model.Portal) ([]*domain_entities_items.Portal, error) {
	momentsQ := dal.Use(p.db.MasterDB(ctx)).PortalMoment
	moments, err := momentsQ.WithContext(ctx).Where(
		momentsQ.Status.Eq(api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED.String()),
		momentsQ.PortalID.In(lo.Map(portals, func(portal *model.Portal, _ int) int64 {
			return portal.ID
		})...),
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "portals: %v", portals)
	}
	momentPortalIdMap := make(map[int64][]int64)
	for _, moment := range moments {
		momentPortalIdMap[moment.PortalID] = append(momentPortalIdMap[moment.PortalID], moment.ID)
	}

	var result []*domain_entities_items.Portal
	for _, portal := range portals {
		entity := &domain_entities_items.Portal{
			Id: portal.ID,
			Story: &domain_entities_items.StoryDetail{
				Summary: &domain_entities_items.StorySummary{
					Id: portal.StoryID,
				},
			},
			Moments: lo.Map(momentPortalIdMap[portal.ID], func(momentId int64, _ int) *domain_entities_items.Moment {
				return &domain_entities_items.Moment{
					Id: momentId,
				}
			}),
			Stat: &domain_entities_items.PortalStat{
				MomentCount:       uint32(len(momentPortalIdMap[portal.ID])),
				UnreadMomentCount: 0,
			},
			ExpiredAt: portal.ExpiredAt,
		}
		result = append(result, entity)
	}
	return result, nil
}

func (p *PortalRepo) injectMomentRelations(ctx context.Context, userId int64, moments ...*domain_entities_items.Moment) error {
	relationsQ := dal.Use(p.db.MasterDB(ctx)).PortalMomentRelation
	relations, err := relationsQ.WithContext(ctx).Where(
		relationsQ.UserID.Eq(userId),
		relationsQ.MomentID.In(lo.Map(moments, func(moment *domain_entities_items.Moment, _ int) int64 {
			return moment.Id
		})...),
	).Find()
	if err != nil {
		return errors.Wrapf(err, "moments: %v", moments)
	}
	relationIdMap := make(map[int64][]*model.PortalMomentRelation)
	for _, relation := range relations {
		relationIdMap[relation.MomentID] = append(relationIdMap[relation.MomentID], relation)
	}
	for _, moment := range moments {
		relations, ok := relationIdMap[moment.Id]
		if !ok {
			continue
		}
		moment.Relations = lo.Map(relations, func(relation *model.PortalMomentRelation, _ int) *domain_entities_items.MomentRelation {
			return &domain_entities_items.MomentRelation{
				UserId:       fmt.Sprint(relation.UserID),
				RelationType: api_items_portal_moment_types_v1.RelationType(api_items_portal_moment_types_v1.RelationType_value[relation.RelationType]),
			}
		})
	}
	return nil
}

func (p *PortalRepo) injectMomentAvatars(ctx context.Context, userId int64, moments ...*domain_entities_items.Moment) error {
	var avatarIds []int64
	for _, moment := range moments {
		if moment.ExtraInfo == nil {
			continue
		}
		avatarIds = append(avatarIds, moment.ExtraInfo.AvatarIds...)
	}
	avatarIds = lo.Uniq(avatarIds)
	if len(avatarIds) == 0 {
		return nil
	}
	avatars, err := p.avatarQueryRepo.BatchGetAvatarWithIds(ctx, userId, avatarIds)
	if err != nil {
		return err
	}
	for _, moment := range moments {
		if moment.ExtraInfo == nil {
			continue
		}
		for _, avatarId := range moment.ExtraInfo.AvatarIds {
			if avatar, ok := avatars[avatarId]; ok {
				moment.ExtraInfo.Avatars = append(moment.ExtraInfo.Avatars, avatar)
			}
		}
	}
	return nil
}
func (p *PortalRepo) injectMomentUsers(ctx context.Context, userId int64, moments ...*domain_entities_items.Moment) error {
	creatorIds := lo.Map(moments, func(moment *domain_entities_items.Moment, _ int) int64 {
		return moment.Creator.ID
	})
	viewerIds := lo.Map(moments, func(moment *domain_entities_items.Moment, _ int) int64 {
		return moment.LatestViewer.User.ID
	})
	allIds := lo.Uniq(append(creatorIds, viewerIds...))
	users, err := p.userQueryRepo.BatchGetUserInfo(ctx, userId, allIds...)
	if err != nil {
		return err
	}
	for _, moment := range moments {
		if user, ok := users[moment.Creator.ID]; ok {
			moment.Creator = user
		}
		if user, ok := users[moment.LatestViewer.User.ID]; ok {
			moment.LatestViewer.User = user
		}
	}
	return nil
}

func (p *PortalRepo) injectPortalMoments(ctx context.Context, userId int64, portals []*domain_entities_items.Portal) error {
	var momentIds []int64
	for _, portal := range portals {
		momentIds = append(momentIds, lo.Map(portal.Moments, func(moment *domain_entities_items.Moment, _ int) int64 {
			return moment.Id
		})...)
		portal.Stat.MomentCount = uint32(len(momentIds))
	}
	moments, err := p.BatchGetMoments(ctx, userId, momentIds)
	if err != nil {
		return err
	}

	for _, portal := range portals {
		var rebuildMoments []*domain_entities_items.Moment
		for _, moment := range portal.Moments {
			momentEntity, ok := moments[moment.Id]
			if !ok {
				continue
			}
			rebuildMoments = append(rebuildMoments, momentEntity)
		}
		portal.Moments = rebuildMoments
	}
	return nil
}

func (p *PortalRepo) injectPortalStories(ctx context.Context, userId int64, portals []*domain_entities_items.Portal) error {
	storyIds := lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) int64 {
		return portal.Story.Summary.Id
	})
	stories, err := p.storyQueryRepo.BatchGetStoryDetails(ctx, userId, storyIds)
	if err != nil {
		return err
	}
	for _, portal := range portals {
		story, ok := stories[portal.Story.Summary.Id]
		if !ok {
			continue
		}
		portal.Story = story
	}
	return nil
}
