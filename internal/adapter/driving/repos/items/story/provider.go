package adapter_driving_repos_items_story

import (
	domain_services_items_portal "boson/internal/domain/services/items/portal"
	domain_services_items_story "boson/internal/domain/services/items/story"
	domain_services_users_boo "boson/internal/domain/services/users/boo"

	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	NewStoryHauntBooShowInfoRepo,
	wire.Bind(new(domain_services_items_story.IHauntBooShowInfoRepository), new(*StoryHauntBooShowInfoRepo)),

	NewStoryCmdRepo,
	wire.Bind(new(domain_services_items_story.IStoryCmdRepository), new(*StoryCmdRepo)),

	NewStoryTemplateRepo,
	wire.Bind(new(domain_services_items_story.ITemplateQueryRepository), new(*StoryTemplateRepo)),

	NewStoryQueryRepo,
	wire.Bind(new(domain_services_items_story.IStoryQueryRepository), new(*StoryQueryRepo)),
	wire.Bind(new(domain_services_items_story.IBooRepository), new(*StoryQueryRepo)),

	NewStoryContextRepo,
	wire.Bind(new(domain_services_items_story.IStoryPlayRecordRepo), new(*StoryContextRepo)),

	NewStoryTopRepo,

	NewStoryReactionRepo,
	NewStoryStatRepo,
	wire.Bind(new(domain_services_items_story.IStoryReactionRepo), new(*StoryReactionRepo)),

	NewHideStickersRepo,
	wire.Bind(new(domain_services_items_story.IHideStickerRepository), new(*HideStickersRepo)),
	wire.Bind(new(domain_services_users_boo.IStickerRepo), new(*HideStickersRepo)),

	NewPortalCmdRepo,
	wire.Bind(new(domain_services_items_portal.IPortalRepository), new(*PortalRepo)),

	NewUserPortalRelationRepo,
)
