package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_items_story "boson/internal/domain/services/items/story"
	domain_services_users_boo "boson/internal/domain/services/users/boo"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var _ domain_services_items_story.IHideStickerRepository = (*HideStickersRepo)(nil)
var _ domain_services_users_boo.IStickerRepo = (*HideStickersRepo)(nil)

type HideStickersRepo struct {
	db *data.Data
}

// UnCollectSticker implements domain_services_items_story.IHideStickerRepository.
func (h *HideStickersRepo) UnCollectSticker(ctx context.Context, userId int64, stickerIds ...int64) error {
	cmd := dal.Use(h.db.MasterDB(ctx)).UserCollectedHideSticker
	if _, err := cmd.WithContext(ctx).Where(
		cmd.UserID.Eq(userId),
		cmd.StickerID.In(stickerIds...),
	).Delete(); err != nil {
		return errors.Wrapf(err, "stickerIds: %v", stickerIds)
	}
	return nil
}

// CollectSticker implements domain_services_items_story.IHideStickerRepository.
func (h *HideStickersRepo) CollectSticker(ctx context.Context, userId int64, stickerIds ...int64) error {
	if len(stickerIds) == 0 {
		return nil
	}
	models := lo.Map(stickerIds, func(item int64, _ int) *model.UserCollectedHideSticker {
		return &model.UserCollectedHideSticker{
			UserID:    userId,
			StickerID: item,
			IsTop:     false,
		}
	})
	cmd := dal.Use(h.db.MasterDB(ctx)).UserCollectedHideSticker
	if err := cmd.WithContext(ctx).Save(models...); err != nil {
		return errors.Wrapf(err, "stickerIds: %v", stickerIds)
	}
	return nil
}

func (h *HideStickersRepo) BatchGetStickers(ctx context.Context, userId int64, stickerIds ...int64) (map[int64]*domain_entities_items.HideSticker, error) {
	q := dal.Use(h.db.MasterDB(ctx)).HideSticker
	stickersModels, err := q.WithContext(ctx).Where(
		q.ID.In(stickerIds...),
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "stickerIds: %v", stickerIds)
	}
	collectedRelationQ := dal.Use(h.db.MasterDB(ctx)).UserCollectedHideSticker
	collectedRelationModels, err := collectedRelationQ.WithContext(ctx).Where(
		collectedRelationQ.UserID.Eq(userId),
		collectedRelationQ.StickerID.In(stickerIds...),
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "stickerIds: %v", stickerIds)
	}

	result := make(map[int64]*domain_entities_items.HideSticker)
	for _, m := range stickersModels {
		entity := &domain_entities_items.HideSticker{
			Id:           fmt.Sprintf("%d", m.ID),
			Collected:    false,
			FromStoryId:  m.FromStoryID,
			FromAvatarId: m.FromAvatarID,
		}
		if err := json.Unmarshal([]byte(m.ExtraInfo), entity); err != nil {
			return nil, errors.Wrapf(err, "stickerIds: %v", stickerIds)
		}
		for _, collectedRelationModel := range collectedRelationModels {
			if collectedRelationModel.StickerID == m.ID {
				entity.Collected = true
				break
			}
		}
		result[m.ID] = entity
	}
	return result, nil
}

// CreateSticker implements domain_services_items_story.IHideStickerRepository.
func (h *HideStickersRepo) CreateStickers(ctx context.Context, userId int64, stickers ...*domain_entities_items.HideSticker) error {
	cmd := dal.Use(h.db.MasterDB(ctx)).HideSticker
	var ms []*model.HideSticker
	for _, sticker := range stickers {
		jsonInfo, err := json.Marshal(sticker)
		if err != nil {
			return errors.Wrapf(err, "sticker: %v", sticker)
		}
		ms = append(ms, &model.HideSticker{
			ID:           cast.ToInt64(sticker.Id),
			FromStoryID:  sticker.FromStoryId,
			FromAvatarID: sticker.FromAvatarId,
			CreatorID:    userId,
			ExtraInfo:    string(jsonInfo),
		})
	}
	if err := cmd.WithContext(ctx).Create(
		ms...,
	); err != nil {
		return errors.Wrapf(err, "stickers: %v", stickers)
	}
	return nil
}

// ListMyCollectedStickers implements domain_services_items_story.IHideStickerRepository.
func (h *HideStickersRepo) ListMyCollectedStickers(ctx context.Context, userId int64, listRequest *api_common_v1.ListRequest) (*api_common_v1.ListResponse, []*domain_services_items_story.CollectStickerEntity, error) {
	limit := cast.ToInt(listRequest.PageSize)
	offset := cast.ToInt(listRequest.PageToken)

	q := dal.Use(h.db.MasterDB(ctx)).UserCollectedHideSticker
	collectionRelationModels, err := q.WithContext(ctx).Where(
		q.UserID.Eq(userId),
	).Order(
		q.UpdatedAt.Desc(),
		q.ID.Desc(),
	).Limit(
		limit + 1, // 多查一个，用于判断是否还有更多
	).Offset(
		offset,
	).Find()
	if err != nil {
		return nil, nil, errors.Wrapf(err, "userId: %v", userId)
	}

	ids := lo.Map(collectionRelationModels, func(item *model.UserCollectedHideSticker, _ int) int64 {
		return item.StickerID
	})

	hasMore := len(ids) > limit
	if hasMore {
		ids = ids[:limit]
	}

	stickers, err := h.BatchGetStickers(ctx, userId, ids...)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "userId: %v", userId)
	}

	var result []*domain_services_items_story.CollectStickerEntity
	for _, id := range ids {
		stickerEntity, ok := stickers[id]
		if !ok {
			continue
		}
		collectedEntity := &domain_services_items_story.CollectStickerEntity{Sticker: stickerEntity}
		for _, relationModel := range collectionRelationModels {
			if relationModel.StickerID == id {
				collectedEntity.CollectedAt = relationModel.CreatedAt
				collectedEntity.IsTop = relationModel.IsTop
				break
			}
		}
		result = append(result, collectedEntity)
	}

	return &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: cast.ToString(len(ids) + offset),
	}, result, nil
}

// TopSticker implements domain_services_items_story.IHideStickerRepository.
func (h *HideStickersRepo) TopSticker(ctx context.Context, userId int64, stickerIds ...int64) error {
	cmd := dal.Use(h.db.MasterDB(ctx)).UserCollectedHideSticker
	if _, err := cmd.WithContext(ctx).Where(
		cmd.UserID.Eq(userId),
		cmd.StickerID.In(stickerIds...),
	).Update(cmd.UpdatedAt, time.Now()); err != nil {
		return errors.Wrapf(err, "stickerIds: %v", stickerIds)
	}
	return nil
}

func (h *HideStickersRepo) BatchetGetWithAvatarIds(ctx context.Context, userId int64, avatarIds ...int64) (map[int64]*domain_entities_items.HideSticker, error) {
	q := dal.Use(h.db.MasterDB(ctx)).HideSticker
	stickers, err := q.WithContext(ctx).Where(
		q.FromAvatarID.In(avatarIds...),
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "avatarIds: %v", avatarIds)
	}
	stickerIds := lo.Map(stickers, func(item *model.HideSticker, _ int) int64 {
		return item.ID
	})
	stickerEntities, err := h.BatchGetStickers(ctx, userId, stickerIds...)
	if err != nil {
		return nil, errors.Wrapf(err, "avatarIds: %v", avatarIds)
	}
	result := make(map[int64]*domain_entities_items.HideSticker)
	for _, sticker := range stickerEntities {
		result[sticker.FromAvatarId] = sticker
	}
	return result, nil
}

// UnTopSticker implements domain_services_items_story.IHideStickerRepository.
func (h *HideStickersRepo) UnTopSticker(ctx context.Context, userId int64, stickerIds ...int64) error {
	cmd := dal.Use(h.db.MasterDB(ctx)).UserCollectedHideSticker
	if _, err := cmd.WithContext(ctx).Where(
		cmd.UserID.Eq(userId),
		cmd.StickerID.In(stickerIds...),
	).Update(cmd.IsTop, false); err != nil {
		return errors.Wrapf(err, "stickerIds: %v", stickerIds)
	}
	return nil
}

func NewHideStickersRepo(db *data.Data) *HideStickersRepo {
	return &HideStickersRepo{db: db}
}
