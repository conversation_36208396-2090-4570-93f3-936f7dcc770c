package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	api_items_portal_types_v1 "boson/api/items/portal/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_users "boson/internal/domain/entities/users"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

var momentLatestViewerKey = "moment_latest_viewer:moment:%d"

func (p *PortalRepo) ListMomentViewers(ctx context.Context, momentId int64, listRequest *api_common_v1.ListRequest) ([]int64, *api_common_v1.ListResponse, error) {
	// 这里需要排除作者自己，所以多查一次
	momentQ := dal.Use(p.db.MasterDB(ctx)).PortalMoment
	moment, err := momentQ.WithContext(ctx).Where(
		momentQ.ID.Eq(momentId),
	).First()
	if err != nil {
		return nil, nil, err
	}
	authorId := moment.CreatorID

	offset := cast.ToInt(listRequest.PageToken)
	limit := cast.ToInt(listRequest.PageSize)
	q := dal.Use(p.db.MasterDB(ctx)).PortalMomentUserReadRelation
	reads, err := q.WithContext(ctx).Where(
		q.ReadMomentID.Eq(momentId),
		q.UserID.Neq(authorId),
	).
		Order(
			q.ID.Desc(),
		).
		Limit(limit + 1).
		Offset(offset).
		Find()
	hasMore := len(reads) > limit
	if hasMore {
		reads = reads[:limit]
	}
	if err != nil {
		return nil, nil, err
	}
	return lo.Map(reads, func(read *model.PortalMomentUserReadRelation, _ int) int64 {
			return read.UserID
		}), &api_common_v1.ListResponse{
			HasMore:       hasMore,
			NextPageToken: fmt.Sprintf("%d", offset+len(reads)),
		}, nil
}

// GetUserLastReadPortalId implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) GetUserLastReadPortalId(ctx context.Context, userId int64) (*int64, error) {
	readQ := dal.Use(p.db.MasterDB(ctx)).PortalReadRelation
	read, err := readQ.WithContext(ctx).Where(
		readQ.UserID.Eq(userId),
	).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Wrapf(err, "userId: %d", userId)
	}
	if read == nil {
		return nil, nil
	}
	r := read.PortalID
	return &r, nil
}

// ReportPortalMomentRead implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) ReportPortalMomentRead(ctx context.Context, userId int64, moment *domain_entities_items.Moment, portal *domain_entities_items.Portal) error {
	return p.db.ExecTx(ctx, func(ctx context.Context) error {
		// 使用原生 SQL 执行 INSERT ... ON DUPLICATE KEY UPDATE
		// 这里处理两个唯一键冲突的情况
		sql := `INSERT INTO portal_moment_user_read_relations (portal_id, user_id, read_moment_id, created_at, updated_at)
			VALUES (?, ?, ?, NOW(), NOW())
			ON DUPLICATE KEY UPDATE 
				read_moment_id = VALUES(read_moment_id),
				updated_at = NOW()`

		if err := p.db.MasterDB(ctx).Exec(sql, portal.Id, userId, moment.Id).Error; err != nil {
			return errors.Wrapf(err, "failed to report moment read, userId: %d, momentId: %d, portalId: %d", userId, moment.Id, portal.Id)
		}

		// 记录 redis 方便后续使用
		// 如果是 moment 作者，则不记录该值
		latestViewerKey := fmt.Sprintf(momentLatestViewerKey, moment.Id)
		if err := p.db.Rdb().Set(ctx, latestViewerKey, userId, time.Hour*24*7).Err(); err != nil {
			return errors.Wrapf(err, "failed to set latest viewer, momentId: %d, userId: %d", moment.Id, userId)
		}

		// 只上报自己的 portal
		if portal.Story.Summary.Author.ID == userId {
			return p.reportPortalRead(ctx, userId, portal.Id)
		}
		return nil
	})
}

func (p *PortalRepo) deletePortalAndReadRecords(ctx context.Context, userId int64, storyId int64) error {
	// 先找到对应的 portal
	portals, err := p.BatchGetPortalsWithStoryIds(ctx, userId, []int64{storyId})
	if err != nil {
		return err
	}
	portalId := portals[storyId].Id

	return p.db.ExecTx(ctx, func(ctx context.Context) error {
		cmd := dal.Use(p.db.MasterDB(ctx)).Portal
		if _, err := cmd.WithContext(ctx).Where(
			cmd.StoryID.Eq(storyId),
		).UpdateColumnSimple(
			cmd.Status.Value(api_items_portal_types_v1.PortalStatus_PORTAL_STATUS_DELETED.String()),
		); err != nil {
			return errors.Wrapf(err, "storyId: %d", storyId)
		}
		if err := p.deletePortalMomentsReadRecord(ctx, userId, portalId); err != nil {

		}
		if err := p.deletePortalReadRecords(ctx, userId, portalId); err != nil {
			return err
		}
		return nil
	})

}

func (p *PortalRepo) deletePortalMomentsReadRecord(ctx context.Context, userId int64, portalId int64) error {
	cmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentUserReadRelation
	if _, err := cmd.WithContext(ctx).Where(
		cmd.PortalID.Eq(portalId),
	).Delete(); err != nil {
		return errors.Wrapf(err, "portalId: %d", portalId)
	}
	return nil
}
func (p *PortalRepo) deletePortalReadRecords(ctx context.Context, userId int64, portalId int64) error {
	cmd := dal.Use(p.db.MasterDB(ctx)).PortalMomentUserReadRelation
	if _, err := cmd.WithContext(ctx).Where(
		cmd.PortalID.Eq(portalId),
	).Delete(); err != nil {
		return errors.Wrapf(err, "portalId: %d", portalId)
	}
	return nil
}

// ReportPortalRead implements domain_services_items_portal.IPortalRepository.
func (p *PortalRepo) reportPortalRead(ctx context.Context, userId int64, portalId int64) error {
	// 使用原生 SQL 执行 INSERT ... ON DUPLICATE KEY UPDATE
	sql := `INSERT INTO portal_read_relations (portal_id, user_id, created_at, updated_at)
		VALUES (?, ?, NOW(), NOW())
		ON DUPLICATE KEY UPDATE portal_id = VALUES(portal_id), updated_at = NOW()`

	if err := p.db.MasterDB(ctx).Exec(sql, portalId, userId).Error; err != nil {
		return errors.Wrapf(err, "failed to report portal read, userId: %d, portalId: %d", userId, portalId)
	}

	return nil
}

func (p *PortalRepo) injectLatestMomentViewer(ctx context.Context, userId int64, moments ...*domain_entities_items.Moment) error {
	if len(moments) == 0 {
		return nil
	}
	// 获取所有 moment IDs
	momentIds := lo.Map(moments, func(moment *domain_entities_items.Moment, _ int) int64 {
		return moment.Id
	})

	latestViewerKeys := lo.Map(momentIds, func(momentId int64, _ int) string {
		return fmt.Sprintf(momentLatestViewerKey, momentId)
	})
	
	// 如果 latestViewerKeys 为空，直接返回，避免 MGet 命令参数错误
	if len(latestViewerKeys) == 0 {
		return nil
	}
	
	latestViewerValues, err := p.db.Rdb().MGet(ctx, latestViewerKeys...).Result()
	if err != nil {
		return errors.Wrapf(err, "latestViewerKeys: %v", latestViewerKeys)
	}

	momentIdsUserIdMap := make(map[int64]int64)

	for idx, momentId := range momentIds {
		latestViewerValue := latestViewerValues[idx]
		userId := cast.ToInt64(latestViewerValue)
		momentIdsUserIdMap[momentId] = userId
	}

	viewIds := lo.Values(momentIdsUserIdMap)
	momentUserRelations, err := p.BatchGetMomentsAndUsersRelations(ctx, momentIds, viewIds)
	if err != nil {
		return err
	}

	for _, moment := range moments {
		userId, ok := momentIdsUserIdMap[moment.Id]
		if !ok {
			continue
		}
		userRelations := momentUserRelations[moment.Id]
		moment.LatestViewer = &domain_entities_items.MomentViewer{
			User: &domain_entities_users.UserSummaryEntity{
				ID: userId,
			},
			Relations: userRelations[userId],
		}
	}
	return nil
}
func (p *PortalRepo) injectPortalReadRelations(ctx context.Context, userId int64, portals []*domain_entities_items.Portal) error {
	readQ := dal.Use(p.db.MasterDB(ctx)).PortalMomentUserReadRelation
	reads, err := readQ.WithContext(ctx).Where(
		readQ.UserID.Eq(userId),
		readQ.PortalID.In(lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) int64 {
			return portal.Id
		})...),
	).Find()
	if err != nil {
		return errors.Wrapf(err, "portals: %v", portals)
	}
	readIdMap := make(map[int64]int64)
	maxIds := make(map[int64]int64)
	for _, read := range reads {
		maxId, ok := maxIds[read.PortalID]
		if read.ID > maxId || !ok {
			readIdMap[read.PortalID] = read.ReadMomentID
			maxIds[read.PortalID] = read.ID
		}
	}
	for _, portal := range portals {
		portal.Stat.UnreadMomentCount = uint32(len(portal.Moments))
		portal.Stat.MomentCount = uint32(len(portal.Moments))
		read, ok := readIdMap[portal.Id]
		if !ok {
			continue
		}
		r := read
		portal.LastReadMomentId = &r
		unreadMoments := lo.Filter(portal.Moments, func(moment *domain_entities_items.Moment, _ int) bool {
			// 上次已读后发的 moment 都算未读
			return moment.Id > read
		})
		portal.Stat.UnreadMomentCount = uint32(len(unreadMoments))
	}
	return nil
}
