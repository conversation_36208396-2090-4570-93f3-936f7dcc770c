package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_items_story "boson/internal/domain/services/items/story"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"
	"strconv"
	"strings"

	"github.com/samber/lo"
	"github.com/spf13/cast"

	"github.com/pkg/errors"
)

type StoryContextRepo struct {
	db          *data.Data
	pPortalRepo *UserPortalRelationRepo
}

var _ domain_services_items_story.IStoryPlayRecordRepo = &StoryContextRepo{}

func NewStoryContextRepo(db *data.Data, pPortalRepo *UserPortalRelationRepo) *StoryContextRepo {
	return &StoryContextRepo{db: db, pPortalRepo: pPortalRepo}
}

type PlayContext interface {
	domain_entities_items.ExchangeImagePlayContext |
		domain_entities_items.StoryPlayTurtleSoupContext |
		domain_entities_items.StoryPlayUnmuteContext |
		domain_entities_items.StoryBasePlayContext |
		domain_entities_items.StoryPlayNowShotContext |
		domain_entities_items.StoryPlayRoastedContext |
		domain_entities_items.StoryPlayWassupContext |
		domain_entities_items.StoryPlayCapsuleContext |
		domain_entities_items.StoryPlayHideContext |
		domain_entities_items.StoryPlayWhoContext |
		domain_entities_items.StoryPlayChatProxyContext |
		domain_entities_items.HauntStoryContext |
		domain_entities_items.StoryPinContext
}

func (r *StoryContextRepo) BatchGetHauntPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.HauntStoryContext, error) {
	return batchGetContext[domain_entities_items.HauntStoryContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetWassupPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayWassupContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayWassupContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetCapsulePlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayCapsuleContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayCapsuleContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetRoastedPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayRoastedContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayRoastedContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetUnmutePlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayUnmuteContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayUnmuteContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetNowShotPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayNowShotContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayNowShotContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetTurtleSoupPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayTurtleSoupContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayTurtleSoupContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetBasePlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryBasePlayContext, error) {
	return batchGetContext[domain_entities_items.StoryBasePlayContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetImageExchangePlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.ExchangeImagePlayContext, error) {
	return batchGetContext[domain_entities_items.ExchangeImagePlayContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetHidePlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayHideContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayHideContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetChatProxyPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayChatProxyContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayChatProxyContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetWhoPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPlayWhoContext, error) {
	return batchGetContext[domain_entities_items.StoryPlayWhoContext](ctx, r.db, loginUserId, storyIds)
}
func (r *StoryContextRepo) BatchGetPinPlayRecords(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryPinContext, error) {
	return batchGetContext[domain_entities_items.StoryPinContext](ctx, r.db, loginUserId, storyIds)
}

func (r *StoryContextRepo) SavePinPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPinContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.IsUnlocked {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPinContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveWhoPlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
func (r *StoryContextRepo) SaveWhoPlayRecords(ctx context.Context, userId int64, playContexts map[int64]domain_entities_items.StoryPlayWhoContext) error {
	storyIdToContexts := make(map[int64]playContextWithStatus[domain_entities_items.StoryPlayWhoContext])
	for storyId, playContext := range playContexts {
		contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
		if playContext.IsUnlocked {
			contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
		}
		storyIdToContexts[storyId] = playContextWithStatus[domain_entities_items.StoryPlayWhoContext]{
			Status:      contextStatus,
			PlayContext: playContext,
		}
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToContexts)
}

func (r *StoryContextRepo) SaveHauntPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.HauntStoryContext) error {
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.HauntStoryContext]{
		storyId: {
			Status: lo.
				If(playContext.Unlocked, api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED).
				Else(api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL),
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveChatProxyPlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
func (r *StoryContextRepo) SaveChatProxyPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayChatProxyContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.Unlocked {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayChatProxyContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

func (r *StoryContextRepo) SaveBasePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryBasePlayContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.IsUnlocked {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryBasePlayContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveUnmutePlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveUnmutePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayUnmuteContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.IsFinished {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayUnmuteContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveHidePlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveHidePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayHideContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.IsConsumed {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayHideContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveNowShotPlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveNowShotPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayNowShotContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.ConsumeStatus == api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayNowShotContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveWassupPlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveWassupPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayWassupContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayWassupContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveCapsulePlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveCapsulePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayCapsuleContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayCapsuleContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveTurtleSoupPlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveTurtleSoupPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayTurtleSoupContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.IsFinished {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayTurtleSoupContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveExchangeImagePlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveExchangeImagePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.ExchangeImagePlayContext, isFinished bool) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if isFinished {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.ExchangeImagePlayContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

// SaveRoastedPlayRecord implements domain_services_items_story.IStoryPlayRecordRepo.
// WARNING 此函数并发不安全，请勿在并发场景下使用
func (r *StoryContextRepo) SaveRoastedPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayRoastedContext) error {
	contextStatus := api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL
	if playContext.IsConsumed {
		contextStatus = api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED
	}
	storyIdToPlayContext := map[int64]playContextWithStatus[domain_entities_items.StoryPlayRoastedContext]{
		storyId: {
			Status:      contextStatus,
			PlayContext: playContext,
		},
	}
	return savePlayContext(ctx, r.db, userId, r.pPortalRepo, storyIdToPlayContext)
}

func (r *StoryContextRepo) BatchListPlayedUserIds(
	ctx context.Context,
	storyIds []int64,
	limit uint32,
) (map[int64]*struct {
	UserIds []int64
	Total   uint32
}, error) {
	return r.BatchListUserIdsByConsumeStatus(ctx, storyIds, limit, api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL)
}

func (r *StoryContextRepo) BatchListUnlockedUserIds(
	ctx context.Context,
	storyIds []int64,
	limit uint32,
) (map[int64]*struct {
	UserIds []int64
	Total   uint32
}, error) {
	return r.BatchListUserIdsByConsumeStatus(ctx, storyIds, limit, api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED)
}

func (r *StoryContextRepo) BatchListUserIdsByConsumeStatus(
	ctx context.Context,
	storyIds []int64,
	limit uint32,
	consumeStatus api_items_story_types_v1.ContextStatus,
) (map[int64]*struct {
	UserIds []int64
	Total   uint32
}, error) {
	q := dal.Use(r.db.MasterDB(ctx)).StoriesUsersPlayRecord

	records, err := q.WithContext(ctx).Where(
		q.StoryID.In(storyIds...),
		q.Status.Eq(consumeStatus.String()),
	).Order(
		q.ID.Desc(),
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "batch list unlocked user ids, story ids: %v", storyIds)
	}

	result := make(map[int64]*struct {
		UserIds []int64
		Total   uint32
	})

	// 对于同一个 story，每个 User 只出现一次
	storyUserMap := make(map[int64]map[int64]struct{})
	for _, record := range records {
		if _, ok := storyUserMap[record.StoryID]; !ok {
			storyUserMap[record.StoryID] = make(map[int64]struct{})
		}

		// 检查这个用户是否处理过？
		_, ok := storyUserMap[record.StoryID][record.UserID]
		if ok {
			continue
		}
		storyUserMap[record.StoryID][record.UserID] = struct{}{}
		info, ok := result[record.StoryID]
		if !ok {
			info = &struct {
				UserIds []int64
				Total   uint32
			}{}
		}
		if len(info.UserIds) < int(limit) {
			info.UserIds = append(info.UserIds, record.UserID)
		}
		info.Total++
		result[record.StoryID] = info
	}

	return result, nil
}

func (r *StoryContextRepo) ListPlayedUserIds(
	ctx context.Context,
	storyId int64,
	req *api_common_v1.ListRequest,
) ([]int64, *api_common_v1.ListResponse, error) {
	return r.ListUserIdsByConsumeStatuses(ctx, storyId, req,
		[]api_items_story_types_v1.ContextStatus{
			api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL,
			api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED,
		})
}

func (r *StoryContextRepo) ListUnlockedUserIds(
	ctx context.Context,
	storyId int64,
	req *api_common_v1.ListRequest,
) ([]int64, *api_common_v1.ListResponse, error) {
	return r.ListUserIdsByConsumeStatuses(ctx, storyId, req, []api_items_story_types_v1.ContextStatus{api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED})
}

func (r *StoryContextRepo) ListUserIdsByConsumeStatuses(
	ctx context.Context,
	storyId int64,
	req *api_common_v1.ListRequest,
	consumeStatuses []api_items_story_types_v1.ContextStatus,
) ([]int64, *api_common_v1.ListResponse, error) {
	q := dal.Use(r.db.MasterDB(ctx)).StoriesUsersPlayRecord
	offset := 0
	if req.PageToken != "" {
		offset = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}
	consumeStatusesStr := lo.Map(consumeStatuses, func(item api_items_story_types_v1.ContextStatus, _ int) string {
		return item.String()
	})
	records, total, err := q.WithContext(ctx).Where(
		q.StoryID.Eq(storyId),
		q.Status.In(consumeStatusesStr...),
	).Order(
		q.ID.Desc(),
	).FindByPage(offset, limit)
	if err != nil {
		return nil, nil, errors.Wrapf(err, "list unlocked user ids, story id: %d, status: %+v", storyId, consumeStatusesStr)
	}

	ids := lo.Map(records, func(item *model.StoriesUsersPlayRecord, _ int) int64 { return item.UserID })
	if len(ids) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}

	hasMore := len(ids)+offset < int(total)

	return ids, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: strconv.Itoa(offset + len(ids)),
	}, nil
}

func (r *StoryContextRepo) BatchGetUnlockedStoryIds(ctx context.Context,
	req *api_common_v1.ListRequest,
	userId int64,
	filterVersion []domain_entities_items.StoryVersion,
	filterPlayTypes []api_items_story_types_v1.StoryPlayType,
) ([]int64, *api_common_v1.ListResponse, error) {
	q := r.db.MasterDB(ctx)
	offset := 0
	if req.PageToken != "" {
		offset = cast.ToInt(req.PageToken)
	}
	limit := 10
	if req.PageSize != "" && cast.ToInt(req.PageSize) > 0 {
		limit = cast.ToInt(req.PageSize)
	}
	var (
		conditions []any
		sqlBuilder strings.Builder
	)

	sqlBuilder.WriteString(`
SELECT 
    s.id
FROM 
    stories s
JOIN 
    stories_users_play_records r ON s.id = r.story_id
WHERE 
    r.user_id = ?
    AND r.status = ?`)
	conditions = append(conditions, userId, api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED.String())

	// 可选：play_type 条件
	if len(filterPlayTypes) > 0 {
		sqlBuilder.WriteString(` AND s.play_type IN (?)`)
		playTypes := lo.Map(filterPlayTypes, func(p api_items_story_types_v1.StoryPlayType, _ int) string {
			return p.String()
		})
		conditions = append(conditions, playTypes)
	}

	// 可选：version 条件
	if len(filterVersion) > 0 {
		sqlBuilder.WriteString(` AND s.version IN (?)`)
		versions := lo.Map(filterVersion, func(v domain_entities_items.StoryVersion, _ int) string {
			return strconv.Itoa(int(v))
		})
		conditions = append(conditions, versions)
	}

	sqlBuilder.WriteString(`
ORDER BY 
    r.id DESC
LIMIT ? OFFSET ?;`)
	conditions = append(conditions, limit+1, offset)

	var ids []int64
	if err := q.Raw(sqlBuilder.String(), conditions...).Scan(&ids).Error; err != nil {
		return nil, nil, errors.Wrapf(err, "list unlocked story ids, user id: %d", userId)
	}

	if len(ids) == 0 {
		return nil, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}

	hasMore := len(ids) > limit
	// 如果查询到的数据超过了limit，说明还有更多数据，去掉最后一条
	if hasMore {
		ids = ids[:limit]
	}
	return ids, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: strconv.Itoa(offset + len(ids)),
	}, nil
}

type playContextWithStatus[T PlayContext] struct {
	Status      api_items_story_types_v1.ContextStatus
	PlayContext T
}

func savePlayContext[T PlayContext](ctx context.Context, data *data.Data, loginUserId int64, pPortalRepo *UserPortalRelationRepo, storyIdToPlayContext map[int64]playContextWithStatus[T]) error {
	if len(storyIdToPlayContext) == 0 {
		return nil
	}

	// 使用事务来保证原子性
	err := data.ExecTx(ctx, func(ctx context.Context) error {
		// 准备批量插入的值
		var values []string
		var args []interface{}

		for storyId, contextWithStatus := range storyIdToPlayContext {
			bs, err := json.Marshal(&contextWithStatus.PlayContext)
			if err != nil {
				return errors.Wrapf(err, "save play record, user id: %d, story id: %d, play context: %v",
					loginUserId, storyId, contextWithStatus.PlayContext)
			}

			values = append(values, "(?, ?, ?, ?)")
			args = append(args, storyId, loginUserId, string(bs), contextWithStatus.Status.String())
		}

		// 构建批量插入SQL
		query := `
			INSERT INTO stories_users_play_records (story_id, user_id, context, status)
			VALUES ` + strings.Join(values, ",") + `
			ON DUPLICATE KEY UPDATE
				context = VALUES(context),
				status = VALUES(status)`

		if err := data.MasterDB(ctx).Exec(query, args...).Error; err != nil {
			return errors.Wrapf(err, "batch save play records, user id: %d", loginUserId)
		}

		// 如果是解锁成功，批量加入 portal
		var unlockStoryIds []int64
		for storyId, contextWithStatus := range storyIdToPlayContext {
			if contextWithStatus.Status == api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED {
				unlockStoryIds = append(unlockStoryIds, storyId)
			}
		}
		if len(unlockStoryIds) > 0 {
			if err := pPortalRepo.JoinPortals(ctx, loginUserId, unlockStoryIds...); err != nil {
				return errors.Wrapf(err, "join portals failed")
			}
		}
		return nil
	})

	return err
}
func batchGetContext[T PlayContext](ctx context.Context, data *data.Data, loginUserId int64, storyIds []int64) (map[int64]*T, error) {
	if len(storyIds) == 0 {
		return nil, nil
	}
	q := dal.Use(data.MasterDB(ctx)).StoriesUsersPlayRecord

	result, err := q.WithContext(ctx).Where(
		q.UserID.Eq(loginUserId),
		q.StoryID.In(storyIds...),
		q.Status.In(api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_NORMAL.String(), api_items_story_types_v1.ContextStatus_CONTEXT_STATUS_UNLOCKED.String()),
	).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "batch get play records, story ids: %v", storyIds)
	}

	playRecords := make(map[int64]*T)

	for _, record := range result {
		var playContext T
		if err := json.Unmarshal([]byte(record.Context), &playContext); err != nil {
			return nil, errors.Wrapf(err, "batch get play records, story ids: %v", storyIds)
		}
		playRecords[record.StoryID] = &playContext
	}

	return playRecords, nil
}
