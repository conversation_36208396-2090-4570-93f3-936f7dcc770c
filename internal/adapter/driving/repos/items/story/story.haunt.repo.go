package adapter_driving_repos_items_story

import (
	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_items_story "boson/internal/domain/services/items/story"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

var _ domain_services_items_story.IBooRepository = (*StoryQueryRepo)(nil)

func (r *StoryQueryRepo) RemoveBooInAssist(ctx context.Context, loginUserId int64, booIds ...int64) error {
	cmd := dal.Use(r.db.MasterDB(ctx)).UserHauntCapturedBoo
	if _, err := cmd.WithContext(ctx).Where(
		cmd.UserID.Eq(loginUserId),
		cmd.BooID.In(booIds...),
	).Delete(); err != nil {
		return errors.Wrapf(err, "booIds: %+v", booIds)
	}
	return nil
}
func (r *StoryQueryRepo) GetBoo(ctx context.Context, userId int64, booId int64) (*domain_entities_items.HauntBoo, error) {
	boos, err := r.BatchGetHauntBoos(ctx, userId, []int64{booId})
	if err != nil {
		return nil, err
	}
	if boo, ok := boos[booId]; ok {
		return boo, nil
	}
	return nil, errors.Wrapf(api_errors_v1.ErrorErrorReasonContentNotFound("boo not found"), "booId: %#v", booId)
}

func (r *StoryQueryRepo) UpdateBooInfo(ctx context.Context, boo *domain_entities_items.HauntBoo) error {
	cmd := dal.Use(r.db.MasterDB(ctx)).HauntBoo
	extraInfo, err := json.Marshal(boo)
	if err != nil {
		return errors.Wrapf(err, "boo: %+v", boo)
	}
	if _, err := cmd.WithContext(ctx).Where(
		cmd.ID.Eq(boo.Id),
	).UpdateColumnSimple(
		cmd.ExtraInfo.Value(string(extraInfo)),
	); err != nil {
		return errors.Wrapf(err, "boo: %+v", boo)
	}
	return nil
}
func (r *StoryQueryRepo) ListHauntBooAssist(ctx context.Context, userId int64, req *api_common_v1.ListRequest) ([]*domain_entities_items.HauntBoo, *api_common_v1.ListResponse, error) {
	offset := cast.ToInt(req.PageToken)
	limit := cast.ToInt(req.PageSize)

	q := dal.Use(r.db.MasterDB(ctx)).UserHauntCapturedBoo
	ms, err := q.
		WithContext(ctx).
		Where(q.UserID.Eq(userId)).
		Order(q.ID.Desc()).
		Offset(offset).
		Limit(limit + 1).
		Find()
	if err != nil {
		return nil, nil, errors.Wrapf(err, "userId: %#v", userId)
	}
	hasMore := false
	if len(ms) > limit {
		hasMore = true
		ms = ms[:limit]
	}

	booIds := lo.Map(ms, func(item *model.UserHauntCapturedBoo, _ int) int64 {
		return item.BooID
	})

	boos, err := r.BatchGetHauntBoos(ctx, userId, booIds)
	if err != nil {
		return nil, nil, err
	}
	var result []*domain_entities_items.HauntBoo
	for _, id := range booIds {
		boo, ok := boos[id]
		if !ok {
			continue
		}
		result = append(result, boo)
	}
	return result, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: cast.ToString(offset + limit),
	}, nil
}

func (r *StoryQueryRepo) AddCapturedBoo(ctx context.Context, userId int64, booId int64) error {
	sql := `INSERT IGNORE INTO user_haunt_captured_boos (user_id, boo_id) VALUES (?, ?)`
	if err := r.db.MasterDB(ctx).Exec(sql, userId, booId).Error; err != nil {
		return errors.Wrapf(err, "userId: %#v, booId: %#v", userId, booId)
	}
	return nil
}

func (s *StoryCmdRepo) createHauntBoo(ctx context.Context, boo *domain_entities_items.HauntBoo) error {
	cmd := dal.Use(s.db.MasterDB(ctx)).HauntBoo
	extraInfo, err := json.Marshal(boo)
	if err != nil {
		return errors.Wrapf(err, "boo: %+v", boo)
	}
	if err := cmd.WithContext(ctx).Create(&model.HauntBoo{
		ID:        boo.Id,
		UserID:    boo.CreatorId,
		ExtraInfo: string(extraInfo),
	}); err != nil {
		return errors.Wrapf(err, "boo: %+v", boo)
	}
	return nil
}

func (r *StoryQueryRepo) BatchGetHauntBoos(ctx context.Context, loginUserId int64, booIds []int64) (map[int64]*domain_entities_items.HauntBoo, error) {
	q := dal.Use(r.db.MasterDB(ctx)).HauntBoo
	boos, err := q.WithContext(ctx).Where(q.ID.In(booIds...)).Find()
	if err != nil {
		return nil, errors.Wrapf(err, "booIds: %+v", booIds)
	}
	userIds := lo.Map(boos, func(item *model.HauntBoo, _ int) int64 {
		return item.UserID
	})

	var avatarIds []int64

	result := make(map[int64]*domain_entities_items.HauntBoo)
	for _, boo := range boos {
		hauntBoo := &domain_entities_items.HauntBoo{
			Id:        boo.ID,
			CreatorId: boo.UserID,
		}
		if err := json.Unmarshal([]byte(boo.ExtraInfo), hauntBoo); err != nil {
			return nil, errors.Wrapf(err, "boo: %+v", boo)
		}
		avatarIds = append(avatarIds, hauntBoo.AvatarId)
		result[boo.ID] = hauntBoo
	}

	userInfos, err := r.usersInfoQueryRepo.BatchGetUserInfo(ctx, loginUserId, userIds...)
	if err != nil {
		return nil, err
	}
	avatars, err := r.avatarQueryRepo.BatchGetAvatarWithIds(ctx, loginUserId, avatarIds)
	if err != nil {
		return nil, err
	}

	for _, boo := range result {
		if userInfo, ok := userInfos[boo.CreatorId]; ok {
			boo.Creator = userInfo
		}
		if avatar, ok := avatars[boo.AvatarId]; ok {
			boo.Avatar = avatar
		}
	}

	return result, nil
}

func (r *StoryQueryRepo) injectHauntBoos(ctx context.Context, loginUserId int64, summaries []*domain_entities_items.StorySummary) error {
	haunts := lo.Filter(summaries, func(item *domain_entities_items.StorySummary, _ int) bool {
		return item.PlayType == api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HAUNT
	})
	booIds := lo.Flatten(lo.Map(haunts, func(item *domain_entities_items.StorySummary, _ int) []int64 {
		booIds := lo.Map(item.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers, func(item *domain_entities_items.HauntBoo, _ int) int64 {
			return item.Id
		})
		booIds = append(booIds, item.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id)
		return booIds
	}))
	booIds = lo.Uniq(booIds)
	if len(booIds) == 0 {
		return nil
	}

	boos, err := r.BatchGetHauntBoos(ctx, loginUserId, booIds)
	if err != nil {
		return err
	}
	for _, haunt := range haunts {
		for idx, a := range haunt.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers {
			boo, ok := boos[a.Id]
			if !ok {
				continue
			}
			haunt.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers[idx] = boo
		}
		boo, ok := boos[haunt.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id]
		if !ok {
			continue
		}
		boo.StoryCreatorBoo = true
		haunt.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers = boo
	}
	return nil
}
