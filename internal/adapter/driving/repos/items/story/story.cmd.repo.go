package adapter_driving_repos_items_story

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_story "boson/internal/domain/services/items/story"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gorm.io/gen/field"
)

var _ domain_services_items_story.IStoryCmdRepository = &StoryCmdRepo{}

type StoryCmdRepo struct {
	db             *data.Data
	templateRepo   *StoryTemplateRepo
	topRepo        *StoryTopRepo
	storyStatRepo  *StoryStatRepo
	storyQueryRepo *StoryQueryRepo
	portalRepo     *PortalRepo
}

func NewStoryCmdRepo(
	db *data.Data,
	templateRepo *StoryTemplateRepo,
	topRepo *StoryTopRepo,
	storyStatRepo *StoryStatRepo,
	portalRepo *PortalRepo,
	storyQueryRepo *StoryQueryRepo,
) *StoryCmdRepo {
	return &StoryCmdRepo{
		db:             db,
		templateRepo:   templateRepo,
		topRepo:        topRepo,
		storyStatRepo:  storyStatRepo,
		portalRepo:     portalRepo,
		storyQueryRepo: storyQueryRepo,
	}
}

func (s *StoryCmdRepo) IncreaseShareStat(ctx context.Context, storyId int64) error {
	return s.storyStatRepo.IncreaseShareStats(ctx, storyId)
}

// UpdateCoverImageKeyAndSize implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) UpdateCoverImageKeyAndSize(ctx context.Context, storyId int64, key domain_entities_resource.ImageResourcePath, width uint32, height uint32) error {
	cmd := dal.Use(s.db.MasterDB(ctx)).Story
	if _, err := cmd.WithContext(ctx).Where(
		cmd.ID.Eq(storyId),
	).UpdateColumnSimple(
		cmd.CoverImageObjectKey.Value(string(key)),
		cmd.CoverImageWidth.Value(int32(width)),
		cmd.CoverImageHeight.Value(int32(height)),
	); err != nil {
		return errors.Wrapf(err, "storyId: %d", storyId)
	}
	return nil
}

// DeleteStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) DeleteStory(ctx context.Context, userId int64, storyId int64) error {
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		// 同时删除 portal
		if err := s.portalRepo.deletePortalAndReadRecords(ctx, userId, storyId); err != nil {
			return err
		}
		cmd := dal.Use(s.db.MasterDB(ctx)).Story
		if _, err := cmd.WithContext(ctx).Where(
			cmd.ID.Eq(storyId),
		).UpdateColumnSimple(
			cmd.Status.Value(api_items_story_types_v1.StoryStatus_STORY_STATUS_DELETED.String()),
		); err != nil {
			return errors.Wrapf(err, "storyId: %d", storyId)
		}
		// 同时删除置顶
		if err := s.topRepo.TopStory(ctx, userId, storyId, false); err != nil {
			return err
		}
		return nil
	})
}

// UpdateStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) UpdateStory(ctx context.Context, userId int64, storyId int64, attr domain_services_items_story.UpdateStoryAttr) error {
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		cmd := dal.Use(s.db.MasterDB(ctx)).Story

		var updates []field.AssignExpr

		if attr.PrivacySettings != nil {
			if attr.PrivacySettings.PrivacyType != nil {
				updates = append(updates, cmd.PrivacyType.Value(attr.PrivacySettings.PrivacyType.String()))
			}
			if attr.PrivacySettings.VisibleBeforeTimestamp != nil {
				// Clamp to a safe MySQL TIMESTAMP (2037-12-31 23:59:59 UTC) to avoid boundary errors
				const mysqlTimestampMax int64 = 2145916799 // 2037-12-31 23:59:59 UTC
				ts := int64(*attr.PrivacySettings.VisibleBeforeTimestamp)
				if ts > mysqlTimestampMax {
					ts = mysqlTimestampMax
				}
				visibleBeforeTimestamp := time.Unix(ts, 0).UTC()
				updates = append(updates, cmd.DisappearAt.Value(visibleBeforeTimestamp))
			}
			// 修改了隐私，自动 Untop 掉
			if err := s.topRepo.TopStory(ctx, userId, storyId, false); err != nil {
				return err
			}
		}
		if len(updates) == 0 {
			return nil
		}
		if _, err := cmd.WithContext(ctx).Where(
			cmd.ID.Eq(storyId),
		).UpdateColumnSimple(
			updates...,
		); err != nil {
			return errors.Wrapf(err, "storyId: %d", storyId)
		}
		return nil
	})
}

// TopStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) TopStory(ctx context.Context, userId int64, storyId int64, isTop bool) error {
	return s.topRepo.TopStory(ctx, userId, storyId, isTop)
}

// CreateRoastedStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateRoastedStory(ctx context.Context, attr domain_services_items_story.CreateRoastedStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

func (s *StoryCmdRepo) UpdateHauntStoryConfigAndStatus(ctx context.Context, storyId int64, config *domain_entities_items.HauntStoryConfig, status api_items_story_types_v1.StoryStatus) error {
	configJsonBs, err := json.Marshal(config)
	if err != nil {
		return errors.Wrapf(err, "config: %+v", config)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		cmd := dal.Use(s.db.MasterDB(ctx)).Story
		if _, err := cmd.WithContext(ctx).Where(
			cmd.ID.Eq(storyId),
		).UpdateColumnSimple(
			cmd.PlayConfig.Value(string(configJsonBs)),
			cmd.Status.Value(status.String()),
		); err != nil {
			return errors.Wrapf(err, "storyId: %d", storyId)
		}
		return nil
	})
}

func (s *StoryCmdRepo) CreateHauntStory(ctx context.Context, attr domain_services_items_story.CreateHauntStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		// 把作者的 boo 也创建出来
		if err := s.createHauntBoo(ctx, attr.PlayConfig.CreatorBooWithQuestionsAndAnswers); err != nil {
			return err
		}
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HAUNT,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

// CreateBasePlayStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateBasePlayStory(ctx context.Context, attr domain_services_items_story.CreateBasePlayStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

// CreateTurtleSoupStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateTurtleSoupStory(ctx context.Context, attr domain_services_items_story.CreateTurtleSoupStoryAttr) error {
	if attr.PlayConfig.MaxTryCount == 0 {
		attr.PlayConfig.MaxTryCount = 10
	}
	if attr.PlayConfig.CommonConfig != nil && attr.PlayConfig.CommonConfig.MaxTryCount == 0 {
		attr.PlayConfig.CommonConfig.MaxTryCount = 10
	}
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}

	playType := api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP
	if attr.IsMass {
		playType = api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			playType,
			string(configJsonBs),
		); err != nil {
			return err
		}
		// if attr.PlayConfig.NeedCreateDiyTemplate && attr.PlayConfig.TemplateId != nil {
		// 	if err := s.templateRepo.CreateTurtleSoupStoryConditionTemplate(
		// 		ctx,
		// 		attr.CreatorID,
		// 		CreateTurtleSoupStoryConditionTemplateAttr{
		// 			FromTemplateId: *attr.PlayConfig.TemplateId,
		// 			Condition: struct {
		// 				Summary      string `json:"summary"`
		// 				Caption      string `json:"caption"`
		// 				IntentPrompt string `json:"intent_prompt"`
		// 			}{
		// 				Summary:      attr.PlayConfig.Caption,
		// 				IntentPrompt: attr.PlayConfig.IntentPrompt,
		// 				Caption:      attr.PlayConfig.Caption,
		// 			},
		// 		},
		// 	); err != nil {
		// 		return err
		// 	}
		// }
		return nil
	})
}

// CreateStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateImageExchangeStory(ctx context.Context, attr domain_services_items_story.CreateImageExchangeStoryAttr) error {
	for _, node := range attr.PlayConfig.Nodes {
		// 给一个默认的最大值，如果客户端没设置的话
		if node.MaxTryCount == 0 {
			node.MaxTryCount = 10
		}
		if node.ID == "" {
			node.ID = strings.ReplaceAll(uuid.New().String(), "-", "")
		}
		// if node.NeedCreateDiyTemplate {
		// 	if err := s.templateRepo.CreateTemplate(
		// 		ctx,
		// 		attr.CreatorID,
		// 		CreateConditionTemplateAttr{
		// 			Title:               attr.Title,
		// 			Description:         attr.Description,
		// 			CoverImageObjectKey: attr.CoverImageObjectKey,
		// 			Type:                api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE,
		// 			ConditionJSON:       string(configJsonBs),
		// 		},
		// 	); err != nil {
		// 		return err
		// 	}
		// }
	}
	if attr.PlayConfig.CommonConfig != nil && attr.PlayConfig.CommonConfig.MaxTryCount == 0 {
		attr.PlayConfig.CommonConfig.MaxTryCount = 10
	}
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.baseCreateStory(
		ctx,
		attr.StoryBaseCreateAttr,
		api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE,
		string(configJsonBs),
	)
}

// CreateUnmuteStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateUnmuteStory(ctx context.Context, attr domain_services_items_story.CreateUnmuteStoryAttr) error {
	if attr.PlayConfig.MaxTryCount == 0 {
		attr.PlayConfig.MaxTryCount = 3
	}
	if attr.PlayConfig.CommonConfig != nil && attr.PlayConfig.CommonConfig.MaxTryCount == 0 {
		attr.PlayConfig.CommonConfig.MaxTryCount = 10
	}
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

func (s *StoryCmdRepo) CreateNowShotStory(ctx context.Context, attr domain_services_items_story.CreateNowShotStoryAttr) error {
	if attr.PlayConfig.TTL == 0 {
		attr.PlayConfig.TTL = 60
	}
	if attr.PlayConfig.CommonConfig != nil && attr.PlayConfig.CommonConfig.MaxTryCount == 0 {
		attr.PlayConfig.CommonConfig.MaxTryCount = 10
	}
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

// CreateChatProxyStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateChatProxyStory(ctx context.Context, attr domain_services_items_story.CreateChatProxyStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

// CreateWassupStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateWassupStory(ctx context.Context, attr domain_services_items_story.CreateWassupStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

// CreateWhoStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateWhoStory(ctx context.Context, attr domain_services_items_story.CreateWhoStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

// CreateHideStory implements domain_services_items_story.IStoryCmdRepository.
func (s *StoryCmdRepo) CreateHideStory(ctx context.Context, attr domain_services_items_story.CreateHideStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HIDE,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

func (s *StoryCmdRepo) CreatePinStory(ctx context.Context, attr domain_services_items_story.CreatePinStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_PIN,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

func (s *StoryCmdRepo) CreateCapsuleStory(ctx context.Context, attr domain_services_items_story.CreateCapsuleStoryAttr) error {
	configJsonBs, err := json.Marshal(attr.PlayConfig)
	if err != nil {
		return errors.Wrapf(err, "attr: %+v", attr)
	}
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.baseCreateStory(
			ctx,
			attr.StoryBaseCreateAttr,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE,
			string(configJsonBs),
		); err != nil {
			return err
		}
		return nil
	})
}

func (s *StoryCmdRepo) baseCreateStory(ctx context.Context, attr domain_services_items_story.StoryBaseCreateAttr, playType api_items_story_types_v1.StoryPlayType, playConfigJsonStr string) error {
	return s.db.ExecTx(ctx, func(ctx context.Context) error {
		cmd := dal.Use(s.db.MasterDB(ctx)).Story
		userStat := dal.Use(s.db.MasterDB(ctx)).UserStat
		storyStat := dal.Use(s.db.MasterDB(ctx)).StoriesStat
		storiesManagement := dal.Use(s.db.MasterDB(ctx)).StoriesManagement

		m := &model.Story{
			ID:                  attr.Id,
			CoverImageObjectKey: string(attr.CoverImage.Path),
			CoverImageHeight:    int32(attr.CoverImage.Height),
			CoverImageWidth:     int32(attr.CoverImage.Width),
			CreatorID:           attr.CreatorID,
			Status:              api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED.String(),
			PlayType:            playType.String(),
			PlayConfig:          playConfigJsonStr,
			// 默认值
			PrivacyType: api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_PUBLIC.String(),
			Version:     domain_entities_items.StoryVersion_V1,
		}
		if attr.Status != nil {
			m.Status = attr.Status.String()
		}
		if attr.Version != nil {
			m.Version = int32(*attr.Version)
		}
		// 默认值，选择一个安全的 MySQL TIMESTAMP（2037-12-31 23:59:59 UTC），避免边界问题
		const mysqlTimestampMax int64 = 2145916799 // 2037-12-31 23:59:59 UTC
		m.DisappearAt = time.Unix(mysqlTimestampMax, 0).UTC()

		if attr.PrivacySettings != nil {
			if attr.PrivacySettings.PrivacyType != nil {
				m.PrivacyType = attr.PrivacySettings.PrivacyType.String()
			}
			if attr.PrivacySettings.VisibleBeforeTimestamp != nil {
				ts := int64(*attr.PrivacySettings.VisibleBeforeTimestamp)
				if ts > mysqlTimestampMax {
					ts = mysqlTimestampMax
				}
				m.DisappearAt = time.Unix(ts, 0).UTC()
			}
		}
		if err := cmd.WithContext(ctx).Create(m); err != nil {
			return errors.Wrapf(err, "attr: %+v", attr)
		}
		if err := storyStat.WithContext(ctx).Create(&model.StoriesStat{
			StoryID:       attr.Id,
			ReactionStats: "{}",
		}); err != nil {
			return errors.Wrapf(err, "attr: %+v", attr)
		}
		if err := storiesManagement.WithContext(ctx).Create(&model.StoriesManagement{
			StoryID:      attr.Id,
			FeedStatus:   "FEED_STATUS_NORMAL",
			FeedBoost:    1,
			StoryQuality: "STORY_QUALITY_NORMAL",
			PushStatus:   "PUSH_STATUS_FOLLOWED",
		}); err != nil {
			return errors.Wrapf(err, "attr: %+v", attr)
		}
		if _, err := userStat.WithContext(ctx).Where(
			userStat.UserID.Eq(attr.CreatorID),
		).UpdateColumnSimple(
			userStat.StoriesCount.Add(1),
		); err != nil {
			return errors.Wrapf(err, "attr: %+v", attr)
		}
		story, err := s.storyQueryRepo.GetStoryDetail(ctx, attr.CreatorID, attr.Id)
		if err != nil {
			return err
		}
		if _, err := s.portalRepo.CreatePortal(ctx, story, attr.UserMoments); err != nil {
			return err
		}
		return nil
	})
}
