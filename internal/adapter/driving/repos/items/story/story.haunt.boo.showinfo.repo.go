package adapter_driving_repos_items_story

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_items_story "boson/internal/domain/services/items/story"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

var _ domain_services_items_story.IHauntBooShowInfoRepository = (*StoryHauntBooShowInfoRepo)(nil)

type StoryHauntBooShowInfoRepo struct {
	huantBooQuery *StoryQueryRepo
	db            *data.Data
}

func NewStoryHauntBooShowInfoRepo(db *data.Data, huantBooQuery *StoryQueryRepo) *StoryHauntBooShowInfoRepo {
	return &StoryHauntBooShowInfoRepo{db: db, huantBooQuery: huantBooQuery}
}

// BatchGetContentPollutionHauntBooShowInfo implements domain_services_items_story.IHauntBooShowInfoRepository.
func (s *StoryHauntBooShowInfoRepo) BatchGetContentPollutionHauntBooShowInfo(ctx context.Context, loginUserId int64, userIds []int64) (map[int64]*domain_entities_items.HauntBooShowInfo, error) {
	q := dal.Use(s.db.MasterDB(ctx)).UserHauntBoosShowInfo
	ms, err := q.WithContext(ctx).Where(
		q.UserID.In(
			userIds...,
		),
		q.Scene.Eq(api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_CONTENT_POLLUTION.String()),
	).Find()

	if err != nil {
		return nil, errors.Wrapf(err, "userIds: %v", userIds)
	}

	showInfos := make(map[int64]*domain_entities_items.HauntBooShowInfo)

	for _, m := range ms {
		showInfo := &domain_entities_items.HauntBooShowInfo{}
		err = json.Unmarshal([]byte(m.ShowInfo), showInfo)
		if err != nil {
			return nil, errors.Wrapf(err, "userId: %d", m.UserID)
		}
		showInfos[m.UserID] = showInfo
	}

	// inject boos
	if err := s.injectBoos(ctx, loginUserId, lo.Values(showInfos)...); err != nil {
		return nil, err
	}
	// inject story
	if err := s.injectStories(ctx, loginUserId, lo.Values(showInfos)...); err != nil {
		return nil, err
	}

	return showInfos, nil
}

// GetUserFeedHauntBooShowInfo implements domain_services_items_story.IHauntBooShowInfoRepository.
func (s *StoryHauntBooShowInfoRepo) GetUserHauntBooShowInfo(ctx context.Context, userId int64, scene api_items_story_types_v1.HauntBooShowInfoSence) (*domain_entities_items.HauntBooShowInfo, error) {
	q := dal.Use(s.db.MasterDB(ctx)).UserHauntBoosShowInfo
	m, err := q.WithContext(ctx).Where(
		q.UserID.Eq(userId),
		q.Scene.Eq(scene.String()),
	).First()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, errors.Wrapf(err, "userId: %d", userId)
	}
	if m == nil {
		return nil, nil
	}
	showInfo := &domain_entities_items.HauntBooShowInfo{}
	err = json.Unmarshal([]byte(m.ShowInfo), showInfo)
	if err != nil {
		return nil, errors.Wrapf(err, "userId: %d", userId)
	}
	// inject boos
	if err := s.injectBoos(ctx, userId, showInfo); err != nil {
		return nil, err
	}
	// inject story
	if err := s.injectStories(ctx, userId, showInfo); err != nil {
		return nil, err
	}
	return showInfo, nil
}

func (s *StoryHauntBooShowInfoRepo) injectStories(ctx context.Context, userId int64, showInfos ...*domain_entities_items.HauntBooShowInfo) error {
	var storyIds []int64
	for _, showInfo := range showInfos {
		storyIds := lo.Map(showInfo.BooShowInfos, func(showInfo *domain_entities_items.ShowInfo, _ int) int64 {
			return showInfo.FromStoryId
		})
		storyIds = append(storyIds, storyIds...)
	}

	stories, err := s.huantBooQuery.BatchGetStoryDetails(ctx, userId, storyIds)
	if err != nil {
		return err
	}
	for _, showInfo := range showInfos {
		for _, showInfo := range showInfo.BooShowInfos {
			story, ok := stories[showInfo.FromStoryId]
			if !ok {
				continue
			}
			showInfo.FromStory = story
		}
	}
	return nil
}

func (s *StoryHauntBooShowInfoRepo) injectBoos(ctx context.Context, userId int64, showInfos ...*domain_entities_items.HauntBooShowInfo) error {
	var booIds []int64
	for _, showInfo := range showInfos {
		for _, booShowInfo := range showInfo.BooShowInfos {
			booIds = append(booIds, booShowInfo.CreatorBooId)
			booIds = append(booIds, booShowInfo.AssistBooIds...)
		}
	}

	boos, err := s.huantBooQuery.BatchGetHauntBoos(ctx, userId, booIds)
	if err != nil {
		return err
	}

	for _, showInfo := range showInfos {
		for _, showInfo := range showInfo.BooShowInfos {
			boo, ok := boos[showInfo.CreatorBooId]
			if !ok {
				continue
			}
			showInfo.CreatorBoo = boo
			for _, assistBooId := range showInfo.AssistBooIds {
				assistBoo, ok := boos[assistBooId]
				if !ok {
					continue
				}
				showInfo.AssistBoos = append(showInfo.AssistBoos, assistBoo)
			}
		}
	}
	return nil
}

// BatchSaveUserHauntBooShowInfos implements domain_services_items_story.IHauntBooShowInfoRepository.
func (s *StoryHauntBooShowInfoRepo) BatchSaveUserHauntBooShowInfos(ctx context.Context, showInfos map[int64]*domain_entities_items.HauntBooShowInfo) error {
	if len(showInfos) == 0 {
		return nil
	}

	// 构建批量插入的 SQL 语句
	var values []string
	var args []interface{}

	for userId, showInfo := range showInfos {
		jsonValue, err := json.Marshal(showInfo)
		if err != nil {
			return errors.Wrapf(err, "userId: %d, showInfo: %+v", userId, showInfo)
		}

		values = append(values, "(?, ?, ?)")
		args = append(args, userId, showInfo.Scene.String(), string(jsonValue))
	}

	// 构建完整的 SQL 语句
	sql := fmt.Sprintf(`
		INSERT INTO user_haunt_boos_show_infos (user_id, scene, show_info)
		VALUES %s
		ON DUPLICATE KEY UPDATE
			show_info = VALUES(show_info),
			updated_at = CURRENT_TIMESTAMP
	`, strings.Join(values, ","))

	// 执行批量插入
	result := s.db.MasterDB(ctx).Exec(sql, args...)
	if result.Error != nil {
		return errors.Wrapf(result.Error, "showInfos count: %d", len(showInfos))
	}

	return nil
}
