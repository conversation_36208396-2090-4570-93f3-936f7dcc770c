package adapter_driving_repos_app_settings

import (
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type Repo struct {
	data *data.Data
}

type AppSettingsKey string

const (
	SnapshotLLMConfig        AppSettingsKey = "snapshot_llm_config"
	RevealLLMConfig          AppSettingsKey = "reveal_llm_config"
	RevealLLMV2Config        AppSettingsKey = "reveal_llm_v2_config"
	PeekLLMConfig            AppSettingsKey = "peek_llm_config"
	FizzLLMConfig            AppSettingsKey = "fizz_llm_config"
	UnmuteLLMConfig          AppSettingsKey = "unmute_llm_config"
	StirupLLMConfig          AppSettingsKey = "stirup_llm_config"
	RoastedLLMConfig         AppSettingsKey = "roasted_llm_config"
	RoastedTopic             AppSettingsKey = "roasted_topics_config"
	WassupVideoConfig        AppSettingsKey = "wassup_video_config"
	CapsuleConfig            AppSettingsKey = "capsule_config"
	ChatProxyLLMConfig       AppSettingsKey = "chatproxy_llm_config"
	StoryCoPilotLLMConfig    AppSettingsKey = "story_co_pilot_llm_config"
	HauntLLMConfig           AppSettingsKey = "haunt_llm_config"
	PinSceneParsingLLMConfig AppSettingsKey = "pin_scene_parsing_llm_config"

	// BOO 任务并发配置
	BOOJobSchedulerConfig AppSettingsKey = "boo_job_scheduler_config"

	// 匹配配置
	PeekMatchConfig AppSettingsKey = "peek_match_config"

	// Story Feed 版本号控制
	StoryFeedVersion AppSettingsKey = "story_feed_version"

	// 聊天代理玩法配置
	ChatProxyConfig AppSettingsKey = "chat_proxy_config"
)

func Save[T any](ctx context.Context, key AppSettingsKey, value T, storage ConfigStorage) error {
	jsonValue, err := json.Marshal(value)
	if err != nil {
		return errors.Wrapf(err, "key:%s, value:%v", key, value)
	}
	return storage.save(ctx, key, string(jsonValue))
}
func Get[T any](ctx context.Context, key AppSettingsKey, storage ConfigStorage) (T, error) {
	value, err := storage.get(ctx, key)
	if err != nil {
		return *new(T), err
	}
	var result T
	if err := json.Unmarshal([]byte(value), &result); err != nil {
		return *new(T), errors.Wrapf(err, "key:%s, value:%s", key, value)
	}
	return result, nil
}

func Init[T any](ctx context.Context, key AppSettingsKey, value T, storage ConfigStorage) error {
	jsonValue, err := json.Marshal(value)
	if err != nil {
		return errors.Wrapf(err, "key:%s, value:%v", key, value)
	}
	return storage.init(ctx, key, string(jsonValue))
}

// ------------------------------------------------------------ private methods
type ConfigStorage interface {
	get(ctx context.Context, key AppSettingsKey) (string, error)
	save(ctx context.Context, key AppSettingsKey, value string) error
	init(ctx context.Context, key AppSettingsKey, value string) error
}

func NewRepo(data *data.Data) *Repo {
	return &Repo{data: data}
}

// TOOD cache
func (r *Repo) get(ctx context.Context, key AppSettingsKey) (string, error) {
	q := dal.Use(r.data.MasterDB(ctx)).AppSetting
	result, err := q.WithContext(ctx).Where(q.Key.Eq(string(key))).First()
	if err != nil {
		return "", errors.Wrapf(err, "key: %s", key)
	}
	return result.Value, nil
}

// TOOD cache
func (r *Repo) save(ctx context.Context, key AppSettingsKey, value string) error {
	q := dal.Use(r.data.MasterDB(ctx)).AppSetting
	// create or update
	_, err := q.WithContext(ctx).Where(q.Key.Eq(string(key))).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// create if not found
			return r.init(ctx, key, value)
		}
		return errors.Wrapf(err, "key: %s", key)
	}
	if _, err := q.WithContext(ctx).Where(q.Key.Eq(string(key))).Update(q.Value, value); err != nil {
		return errors.Wrapf(err, "key: %s", key)
	}
	return nil
}

func (r *Repo) init(ctx context.Context, key AppSettingsKey, value string) error {
	q := dal.Use(r.data.MasterDB(ctx)).AppSetting
	if err := q.WithContext(ctx).Save(&model.AppSetting{Key: string(key), Value: value}); err != nil {
		return errors.Wrapf(err, "key: %s", key)
	}
	return nil
}
