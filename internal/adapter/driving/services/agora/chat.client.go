package driving_services_agora

import (
	"boson/internal/conf"
	domain_services_im "boson/internal/domain/services/im"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"

	"github.com/AgoraIO/Tools/DynamicKey/AgoraDynamicKey/go/src/chatTokenBuilder"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
)

var _ domain_services_im.IIMClient = &Client{}

// SendMessage implements domain_services_im.IIMClient.
func (c *Client) SendMessage(ctx context.Context, fromUserId int64, message *api_im_message_types_v1.Message, toUserIds ...int64) error {
	if c.conf.Env == conf.ENV_ENV_UT {
		return nil
	}
	switch message.MessageType {
	case api_im_message_types_v1.MessageType_MESSAGE_TYPE_TEXT:
		return c.baseSendMessage(ctx, fromUserId, "txt", map[string]interface{}{"msg": message.Body.GetContent()}, false, toUserIds...)
	case api_im_message_types_v1.MessageType_MESSAGE_TYPE_CUSTOM:
		return c.sendCustomMessageToUser(ctx, fromUserId, message.Body.GetCustomMessagePayload(), toUserIds...)
	default:
		return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("unsupported message type"))
	}
}
func (c *Client) SendCustomMessages(ctx context.Context, fromUserId int64, message *api_im_message_types_v1.CustomMessagePayload, toUserIds ...int64) error {
	return c.sendCustomMessageToUser(ctx, fromUserId, message, toUserIds...)
}

func (c *Client) sendCustomMessageToUser(ctx context.Context, fromUserId int64, message *api_im_message_types_v1.CustomMessagePayload, toUserIds ...int64) error {
	if c.conf.Env == conf.ENV_ENV_UT {
		return nil
	}
	body := map[string]interface{}{
		"customEvent": message.CustomMessageType,
		"customExts":  map[string]string{},
	}
	silence := false
	switch message.CustomMessageType {
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION.String():
		haunt := message.GetStoryHauntInteraction()
		customExts := map[string]string{
			"video_url":       haunt.GetVideoUrl(),
			"video_cover_url": haunt.GetVideoCoverUrl(),
			"boo_avatar_url":  haunt.GetBooAvatarUrl(),
			"story_id":        haunt.GetStoryId(),
			"consume_status":  message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION.String():
		bs, err := json.Marshal(message.GetStoryExchangeImageInteraction())
		if err != nil {
			return errors.Wrapf(err, "message %#v", message)
		}
		var customExts map[string]string
		if err := json.Unmarshal(bs, &customExts); err != nil {
			return errors.Wrapf(err, "message %#v, bs : %s", message, string(bs))
		}
		customExts["consume_status"] = message.ConsumeStatus
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION.String():
		turtleSoup := message.GetStoryTurtleSoupInteraction()
		customExts := map[string]string{
			"payload_type":        turtleSoup.PayloadType,
			"story_info_json_str": turtleSoup.StoryInfoJsonStr,
			"consume_status":      message.ConsumeStatus,
		}
		// 根据负载类型处理不同的字段
		switch turtleSoup.PayloadType {
		case api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_TEXT.String():
			customExts["text"] = turtleSoup.GetText()
		case api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_END_IMAGE.String():
			endPayload := turtleSoup.GetEndImagePayload()
			customExts["end_image_url"] = endPayload.EndImageUrl
			customExts["end_message"] = endPayload.EndMessage
		}
		customExts["ai_response"] = turtleSoup.GetAiResponse()
		customExts["text"] = turtleSoup.GetUserText()

		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_FIZZ_INTERACTION.String():
		fizz := message.GetStoryFizzInteraction()
		customExts := map[string]string{
			"fizz_id":        fizz.GetFizzId(),
			"payload_type":   fizz.GetPayloadType(),
			"text":           fizz.GetText(),
			"expire_at":      fizz.GetExpireAt(),
			"media_id":       fizz.GetMediaId(),
			"consume_status": message.ConsumeStatus,
		}
		switch fizz.GetPayloadType() {
		case api_im_message_types_v1.StoryFizzCustomMessagePayload_PAYLOAD_TYPE_IMAGE.String():
			customExts["image_url"] = fizz.GetImageUrl()
		case api_im_message_types_v1.StoryFizzCustomMessagePayload_PAYLOAD_TYPE_VIDEO.String():
			customExts["video_url"] = fizz.GetVideoPayload().GetVideoUrl()
			customExts["cover_url"] = fizz.GetVideoPayload().GetCoverUrl()
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION.String():
		nowShot := message.GetStoryNowShotInteraction()
		customExts := map[string]string{
			"title":              nowShot.GetTitle(),
			"payload_type":       nowShot.GetPayloadType(),
			"story_id":           nowShot.GetStoryId(),
			"story_cover_url":    nowShot.GetStoryCoverUrl(),
			"consumer_image_url": nowShot.GetConsumerImageUrl(),
			"consumer_video_url": nowShot.GetConsumerVideoUrl(),

			"consumer_interaction_image_url": nowShot.GetConsumerInteractionImageUrl(),
			"consumer_interaction_video_url": nowShot.GetConsumerInteractionVideoUrl(),

			"consume_status": message.ConsumeStatus,
		}

		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION.String():
		unmute := message.GetStoryUnmuteInteraction()
		customExts := map[string]string{
			"title":                          unmute.GetTitle(),
			"story_id":                       unmute.GetStoryId(),
			"story_cover_url":                unmute.GetStoryCoverUrl(),
			"consumer_audio_url":             unmute.GetConsumerAudioUrl(),
			"consumer_interaction_image_url": unmute.GetConsumerInteractionImageUrl(),
			"ai_response":                    unmute.GetAiResponse(),
			"consume_status":                 message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION.String():
		wassup := message.GetStoryWassupInteraction()
		customExts := map[string]string{
			"consumer_video_cover_url": wassup.GetConsumerVideoCoverUrl(),
			"consumer_video_url":       wassup.GetConsumerVideoUrl(),
			"story_cover_url":          wassup.GetStoryCoverUrl(),
			"story_id":                 wassup.GetStoryId(),
			"consume_status":           message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_RELAY_FIZZ_INVITE.String():
		customExts := map[string]string{
			"room_id":        message.GetRelayFizzJoinInvite().GetRoomId(),
			"creator_id":     message.GetRelayFizzJoinInvite().GetCreatorId(),
			"expire_at":      fmt.Sprint(message.GetRelayFizzJoinInvite().ExpireAtUnixstamp),
			"consume_status": message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_RUSH_FIZZ_INVITE.String():
		customExts := map[string]string{
			"room_id":        message.GetRushFizzJoinInvite().GetRoomId(),
			"creator_id":     message.GetRushFizzJoinInvite().GetCreatorId(),
			"expire_at":      fmt.Sprint(message.GetRushFizzJoinInvite().ExpireAtUnixstamp),
			"consume_status": message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION.String():
		capsule := message.GetStoryCapsuleInteraction()
		customExts := map[string]string{
			"title":              capsule.GetTitle(),
			"story_id":           capsule.GetStoryId(),
			"story_cover_url":    capsule.GetStoryCoverUrl(),
			"consumer_image_url": capsule.GetConsumerImageUrl(),
			"in_days":            fmt.Sprintf("%d", capsule.GetInDays()),
			"moments":            fmt.Sprintf("%d", capsule.GetMoments()),
			"consume_status":     message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_BOO_INTERACTION.String():
		boo := message.GetBooInteraction()
		customExts := map[string]string{
			"message_type": boo.GetMessageType(),
		}
		if boo.VideoPayload != nil {
			customExts["video_url"] = boo.VideoPayload.GetVideoUrl()
			customExts["cover_url"] = boo.VideoPayload.GetCoverUrl()
		} else {
			// 文字消息静默发送
			silence = true
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION.String():
		roasted := message.GetStoryRoastedInteraction()
		customExts := map[string]string{
			"consumer_video_cover_url": roasted.GetConsumerVideoCoverUrl(),
			"consumer_video_url":       roasted.GetConsumerVideoUrl(),
			"story_cover_url":          roasted.GetStoryCoverUrl(),
			"story_id":                 roasted.GetStoryId(),
			"consume_status":           message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION.String():
		chatProxy := message.GetStoryChatproxyInteraction()
		customExts := map[string]string{
			"consumer_video_cover_url": chatProxy.GetConsumerVideoCoverUrl(),
			"consumer_video_url":       chatProxy.GetConsumerVideoUrl(),
			"story_cover_url":          chatProxy.GetStoryCoverUrl(),
			"story_id":                 chatProxy.GetStoryId(),
			"consume_status":           message.ConsumeStatus,
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION.String():
		who := message.GetStoryWhoInteraction()
		avatarUrlJsonBytes, _ := json.Marshal(who.GetAvatarUrls())
		customExts := map[string]string{
			"consume_status":      message.ConsumeStatus,
			"correct":             fmt.Sprintf("%t", who.GetCorrect()),
			"tried_times":         fmt.Sprintf("%d", who.GetTriedTimes()),
			"selected_avatar_url": who.GetSelectedAvatarUrl(),
			"selected_user_name":  who.GetSelectedUserName(),
			"story_id":            who.GetStoryId(),
			"avatar_urls":         string(avatarUrlJsonBytes),
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION.String():
		basePlay := message.GetStoryBaseplayInteraction()
		customExts := map[string]string{
			"consume_status":           message.ConsumeStatus,
			"story_id":                 basePlay.GetStoryId(),
			"story_cover_url":          basePlay.GetStoryCoverUrl(),
			"title":                    basePlay.GetTitle(),
			"consumer_story_cover_url": basePlay.GetConsumerStoryCoverUrl(),
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED.String():
		customExts := map[string]string{
			"consume_status":  message.ConsumeStatus,
			"story_id":        message.GetStoryHideStickerUnlocked().GetStoryId(),
			"story_cover_url": message.GetStoryHideStickerUnlocked().GetStoryCoverUrl(),
			"sticker_url":     message.GetStoryHideStickerUnlocked().GetStickerUrl(),
			"title":           message.GetStoryHideStickerUnlocked().GetTitle(),
		}
		body["customExts"] = customExts
	case api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_MOMENT_QUOTE.String():
		momentQuote := message.GetStoryMomentQuote()
		customExts := map[string]string{
			"moment_id":                    momentQuote.GetMomentId(),
			"portal_id":                    momentQuote.GetPortalId(),
			"first_moment_cover_image_url": momentQuote.GetFirstMomentCoverImageUrl(),
			"text":                         momentQuote.GetText(),
		}
		body["customExts"] = customExts
	default:
		return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("unsupported custom message type"))
	}
	return c.baseSendMessage(ctx, fromUserId, "custom", body, silence, toUserIds...)
}

func (c *Client) baseSendMessage(ctx context.Context, fromUserId int64, mtype string, body map[string]interface{}, silence bool, toUserIds ...int64) error {
	// 构建请求URL
	url := fmt.Sprintf(
		"https://%s/%s/%s/messages/users",
		c.conf.ThirdParty.Agora.Host,
		c.conf.ThirdParty.Agora.OrgName,
		c.conf.ThirdParty.Agora.AppName,
	)

	// 获取发送者用户信息用于推送
	var pushExt map[string]interface{}
	var apnsExt map[string]interface{}
	if mtype == "txt" && c.userInfoService != nil {
		userInfoMap, err := c.userInfoService.BatchGetUserInfo(ctx, fromUserId, fromUserId)
		if err == nil && len(userInfoMap) > 0 {
			senderInfo := userInfoMap[fromUserId]
			if senderInfo != nil {
				pushExt = map[string]interface{}{
					"title":   senderInfo.Nickname,
					"content": body["msg"],
				}
				apnsExt = map[string]interface{}{
					"image_url": senderInfo.AvatarImagePath.UserDetailAvatar(),
				}
			}
		} else {
			c.logger.WithContext(ctx).Warnf("Failed to get user info for push template, userId: %d, err: %v", fromUserId, err)
		}
	}

	ext := map[string]interface{}{
		"em_ignore_notification": silence,
	}
	if pushExt != nil {
		ext["em_push_ext"] = pushExt
	}
	if apnsExt != nil {
		ext["em_apns_ext"] = apnsExt
	}

	// 构建请求体
	reqBody := map[string]interface{}{
		"from": fmt.Sprintf("%d", fromUserId),
		"to": lo.Map(toUserIds, func(id int64, _ int) string {
			return fmt.Sprint(id)
		}),
		"type": mtype,
		"body": body,
		// Whether to synchronize the message to the message sender.
		"sync_device": true,
		/*Whether to send a silent message:
		true: Yes;
		(Default)false: No.
		Sending silent messages means that when the user is offline, Agora Chat will not push message notifications to
		the user's device through a third-party message push service. Therefore, users will not receive push notifications
		for messages. When the user goes online again, all messages sent from the offline period will be received. Unlike
		the Do Not Disturb mode which is set by the recipient to prevent notifications during a certain period, sending
		silent messages is set by the sender.
		*/
		"ext": ext,
	}

	// 有一些偶发的消息结构错误，在这里打印下
	c.logger.WithContext(ctx).Debugf("req body: %#v", reqBody)

	// 构建请求
	bs, err := json.Marshal(reqBody)
	if err != nil {
		return errors.Wrapf(err, "from user %d to user %#v", fromUserId, toUserIds)
	}
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(bs))
	if err != nil {
		return errors.Wrapf(err, "from user %d to user %#v", fromUserId, toUserIds)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	token, err := chatTokenBuilder.BuildChatAppToken(
		c.conf.ThirdParty.Agora.AppId,
		c.conf.ThirdParty.Agora.AppCert,
		// 24 hours
		86400*1000,
	)
	if err != nil {
		return errors.Wrapf(err, "from user %d to user %#v", fromUserId, toUserIds)
	}
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

	// 发送请求
	resp, err := otelhttp.DefaultClient.Do(req)
	if err != nil {
		return errors.Wrapf(err, "from user %d to user %#v", fromUserId, toUserIds)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		bs, _ := io.ReadAll(resp.Body)
		err := fmt.Errorf("send message failed with status code: %d, body: %s", resp.StatusCode, string(bs))
		return errors.Wrapf(err, "from user %d to user %#v", fromUserId, toUserIds)
	}

	return nil
}
