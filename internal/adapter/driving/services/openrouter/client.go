package adapter_driving_services_openrouter

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"

	"boson/internal/conf"

	adapter_driving_services_llm "boson/internal/adapter/driving/services/llm"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	openai "github.com/sashabaranov/go-openai"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
)

type Client struct {
	logger *log.Helper
	conf   *conf.Bootstrap
}

func NewClient(config *conf.Bootstrap, logger *log.Helper) *Client {
	c := &Client{conf: config, logger: logger}
	return c
}

func (c *Client) buildOpenaiClient(config *conf.Bootstrap, chatCfg adapter_driving_services_llm.ChatCompletionConfig) *openai.Client {
	defaultHttpClient := otelhttp.DefaultClient
	if config.Env == conf.ENV_ENV_LOCAL {
		// 开启 7890 代理
		proxyUrl, err := url.Parse("http://127.0.0.1:7890")
		if err != nil {
			panic(err)
		}
		defaultHttpClient = &http.Client{
			Transport: &http.Transport{
				Proxy: http.ProxyURL(proxyUrl),
			},
		}
	}
	cfg := openai.DefaultConfig(chatCfg.ApiKey)
	cfg.BaseURL = chatCfg.Host
	cfg.HTTPClient = defaultHttpClient
	return openai.NewClientWithConfig(cfg)
}

func (c *Client) Complete(
	ctx context.Context,
	cfg adapter_driving_services_llm.ChatCompletionConfig,
	messages []openai.ChatCompletionMessage,
) (string, error) {
	// log messages
	c.logger.WithContext(ctx).Infof("[LLM Query] messages: %v, model: %v, temperature: %v", messages, cfg.Model, cfg.Temperature)
	response, err := c.buildOpenaiClient(c.conf, cfg).CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:          cfg.Model,
		Messages:       messages,
		Temperature:    cfg.Temperature,
		ResponseFormat: cfg.ResponseFormat,
	})
	if err != nil {
		return "", errors.WithStack(err)
	}
	c.logger.WithContext(ctx).Infof("[LLM Query] response: %v", response.Choices[0].Message.Content)
	return response.Choices[0].Message.Content, nil
}

func (c *Client) ChatCompletion(
	ctx context.Context,
	cfg adapter_driving_services_llm.ChatCompletionConfig,
	messages []openai.ChatCompletionMessage,
) (string, error) {
	c.logger.WithContext(ctx).Infof("[LLM Query] messages: %v, cfg: %v", messages, cfg)
	response, err := c.buildOpenaiClient(c.conf, cfg).CreateChatCompletion(ctx, openai.ChatCompletionRequest{
		Model:          cfg.Model,
		Messages:       messages,
		Temperature:    cfg.Temperature,
		ResponseFormat: cfg.ResponseFormat,
	})
	if err != nil {
		// 详细记录错误信息
		c.logger.WithContext(ctx).Errorf("[LLM Query] Error occurred: %+v", err)
		c.logger.WithContext(ctx).Errorf("[LLM Query] Error type: %T", err)
		c.logger.WithContext(ctx).Errorf("[LLM Query] Error string: %s", err.Error())
		c.logger.WithContext(ctx).Infof("[LLM Query] Response when error: %+v", response)

		// 如果是 OpenAI API 错误，尝试提取更多信息
		if apiErr, ok := err.(*openai.APIError); ok {
			c.logger.WithContext(ctx).Errorf("[LLM Query] OpenAI API Error - Code: %s, Message: %s, Type: %s",
				apiErr.Code, apiErr.Message, apiErr.Type)
			c.logger.WithContext(ctx).Errorf("[LLM Query] OpenAI API Error - HTTPStatusCode: %d", apiErr.HTTPStatusCode)
		}

		return "", errors.WithStack(err)
	}
	c.logger.WithContext(ctx).Infof("[LLM Query] response: %v", response.Choices[0].Message.Content)
	return response.Choices[0].Message.Content, nil
}

// 内部会开启异步，返回一个 channel，需要外部通过 for 循环消费
func (c *Client) ChatCompletionWithStreaming(
	ctx context.Context,
	cfg adapter_driving_services_llm.ChatCompletionConfig,
	messages []openai.ChatCompletionMessage,
) (<-chan StreamEvent, error) {

	req := openai.ChatCompletionRequest{
		Model:          cfg.Model,
		Messages:       messages,
		Stream:         true,
		Temperature:    cfg.Temperature,
		ResponseFormat: cfg.ResponseFormat,
	}

	stream, err := c.buildOpenaiClient(c.conf, cfg).CreateChatCompletionStream(ctx, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	reqJsonStr, err := json.Marshal(req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	c.logger.WithContext(ctx).Infof("req: %s", string(reqJsonStr))

	resultChan := make(chan StreamEvent)
	go func() {
		defer close(resultChan)
		defer stream.Close()
		for {
			response, err := stream.Recv()
			if errors.Is(err, io.EOF) {
				return
			}
			if err != nil {
				resultChan <- StreamEvent{
					Error: errors.WithStack(err),
				}
				return
			}
			resultChan <- StreamEvent{
				Content: response.Choices[0].Delta.Content,
			}
		}
	}()
	return resultChan, nil
}
