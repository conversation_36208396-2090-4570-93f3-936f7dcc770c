package adapter_driving_services_recommend

import (
	api_common_v1 "boson/api/common/v1"
	api_users_recommend_v1 "boson/api/users/recommend/v1"
	"boson/internal/conf"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_services_fizz "boson/internal/domain/services/fizz"
	domain_services_items_story "boson/internal/domain/services/items/story"
	domain_services_users_recommend "boson/internal/domain/services/users/recommend"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
)

var _ domain_services_items_story.IRecommendService = (*Client)(nil)
var _ domain_services_fizz.IRecommendFizzsClient = (*Client)(nil)
var _ domain_services_users_recommend.IRecommendUsersClient = (*Client)(nil)

type Client struct {
	config *conf.Bootstrap
}

func NewClient(config *conf.Bootstrap) *Client {
	return &Client{
		config: config,
	}
}

// RecommendUsers implements domain_services_users_recommend.IRecommendUsersClient.
func (c *Client) RecommendUsers(ctx context.Context, userId int64, personId *int64, scenario api_users_recommend_v1.RecmmendScenario, listRequest *api_common_v1.ListRequest) (recommendUsers []*domain_services_users_recommend.RecommendUser, sortRequestId string, res *api_common_v1.ListResponse, err error) {
	if c.config.Env == conf.ENV_ENV_UT {
		return nil, "", nil, nil
	}

	path := "person"
	switch scenario {
	case api_users_recommend_v1.RecmmendScenario_RECOMMEND_SCENARIO_NOTIFICATION_PAGE:
		path = "chat"
	case api_users_recommend_v1.RecmmendScenario_RECOMMEND_SCENARIO_PERSON_PAGE:
		path = "person"
	case api_users_recommend_v1.RecmmendScenario_RECOMMEND_SCENARIO_SEARCH_LOKBOX_STARS:
		path = "search/lokbox_stars"
	case api_users_recommend_v1.RecmmendScenario_RECOMMEND_SCENARIO_SEARCH_FIND_FRIENDS:
		path = "search/find_friends"
	}

	limit := cast.ToInt64(listRequest.PageSize)
	if limit == 0 {
		limit = 10
	}

	urlStr := fmt.Sprintf("http://flippop-user-sort:8080/%s?uid=%d&limit=%d", path, userId, limit)
	if personId != nil {
		urlStr += fmt.Sprintf("&person_id=%d", *personId)
	}
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, urlStr, nil)
	resp, err := otelhttp.DefaultClient.Do(req)
	if err != nil {
		return nil, "", nil, errors.Wrapf(err, "failed to recommend, user id: %d, scenario: %s", userId, scenario)
	}
	defer resp.Body.Close()

	type Response struct {
		Data struct {
			Items []struct {
				Id                   int64   `json:"id"`
				Reason               *string `json:"reason"`
				RecommendReasonExtra *struct {
					ReasonType   string  `json:"reason_type"`
					RelatedUsers []int64 `json:"related_users"`
				} `json:"reason_extra"`
				StoryId *int64 `json:"story_id"`
			} `json:"items"`
			HasMore       bool   `json:"has_more"`
			SortRequestId string `json:"sort_request_id"`
		} `json:"data"`
		Message string `json:"message"`
		Code    int    `json:"code"`
	}

	var response Response
	bs, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(bs, &response)
	if err != nil {
		return nil, "", nil, errors.Wrapf(err, "failed to recommend, user id: %d, bs: %s", userId, string(bs))
	}

	if response.Code != 200 {
		err := fmt.Errorf("response's code is not 200, code: %d, message: %s", response.Code, response.Message)
		return nil, "", nil, errors.WithStack(err)
	}

	for _, item := range response.Data.Items {
		recommendUser := &domain_services_users_recommend.RecommendUser{
			UserInfo:        &domain_entities_users.UserSummaryEntity{ID: item.Id},
			RecommendReason: "",
		}
		if item.Reason != nil {
			recommendUser.RecommendReason = *item.Reason
		}
		if item.RecommendReasonExtra != nil {
			recommendUser.RecommendReasonExtra = &domain_services_users_recommend.RecommendReasonExtra{
				ReasonType: item.RecommendReasonExtra.ReasonType,
			}
			// Convert related user IDs to UserSummaryEntity
			for _, relatedUserId := range item.RecommendReasonExtra.RelatedUsers {
				recommendUser.RecommendReasonExtra.RelatedUsers = append(
					recommendUser.RecommendReasonExtra.RelatedUsers,
					&domain_entities_users.UserSummaryEntity{ID: relatedUserId},
				)
			}
		}
		if item.StoryId != nil {
			recommendUser.RelatedStoryId = fmt.Sprintf("%d", *item.StoryId)
		}
		recommendUsers = append(recommendUsers, recommendUser)
	}

	return recommendUsers, response.Data.SortRequestId, &api_common_v1.ListResponse{
		HasMore: response.Data.HasMore,
	}, nil
}

// RecommendUnfollowedCreatorStory implements domain_services_items_story.IRecommendService.
func (c *Client) RecommendUnfollowedCreators(ctx context.Context, userId int64) (items []struct {
	UserId  int64
	StoryId int64
}, sortRequestId string, err error) {
	if c.config.Env == conf.ENV_ENV_UT {
		return nil, "", nil
	}
	urlStr := fmt.Sprintf("http://flippop-user-sort:8080/sort?uid=%d", userId)
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, urlStr, nil)
	resp, err := otelhttp.DefaultClient.Do(req)
	if err != nil {
		return nil, "", errors.Wrapf(err, "failed to recommend, user id: %d", userId)
	}
	defer resp.Body.Close()

	type Response struct {
		Data struct {
			Items []struct {
				Id      int64 `json:"id"`
				StoryId int64 `json:"story_id"`
			} `json:"items"`
			HasMore       bool   `json:"has_more"`
			SortRequestId string `json:"sort_request_id"`
		} `json:"data"`
		Message string `json:"message"`
		Code    int    `json:"code"`
	}

	var response Response
	bs, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(bs, &response)
	if err != nil {
		return nil, "", errors.Wrapf(err, "failed to recommend, user id: %d, bs: %s", userId, string(bs))
	}

	if response.Code != 200 {
		err := fmt.Errorf("response's code is not 200, code: %d, message: %s", response.Code, response.Message)
		return nil, "", errors.WithStack(err)
	}

	for _, item := range response.Data.Items {
		items = append(items, struct {
			UserId  int64
			StoryId int64
		}{
			UserId:  item.Id,
			StoryId: item.StoryId,
		})
	}

	return items, response.Data.SortRequestId, nil
}

func (c *Client) RecommendFizzs(ctx context.Context, userId int64, loadmore bool) (fizzId []int64, sortRequestId string, hasMore bool, err error) {
	pullType := 0
	if loadmore {
		pullType = 1
	}
	urlStr := fmt.Sprintf("http://flippop-fizz-sort:8080/sort?uid=%d&pull_type=%d", userId, pullType)
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, urlStr, nil)
	resp, err := otelhttp.DefaultClient.Do(req)
	if err != nil {
		return nil, "", false, errors.Wrapf(err, "failed to recommend, user id: %d, loadmore: %t", userId, loadmore)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		err := fmt.Errorf("response's status code is not 200, status code: %d", resp.StatusCode)
		return nil, "", false, errors.WithStack(err)
	}

	// {"data":{"items":[{"id":1919334794455216128},{"id":1917894875212472320},{"id":1915779928041488384},{"id":1919649401961500672},{"id":1919639911436169216},{"id":1919639749720584192},{"id":1917227015020593152},{"id":1917084231050547200}],"has_more":true,"sort_request_id":"f569fd8f-cb3d-41be-bc21-287d0c20ddce"},"message":"success","code":200}
	type Response struct {
		Data struct {
			Items []struct {
				Id int64 `json:"id"`
			} `json:"items"`
			HasMore       bool   `json:"has_more"`
			SortRequestId string `json:"sort_request_id"`
		} `json:"data"`
		Message string `json:"message"`
		Code    int    `json:"code"`
	}

	var response Response
	bs, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(bs, &response)
	if err != nil {
		return nil, "", false, errors.Wrapf(err, "failed to recommend, user id: %d, loadmore: %t, bs: %s", userId, loadmore, string(bs))
	}

	if response.Code != 200 {
		err := fmt.Errorf("response's code is not 200, code: %d, message: %s", response.Code, response.Message)
		return nil, "", false, errors.WithStack(err)
	}

	hasMore = response.Data.HasMore

	var fizzIds []int64
	for _, item := range response.Data.Items {
		fizzIds = append(fizzIds, item.Id)
	}

	return fizzIds, response.Data.SortRequestId, hasMore, nil
}

func (c *Client) RecommendStories(ctx context.Context, userId int64, loadmore bool) (storyId []int64, sortRequestId string, hasMore bool, err error) {
	pullType := 0
	if loadmore {
		pullType = 1
	}
	urlStr := fmt.Sprintf("http://flippop-story-sort:8080/sort?uid=%d&pull_type=%d", userId, pullType)
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, urlStr, nil)
	resp, err := otelhttp.DefaultClient.Do(req)
	if err != nil {
		return nil, "", false, errors.Wrapf(err, "failed to recommend, user id: %d, loadmore: %t", userId, loadmore)
	}
	defer resp.Body.Close()

	type Response struct {
		Data struct {
			Items []struct {
				Id int64 `json:"id"`
			} `json:"items"`
			HasMore       bool   `json:"has_more"`
			SortRequestId string `json:"sort_request_id"`
		} `json:"data"`
		Message string `json:"message"`
		Code    int    `json:"code"`
	}

	var response Response
	bs, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(bs, &response)
	if err != nil {
		return nil, "", false, errors.Wrapf(err, "failed to recommend, user id: %d, loadmore: %t, bs: %s", userId, loadmore, string(bs))
	}

	if response.Code != 200 {
		err := fmt.Errorf("response's code is not 200, code: %d, message: %s", response.Code, response.Message)
		return nil, "", false, errors.WithStack(err)
	}

	hasMore = response.Data.HasMore

	storyIds := make([]int64, 0, len(response.Data.Items))
	for _, item := range response.Data.Items {
		storyIds = append(storyIds, item.Id)
	}

	return storyIds, response.Data.SortRequestId, hasMore, nil
}

func (c *Client) RecommendTrendingPortals(ctx context.Context, userId int64, loadmore bool, pageSize int32) (portalId []int64, sortRequestId string, hasMore bool, err error) {
	pullType := 0
	if loadmore {
		pullType = 1
	}
	u := &url.URL{
		Scheme: "http",
		Host:   "flippop-story-sort:8080",
		Path:   "/trending_portals",
	}

	q := url.Values{
		"uid":        {fmt.Sprintf("%d", userId)},
		"pull_type":  {fmt.Sprintf("%d", pullType)},
		"item_count": {fmt.Sprintf("%d", pageSize)},
	}
	u.RawQuery = q.Encode()
	urlStr := u.String()
	req, _ := http.NewRequestWithContext(ctx, http.MethodGet, urlStr, nil)
	resp, err := otelhttp.DefaultClient.Do(req)
	if err != nil {
		return nil, "", false, errors.Wrapf(err, "failed to recommend trending portals, user id: %d, loadmore: %t", userId, loadmore)
	}
	defer resp.Body.Close()

	type Response struct {
		Data struct {
			Items []struct {
				Id int64 `json:"portal_id"`
			} `json:"items"`
			HasMore       bool   `json:"has_more"`
			SortRequestId string `json:"sort_request_id"`
		} `json:"data"`
		Message string `json:"message"`
		Code    int    `json:"code"`
	}

	var response Response
	bs, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(bs, &response)
	if err != nil {
		return nil, "", false, errors.Wrapf(err, "failed to recommend trending portals, user id: %d, loadmore: %t, bs: %s", userId, loadmore, string(bs))
	}

	if response.Code != 200 {
		err := fmt.Errorf("response's code is not 200, code: %d, message: %s", response.Code, response.Message)
		return nil, "", false, errors.WithStack(err)
	}

	hasMore = response.Data.HasMore

	portalIds := make([]int64, 0, len(response.Data.Items))
	for _, item := range response.Data.Items {
		portalIds = append(portalIds, item.Id)
	}

	return portalIds, response.Data.SortRequestId, hasMore, nil
}
