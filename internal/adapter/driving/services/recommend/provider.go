package adapter_driving_services_recommend

import (
	domain_services_fizz "boson/internal/domain/services/fizz"
	domain_services_items_portal "boson/internal/domain/services/items/portal"
	domain_services_items_story "boson/internal/domain/services/items/story"
	domain_services_users_recommend "boson/internal/domain/services/users/recommend"

	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(
	wire.Bind(new(domain_services_items_story.IRecommendService), new(*Client)),
	wire.Bind(new(domain_services_fizz.IRecommendFizzsClient), new(*Client)),
	wire.Bind(new(domain_services_users_recommend.IRecommendUsersClient), new(*Client)),
	wire.Bind(new(domain_services_items_portal.IRecommendPortalsService), new(*Client)),
	NewClient,
)
