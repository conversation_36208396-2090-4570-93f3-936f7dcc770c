package domain_interfaces

import (
	api_search_v1 "boson/api/search/v1"
	domain_entities_search "boson/internal/domain/entities/search"
	"context"
)

type ISearchHistoryRepository interface {
	// GetSearchHistory 获取用户搜索历史
	GetSearchHistory(ctx context.Context, userID int64, count int32) ([]*domain_entities_search.SearchHistory, error)

	// DeleteSearchHistory 删除用户搜索历史
	DeleteSearchHistory(ctx context.Context, userID int64, searchIDs []int64) error

	// CreateSearchHistory 创建搜索历史记录
	CreateSearchHistory(ctx context.Context, userID int64, searchText string, searchType api_search_v1.SearchType) (int64, error)

	// BatchCreateSearchHistory 批量创建搜索历史记录
	BatchCreateSearchHistory(ctx context.Context, userID int64, items []struct {
		SearchText string
		SearchType api_search_v1.SearchType
	}) ([]int64, error)
}
