package domain_entities_push

import (
	"time"
)

// PushInterceptionEntity 推送拦截实体
type PushInterceptionEntity struct {
	ID        int64     `json:"id"`
	UserID    int64     `json:"user_id"`
	PushType  string    `json:"push_type"`
	PushValue string    `json:"push_value"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// CreatePushInterceptionAttr 创建推送拦截记录的属性
type CreatePushInterceptionAttr struct {
	ID        int64
	UserID    int64
	PushType  string
	PushValue string
	CreatedAt time.Time
}

// CheckPushInterceptionAttr 检查推送拦截的属性
type CheckPushInterceptionAttr struct {
	UserID      int64
	PushType    string
	PushValue   string
	WithinHours int // 检查多少小时内是否有相同推送
}
