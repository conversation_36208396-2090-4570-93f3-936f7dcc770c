package domain_entities_items

import (
	domain_entities_resource "boson/internal/domain/entities/resource"
)

type Area struct {
	X      float64
	Y      float64
	Width  float64
	Height float64
}
type PinEmojiResource struct {
	GeneratedEmojiResource *domain_entities_resource.Resource `json:"generated_emoji_resource"`
	DefaultEmoji           string                             `json:"default_emoji"`
	Area                   *Area                              `json:"area"`
}

type StoryPinConfig struct {
	BackgroundImage   *domain_entities_resource.Resource `json:"background_image"`
	PinEmojiResources []*PinEmojiResource                `json:"pin_emoji_resources"`
	Caption           *AttachedText                      `json:"caption"`
}

type StoryPinContext struct {
	IsUnlocked         bool                               `json:"is_unlocked"`
	SuccessCostSeconds uint32                             `json:"success_cost_seconds"`
	FailedImage        *domain_entities_resource.Resource `json:"failed_image"`
}
