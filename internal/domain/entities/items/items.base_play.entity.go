package domain_entities_items

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"encoding/json"
)

type StoryBasePlayContext struct {
	CurrentNodeID  string `json:"current_node_id"`
	CurrentNodeIdx uint32 `json:"current_node_idx"`
	IsUnlocked     bool   `json:"is_unlocked"`
}

type AttachedText = api_items_story_types_v1.AttachmentText

type StotyPlayBaseConfigNode struct {
	ID            string                                     `json:"id"`
	ResourceType  api_items_story_types_v1.ResourceType      `json:"-"`
	ResourceKey   string                                     `json:"resource_key"`
	CoverImageKey domain_entities_resource.ImageResourcePath `json:"cover_image_key"`
	AttachedTexts []*AttachedText                            `json:"attached_texts,omitempty"`
}

func (c *StotyPlayBaseConfigNode) GetResourceURL() string {
	switch c.ResourceType {
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
		videoPath := domain_entities_resource.VideoResourcePath(c.<PERSON>)
		return videoPath.URL()
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
		imagePath := domain_entities_resource.ImageResourcePath(c.ResourceKey)
		return imagePath.ItemPosterInSummary()
	default:
		return ""
	}
}

type StoryPlayBasePlayConfig struct {
	Nodes []*StotyPlayBaseConfigNode `json:"nodes"`
}

func (c *StotyPlayBaseConfigNode) MarshalJSON() ([]byte, error) {
	type Alias StotyPlayBaseConfigNode
	return json.Marshal(&struct {
		*Alias
		ResourceType string `json:"resource_type"`
	}{
		Alias:        (*Alias)(c),
		ResourceType: c.ResourceType.String(),
	})
}

func (c *StotyPlayBaseConfigNode) UnmarshalJSON(data []byte) error {
	type Alias StotyPlayBaseConfigNode
	aux := &struct {
		*Alias
		ResourceType string `json:"resource_type"`
	}{
		Alias:        (*Alias)(c),
		ResourceType: c.ResourceType.String(),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	switch aux.ResourceType {
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE.String():
		c.ResourceType = api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE
	case api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO.String():
		c.ResourceType = api_items_story_types_v1.ResourceType_RESOURCE_TYPE_VIDEO
	default:
		c.ResourceType = api_items_story_types_v1.ResourceType_RESOURCE_TYPE_UNSPECIFIED
	}

	return nil
}
