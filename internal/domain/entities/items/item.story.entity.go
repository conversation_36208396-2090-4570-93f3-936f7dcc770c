package domain_entities_items

import (
	api_items_story_activity_types_v1 "boson/api/items/story/activity/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	"time"

	"github.com/samber/lo"
)

type Condition api_items_story_types_v1.Condition

type CommonConfig struct {
	Cover            *domain_entities_resource.CoverImage `json:"cover,omitempty"`
	Resource         *Resource                            `json:"resource,omitempty"`
	CoverCaptions    []*AttachedText                      `json:"cover_captions,omitempty"`
	ResourceCaptions []*AttachedText                      `json:"resource_captions,omitempty"`
	MaxTryCount      uint32                               `json:"max_try_count"`
	Condition        *Condition                           `json:"condition"`
}
type StoryVersion = int32

const (
	StoryVersion_V1 = 1
	StoryVersion_V2 = 2
)

type StoryStats struct {
	// 特殊的 emoji 统计
	ReactionEmojiStats map[string]uint32
	// 分享统计
	ShareStat uint32
}

type StoryCoverImage struct {
	Path   domain_entities_resource.ImageResourcePath
	Width  int
	Height int
	Type   domain_entities_resource.CoverType
}

type StorySummary struct {
	Id     int64
	Author *domain_entities_users.UserSummaryEntity

	StoryCoverImage *StoryCoverImage

	PlayType        api_items_story_types_v1.StoryPlayType
	SubPlayType     api_items_story_types_v1.ExchangeImagePlayMode
	Status          api_items_story_types_v1.StoryStatus
	PrivacySettings *StoryPrivacySettings

	PlayTurtleSoupExample     *StoryPlayTurtleSoupExample
	PlayTurtleSoupMassExample *StoryPlayTurtleSoupMassExample
	PlayExchangeImageExample  *StoryPlayExchangeImageExample
	PlayUnmuteExample         *StoryPlayUnmuteExample
	PlayBasePlayExample       *StoryPlayBasePlayExample
	PlayNowShotExample        *StoryPlayNowShotExample
	PlayRoastedExample        *StoryPlayRoastedExample
	PlayCapsuleExample        *StoryPlayCapsuleExample

	ExchangeImagePlayConfig *ExchanegImagePlayConfig
	TurtleSoupPlayConfig    *StoryPlayTurtleSoupConfig
	UnmutePlayConfig        *StoryPlayUnmuteConfig
	BasePlayConfig          *StoryPlayBasePlayConfig
	NowShotConfig           *StoryPlayNowShotConfig
	RoastedPlayConfig       *StoryPlayRoastedConfig
	WassupPlayConfig        *StoryPlayWassupConfig
	CapsulePlayConfig       *StoryPlayCapsuleConfig
	HidePlayConfig          *StoryPlayHideConfig
	ChatProxyPlayConfig     *StoryPlayChatProxyConfig
	WhoPlayConfig           *StoryPlayWhoConfig
	HauntPlayConfig         *HauntStoryConfig
	PinPlayConfig           *StoryPinConfig

	LoginUserMadeReactions []*StoryReaction
	Stats                  *StoryStats

	UnlockedUsersInfo *UnlockedUsersInfo

	CreatedAt time.Time
	UpdatedAt time.Time

	Version StoryVersion

	HasUnlocked bool

	// 如果是推荐出来的 story，则会有此值，用于客户端埋点
	SortRequestId string
}
type UnlockedUsersInfo struct {
	Users []*domain_entities_users.UserSummaryEntity
	Total uint32
}

func (s *StorySummary) IsLoginUserMadeReactionMade(emoji string) bool {
	return lo.ContainsBy(s.LoginUserMadeReactions, func(reaction *StoryReaction) bool {
		return reaction.EmojiStr == emoji
	})
}

type StoryExampleCommonInfo = api_items_story_types_v1.ExampleCommonInfo
type StoryPlayTurtleSoupExample = api_items_story_types_v1.StoryPlayTurtleSoupExample
type StoryPlayTurtleSoupMassExample = api_items_story_types_v1.StoryPlayTurtleSoupMassExample
type StoryPlayExchangeImageExample = api_items_story_types_v1.StoryPlayExchangeImageExample
type StoryPlayBasePlayExample = api_items_story_types_v1.StoryPlayBasePlayExample
type StoryPlayNowShotExample = api_items_story_types_v1.StoryPlayNowShotExample
type StoryPlayNowShotExample_Case = api_items_story_types_v1.StoryPlayNowShotExample_Case
type StoryPlayUnmuteExample = api_items_story_types_v1.StoryPlayUnmuteExample
type StoryPlayRoastedExample = api_items_story_types_v1.StoryPlayRoastedExample
type StoryPlayCapsuleExample = api_items_story_types_v1.StoryPlayCapsuleExample
type StoryPlayUnmuteExample_Case = api_items_story_types_v1.StoryPlayUnmuteExample_Case
type StoryPlayExchangeImageExample_Case = api_items_story_types_v1.StoryPlayExchangeImageExample_Case
type StoryPlayTurtleSoupExample_Case = api_items_story_types_v1.StoryPlayTurtleSoupExample_Case
type StoryPlayTurtleSoupMassExample_Case = api_items_story_types_v1.StoryPlayTurtleSoupMassExample_Case

type SubmittedResource struct {
	Resource      *domain_entities_resource.Resource `json:"resource"`
	UpdatedAtUnix int64                              `json:"updated_at_unix"`
}

type StoryBaseContext struct {
	UserSubmittedResources []*SubmittedResource `json:"user_submitted_resources"`
	IsPostMoment           bool                 `json:"is_post_moment"`
}

type StoryDetail struct {
	Summary *StorySummary

	ExchangeImagePlayContext *ExchangeImagePlayContext
	TurtleSoupPlayContext    *StoryPlayTurtleSoupContext
	UnmutePlayContext        *StoryPlayUnmuteContext
	BasePlayContext          *StoryBasePlayContext
	NowShotPlayContext       *StoryPlayNowShotContext
	RoastedPlayContext       *StoryPlayRoastedContext
	WassupPlayContext        *StoryPlayWassupContext
	CapsulePlayContext       *StoryPlayCapsuleContext
	HidePlayContext          *StoryPlayHideContext
	ChatproxyContext         *StoryPlayChatProxyContext
	WhoPlayContext           *StoryPlayWhoContext
	HauntPlayContext         *HauntStoryContext
	PinPlayContext           *StoryPinContext

	// 如果是推荐出来的 story，则会有此值，用于客户端埋点
	SortRequestId string

	// 这是个临时字段，由于开发时间太紧张了，需要在所有的
	// unlock 接口内返回加入的 portal id 字段，无法通过
	// 接口返回，所以临时加了这个字段，后续需要删除 @Larry
	// 此值目前仅会在 unlock 后得到注入，其他地方不会注入
	JoinedPortalId int64

	HauntShowInfo *ShowInfo
}

func (s *StoryDetail) GetStoryNextResourceCoverPath() domain_entities_resource.ImageResourcePath {
	switch s.Summary.PlayType {
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
		switch s.Summary.ExchangeImagePlayConfig.PlayMode {
		case api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_MASSE:
			// 如果是马赛克玩法，结束图就是当前 node 的封面
			return s.Summary.ExchangeImagePlayConfig.Nodes[0].GetCoverImagePath()
		case api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_LINEAR:
			// 如果是线性玩法，如果有下一轮，则是下一轮的封面，否则就是当前的封面
			if s.ExchangeImagePlayContext.CurrentNodeIdx < len(s.Summary.ExchangeImagePlayConfig.Nodes)-1 {
				return s.Summary.ExchangeImagePlayConfig.Nodes[s.ExchangeImagePlayContext.CurrentNodeIdx+1].GetCoverImagePath()
			}
			return s.Summary.ExchangeImagePlayConfig.Nodes[0].GetCoverImagePath()
		}
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
		switch s.Summary.TurtleSoupPlayConfig.EndResourceType {
		case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
			return *s.Summary.TurtleSoupPlayConfig.EndResourceImageKey
		case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
			return *s.Summary.TurtleSoupPlayConfig.EndThumbnailKey
		}
	}
	return ""
}

func (s *StorySummary) GetStoryCover() (*domain_entities_resource.CoverImage, error) {

	// TODO(Ricky): 目前s.StoryCoverImage还未注入
	switch s.Version {
	case StoryVersion_V1:

	case StoryVersion_V2:
		switch s.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WHO:
			return &domain_entities_resource.CoverImage{
				CoverType:    api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:  string(s.WhoPlayConfig.CoverResource.GetCoverImageObjectKey()),
				ThumbnailKey: s.WhoPlayConfig.CoverResource.GetCoverImageObjectKey(),
				Height:       s.WhoPlayConfig.CoverResource.GetCoverImageSize().Height,
				Width:        s.WhoPlayConfig.CoverResource.GetCoverImageSize().Width,
			}, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HIDE:
			return &domain_entities_resource.CoverImage{
				CoverType:    api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:  string(s.HidePlayConfig.BackgroundImage.GetCoverImageObjectKey()),
				ThumbnailKey: s.HidePlayConfig.BackgroundImage.GetCoverImageObjectKey(),
				Height:       s.HidePlayConfig.BackgroundImage.GetCoverImageSize().Height,
				Width:        s.HidePlayConfig.BackgroundImage.GetCoverImageSize().Width,
			}, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
			return &domain_entities_resource.CoverImage{
				CoverType:    api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:  string(s.WassupPlayConfig.CoverResource.GetCoverImageObjectKey()),
				ThumbnailKey: s.WassupPlayConfig.CoverResource.GetCoverImageObjectKey(),
				Height:       s.WassupPlayConfig.CoverResource.GetCoverImageSize().Height,
				Width:        s.WassupPlayConfig.CoverResource.GetCoverImageSize().Width,
			}, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
			return &domain_entities_resource.CoverImage{
				CoverType:    api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:  string(s.ChatProxyPlayConfig.CoverResource.GetCoverImageObjectKey()),
				ThumbnailKey: s.ChatProxyPlayConfig.CoverResource.GetCoverImageObjectKey(),
				Height:       s.ChatProxyPlayConfig.CoverResource.GetCoverImageSize().Height,
				Width:        s.ChatProxyPlayConfig.CoverResource.GetCoverImageSize().Width,
			}, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			return s.ExchangeImagePlayConfig.CommonConfig.Cover, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
			return s.TurtleSoupPlayConfig.CommonConfig.Cover, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			return s.UnmutePlayConfig.CommonConfig.Cover, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			return s.NowShotConfig.CommonConfig.Cover, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
			return &domain_entities_resource.CoverImage{
				CoverType:    api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:  string(s.BasePlayConfig.Nodes[0].CoverImageKey),
				ThumbnailKey: s.BasePlayConfig.Nodes[0].CoverImageKey,
			}, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
			return &domain_entities_resource.CoverImage{
				CoverType:    api_items_story_types_v1.ResourceType_RESOURCE_TYPE_IMAGE,
				ResourceKey:  string(s.RoastedPlayConfig.Cover.GetCoverImageObjectKey()),
				ThumbnailKey: s.RoastedPlayConfig.Cover.GetCoverImageObjectKey(),
				Height:       s.RoastedPlayConfig.Cover.GetCoverImageSize().Height,
				Width:        s.RoastedPlayConfig.Cover.GetCoverImageSize().Width,
			}, nil
		}
	}
	return nil, nil
}

func (s *StorySummary) GetStoryPlayCaption() (*AttachedText, error) {
	switch s.Version {
	case StoryVersion_V1:

	case StoryVersion_V2:
		switch s.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			return s.ExchangeImagePlayConfig.CommonConfig.Condition.Hint, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
			return s.TurtleSoupPlayConfig.CommonConfig.Condition.Hint, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			return s.UnmutePlayConfig.CommonConfig.Condition.Hint, nil
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			return s.NowShotConfig.CommonConfig.Condition.Hint, nil
		}
	}
	return nil, nil
}

func (s *StorySummary) GetStoryCoverPath() domain_entities_resource.ImageResourcePath {
	if s.StoryCoverImage != nil && s.StoryCoverImage.Path != "" {
		return s.StoryCoverImage.Path
	}
	return s.getStoryCoverPathFromConfig()
}
func (s *StorySummary) getStoryCoverPathFromConfig() domain_entities_resource.ImageResourcePath {
	switch s.Version {
	case StoryVersion_V1:
		switch s.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			if len(s.ExchangeImagePlayConfig.Nodes) > 0 {
				switch s.ExchangeImagePlayConfig.Nodes[0].ResourceType {
				case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE:
					return *s.ExchangeImagePlayConfig.Nodes[0].ImageKey
				case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO:
					return *s.ExchangeImagePlayConfig.Nodes[0].ThumbnailObjectKey
				}
			} else {
				if api_items_story_types_v1.ResourceType(s.ExchangeImagePlayConfig.CommonConfig.Cover.CoverType) == api_items_story_types_v1.ResourceType_RESOURCE_TYPE_GRADIENT {
					return domain_entities_resource.EmptyImage
				}
				return s.ExchangeImagePlayConfig.CommonConfig.Cover.ThumbnailKey
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP,
			api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP_MASS:
			switch s.TurtleSoupPlayConfig.ResourceType {
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_IMAGE:
				return *s.TurtleSoupPlayConfig.ResourceImageKey
			case api_items_story_types_v1.StoryPlayTurtleSoupConfig_RESOURCE_TYPE_VIDEO:
				return *s.TurtleSoupPlayConfig.ThumbnailKey
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
			if len(s.BasePlayConfig.Nodes) > 0 {
				return s.BasePlayConfig.Nodes[0].CoverImageKey
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			switch s.UnmutePlayConfig.ResourceType {
			case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
				return *s.UnmutePlayConfig.ThumbnailKey
			case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
				return *s.UnmutePlayConfig.ResourceImageKey
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			switch s.NowShotConfig.ResourceType {
			case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
				return *s.NowShotConfig.ResourceImageKey
			case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
				return *s.NowShotConfig.ThumbnailKey
			}
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE:
			return s.CapsulePlayConfig.CoverImage
		}
	case StoryVersion_V2:
		switch s.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			return s.ExchangeImagePlayConfig.CommonConfig.Cover.GetThumbnail()
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
			return s.TurtleSoupPlayConfig.CommonConfig.Cover.GetThumbnail()
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
			return s.UnmutePlayConfig.CommonConfig.Cover.GetThumbnail()
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			return s.NowShotConfig.CommonConfig.Cover.GetThumbnail()
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
			if len(s.BasePlayConfig.Nodes) > 0 {
				return s.BasePlayConfig.Nodes[0].CoverImageKey
			}
		}
	}
	return ""
}

type ConsumptionStatus api_items_story_activity_types_v1.ConsumptionStatus
