package domain_entities_items

import (
	"time"
)

// Activity type constants
const (
	ActivityType_STORY_LIKE    = "ACTIVITY_TYPE_STORY_LIKE"
	ActivityType_STORY_COMMENT = "ACTIVITY_TYPE_STORY_COMMENT"
)

type StoryReaction struct {
	StoryId       int64
	StoryAuthorId int64
	UserId        int64
	EmojiStr      string
	Comment       string

	CreatedAt time.Time
}

type ActivityItem struct {
	ID                 string
	ActorUserID        int64
	StoryID            int64
	ActivityType       string
	CreatedAtTimestamp uint32

	// Optional details
	Emoji   string
	Comment string
}
