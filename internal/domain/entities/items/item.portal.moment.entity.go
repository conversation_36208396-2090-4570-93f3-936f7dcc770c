package domain_entities_items

import (
	domain_entities_boo "boson/internal/domain/entities/boo"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"time"

	api_items_portal_moment_types_v1 "boson/api/items/portal/moments/types/v1"

	domain_entities_users "boson/internal/domain/entities/users"
)

type MomentRelation struct {
	UserId       string
	RelationType api_items_portal_moment_types_v1.RelationType
}

type MomentStat = api_items_portal_moment_types_v1.Stat

type MomentExtraInfo struct {
	AttachmentTexts []*AttachedText                    `json:"attachment_texts"`
	Resource        *domain_entities_resource.Resource `json:"resource"`
	Avatars         []*domain_entities_boo.Avatar      `json:"-"`
	AvatarIds       []int64                            `json:"avatar_ids"`
}

type MomentViewer struct {
	User      *domain_entities_users.UserSummaryEntity
	Relations []*MomentRelation
}

type Moment struct {
	Id       int64
	PortalId int64

	Creator   *domain_entities_users.UserSummaryEntity
	Type      api_items_portal_moment_types_v1.MomentType
	CreatedAt time.Time
	Status    api_items_portal_moment_types_v1.Status

	ExtraInfo *MomentExtraInfo

	CreateType api_items_portal_moment_types_v1.MomentCreateType

	Relations []*MomentRelation
	Stat      *MomentStat

	// 最新的查看者
	LatestViewer *MomentViewer

	HauntShowInfo *ShowInfo
}
