package domain_entities_items

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"fmt"
	"strconv"
)

type Point struct {
	X uint32
	Y uint32
}
type StickerTransform_Location struct {
	X string `json:"x"`
	Y string `json:"y"`
}
type StickerTransform_Size struct {
	Width  string `json:"width"`
	Height string `json:"height"`
}
type StickerTransform struct {
	Location *StickerTransform_Location `json:"location"`
	Size     *StickerTransform_Size     `json:"size"`
}

type StickerTriggerType = api_items_story_types_v1.HideStickerTriggerType

type StickerWithTriggerTypeContinuousClickData struct {
	NeedClickCount uint32         `json:"need_click_count"`
	Stickers       []*HideSticker `json:"stickers"`
}
type StickerWithClickLocationAggregation struct {
	Stickers []*HideSticker             `json:"stickers"`
	Location *StickerTransform_Location `json:"location"`
}

func (d *StickerWithTriggerTypeContinuousClickData) GetStickersWithClickLocationAggregation() []*StickerWithClickLocationAggregation {
	if d.Stickers == nil || len(d.Stickers) == 0 {
		return []*StickerWithClickLocationAggregation{}
	}

	// 定义网格大小，将归一化坐标(0-1)划分成10x10的网格
	const gridSize = 0.1

	// 用于存储每个网格区域的贴纸
	gridMap := make(map[string][]*HideSticker)

	// 遍历所有贴纸，分配到对应的网格区域
	for _, sticker := range d.Stickers {
		if sticker.Transform == nil || sticker.Transform.Location == nil {
			continue
		}

		// 解析坐标
		x, err := strconv.ParseFloat(sticker.Transform.Location.X, 64)
		if err != nil {
			continue
		}
		y, err := strconv.ParseFloat(sticker.Transform.Location.Y, 64)
		if err != nil {
			continue
		}

		// 确保坐标在0-1范围内
		if x < 0 || x > 1 || y < 0 || y > 1 {
			continue
		}

		// 计算网格索引
		gridX := int(x / gridSize)
		gridY := int(y / gridSize)

		// 防止边界情况（当坐标正好为1时）
		if gridX >= int(1/gridSize) {
			gridX = int(1/gridSize) - 1
		}
		if gridY >= int(1/gridSize) {
			gridY = int(1/gridSize) - 1
		}

		// 生成网格键
		gridKey := fmt.Sprintf("%d_%d", gridX, gridY)

		// 将贴纸添加到对应的网格
		gridMap[gridKey] = append(gridMap[gridKey], sticker)
	}

	// 生成聚合结果
	var result []*StickerWithClickLocationAggregation
	for gridKey, stickers := range gridMap {
		if len(stickers) == 0 {
			continue
		}

		// 计算网格中心坐标
		var gridX, gridY int
		fmt.Sscanf(gridKey, "%d_%d", &gridX, &gridY)

		// 网格中心坐标
		centerX := (float64(gridX) + 0.5) * gridSize
		centerY := (float64(gridY) + 0.5) * gridSize

		// 创建聚合结果
		aggregation := &StickerWithClickLocationAggregation{
			Stickers: stickers,
			Location: &StickerTransform_Location{
				X: fmt.Sprintf("%.3f", centerX),
				Y: fmt.Sprintf("%.3f", centerY),
			},
		}

		result = append(result, aggregation)
	}

	return result
}

type StickerWithTriggerTypeDragData struct {
	CutObjects []*StickerWithTriggerTypeDragData_CutObject `json:"cut_objects"`
}

type StickerWithTriggerTypeDragData_CutObject struct {
	MaskImageKey domain_entities_resource.ImageResourcePath `json:"mask_image_key"`
	Sticker      *HideSticker                               `json:"sticker"`
}

type StickerWithTriggerTypeLikeData struct {
	Stickers []*HideSticker `json:"stickers"`
}

type StickerWithTriggerTypeShakeData struct {
	NeedShakeCount           uint32         `json:"need_shake_count"`
	NeedShakeDurationSeconds uint32         `json:"need_shake_duration_seconds"`
	Stickers                 []*HideSticker `json:"stickers"`
}

type StickerWithTriggerTypeLongPressData struct {
	Stickers             []*HideSticker `json:"stickers"`
	PressDurationSeconds uint32         `json:"press_duration_seconds"`
}

type StickerTriggerData struct {
	StickerTriggerType  StickerTriggerType                         `json:"sticker_trigger_type"`
	ContinuousClickData *StickerWithTriggerTypeContinuousClickData `json:"continuous_click_data"`
	DragData            *StickerWithTriggerTypeDragData            `json:"drag_data"`
	LikeData            *StickerWithTriggerTypeLikeData            `json:"like_data"`
	ShakeData           *StickerWithTriggerTypeShakeData           `json:"shake_data"`
	LongPressData       *StickerWithTriggerTypeLongPressData       `json:"long_press_data"`
}

type HideSticker struct {
	Id           string                             `json:"id"`
	FromStoryId  int64                              `json:"-"`
	FromAvatarId int64                              `json:"-"`
	Collected    bool                               `json:"-"` // 是否被用户收藏，不在 extra 内存储
	Resource     *domain_entities_resource.Resource `json:"resource"`
	Transform    *StickerTransform                  `json:"transform"`
}

type StoryPlayHideConfig struct {
	BackgroundImage *domain_entities_resource.Resource         `json:"background_image"`
	TriggerData     map[StickerTriggerType]*StickerTriggerData `json:"trigger_data"`
	AttachmentTexts []*AttachedText                            `json:"attachment_texts"`
}

func (c *StoryPlayHideConfig) GetStickers() []*HideSticker {
	stickers := []*HideSticker{}
	for _, data := range c.TriggerData {
		switch data.StickerTriggerType {
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK:
			if data.ContinuousClickData == nil {
				continue
			}
			stickers = append(stickers, data.ContinuousClickData.Stickers...)
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_DRAG:
			if data.DragData == nil {
				continue
			}
			for _, cutObject := range data.DragData.CutObjects {
				stickers = append(stickers, cutObject.Sticker)
			}
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LIKE:
			if data.LikeData == nil {
				continue
			}
			stickers = append(stickers, data.LikeData.Stickers...)
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_SHAKE:
			if data.ShakeData == nil {
				continue
			}
			stickers = append(stickers, data.ShakeData.Stickers...)
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LONG_PRESS:
			if data.LongPressData == nil {
				continue
			}
			stickers = append(stickers, data.LongPressData.Stickers...)
		}
	}
	return stickers
}

type UnlockSticker struct {
	CreatedAt uint32       `json:"created_at"`
	Sticker   *HideSticker `json:"sticker"`
}

type StoryPlayHideContext struct {
	IsConsumed       bool             `json:"is_consumed"`
	UnlockedStickers []*UnlockSticker `json:"unlocked_stickers"`
}
