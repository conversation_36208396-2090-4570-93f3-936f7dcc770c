package domain_entities_items

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
)

type ChatProxyQuestion = api_items_story_types_v1.ChatProxyQuestion

type ChatproxyMsg struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}
type StoryPlayChatProxyContext struct {
	StoryBaseContext
	Unlocked    bool            `json:"unlocked"`
	HistoryMsgs []*ChatproxyMsg `json:"history_msgs"`
}

type StoryPlayChatProxyCaption = api_items_story_types_v1.StoryPlayChatProxyCaption
type StoryPlayChatProxyGreeting = api_items_story_types_v1.StoryPlayChatProxyGreeting

type StoryPlayChatProxyConfig struct {
	Caption                       *StoryPlayChatProxyCaption         `json:"caption"`
	Greeting                      *StoryPlayChatProxyGreeting        `json:"greeting"`
	Topics                        []string                           `json:"topics"`
	UnlockResource                *domain_entities_resource.Resource `json:"unlock_resource"`
	CoverResource                 *domain_entities_resource.Resource `json:"cover_resource"`
	CoverAttachmentTexts          []*AttachedText                    `json:"cover_attachment_texts"`
	UnlockResourceAttachmentTexts []*AttachedText                    `json:"unlock_resource_attachment_texts"`
}
