package domain_entities_items

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_boo "boson/internal/domain/entities/boo"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
)

type HauntQuestion struct {
	Title       string
	Description string
	IsRequired  bool
}

type HauntBoo struct {
	Id                   int64                                      `json:"id,string"`
	CreatorId            int64                                      `json:"creator_id,string"`
	Creator              *domain_entities_users.UserSummaryEntity   `json:"-"`
	Avatar               *domain_entities_boo.Avatar                `json:"-"`
	AvatarId             int64                                      `json:"avatar_id,string"`
	VideoKey             domain_entities_resource.VideoResourcePath `json:"video_key"`
	QuestionsWithAnswers []*HauntQuestionWithAnswer                 `json:"questions_with_answers"`
	Hint                 string                                     `json:"hint"`
	StoryCreatorBoo      bool                                       `json:"story_creator_boo"`
}

type HauntQuestionWithAnswer struct {
	Question *HauntQuestion
	Answer   string
}

type HauntBooShowInfo struct {
	BooShowInfos []*ShowInfo                                    `json:"boo_show_infos"`
	LatestShowAt uint32                                         `json:"latest_show_at"`
	Scene        api_items_story_types_v1.HauntBooShowInfoSence `json:"scene,string"`
}

type ShowInfo struct {
	CreatorBoo   *HauntBoo    `json:"-"`
	CreatorBooId int64        `json:"creator_boo_id"`
	AssistBoos   []*HauntBoo  `json:"-"`
	AssistBooIds []int64      `json:"assist_boo_ids"`
	FromStoryId  int64        `json:"from_story_id"`
	FromStory    *StoryDetail `json:"-"`
	ExpireAt     uint32       `json:"expire_at"`
	MaxShowCount uint32       `json:"max_show_count"`
	ShowCount    uint32       `json:"show_count"`
	LatestShowAt uint32       `json:"latest_show_at"`
}
