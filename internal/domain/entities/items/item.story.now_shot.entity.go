package domain_entities_items

import (
	"encoding/json"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
)

type StoryPlayNowShotContext struct {
	StoryBaseContext
	ConsumeStatus       api_items_story_types_v1.StoryPlayNowShotContext_ConsumeStatus `json:"-"`
	StartTime           uint32                                                         `json:"start_time"`
	UserSubmitImageKeys []domain_entities_resource.ImageResourcePath                   `json:"user_submit_image_keys"`
	UserSubmitVideoKeys []domain_entities_resource.VideoResourcePath                   `json:"user_submit_video_keys"`
	TTL                 *uint32                                                        `json:"ttl,omitempty"`
}

func (c *StoryPlayNowShotContext) MarshalJSON() ([]byte, error) {
	type Alias StoryPlayNowShotContext
	return json.Marshal(&struct {
		*Alias
		ConsumeStatus string `json:"consume_status"`
	}{
		Alias:         (*<PERSON><PERSON>)(c),
		ConsumeStatus: c.ConsumeStatus.String(),
	})
}

func (c *StoryPlayNowShotContext) UnmarshalJSON(data []byte) error {
	type Alias StoryPlayNowShotContext
	aux := &struct {
		*Alias
		ConsumeStatus string `json:"consume_status"`
	}{
		Alias: (*Alias)(c),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	switch aux.ConsumeStatus {
	case api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNSPECIFIED.String():
		c.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNSPECIFIED
	case api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_LOCK.String():
		c.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_LOCK
	case api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK.String():
		c.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK
	case api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_FAILED.String():
		c.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_FAILED
	default:
		c.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNSPECIFIED
	}

	return nil
}

type StoryPlayNowShotConfig struct {
	Caption string `json:"caption,omitempty"`
	TTL     uint32 `json:"ttl"`

	ResourceType     api_items_story_types_v1.StoryPlayNowShotConfig_ResourceType `json:"-"`
	ResourceVideoKey *domain_entities_resource.VideoResourcePath                  `json:"resource_video_key,omitempty"`
	ResourceImageKey *domain_entities_resource.ImageResourcePath                  `json:"resource_image_key,omitempty"`
	ThumbnailKey     *domain_entities_resource.ImageResourcePath                  `json:"thumbnail_key,omitempty"`

	EndResourceType     api_items_story_types_v1.StoryPlayNowShotConfig_ResourceType `json:"-"`
	EndResourceVideoKey *domain_entities_resource.VideoResourcePath                  `json:"end_resource_video_key,omitempty"`
	EndResourceImageKey *domain_entities_resource.ImageResourcePath                  `json:"end_resource_image_key,omitempty"`
	EndThumbnailKey     *domain_entities_resource.ImageResourcePath                  `json:"end_thumbnail_key,omitempty"`

	CommonConfig *CommonConfig `json:"common_config,omitempty"`
}

func (c *StoryPlayNowShotConfig) MarshalJSON() ([]byte, error) {
	type Alias StoryPlayNowShotConfig
	return json.Marshal(&struct {
		*Alias
		ResourceType    string `json:"resource_type"`
		EndResourceType string `json:"end_resource_type"`
	}{
		Alias:           (*Alias)(c),
		ResourceType:    c.ResourceType.String(),
		EndResourceType: c.EndResourceType.String(),
	})
}

func (c *StoryPlayNowShotConfig) UnmarshalJSON(data []byte) error {
	type Alias StoryPlayNowShotConfig
	aux := &struct {
		*Alias
		ResourceType    string `json:"resource_type"`
		EndResourceType string `json:"end_resource_type"`
	}{
		Alias: (*Alias)(c),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	switch aux.ResourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED.String():
		c.ResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE.String():
		c.ResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO.String():
		c.ResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO
	default:
		c.ResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED
	}

	switch aux.EndResourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED.String():
		c.EndResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE.String():
		c.EndResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO.String():
		c.EndResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO
	default:
		c.EndResourceType = api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_UNSPECIFIED
	}

	return nil
}
