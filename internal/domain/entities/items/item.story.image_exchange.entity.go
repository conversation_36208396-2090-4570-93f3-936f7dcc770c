package domain_entities_items

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"encoding/json"
)

type ExchangeImageConditionTemplate struct {
	ID                  int64                                      `json:"id"`
	CoverImageObjectKey domain_entities_resource.ImageResourcePath `json:"cover_image_object_key"`
	Condition           ExchangeImageCondition                     `json:"condition"`
	Typ                 api_items_story_types_v1.TemplateType      `json:"typ"`
}
type ExchangeImageCondition struct {
	Tips                        string                                       `json:"tips"`
	PositiveText                string                                       `json:"positive_text"`
	PositiveImageObjectKeys     []domain_entities_resource.ImageResourcePath `json:"positive_image_object_keys"`
	NegativeText                string                                       `json:"negative_text"`
	NegativeImageObjectKeys     []domain_entities_resource.ImageResourcePath `json:"negative_image_object_keys"`
	PartialMatchText            string                                       `json:"partial_match_text"`
	PartialMatchImageObjectKeys []domain_entities_resource.ImageResourcePath `json:"partial_match_image_object_keys"`
	LLMPrompt                   string                                       `json:"llm_prompt"`
}
type ExchangeImagePlayNode struct {
	ID                    string                                                                  `json:"id"`
	ResourceType          api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_ResourceType `json:"resource_type,string"`
	ImageKey              *domain_entities_resource.ImageResourcePath                             `json:"image_key,omitempty"`
	VideoKey              *domain_entities_resource.VideoResourcePath                             `json:"video_key,omitempty"`
	ThumbnailObjectKey    *domain_entities_resource.ImageResourcePath                             `json:"thumbnail_object_key,omitempty"`
	Condition             ExchangeImageCondition                                                  `json:"condition"`
	AllowUseAlbum         bool                                                                    `json:"allow_use_album"`
	MaxTryCount           uint32                                                                  `json:"max_try_count"`
	NeedCreateDiyTemplate bool                                                                    `json:"need_create_diy_template"`
	Resource              *Resource                                                               `json:"resource,omitempty"`
}

func (n *ExchangeImagePlayNode) GetCoverImagePath() domain_entities_resource.ImageResourcePath {
	if n.Resource != nil {
		return n.Resource.CoverImageKey
	}
	// TODO(Ricky): 历史老逻辑，后续废弃
	switch n.ResourceType {
	case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE:
		return *n.ImageKey
	case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO:
		return *n.ThumbnailObjectKey
	}
	return ""
}

type ExchanegImagePlayConfig struct {
	PlayMode     api_items_story_types_v1.ExchangeImagePlayMode `json:"play_mode,string"`
	Nodes        []*ExchangeImagePlayNode                       `json:"nodes"`
	CommonConfig *CommonConfig                                  `json:"common_config,omitempty"`
}

type ExchangeImagePlayContext struct {
	StoryBaseContext
	CurrentNodeID          string                                                  `json:"current_node_id"`
	CurrentNodeIdx         int                                                     `json:"current_node_idx"`
	CurrentTryCount        map[string]uint32                                       `json:"current_try_count"`
	CurrentSuccessProgress map[string]uint32                                       `json:"current_success_progress"`
	UserExchangeImageKeys  map[string][]domain_entities_resource.ImageResourcePath `json:"user_exchange_image_keys"`
	TryCount               uint32                                                  `json:"try_count"`
	UserTrialImageUrls     []domain_entities_resource.ImageResourcePath            `json:"user_trial_image_urls"`
	IsFinished             bool                                                    `json:"is_finished"`
}

func (c *StoryDetail) IsFinished() bool {
	switch c.Summary.PlayType {
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
		if c.ExchangeImagePlayContext == nil {
			return false
		}
		var currentNode *ExchangeImagePlayNode
		var currentNodeIdx int
		for idx, node := range c.Summary.ExchangeImagePlayConfig.Nodes {
			if node.ID == c.ExchangeImagePlayContext.CurrentNodeID {
				currentNode = node
				currentNodeIdx = idx
				break
			}
		}
		if currentNode == nil {
			return false
		}
		// 进度为 1 且当前节点为最后一个节点
		// 1. 如果是 linear 模式，仅需要查看是否是最后一个节点
		// 2. 如果是 masse 模式，则查看进度，由于 mass 模式下，只有一个节点，所以也一定是最后一个节点
		if c.Summary.ExchangeImagePlayConfig.PlayMode == api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_LINEAR {
			return currentNodeIdx == len(c.Summary.ExchangeImagePlayConfig.Nodes)-1
		} else {
			return c.ExchangeImagePlayContext.CurrentSuccessProgress[currentNode.ID] == 1
		}
	default:
		return false
	}
}

func (n *ExchangeImagePlayNode) MarshalJSON() ([]byte, error) {
	type Alias ExchangeImagePlayNode
	return json.Marshal(&struct {
		*Alias
		ResourceType string `json:"resource_type"`
	}{
		Alias:        (*Alias)(n),
		ResourceType: n.ResourceType.String(),
	})
}

func (n *ExchangeImagePlayNode) UnmarshalJSON(data []byte) error {
	type Alias ExchangeImagePlayNode
	aux := &struct {
		*Alias
		ResourceType string `json:"resource_type"`
	}{
		Alias: (*Alias)(n),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	switch aux.ResourceType {
	case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_UNSPECIFIED.String():
		n.ResourceType = api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_UNSPECIFIED
	case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE.String():
		n.ResourceType = api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_IMAGE
	case api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO.String():
		n.ResourceType = api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_VIDEO
	default:
		n.ResourceType = api_items_story_types_v1.StoryPlayExchangeImageConfig_Node_RESOURCE_TYPE_UNSPECIFIED
	}
	return nil
}

func (c *ExchanegImagePlayConfig) MarshalJSON() ([]byte, error) {
	type Alias ExchanegImagePlayConfig
	return json.Marshal(&struct {
		*Alias
		PlayMode string `json:"play_mode"`
	}{
		Alias:    (*Alias)(c),
		PlayMode: c.PlayMode.String(),
	})
}

func (c *ExchanegImagePlayConfig) UnmarshalJSON(data []byte) error {
	type Alias ExchanegImagePlayConfig
	aux := &struct {
		*Alias
		PlayMode string `json:"play_mode"`
	}{
		Alias: (*Alias)(c),
	}
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	switch aux.PlayMode {
	case api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_UNSPECIFIED.String():
		c.PlayMode = api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_UNSPECIFIED
	case api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_MASSE.String():
		c.PlayMode = api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_MASSE
	case api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_LINEAR.String():
		c.PlayMode = api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_LINEAR
	default:
		c.PlayMode = api_items_story_types_v1.ExchangeImagePlayMode_PLAY_MODE_UNSPECIFIED
	}
	return nil
}
