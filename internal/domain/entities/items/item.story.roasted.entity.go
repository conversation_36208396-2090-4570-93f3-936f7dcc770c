package domain_entities_items

import domain_entities_resource "boson/internal/domain/entities/resource"

type StoryPlayRoastedConfig struct {
	Topic        *StoryPlayRoastedTopic             `json:"topic"`
	Resource     *Resource                          `json:"resource"` // deprecated, TODO: DB数据删除后删除
	BaseResource *domain_entities_resource.Resource `json:"base_resource"`
	Cover        *domain_entities_resource.Resource `json:"cover"`
	// 从其他 roasted story 创作时，需要传此值
	// 如果不是，则不传
	FromStoryID *int64 `json:"from_story_id"`
}

type StoryPlayRoastedTopicBotAnnouncement struct {
	Content     string                                     `json:"content"`
	TTSAudioKey domain_entities_resource.AudioResourcePath `json:"tts_audio_key"`
}

type StoryPlayRoastedTopic struct {
	Greeting  *StoryPlayRoastedTopicBotAnnouncement `json:"greeting"`
	Ending    *StoryPlayRoastedTopicBotAnnouncement `json:"ending"`
	Questions []*StoryPlayRoastedQuestion           `json:"questions"`
}

type StoryPlayRoastedQuestion struct {
	Question    string                                     `json:"question"`
	Thinking    string                                     `json:"thinking"`
	TTSAudioKey domain_entities_resource.AudioResourcePath `json:"tts_audio_key"`
	Words       []Word                                     `json:"words"`
}

type StoryPlayRoastedContext struct {
	StoryBaseContext
	IsConsumed bool `json:"is_consumed"`
}
