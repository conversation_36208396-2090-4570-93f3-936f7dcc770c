package domain_entities_items

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_resource "boson/internal/domain/entities/resource"
)

type StoryPlayWassupConfig struct {
	UnlockResource  *domain_entities_resource.Resource `json:"unlock_resource"`
	CoverResource   *domain_entities_resource.Resource `json:"cover_resource"`
	CoverImageTexts []*AttachedText                    `json:"cover_image_texts"`
	IsMassCover     bool                               `json:"is_mass_cover"`
}

type StoryPlayWassupContext struct {
	StoryBaseContext
	IsUnlocked bool `json:"is_unlocked"`
}

type StoryPlayWassupBotMessage = api_items_story_types_v1.StoryPlayWassupBotMessage
