package domain_services_users_notifications

import (
	api_common_v1 "boson/api/common/v1"
	api_users_notifications_types_v1 "boson/api/users/notifications/v1/types"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_notifications "boson/internal/domain/entities/notifications"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_push "boson/internal/domain/services/push"
	domain_services_users_info "boson/internal/domain/services/users/info"
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

type IUserNotificationFizzQueryRepo interface {
	AllUserIdInFizz(ctx context.Context, fizzId int64) ([]int64, error)
}

type IStoryQueryService interface {
	GetStoryDetail(ctx context.Context, loginUserId int64, storyId int64) (*domain_entities_items.StoryDetail, error)
}

type IUserNoficationRepo interface {
	SaveUserSystemNotification(
		ctx context.Context,
		notifications ...*domain_entities_notifications.SystemNotification,
	) error

	ListUserSystemNotifications(
		ctx context.Context,
		loginUserId int64,
		fromFizzId *int64,
		types []api_users_notifications_types_v1.SystemNotificationType,
		listRequest *api_common_v1.ListRequest,
	) ([]*domain_entities_notifications.SystemNotification, *api_common_v1.ListResponse, error)

	BatchReadUserSystemNotifications(
		ctx context.Context,
		loginUserId int64,
		ids []int64,
	) error

	GetUserSystemNotificationStatSummary(
		ctx context.Context,
		loginUserId int64,
	) (map[api_users_notifications_types_v1.SystemNotificationType]*domain_entities_notifications.UserNormalNotificationSummary,
		map[int64]*domain_entities_notifications.UserFizzEventNotificationSummary,
		error,
	)
}

type UsersNotificationsService struct {
	idGenerator                   domain_interfaces.UniqIdGenerator
	userNotificationRepo          IUserNoficationRepo
	userNotificationFizzQueryRepo IUserNotificationFizzQueryRepo
	userInfoQueryService          *domain_services_users_info.UsersInfoService
	pushService                   *domain_services_push.PushService
	storyQueryService             IStoryQueryService
	logger                        *log.Helper
}

func NewUsersNotificationsService(
	idGenerator domain_interfaces.UniqIdGenerator,
	userNotificationRepo IUserNoficationRepo,
	userInfoQueryService *domain_services_users_info.UsersInfoService,
	userNotificationFizzQueryRepo IUserNotificationFizzQueryRepo,
	pushService *domain_services_push.PushService,
	storyQueryService IStoryQueryService,
	logger *log.Helper,
) *UsersNotificationsService {
	return &UsersNotificationsService{
		idGenerator:                   idGenerator,
		userNotificationRepo:          userNotificationRepo,
		userInfoQueryService:          userInfoQueryService,
		userNotificationFizzQueryRepo: userNotificationFizzQueryRepo,
		pushService:                   pushService,
		storyQueryService:             storyQueryService,
		logger:                        logger,
	}
}

func (s *UsersNotificationsService) BatchReadUserSystemNotifications(
	ctx context.Context,
	loginUserId int64,
	ids []int64,
) error {
	return s.userNotificationRepo.BatchReadUserSystemNotifications(ctx, loginUserId, ids)
}

func (s *UsersNotificationsService) GetUserSystemNotificationStatSummary(
	ctx context.Context,
	loginUserId int64,
) (
	map[api_users_notifications_types_v1.SystemNotificationType]*domain_entities_notifications.UserNormalNotificationSummary,
	map[int64]*domain_entities_notifications.UserFizzEventNotificationSummary,
	error,
) {
	return s.userNotificationRepo.GetUserSystemNotificationStatSummary(ctx, loginUserId)
}

func (s *UsersNotificationsService) ListUserSystemNotifications(
	ctx context.Context,
	loginUserId int64,
	fromFizzId *int64,
	types []api_users_notifications_types_v1.SystemNotificationType,
	listRequest *api_common_v1.ListRequest,
) ([]*domain_entities_notifications.SystemNotification, *api_common_v1.ListResponse, error) {
	notifications, res, err := s.userNotificationRepo.ListUserSystemNotifications(ctx, loginUserId, fromFizzId, types, listRequest)
	if err != nil {
		return nil, nil, err
	}
	if len(notifications) == 0 {
		return nil, res, nil
	}
	return notifications, res, nil
}

func (s *UsersNotificationsService) ConsumeFollowEvent(ctx context.Context, toUser *domain_entities_users.UserDetailEntity, fromUser *domain_entities_users.UserDetailEntity) error {
	title := "You have a new follower!"
	content := fmt.Sprintf("%s is now following you - say hi!", fromUser.Summary.Nickname)
	userId := toUser.Summary.ID
	pushMessageId := s.idGenerator.Generate()
	// 构建 notification 进行存储
	notification := &domain_entities_notifications.SystemNotification{
		Id:             pushMessageId,
		ToUserId:       userId,
		CreatedAt:      time.Now(),
		Title:          title,
		CoverImagePath: fromUser.Summary.AvatarImagePath,
		Type:           api_users_notifications_types_v1.SystemNotificationType_SYSTEM_NOTIFICATION_TYPE_FOLLOWED_BY_OTHERS,
		Payload: &domain_entities_notifications.SystemNotificationPayload{
			FollowedByOthersEvents: &domain_entities_notifications.FollowedByOthersEventsPayload{
				User: fromUser,
			},
		},
	}
	// 发送推送通知
	// 获取关注者头像的完整URL
	var imageURL string
	if fromUser.Summary.AvatarImagePath != "" {
		imageURL = fromUser.Summary.AvatarImagePath.UserDetailAvatar()
	}

	host := "new_follower"
	pathSegments := []string{}
	params := map[string]string{
		"user_id":         strconv.FormatInt(toUser.Summary.ID, 10),
		"push_message_id": strconv.FormatInt(pushMessageId, 10),
		"follower_id":     strconv.FormatInt(fromUser.Summary.ID, 10),
	}
	// 跳转到关注者的个人页面
	jumpScheme, err := domain_entities_notifications.BuildJumpURL(host, pathSegments, params)
	if err != nil {
		s.logger.Errorf("build jump scheme failed: %v", err)
	}

	// 使用带拦截功能的推送方法，防止24小时内重复推送
	pushType := host                                        // 直接使用 host 作为推送类型
	pushValue := strconv.FormatInt(fromUser.Summary.ID, 10) // 使用关注者ID作为推送值
	err = s.pushService.PushWithInterception(ctx, []int64{userId}, title, content, imageURL, jumpScheme, pushType, pushValue)
	if err != nil {
		// 尽力发送，忽略失败
		s.logger.WithContext(ctx).Warnf("push user relations change user %d: %s follow user %d: %s failed: %v", userId, fromUser.Summary.Nickname, userId, toUser.Summary.Nickname, err)
	}
	return s.userNotificationRepo.SaveUserSystemNotification(ctx, notification)
}

func (s *UsersNotificationsService) ConsumeNewFollowingPlayEvent(ctx context.Context, pushMessageId string, storyId int64, followUserId int64, expireTime int64) error {
	now := time.Now().Unix()
	if now > expireTime {
		return nil
	}
	story, err := s.storyQueryService.GetStoryDetail(ctx, followUserId, storyId)
	if err != nil {
		s.logger.WithContext(ctx).Errorf("failed to get story detail for story %d: %v", storyId, err)
		return err
	}

	var imageURL string
	if story.Summary != nil {
		coverImage, err := story.Summary.GetStoryCover()
		if err != nil {
			s.logger.WithContext(ctx).Warnf("failed to get story cover for story %d: %v", storyId, err)
		} else if coverImage != nil {
			imageURL = coverImage.GetThumbnail().OriginalAccessURL()
		}
	}

	// 3. 构建推送消息
	title := story.Summary.Author.Nickname
	content := "New play alert! Let's see if you can unlock it"

	// 4. 构建跳转链接
	host := "new_following_play"
	pathSegments := []string{}
	params := map[string]string{
		"play_id":         strconv.FormatInt(storyId, 10),
		"user_id":         strconv.FormatInt(followUserId, 10),
		"push_message_id": pushMessageId,
	}
	jumpScheme, err := domain_entities_notifications.BuildJumpURL(host, pathSegments, params)
	if err != nil {
		s.logger.Errorf("build jump scheme failed: %v", err)
	}

	// 5. 使用带拦截功能的推送方法，防止24小时内重复推送
	pushType := host                            // 直接使用 host 作为推送类型
	pushValue := strconv.FormatInt(storyId, 10) // 使用故事ID作为推送值
	err = s.pushService.PushWithInterception(ctx, []int64{followUserId}, title, content, imageURL, jumpScheme, pushType, pushValue)
	if err != nil {
		// 尽力发送，忽略失败
		s.logger.WithContext(ctx).Warnf("push story created event to user %d for story %d failed: %v", followUserId, storyId, err)
	}

	return nil
}
