package domain_services_users_recommend

import (
	"context"

	api_common_v1 "boson/api/common/v1"
	api_users_recommend_v1 "boson/api/users/recommend/v1"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_services_users_info "boson/internal/domain/services/users/info"
)

type IRecommendUsersClient interface {
	RecommendUsers(ctx context.Context, userId int64, personId *int64, scenario api_users_recommend_v1.RecmmendScenario, req *api_common_v1.ListRequest) (
		recommendUsers []*RecommendUser,
		sortRequestId string,
		res *api_common_v1.ListResponse,
		err error,
	)
}

type UsersRecommendService struct {
	IRecommendUsersClient
	UserInfoService *domain_services_users_info.UsersInfoService
}

func NewUsersRecommendService(
	recommendUsersClient IRecommendUsersClient,
	userInfoService *domain_services_users_info.UsersInfoService,
) *UsersRecommendService {
	return &UsersRecommendService{
		IRecommendUsersClient: recommendUsersClient,
		UserInfoService:       userInfoService,
	}
}

type RecommendUser struct {
	UserInfo             *domain_entities_users.UserSummaryEntity
	RecommendReason      string
	RecommendReasonExtra *RecommendReasonExtra
	RelatedStoryId       string
}

type RecommendReasonExtra struct {
	ReasonType   string
	RelatedUsers []*domain_entities_users.UserSummaryEntity
}

func (s *UsersRecommendService) RecommendUsers(
	ctx context.Context,
	userId int64,
	personId *int64,
	scenario api_users_recommend_v1.RecmmendScenario,
	req *api_common_v1.ListRequest,
) (
	recommendUsers []*RecommendUser,
	sortRequestId string,
	res *api_common_v1.ListResponse,
	err error,
) {
	recommendUsers, sortRequestId, res, err = s.IRecommendUsersClient.RecommendUsers(ctx, userId, personId, scenario, req)
	if err != nil {
		return
	}
	var userIds []int64
	for _, recommendUser := range recommendUsers {
		userIds = append(userIds, recommendUser.UserInfo.ID)
		// 收集 RecommendReasonExtra 中的相关用户 ID
		if recommendUser.RecommendReasonExtra != nil {
			for _, relatedUser := range recommendUser.RecommendReasonExtra.RelatedUsers {
				userIds = append(userIds, relatedUser.ID)
			}
		}
	}

	userInfos, err := s.UserInfoService.BatchGetUserInfo(ctx, userId, userIds...)
	if err != nil {
		return
	}
	for _, recommendUser := range recommendUsers {
		// 填充主要推荐用户信息
		if userInfo, ok := userInfos[recommendUser.UserInfo.ID]; ok {
			recommendUser.UserInfo = userInfo
		}
		// 填充相关用户信息
		if recommendUser.RecommendReasonExtra != nil {
			for i, relatedUser := range recommendUser.RecommendReasonExtra.RelatedUsers {
				if userInfo, ok := userInfos[relatedUser.ID]; ok {
					recommendUser.RecommendReasonExtra.RelatedUsers[i] = userInfo
				}
			}
		}
	}
	return
}
