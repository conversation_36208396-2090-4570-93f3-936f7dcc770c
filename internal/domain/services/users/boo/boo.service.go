package domain_services_users_boo

import (
	"context"
	"fmt"
	"time"

	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_users_boo_types_v1 "boson/api/users/boo/types/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_boo "boson/internal/domain/entities/boo"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_im "boson/internal/domain/services/im"
	domain_services_rtm "boson/internal/domain/services/rtm"
	domain_services_users_info_interfaces "boson/internal/domain/services/users/info/interfaces"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
)

type CreateBooAttr struct {
	ID         int64
	UserId     int64
	AvatarId   int64
	Animations []*domain_entities_boo.BooAnimation
	Status     api_users_boo_types_v1.Status
}
type CreateAvatarAttr struct {
	UserId           int64
	RootJobId        int64
	AvatarObjectKeys []domain_entities_resource.ImageResourcePath
	Ids              []int64
}

const (
	UpdateUserBooShowInfoLockKeyFmt = "update_user_boo_show_info_%d"
)

type IStickerRepo interface {
	CreateStickers(ctx context.Context, userId int64, stickers ...*domain_entities_items.HideSticker) error
}

type IBooRepository interface {
	BatchGetBoo(ctx context.Context, userId int64, booIds []int64) (map[int64]*domain_entities_boo.Boo, error)
	BatchGetBooWithAvatarKeys(ctx context.Context, userId int64, avatarKeys []domain_entities_resource.ImageResourcePath) (map[domain_entities_resource.ImageResourcePath]*domain_entities_boo.Boo, error)
	BatchGetBooWithAvatarIds(ctx context.Context, userId int64, avatarIds []int64) (map[int64]*domain_entities_boo.Boo, error)
	GetBoo(ctx context.Context, userId int64, booId int64) (*domain_entities_boo.Boo, error)
	GetBooLatestShowInfo(ctx context.Context, userId int64, withLock bool) (*domain_entities_boo.BooShowInfo, error)

	SaveBooLatestShowInfo(ctx context.Context, userId int64, showInfo *domain_entities_boo.BooShowInfo) error

	CreateAvatars(ctx context.Context, attr CreateAvatarAttr) ([]*domain_entities_boo.Avatar, error)
	UnpickedAllAvatarAsUnused(ctx context.Context, userId int64) error
	PickedAvatarAsUsed(ctx context.Context, userId int64, avatarId int64) error
	GetPickedAvatar(ctx context.Context, userId int64) (*domain_entities_boo.Avatar, error)
	ListUserAvatars(ctx context.Context, userId int64) ([]*domain_entities_boo.Avatar, error)
	SetAvatarGeneratedBooId(ctx context.Context, avatarId int64, generatedBooId int64) error

	CreateBoo(ctx context.Context, attr CreateBooAttr) error

	AddBooAnimation(ctx context.Context, booId int64, animations []*domain_entities_boo.BooAnimation) error

	UpdateSendRecordStatus(ctx context.Context, booId int64, status api_users_boo_types_v1.BooSendRecordStatus, toUserIds ...int64) error

	CreateSendRecord(
		ctx context.Context,
		fromUserId int64,
		booId int64,
		status api_users_boo_types_v1.BooSendRecordStatus,
		expiredAt time.Time,
		toUserIds ...int64,
	) error

	ListSentBooUserIdsWithFromUserId(
		ctx context.Context,
		userId int64,
		from, end time.Time,
		statues []api_users_boo_types_v1.BooSendRecordStatus,
	) ([]int64, error)
	ListSentBooUserIdsWithBooId(
		ctx context.Context,
		booId int64,
		from, end time.Time,
		statues []api_users_boo_types_v1.BooSendRecordStatus,
	) ([]int64, error)

	UpdateBooStatus(ctx context.Context, userId int64, booId int64, status api_users_boo_types_v1.Status) error

	SaveEmotionImages(ctx context.Context, avatarId int64, emotionImages map[api_users_boo_types_v1.BooEmotionType]domain_entities_resource.ImageResourcePath) error
}

type BooService struct {
	booRepository   IBooRepository
	stickerRepo     IStickerRepo
	txManager       domain_interfaces.TransactionManager
	locker          domain_interfaces.Locker
	uniqIdGenerator domain_interfaces.UniqIdGenerator
	rtmService      *domain_services_rtm.RtmService
	imService       *domain_services_im.IMService
	userInfo        domain_services_users_info_interfaces.IUsersInfoQueryRepo
}

func NewBooService(booRepository IBooRepository, txManager domain_interfaces.TransactionManager,
	locker domain_interfaces.Locker, uniqIdGenerator domain_interfaces.UniqIdGenerator,
	imService *domain_services_im.IMService, userInfo domain_services_users_info_interfaces.IUsersInfoQueryRepo,
	rtmService *domain_services_rtm.RtmService, stickerRepo IStickerRepo) *BooService {
	return &BooService{
		booRepository:   booRepository,
		txManager:       txManager,
		locker:          locker,
		uniqIdGenerator: uniqIdGenerator,
		imService:       imService,
		userInfo:        userInfo,
		rtmService:      rtmService,
		stickerRepo:     stickerRepo,
	}
}
func (s *BooService) CreateAvatars(
	ctx context.Context,
	userId int64,
	jobId int64,
	avatarObjectPaths []domain_entities_resource.ImageResourcePath,
) ([]*domain_entities_boo.Avatar, error) {
	avatars, err := s.booRepository.CreateAvatars(ctx, CreateAvatarAttr{
		UserId:           userId,
		RootJobId:        jobId,
		AvatarObjectKeys: avatarObjectPaths,
		Ids:              s.uniqIdGenerator.BatchGenerate(len(avatarObjectPaths)),
	})
	if err != nil {
		return nil, err
	}
	// 存为贴纸
	stickers := []*domain_entities_items.HideSticker{}
	for _, avatar := range avatars {
		stickers = append(stickers, &domain_entities_items.HideSticker{
			Id:           fmt.Sprintf("%d", s.uniqIdGenerator.Generate()),
			FromAvatarId: avatar.Id,
			FromStoryId:  jobId,
			Resource:     domain_entities_resource.NewResourceBuilder().WithImageObjectKey(avatar.ObjectPath, 0, 0).Build(),
		})
	}
	if err := s.stickerRepo.CreateStickers(ctx, userId, stickers...); err != nil {
		return nil, err
	}

	return avatars, nil
}

func (s *BooService) ReportCaptureBooAction(
	ctx context.Context,
	userId int64,
	booId int64,
	scene api_users_boo_types_v1.BooShowScene,
	emotionType api_users_boo_types_v1.BooEmotionType,
	actionType api_users_boo_types_v1.UserActionType,
) (*domain_entities_boo.BooShowInfo, string, error) {

	actionId := s.uniqIdGenerator.Generate()

	if actionType == api_users_boo_types_v1.UserActionType_USER_ACTION_TYPE_CAPTURE_FAILED {
		if err := s.sendCaptureMessage(ctx, userId, booId, api_im_message_types_v1.BooInteractionCustomMessagePayload_MESSAGE_TYPE_CAUGHT_FAILED_BOO.String()); err != nil {
			return nil, "", err
		}
	}
	// 目前当且仅当抓鬼成功时才会操作
	if actionType != api_users_boo_types_v1.UserActionType_USER_ACTION_TYPE_CAPTURE_SUCCESS {
		latestInfo, err := s.GetUserLatestShowInfo(ctx, userId, false)
		if err != nil {
			return nil, "", err
		}
		return latestInfo, "", nil
	}

	if err := s.txManager.ExecTx(ctx, func(ctx context.Context) error {

		showInfo, err := s.GetUserLatestShowInfo(ctx, userId, true)
		if err != nil {
			return err
		}
		if showInfo == nil {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("show info not found"))
		}

		// 有这个鬼吗？
		if showInfo.GetBooWithId(booId) == nil {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("boo not found"))
		}

		// 更下记录
		if err := s.booRepository.UpdateSendRecordStatus(ctx, booId, api_users_boo_types_v1.BooSendRecordStatus_BOO_SEND_RECORD_STATUS_CAPTURED, userId); err != nil {
			return err
		}

		// 抓鬼成功，从 show info 中移除这个鬼
		showInfo.RemoveReceivedBoo(booId)

		return s.booRepository.SaveBooLatestShowInfo(ctx, userId, showInfo)
	}); err != nil {
		return nil, "", err
	}

	latestInfo, err := s.GetUserLatestShowInfo(ctx, userId, false)
	if err != nil {
		return nil, "", err
	}
	return latestInfo, fmt.Sprint(actionId), nil
}

func (s *BooService) SaveEmotionImages(ctx context.Context, avatarId int64, emotionImages map[api_users_boo_types_v1.BooEmotionType]domain_entities_resource.ImageResourcePath) error {
	return s.booRepository.SaveEmotionImages(ctx, avatarId, emotionImages)
}
func (s *BooService) AddBooAnimation(ctx context.Context, booId int64, animations []*domain_entities_boo.BooAnimation) (*domain_entities_boo.Boo, error) {
	if err := s.booRepository.AddBooAnimation(ctx, booId, animations); err != nil {
		return nil, err
	}
	return s.booRepository.GetBoo(ctx, booId, booId)
}
func (s *BooService) BatchGetBoosWithIds(ctx context.Context, userId int64, booIds []int64) (map[int64]*domain_entities_boo.Boo, error) {
	return s.booRepository.BatchGetBoo(ctx, userId, booIds)
}
func (s *BooService) GetUserPickedAvatarGeneratedBoo(ctx context.Context, userId int64) (*domain_entities_boo.Boo, error) {
	avatar, err := s.GetPickedAvatar(ctx, userId)
	if err != nil {
		return nil, err
	}
	if avatar == nil {
		return nil, nil
	}
	boo, err := s.booRepository.GetBoo(ctx, userId, avatar.GeneratedBooId)
	if err != nil {
		return nil, err
	}
	if boo.Status != api_users_boo_types_v1.Status_STATUS_GENERATED {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("boo not generated"))
	}
	return boo, nil
}
func (s *BooService) BatchGetBoosWithAvatarKeys(ctx context.Context, userId int64, avatarKeys []domain_entities_resource.ImageResourcePath) (map[domain_entities_resource.ImageResourcePath]*domain_entities_boo.Boo, error) {
	return s.booRepository.BatchGetBooWithAvatarKeys(ctx, userId, avatarKeys)
}
func (s *BooService) BatchGetBoosWithAvatarIds(ctx context.Context, userId int64, avatarIds ...int64) (map[int64]*domain_entities_boo.Boo, error) {
	return s.booRepository.BatchGetBooWithAvatarIds(ctx, userId, avatarIds)
}
func (s *BooService) UnpickedAllAvatarAsUnused(ctx context.Context, userId int64) error {
	return s.booRepository.UnpickedAllAvatarAsUnused(ctx, userId)
}
func (s *BooService) pickedAvatarAsUsed(ctx context.Context, userId int64, avatarObjectKey domain_entities_resource.ImageResourcePath) error {
	avatars, err := s.booRepository.ListUserAvatars(ctx, userId)
	if err != nil {
		return err
	}
	for _, avatar := range avatars {
		if avatar.ObjectPath == avatarObjectKey {
			return s.booRepository.PickedAvatarAsUsed(ctx, userId, avatar.Id)
		}
	}
	return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("avatar not found"))
}
func (s *BooService) GetPickedAvatar(ctx context.Context, userId int64) (*domain_entities_boo.Avatar, error) {
	return s.booRepository.GetPickedAvatar(ctx, userId)
}
func (s *BooService) SetAvatarGeneratedBooId(ctx context.Context, avatarId int64, generatedBooId int64) error {
	return s.booRepository.SetAvatarGeneratedBooId(ctx, avatarId, generatedBooId)
}
func (s *BooService) UpdateBooStatus(ctx context.Context, userId int64, booId int64, status api_users_boo_types_v1.Status) error {
	return s.booRepository.UpdateBooStatus(ctx, userId, booId, status)
}
func (s *BooService) ListUserAvatars(ctx context.Context, userId int64) ([]*domain_entities_boo.Avatar, error) {
	return s.booRepository.ListUserAvatars(ctx, userId)
}
func (s *BooService) CreateBoo(ctx context.Context, attr CreateBooAttr) (*domain_entities_boo.Boo, error) {
	if attr.ID == 0 {
		attr.ID = s.uniqIdGenerator.Generate()
	}
	if err := s.booRepository.CreateBoo(ctx, attr); err != nil {
		return nil, err
	}
	boo, err := s.booRepository.GetBoo(ctx, attr.UserId, attr.ID)
	if err != nil {
		return nil, err
	}
	return boo, nil
}
func (s *BooService) GetUserLatestShowInfo(ctx context.Context, userId int64, withLock bool) (*domain_entities_boo.BooShowInfo, error) {
	return s.booRepository.GetBooLatestShowInfo(ctx, userId, withLock)
}

func (s *BooService) SendBooToSendRecoredUsers(ctx context.Context, fromUserId int64, boo *domain_entities_boo.Boo) error {
	records, err := s.booRepository.ListSentBooUserIdsWithBooId(
		ctx,
		boo.ID,
		time.Now().Add(-time.Hour*24*30),
		time.Now(),
		[]api_users_boo_types_v1.BooSendRecordStatus{
			api_users_boo_types_v1.BooSendRecordStatus_BOO_SEND_RECORD_STATUS_PENDING,
		},
	)
	if err != nil {
		return err
	}
	// 已经记录过了，不再重复记录而是更新
	return s.TrySendBooToUsers(ctx, fromUserId, boo, false, records...)
}

// 尝试发送 boo 到对方，如果 boo 状态正确，直接添加进对方的 showInfo 并且记录；否则创建一个发送记录
func (s *BooService) TrySendBooToUsers(
	ctx context.Context,
	fromUserId int64,
	boo *domain_entities_boo.Boo,
	needSaveRecordWhenBooExist bool,
	toUserIds ...int64,
) error {
	if boo == nil {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("boo not found"))
	}
	if boo.Creator.ID != fromUserId {
		// 权限不足
		return errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("permission denied"))
	}
	// 如果 boo 状态正确，直接添加进对方的 showInfo
	if boo.Status == api_users_boo_types_v1.Status_STATUS_GENERATED {
		for _, toUserId := range toUserIds {
			toUserId := toUserId
			if _, err := s.addBooToUsersShowInfo(ctx, fromUserId, toUserId, boo); err != nil {
				return err
			}
		}
		if needSaveRecordWhenBooExist {
			return s.booRepository.CreateSendRecord(
				ctx,
				fromUserId,
				boo.ID,
				api_users_boo_types_v1.BooSendRecordStatus_BOO_SEND_RECORD_STATUS_SUCCESS,
				time.Now().Add(time.Hour*24*30),
				toUserIds...,
			)
		}
		return s.booRepository.UpdateSendRecordStatus(
			ctx,
			boo.ID,
			api_users_boo_types_v1.BooSendRecordStatus_BOO_SEND_RECORD_STATUS_SUCCESS,
			toUserIds...,
		)
	}
	// 创建发送记录，如果 boo 的状态异常，等待 boo 完成后再实现 add to showInfo
	return s.booRepository.CreateSendRecord(ctx, fromUserId, boo.ID, api_users_boo_types_v1.BooSendRecordStatus_BOO_SEND_RECORD_STATUS_PENDING, time.Now().Add(time.Hour*24*30), toUserIds...)
}

func (s *BooService) ListSentBooUserIdsWithFromUserId(ctx context.Context, userId int64, from, end time.Time, statues []api_users_boo_types_v1.BooSendRecordStatus) ([]int64, error) {
	records, err := s.booRepository.ListSentBooUserIdsWithFromUserId(ctx, userId, from, end, statues)
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (s *BooService) SaveBooShowRecord(
	ctx context.Context,
	userId int64,
	booId int64,
	scene api_users_boo_types_v1.BooShowScene,
	emotionType api_users_boo_types_v1.BooEmotionType,
) (*domain_entities_boo.BooShowInfo, error) {
	if err := s.txManager.ExecTx(ctx, func(ctx context.Context) error {
		showInfo, err := s.booRepository.GetBooLatestShowInfo(ctx, userId, true)
		if err != nil {
			return err
		}
		if showInfo == nil {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("show info not found"))
		}
		if err := showInfo.AddShowRecord(booId, scene, emotionType); err != nil {
			return err
		}
		err = s.sendCaptureMessage(ctx, userId, booId, api_im_message_types_v1.BooInteractionCustomMessagePayload_MESSAGE_TYPE_FOUND_BOO.String())
		if err != nil {
			return err
		}
		return s.booRepository.SaveBooLatestShowInfo(ctx, userId, showInfo)
	}); err != nil {
		return nil, err
	}
	return s.booRepository.GetBooLatestShowInfo(ctx, userId, false)
}

func (s *BooService) defaultShowInfo(userId int64) *domain_entities_boo.BooShowInfo {
	return &domain_entities_boo.BooShowInfo{
		UserId: userId,
		Data: &domain_entities_boo.BooShowInfoData{
			ShowConfig: &domain_entities_boo.ShowConfig{
				EveryShowIntervalSeconds: uint32(time.Minute.Seconds()),
			},
			ReceivedBoos: []*domain_entities_boo.ReceivedBoo{},
		},
	}
}

func (s *BooService) SendCaptureRecordVideo(ctx context.Context, userId int64, booId int64, videoObjectKey domain_entities_resource.VideoResourcePath, videoCoverObjectKey domain_entities_resource.ImageResourcePath) error {
	boo, err := s.booRepository.GetBoo(ctx, userId, booId)
	if err != nil {
		return err
	}

	if err = s.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_BooInteraction{
			BooInteraction: &api_im_message_types_v1.BooInteractionCustomMessagePayload{
				MessageType: api_im_message_types_v1.BooInteractionCustomMessagePayload_MESSAGE_TYPE_CAUGHT_BOO.String(),
				VideoPayload: &api_im_message_types_v1.BooInteractionCustomMessagePayload_VideoPayload{
					VideoUrl: videoObjectKey.URL(),
					CoverUrl: videoCoverObjectKey.ItemPosterInSummary(),
				},
				BooAvatarUrl: boo.Avatar.GetEmotionImage(api_users_boo_types_v1.BooEmotionType_BOO_EMOTION_TYPE_SCARED).UserDetailAvatar(),
			},
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_BOO_INTERACTION.String(),
	}, boo.Creator.ID); err != nil {
		return err
	}
	return nil
}

// ************************* private funcs
func (s *BooService) addBooToUsersShowInfo(ctx context.Context, fromUserId, toUserId int64, boo *domain_entities_boo.Boo) (*domain_entities_boo.BooShowInfo, error) {
	if toUserId == boo.Creator.ID {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("cannot add boo to self"))
	}
	// 这里会设计 BooShowInfo 的初始化，所以要加锁
	if err := s.locker.Lock(ctx, fmt.Sprintf(UpdateUserBooShowInfoLockKeyFmt, toUserId), func(ctx context.Context) error {
		return s.txManager.ExecTx(ctx, func(ctx context.Context) error {
			showInfo, err := s.booRepository.GetBooLatestShowInfo(ctx, toUserId, true)
			if err != nil {
				return err
			}
			if showInfo == nil {
				showInfo = s.defaultShowInfo(toUserId)
			}
			if err := showInfo.AddReceivedBoo(boo); err != nil {
				return err
			}
			return s.booRepository.SaveBooLatestShowInfo(ctx, toUserId, showInfo)
		})
	}, time.Second*10); err != nil {
		return nil, err
	}
	// 发送 rtm 通知对方 showinfo 更新了
	latestShowInfo, err := s.booRepository.GetBooLatestShowInfo(ctx, toUserId, false)
	if err != nil {
		return nil, err
	}
	messageData, err := proto.Marshal(&api_users_boo_types_v1.BooShowInfoUpdatedMessage{
		Topic:             api_users_boo_types_v1.BooShowInfoUpdatedTopic_BOO_SHOW_INFO_UPDATED_BY_RECEIVED_NEW_BOO,
		LatestBooShowInfo: adapter_driven_assembler.ConverShowInfoToDto(latestShowInfo),
	})
	if err != nil {
		return nil, errors.Wrapf(err, "fromUserId: %d, toUserId: %d, booId: %d", fromUserId, toUserId, boo.ID)
	}
	if err := s.rtmService.SendMessageToUsers(ctx, fromUserId, []int64{toUserId}, string(messageData)); err != nil {
		return nil, errors.Wrapf(err, "fromUserId: %d, toUserId: %d, booId: %d", fromUserId, toUserId, boo.ID)
	}
	return s.booRepository.GetBooLatestShowInfo(ctx, toUserId, false)
}

func (s *BooService) sendCaptureMessage(ctx context.Context, userId int64, booId int64, msg string) error {
	boo, err := s.booRepository.GetBoo(ctx, userId, booId)
	if err != nil {
		return err
	}
	if err = s.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_BooInteraction{
			BooInteraction: &api_im_message_types_v1.BooInteractionCustomMessagePayload{
				MessageType: msg,
			},
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_BOO_INTERACTION.String(),
	}, boo.Creator.ID); err != nil {
		return err
	}
	return nil
}
