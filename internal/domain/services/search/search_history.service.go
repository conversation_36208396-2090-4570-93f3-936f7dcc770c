package domain_services_search

import (
	api_search_v1 "boson/api/search/v1"
	domain_entities_search "boson/internal/domain/entities/search"
	domain_interfaces "boson/internal/domain/interfaces"
	"context"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
)

type SearchHistoryService struct {
	searchHistoryRepo domain_interfaces.ISearchHistoryRepository
}

func NewSearchHistoryService(
	searchHistoryRepo domain_interfaces.ISearchHistoryRepository,
) *SearchHistoryService {
	return &SearchHistoryService{
		searchHistoryRepo: searchHistoryRepo,
	}
}

func (s *SearchHistoryService) GetSearchHistory(
	ctx context.Context,
	userID int64,
	count int32,
) ([]*domain_entities_search.SearchHistory, error) {
	if count <= 0 || count > 100 {
		return nil, errors.New("count must be between 1 and 100")
	}

	histories, err := s.searchHistoryRepo.GetSearchHistory(ctx, userID, count)
	if err != nil {
		return nil, errors.Wrapf(err, "userID: %d, count: %d", userID, count)
	}

	return histories, nil
}

func (s *SearchHistoryService) DeleteSearchHistory(
	ctx context.Context,
	userID int64,
	searchIDs []string,
) error {
	if len(searchIDs) == 0 || len(searchIDs) > 100 {
		return errors.New("search_ids length must be between 1 and 100")
	}

	// Convert string IDs to int64
	var searchIDsInt64 []int64
	for _, idStr := range searchIDs {
		id := cast.ToInt64(idStr)
		if id <= 0 {
			return errors.Errorf("invalid search_id: %s", idStr)
		}
		searchIDsInt64 = append(searchIDsInt64, id)
	}

	err := s.searchHistoryRepo.DeleteSearchHistory(ctx, userID, searchIDsInt64)
	if err != nil {
		return errors.Wrapf(err, "userID: %d, searchIDs: %v", userID, searchIDs)
	}

	return nil
}

func (s *SearchHistoryService) UploadSearchHistory(
	ctx context.Context,
	userID int64,
	searchItems []*api_search_v1.SearchHistoryUploadItem,
) ([]string, error) {
	if len(searchItems) == 0 || len(searchItems) > 100 {
		return nil, errors.New("search_items length must be between 1 and 100")
	}

	// Convert to repository format
	var items []struct {
		SearchText string
		SearchType api_search_v1.SearchType
	}

	for _, item := range searchItems {
		searchType := api_search_v1.SearchType_value[item.SearchType]
		if searchType == 0 {
			return nil, errors.Errorf("invalid search_type: %s", item.SearchType)
		}

		items = append(items, struct {
			SearchText string
			SearchType api_search_v1.SearchType
		}{
			SearchText: item.SearchText,
			SearchType: api_search_v1.SearchType(searchType),
		})
	}

	searchIDs, err := s.searchHistoryRepo.BatchCreateSearchHistory(ctx, userID, items)
	if err != nil {
		return nil, errors.Wrapf(err, "userID: %d, searchItems: %v", userID, searchItems)
	}

	// Convert int64 IDs to strings
	var result []string
	for _, id := range searchIDs {
		result = append(result, cast.ToString(id))
	}

	return result, nil
}
