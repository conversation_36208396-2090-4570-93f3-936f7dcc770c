package domain_services_interfaces_push

import (
	"context"

	domain_entities_push "boson/internal/domain/entities/push"
)

// IPushInterceptionRepository 推送拦截仓储接口
type IPushInterceptionRepository interface {
	// CreatePushInterception 创建推送拦截记录
	CreatePushInterception(ctx context.Context, attr domain_entities_push.CreatePushInterceptionAttr) error

	// CheckPushInterceptionExists 检查指定时间内是否存在相同的推送拦截记录
	CheckPushInterceptionExists(ctx context.Context, attr domain_entities_push.CheckPushInterceptionAttr) (bool, error)
}

// IPushInterceptionService 推送拦截服务接口
type IPushInterceptionService interface {
	// ShouldInterceptPush 检查是否应该拦截推送
	ShouldInterceptPush(ctx context.Context, userID int64, pushType string, pushValue string) (bool, error)

	// RecordPushInterception 记录推送拦截
	RecordPushInterception(ctx context.Context, userID int64, pushType string, pushValue string) error
}
