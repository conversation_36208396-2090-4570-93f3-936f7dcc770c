package domain_services_items_portal

import (
	"context"
	"fmt"
	"time"

	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_items_portal_moment_types_v1 "boson/api/items/portal/moments/types/v1"
	api_items_portal_v1 "boson/api/items/portal/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_im "boson/internal/domain/services/im"
	domain_services_items_story "boson/internal/domain/services/items/story"
	domain_services_users_info "boson/internal/domain/services/users/info"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type IRecommendPortalsService interface {
	RecommendTrendingPortals(ctx context.Context, userId int64, loadmore bool, pageSize int32) (portalId []int64, sortRequestId string, hasMore bool, err error)
}

type IPortalRepository interface {
	ListCreatedPortals(ctx context.Context, userId int64, expiredAfrer time.Time) ([]*domain_entities_items.Portal, error)
	ListMyPortals(ctx context.Context, userId int64, listRequest *api_common_v1.ListRequest) ([]*domain_entities_items.Portal, *api_common_v1.ListResponse, error)
	CreatePortal(ctx context.Context, story *domain_entities_items.StoryDetail, userMomentInfos []*domain_entities_items.MomentExtraInfo) (*domain_entities_items.Portal, error)
	BatchGetPortals(ctx context.Context, userId int64, portalIds []int64) (map[int64]*domain_entities_items.Portal, error)
	BatchGetPortalsWithStoryIds(ctx context.Context, userId int64, storyIds []int64) (map[int64]*domain_entities_items.Portal, error)
	ListCouldAppendMomentPortals(ctx context.Context, userId int64) ([]*domain_entities_items.Portal, error)
	AppendMoment(
		ctx context.Context,
		userId int64,
		portal *domain_entities_items.Portal,
		moment *domain_entities_items.Moment,
		createType api_items_portal_moment_types_v1.MomentCreateType,
	) error
	BatchGetMoments(ctx context.Context, userId int64, momentIds []int64) (map[int64]*domain_entities_items.Moment, error)
	DeleteMoment(ctx context.Context, momentId int64) error

	MakeRelation(ctx context.Context, userId int64, momentId int64, relationType api_items_portal_moment_types_v1.RelationType) error
	RemoveRelation(ctx context.Context, userId int64, momentId int64, relationType api_items_portal_moment_types_v1.RelationType) error
	BatchGetMomentAndUsersRelations(ctx context.Context, momentId int64, userIds []int64) (map[int64][]*domain_entities_items.MomentRelation, error)

	GetUserLastReadPortalId(ctx context.Context, userId int64) (*int64, error)
	GetUserHasPostedStories(ctx context.Context, userId int64) (bool, error)

	ReportPortalMomentRead(ctx context.Context, userId int64, moment *domain_entities_items.Moment, portal *domain_entities_items.Portal) error

	ListUserCreatedPortalsWithTimeRange(ctx context.Context, userId int64, reqUserId int64, req *api_common_v1.ListRequest) (map[time.Time][]*domain_entities_items.Portal, *api_common_v1.ListResponse, error)
	ListUserCreatedActivePortals(ctx context.Context, userId int64, reqUserId int64, req *api_common_v1.ListRequest) ([]*domain_entities_items.Portal, *api_common_v1.ListResponse, error)

	ListMomentViewers(ctx context.Context, momentId int64, listRequest *api_common_v1.ListRequest) ([]int64, *api_common_v1.ListResponse, error)
}

type PortalService struct {
	uniqIdGenerator         domain_interfaces.UniqIdGenerator
	portalRepository        IPortalRepository
	storyQueryService       *domain_services_items_story.StoryQueryService
	userInfoQueryService    *domain_services_users_info.UsersInfoService
	recommendPortalsService IRecommendPortalsService
	hauntShowInfoService    *domain_services_items_story.HauntBooShowInfoService
	imService               *domain_services_im.IMService
}

func NewPortalService(
	uniqIdGenerator domain_interfaces.UniqIdGenerator,
	portalRepository IPortalRepository,
	storyQueryService *domain_services_items_story.StoryQueryService,
	userInfoQueryService *domain_services_users_info.UsersInfoService,
	recommendPortalsService IRecommendPortalsService,
	hauntShowInfoService *domain_services_items_story.HauntBooShowInfoService,
	imService *domain_services_im.IMService,
) *PortalService {
	return &PortalService{
		uniqIdGenerator:         uniqIdGenerator,
		portalRepository:        portalRepository,
		storyQueryService:       storyQueryService,
		userInfoQueryService:    userInfoQueryService,
		recommendPortalsService: recommendPortalsService,
		hauntShowInfoService:    hauntShowInfoService,
		imService:               imService,
	}
}

func (s *PortalService) DeleteMoment(ctx context.Context, userId int64, momentId int64) error {
	moments, err := s.portalRepository.BatchGetMoments(ctx, userId, []int64{momentId})
	if err != nil {
		return err
	}
	if len(moments) == 0 {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("moment : %d not found", momentId))
	}
	moment := moments[momentId]
	if moment.Creator.ID != userId {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("moment : %d is not owned by user : %d", momentId, userId))
	}
	if moment.Status != api_items_portal_moment_types_v1.Status_STATUS_PUBLISHED {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("moment : %d is not published", momentId))
	}
	if moment.CreateType == api_items_portal_moment_types_v1.MomentCreateType_MOMENT_CREATE_TYPE_INIT {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("moment : %d is init, can not delete", momentId))
	}
	return s.portalRepository.DeleteMoment(ctx, momentId)
}

func (s *PortalService) GetUserCreatedPortalsInfo(
	ctx context.Context,
	userId int64,
) (*CreatedPortalsInfo, error) {
	info := &CreatedPortalsInfo{}
	createdPortals, err := s.portalRepository.ListCreatedPortals(ctx, userId, time.Now())
	if err != nil {
		return nil, err
	}
	info.Portals = createdPortals
	if info.LastReadPortalId, err = s.portalRepository.GetUserLastReadPortalId(ctx, userId); err != nil {
		return nil, err
	}
	if info.HasPostedStories, err = s.portalRepository.GetUserHasPostedStories(ctx, userId); err != nil {
		return nil, err
	}

	return info, nil
}

func (s *PortalService) ListMyCreatedPortalsWithTimeRange(ctx context.Context, userId int64, reqUserId int64, req *api_common_v1.ListRequest) (map[time.Time][]*domain_entities_items.Portal, *api_common_v1.ListResponse, error) {
	return s.portalRepository.ListUserCreatedPortalsWithTimeRange(ctx, userId, reqUserId, req)
}

type CreatedPortalsInfo struct {
	Portals          []*domain_entities_items.Portal
	HasPostedStories bool
	LastReadPortalId *int64
}

func (s *PortalService) ListMomentViewers(ctx context.Context, userId int64, momentId int64, listRequest *api_common_v1.ListRequest) ([]*api_items_portal_moment_types_v1.Viewer, *api_common_v1.ListResponse, error) {
	viewerIds, res, err := s.portalRepository.ListMomentViewers(ctx, momentId, listRequest)
	if err != nil {
		return nil, nil, err
	}
	if len(viewerIds) == 0 {
		return nil, res, nil
	}
	relationsMap, err := s.portalRepository.BatchGetMomentAndUsersRelations(ctx, momentId, viewerIds)
	if err != nil {
		return nil, nil, err
	}

	users, err := s.userInfoQueryService.BatchGetUserInfo(ctx, userId, viewerIds...)
	if err != nil {
		return nil, nil, err
	}
	result := make([]*api_items_portal_moment_types_v1.Viewer, 0, len(viewerIds))
	for _, viewerId := range viewerIds {
		if user, ok := users[viewerId]; ok {
			result = append(result, &api_items_portal_moment_types_v1.Viewer{
				User: adapter_driven_assembler.ConvertUserInfoToSummary(user),
				UserRelations: lo.Map(relationsMap[viewerId], func(relation *domain_entities_items.MomentRelation, _ int) *api_items_portal_moment_types_v1.MomentRelation {
					return &api_items_portal_moment_types_v1.MomentRelation{
						RelationType:       relation.RelationType,
						CreatedAtTimestamp: uint32(time.Now().Unix()),
					}
				}),
			})
		}
	}
	return result, res, nil
}
func (s *PortalService) ReportPortalMomentRead(ctx context.Context, userId int64, portalId int64, momentId int64) error {
	portals, err := s.portalRepository.BatchGetPortals(ctx, userId, []int64{portalId})
	if err != nil {
		return err
	}
	if len(portals) == 0 {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("portal : %d not found", portalId))
	}
	portal := portals[portalId]
	moments, err := s.portalRepository.BatchGetMoments(ctx, userId, []int64{momentId})
	if err != nil {
		return err
	}
	if len(moments) == 0 {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("moment : %d not found", momentId))
	}
	moment := moments[momentId]
	return s.portalRepository.ReportPortalMomentRead(ctx, userId, moment, portal)
}

func (s *PortalService) BatchGetPortalsWithStoryIds(ctx context.Context, userId int64, storyIds []int64) (map[int64]*domain_entities_items.Portal, error) {
	return s.portalRepository.BatchGetPortalsWithStoryIds(ctx, userId, storyIds)
}

func (s *PortalService) BatchGetPortals(ctx context.Context, userId int64, portalIds []int64) (map[int64]*domain_entities_items.Portal, error) {
	portals, err := s.portalRepository.BatchGetPortals(ctx, userId, portalIds)
	if err != nil {
		return nil, err
	}
	if err := s.hauntShowInfoService.InjectMomentHauntBooShowInfo(ctx, userId, lo.Values(portals)...); err != nil {
		return nil, err
	}
	return portals, nil
}

func (s *PortalService) BatchGetMoments(ctx context.Context, userId int64, momentIds []int64) (map[int64]*domain_entities_items.Moment, error) {
	return s.portalRepository.BatchGetMoments(ctx, userId, momentIds)
}

func (s *PortalService) SendMomentInvite(
	ctx context.Context,
	userId int64,
	inviteType api_items_portal_v1.SendMomentInviteRequest_InviteTargetType,
	storyId int64,
	momentId int64,
	receiverIds []int64) error {
	var shareCreateMomentType api_im_message_types_v1.StoryShareCreateMomentCustomMessagePayload_ShareCreateMomentType
	var coverUrl string
	switch inviteType {
	case api_items_portal_v1.SendMomentInviteRequest_INVITE_TARGET_TYPE_STORY:
		shareCreateMomentType = api_im_message_types_v1.StoryShareCreateMomentCustomMessagePayload_SHARE_CREATE_MOMENT_TYPE_STORY
		story, err := s.storyQueryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}
		cover, err := story.Summary.GetStoryCover()
		if err != nil {
			return err
		}
		coverUrl = cover.GetResourceURL()
	case api_items_portal_v1.SendMomentInviteRequest_INVITE_TARGET_TYPE_MOMENT:
		shareCreateMomentType = api_im_message_types_v1.StoryShareCreateMomentCustomMessagePayload_SHARE_CREATE_MOMENT_TYPE_MOMENT
		moments, err := s.BatchGetMoments(ctx, userId, []int64{momentId})
		if err != nil {
			return err
		}
		moment, ok := moments[momentId]
		if !ok {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("moment : %d not found", momentId))
		}
		if moment.ExtraInfo != nil && moment.ExtraInfo.Resource != nil {
			coverUrl = moment.ExtraInfo.Resource.GetObjectAccessURL()
		}
	}

	if err := s.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_SHARE_CREATE_MOMENT.String(),
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryShareCreateMoment{
			StoryShareCreateMoment: &api_im_message_types_v1.StoryShareCreateMomentCustomMessagePayload{
				StoryCoverUrl:         coverUrl,
				StoryId:               fmt.Sprint(storyId),
				MomentId:              fmt.Sprint(momentId),
				ShareCreateMomentType: shareCreateMomentType,
			},
		},
		ConsumeStatus: api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
	}, receiverIds...); err != nil {
		return err
	}
	return nil
}

func (s *PortalService) ListMyPortals(
	ctx context.Context,
	userId int64,
	listRequest *api_common_v1.ListRequest,
) (
	myportals []*domain_entities_items.Portal,
	createdPortalsInfo CreatedPortalsInfo,
	res *api_common_v1.ListResponse,
	err error,
) {
	if createdPortalsInfo.LastReadPortalId, err = s.portalRepository.GetUserLastReadPortalId(ctx, userId); err != nil {
		return nil, createdPortalsInfo, nil, err
	}
	if createdPortalsInfo.HasPostedStories, err = s.portalRepository.GetUserHasPostedStories(ctx, userId); err != nil {
		return nil, createdPortalsInfo, nil, err
	}

	// 如果是第一页，拼接所有 my created portals
	if listRequest.PageToken == "" {
		createdPortals, err := s.portalRepository.ListCreatedPortals(ctx, userId, time.Now())
		if err != nil {
			return nil, createdPortalsInfo, nil, err
		}
		createdPortalsInfo.Portals = createdPortals
	}
	myportals, res, err = s.portalRepository.ListMyPortals(ctx, userId, listRequest)
	if err != nil {
		return nil, createdPortalsInfo, nil, err
	}

	if err := s.hauntShowInfoService.InjectMomentHauntBooShowInfo(ctx, userId, myportals...); err != nil {
		return nil, createdPortalsInfo, nil, err
	}
	return myportals, createdPortalsInfo, res, nil
}

func (s *PortalService) ListCouldAppendMomentPortals(ctx context.Context, userId int64) ([]*domain_entities_items.Portal, error) {
	// 查询还没过期的 Portal
	portals, err := s.portalRepository.ListCreatedPortals(ctx, userId, time.Now())
	if err != nil {
		return nil, err
	}
	return portals, nil
}

func (s *PortalService) AppendMoment(
	ctx context.Context,
	userId int64,
	storyId int64,
	resource *domain_entities_resource.Resource,
	attachmentTexts []*domain_entities_items.AttachedText,
) (*domain_entities_items.Moment, error) {
	portals, err := s.portalRepository.BatchGetPortalsWithStoryIds(ctx, userId, []int64{storyId})
	if err != nil {
		return nil, err
	}

	portal := portals[storyId]
	if portal == nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("portal : %d not found", storyId))
	}
	if portal.Story.Summary.Author.ID != userId {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("portal : %d is not owned by user : %d", storyId, userId))
	}
	// 过期了不允许再发
	if portal.ExpiredAt.Before(time.Now()) {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("portal : %d is expired", storyId))
	}

	moment := &domain_entities_items.Moment{
		Id:       s.uniqIdGenerator.Generate(),
		PortalId: portal.Id,
		ExtraInfo: &domain_entities_items.MomentExtraInfo{
			Resource:        resource,
			AttachmentTexts: attachmentTexts,
		},
		Type: api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_NORMAL,
		Creator: &domain_entities_users.UserSummaryEntity{
			ID: userId,
		},
		CreatedAt: time.Now(),
	}
	if err := s.portalRepository.AppendMoment(ctx, userId, portal, moment, api_items_portal_moment_types_v1.MomentCreateType_MOMENT_CREATE_TYPE_APPEND); err != nil {
		return nil, err
	}
	moments, err := s.portalRepository.BatchGetMoments(ctx, userId, []int64{moment.Id})
	if err != nil {
		return nil, err
	}
	return moments[moment.Id], nil
}

func (s *PortalService) ListTrendingPortals(
	ctx context.Context,
	userId int64,
	listRequest *api_common_v1.ListRequest,
) ([]*domain_entities_items.Portal, *api_common_v1.ListResponse, error) {
	// 判断是否为首页加载
	loadmore := listRequest.PageToken != ""

	// 从 listRequest 中提取 pageSize
	pageSize := int32(20) // 默认值
	if listRequest.PageSize != "" {
		if parsedSize := cast.ToInt32(listRequest.PageSize); parsedSize > 0 {
			pageSize = parsedSize
		}
	}

	// 从推荐服务获取 portal IDs
	portalIds, sortRequestId, hasMore, err := s.recommendPortalsService.RecommendTrendingPortals(ctx, userId, loadmore, pageSize)
	if err != nil {
		return nil, nil, err
	}

	// 如果没有推荐的 portal，返回空结果
	if len(portalIds) == 0 {
		return []*domain_entities_items.Portal{}, &api_common_v1.ListResponse{
			HasMore:       false,
			NextPageToken: "",
		}, nil
	}

	// 批量获取 portal 详情
	portalMap, err := s.portalRepository.BatchGetPortals(ctx, userId, portalIds)
	if err != nil {
		return nil, nil, err
	}

	// 按照推荐顺序排列 portals
	var portals []*domain_entities_items.Portal
	for _, portalId := range portalIds {
		if portal, exists := portalMap[portalId]; exists {
			portals = append(portals, portal)
		}
	}

	// 生成下一页的 page token
	nextPageToken := ""
	if hasMore && len(portals) > 0 {
		nextPageToken = sortRequestId
	}

	if err := s.hauntShowInfoService.InjectMomentHauntBooShowInfo(ctx, userId, portals...); err != nil {
		return nil, nil, err
	}

	return portals, &api_common_v1.ListResponse{
		HasMore:       hasMore,
		NextPageToken: nextPageToken,
	}, nil
}
