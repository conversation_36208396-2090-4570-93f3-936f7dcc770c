package domain_services_items_story

import (
	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_items_story_activity_types_v1 "boson/api/items/story/activity/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	driven_http_types "boson/internal/adapter/driven/http/types"
	adapter_driving_repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_services_users_info "boson/internal/domain/services/users/info"
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type ITemplateQueryRepository interface {
	ListCommonStoryConditionTemplates(ctx context.Context, userId int64, storyPlayType api_items_story_types_v1.StoryPlayType) ([]*domain_entities_items.CommonStoryPlayConditionTemplate, error)
	ListExchangeImageStoryConditionTemplates(ctx context.Context, userId int64) ([]*domain_entities_items.ExchangeImageConditionTemplate, error)
	ListTurtleSoupStoryConditionTemplates(ctx context.Context, userId int64) ([]*domain_entities_items.StoryPlayTurtleSoupConditionTemplate, error)
	ListUnmuteStoryConditionTemplates(ctx context.Context, userId int64) ([]*domain_entities_items.StoryPlayUnmuteConditionTemplate, error)
	GetOneRandomTopic(ctx context.Context, userId int64) (*domain_entities_items.StoryPlayRoastedTopic, error)
}

type CreatedStory struct {
	Story *domain_entities_items.StorySummary
	IsTop bool
}

type CreatedStoryV2 struct {
	Story *domain_entities_items.StoryDetail
	IsTop bool
}
type IStoryQueryRepository interface {
	GetStoryDetail(ctx context.Context, loginUserId int64, storyId int64) (*domain_entities_items.StoryDetail, error)
	BatchGetStoryDetails(ctx context.Context, loginUserId int64, storyIds []int64) (map[int64]*domain_entities_items.StoryDetail, error)
	CacheLLMFramesDescription(ctx context.Context, userId int64, imageKey string, description string, shootingModel string, caption string) error
	RetrieveLLMFramesDescription(ctx context.Context, userId int64, imageKeys []string) ([]string, []string, error)

	BatchGetSummaries(
		ctx context.Context,
		userId int64,
		storyIds []int64,
	) (map[int64]*domain_entities_items.StorySummary, error)

	ListHomePageStory(
		ctx context.Context,
		loginUserId int64,
		req *api_common_v1.ListRequest,
		filterPlayTypes []api_items_story_types_v1.StoryPlayType,
		filterVersion []domain_entities_items.StoryVersion,
	) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error)

	ListSameAuthorStoryWithAnchor(
		ctx context.Context,
		loginUserId int64,
		anchorStoryId int64,
		idDesc bool,
		timeRange time.Duration,
		limit uint32,
		filterPlayTypes []api_items_story_types_v1.StoryPlayType,
		filterPrivacyTypes []api_items_story_types_v1.PrivacyType,
	) ([]int64, error)

	ListUserCreatedStory(
		ctx context.Context,
		loginUserId int64,
		creatorId int64,
		filterPrivacyTypes []api_items_story_types_v1.PrivacyType,
		filterVersion []domain_entities_items.StoryVersion,
		req *api_common_v1.ListRequest,
	) ([]*CreatedStory, *api_common_v1.ListResponse, error)

	ListUserCreatedStoryV2(
		ctx context.Context,
		loginUserId int64,
		creatorId int64,
		filterPrivacyTypes []api_items_story_types_v1.PrivacyType,
		filterVersion []domain_entities_items.StoryVersion,
		req *api_common_v1.ListRequest,
	) ([]*CreatedStoryV2, *api_common_v1.ListResponse, error)

	ListFollowingCreatorStory(
		ctx context.Context,
		loginUserId int64,
		req *api_common_v1.ListRequest,
		filterVersion []domain_entities_items.StoryVersion,
	) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error)

	ListUsersByConsumptionStatus(
		ctx context.Context,
		loginUserId int64,
		storyId int64,
		consumptionStatus api_items_story_activity_types_v1.ConsumptionStatus,
		req *api_common_v1.ListRequest,
	) ([]*domain_entities_users.UserSummaryEntity, *api_common_v1.ListResponse, error)

	ListUnlockedStory(
		ctx context.Context,
		loginUserId int64,
		req *api_common_v1.ListRequest,
		filterVersion []domain_entities_items.StoryVersion,
		filterPlayTypes []api_items_story_types_v1.StoryPlayType,
	) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error)
}
type IRecommendService interface {
	RecommendStories(ctx context.Context, userId int64, loadmore bool) (storyId []int64, sortRequestId string, hasMore bool, err error)
	RecommendUnfollowedCreators(ctx context.Context, userId int64) (items []struct {
		UserId  int64
		StoryId int64
	}, sortRequestId string, err error)
}

type ListSameAuthorStoryWithAnchorAttr struct {
	IdDesc          bool
	Limit           uint32
	FilterPlayTypes []api_items_story_types_v1.StoryPlayType
}
type ListSameAuthorStoryWithAnchorResult struct {
	IdDesc  bool
	Details []*domain_entities_items.StoryDetail
}

type StoryQueryService struct {
	appSettingRepo adapter_driving_repos_app_settings.ConfigStorage
	IStoryQueryRepository
	ITemplateQueryRepository
	IRecommendService
	userInfoService        *domain_services_users_info.UsersInfoService
	haunBooShowInfoService *HauntBooShowInfoService
}

func NewStoryQueryService(
	storyQueryRepository IStoryQueryRepository,
	templateQueryRepository ITemplateQueryRepository,
	recommendService IRecommendService,
	userInfoService *domain_services_users_info.UsersInfoService,
	appSettingRepo adapter_driving_repos_app_settings.ConfigStorage,
	haunBooShowInfoService *HauntBooShowInfoService,
) *StoryQueryService {
	return &StoryQueryService{
		appSettingRepo,
		storyQueryRepository,
		templateQueryRepository,
		recommendService,
		userInfoService,
		haunBooShowInfoService,
	}
}

func (s *StoryQueryService) GetOneRandomTopic(
	ctx context.Context,
	loginUserId int64,
	copyStoryId *int64,
) (*domain_entities_items.StoryPlayRoastedTopic, error) {
	// 2025-06-03 yufeng 要求不再跟随老的 story 返回 topic
	// if copyStoryId != nil {
	// 	story, err := s.IStoryQueryRepository.GetStoryDetail(ctx, loginUserId, *copyStoryId)
	// 	if err != nil {
	// 		return nil, err
	// 	}
	// 	if story.Summary.PlayType != api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED {
	// 		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("story is not roasted topic"))
	// 	}
	// 	// 直接返回 story 的 topic
	// 	return story.Summary.RoastedPlayConfig.Topic, nil
	// }
	return s.ITemplateQueryRepository.GetOneRandomTopic(ctx, loginUserId)
}

func (s *StoryQueryService) GetStoryDetail(
	ctx context.Context,
	loginUserId int64,
	storyId int64,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.IStoryQueryRepository.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return nil, err
	}
	filterPrivacyTypes := s.calculateFilterPrivacyTypesWithCreator(ctx, loginUserId, story.Summary.Author)
	if !lo.Contains(filterPrivacyTypes, story.Summary.PrivacySettings.PrivacyType) {
		// 直接返回 404
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("story not found"))
	}
	return story, nil
}

func (s *StoryQueryService) GetStoryDetailWithStoryType(
	ctx context.Context,
	loginUserId int64,
	storyId int64,
	storyType api_items_story_types_v1.StoryPlayType,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return nil, err
	}
	if story.Summary.PlayType != storyType {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("story type not matched"))
	}
	return story, nil
}

func (s *StoryQueryService) ListUserCreatedStory(
	ctx context.Context,
	loginUserId int64,
	creatorId int64,
	req *api_common_v1.ListRequest,
) ([]*CreatedStory, *api_common_v1.ListResponse, error) {
	filterPrivacyTypes, err := s.calculateFilterPrivacyTypes(ctx, loginUserId, creatorId)
	if err != nil {
		return nil, nil, err
	}
	storyVersions, err := s.getStoryFeedVersion(ctx)
	if err != nil {
		return nil, nil, err
	}
	return s.IStoryQueryRepository.ListUserCreatedStory(ctx, loginUserId, creatorId, filterPrivacyTypes, storyVersions, req)
}

func (s *StoryQueryService) ListUserCreatedStoryV2(
	ctx context.Context,
	loginUserId int64,
	creatorId int64,
	req *api_common_v1.ListRequest,
) ([]*CreatedStoryV2, *api_common_v1.ListResponse, error) {
	filterPrivacyTypes, err := s.calculateFilterPrivacyTypes(ctx, loginUserId, creatorId)
	if err != nil {
		return nil, nil, err
	}
	storyVersions, err := s.getStoryFeedVersion(ctx)
	if err != nil {
		return nil, nil, err
	}
	return s.IStoryQueryRepository.ListUserCreatedStoryV2(ctx, loginUserId, creatorId, filterPrivacyTypes, storyVersions, req)
}

func (s *StoryQueryService) ListSameAuthorStoryWithAnchor(
	ctx context.Context,
	loginUserId int64,
	anchorStoryId int64,
	attrs []*ListSameAuthorStoryWithAnchorAttr,
) ([]*ListSameAuthorStoryWithAnchorResult, error) {
	if len(attrs) > 2 {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("attrs length must be less than 2"))
	}

	anchorStory, err := s.IStoryQueryRepository.GetStoryDetail(ctx, loginUserId, anchorStoryId)
	if err != nil {
		return nil, err
	}

	filterPrivacyTypes, err := s.calculateFilterPrivacyTypes(ctx, loginUserId, anchorStory.Summary.Author.ID)
	if err != nil {
		return nil, err
	}

	var result []*ListSameAuthorStoryWithAnchorResult
	for _, attr := range attrs {
		storyIds, err := s.IStoryQueryRepository.ListSameAuthorStoryWithAnchor(
			ctx,
			loginUserId,
			anchorStoryId,
			attr.IdDesc,
			time.Hour*24,
			attr.Limit,
			attr.FilterPlayTypes,
			filterPrivacyTypes,
		)
		if err != nil {
			return nil, err
		}
		detailsMap, err := s.IStoryQueryRepository.BatchGetStoryDetails(ctx, loginUserId, storyIds)
		if err != nil {
			return nil, err
		}
		details := make([]*domain_entities_items.StoryDetail, 0, len(storyIds))
		for _, storyId := range storyIds {
			if detail, ok := detailsMap[storyId]; ok {
				details = append(details, detail)
			}
		}
		result = append(result, &ListSameAuthorStoryWithAnchorResult{
			IdDesc:  attr.IdDesc,
			Details: details,
		})
	}
	return result, nil
}

func (s *StoryQueryService) ListFollowingCreatorStory(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
) (
	followingCreatorStories []*domain_entities_items.StoryDetail,
	recommendedUnfollowedCreatorStories []*domain_entities_items.StoryDetail,
	listResp *api_common_v1.ListResponse,
	hauntBooShowInfo *domain_entities_items.ShowInfo,
	err error,
) {

	storyVersions, err := s.getStoryFeedVersion(ctx)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	hauntBooShowInfo, err = s.haunBooShowInfoService.getHauntBooShowInfo(ctx, loginUserId, api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION, nil)
	if err != nil {
		return nil, nil, nil, nil, err
	}

	if followingCreatorStories, listResp, err = s.IStoryQueryRepository.ListFollowingCreatorStory(ctx, loginUserId, req, storyVersions); err != nil {
		return nil, nil, nil, nil, err
	}

	// 和别的方法不同，这里不可能通过底层复杂 join 去结算不同的作者隐私设置
	// 直接内存过滤，带来隐患是可能数量不足，短期内是可以接受的
	followingCreatorStories = s.filterDetailStories(ctx, loginUserId, followingCreatorStories)

	if len(followingCreatorStories) == 0 {
		// 关注的人都没有发任何作品，走推荐
		items, _, err := s.IRecommendService.RecommendUnfollowedCreators(ctx, loginUserId)
		if err != nil {
			return nil, nil, nil, nil, err
		}
		storyIds := lo.Map(items, func(item struct {
			UserId  int64
			StoryId int64
		}, _ int) int64 {
			return item.StoryId
		})
		storiesMap, err := s.IStoryQueryRepository.BatchGetStoryDetails(ctx, loginUserId, storyIds)
		if err != nil {
			return nil, nil, nil, nil, err
		}
		for _, item := range items {
			if story, ok := storiesMap[item.StoryId]; ok {
				recommendedUnfollowedCreatorStories = append(recommendedUnfollowedCreatorStories, story)
			}
		}
		recommendedUnfollowedCreatorStories = s.filterDetailStories(ctx, loginUserId, recommendedUnfollowedCreatorStories)
		// 这里走了推荐，额外过滤下，客户端发版后可以删除
		recommendedUnfollowedCreatorStories = s.filterDetailStoriesByVersion(ctx, loginUserId, recommendedUnfollowedCreatorStories, storyVersions)
	}

	if err := s.haunBooShowInfoService.InjectStoryHauntBooShowInfo(ctx, loginUserId, followingCreatorStories...); err != nil {
		return nil, nil, nil, nil, err
	}

	return followingCreatorStories, recommendedUnfollowedCreatorStories, listResp, hauntBooShowInfo, nil
}

func (s *StoryQueryService) ListUnlockedStory(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
	filterPlayTypes []api_items_story_types_v1.StoryPlayType,
) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, error) {
	storyVersions, err := s.getStoryFeedVersion(ctx)
	if err != nil {
		return nil, nil, err
	}
	stories, listResp, err := s.IStoryQueryRepository.ListUnlockedStory(ctx, loginUserId, req, storyVersions, filterPlayTypes)
	if err != nil {
		return nil, nil, err
	}

	return stories, listResp, nil
}

func (s *StoryQueryService) ListHomePageStory(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
	useRecommended bool,
	filterPlayTypes []api_items_story_types_v1.StoryPlayType,
) ([]*domain_entities_items.StoryDetail, *api_common_v1.ListResponse, string, *domain_entities_items.ShowInfo, error) {
	storyVersions, err := s.getStoryFeedVersion(ctx)
	if err != nil {
		return nil, nil, "", nil, err
	}
	hauntSHowInfo, err := s.haunBooShowInfoService.getHauntBooShowInfo(ctx, loginUserId, api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION, nil)
	if err != nil {
		return nil, nil, "", nil, err
	}
	if useRecommended {
		stories, listResp, sortRequestId, err := s.ListHomePageStoryWithRecommended(ctx, loginUserId, req)
		if err != nil {
			return nil, nil, "", nil, err
		}
		// 和别的方法不同，这里不可能通过底层复杂 join 去结算不同的作者隐私设置
		// 直接内存过滤，带来隐患是可能数量不足，短期内是可以接受的
		stories = s.filterDetailStories(ctx, loginUserId, stories)
		// 这里走了推荐，额外过滤下，客户端发版后可以删除
		stories = s.filterDetailStoriesByVersion(ctx, loginUserId, stories, storyVersions)
		return stories, listResp, sortRequestId, hauntSHowInfo, nil
	}

	stories, listResp, err := s.IStoryQueryRepository.ListHomePageStory(ctx, loginUserId, req, filterPlayTypes, storyVersions)
	if err != nil {
		return nil, nil, "", nil, err
	}
	// 和别的方法不同，这里不可能通过底层复杂 join 去结算不同的作者隐私设置
	// 直接内存过滤，带来隐患是可能数量不足，短期内是可以接受的
	stories = s.filterDetailStories(ctx, loginUserId, stories)
	if err := s.haunBooShowInfoService.InjectStoryHauntBooShowInfo(ctx, loginUserId, stories...); err != nil {
		return nil, nil, "", nil, err
	}
	return stories, listResp, "", hauntSHowInfo, nil
}

func (s *StoryQueryService) ListHomePageStoryWithRecommended(ctx context.Context, loginUserId int64, req *api_common_v1.ListRequest) (
	[]*domain_entities_items.StoryDetail,
	*api_common_v1.ListResponse,
	string,
	error,
) {
	storyIds, sortRequestId, hasMore, err := s.IRecommendService.RecommendStories(
		ctx,
		loginUserId,
		req.PageToken != "" && req.PageToken != "0",
	)
	if err != nil {
		return nil, nil, "", err
	}

	storiesMap, err := s.IStoryQueryRepository.BatchGetStoryDetails(ctx, loginUserId, storyIds)
	if err != nil {
		return nil, nil, "", err
	}

	var stories []*domain_entities_items.StoryDetail
	for _, id := range storyIds {
		if story, ok := storiesMap[id]; ok {
			// 注入 sortRequestId
			story.SortRequestId = sortRequestId
			stories = append(stories, story)
		}
	}

	return stories, &api_common_v1.ListResponse{
		NextPageToken: sortRequestId, // 这里返回 sortRequestId 作为 next page token，用于判断是否是 LoadMore
		HasMore:       hasMore,
	}, sortRequestId, nil
}

func (s *StoryQueryService) ListUsersByConsumptionStatus(ctx context.Context, loginUserId int64, storyId int64, consumptionStatus api_items_story_activity_types_v1.ConsumptionStatus, req *api_common_v1.ListRequest,
) ([]*domain_entities_users.UserSummaryEntity, *api_common_v1.ListResponse, error) {
	users, listResp, err := s.IStoryQueryRepository.ListUsersByConsumptionStatus(ctx, loginUserId, storyId, consumptionStatus, req)
	if err != nil {
		return nil, nil, err
	}
	return users, listResp, nil
}

func (s *StoryQueryService) filterDetailStoriesByVersion(ctx context.Context, loginUserId int64, stories []*domain_entities_items.StoryDetail, storyVersions []domain_entities_items.StoryVersion) []*domain_entities_items.StoryDetail {
	return lo.Filter(stories, func(story *domain_entities_items.StoryDetail, _ int) bool {
		return lo.Contains(storyVersions, story.Summary.Version)
	})
}
func (s *StoryQueryService) filterDetailStories(ctx context.Context, loginUserId int64, stories []*domain_entities_items.StoryDetail) []*domain_entities_items.StoryDetail {
	return lo.Filter(stories, func(story *domain_entities_items.StoryDetail, _ int) bool {
		filterPrivacyTypes := s.calculateFilterPrivacyTypesWithCreator(ctx, loginUserId, story.Summary.Author)
		isAfterVisibleBeforeTimestamp := story.Summary.PrivacySettings.VisibleBeforeTimestamp > uint32(time.Now().Unix())
		return lo.Contains(filterPrivacyTypes, story.Summary.PrivacySettings.PrivacyType) && isAfterVisibleBeforeTimestamp
	})
}
func (s *StoryQueryService) calculateFilterPrivacyTypes(ctx context.Context, loginUserId int64, creatorId int64) ([]api_items_story_types_v1.PrivacyType, error) {
	creatorInfo, err := s.userInfoService.GetUserDetail(ctx, loginUserId, creatorId)
	if err != nil {
		return nil, err
	}
	return s.calculateFilterPrivacyTypesWithCreator(ctx, loginUserId, &creatorInfo.Summary), nil
}
func (s *StoryQueryService) calculateFilterPrivacyTypesWithCreator(ctx context.Context, loginUserId int64, creator *domain_entities_users.UserSummaryEntity) []api_items_story_types_v1.PrivacyType {
	// 默认只能拉取公开的
	filterPrivacyTypes := []api_items_story_types_v1.PrivacyType{
		api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_PUBLIC,
	}
	// 如果是同一个人，则不限制
	if loginUserId == creator.ID {
		filterPrivacyTypes = append(
			filterPrivacyTypes,
			api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_PRIVATE,
			api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_FRIEND,
			api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_FOLLOWER,
			api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_ALLOWLIST,
		)
		return filterPrivacyTypes
	}
	// 如果登录用户被作者关注了，则有 friend
	if creator.IsFollower() {
		filterPrivacyTypes = append(
			filterPrivacyTypes,
			api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_FRIEND,
		)
	}
	if creator.IsFollowing() {
		filterPrivacyTypes = append(
			filterPrivacyTypes,
			api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_FOLLOWER,
		)
	}
	return filterPrivacyTypes
}

func (s *StoryQueryService) getStoryFeedVersion(ctx context.Context) ([]domain_entities_items.StoryVersion, error) {
	type FeedConfig struct {
		MinV2Version driven_http_types.AppVersion `json:"min_v2_version"`
	}
	cfg, err := adapter_driving_repos_app_settings.Get[FeedConfig](ctx, adapter_driving_repos_app_settings.StoryFeedVersion, s.appSettingRepo)
	if err != nil {
		return nil, err
	}
	if driven_http_types.AppVersionIsGreaterThanOrEqualFromContext(ctx, cfg.MinV2Version) {
		return []domain_entities_items.StoryVersion{domain_entities_items.StoryVersion_V2}, nil
	}
	return []domain_entities_items.StoryVersion{domain_entities_items.StoryVersion_V1}, nil
}
