package domain_services_items_story

import (
	api_common_v1 "boson/api/common/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	"context"
	"time"
)

type CollectStickerEntity struct {
	Sticker     *domain_entities_items.HideSticker
	CollectedAt time.Time
	IsTop       bool
}
type IHideStickerRepository interface {
	CreateStickers(ctx context.Context, userId int64, stickers ...*domain_entities_items.HideSticker) error
	CollectSticker(ctx context.Context, userId int64, stickerIds ...int64) error
	UnCollectSticker(ctx context.Context, userId int64, stickerIds ...int64) error
	TopSticker(ctx context.Context, userId int64, stickerIds ...int64) error
	UnTopSticker(ctx context.Context, userId int64, stickerIds ...int64) error
	ListMyCollectedStickers(ctx context.Context, userId int64, listRequest *api_common_v1.ListRequest) (*api_common_v1.ListResponse, []*CollectStickerEntity, error)
	BatchetGetWithAvatarIds(ctx context.Context, userId int64, avatarIds ...int64) (map[int64]*domain_entities_items.HideSticker, error)
}

type HideStickerService struct {
	repo IHideStickerRepository
}

func NewHideStickerService(repo IHideStickerRepository) *HideStickerService {
	return &HideStickerService{repo: repo}
}

func (s *HideStickerService) BatchetGetWithAvatarIds(ctx context.Context, userId int64, avatarIds ...int64) (map[int64]*domain_entities_items.HideSticker, error) {
	return s.repo.BatchetGetWithAvatarIds(ctx, userId, avatarIds...)
}
func (s *HideStickerService) CreateStickers(ctx context.Context, userId int64, stickers ...*domain_entities_items.HideSticker) error {
	return s.repo.CreateStickers(ctx, userId, stickers...)
}
func (s *HideStickerService) CollectStickers(ctx context.Context, userId int64, stickerIds ...int64) error {
	return s.repo.CollectSticker(ctx, userId, stickerIds...)
}
func (s *HideStickerService) UnCollectStickers(ctx context.Context, userId int64, stickerIds ...int64) error {
	return s.repo.UnCollectSticker(ctx, userId, stickerIds...)
}
func (s *HideStickerService) TopStickers(ctx context.Context, userId int64, stickerIds ...int64) error {
	return s.repo.TopSticker(ctx, userId, stickerIds...)
}
func (s *HideStickerService) UnTopStickers(ctx context.Context, userId int64, stickerIds ...int64) error {
	return s.repo.UnTopSticker(ctx, userId, stickerIds...)
}
func (s *HideStickerService) ListMyCollectedStickers(ctx context.Context, userId int64, listRequest *api_common_v1.ListRequest) (*api_common_v1.ListResponse, []*CollectStickerEntity, error) {
	return s.repo.ListMyCollectedStickers(ctx, userId, listRequest)
}
