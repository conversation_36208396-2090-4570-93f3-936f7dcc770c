package domain_services_items_story

import (
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_user "boson/internal/domain/entities/users"
	"context"
	"fmt"
	"time"

	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	domain_services_users_info "boson/internal/domain/services/users/info"
	emojiPkg "boson/pkg/emoji"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type IStoryReactionRepo interface {
	ListReactionMadeUsers(
		ctx context.Context,
		userId int64,
		storyId int64,
		emojis []string,
		req *api_common_v1.ListRequest,
	) ([]int64, *api_common_v1.ListResponse, error)

	CreateReaction(
		ctx context.Context,
		reaction *domain_entities_items.StoryReaction,
	) error
	DeleteReaction(
		ctx context.Context,
		storyId int64,
		userId int64,
		emoji string,
	) error
	ListActivities(
		ctx context.Context,
		userId int64,
		req *api_common_v1.ListRequest,
	) ([]*domain_entities_items.ActivityItem, *api_common_v1.ListResponse, error)
	GetActivityUnreadCount(
		ctx context.Context,
		userId int64,
	) (int64, error)
	MarkActivitiesAsRead(
		ctx context.Context,
		userId int64,
	) error
}

type StoryReactionService struct {
	userQueryService  *domain_services_users_info.UsersInfoService
	storyQueryService *StoryQueryService
	repo              IStoryReactionRepo
}

func NewStoryReactionService(
	userQueryService *domain_services_users_info.UsersInfoService,
	storyQueryService *StoryQueryService,
	repo IStoryReactionRepo,
) *StoryReactionService {
	return &StoryReactionService{
		userQueryService:  userQueryService,
		storyQueryService: storyQueryService,
		repo:              repo,
	}
}

func (s *StoryReactionService) ListReactionMadeUsers(
	ctx context.Context,
	loginUserId int64,
	storyId int64,
	emojis []string,
	req *api_common_v1.ListRequest,
) ([]*domain_entities_user.UserSummaryEntity, *api_common_v1.ListResponse, error) {
	userIds, listResponse, err := s.repo.ListReactionMadeUsers(ctx, loginUserId, storyId, emojis, req)
	if err != nil {
		return nil, nil, err
	}
	userSummaries, err := s.userQueryService.BatchGetUserInfo(ctx, loginUserId, userIds...)
	if err != nil {
		return nil, nil, err
	}
	var result []*domain_entities_user.UserSummaryEntity
	for _, userId := range userIds {
		if userInfo, ok := userSummaries[userId]; ok {
			result = append(result, userInfo)
		}
	}
	return result, listResponse, nil
}

func (s *StoryReactionService) DeleteReaction(
	ctx context.Context,
	loginUserId int64,
	storyId int64,
	emoji string,
) (*domain_entities_items.StoryDetail, error) {
	if !emojiPkg.IsOnlyOneEmoji(emoji) {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("emoji must be a single character"))
	}
	story, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return nil, err
	}
	if !story.Summary.IsLoginUserMadeReactionMade(emoji) {
		return story, nil
	}
	reaction := lo.Filter(story.Summary.LoginUserMadeReactions, func(reaction *domain_entities_items.StoryReaction, _ int) bool {
		return reaction.EmojiStr == emoji
	})
	if len(reaction) == 0 {
		err := fmt.Errorf("reaction not found")
		return nil, errors.WithStack(err)
	}
	if err = s.repo.DeleteReaction(ctx, storyId, loginUserId, emoji); err != nil {
		return nil, err
	}
	storyAfterReaction, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return nil, err
	}
	return storyAfterReaction, nil
}


// ListActivities returns activities for the login user
func (s *StoryReactionService) ListActivities(
	ctx context.Context,
	loginUserId int64,
	req *api_common_v1.ListRequest,
) ([]*domain_entities_items.ActivityItem, *api_common_v1.ListResponse, error) {
	return s.repo.ListActivities(ctx, loginUserId, req)
}

// GetActivityUnreadCount returns the unread activity count for the login user
func (s *StoryReactionService) GetActivityUnreadCount(
	ctx context.Context,
	loginUserId int64,
) (int64, error) {
	return s.repo.GetActivityUnreadCount(ctx, loginUserId)
}

// MarkActivitiesAsRead marks all activities as read for the login user
func (s *StoryReactionService) MarkActivitiesAsRead(
	ctx context.Context,
	loginUserId int64,
) error {
	return s.repo.MarkActivitiesAsRead(ctx, loginUserId)
}

func (s *StoryReactionService) CreateReaction(
	ctx context.Context,
	userId int64,
	storyId int64,
	emoji string,
	comment string,
) (*domain_entities_items.StoryDetail, error) {
	if !emojiPkg.IsOnlyOneEmoji(emoji) {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("emoji must be a single character"))
	}
	story, err := s.storyQueryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	if story.Summary.IsLoginUserMadeReactionMade(emoji) {
		return story, nil
	}

	switch emoji {
	case "🔥":
		// ensure story_author_id is set for like activities
		err = s.createLikeReaction(ctx, userId, storyId, story.Summary.Author.ID, comment)
	default:
		// ensure story_author_id is set for other reactions as well
		reaction := domain_entities_items.StoryReaction{
			StoryId:       storyId,
			StoryAuthorId: story.Summary.Author.ID,
			UserId:        userId,
			EmojiStr:      emoji,
			Comment:       comment,
			CreatedAt:     time.Now(),
		}
		err = s.repo.CreateReaction(ctx, &reaction)
	}
	if err != nil {
		return nil, err
	}

	storyAfterReaction, err := s.storyQueryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return storyAfterReaction, nil
}

func (s *StoryReactionService) createLikeReaction(
	ctx context.Context,
	userId int64,
	storyId int64,
	storyAuthorId int64,
	comment string,
) error {
	reaction := domain_entities_items.StoryReaction{
		StoryId:       storyId,
		StoryAuthorId: storyAuthorId,
		UserId:        userId,
		EmojiStr:      "🔥",
		Comment:       comment,
		CreatedAt:     time.Now(),
	}
	return s.repo.CreateReaction(ctx, &reaction)
}
