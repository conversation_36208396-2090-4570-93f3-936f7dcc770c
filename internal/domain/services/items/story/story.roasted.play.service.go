package domain_services_items_story

import (
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_ai "boson/internal/domain/services/ai"
	domain_services_ai_types "boson/internal/domain/services/ai/types"
	"context"
	"fmt"
	"strings"

	"github.com/samber/lo"
)

func (s *StoryPlayService) ConsumeRoastedTopic(
	ctx context.Context,
	userId int64,
	storyId *int64,
	userAudioKey string,
	currentFollowQuestionCount uint32, // 当前节点下，这是第几个追问，如果不是追问，则为 0 ，
	totalVideoDurationSeconds uint32, // 目前用户总共拍摄了多久的视频，单位秒
	question *domain_entities_items.StoryPlayRoastedQuestion,
) (*domain_entities_items.StoryPlayRoastedQuestion, error) {
	if storyId != nil {
		if _, err := s.PlayRoastedStory(ctx, userId, *storyId); err != nil {
			return nil, err
		}
	}
	userAnswer, _, err := s.asrService.Transcribe(ctx, userAudioKey, nil, nil)
	if err != nil {
		return nil, err
	}
	roasted_question, isStop, err := s.llmService.GenerateRoastedR2(ctx, question.Thinking, question.Question, userAnswer, currentFollowQuestionCount, totalVideoDurationSeconds)
	if err != nil {
		return nil, err
	}
	if isStop {
		return nil, nil
	}

	type ParsedLine struct {
		Text string
		Tag  string
	}

	var parsedLines []ParsedLine

	// 1. Split the input string into individual lines.
	lines := strings.Split(roasted_question, "\n")

	// 2. Process each line to extract the text and the tag from the end.
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			continue // Skip any empty lines that might result from splitting.
		}

		// Find the last '[' which marks the beginning of the tag.
		lastOpenBracket := strings.LastIndex(trimmedLine, "[")
		// Find the last ']' which marks the end of the tag.
		lastCloseBracket := strings.LastIndex(trimmedLine, "]")

		// Basic validation to ensure we found a pair of brackets in the correct order.
		if lastOpenBracket == -1 || lastCloseBracket == -1 || lastOpenBracket > lastCloseBracket {
			// This line does not conform to the "Text [Tag]" format.
			// You can choose to log this event or handle it as an error.
			// For this example, we'll skip malformed lines.
			continue
		}

		// Extract the text part (everything before the last '[') and trim whitespace.
		text := strings.TrimSpace(trimmedLine[:lastOpenBracket])

		// Extract the tag part (everything between the brackets) and trim whitespace.
		tag := strings.TrimSpace(trimmedLine[lastOpenBracket+1 : lastCloseBracket])

		// Only add the parsed line if both text and tag are non-empty.
		if text != "" && tag != "" {
			parsedLines = append(parsedLines, ParsedLine{Text: text, Tag: tag})
		}
	}

	// 3. Now, build the two separate strings from our parsed data.
	var ttsParts []string
	var frontendParts []string

	for _, p := range parsedLines {
		// Add the clean text to the list for the TTS string.
		ttsParts = append(ttsParts, p.Text)

		// Format the line back to the OLD format "[Tag] Text" for the frontend.
		frontendLine := fmt.Sprintf("%s [%s]", p.Text, p.Tag)
		frontendParts = append(frontendParts, frontendLine)
	}

	ttsQuestion := strings.Join(ttsParts, "\n<break time=\"0.4s\" />\n")
	frontendQuestion := strings.Join(frontendParts, "\\n")

	// 生成音频
	ttsResult, words, err := s.ttsService.TextToSpeechWithCDNURL(
		ctx,
		userId,
		ttsQuestion,
		domain_services_ai.RoastedStoryNextQuestion,
	)
	if err != nil {
		return nil, err
	}

	new_thinking := question.Thinking // now used as conversation history
	round := currentFollowQuestionCount
	round_str := fmt.Sprintf("[Round %d]", round)
	new_thinking = new_thinking + "\n\n" + round_str
	new_thinking = new_thinking + "Question: " + question.Question + "\n" +
		"User Answer: " + userAnswer + "\n"

	return &domain_entities_items.StoryPlayRoastedQuestion{
		Question:    frontendQuestion,
		Thinking:    new_thinking,
		TTSAudioKey: ttsResult,
		Words: lo.Map(words, func(word *domain_services_ai_types.WordAttr, _ int) domain_entities_items.Word {
			return domain_entities_items.Word{
				Text:      word.Word,
				StartTime: word.StartTime,
				EndTime:   word.EndTime,
			}
		}),
	}, nil
}
