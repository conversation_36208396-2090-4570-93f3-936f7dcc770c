package domain_services_items_story

import (
	"github.com/google/wire"

	domain_services_users_notifications "boson/internal/domain/services/users/nofications"
)

var ProviderSet = wire.NewSet(
	NewStoryCmdService,
	NewStoryPlayService,
	NewStoryQueryService,
	NewStoryReactionService,
	NewHideStickerService,
	NewHauntBooShowInfoService,
	wire.Bind(new(domain_services_users_notifications.IStoryQueryService), new(*StoryQueryService)),
)
