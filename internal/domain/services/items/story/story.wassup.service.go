package domain_services_items_story

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_ai "boson/internal/domain/services/ai"
	domain_services_ai_types "boson/internal/domain/services/ai/types"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
)

var (
	wassupCacheKey = "wassup_user_%d_history_msgs"
)

func (s *StoryPlayService) GetWassupNextQuestion(ctx context.Context, userId int64, userAudioKey domain_entities_resource.AudioResourcePath) (*domain_entities_items.StoryPlayWassupBotMessage, bool, error) {
	// 拿到历史记录
	historyMsgKey := fmt.Sprintf(wassupCacheKey, userId)
	historyMsgsJson, err := s.data.Rdb().Get(ctx, historyMsgKey).Result()
	if err != nil {
		return nil, false, errors.Wrapf(err, "user : %d", userId)
	}
	var historyMsgs []openai.ChatCompletionMessage
	if err := json.Unmarshal([]byte(historyMsgsJson), &historyMsgs); err != nil {
		return nil, false, errors.Wrapf(err, "user : %d", userId)
	}
	// asr
	audioAsrResult, _, err := s.asrService.Transcribe(ctx, string(userAudioKey), nil, nil)
	if err != nil {
		return nil, false, err
	}
	res, historyMsgsAfterCall, err := s.llmService.GenerateStoryCoPilotResponse(ctx, &domain_services_ai.StoryCoPilotRequest{
		UserNarration: audioAsrResult,
	}, historyMsgs)
	if err != nil {
		return nil, false, err
	}
	// 更新历史记录
	historyMsgsAfterCallJson, err := json.Marshal(historyMsgsAfterCall)
	if err != nil {
		return nil, false, errors.Wrapf(err, "user : %d", userId)
	}
	_, err = s.data.Rdb().Set(ctx, historyMsgKey, historyMsgsAfterCallJson, time.Minute*30).Result()
	if err != nil {
		return nil, false, errors.Wrapf(err, "user : %d", userId)
	}

	resultMsg, err := s.buildWassupMessages(ctx, userId, res)
	if err != nil {
		return nil, false, err
	}
	return resultMsg, res.End, nil
}
func (s *StoryPlayService) ConsumeWassupStory(
	ctx context.Context,
	userId int64,
	storyId int64,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	playContext := story.WassupPlayContext
	playContext.IsUnlocked = true
	if err := s.IStoryPlayRecordRepo.SaveWassupPlayRecord(ctx, storyId, userId, *playContext); err != nil {
		return nil, err
	}

	story, err = s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}
func (s *StoryCmdService) CreateWassupStory(ctx context.Context, userId int64, attr CreateWassupStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = userId
	attr.Version = lo.ToPtr(int32(domain_entities_items.StoryVersion_V2))

	if err := s.IStoryCmdRepository.CreateWassupStory(ctx, attr); err != nil {
		return nil, err
	}

	story, err := s.storyQueryService.GetStoryDetail(ctx, userId, attr.Id)
	if err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) GetWassupGreetings(ctx context.Context, userId int64, imageKeys []string, audioAsrResult string) (*domain_entities_items.StoryPlayWassupBotMessage, error) {
	// 清空历史记录
	historyMsgKey := fmt.Sprintf("wassup_user_%d_history_msgs", userId)
	_, err := s.data.Rdb().Del(ctx, historyMsgKey).Result()
	if err != nil {
		return nil, errors.Wrapf(err, "user : %d", userId)
	}

	res, historyMsgs, err := s.llmService.GenerateStoryCoPilotResponse(ctx, &domain_services_ai.StoryCoPilotRequest{
		ImageKey:      domain_entities_resource.ImageResourcePath(imageKeys[0]),
		UserNarration: audioAsrResult,
	}, nil)
	if err != nil {
		return nil, err
	}
	// 缓存当前的记录
	historyMsgsJson, err := json.Marshal(historyMsgs)
	if err != nil {
		return nil, errors.Wrapf(err, "user : %d", userId)
	}
	// 只缓存 30min
	_, err = s.data.Rdb().Set(ctx, historyMsgKey, historyMsgsJson, time.Minute*30).Result()
	if err != nil {
		return nil, errors.Wrapf(err, "user : %d", userId)
	}

	return s.buildWassupMessages(ctx, userId, res)
}

func (s *StoryPlayService) buildWassupMessages(ctx context.Context, userId int64, llmRes domain_services_ai.StoryCoPilotResponse) (*domain_entities_items.StoryPlayWassupBotMessage, error) {
	type ParsedLine struct {
		Text string
		Tag  string
	}

	var parsedLines []ParsedLine

	// 2. Process each line to extract the text and the tag from the end.
	for _, line := range llmRes.Response {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			continue // Skip any empty lines that might result from splitting.
		}

		// Find the last '[' which marks the beginning of the tag.
		lastOpenBracket := strings.LastIndex(trimmedLine, "[")
		// Find the last ']' which marks the end of the tag.
		lastCloseBracket := strings.LastIndex(trimmedLine, "]")

		// Basic validation to ensure we found a pair of brackets in the correct order.
		if lastOpenBracket == -1 || lastCloseBracket == -1 || lastOpenBracket > lastCloseBracket {
			// This line does not conform to the "Text [Tag]" format.
			// You can choose to log this event or handle it as an error.
			// For this example, we'll skip malformed lines.
			continue
		}

		// Extract the text part (everything before the last '[') and trim whitespace.
		text := strings.TrimSpace(trimmedLine[:lastOpenBracket])

		// Extract the tag part (everything between the brackets) and trim whitespace.
		tag := strings.TrimSpace(trimmedLine[lastOpenBracket+1 : lastCloseBracket])

		// Only add the parsed line if both text and tag are non-empty.
		if text != "" && tag != "" {
			parsedLines = append(parsedLines, ParsedLine{Text: text, Tag: tag})
		}
	}

	// 3. Now, build the two separate strings from our parsed data.
	var ttsParts []string
	var frontendParts []string

	for _, p := range parsedLines {
		// Add the clean text to the list for the TTS string.
		ttsParts = append(ttsParts, p.Text)

		// Format the line back to the OLD format "[Tag] Text" for the frontend.
		frontendLine := fmt.Sprintf("%s [%s]", p.Text, p.Tag)
		frontendParts = append(frontendParts, frontendLine)
	}

	ttsQuestion := strings.Join(ttsParts, "\n<break time=\"0.4s\" />\n")
	frontendQuestion := strings.Join(frontendParts, "\\n")

	// 生成音频
	ttsResult, words, err := s.ttsService.TextToSpeechWithCDNURL(
		ctx,
		userId,
		ttsQuestion,
		domain_services_ai.WassupCopilot,
	)
	if err != nil {
		return nil, err
	}
	return &domain_entities_items.StoryPlayWassupBotMessage{
		Content:     frontendQuestion,
		TtsAudioUrl: ttsResult.URL(),
		Words: lo.Map(words, func(word *domain_services_ai_types.WordAttr, _ int) *api_items_story_types_v1.Word {
			return &api_items_story_types_v1.Word{
				Text:      word.Word,
				StartTime: word.StartTime,
				EndTime:   word.EndTime,
			}
		}),
	}, nil
}
