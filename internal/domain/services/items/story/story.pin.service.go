package domain_services_items_story

import (
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_ai "boson/internal/domain/services/ai"
	"context"

	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"
)

type CreatePinStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPinConfig
}

func (s *StoryPlayService) ConsumePinStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	success bool,
	failedImage *domain_entities_resource.Resource,
	successCostSeconds uint32,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	story.PinPlayContext.IsUnlocked = success
	if !success {
		story.PinPlayContext.FailedImage = failedImage
	}
	story.PinPlayContext.SuccessCostSeconds = successCostSeconds
	if err := s.IStoryPlayRecordRepo.SavePinPlayRecord(ctx, storyId, userId, *story.PinPlayContext); err != nil {
		return nil, err
	}
	return s.queryService.GetStoryDetail(ctx, userId, storyId)
}

func (s *StoryCmdService) CreatePinStory(ctx context.Context, loginUserId int64, attr CreatePinStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	attr.Version = lo.ToPtr(int32(domain_entities_items.StoryVersion_V2))
	if err := s.IStoryCmdRepository.CreatePinStory(ctx, attr); err != nil {
		return nil, err
	}
	return s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
}

func (s *StoryPlayService) PinAutoGenerateEmoji(ctx context.Context, userId int64, image domain_entities_resource.ImageResourcePath) ([]EmojiWithArea, error) {
	parsing, err := s.llmService.GeneratePinSceneParsing(ctx, &domain_services_ai.PinSceneParsingRequest{
		ImageKey: image,
	})
	if err != nil {
		return nil, err
	}
	return lo.Map(parsing, func(item domain_services_ai.PinSceneParsingEntryNewFormat, _ int) EmojiWithArea {
		return EmojiWithArea{
			Emoji: item.Emoji,
			Area: domain_entities_items.Area{
				X:      item.CenterX,
				Y:      item.CenterY,
				Width:  item.Width,
				Height: item.Height,
			},
		}
	}), nil
}

type EmojiWithArea struct {
	Emoji string
	Area  domain_entities_items.Area
}
type ImageWithArea struct {
	Image domain_entities_resource.ImageResourcePath
	Areas domain_entities_items.Area
}

// 手动生成 emoji
func (s *StoryPlayService) PinManualGenerateEmoji(ctx context.Context, userId int64, image domain_entities_resource.ImageResourcePath, areas []domain_entities_items.Area) ([]ImageWithArea, error) {
	dataChan := make(chan ImageWithArea, len(areas))
	var errgroup errgroup.Group
	for _, area := range areas {
		area := area
		errgroup.Go(func() error {
			grounding, err := s.llmService.GeneratePinObjectEmoji(ctx, &domain_services_ai.PinObjectEmojiRequest{
				ImageKey: image,
				CenterX:  area.X,
				CenterY:  area.Y,
				Width:    area.Width,
				Height:   area.Height,
			})
			if err != nil {
				return err
			}
			dataChan <- ImageWithArea{
				Image: grounding.ImageKey,
				Areas: area,
			}
			return nil
		})
	}
	if err := errgroup.Wait(); err != nil {
		return nil, err
	}
	close(dataChan)

	var result []ImageWithArea
	for data := range dataChan {
		result = append(result, data)
	}
	return result, nil
}
