package domain_services_items_story

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"strings"

	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_items_story_v2 "boson/api/items/story/v2"
	adapter_driving_repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_ai "boson/internal/domain/services/ai"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
)

type StoryBaseCreateAttr struct {
	Id              int64
	CreatorID       int64
	CoverImage      domain_entities_items.StoryCoverImage
	PrivacySettings *PrivacySettingUpdateAttr
	UserMoments     []*domain_entities_items.MomentExtraInfo
	// default value is v1
	Version *domain_entities_items.StoryVersion
	Status  *api_items_story_types_v1.StoryStatus
}
type CreateImageExchangeStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.ExchanegImagePlayConfig
}

type CreateRoastedStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayRoastedConfig
}
type CreateTurtleSoupStoryAttr struct {
	StoryBaseCreateAttr
	IsMass     bool
	PlayConfig *domain_entities_items.StoryPlayTurtleSoupConfig
}

type CreateUnmuteStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayUnmuteConfig
}

type CreateBasePlayStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayBasePlayConfig
}

type CreateNowShotStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayNowShotConfig
}

type WassupSummaryInput struct {
	ScreenShots []*domain_entities_resource.ImageResourcePath
	Asr         string
}

type CreateWassupStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayWassupConfig
}

type CreateCapsuleStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayCapsuleConfig
}

type PrivacySettingUpdateAttr struct {
	PrivacyType            *api_items_story_types_v1.PrivacyType
	VisibleBeforeTimestamp *uint32
	AllowlistUserIds       []int64
}

type UpdateStoryAttr struct {
	PrivacySettings *PrivacySettingUpdateAttr
}

type IStoryCmdRepository interface {
	CreateImageExchangeStory(ctx context.Context, attr CreateImageExchangeStoryAttr) error
	CreateTurtleSoupStory(ctx context.Context, attr CreateTurtleSoupStoryAttr) error
	CreateUnmuteStory(ctx context.Context, attr CreateUnmuteStoryAttr) error
	CreateBasePlayStory(ctx context.Context, attr CreateBasePlayStoryAttr) error
	CreateNowShotStory(ctx context.Context, attr CreateNowShotStoryAttr) error
	CreateRoastedStory(ctx context.Context, attr CreateRoastedStoryAttr) error
	CreateCapsuleStory(ctx context.Context, attr CreateCapsuleStoryAttr) error
	CreateHideStory(ctx context.Context, attr CreateHideStoryAttr) error
	CreateChatProxyStory(ctx context.Context, attr CreateChatProxyStoryAttr) error
	CreateWhoStory(ctx context.Context, attr CreateWhoStoryAttr) error
	CreateWassupStory(ctx context.Context, attr CreateWassupStoryAttr) error
	CreateHauntStory(ctx context.Context, attr CreateHauntStoryAttr) error
	CreatePinStory(ctx context.Context, attr CreatePinStoryAttr) error

	UpdateHauntStoryConfigAndStatus(ctx context.Context, storyId int64, config *domain_entities_items.HauntStoryConfig, status api_items_story_types_v1.StoryStatus) error

	TopStory(ctx context.Context, userId int64, storyId int64, isTop bool) error
	DeleteStory(ctx context.Context, userId int64, storyId int64) error
	UpdateStory(ctx context.Context, userId int64, storyId int64, attr UpdateStoryAttr) error

	UpdateCoverImageKeyAndSize(ctx context.Context, storyId int64, key domain_entities_resource.ImageResourcePath, width uint32, height uint32) error
	IncreaseShareStat(ctx context.Context, storyId int64) error
}

type StoryCmdService struct {
	IStoryCmdRepository

	uniqIdGen          domain_interfaces.UniqIdGenerator
	contextService     *StoryPlayService
	storyQueryService  *StoryQueryService
	logger             *log.Helper
	txManager          domain_interfaces.TransactionManager
	hideStickerService *HideStickerService
	ttsService         *domain_services_ai.TtsService
	configStorage      adapter_driving_repos_app_settings.ConfigStorage
	llmService         *domain_services_ai.LlmService
	booRepository      IBooRepository
	avatarRepository   IAvatarRepository
	booAIService       *domain_services_ai.BooAIService
}

func NewStoryCmdService(
	uniqIdGen domain_interfaces.UniqIdGenerator,
	storyCmdRepo IStoryCmdRepository,
	contextService *StoryPlayService,
	storyQueryService *StoryQueryService,
	logger *log.Helper,
	txManager domain_interfaces.TransactionManager,
	hideStickerService *HideStickerService,
	ttsService *domain_services_ai.TtsService,
	configStorage adapter_driving_repos_app_settings.ConfigStorage,
	llmService *domain_services_ai.LlmService,
	booRepository IBooRepository,
	avatarRepository IAvatarRepository,
	booAIService *domain_services_ai.BooAIService,
) *StoryCmdService {
	return &StoryCmdService{
		uniqIdGen:           uniqIdGen,
		IStoryCmdRepository: storyCmdRepo,
		contextService:      contextService,
		storyQueryService:   storyQueryService,
		logger:              logger,
		txManager:           txManager,
		hideStickerService:  hideStickerService,
		ttsService:          ttsService,
		configStorage:       configStorage,
		llmService:          llmService,
		booRepository:       booRepository,
		avatarRepository:    avatarRepository,
		booAIService:        booAIService,
	}
}

func (s *StoryCmdService) UpdateStory(ctx context.Context, loginUserId int64, storyId int64, attr UpdateStoryAttr) (*domain_entities_items.StoryDetail, error) {
	story, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return nil, err
	}
	if story.Summary.Author.ID != loginUserId {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("you are not the creator of this story"))
	}
	if err := s.IStoryCmdRepository.UpdateStory(ctx, loginUserId, storyId, attr); err != nil {
		return nil, err
	}
	return s.storyQueryService.GetStoryDetail(ctx, loginUserId, storyId)
}

func (s *StoryCmdService) DeleteStory(ctx context.Context, loginUserId int64, storyId int64) error {
	// check if has permission
	story, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return err
	}
	if story.Summary.Author.ID != loginUserId {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("you are not the creator of this story"))
	}
	return s.IStoryCmdRepository.DeleteStory(ctx, loginUserId, storyId)
}

func (s *StoryCmdService) TopStory(ctx context.Context, loginUserId int64, storyId int64, isTop bool) error {
	story, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return err
	}
	if story.Summary.Author.ID != loginUserId {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonPermissionDenied("you are not the creator of this story"))
	}
	return s.IStoryCmdRepository.TopStory(ctx, loginUserId, storyId, isTop)
}

func (s *StoryCmdService) CreateRoastedStory(ctx context.Context, loginUserId int64, attr CreateRoastedStoryAttr) (*domain_entities_items.StoryDetail, error) {
	var fromStory *domain_entities_items.StoryDetail
	var err error
	if attr.PlayConfig.FromStoryID != nil && *attr.PlayConfig.FromStoryID != 0 {
		fromStory, err = s.storyQueryService.GetStoryDetail(ctx, loginUserId, *attr.PlayConfig.FromStoryID)
		if err != nil {
			return nil, err
		}
		if fromStory.Summary.PlayType != api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED {
			return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("from_story_id must be roasted story"))
		}
	}
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateRoastedStory(ctx, attr); err != nil {
		return nil, err
	}
	createdStory, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, createdStory)
	if attr.PlayConfig.FromStoryID != nil && fromStory != nil {
		newContext := domain_entities_items.StoryPlayRoastedContext{
			IsConsumed: true,
		}
		if err := s.contextService.SaveRoastedPlayRecord(ctx, *attr.PlayConfig.FromStoryID, loginUserId, newContext); err != nil {
			return nil, err
		}
	}
	return createdStory, nil
}

func (s *StoryCmdService) CreateBasePlayStory(ctx context.Context, loginUserId int64, attr CreateBasePlayStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateBasePlayStory(ctx, attr); err != nil {
		return nil, err
	}
	detail, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, detail)
	return detail, nil
}

func (s *StoryCmdService) CreateImageExchangeStory(ctx context.Context, loginUserId int64, attr CreateImageExchangeStoryAttr) (*domain_entities_items.StoryDetail, error) {
	/*if err := s.checkExchangeImageConfig(ctx, attr); err != nil {
		return nil, err
	}*/
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateImageExchangeStory(ctx, attr); err != nil {
		return nil, err
	}
	detail, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	if detail.Summary.StoryCoverImage == nil || detail.Summary.StoryCoverImage.Width == 0 || detail.Summary.StoryCoverImage.Height == 0 {
		s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, detail)
	}
	return detail, nil
}

func (s *StoryCmdService) CreateTurtleSoupStory(ctx context.Context, loginUserId int64, attr CreateTurtleSoupStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateTurtleSoupStory(ctx, attr); err != nil {
		return nil, err
	}
	detail, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, detail)
	return detail, nil
}

func (s *StoryCmdService) CreateUnmuteStory(ctx context.Context, loginUserId int64, attr CreateUnmuteStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateUnmuteStory(ctx, attr); err != nil {
		return nil, err
	}
	detail, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, detail)
	return detail, nil
}

func (s *StoryCmdService) CreateNowShotStory(ctx context.Context, loginUserId int64, attr CreateNowShotStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateNowShotStory(ctx, attr); err != nil {
		return nil, err
	}
	detail, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	if detail.Summary.StoryCoverImage == nil || detail.Summary.StoryCoverImage.Width == 0 || detail.Summary.StoryCoverImage.Height == 0 {
		s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, detail)
	}
	return detail, nil
}

func (s *StoryCmdService) CreateCapsuleStory(ctx context.Context, loginUserId int64, attr CreateCapsuleStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	if err := s.IStoryCmdRepository.CreateCapsuleStory(ctx, attr); err != nil {
		return nil, err
	}
	detail, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	s.tryBuildCoverImageKeyAndSizeFromConfig(ctx, detail)
	return detail, nil
}

func (s *StoryCmdService) IncreaseShareCount(ctx context.Context, storyId int64) error {
	return s.IncreaseShareStat(ctx, storyId)
}

func (s *StoryCmdService) tryBuildCoverImageKeyAndSizeFromConfig(ctx context.Context, story *domain_entities_items.StoryDetail) {
	coverImage, err := story.Summary.GetStoryCover()
	if err != nil {
		return
	}

	if coverImage == nil {
		coverImageKey := story.Summary.GetStoryCoverPath()
		info, err := getImageKeyWidthHeight(coverImageKey)
		if err != nil {
			s.logger.WithContext(ctx).Warnf("failed to get image key width height: %v", err)
			return
		}
		coverImage = &domain_entities_resource.CoverImage{
			ThumbnailKey: coverImageKey,
			Width:        uint32(info.Width),
			Height:       uint32(info.Height),
		}
	}
	if err := s.IStoryCmdRepository.UpdateCoverImageKeyAndSize(ctx, story.Summary.Id, coverImage.ThumbnailKey, coverImage.Width, coverImage.Height); err != nil {
		s.logger.WithContext(ctx).Warnf("failed to update cover image key and size: %v", err)
	}
}

func getImageKeyWidthHeight(imageKey domain_entities_resource.ImageResourcePath) (*struct {
	Width  int
	Height int
}, error) {
	url := imageKey.ItemPosterInSummary()
	resp, err := http.Get(url)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch image: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应头，检查Content-Type
	contentType := resp.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		return nil, fmt.Errorf("invalid content type: %s", contentType)
	}

	// 读取所有数据到内存中
	imageData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read image data: %w", err)
	}

	// 创建一个bytes.Reader用于多次读取
	reader := bytes.NewReader(imageData)

	// 尝试使用不同的解码器
	var img image.Image
	var decodeErr error

	// 重置reader位置
	reader.Seek(0, 0)
	img, decodeErr = jpeg.Decode(reader)
	if decodeErr != nil {
		// 重置reader位置
		reader.Seek(0, 0)
		img, decodeErr = png.Decode(reader)
		if decodeErr != nil {
			// 重置reader位置
			reader.Seek(0, 0)
			img, decodeErr = gif.Decode(reader)
			if decodeErr != nil {
				return nil, fmt.Errorf("failed to decode image (tried jpeg, png, gif): %w", decodeErr)
			}
		}
	}

	return &struct {
		Width  int
		Height int
	}{
		Width:  img.Bounds().Dx(),
		Height: img.Bounds().Dy(),
	}, nil
}

// SendStoryMessage sends a message for different story types
func (s *StoryCmdService) SendStoryMessage(
	ctx context.Context,
	userId int64,
	storyId int64,
	messageType api_items_story_v2.MessageType,
	userVideoKey domain_entities_resource.VideoResourcePath,
	userVideoCoverKey domain_entities_resource.ImageResourcePath,
	consumerStoryCoverKey domain_entities_resource.ImageResourcePath,
	consumeStatus *api_im_message_types_v1.ConsumeStatus,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.storyQueryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}

	switch messageType {
	case api_items_story_v2.MessageType_MESSAGE_TYPE_WASSUP:
		return s.sendWassupMessage(ctx, story, userId, userVideoKey, userVideoCoverKey)
	case api_items_story_v2.MessageType_MESSAGE_TYPE_CHATPROXY:
		return s.sendChatProxyMessage(ctx, story, userId, userVideoKey, userVideoCoverKey)
	case api_items_story_v2.MessageType_MESSAGE_TYPE_ROASTED:
		return s.sendRoastedMessage(ctx, story, userId, userVideoKey, userVideoCoverKey)
	case api_items_story_v2.MessageType_MESSAGE_TYPE_BASEPLAY: // TODO: move to consume_baseplay_story
		return s.SendBasePlayMessage(ctx, story, userId, consumerStoryCoverKey, consumeStatus)
	default:
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("unsupported message type"))
	}
}

func (s *StoryCmdService) SendBasePlayMessage(
	ctx context.Context,
	story *domain_entities_items.StoryDetail,
	userId int64,
	consumerStoryCoverKey domain_entities_resource.ImageResourcePath,
	consumeStatus *api_im_message_types_v1.ConsumeStatus,
) (*domain_entities_items.StoryDetail, error) {
	if consumerStoryCoverKey == "" {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("consumerStoryCoverKey is nil"))
	}

	cover, err := story.Summary.GetStoryCover()
	if err != nil {
		return nil, err
	}

	status := api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL
	if consumeStatus != nil {
		status = *consumeStatus
	}

	if err := s.contextService.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_BASEPLAY_INTERACTION.String(),
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryBaseplayInteraction{
			StoryBaseplayInteraction: &api_im_message_types_v1.StoryBaseplayCustomMessagePayload{
				StoryCoverUrl:         cover.GetResourceURL(),
				StoryId:               fmt.Sprint(story.Summary.Id),
				ConsumerStoryCoverUrl: consumerStoryCoverKey.OriginalAccessURL(),
				Title:                 "Get your Portal",
			},
		},
		ConsumeStatus: status.String(),
	}, story.Summary.Author.ID); err != nil {
		return nil, err
	}

	return s.storyQueryService.GetStoryDetail(ctx, userId, story.Summary.Id)
}

func (s *StoryCmdService) sendWassupMessage(
	ctx context.Context,
	story *domain_entities_items.StoryDetail,
	userId int64,
	userVideoKey domain_entities_resource.VideoResourcePath,
	userVideoCoverKey domain_entities_resource.ImageResourcePath,
) (*domain_entities_items.StoryDetail, error) {
	cover, err := story.Summary.GetStoryCover()
	if err != nil {
		return nil, err
	}

	if err := s.contextService.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_WASSUP_INTERACTION.String(),
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryWassupInteraction{
			StoryWassupInteraction: &api_im_message_types_v1.StoryWassupCustomMessagePayload{
				ConsumerVideoCoverUrl: userVideoCoverKey.OriginalAccessURL(),
				ConsumerVideoUrl:      userVideoKey.URL(),
				StoryCoverUrl:         cover.GetResourceURL(),
				StoryId:               fmt.Sprint(story.Summary.Id),
			},
		},
		ConsumeStatus: api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
	}, story.Summary.Author.ID); err != nil {
		return nil, err
	}

	return s.storyQueryService.GetStoryDetail(ctx, userId, story.Summary.Id)
}

func (s *StoryCmdService) sendChatProxyMessage(
	ctx context.Context,
	story *domain_entities_items.StoryDetail,
	userId int64,
	userVideoKey domain_entities_resource.VideoResourcePath,
	userVideoCoverKey domain_entities_resource.ImageResourcePath,
) (*domain_entities_items.StoryDetail, error) {
	storyCover, err := story.Summary.GetStoryCover()
	if err != nil {
		return nil, err
	}

	if err := s.contextService.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_CHATPROXY_INTERACTION.String(),
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryChatproxyInteraction{
			StoryChatproxyInteraction: &api_im_message_types_v1.StoryChatProxyCustomMessagePayload{
				ConsumerVideoCoverUrl: userVideoCoverKey.OriginalAccessURL(),
				ConsumerVideoUrl:      userVideoKey.URL(),
				StoryCoverUrl:         storyCover.GetResourceURL(),
				StoryId:               fmt.Sprint(story.Summary.Id),
			},
		},
		ConsumeStatus: api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
	}, story.Summary.Author.ID); err != nil {
		return nil, err
	}

	return s.storyQueryService.GetStoryDetail(ctx, userId, story.Summary.Id)
}

func (s *StoryCmdService) sendRoastedMessage(
	ctx context.Context,
	story *domain_entities_items.StoryDetail,
	userId int64,
	userVideoKey domain_entities_resource.VideoResourcePath,
	userVideoCoverKey domain_entities_resource.ImageResourcePath,
) (*domain_entities_items.StoryDetail, error) {
	storyCover, err := story.Summary.GetStoryCover()
	if err != nil {
		return nil, err
	}

	customPayload := &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryRoastedInteraction{
			StoryRoastedInteraction: &api_im_message_types_v1.StoryRoastedCustomMessagePayload{
				ConsumerVideoCoverUrl: userVideoCoverKey.OriginalAccessURL(),
				ConsumerVideoUrl:      userVideoKey.URL(),
				StoryCoverUrl:         storyCover.GetResourceURL(),
				StoryId:               fmt.Sprint(story.Summary.Id),
			},
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_ROASTED_INTERACTION.String(),
		ConsumeStatus:     api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
	}

	if err := s.contextService.imService.SendCustomMessages(ctx, userId, customPayload, story.Summary.Author.ID); err != nil {
		return nil, err
	}

	return s.storyQueryService.GetStoryDetail(ctx, userId, story.Summary.Id)
}
