package domain_services_items_story

import (
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type IImageMaskGenerateService interface {
	GenerateImageMask(
		ctx context.Context,
		imageKey domain_entities_resource.ImageResourcePath,
		points []*domain_entities_items.Point,
	) (string, error)
}

func (s *StoryPlayService) ConsumeHideStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	stickerIds ...string,
) (*domain_entities_items.StoryDetail, error) {

	// 加锁，禁止用户并发消费
	lockKey := fmt.Sprintf("story_play_hide_story_%d_%d", storyId, userId)

	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}

		stickers := story.Summary.HidePlayConfig.GetStickers()
		stickers = lo.Filter(stickers, func(item *domain_entities_items.HideSticker, _ int) bool {
			return lo.Contains(stickerIds, item.Id)
		})
		// 去除那些可能已经存在的
		var needAddStickers []*domain_entities_items.HideSticker
		for _, waitToAddSticker := range stickers {
			exist := lo.ContainsBy(story.HidePlayContext.UnlockedStickers, func(item *domain_entities_items.UnlockSticker) bool {
				return item.Sticker.Id == waitToAddSticker.Id
			})
			if exist {
				continue
			}
			needAddStickers = append(needAddStickers, waitToAddSticker)
		}

		story.HidePlayContext.UnlockedStickers = append(story.HidePlayContext.UnlockedStickers, lo.Map(needAddStickers, func(item *domain_entities_items.HideSticker, _ int) *domain_entities_items.UnlockSticker {
			return &domain_entities_items.UnlockSticker{
				CreatedAt: uint32(time.Now().Unix()),
				Sticker:   item,
			}
		})...)
		story.HidePlayContext.IsConsumed = true

		// 记录为收藏
		if err := s.hideStickerService.CollectStickers(ctx, userId, lo.Map(needAddStickers, func(item *domain_entities_items.HideSticker, _ int) int64 {
			return cast.ToInt64(item.Id)
		})...); err != nil {
			return err
		}

		if err := s.sendHideStickerUnlockedMessage(ctx, story, userId, needAddStickers); err != nil {
			return err
		}

		return s.IStoryPlayRecordRepo.SaveHidePlayRecord(ctx, storyId, userId, *story.HidePlayContext)
	}, time.Second*10); err != nil {
		return nil, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) sendHideStickerUnlockedMessage(
	ctx context.Context,
	story *domain_entities_items.StoryDetail,
	userId int64,
	stickers []*domain_entities_items.HideSticker,
) error {
	if len(stickers) == 0 {
		return nil
	}

	for _, sticker := range stickers {
		customPayload := &api_im_message_types_v1.CustomMessagePayload{
			Payload: &api_im_message_types_v1.CustomMessagePayload_StoryHideStickerUnlocked{
				StoryHideStickerUnlocked: &api_im_message_types_v1.StoryHideStickerUnlockedCustomMessagePayload{
					StoryId:       strconv.FormatInt(story.Summary.Id, 10),
					Title:         "Unlock your Stickers",
					StoryCoverUrl: story.Summary.GetStoryCoverPath().ItemPosterInSummary(),
					StickerUrl:    sticker.Resource.GetObjectAccessURL(),
				},
			},
			CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_HIDE_STICKER_UNLOCKED.String(),
			ConsumeStatus:     api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
		}
		if err := s.imService.SendCustomMessages(ctx, userId, customPayload, story.Summary.Author.ID); err != nil {
			return err
		}
	}

	return nil
}

func (s *StoryPlayService) GenerateImageMask(
	ctx context.Context,
	imageKey domain_entities_resource.ImageResourcePath,
	points []*domain_entities_items.Point,
) (string, error) {
	return s.imageMaskGenerateService.GenerateImageMask(ctx, imageKey, points)
}

type CreateHideStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayHideConfig
}

func (s *StoryCmdService) CreateHideStory(ctx context.Context, loginUserId int64, attr CreateHideStoryAttr) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	attr.Version = lo.ToPtr(int32(domain_entities_items.StoryVersion_V2))
	// 给所有 sticker 添加 id
	for playType, data := range attr.PlayConfig.TriggerData {
		switch playType {
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LONG_PRESS:
			data.LongPressData.Stickers = lo.Map(data.LongPressData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *domain_entities_items.HideSticker {
				item.Id = fmt.Sprint(s.uniqIdGen.Generate())
				item.FromStoryId = attr.Id
				return item
			})
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_CONTINUOUSLY_CLICK:
			data.ContinuousClickData.Stickers = lo.Map(data.ContinuousClickData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *domain_entities_items.HideSticker {
				item.Id = fmt.Sprint(s.uniqIdGen.Generate())
				item.FromStoryId = attr.Id
				return item
			})
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_DRAG:
			for _, cutObject := range data.DragData.CutObjects {
				cutObject.Sticker.Id = fmt.Sprint(s.uniqIdGen.Generate())
				cutObject.Sticker.FromStoryId = attr.Id
			}
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_LIKE:
			data.LikeData.Stickers = lo.Map(data.LikeData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *domain_entities_items.HideSticker {
				item.Id = fmt.Sprint(s.uniqIdGen.Generate())
				item.FromStoryId = attr.Id
				return item
			})
		case api_items_story_types_v1.HideStickerTriggerType_STICKER_TRIGGER_TYPE_SHAKE:
			data.ShakeData.Stickers = lo.Map(data.ShakeData.Stickers, func(item *domain_entities_items.HideSticker, _ int) *domain_entities_items.HideSticker {
				item.Id = fmt.Sprint(s.uniqIdGen.Generate())
				item.FromStoryId = attr.Id
				return item
			})
		}
	}
	attr.CoverImage = domain_entities_items.StoryCoverImage{
		Path:   attr.PlayConfig.BackgroundImage.GetCoverImageObjectKey(),
		Width:  int(attr.PlayConfig.BackgroundImage.GetCoverImageSize().Width),
		Height: int(attr.PlayConfig.BackgroundImage.GetCoverImageSize().Height),
	}
	if err := s.txManager.ExecTx(ctx, func(ctx context.Context) error {
		if err := s.IStoryCmdRepository.CreateHideStory(ctx, attr); err != nil {
			return err
		}
		return s.hideStickerService.CreateStickers(ctx, loginUserId, attr.PlayConfig.GetStickers()...)
	}); err != nil {
		return nil, err
	}
	return s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
}
