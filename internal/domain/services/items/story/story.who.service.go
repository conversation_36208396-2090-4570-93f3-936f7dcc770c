package domain_services_items_story

import (
	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_users "boson/internal/domain/entities/users"
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type CreateWhoStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayWhoConfig
}

func (s *StoryPlayService) ConsumeWhoStorySelectOption(ctx context.Context, userId int64, storyId int64, optionUserId int64) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}

	var selectOptionUser *domain_entities_users.UserSummaryEntity
	for _, enableOptionUserId := range story.WhoPlayContext.EnabledOptionUserIds {
		if enableOptionUserId == optionUserId {
			for _, option := range story.Summary.WhoPlayConfig.OptionUsers {
				if option.ID == enableOptionUserId {
					selectOptionUser = option
					break
				}
			}
			break
		}
	}
	if selectOptionUser == nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("option user not found"))
	}
	correct := story.Summary.Author.ID == optionUserId
	story.WhoPlayContext.IsUnlocked = correct
	story.WhoPlayContext.IsConsumed = true
	// 选择后，就从待选项里移除
	story.WhoPlayContext.EnabledOptionUserIds = lo.Filter(story.WhoPlayContext.EnabledOptionUserIds, func(id int64, _ int) bool {
		return id != optionUserId
	})
	if correct {
		// 当解锁时，重置EnabledOptionUserIds为初始值
		story.WhoPlayContext.EnabledOptionUserIds = story.Summary.WhoPlayConfig.OptionUserIds
	}
	if err := s.IStoryPlayRecordRepo.SaveWhoPlayRecords(ctx, userId, map[int64]domain_entities_items.StoryPlayWhoContext{
		storyId: *story.WhoPlayContext,
	}); err != nil {
		return nil, err
	}

	message := &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryWhoInteraction{
			StoryWhoInteraction: &api_im_message_types_v1.StoryWhoInteractionCustomMessagePayload{
				AvatarUrls: lo.Map(story.Summary.WhoPlayConfig.OptionUserIds, func(optionUserId int64, _ int) string {
					for _, option := range story.Summary.WhoPlayConfig.OptionUsers {
						if option.ID == optionUserId {
							return option.AvatarImagePath.ItemPosterInSummary()
						}
					}
					return ""
				}),
				StoryId:           fmt.Sprintf("%d", story.Summary.Id),
				SelectedAvatarUrl: selectOptionUser.AvatarImagePath.ItemPosterInSummary(),
				SelectedUserName:  selectOptionUser.Nickname,
				Correct:           correct,
				TriedTimes:        story.WhoPlayContext.TriedCount,
			},
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_WHO_INTERACTION.String(),
		ConsumeStatus: lo.If(
			correct,
			api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
		).Else(
			api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_FAILED.String(),
		),
	}

	if err := s.imService.SendCustomMessages(ctx, userId, message, story.Summary.Author.ID); err != nil {
		return nil, err
	}
	if story, err = s.queryService.GetStoryDetail(ctx, userId, storyId); err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) ConsumeWhoStoryAddTryPoint(ctx context.Context, userId int64, storyId int64, points ...*api_items_story_types_v1.WhoStoryTryPoint) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	if story.WhoPlayContext.TriedCount+uint32(len(points)) > story.WhoPlayContext.MaxTryCount {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("max try count is less than tried count"))
	}
	story.WhoPlayContext.TriedCount += uint32(len(points))
	story.WhoPlayContext.TriedPoints = append(story.WhoPlayContext.TriedPoints, points...)
	story.WhoPlayContext.IsConsumed = true
	if err := s.IStoryPlayRecordRepo.SaveWhoPlayRecords(ctx, userId, map[int64]domain_entities_items.StoryPlayWhoContext{
		storyId: *story.WhoPlayContext,
	}); err != nil {
		return nil, err
	}
	if story, err = s.queryService.GetStoryDetail(ctx, userId, storyId); err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryCmdService) CreateWhoStory(ctx context.Context, loginUserId int64, attr CreateWhoStoryAttr) (*domain_entities_items.StoryDetail, error) {
	if attr.Id == 0 {
		attr.Id = s.uniqIdGen.Generate()
	}
	attr.CreatorID = loginUserId
	attr.Version = lo.ToPtr(int32(domain_entities_items.StoryVersion_V2))

	if !lo.Contains(attr.PlayConfig.OptionUserIds, loginUserId) {
		// 手动拼接一个进去
		attr.PlayConfig.OptionUserIds = append(attr.PlayConfig.OptionUserIds, loginUserId)
	}
	if len(attr.PlayConfig.OptionUserIds) > 3 {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("option_users must be less than 3"))
	}
	attr.CoverImage = domain_entities_items.StoryCoverImage{
		Path:   attr.PlayConfig.CoverResource.GetCoverImageObjectKey(),
		Width:  int(attr.PlayConfig.CoverResource.GetCoverImageSize().Width),
		Height: int(attr.PlayConfig.CoverResource.GetCoverImageSize().Height),
	}
	if err := s.IStoryCmdRepository.CreateWhoStory(ctx, attr); err != nil {
		return nil, err
	}
	return s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
}
