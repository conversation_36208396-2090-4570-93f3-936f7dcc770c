package domain_services_items_story

import (
	"context"
	"fmt"
	"time"

	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_boo "boson/internal/domain/entities/boo"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_ai "boson/internal/domain/services/ai"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
)

type IBooRepository interface {
	AddCapturedBoo(ctx context.Context, loginUserId int64, booId int64) error
	RemoveBooInAssist(ctx context.Context, loginUserId int64, booIds ...int64) error
	ListHauntBooAssist(ctx context.Context, loginUserId int64, req *api_common_v1.ListRequest) ([]*domain_entities_items.HauntBoo, *api_common_v1.ListResponse, error)
	UpdateBooInfo(ctx context.Context, boo *domain_entities_items.HauntBoo) error
	BatchGetHauntBoos(ctx context.Context, userId int64, booIds []int64) (map[int64]*domain_entities_items.HauntBoo, error)
}

type IAvatarRepository interface {
	GetAvatar(ctx context.Context, userId int64, avatarId int64) (*domain_entities_boo.Avatar, error)
	ListRandomAvatars(ctx context.Context, loginUserId int64, count uint32) ([]*domain_entities_boo.Avatar, error)
}

type CreateHauntStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.HauntStoryConfig
}

func (s *StoryCmdService) CreateHauntStory(ctx context.Context, loginUserId int64, attr CreateHauntStoryAttr) (*domain_entities_items.StoryDetail, error) {
	assistBooIds := lo.Map(attr.PlayConfig.AssitBoosWithQuestionsAndAnswers, func(item *domain_entities_items.HauntBoo, _ int) int64 {
		return item.Id
	})
	assistBooIds = lo.Uniq(assistBooIds)
	if len(assistBooIds) > 0 {
		// 移除掉
		if err := s.booRepository.RemoveBooInAssist(ctx, loginUserId, assistBooIds...); err != nil {
			return nil, err
		}
		assistBoos, err := s.booRepository.BatchGetHauntBoos(ctx, loginUserId, assistBooIds)
		if err != nil {
			return nil, err
		}
		for idx, boo := range attr.PlayConfig.AssitBoosWithQuestionsAndAnswers {
			assistBoo, ok := assistBoos[boo.Id]
			if !ok {
				return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("assist boo not found"))
			}
			attr.PlayConfig.AssitBoosWithQuestionsAndAnswers[idx] = assistBoo
		}
	}

	avatar, err := s.avatarRepository.GetAvatar(ctx, loginUserId, attr.PlayConfig.CreatorBooWithQuestionsAndAnswers.AvatarId)
	if err != nil {
		return nil, err
	}
	attr.PlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar = avatar

	attr.Id = s.uniqIdGen.Generate()
	attr.PlayConfig.CreatorBooWithQuestionsAndAnswers.Id = s.uniqIdGen.Generate()
	attr.PlayConfig.CreatorBooWithQuestionsAndAnswers.CreatorId = loginUserId
	// 需要异步生成
	attr.Status = lo.ToPtr(api_items_story_types_v1.StoryStatus_STORY_STATUS_GENERATING)
	if err := s.IStoryCmdRepository.CreateHauntStory(ctx, attr); err != nil {
		return nil, err
	}

	story, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}

	go s.generateHauntStoryConfig(ctx, loginUserId, story)

	return story, nil
}

func (s *StoryPlayService) AddCapturedBoo(ctx context.Context, loginUserId int64, booId int64) error {
	if err := s.booRepository.AddCapturedBoo(ctx, loginUserId, booId); err != nil {
		return err
	}
	return nil
}

func (s *StoryPlayService) ConsumeHauntStory(
	ctx context.Context,
	loginUserId int64,
	storyId int64,
	unlocked bool,
	isFromFeed bool,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return nil, err
	}
	story.HauntPlayContext.TryCount += 1
	// @TODO Larry 考虑到端上可能会缓存这个展示信息，在这里就不再限制了
	// if story.HauntPlayContext.TryCount > 2 {
	// 	return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("haunt story try count exceed"))
	// }
	story.HauntPlayContext.Unlocked = unlocked
	// 非自己消费，且未解锁，则污染
	if !unlocked && story.Summary.Author.ID != loginUserId {
		if err := s.hauntBooShowInfoService.AppendHauntBooIntoUserShowInfo(ctx, loginUserId, story, isFromFeed); err != nil {
			return nil, err
		}
	}
	if unlocked {
		if err := s.hauntBooShowInfoService.BooCaptured(ctx, loginUserId, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id, isFromFeed); err != nil {
			return nil, err
		}
	}
	if err := s.IStoryPlayRecordRepo.SaveHauntPlayRecord(ctx, storyId, loginUserId, *story.HauntPlayContext); err != nil {
		return nil, err
	}
	return s.queryService.GetStoryDetail(ctx, loginUserId, storyId)
}
func (s *StoryPlayService) ListHauntBooAssist(ctx context.Context, loginUserId int64, req *api_common_v1.ListRequest) ([]*domain_entities_items.HauntBoo, *api_common_v1.ListResponse, error) {
	boos, lisp, err := s.booRepository.ListHauntBooAssist(ctx, loginUserId, req)
	if err != nil {
		return nil, nil, err
	}
	return boos, lisp, nil
}
func (s *StoryPlayService) SendHauntCaptureVideo(ctx context.Context, loginUserId int64, storyId int64, videoKey domain_entities_resource.VideoResourcePath, videoCoverKey domain_entities_resource.ImageResourcePath) error {
	story, err := s.queryService.GetStoryDetail(ctx, loginUserId, storyId)
	if err != nil {
		return err
	}
	creatorAvatar := story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar
	message := &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryHauntInteraction{
			StoryHauntInteraction: &api_im_message_types_v1.StoryHauntCustomMessagePayload{
				VideoUrl:      videoKey.URL(),
				VideoCoverUrl: videoCoverKey.ItemPosterInSummary(),
				BooAvatarUrl:  creatorAvatar.ObjectPath.ItemPosterInSummary(),
				StoryId:       fmt.Sprintf("%d", storyId),
			},
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_HAUNT_INTERACTION.String(),
		ConsumeStatus: lo.If(story.HauntPlayContext.Unlocked, api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String()).
			Else(api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_FAILED.String()),
	}

	if err := s.imService.SendCustomMessages(ctx, loginUserId, message, story.Summary.Author.ID); err != nil {
		return err
	}
	return nil
}
func (s *StoryPlayService) CheckHauntImage(ctx context.Context, imageKey domain_entities_resource.ImageResourcePath, textCondition string) (bool, []float64, string, error) {
	judge, err := s.llmService.HauntLLMJudge(ctx, &domain_services_ai.HauntLLMJudgeRequest{
		ImageKey:      imageKey,
		TextCondition: textCondition,
	})
	if err != nil {
		return false, nil, "", err
	}
	return judge.Pass, judge.Point, judge.Feedback, nil
}

func (s *StoryPlayService) ListRandomAvatars(ctx context.Context, loginUserId int64, count uint32) ([]*domain_entities_boo.Avatar, error) {
	avatars, err := s.avatarRepository.ListRandomAvatars(ctx, loginUserId, count)
	if err != nil {
		return nil, err
	}
	return avatars, nil
}

func (s *StoryPlayService) AddCapturedBooInToCollectedStickers(ctx context.Context, loginUserId int64, booId int64) (*domain_entities_items.HideSticker, error) {
	boos, err := s.booRepository.BatchGetHauntBoos(ctx, loginUserId, []int64{booId})
	if err != nil {
		return nil, err
	}
	boo, ok := boos[booId]
	if !ok {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("boo not found"))
	}
	stickers, err := s.hideStickerService.BatchetGetWithAvatarIds(ctx, loginUserId, boo.AvatarId)
	if err != nil {
		return nil, err
	}
	sticker := stickers[boo.AvatarId]
	if sticker == nil {
		return nil, errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("sticker not found"))
	}
	stickerIds := []int64{}
	for _, sticker := range stickers {
		stickerIds = append(stickerIds, cast.ToInt64(sticker.Id))
	}
	if err := s.hideStickerService.CollectStickers(ctx, loginUserId, stickerIds...); err != nil {
		return nil, err
	}
	sticker.Collected = true
	return sticker, nil
}

func (s *StoryPlayService) AddCaptureBooIntoMyAssist(ctx context.Context, loginUserId int64, storyId int64, booId int64) error {
	if err := s.booRepository.AddCapturedBoo(ctx, loginUserId, booId); err != nil {
		return err
	}
	return nil
}

func (s *StoryCmdService) generateHauntStoryConfig(ctx context.Context, loginUserId int64, storyDetail *domain_entities_items.StoryDetail) {
	var (
		errgroup      errgroup.Group
		generatedHint string
		videoKey      domain_entities_resource.VideoResourcePath
	)
	errgroup.Go(func() error {
		// hint generate async
		hintCtx := context.Background()
		hintCtx, _ = context.WithTimeout(hintCtx, 15*time.Minute)
		hit, err := s.generateHauntLLMHint(hintCtx, loginUserId, storyDetail.Summary.Id, storyDetail.Summary.HauntPlayConfig)
		if err != nil {
			return err
		}
		generatedHint = hit
		return nil
	})
	errgroup.Go(func() error {
		clipCtx := context.Background()
		clipCtx, _ = context.WithTimeout(clipCtx, 15*time.Minute)
		clipVideoKey, err := s.generateHauntLLMClip(clipCtx, loginUserId, storyDetail.Summary.Id, storyDetail.Summary.HauntPlayConfig)
		if err != nil {
			return err
		}
		videoKey = clipVideoKey
		return nil
	})

	if err := errgroup.Wait(); err != nil {
		s.logger.WithContext(ctx).Errorf("story : %d, asyncGenerateHauntStoryConfig failed: %v", storyDetail.Summary.Id, err)
		return
	}

	storyDetail.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Hint = generatedHint
	storyDetail.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.VideoKey = videoKey
	if err := s.booRepository.UpdateBooInfo(context.Background(), storyDetail.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers); err != nil {
		s.logger.WithContext(ctx).Errorf("story : %d, update boo video key and hint failed: %v", storyDetail.Summary.Id, err)
		return
	}

	storyDetail.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Hint = generatedHint
	storyDetail.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.VideoKey = videoKey
	if err := s.IStoryCmdRepository.UpdateHauntStoryConfigAndStatus(context.Background(), storyDetail.Summary.Id, storyDetail.Summary.HauntPlayConfig, api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED); err != nil {
		s.logger.WithContext(ctx).Errorf("story : %d, update story config and status failed: %v", storyDetail.Summary.Id, err)
		return
	}
}

func (s *StoryCmdService) generateHauntLLMClip(ctx context.Context, loginUserId int64, storyId int64, attr *domain_entities_items.HauntStoryConfig) (domain_entities_resource.VideoResourcePath, error) {
	// 获取 howToReact 和 avatarId
	howToReact := attr.CreatorBooWithQuestionsAndAnswers.QuestionsWithAnswers[2].Answer
	avatarId := attr.CreatorBooWithQuestionsAndAnswers.AvatarId

	// 获取 avatar 信息
	avatar, err := s.avatarRepository.GetAvatar(ctx, loginUserId, avatarId)
	if err != nil {
		return "", err
	}

	// 根据 howToReact 调用 HauntLLMReaction
	var wrappedPrompt string
	if howToReact != "" {
		reactionResponse, err := s.llmService.HauntLLMReaction(ctx, &domain_services_ai.HauntLLMReactionRequest{
			UserTypedHowToReactWhenCaptured: howToReact,
		})
		if err != nil {
			return "", err
		}
		wrappedPrompt = reactionResponse.VideoPromptHowToReactWhenCaptured
	} else {
		// 如果 howToReact 为空，使用 HauntLLMSampledReaction
		sampledResponse, err := s.llmService.HauntLLMSampledReaction(ctx)
		if err != nil {
			return "", err
		}
		wrappedPrompt = sampledResponse.VideoPromptHowToReactWhenCaptured
	}

	// 调用 GenerateHauntTask 生成视频
	return s.booAIService.GenerateHauntTask(ctx, avatar.ObjectPath, wrappedPrompt)
}

func (s *StoryCmdService) generateHauntLLMHint(ctx context.Context, loginUserId int64, storyId int64, attr *domain_entities_items.HauntStoryConfig) (string, error) {
	whenAppear := attr.CreatorBooWithQuestionsAndAnswers.QuestionsWithAnswers[0].Answer
	creatorHint := attr.CreatorBooWithQuestionsAndAnswers.QuestionsWithAnswers[1].Answer

	hitRes, err := s.llmService.GenerateHauntLLMHint(ctx, &domain_services_ai.HauntLLMHintRequest{
		WhenToAppear: whenAppear,
		CreatorHint:  creatorHint,
		CreatorName:  attr.CreatorBooWithQuestionsAndAnswers.Creator.Nickname,
	})
	if err != nil {
		return "", err
	}

	// if hitRes.Meaningful is false, then change whenAppear to `any image shall be good` and update config to database
	if !hitRes.Meaningful {
		whenAppear = "any image shall be good"
		attr.CreatorBooWithQuestionsAndAnswers.QuestionsWithAnswers[0].Answer = whenAppear
		if err := s.booRepository.UpdateBooInfo(context.Background(), attr.CreatorBooWithQuestionsAndAnswers); err != nil {
			s.logger.WithContext(ctx).Errorf("story : %d, update boo whenAppear failed: %v", storyId, err)
			return "", err
		}
	}

	return hitRes.HintToDisplay, nil
}
