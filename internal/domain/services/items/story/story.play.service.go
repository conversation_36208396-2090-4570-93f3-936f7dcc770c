package domain_services_items_story

import (
	api_items_story_v1 "boson/api/items/story/v1"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"

	api_errors_v1 "boson/api/errors/v1"
	api_im_message_types_v1 "boson/api/im/message/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	adapter_driving_repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_ai "boson/internal/domain/services/ai"
	domain_services_im "boson/internal/domain/services/im"
	domain_services_users_info "boson/internal/domain/services/users/info"
	"boson/internal/infra/data"
)

type IStoryPlayRecordRepo interface {
	SaveExchangeImagePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.ExchangeImagePlayContext, isFinished bool) error
	SaveTurtleSoupPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayTurtleSoupContext) error
	SaveUnmutePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayUnmuteContext) error
	SaveBasePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryBasePlayContext) error
	SaveNowShotPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayNowShotContext) error
	SaveRoastedPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayRoastedContext) error
	SaveWassupPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayWassupContext) error
	SaveCapsulePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayCapsuleContext) error
	SaveHidePlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayHideContext) error
	SaveChatProxyPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPlayChatProxyContext) error
	SaveWhoPlayRecords(ctx context.Context, userId int64, playContexts map[int64]domain_entities_items.StoryPlayWhoContext) error
	SaveHauntPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.HauntStoryContext) error
	SavePinPlayRecord(ctx context.Context, storyId int64, userId int64, playContext domain_entities_items.StoryPinContext) error
}

type IStoryConsumeChecker interface {
	UnmuteConsume(ctx context.Context, prompt string, audioKey string) (api_items_story_types_v1.UnmuteMatchStatus, string, error)
}

type StoryPlayService struct {
	IStoryPlayRecordRepo
	booRepository            IBooRepository
	configStorage            adapter_driving_repos_app_settings.ConfigStorage
	llmService               *domain_services_ai.LlmService
	ttsService               *domain_services_ai.TtsService
	Locker                   domain_interfaces.Locker
	queryService             *StoryQueryService
	imService                *domain_services_im.IMService
	asrService               *domain_services_ai.AsrService
	userQueryService         *domain_services_users_info.UsersInfoService
	cdnService               domain_interfaces.ICDN
	imageMaskGenerateService IImageMaskGenerateService
	hideStickerService       *HideStickerService
	avatarRepository         IAvatarRepository
	hauntBooShowInfoService  *HauntBooShowInfoService
	data                     *data.Data
	uniqIdGen                domain_interfaces.UniqIdGenerator
}

func NewStoryPlayService(
	storyPlayRecordRepo IStoryPlayRecordRepo,
	llmService *domain_services_ai.LlmService,
	ttsService *domain_services_ai.TtsService,
	queryService *StoryQueryService,
	locker domain_interfaces.Locker,
	imService *domain_services_im.IMService,
	asrService *domain_services_ai.AsrService,
	userQueryService *domain_services_users_info.UsersInfoService,
	configStorage adapter_driving_repos_app_settings.ConfigStorage,
	cdnService domain_interfaces.ICDN,
	imageMaskGenerateService IImageMaskGenerateService,
	hideStickerService *HideStickerService,
	booRepository IBooRepository,
	avatarRepository IAvatarRepository,
	hauntBooShowInfoService *HauntBooShowInfoService,
	data *data.Data,
	uniqIdGen domain_interfaces.UniqIdGenerator,
) *StoryPlayService {
	return &StoryPlayService{
		storyPlayRecordRepo,
		booRepository,
		configStorage,
		llmService,
		ttsService,
		locker,
		queryService,
		imService,
		asrService,
		userQueryService,
		cdnService,
		imageMaskGenerateService,
		hideStickerService,
		avatarRepository,
		hauntBooShowInfoService,
		data,
		uniqIdGen,
	}
}

func (s *StoryPlayService) PlayBasePlayStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	nextNodeIdx uint32,
) (*domain_entities_items.StoryDetail, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY)
	if err != nil {
		return nil, err
	}

	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetailWithStoryType(ctx, userId, storyId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY)
		if err != nil {
			return err
		}
		playContext := story.BasePlayContext
		playConfig := story.Summary.BasePlayConfig
		if playContext != nil {
			if nextNodeIdx >= uint32(len(playConfig.Nodes)) {
				return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("you have reached the maximum number of attempts"))
			}
			playContext.CurrentNodeIdx = nextNodeIdx
			playContext.CurrentNodeID = playConfig.Nodes[playContext.CurrentNodeIdx].ID

			playContext.IsUnlocked = true
		}
		// save context
		if err := s.SaveBasePlayRecord(ctx, storyId, userId, *playContext); err != nil {
			return err
		}
		return nil
	}, 10*time.Second); err != nil {
		return nil, err
	}

	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}
func (s *StoryPlayService) PlayTurtleSoupStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	userMessage string,
) (*domain_entities_items.StoryDetail, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP)
	if err != nil {
		return nil, err
	}

	var aiResponse string
	var hitWords []string
	var matchStatus api_items_story_types_v1.TurtleSoupMatchStatus

	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}

		config := story.Summary.TurtleSoupPlayConfig
		prompt := config.IntentPrompt
		if prompt == "" && config.CommonConfig != nil {
			prompt = config.CommonConfig.Condition.Prompt
		}
		caption := config.Caption
		if caption == "" && config.CommonConfig != nil {
			caption = config.CommonConfig.Condition.Hint.Text
		}
		aiResponse, hitWords, matchStatus, err = s.llmService.TurtleSoupConsume(ctx, userId, storyId, userMessage, prompt, caption, config.CustomAiResponses, story.TurtleSoupPlayContext.TryCount)
		if err != nil {
			return err
		}

		// update context
		context := story.TurtleSoupPlayContext

		// 更新 context
		context.AiResponse = aiResponse
		context.HitWords = hitWords
		context.IsFinished = matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_MATCHED
		// 默认
		context.Tips = "The following content is a bit blurry"
		context.UserMessage = userMessage
		// 根据 matchStatus 更新 tips
		if matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_NOT_MATCHED {
			context.Tips = "The content is still blurry... "
		}
		if matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED {
			context.Tips = "Some content is gradually emerging"
		}
		if matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_MATCHED {
			context.Tips = ""
		}
		context.TryCount++

		if err := s.sendTurtleSoupMessage(ctx, story, userId, context); err != nil {
			return err
		}
		return s.SaveTurtleSoupPlayRecord(ctx, storyId, userId, *context)
	}, 10*time.Second); err != nil {
		return nil, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) PlayRoastedStory(
	ctx context.Context,
	userId int64,
	storyId int64,
) (*domain_entities_items.StoryDetail, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED)
	if err != nil {
		return nil, err
	}

	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}
		context := story.RoastedPlayContext
		context.IsConsumed = true
		return s.SaveRoastedPlayRecord(ctx, storyId, userId, *context)
	}, 10*time.Second); err != nil {
		return nil, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}
func (s *StoryPlayService) PlayExchangeImageStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	imageKey domain_entities_resource.ImageResourcePath,
) (*domain_entities_items.StoryDetail, string, api_items_story_types_v1.ExchangeImageMatchStatus, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE)
	if err != nil {
		return nil, "", api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, err
	}

	var aiResponse string
	var matchStatus api_items_story_types_v1.ExchangeImageMatchStatus
	var isFinished = false

	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}
		context := story.ExchangeImagePlayContext

		if story.ExchangeImagePlayContext.IsFinished {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("you have finished this story"))
		}
		if story.ExchangeImagePlayContext.TryCount > story.Summary.ExchangeImagePlayConfig.CommonConfig.MaxTryCount {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("you have reached the maximum number of attempts"))
		}
		matchStatus, aiResponse, err = s.llmService.ImageExchange(ctx, imageKey, domain_entities_items.ExchangeImageCondition{
			Tips:      story.Summary.ExchangeImagePlayConfig.CommonConfig.Condition.Hint.Text,
			LLMPrompt: story.Summary.ExchangeImagePlayConfig.CommonConfig.Condition.Prompt,
		})
		if err != nil {
			return err
		}

		// 更新 context
		// 增加次数
		context.TryCount++
		context.UserTrialImageUrls = append(context.UserTrialImageUrls, imageKey)
		// 完全 match 时，才更新进度
		if matchStatus == api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_MATCHED {
			context.IsFinished = true
			isFinished = true
			context.UserSubmittedResources = append(context.UserSubmittedResources, &domain_entities_items.SubmittedResource{
				Resource:      domain_entities_resource.NewResourceBuilder().WithImageObjectKey(imageKey, 0, 0).Build(),
				UpdatedAtUnix: time.Now().Unix(),
			})
		}
		if err := s.sendExchangeImageMessage(ctx, userId, story, imageKey, story.Summary.ExchangeImagePlayConfig.CommonConfig.Resource.CoverImageKey, "", matchStatus); err != nil {
			return err
		}

		return s.SaveExchangeImagePlayRecord(ctx, storyId, userId, *context, isFinished)
	}, 10*time.Second); err != nil {
		return nil, "", api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, err
	}

	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, "", api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, err
	}
	return story, aiResponse, matchStatus, nil
}

func (s *StoryPlayService) PlayUnmuteStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	audioKey string,
) (*domain_entities_items.StoryDetail, string, api_items_story_types_v1.UnmuteMatchStatus, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE)
	if err != nil {
		return nil, "", api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_UNSPECIFIED, err
	}
	var (
		aiResponse  string
		matchStatus api_items_story_types_v1.UnmuteMatchStatus
	)

	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}
		playContext := story.UnmutePlayContext
		playConfig := story.Summary.UnmutePlayConfig
		if playContext != nil {
			// if playConfig.MaxTryCount > 0 && playContext.TryCount >= playConfig.MaxTryCount {
			// 	return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("you have reached the maximum number of attempts"))
			// }
		}
		prompt := playConfig.Intention
		if len(prompt) == 0 && playConfig.CommonConfig != nil {
			prompt = playConfig.CommonConfig.Condition.Prompt
		}
		caption := playConfig.Prompt.Text
		if len(caption) == 0 && playConfig.CommonConfig != nil {
			caption = playConfig.CommonConfig.Condition.Hint.Text
		}
		matchStatus, aiResponse, err = s.llmService.UnmuteConsume(ctx, prompt, caption, audioKey, playConfig.CustomAiResponses, playContext.TryCount)
		if err != nil {
			return err
		}

		// update context
		if playContext.AudioKeys == nil {
			playContext.AudioKeys = []string{}
		}
		playContext.AudioKeys = append(playContext.AudioKeys, audioKey)
		playContext.AiResponse = aiResponse
		playContext.TryCount++
		playContext.IsFinished = matchStatus == api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_MATCHED

		consumeStatus := api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_FAILED
		if playContext.IsFinished {
			consumeStatus = api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL
		}
		if err := s.sendUnmuteMessage(ctx, userId, story, domain_entities_resource.AudioResourcePath(audioKey), aiResponse, consumeStatus); err != nil {
			return err
		}
		return s.SaveUnmutePlayRecord(ctx, storyId, userId, *playContext)
	}, 10*time.Second); err != nil {
		return nil, "", api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_UNSPECIFIED, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, "", api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_UNSPECIFIED, err
	}
	return story, aiResponse, matchStatus, nil
}

func (s *StoryPlayService) PlayCapsuleStory(ctx context.Context, userId int64, storyId int64, sendToAuthor bool, savedStoryId int64) (*domain_entities_items.StoryDetail, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE)
	if err != nil {
		return nil, err
	}
	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetailWithStoryType(ctx, userId, storyId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE)
		if err != nil {
			return err
		}
		playContext := story.CapsulePlayContext
		playContext.IsConsumed = true
		playContext.SendToAuthor = sendToAuthor
		playContext.SavedStoryId = savedStoryId
		if sendToAuthor {
			err := s.sendCapsuleMessage(ctx, userId, story, story)
			if err != nil {
				return err
			}
		}
		return s.SaveCapsulePlayRecord(ctx, storyId, userId, *playContext)
	}, 10*time.Second); err != nil {
		return nil, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) PlayNowShotStory(
	ctx context.Context,
	userId int64,
	storyId int64,
	resourceType api_items_story_v1.ConsumeNowShotStoryRequest_ResourceType,
	imageKey domain_entities_resource.ImageResourcePath,
	videoKey domain_entities_resource.VideoResourcePath,
	startTime uint32,
) (*domain_entities_items.StoryDetail, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT)
	if err != nil {
		return nil, err
	}
	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetailWithStoryType(ctx, userId, storyId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT)
		if err != nil {
			return err
		}
		playContext := story.NowShotPlayContext
		playConfig := story.Summary.NowShotConfig
		switch playContext.ConsumeStatus {
		case api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_FAILED:
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("you have failed to unlock this story"))
		case api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK:
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("you have unlocked this story"))
		}
		// update start time
		if playContext.StartTime == 0 && startTime != 0 {
			playContext.StartTime = startTime
			playContext.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_LOCK
		}
		// check start time
		if playContext.StartTime == 0 {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("no start time specified"))
		}
		// save user submit record
		if len(imageKey) != 0 {
			playContext.UserSubmitImageKeys = append(playContext.UserSubmitImageKeys, imageKey)
			playContext.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK
			playContext.UserSubmittedResources = append(playContext.UserSubmittedResources, &domain_entities_items.SubmittedResource{
				Resource:      domain_entities_resource.NewResourceBuilder().WithImageObjectKey(imageKey, 0, 0).Build(),
				UpdatedAtUnix: time.Now().Unix(),
			})
		}
		if len(videoKey) != 0 {
			playContext.UserSubmitVideoKeys = append(playContext.UserSubmitVideoKeys, videoKey)
			playContext.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK
			playContext.UserSubmittedResources = append(playContext.UserSubmittedResources, &domain_entities_items.SubmittedResource{
				Resource:      domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(videoKey, "", 0, 0).Build(),
				UpdatedAtUnix: time.Now().Unix(),
			})
		}
		// check timeout
		if IsTimedOut(playContext.StartTime, playConfig.TTL) {
			playContext.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_FAILED
			if err := s.SaveNowShotPlayRecord(ctx, storyId, userId, *playContext); err != nil {
				return err
			}
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("running out of time to unlock this story "))
		}
		// send message to creator
		if playContext.ConsumeStatus == api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK {
			if err := s.sendNowShotMessage(ctx, userId, story, resourceType, imageKey, videoKey); err != nil {
				return err
			}
		}
		// not timeout
		return s.SaveNowShotPlayRecord(ctx, storyId, userId, *playContext)
	}, 10*time.Second); err != nil {
		return nil, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}

// PlayNowShotStoryV2 V2版本的NowShot消费逻辑
// 在V2版本中，客户端负责判断成功与否，服务端只需要记录结果
func (s *StoryPlayService) PlayNowShotStoryV2(
	ctx context.Context,
	userId int64,
	storyId int64,
	succeeded bool,
	resourceType api_items_story_v1.ConsumeNowShotStoryRequest_ResourceType,
	imageKey domain_entities_resource.ImageResourcePath,
	videoKey domain_entities_resource.VideoResourcePath,
	ttl *uint32,
) (*domain_entities_items.StoryDetail, error) {
	// 加锁，禁止用户并发消费
	lockKey, err := getLockKey(storyId, userId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT)
	if err != nil {
		return nil, err
	}
	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetailWithStoryType(ctx, userId, storyId, api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT)
		if err != nil {
			return err
		}

		playContext := story.NowShotPlayContext

		// 如果请求中提供了TTL，则保存到context中
		if ttl != nil {
			playContext.TTL = ttl
		}

		if succeeded {
			// 客户端判断成功，记录成功状态
			playContext.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK

			// 保存用户提交的资源
			if len(imageKey) != 0 {
				playContext.UserSubmitImageKeys = append(playContext.UserSubmitImageKeys, imageKey)
			}
			if len(videoKey) != 0 {
				playContext.UserSubmitVideoKeys = append(playContext.UserSubmitVideoKeys, videoKey)
			}

			// 发送消息给创作者
			if err := s.sendNowShotMessage(ctx, userId, story, resourceType, imageKey, videoKey); err != nil {
				return err
			}
		} else {
			// 客户端判断失败：仅在当前未处于 UNLOCK 时记录失败，避免已解锁被降级
			if playContext.ConsumeStatus != api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_UNLOCK {
				playContext.ConsumeStatus = api_items_story_types_v1.StoryPlayNowShotContext_CONSUME_STATUS_FAILED
			}
		}

		// 保存游戏记录
		return s.SaveNowShotPlayRecord(ctx, storyId, userId, *playContext)
	}, 10*time.Second); err != nil {
		return nil, err
	}

	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) sendUnmuteMessage(
	ctx context.Context,
	userId int64,
	story *domain_entities_items.StoryDetail,
	audioKey domain_entities_resource.AudioResourcePath,
	aiResponse string,
	consumeStatus api_im_message_types_v1.ConsumeStatus,
) error {
	payload := &api_im_message_types_v1.StoryUnmuteCustomMessagePayload{
		Title:            "The other party replied to your story",
		StoryId:          fmt.Sprintf("%d", story.Summary.Id),
		StoryCoverUrl:    story.Summary.GetStoryCoverPath().ItemPosterInSummary(),
		ConsumerAudioUrl: audioKey.URL(),
		AiResponse:       aiResponse,
	}
	switch story.Summary.UnmutePlayConfig.ResourceType {
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_IMAGE:
		payload.ConsumerInteractionImageUrl = story.Summary.UnmutePlayConfig.ResourceImageKey.ItemPosterInSummary()
	case api_items_story_types_v1.StoryPlayUnmuteConfig_RESOURCE_TYPE_VIDEO:
		payload.ConsumerInteractionImageUrl = story.Summary.UnmutePlayConfig.ThumbnailKey.ItemPosterInSummary()
	}

	return s.imService.SendCustomMessages(ctx, userId, &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryUnmuteInteraction{
			StoryUnmuteInteraction: payload,
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_UNMUTE_INTERACTION.String(),
		ConsumeStatus:     consumeStatus.String(),
	}, story.Summary.Author.ID)
}

func (s *StoryPlayService) sendExchangeImageMessage(
	ctx context.Context,
	userId int64,
	story *domain_entities_items.StoryDetail,
	imageKey domain_entities_resource.ImageResourcePath,
	interactImage domain_entities_resource.ImageResourcePath,
	nodeId string,
	matchStatus api_items_story_types_v1.ExchangeImageMatchStatus,
) error {
	consumeStatus := api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_FAILED.String()
	if matchStatus == api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_MATCHED {
		consumeStatus = api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String()
	} else if matchStatus == api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_PARTIALLY_MATCHED {
		consumeStatus = api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_PARTIAL_SUCCESSFUL.String()
	}
	storyId := story.Summary.Id
	customPayload := &api_im_message_types_v1.CustomMessagePayload{
		Payload: &api_im_message_types_v1.CustomMessagePayload_StoryExchangeImageInteraction{
			StoryExchangeImageInteraction: &api_im_message_types_v1.StoryExchangeImageInteractionCustomMessagePayload{
				ConsumerImageUrl:            imageKey.ItemPosterInSummary(),
				ConsumerInteractionImageUrl: interactImage.ItemPosterInSummary(),
				StoryId:                     fmt.Sprintf("%d", storyId),
				NodeId:                      nodeId,
				Title:                       "The other party replied to your story",
				StoryCoverUrl:               story.Summary.GetStoryCoverPath().ItemPosterInSummary(),
			},
		},
		CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_EXCHANGE_IMAGE_INTERACTION.String(),
		ConsumeStatus:     consumeStatus,
	}
	return s.imService.SendCustomMessages(ctx, userId, customPayload, story.Summary.Author.ID)
}

func (s *StoryPlayService) sendTurtleSoupMessage(
	ctx context.Context,
	story *domain_entities_items.StoryDetail,
	userId int64,
	context *domain_entities_items.StoryPlayTurtleSoupContext,
) error {
	// 基础的 story info json
	storyInfo := map[string]string{
		"story_id":         fmt.Sprintf("%d", story.Summary.Id),
		"story_cover_url":  story.Summary.GetStoryCoverPath().ItemPosterInSummary(),
		"caption":          story.Summary.TurtleSoupPlayConfig.Caption,
		"tips":             context.Tips,
		"ai_response":      context.AiResponse,
		"end_message":      story.Summary.TurtleSoupPlayConfig.EndMessage,
		"end_message_font": story.Summary.TurtleSoupPlayConfig.EndMessageFont,
	}
	storyInfoJson, err := json.Marshal(storyInfo)
	if err != nil {
		return errors.Wrapf(err, "story %#v", storyInfo)
	}
	consumeStatus := api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_FAILED.String()
	if context.IsFinished {
		consumeStatus = api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String()
	}
	/*
		// 构建 im 消息
		message := domain_services_im.CustomMessageSend{
			// 先拼接一条消费者的文字消息

			FromUserId: userId,
			ToUserId:   story.Summary.Author.ID,
			Payload: &api_im_message_types_v1.CustomMessagePayload{
				Payload: &api_im_message_types_v1.CustomMessagePayload_StoryTurtleSoupInteraction{
					StoryTurtleSoupInteraction: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload{
						PayloadType: api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_TEXT.String(),
						Payload: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_Text{
							Text: context.UserMessage,
						},
						UserText: context.UserMessage,
						AiResponse:       context.AiResponse,
						StoryInfoJsonStr: string(storyInfoJson),
					},
				},
				CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION.String(),
				ConsumeStatus:     consumeStatus,
			},
		}
	*/
	var message domain_services_im.CustomMessageSend
	// 根据 match 情况，拼接不同的消息，ai response作为创作者的回复
	// 如果完全匹配了，需要发送一条结束消息
	if context.IsFinished {
		message = domain_services_im.CustomMessageSend{
			FromUserId: userId,
			ToUserId:   story.Summary.Author.ID,
			Payload: &api_im_message_types_v1.CustomMessagePayload{
				Payload: &api_im_message_types_v1.CustomMessagePayload_StoryTurtleSoupInteraction{
					StoryTurtleSoupInteraction: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload{
						PayloadType: api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_END_IMAGE.String(),
						Payload: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_EndImagePayload_{
							EndImagePayload: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_EndImagePayload{
								EndImageUrl: story.GetStoryNextResourceCoverPath().ItemPosterInSummary(),
								EndMessage:  story.Summary.TurtleSoupPlayConfig.EndMessage,
							},
						},
						UserText:         context.UserMessage,
						AiResponse:       context.AiResponse,
						StoryInfoJsonStr: string(storyInfoJson),
					},
				},
				CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION.String(),
				ConsumeStatus:     consumeStatus,
			},
		}
		return s.imService.SendCustomMessagesWithSends(ctx, message)
	}

	// 如果没有匹配，给消费者再发送一条 ai response 的消息
	message = domain_services_im.CustomMessageSend{
		FromUserId: userId,
		ToUserId:   story.Summary.Author.ID,
		Payload: &api_im_message_types_v1.CustomMessagePayload{
			Payload: &api_im_message_types_v1.CustomMessagePayload_StoryTurtleSoupInteraction{
				StoryTurtleSoupInteraction: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload{
					PayloadType: api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_PAYLOAD_TYPE_TEXT.String(),
					Payload: &api_im_message_types_v1.StoryTurtleSoupCustomMessagePayload_Text{
						Text: context.AiResponse,
					},
					UserText:         context.UserMessage,
					AiResponse:       context.AiResponse,
					StoryInfoJsonStr: string(storyInfoJson),
				},
			},
			CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_TURTLE_SOUP_INTERACTION.String(),
			ConsumeStatus:     consumeStatus,
		},
	}
	return s.imService.SendCustomMessagesWithSends(ctx, message)
}

func (s *StoryPlayService) sendNowShotMessage(
	ctx context.Context,
	userId int64,
	story *domain_entities_items.StoryDetail,
	resourceType api_items_story_v1.ConsumeNowShotStoryRequest_ResourceType,
	imageKey domain_entities_resource.ImageResourcePath,
	videoKey domain_entities_resource.VideoResourcePath) error {
	payload := &api_im_message_types_v1.StoryNowShotCustomMessagePayload{
		Title:         "The other party replied to your story",
		StoryId:       fmt.Sprintf("%d", story.Summary.Id),
		StoryCoverUrl: story.Summary.GetStoryCoverPath().ItemPosterInSummary(),
	}
	switch resourceType {
	case api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_IMAGE:
		payload.PayloadType = api_im_message_types_v1.StoryNowShotCustomMessagePayload_PAYLOAD_TYPE_IMAGE.String()
		payload.ConsumerImageUrl = imageKey.ItemPosterInSummary()
	case api_items_story_v1.ConsumeNowShotStoryRequest_RESOURCE_TYPE_VIDEO:
		payload.PayloadType = api_im_message_types_v1.StoryNowShotCustomMessagePayload_PAYLOAD_TYPE_VIDEO.String()
		payload.ConsumerImageUrl = imageKey.ItemPosterInSummary() // thumbnail
		payload.ConsumerVideoUrl = videoKey.URL()
	}
	switch story.Summary.NowShotConfig.ResourceType {
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_IMAGE:
		payload.ConsumerInteractionImageUrl = story.Summary.NowShotConfig.ResourceImageKey.ItemPosterInSummary()
	case api_items_story_types_v1.StoryPlayNowShotConfig_RESOURCE_TYPE_VIDEO:
		payload.ConsumerInteractionVideoUrl = story.Summary.NowShotConfig.ResourceVideoKey.URL()
		payload.ConsumerInteractionImageUrl = story.Summary.NowShotConfig.ThumbnailKey.ItemPosterInSummary()
	}
	return s.imService.SendCustomMessagesWithSends(ctx, domain_services_im.CustomMessageSend{
		FromUserId: userId,
		ToUserId:   story.Summary.Author.ID,
		Payload: &api_im_message_types_v1.CustomMessagePayload{
			Payload: &api_im_message_types_v1.CustomMessagePayload_StoryNowShotInteraction{
				StoryNowShotInteraction: payload,
			},
			CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_NOW_SHOT_INTERACTION.String(),
			ConsumeStatus:     api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
		},
	})
}

func (s *StoryPlayService) sendCapsuleMessage(ctx context.Context, userId int64, story *domain_entities_items.StoryDetail, savedStory *domain_entities_items.StoryDetail) error {
	payload := &api_im_message_types_v1.StoryCapsuleCustomMessagePayload{
		Title:            "The other party replied to your story",
		StoryId:          fmt.Sprintf("%d", story.Summary.Id),
		StoryCoverUrl:    story.Summary.GetStoryCoverPath().ItemPosterInSummary(),
		ConsumerImageUrl: savedStory.Summary.CapsulePlayConfig.CoverImage.ItemPosterInSummary(),
		InDays:           savedStory.Summary.CapsulePlayConfig.CapsulePhotoInfo.InDays,
		Moments:          savedStory.Summary.CapsulePlayConfig.CapsulePhotoInfo.Moments,
	}
	return s.imService.SendCustomMessagesWithSends(ctx, domain_services_im.CustomMessageSend{
		FromUserId: userId,
		ToUserId:   story.Summary.Author.ID,
		Payload: &api_im_message_types_v1.CustomMessagePayload{
			Payload: &api_im_message_types_v1.CustomMessagePayload_StoryCapsuleInteraction{
				StoryCapsuleInteraction: payload,
			},
			CustomMessageType: api_im_message_types_v1.CustomMessageType_CUSTOM_MESSAGE_TYPE_STORY_CAPSULE_INTERACTION.String(),
			ConsumeStatus:     api_im_message_types_v1.ConsumeStatus_CONSUME_STATUS_SUCCESSFUL.String(),
		},
	})
}

func (s *StoryPlayService) UpdateStoryPostMomentStatus(ctx context.Context, userId int64, storyId int64) error {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return err
	}
	lockKey, err := getLockKey(storyId, userId, story.Summary.PlayType)
	if err != nil {
		return err
	}
	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}
		switch story.Summary.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
			story.WassupPlayContext.StoryBaseContext.IsPostMoment = true
			return s.SaveWassupPlayRecord(ctx, storyId, userId, *story.WassupPlayContext)
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
			story.ExchangeImagePlayContext.StoryBaseContext.IsPostMoment = true
			return s.SaveExchangeImagePlayRecord(ctx, storyId, userId, *story.ExchangeImagePlayContext, story.ExchangeImagePlayContext.IsFinished)
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
			story.NowShotPlayContext.StoryBaseContext.IsPostMoment = true
			return s.SaveNowShotPlayRecord(ctx, storyId, userId, *story.NowShotPlayContext)
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
			story.RoastedPlayContext.StoryBaseContext.IsPostMoment = true
			return s.SaveRoastedPlayRecord(ctx, storyId, userId, *story.RoastedPlayContext)
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
			story.ChatproxyContext.StoryBaseContext.IsPostMoment = true
			return s.SaveChatProxyPlayRecord(ctx, storyId, userId, *story.ChatproxyContext)
		default:
			return errors.New("story play type not supported")
		}
	}, 10*time.Second); err != nil {
		return err
	}

	return nil
}

func (s *StoryPlayService) UpdateUserSubmittedResource(ctx context.Context, userId int64, storyId int64, resource *domain_entities_resource.Resource) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	lockKey, err := getLockKey(storyId, userId, story.Summary.PlayType)
	if err != nil {
		return nil, err
	}
	if err := s.Locker.Lock(ctx, lockKey, func(ctx context.Context) error {
		story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
		if err != nil {
			return err
		}

		submittedResource := &domain_entities_items.SubmittedResource{
			Resource:      resource,
			UpdatedAtUnix: time.Now().Unix(),
		}

		switch story.Summary.PlayType {
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
			story.WassupPlayContext.StoryBaseContext.UserSubmittedResources = append(story.WassupPlayContext.StoryBaseContext.UserSubmittedResources, submittedResource)
			return s.SaveWassupPlayRecord(ctx, storyId, userId, *story.WassupPlayContext)
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
			story.RoastedPlayContext.StoryBaseContext.UserSubmittedResources = append(story.RoastedPlayContext.StoryBaseContext.UserSubmittedResources, submittedResource)
			return s.SaveRoastedPlayRecord(ctx, storyId, userId, *story.RoastedPlayContext)
		case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
			story.ChatproxyContext.StoryBaseContext.UserSubmittedResources = append(story.ChatproxyContext.StoryBaseContext.UserSubmittedResources, submittedResource)
			return s.SaveChatProxyPlayRecord(ctx, storyId, userId, *story.ChatproxyContext)
		default:
			return nil
		}
	}, 10*time.Second); err != nil {
		return nil, err
	}
	return s.queryService.GetStoryDetail(ctx, userId, storyId)
}

func getLockKey(storyId int64, userId int64, storyType api_items_story_types_v1.StoryPlayType) (string, error) {
	switch storyType {
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_BASE_PLAY:
		return fmt.Sprintf("story_play_base_play_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_TURTLE_SOUP:
		return fmt.Sprintf("story_play_turtle_soup_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_EXCHANGE_IMAGE:
		return fmt.Sprintf("story_play_exchange_image_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_ROASTED:
		return fmt.Sprintf("story_play_roasted_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_NOW_SHOT:
		return fmt.Sprintf("story_play_now_shot_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_UNMUTE:
		return fmt.Sprintf("story_play_unmute_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CAPSULE:
		return fmt.Sprintf("story_play_capsule_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_CHATPROXY:
		return fmt.Sprintf("story_play_chatproxy_story_%d_%d", storyId, userId), nil
	case api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_WASSUP_V2:
		return fmt.Sprintf("story_play_wassup_story_%d_%d", storyId, userId), nil
	default:
		return "", errors.New("story play type not supported")
	}
}

// IsTimedOut checks if startTime + ttl has passed compared to current time
func IsTimedOut(startTime uint32, ttl uint32) bool {
	now := uint32(time.Now().Unix()) // 当前时间戳（秒）
	return now >= startTime+ttl
}
