package domain_services_items_story

import (
	api_errors_v1 "boson/api/errors/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_interfaces "boson/internal/domain/interfaces"
	"boson/internal/infra/data"
	"context"
	"fmt"
	"math/rand/v2"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

var (
	showInfoUpdateLockKeyFormat = "update_haunt_boo_show_info_user_%d"
)

const (
	defaultFeedHauntBooShowInfoMaxShowCount = 2
	defaultFeedHauntBooShowInfoExpireAt     = time.Hour * 24 * 7
	defaultUserEveryDayMaxBooShowCount      = 6
	defaultFeedHauntBooShowInfoShowInterval = time.Minute * 2
)

type IHauntBooShowInfoRepository interface {
	GetUserHauntBooShowInfo(ctx context.Context, userId int64, scene api_items_story_types_v1.HauntBooShowInfoSence) (*domain_entities_items.HauntBooShowInfo, error)
	BatchGetContentPollutionHauntBooShowInfo(ctx context.Context, loginUserId int64, userIds []int64) (map[int64]*domain_entities_items.HauntBooShowInfo, error)
	BatchSaveUserHauntBooShowInfos(ctx context.Context, showInfos map[int64]*domain_entities_items.HauntBooShowInfo) error
}

type HauntBooShowInfoService struct {
	db                         *data.Data
	hauntBooShowInfoRepository IHauntBooShowInfoRepository
	locker                     domain_interfaces.Locker
}

func NewHauntBooShowInfoService(hauntBooShowInfoRepository IHauntBooShowInfoRepository, locker domain_interfaces.Locker, db *data.Data) *HauntBooShowInfoService {
	return &HauntBooShowInfoService{hauntBooShowInfoRepository: hauntBooShowInfoRepository, locker: locker, db: db}
}

func (s *HauntBooShowInfoService) BooCaptured(ctx context.Context, userId int64, booId int64, fromFeed bool) error {
	if fromFeed {
		showInfos, err := s.hauntBooShowInfoRepository.GetUserHauntBooShowInfo(ctx, userId, api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION)
		if err != nil {
			return err
		}
		if showInfos == nil {
			return nil
		}
		// 移除这个 Boo
		showInfos.BooShowInfos = lo.Filter(showInfos.BooShowInfos, func(showInfo *domain_entities_items.ShowInfo, _ int) bool {
			return showInfo.CreatorBooId != booId
		})
		return s.hauntBooShowInfoRepository.BatchSaveUserHauntBooShowInfos(ctx, map[int64]*domain_entities_items.HauntBooShowInfo{
			userId: showInfos,
		})
	}
	return nil
}

func (s *HauntBooShowInfoService) AppendHauntBooIntoUserShowInfo(
	ctx context.Context,
	userId int64,
	story *domain_entities_items.StoryDetail,
	isFromFeed bool,
) error {
	// 分布式锁
	key := fmt.Sprintf(showInfoUpdateLockKeyFormat, userId)
	return s.locker.Lock(ctx, key, func(ctx context.Context) error {
		sence := api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION
		if isFromFeed {
			// feed 进入时，污染内容
			sence = api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_CONTENT_POLLUTION
		}
		return s.appendHauntBooIntoUserShowInfo(ctx, userId, story, sence)
	}, time.Second*10)
}

func (s *HauntBooShowInfoService) ReportHauntBooShowEvent(
	ctx context.Context,
	userId int64,
	booId int64,
	scene api_items_story_types_v1.HauntBooShowInfoSence,
	objectId *int64,
) error {
	if scene == api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION {
		return s.reportFeedHauntBooShowInfoEvent(ctx, userId, booId)
	}
	return nil
}

func (s *HauntBooShowInfoService) GetUserHauntBooShowInfo(
	ctx context.Context,
	userId int64,
	sence api_items_story_types_v1.HauntBooShowInfoSence,
	// objectId 是针对 sence 的，比如 sence 是 SHOW_INFO_SENCE_MOMENT_POLLUTION 时，objectId 是 momentId
	objectId *int64,
) (*domain_entities_items.ShowInfo, error) {
	return s.getHauntBooShowInfo(ctx, userId, sence, objectId)
}

func (s *HauntBooShowInfoService) appendHauntBooIntoUserShowInfo(ctx context.Context, userId int64, story *domain_entities_items.StoryDetail, sence api_items_story_types_v1.HauntBooShowInfoSence) error {
	hauntBooShowInfo, err := s.hauntBooShowInfoRepository.GetUserHauntBooShowInfo(ctx, userId, sence)
	if err != nil {
		return err
	}
	if hauntBooShowInfo == nil {
		hauntBooShowInfo = &domain_entities_items.HauntBooShowInfo{
			BooShowInfos: make([]*domain_entities_items.ShowInfo, 0),
			Scene:        sence,
		}
	}
	hauntBooShowInfo.BooShowInfos = s.handleDirtyShowInfo(hauntBooShowInfo.BooShowInfos)

	// 找到是否有重复的 showInfo
	showInfo, ok := lo.Find(hauntBooShowInfo.BooShowInfos, func(showInfo *domain_entities_items.ShowInfo) bool {
		return showInfo.CreatorBooId == story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id
	})

	if ok {
		// count & expire 重置
		showInfo.ShowCount = 0
		showInfo.ExpireAt = uint32(time.Now().Add(defaultFeedHauntBooShowInfoExpireAt).Unix())
		return s.hauntBooShowInfoRepository.BatchSaveUserHauntBooShowInfos(ctx, map[int64]*domain_entities_items.HauntBooShowInfo{
			userId: hauntBooShowInfo,
		})
	}

	hauntBooShowInfo.BooShowInfos = append(hauntBooShowInfo.BooShowInfos, &domain_entities_items.ShowInfo{
		FromStoryId:  story.Summary.Id,
		CreatorBooId: story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id,
		AssistBooIds: lo.Map(story.Summary.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers, func(boo *domain_entities_items.HauntBoo, _ int) int64 {
			return boo.Id
		}),
		ExpireAt:     uint32(time.Now().Add(time.Hour * 24 * 30).Unix()),
		MaxShowCount: defaultFeedHauntBooShowInfoMaxShowCount,
		ShowCount:    0,
	})

	return s.hauntBooShowInfoRepository.BatchSaveUserHauntBooShowInfos(ctx, map[int64]*domain_entities_items.HauntBooShowInfo{
		userId: hauntBooShowInfo,
	})
}

func (s *HauntBooShowInfoService) InjectMomentHauntBooShowInfo(ctx context.Context, loginUserId int64, portals ...*domain_entities_items.Portal) error {
	creatorIds := lo.Map(portals, func(portal *domain_entities_items.Portal, _ int) int64 {
		return portal.Story.Summary.Author.ID
	})
	// 去除作者 & 去重
	creatorIds = lo.Filter(creatorIds, func(id int64, _ int) bool {
		return id != loginUserId
	})
	creatorIds = lo.Uniq(creatorIds)

	var err error
	// 过滤下那些已经针对 creator - user 下发了污染信息的 creators
	if creatorIds, err = s.contentPollutionFrequencyControl(ctx, loginUserId, creatorIds); err != nil {
		return err
	}
	if len(creatorIds) == 0 {
		return nil
	}

	// 检查这些作者的内容是否被污染了
	showInfos, err := s.hauntBooShowInfoRepository.BatchGetContentPollutionHauntBooShowInfo(ctx, loginUserId, creatorIds)
	if err != nil {
		return err
	}
	for _, showInfo := range showInfos {
		showInfo.BooShowInfos = s.handleDirtyShowInfo(showInfo.BooShowInfos)
	}

	// 对于所有内容，每个作者也只下发一次
	injectedCreatorIds := make(map[int64]struct{})

	for _, portal := range portals {
		if _, ok := injectedCreatorIds[portal.Story.Summary.Author.ID]; ok {
			continue
		}
		showInfo, ok := showInfos[portal.Story.Summary.Author.ID]
		if !ok || len(showInfo.BooShowInfos) == 0 {
			continue
		}
		// 随机挑选一个 moment 进行附着
		moment := portal.Moments[rand.IntN(len(portal.Moments))]
		moment.HauntShowInfo = showInfo.BooShowInfos[0]
	}

	// 记录下对他们的下发
	if err = s.recordContentPollutionShowInfo(ctx, loginUserId, lo.Keys(injectedCreatorIds)); err != nil {
		return err
	}

	return nil
}

func (s *HauntBooShowInfoService) InjectStoryHauntBooShowInfo(ctx context.Context, loginUserId int64, stories ...*domain_entities_items.StoryDetail) error {
	creatorIds := lo.Map(stories, func(story *domain_entities_items.StoryDetail, _ int) int64 {
		return story.Summary.Author.ID
	})
	// 去除作者 & 去重
	creatorIds = lo.Filter(creatorIds, func(id int64, _ int) bool {
		return id != loginUserId
	})
	creatorIds = lo.Uniq(creatorIds)

	var err error
	// 过滤下那些已经针对 creator - user 下发了污染信息的 creators
	if creatorIds, err = s.contentPollutionFrequencyControl(ctx, loginUserId, creatorIds); err != nil {
		return err
	}
	if len(creatorIds) == 0 {
		return nil
	}

	// 检查这些作者的内容是否被污染了
	showInfos, err := s.hauntBooShowInfoRepository.BatchGetContentPollutionHauntBooShowInfo(ctx, loginUserId, creatorIds)
	if err != nil {
		return err
	}
	for _, showInfo := range showInfos {
		showInfo.BooShowInfos = s.handleDirtyShowInfo(showInfo.BooShowInfos)
	}

	// 对于所有内容，每个作者也只下发一次
	injectedCreatorIds := make(map[int64]struct{})

	for _, story := range stories {
		if _, ok := injectedCreatorIds[story.Summary.Author.ID]; ok {
			continue
		}
		showInfo, ok := showInfos[story.Summary.Author.ID]
		if !ok || len(showInfo.BooShowInfos) == 0 {
			continue
		}
		story.HauntShowInfo = showInfo.BooShowInfos[0]
		injectedCreatorIds[story.Summary.Author.ID] = struct{}{}
	}

	// 记录下对他们的下发
	if err = s.recordContentPollutionShowInfo(ctx, loginUserId, lo.Keys(injectedCreatorIds)); err != nil {
		return err
	}

	return nil
}

func (s *HauntBooShowInfoService) getHauntBooShowInfo(ctx context.Context, userId int64, sence api_items_story_types_v1.HauntBooShowInfoSence, objectId *int64) (*domain_entities_items.ShowInfo, error) {
	showInfos, err := s.hauntBooShowInfoRepository.GetUserHauntBooShowInfo(ctx, userId, sence)
	if err != nil {
		return nil, err
	}
	if showInfos == nil {
		return nil, nil
	}
	showInfos.BooShowInfos = s.handleDirtyShowInfo(showInfos.BooShowInfos)
	showInfo := s.getFirstCountShowInfo(showInfos)
	if showInfo == nil {
		return nil, nil
	}
	return showInfo, nil
}

func (s *HauntBooShowInfoService) reportFeedHauntBooShowInfoEvent(
	ctx context.Context,
	userId int64,
	booId int64,
) error {
	feedShowInfo, err := s.hauntBooShowInfoRepository.GetUserHauntBooShowInfo(ctx, userId, api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION)
	if err != nil {
		return err
	}
	if feedShowInfo == nil {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("can't get user %d feed haunt boo show info", userId))
	}

	feedShowInfo.BooShowInfos = s.handleDirtyShowInfo(feedShowInfo.BooShowInfos)

	booShowInfo, ok := lo.Find(feedShowInfo.BooShowInfos, func(showInfo *domain_entities_items.ShowInfo) bool {
		return showInfo.CreatorBooId == booId
	})
	if !ok {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonContentNotFound("can't get this boo %d from user %d boo show info", booId, userId))
	}

	booShowInfo.ShowCount++
	booShowInfo.LatestShowAt = uint32(time.Now().Unix())
	feedShowInfo.LatestShowAt = uint32(time.Now().Unix())
	if booShowInfo.ShowCount >= booShowInfo.MaxShowCount {
		// 移除
		feedShowInfo.BooShowInfos = lo.Filter(feedShowInfo.BooShowInfos, func(showInfo *domain_entities_items.ShowInfo, _ int) bool {
			return showInfo.CreatorBooId != booId
		})
	}

	return s.hauntBooShowInfoRepository.BatchSaveUserHauntBooShowInfos(ctx, map[int64]*domain_entities_items.HauntBooShowInfo{
		userId: feedShowInfo,
	})
}

func (s *HauntBooShowInfoService) getFirstCountShowInfo(feedShowInfo *domain_entities_items.HauntBooShowInfo) *domain_entities_items.ShowInfo {
	if feedShowInfo == nil || len(feedShowInfo.BooShowInfos) == 0 {
		return nil
	}
	// 上次与现在展现间隔小于 defaultFeedHauntBooShowInfoShowInterval
	lastShowAtT := time.Unix(int64(feedShowInfo.LatestShowAt), 0)
	if lastShowAtT.After(time.Now()) && lastShowAtT.Add(defaultFeedHauntBooShowInfoShowInterval).Before(time.Now()) {
		return nil
	}
	return feedShowInfo.BooShowInfos[0]
}

func (s *HauntBooShowInfoService) handleDirtyShowInfo(showInfos []*domain_entities_items.ShowInfo) []*domain_entities_items.ShowInfo {
	// 过滤下那些异常的 showInfo
	return lo.Filter(showInfos, func(showInfo *domain_entities_items.ShowInfo, _ int) bool {
		return showInfo.ShowCount < showInfo.MaxShowCount && showInfo.ExpireAt > uint32(time.Now().Unix())
	})
}

var creatorContentPollutionShowInfoKeyFormat = "creator_content_pollution_creator_%d_user_%d"

// 针对内容污染的频率控制
func (s *HauntBooShowInfoService) contentPollutionFrequencyControl(ctx context.Context, userId int64, creatorIds []int64) ([]int64, error) {
	// 如果 creatorIds 为空，直接返回空切片，避免 MGet 命令参数错误
	if len(creatorIds) == 0 {
		return []int64{}, nil
	}

	keys := lo.Map(creatorIds, func(creatorId int64, _ int) string {
		return fmt.Sprintf(creatorContentPollutionShowInfoKeyFormat, creatorId, userId)
	})

	values, err := s.db.Rdb().MGet(ctx, keys...).Result()
	if err != nil {
		return nil, errors.Wrapf(err, "user %d, creator : %#v", userId, creatorIds)
	}

	// 过滤掉在Redis中存在的creatorId（即已经被记录过的）
	filteredCreatorIds := make([]int64, 0, len(creatorIds))
	for idx, value := range values {
		if value == nil {
			// 如果Redis中没有记录，说明这个creatorId没有被污染过，可以展示
			filteredCreatorIds = append(filteredCreatorIds, creatorIds[idx])
		}
	}

	return filteredCreatorIds, nil
}

// 记录对某用户下发了针对某些作者内容被污染记录，控制在 3*24hr 不在下发
func (s *HauntBooShowInfoService) recordContentPollutionShowInfo(ctx context.Context, userId int64, creatorIds []int64) error {
	keys := lo.Map(creatorIds, func(creatorId int64, _ int) string {
		return fmt.Sprintf(creatorContentPollutionShowInfoKeyFormat, creatorId, userId)
	})

	// 批量设置Redis记录，有效期3天
	expiration := time.Hour * 24 * 3
	for _, key := range keys {
		if err := s.db.Rdb().Set(ctx, key, "1", expiration).Err(); err != nil {
			return errors.Wrapf(err, "user %d, creatorIds : %#v, key : %s", userId, creatorIds, key)
		}
	}

	return nil
}
