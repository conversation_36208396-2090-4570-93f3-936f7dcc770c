package domain_services_items_story

import (
	"context"
	"fmt"
	"strings"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_services_ai "boson/internal/domain/services/ai"
	domain_services_ai_types "boson/internal/domain/services/ai/types"

	"github.com/samber/lo"
	"github.com/sashabaranov/go-openai"
)

type ChatProxyConfig struct {
	Greetings []*domain_entities_items.StoryPlayChatProxyGreeting
}

type CreateChatProxyStoryAttr struct {
	StoryBaseCreateAttr
	PlayConfig *domain_entities_items.StoryPlayChatProxyConfig
}

func (s *StoryPlayService) ConsumeChatProxy(
	ctx context.Context,
	userId int64,
	storyId int64,
) (*domain_entities_items.StoryDetail, error) {
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, err
	}
	return story, nil
}

func (s *StoryPlayService) GetChatProxyNextTopics(
	ctx context.Context,
	userId int64,
	storyId int64,
	userAudioKey string,
	roundIndex uint32,
) (*domain_entities_items.StoryDetail, *domain_entities_items.ChatProxyQuestion, bool, error) {
	userAnswer, _, err := s.asrService.Transcribe(ctx, userAudioKey, nil, nil)
	if err != nil {
		return nil, nil, false, err
	}
	story, err := s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, nil, false, err
	}

	// 默认是从nil开始
	var history_msg []openai.ChatCompletionMessage

	// 定义 greeting，第一轮时使用故事的开场白
	var greeting string
	if roundIndex == 0 && story.Summary.ChatProxyPlayConfig.Greeting != nil {
		greeting = story.Summary.ChatProxyPlayConfig.Greeting.Content
	}

	if roundIndex > 0 {
		// 还在一个 session 内，此时要从 context 取出历史记录
		history_msg = lo.Map(story.ChatproxyContext.HistoryMsgs, func(item *domain_entities_items.ChatproxyMsg, _ int) openai.ChatCompletionMessage {
			return openai.ChatCompletionMessage{
				Role:    item.Role,
				Content: item.Content,
			}
		})
	}

	res, history_msg_after_llm, err := s.llmService.GenerateChatProxyResponse(ctx, &domain_services_ai.ChatProxyLLMRequest{
		Caption:     story.Summary.ChatProxyPlayConfig.Caption.Content,
		CreatorName: story.Summary.Author.Nickname,
		Topics:      story.Summary.ChatProxyPlayConfig.Topics,
		A1:          userAnswer,
	}, history_msg, greeting)
	if err != nil {
		return nil, nil, false, err
	}

	type ParsedLine struct {
		Text string
		Tag  string
	}

	var parsedLines []ParsedLine

	// 2. Process each line to extract the text and the tag from the end.
	for _, line := range res.Response {
		trimmedLine := strings.TrimSpace(line)
		if trimmedLine == "" {
			continue // Skip any empty lines that might result from splitting.
		}

		// Find the last '[' which marks the beginning of the tag.
		lastOpenBracket := strings.LastIndex(trimmedLine, "[")
		// Find the last ']' which marks the end of the tag.
		lastCloseBracket := strings.LastIndex(trimmedLine, "]")

		// Basic validation to ensure we found a pair of brackets in the correct order.
		if lastOpenBracket == -1 || lastCloseBracket == -1 || lastOpenBracket > lastCloseBracket {
			// This line does not conform to the "Text [Tag]" format.
			// You can choose to log this event or handle it as an error.
			// For this example, we'll skip malformed lines.
			continue
		}

		// Extract the text part (everything before the last '[') and trim whitespace.
		text := strings.TrimSpace(trimmedLine[:lastOpenBracket])

		// Extract the tag part (everything between the brackets) and trim whitespace.
		tag := strings.TrimSpace(trimmedLine[lastOpenBracket+1 : lastCloseBracket])

		// Only add the parsed line if both text and tag are non-empty.
		if text != "" && tag != "" {
			parsedLines = append(parsedLines, ParsedLine{Text: text, Tag: tag})
		}
	}

	// 3. Now, build the two separate strings from our parsed data.
	var ttsParts []string
	var frontendParts []string

	for _, p := range parsedLines {
		// Add the clean text to the list for the TTS string.
		ttsParts = append(ttsParts, p.Text)

		// Format the line back to the OLD format "[Tag] Text" for the frontend.
		frontendLine := fmt.Sprintf("%s [%s]", p.Text, p.Tag)
		frontendParts = append(frontendParts, frontendLine)
	}

	ttsQuestion := strings.Join(ttsParts, "\n<break time=\"0.4s\" />\n")
	frontendQuestion := strings.Join(frontendParts, "\\n")

	// 生成音频
	ttsResult, words, err := s.ttsService.TextToSpeechWithCDNURL(
		ctx,
		userId,
		ttsQuestion,
		domain_services_ai.ChatProxyCaption,
	)
	if err != nil {
		return nil, nil, false, err
	}

	// save context
	story.ChatproxyContext.Unlocked = res.End
	story.ChatproxyContext.HistoryMsgs = lo.Map(history_msg_after_llm, func(item openai.ChatCompletionMessage, _ int) *domain_entities_items.ChatproxyMsg {
		return &domain_entities_items.ChatproxyMsg{
			Role:    item.Role,
			Content: item.Content,
		}
	})
	if err := s.IStoryPlayRecordRepo.SaveChatProxyPlayRecord(ctx, storyId, userId, *story.ChatproxyContext); err != nil {
		return nil, nil, false, err
	}

	story, err = s.queryService.GetStoryDetail(ctx, userId, storyId)
	if err != nil {
		return nil, nil, false, err
	}
	return story, &domain_entities_items.ChatProxyQuestion{
		Question: frontendQuestion,
		Words: lo.Map(words, func(word *domain_services_ai_types.WordAttr, _ int) *api_items_story_types_v1.Word {
			return &api_items_story_types_v1.Word{
				Text:      word.Word,
				StartTime: word.StartTime,
				EndTime:   word.EndTime,
			}
		}),
		TtsAudioUrl: ttsResult.URL(),
	}, res.End, nil
}

func (s *StoryCmdService) CreateChatProxyStory(
	ctx context.Context,
	loginUserId int64,
	attr CreateChatProxyStoryAttr,
) (*domain_entities_items.StoryDetail, error) {
	attr.Id = s.uniqIdGen.Generate()
	attr.CreatorID = loginUserId
	attr.Version = lo.ToPtr(int32(domain_entities_items.StoryVersion_V2))

	captionTTS, words, err := s.ttsService.TextToSpeechWithCDNURL(
		ctx,
		loginUserId,
		attr.PlayConfig.Caption.Content,
		domain_services_ai.ChatProxyCaption,
	)
	if err != nil {
		return nil, err
	}

	// 获取用户信息用于生成开场白
	userInfo, err := s.contextService.userQueryService.GetUserDetail(ctx, loginUserId, loginUserId)
	if err != nil {
		return nil, err
	}

	// 使用模型生成开场白
	openingContent, err := s.contextService.llmService.GenerateChatProxyOpening(ctx, &domain_services_ai.ChatProxyLLMOpeningRequest{
		Caption:     attr.PlayConfig.Caption.Content,
		CreatorName: userInfo.Summary.Nickname,
		Topics:      attr.PlayConfig.Topics,
	})
	if err != nil {
		return nil, err
	}

	// 为开场白生成 TTS 音频
	openingTTS, openingWords, err := s.ttsService.TextToSpeechWithCDNURL(
		ctx,
		loginUserId,
		openingContent,
		domain_services_ai.ChatProxyCaption,
	)
	if err != nil {
		return nil, err
	}

	// 设置生成的开场白
	attr.PlayConfig.Greeting = &domain_entities_items.StoryPlayChatProxyGreeting{
		Content:     openingContent,
		TtsAudioUrl: openingTTS.URL(),
		Words: lo.Map(openingWords, func(word *domain_services_ai_types.WordAttr, _ int) *api_items_story_types_v1.Word {
			return &api_items_story_types_v1.Word{
				Text:      word.Word,
				StartTime: word.StartTime,
				EndTime:   word.EndTime,
			}
		}),
	}

	attr.PlayConfig.Caption.TtsAudioUrl = captionTTS.URL()
	attr.PlayConfig.Caption.Words = lo.Map(words, func(word *domain_services_ai_types.WordAttr, _ int) *api_items_story_types_v1.Word {
		return &api_items_story_types_v1.Word{
			Text:      word.Word,
			StartTime: word.StartTime,
		}
	})

	if err := s.IStoryCmdRepository.CreateChatProxyStory(ctx, attr); err != nil {
		return nil, err
	}

	story, err := s.storyQueryService.GetStoryDetail(ctx, loginUserId, attr.Id)
	if err != nil {
		return nil, err
	}
	return story, nil
}
