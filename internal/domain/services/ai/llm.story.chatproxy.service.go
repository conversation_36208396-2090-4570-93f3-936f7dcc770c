package domain_services_ai

import (
	repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	"context"
	"encoding/json"

	// "math/rand"

	"github.com/pkg/errors"
	"github.com/sashabaranov/go-openai"
)

// ChatProxyRequest
// "Caption": "Ask me out for a drink.",
// "CreatorName": "Phoebe",
// "Topics": [     "Do you have a favorite spot for drinks?",     "What happens after the drink?",     "Be honest… do I look good in this photo?"   ],
// "A1": "Iris bar is good. it's by the chef of Nisei. environment is cozy and drink is delicate japanese cocktail with decent snacks"
type ChatProxyLLMRequest struct {
	Caption     string   `json:"Caption"`
	CreatorName string   `json:"CreatorName"`
	Topics      []string `json:"Topics"`
	A1          string   `json:"A1"`
}

type ChatProxyLLMOpeningRequest struct {
	Caption     string   `json:"Caption"`
	CreatorName string   `json:"CreatorName"`
	Topics      []string `json:"Topics"`
}

// ChatProxyResponse
//
//	{
//		"response": [
//			"That sounds like a perfect mix — classy but still cozy. [Affection]",
//			"What kind of vibe do you usually go for when you're picking a spot? [Curiosity]"
//		],
//		"end": false
//	}
type ChatProxyLLMResponse struct {
	Response []string `json:"response"`
	End      bool     `json:"end"`
}

type ChatProxyOpeningResponse struct {
	Opening string `json:"opening"`
}

func (s *LlmService) GenerateChatProxyOpening(ctx context.Context, request *ChatProxyLLMOpeningRequest) (string, error) {
	llmConfig, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.ChatProxyLLMConfig)
	if err != nil {
		return "", errors.WithStack(err)
	}

	system_prompt := llmConfig.ChatProxyAIResponse.OpeningSystemPrompt

	// json encode the request
	request_json, err := json.Marshal(request)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// prepare messages
	messages := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: system_prompt,
		},
		{
			Role:    "user",
			Content: string(request_json),
		},
	}

	// call openrouter complete with response format
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "chat_proxy_opening",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"opening": map[string]string{
						"type": "string",
					},
				},
				"required":             []string{"opening"},
				"additionalProperties": false,
			},
		},
	}

	response, err := s.OpenRouterComplete(ctx, messages, llmConfig.ChatProxyAIResponse.Model, llmConfig.ChatProxyAIResponse.Temperature, responseFormat, nil, nil)
	if err != nil {
		return "", errors.WithStack(err)
	}

	// parse the response
	var openingResponse ChatProxyOpeningResponse
	if err := json.Unmarshal([]byte(response), &openingResponse); err != nil {
		return "", errors.WithStack(err)
	}

	return openingResponse.Opening, nil
}

func (s *LlmService) GenerateChatProxyResponse(ctx context.Context, request *ChatProxyLLMRequest, history_msg []openai.ChatCompletionMessage, greeting string) (ChatProxyLLMResponse, []openai.ChatCompletionMessage, error) {
	llmConfig, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.ChatProxyLLMConfig)
	if err != nil {
		return ChatProxyLLMResponse{}, history_msg, err
	}

	// make a copy of history_msg for the final return
	history_msg_copy := make([]openai.ChatCompletionMessage, len(history_msg))
	copy(history_msg_copy, history_msg)

	// if length of history_msg is 0, then we need to generate the first message
	if len(history_msg) == 0 {
		system_prompt := llmConfig.ChatProxyAIResponse.SystemPrompt
		history_msg = append(history_msg, openai.ChatCompletionMessage{
			Role:    "system",
			Content: system_prompt,
		})
		// Also add to the copy that will be returned
		history_msg_copy = append(history_msg_copy, openai.ChatCompletionMessage{
			Role:    "system",
			Content: system_prompt,
		})

		// If greeting is provided, add it as the first assistant message
		if greeting != "" {
			greetingMessage := openai.ChatCompletionMessage{
				Role:    "assistant",
				Content: greeting,
			}
			history_msg = append(history_msg, greetingMessage)
			history_msg_copy = append(history_msg_copy, greetingMessage)
		}
	}

	// json encode a request, and add the string to user prompt
	request_json, err := json.Marshal(request)
	if err != nil {
		return ChatProxyLLMResponse{}, history_msg_copy, errors.WithStack(err)
	}
	userMessage := openai.ChatCompletionMessage{
		Role:    "user",
		Content: string(request_json),
	}
	history_msg = append(history_msg, userMessage)
	// Also add to the copy that will be returned
	history_msg_copy = append(history_msg_copy, userMessage)

	// call openrouter complete
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "chat_proxy_response",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"response": map[string]interface{}{
						"type": "array",
						"items": map[string]string{
							"type": "string",
						},
					},
					"end": map[string]string{"type": "boolean"},
				},
				"required":             []string{"response", "end"},
				"additionalProperties": false,
			},
		},
	}

	response, err := s.OpenRouterComplete(ctx, history_msg, llmConfig.ChatProxyAIResponse.Model, llmConfig.ChatProxyAIResponse.Temperature, responseFormat, nil, nil)
	if err != nil {
		return ChatProxyLLMResponse{}, history_msg_copy, errors.WithStack(err)
	}

	// parse the response
	var chatProxyResponse ChatProxyLLMResponse
	if err := json.Unmarshal([]byte(response), &chatProxyResponse); err != nil {
		return ChatProxyLLMResponse{}, history_msg_copy, errors.WithStack(err)
	}

	// append assistant output to history_msg_copy
	history_msg_copy = append(history_msg_copy, openai.ChatCompletionMessage{
		Role:    "assistant",
		Content: response,
	})

	// return the response and history_msg_copy
	return chatProxyResponse, history_msg_copy, nil
}
