package domain_services_ai

import (
	repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	"boson/internal/conf"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"context"
	"encoding/json"
	"math/rand"
	"time"

	"github.com/pkg/errors"
	"github.com/sa<PERSON><PERSON>nov/go-openai"
	// "math/rand"
)

type HauntLLMJudgeRequest struct {
	ImageKey      domain_entities_resource.ImageResourcePath
	TextCondition string
}

type HauntLLMJudgeResponse struct {
	Pass     bool
	Point    []float64
	Feedback string
}

func (s *LlmService) HauntLLMJudge(ctx context.Context, request *HauntLLMJudgeRequest) (HauntLLMJudgeResponse, error) {
	// 1. Load LLM configuration
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.HauntLLMConfig)
	if err != nil {
		return HauntLLMJudgeResponse{}, errors.Wrapf(err, "failed to load haunt LLM config")
	}

	hauntPrompt := llmPrompt.HauntAIResponse

	// 2. Build user prompt with image and text condition
	userPromptContent := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: "Please evaluate this image against the following condition:\n\nCondition: " + request.TextCondition + "\n\nDoes the image meet this condition? If yes, also identify the most relevant point in the image.",
		},
		{
			Type: openai.ChatMessagePartTypeImageURL,
			ImageURL: &openai.ChatMessageImageURL{
				URL:    request.ImageKey.UserCopilotMessage(),
				Detail: openai.ImageURLDetailAuto,
			},
		},
	}

	// 3. Prepare messages for LLM
	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: hauntPrompt.SystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}

	// 4. Define response format - 使用新的 JSON Schema
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"match_level": map[string]string{"type": "integer"},
					"feedback":    map[string]string{"type": "string"},
				},
				"required":             []string{"match_level", "feedback"},
				"additionalProperties": false,
			},
		},
	}

	// 5. Call LLM
	response, err := s.OpenRouterComplete(ctx, prompts, hauntPrompt.Model, hauntPrompt.Temperature, responseFormat, nil, nil)
	if err != nil {
		return HauntLLMJudgeResponse{}, errors.Wrapf(err, "failed to call OpenRouter for haunt judgment")
	}

	// 6. Parse response - new format with additional fields
	var parsed struct {
		MatchLevel int    `json:"match_level"`
		Feedback   string `json:"feedback"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return HauntLLMJudgeResponse{}, errors.Wrapf(err, "failed to parse haunt LLM response: %s", response)
	}

	// 7. Convert match_level to pass (match_level 3 = pass, match_level 1 = fail)
	pass := parsed.MatchLevel == 3

	// 8. Use fixed point [0.5, 0.5] for backward compatibility
	fixedPoint := []float64{0.5, 0.5}

	return HauntLLMJudgeResponse{
		Pass:     pass,
		Point:    fixedPoint,
		Feedback: parsed.Feedback,
	}, nil
}

type HauntLLMHintRequest struct {
	WhenToAppear string
	CreatorHint  string // would be empty "" if not specified
	CreatorName  string
}

type HauntLLMHintResponse struct {
	HintToDisplay string
	Meaningful    bool
}

func (s *LlmService) GenerateHauntLLMHint(ctx context.Context, request *HauntLLMHintRequest) (HauntLLMHintResponse, error) {
	if s.conf.Env == conf.ENV_ENV_UT {
		time.Sleep(3 * time.Second)
		return HauntLLMHintResponse{
			HintToDisplay: "测试提示",
		}, nil
	}
	// 1. Load LLM configuration
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.HauntLLMConfig)
	if err != nil {
		return HauntLLMHintResponse{}, errors.Wrapf(err, "failed to load haunt LLM config")
	}

	hauntPrompt := llmPrompt.HauntAIResponse

	// 2. Build user prompt with condition using the new template
	userPrompt := "## Condition\n" + request.WhenToAppear +
		"\n\n## Hint\n" + request.CreatorHint +
		"\n\n## CreatorName\n" + request.CreatorName

	// 3. Prepare messages for LLM
	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: hauntPrompt.HintSystemPrompt,
		},
		{
			Role:    "user",
			Content: userPrompt,
		},
	}

	// 4. Define response format for hint generation
	// {
	// 	"sentence": "Rewritten sentence",
	// 	'meaningful': "True or False",
	//   }
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "haunt_hint",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"sentence":   map[string]string{"type": "string"},
					"meaningful": map[string]string{"type": "boolean"},
				},
				"required":             []string{"sentence", "meaningful"},
				"additionalProperties": false,
			},
		},
	}

	// 5. Call LLM
	response, err := s.OpenRouterComplete(ctx, prompts, hauntPrompt.Model, hauntPrompt.Temperature, responseFormat, nil, nil)
	if err != nil {
		return HauntLLMHintResponse{}, errors.Wrapf(err, "failed to call OpenRouter for haunt hint generation")
	}

	// 6. Parse response
	var parsed struct {
		Sentence   string `json:"sentence"`
		Meaningful bool   `json:"meaningful"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return HauntLLMHintResponse{}, errors.Wrapf(err, "failed to parse haunt hint response: %s", response)
	}

	// 7. If meaningful is true, then return sentence. else, return `Creator Name is keeping changing their mind. Feel free to take a shoot!`
	hintToDisplay := parsed.Sentence
	if !parsed.Meaningful {
		// hintToDisplay = request.CreatorName + " is keeping changing their mind. Feel free to take a shoot!"

		// Randomly sample from the following
		//  '<creatorname> is keeping changing their mind. Shoot anything!', 'Find me wandering nearby when you least expect it.', '<creatorname> would like you to shoot anything that you like.', '<creatorname> is letting me appear whenever I want! Boo!'
		sampleList := []string{
			request.CreatorName + " is keeping changing their mind. Shoot anything!",
			"Find me wandering nearby when you least expect it.",
			request.CreatorName + " would like you to shoot anything that you like.",
			request.CreatorName + " is letting me appear whenever I want! Boo!",
		}
		hintToDisplay = sampleList[rand.Intn(len(sampleList))]
	}

	return HauntLLMHintResponse{
		HintToDisplay: hintToDisplay,
		Meaningful:    parsed.Meaningful,
	}, nil
}

type HauntLLMReactionRequest struct {
	UserTypedHowToReactWhenCaptured string
}

type HauntLLMReactionResponse struct {
	VideoPromptHowToReactWhenCaptured string
}

func (s *LlmService) HauntLLMReaction(ctx context.Context, request *HauntLLMReactionRequest) (HauntLLMReactionResponse, error) {
	if s.conf.Env == conf.ENV_ENV_UT {
		time.Sleep(3 * time.Second)
		return HauntLLMReactionResponse{
			VideoPromptHowToReactWhenCaptured: "测试提示",
		}, nil
	}
	// 1. Build user prompt with customized content
	userPrompt := "Rephrase '" + request.UserTypedHowToReactWhenCaptured +
		"' into a prompt for KlingAI video generation, including detailed facial expression plus body movement. " +
		"Make it exaggerated and enhance expressiveness. Don't be too complex. " +
		"Must avoid the risk of introducing any special effects like 'radiating energy'. " +
		"Use simple words. Begin with 'Animate the chibi-style 3D ghost as it is: '. " +
		"Use fewer adjectives and adverbs unless necessary, and more specific descriptions. " +
		"The prompt must follow exactly the original meaning of the user input. " +
		"The character has no legs/feet, so do not include them or actions like walking/running/jumping/spinning/pulsing. " +
		"Do not use words like 'as if', 'like', 'forward', 'upward'."

	// 2. Prepare messages for LLM
	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: "You are an assistant that rephrase user input into an exaggerated and expressive prompt for video generation.",
		},
		{
			Role:    "user",
			Content: userPrompt,
		},
	}

	// 3. Call LLM with hardcoded settings
	model := "openai/gpt-4o-2024-11-20"
	temperature := 0.7

	response, err := s.OpenRouterComplete(ctx, prompts, model, temperature, NewResponseFormat{}, nil, nil)
	if err != nil {
		return HauntLLMReactionResponse{}, errors.Wrapf(err, "failed to call OpenRouter for haunt reaction generation")
	}

	// Wrap the response with the template
	wrappedResponse := "Keep the character within the window throughout the animation. Keep the green background. Keep every aspect of the reference appearance (facial features, hairstyle, eye/skin color, body parts) with zero alteration — no clothing, no post-processing, no legs, no feet, no added elements, no special effects! No visible teeth. " +
		response +
		" Tail: Keep the ghost tail exactly as in the reference — a single simple, short, smooth ghost-like shape."

	return HauntLLMReactionResponse{
		VideoPromptHowToReactWhenCaptured: wrappedResponse,
	}, nil
}

// 用户没有填"How will boo react when found "，从候选Prompt里sample一个填入
type HauntLLMSampledReactionResponse struct {
	VideoPromptHowToReactWhenCaptured string
}

func (s *LlmService) HauntLLMSampledReaction(ctx context.Context) (HauntLLMSampledReactionResponse, error) {
	if s.conf.Env == conf.ENV_ENV_UT {
		time.Sleep(3 * time.Second)
		return HauntLLMSampledReactionResponse{
			VideoPromptHowToReactWhenCaptured: "测试提示",
		}, nil
	}
	// Define candidate reactions
	candidateReactions := []string{
		"Animate the chibi-style 3D ghost as it is: mischievous and full of playful energy. Make it tilt its round head side to side quickly, wiggle its small arms in the air, and puff out its cheeks with a cheeky grin. Let it sway its body left and right in slow, bouncy motions, while its big, shiny eyes squint with joy.",
		"Animate the chibi-style 3D ghost as it is leaning forward slightly, puffing out its cheeks with a sly smirk, and narrowing its eyes. Its small arms wave quickly in sharp, taunting motions, pointing at an unseen target and clapping mockingly. The ghost's head tilts side to side with exaggerated movements, while its mouth stretches into a wide, mischievous grin, showing teeth briefly before returning to smirking. ",
		"Animate the chibi-style 3D ghost as it is puffing out its cheeks to look annoyed, its round body leaning slightly closer, arms flailing with quick, sharp movements. Its big eyes narrow into a glare, eyebrows furrow deeply, and its mouth forms a pouty, determined frown. ",
		"Animate the chibi-style 3D ghost as it is: gasping sharply, its eyes widening rapidly to oversized ovals, mouth dropping open into a perfect \"O\" shape. Its small arms shoot out to its sides, trembling slightly in a frozen pose of shock. The ghost leans back slightly, its entire body shaking subtly to emphasize the sudden burst of surprise. ",
		"Animate the chibi-style 3D ghost in a slow, sinuous sway, it drifts side to side. It tilts its head at a teasing angle, a sultry smirk curling its lips as eyes with irresistible allure. Both arms sway languidly, fingertips play through the air.",
	}

	// Generate random index
	randomIndex := rand.Intn(len(candidateReactions))

	ret := candidateReactions[randomIndex]

	wrappedRet := "Keep the character within the window throughout the animation. Keep the green background. Keep every aspect of the reference appearance (facial features, hairstyle, eye/skin color, body parts) with zero alteration — no clothing, no post-processing, no legs, no feet, no added elements, no special effects! No visible teeth. " +
		ret +
		" Tail: Keep the ghost tail exactly as in the reference — a single simple, short, smooth ghost-like shape."

	// Return randomly selected reaction
	return HauntLLMSampledReactionResponse{
		VideoPromptHowToReactWhenCaptured: wrappedRet,
	}, nil
}
