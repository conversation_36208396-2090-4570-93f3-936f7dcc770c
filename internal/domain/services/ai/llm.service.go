package domain_services_ai

import (
	api_fizz_resource_types_v1 "boson/api/fizz/resource/types/v1"
	api_fizz_stirup_v1 "boson/api/fizz/stirup/v1"
	adapter_driving_services_llm "boson/internal/adapter/driving/services/llm"
	adapter_driving_services_openrouter "boson/internal/adapter/driving/services/openrouter"
	"boson/internal/conf"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"github.com/sashabaranov/go-openai"
	"gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/samber/lo"

	api_items_story_types_v1 "boson/api/items/story/types/v1"
	repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_fizz "boson/internal/domain/entities/fizz"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_fizz "boson/internal/domain/services/fizz"
	"boson/internal/infra/data"
	"boson/internal/infra/data/dal"
	"boson/internal/infra/data/model"
)

type ImageContent struct {
	Type     string `json:"type"`
	ImageURL struct {
		URL string `json:"url"`
	} `json:"image_url"`
}

// UserPrompt represents the user prompt structure.
type UserPrompt struct {
	Role    string        `json:"role"`
	Content []interface{} `json:"content"`
}

type ResponseFormatSchema map[string]interface{}

func (r *ResponseFormatSchema) MarshalJSON() ([]byte, error) {
	type alias ResponseFormatSchema
	return json.Marshal((*alias)(r))
}

// ResponseFormat represents the OpenAI response format.
type ResponseFormat struct {
	Type       string `json:"type"`
	JSONSchema *struct {
		Name   string                 `json:"name"`
		Schema map[string]interface{} `json:"schema"`
	} `json:"json_schema,omitempty"`
}

// NewResponseFormat represents the OpenAI response format.
type NewResponseFormat struct {
	Type       string `json:"type"`
	JSONSchema *struct {
		Name   string                `json:"name"`
		Schema *ResponseFormatSchema `json:"schema"`
	} `json:"json_schema,omitempty"`
}

type ILlmClient interface {
	Complete(ctx context.Context, prompts []interface{}, model string, temperature float64, responseFormat ResponseFormat) (string, error)
	CompleteWithStream(ctx context.Context, prompts []interface{}, model string, temperature float64, responseFormat ResponseFormat) (<-chan string, error)
	ChatCompletion(ctx context.Context, cfg adapter_driving_services_llm.ChatCompletionConfig, messages []openai.ChatCompletionMessage) (string, error)
}

var _ domain_services_fizz.IJoinConditionChecker = &LlmService{}
var _ domain_services_fizz.ICaptionGenerator = &LlmService{}

type LlmService struct {
	conf        *conf.Bootstrap
	logger      *log.Helper
	client      ILlmClient
	asrClient   IAsrClient
	appSettings repos_app_settings.ConfigStorage
	openRouter  *adapter_driving_services_openrouter.Client
	data        *data.Data
	cdnService  domain_interfaces.ICDN
}

func NewLlmService(
	config *conf.Bootstrap,
	logger *log.Helper,
	client ILlmClient,
	asrClient IAsrClient,
	appSettings repos_app_settings.ConfigStorage,
	openRouter *adapter_driving_services_openrouter.Client,
	data *data.Data,
	cdnService domain_interfaces.ICDN,
) *LlmService {
	return &LlmService{
		conf:        config,
		logger:      logger,
		client:      client,
		asrClient:   asrClient,
		appSettings: appSettings,
		openRouter:  openRouter,
		data:        data,
		cdnService:  cdnService,
	}
}

// Stirup settings
type StirupHint struct {
	ShootingMode string `json:"shooting_mode"`
	Caption      string `json:"caption"`
}

type StageInputPanel struct {
	Frames           []domain_entities_resource.ImageResourcePath `json:"frames"`
	ImageDescription string                                       `json:"image_description,omitempty"`
	AsrResult        string                                       `json:"asr_result"`
}

type StirupPanelInput struct {
	UserInputTheme  string          `json:"user_input_theme"`
	UserInputStyle  string          `json:"user_input_style"`
	HintStage0      StirupHint      `json:"init_hint"`
	UserInputPanel1 StageInputPanel `json:"user_input_panel1"`
	HintStage1      StirupHint      `json:"hint_stage1"`
	UserInputPanel2 StageInputPanel `json:"user_input_panel2"`
	HintStage2      StirupHint      `json:"hint_stage2"`
	UserInputPanel3 StageInputPanel `json:"user_input_panel3"`
}

type WassupVideo struct {
	// a list of images
	Frames    []*domain_entities_resource.ImageResourcePath `json:"frames"`
	AsrResult string                                        `json:"asr_result"`
}

type CapsuleChatRound struct {
	Guidance string `json:"guidance"`
	Reply    string `json:"reply,omitempty"`
}

type CapsuleChat struct {
	ChatRounds []CapsuleChatRound `json:"chat_rounds"`
}

type LlmConfig struct {
	Snapshot struct {
		SystemPrompt       string  `json:"system_prompt"`
		UserPromptTemplate string  `json:"user_prompt_template"`
		Model              string  `json:"model"`
		Temperature        float64 `json:"temperature"`
	} `json:"snapshot"`
	RevealImageMatch struct {
		SystemPrompt       string  `json:"system_prompt"`
		UserPromptTemplate string  `json:"user_prompt_template"`
		Model              string  `json:"model"`
		Temperature        float64 `json:"temperature"`
	} `json:"reveal_image_match"`
	RevealAIResponse struct {
		SystemPrompt       string  `json:"system_prompt"`
		UserPromptTemplate string  `json:"user_prompt_template"`
		Model              string  `json:"model"`
		Temperature        float64 `json:"temperature"`
	} `json:"reveal_ai_response"`
	RevealImageMatchV2 struct {
		SystemPrompt       string  `json:"system_prompt"`
		UserPromptTemplate string  `json:"user_prompt_template"`
		Model              string  `json:"model"`
		Temperature        float64 `json:"temperature"`
	} `json:"reveal_image_match_v2"`
	PeekMatch struct {
		SystemPrompt       string  `json:"system_prompt"`
		UserPromptTemplate string  `json:"user_prompt_template"`
		Model              string  `json:"model"`
		Temperature        float64 `json:"temperature"`
		HitWordsTemplate   string  `json:"hit_words_template"`
	} `json:"peek_match"`
	FizzJoin struct {
		QuestionSystemPrompt   string  `json:"question_system_prompt"`
		QuestionPromptTemplate string  `json:"question_prompt_template"`
		PhotoSystemPrompt      string  `json:"photo_system_prompt"`
		PhotoPromptTemplate    string  `json:"photo_prompt_template"`
		Model                  string  `json:"model"`
		Temperature            float64 `json:"temperature"`
	} `json:"fizz_join"`
	UnmuteAIResponse struct {
		SystemPrompt       string  `json:"system_prompt"`
		UserPromptTemplate string  `json:"user_prompt_template"`
		Model              string  `json:"model"`
		Temperature        float64 `json:"temperature"`
	} `json:"unmute_ai_response"`
	StirupInit struct {
		InitPrompt     string  `json:"init_prompt"`
		UserInputTheme string  `json:"user_input_theme"`
		Model          string  `json:"model"`
		Temperature    float64 `json:"temperature"`
	} `json:"stirup_init"`
	StirupPanel struct {
		PanelPrompt    string  `json:"panel_prompt"`
		UserInputTheme string  `json:"user_input_theme"`
		Model          string  `json:"model"`
		Temperature    float64 `json:"temperature"`
	} `json:"stirup_panel"`
	RoastedAIResponse struct {
		R1SystemPrompt       string  `json:"r1_system_prompt"`
		R2SystemPrompt       string  `json:"r2_system_prompt"`
		R1UserPromptTemplate string  `json:"r1_user_prompt_template"`
		R2UserPromptTemplate string  `json:"r2_user_prompt_template"`
		Model                string  `json:"model"`
		Temperature          float64 `json:"temperature"`
	} `json:"roasted_ai_response"`
	WassupVideoConclusion struct {
		SystemPrompt string  `json:"system_prompt"`
		Model        string  `json:"model"`
		Temperature  float64 `json:"temperature"`
	} `json:"wassup_video_conclusion"`
	CapsuleAIResponse struct {
		UserSimulatorSystemPrompt string  `json:"user_simulator_system_prompt"`
		SystemPrompt              string  `json:"system_prompt"`
		Model                     string  `json:"model"`
		Temperature               float64 `json:"temperature"`
	} `json:"capsule_ai_response"`
	ChatProxyAIResponse struct {
		SystemPrompt        string  `json:"system_prompt"`
		OpeningSystemPrompt string  `json:"opening_system_prompt"`
		Model               string  `json:"model"`
		Temperature         float64 `json:"temperature"`
	} `json:"chatproxy_ai_response"`
	StoryCoPilotAIResponse struct {
		SystemPrompt string  `json:"system_prompt"`
		Model        string  `json:"model"`
		Temperature  float64 `json:"temperature"`
	} `json:"story_co_pilot_ai_response"`
	HauntAIResponse struct {
		SystemPrompt     string  `json:"system_prompt"`
		HintSystemPrompt string  `json:"hint_system_prompt"`
		Model            string  `json:"model"`
		Temperature      float64 `json:"temperature"`
	} `json:"haunt_ai_response"`
	PinSceneParsing struct {
		SystemPrompt string  `json:"system_prompt"`
		Model        string  `json:"model"`
		Temperature  float64 `json:"temperature"`
	} `json:"pin_scene_parsing"`
}

func (s *LlmService) LoadSnapshotPrompt(ctx context.Context, key repos_app_settings.AppSettingsKey) (LlmConfig, error) {
	prompt, err := repos_app_settings.Get[LlmConfig](ctx, key, s.appSettings)
	if err != nil {
		return LlmConfig{}, err
	}
	return prompt, nil
}

func (s *LlmService) Complete(ctx context.Context, prompts []interface{}, model string, temperature float64, responseFormat ResponseFormat) (string, error) {
	resp, err := s.client.Complete(ctx, prompts, model, temperature, responseFormat)
	if err != nil {
		return "", err
	}
	return resp, nil
}

// OpenRouterComplete 暂时测试用，后面换成interface
// 可选参数 host 与 apiKey 作为覆盖，未提供时使用默认值
func (s *LlmService) OpenRouterComplete(ctx context.Context, prompts []openai.ChatCompletionMessage, model string, temperature float64, responseFormat NewResponseFormat, host *string, apiKey *string) (string, error) {
	// fallback defaults
	cfgHost := "https://openrouter.ai/api/v1"
	if host != nil && *host != "" {
		cfgHost = *host
	}
	cfgApiKey := s.conf.ThirdParty.OpenRouter.GetApiKey()
	if apiKey != nil && *apiKey != "" {
		cfgApiKey = *apiKey
	}

	config := adapter_driving_services_llm.ChatCompletionConfig{
		Host:        cfgHost,
		ApiKey:      cfgApiKey,
		Model:       model,
		Temperature: float32(temperature),
	}

	// 根据 responseFormat.Type 设置 ResponseFormat
	if responseFormat.JSONSchema != nil {
		config.ResponseFormat = &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONSchema,
			JSONSchema: &openai.ChatCompletionResponseFormatJSONSchema{
				Name:   responseFormat.JSONSchema.Name,
				Strict: true,
				Schema: responseFormat.JSONSchema.Schema,
			},
		}
	} else if responseFormat.Type == "json_object" {
		config.ResponseFormat = &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeJSONObject,
		}
	} else {
		config.ResponseFormat = &openai.ChatCompletionResponseFormat{
			Type: openai.ChatCompletionResponseFormatTypeText,
		}
	}

	resp, err := s.openRouter.ChatCompletion(ctx, config, prompts)
	if err != nil {
		return "", err
	}
	return resp, nil
}

func (s *LlmService) CompleteWithStream(ctx context.Context, prompts []interface{}, model string, temperature float64, responseFormat ResponseFormat) (<-chan string, error) {
	resp, err := s.client.CompleteWithStream(ctx, prompts, model, temperature, responseFormat)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *LlmService) RevealImageMatching(ctx context.Context, llmPrompt LlmConfig, imageKey domain_entities_resource.ImageResourcePath, condition domain_entities_items.ExchangeImageCondition) (api_items_story_types_v1.ExchangeImageMatchStatus, string, string, string, error) {
	revealPrompt := llmPrompt.RevealImageMatch

	// user prompt content = user image content + creator's condition content

	// conditionContent := strings.Replace(revealPrompt.UserPromptTemplate, "{positive_examples}", condition.PositiveText, -1)
	// conditionContent = strings.Replace(conditionContent, "{negative_examples}", condition.NegativeText, -1)
	conditionContent := ""
	if condition.LLMPrompt != "" {
		conditionContent = strings.Replace(revealPrompt.UserPromptTemplate, "{creator_criteria}", condition.LLMPrompt, -1)
	} else {
		conditionContent = strings.Replace(revealPrompt.UserPromptTemplate, "{creator_criteria}", condition.PositiveText, -1)
	}

	// Note(squids): add caption as another input
	conditionContent = strings.Replace(conditionContent, "{creator_caption}", condition.Tips, -1)

	cond := openai.ChatMessagePart{
		Type: openai.ChatMessagePartTypeText,
		Text: conditionContent,
	}

	userImageContent := openai.ChatMessagePart{
		Type: openai.ChatMessagePartTypeImageURL,
		ImageURL: &openai.ChatMessageImageURL{
			URL:    imageKey.UserCopilotMessage(),
			Detail: openai.ImageURLDetailAuto,
		},
	}

	userPromptContent := []openai.ChatMessagePart{
		cond,
		userImageContent,
	}

	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: revealPrompt.SystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}

	// TODO(squids): add ab switch to select version
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					// "intention_decoding": map[string]string{"type": "string"},
					// "reasoning":          map[string]string{"type": "string"},
					"match_level": map[string]string{"type": "integer"},
					"feedback":    map[string]string{"type": "string"},
				},
				// "required":             []string{"intention_decoding", "reasoning", "match_level"},
				"required":             []string{"match_level", "feedback"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, prompts, "EigenAI/testVLM", revealPrompt.Temperature, responseFormat,
		lo.ToPtr("http://147.185.41.242:30003/v1"),
		lo.ToPtr("sk-9d30b8653384e598b0fb10b097869c91"))
	if err != nil {
		return api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, "", "", "", err
	}
	var parsed struct {
		// IntentionDecoding string `json:"intention_decoding"`
		// Reasoning         string `json:"reasoning"`
		MatchLevel int32  `json:"match_level"`
		FeedBack   string `json:"feedback"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, "", "", "", err
	}
	// return api_items_story_types_v1.ExchangeImageMatchStatus(parsed.MatchLevel), parsed.IntentionDecoding, parsed.Reasoning, nil
	return api_items_story_types_v1.ExchangeImageMatchStatus(parsed.MatchLevel), "", "", parsed.FeedBack, nil
}

func (s *LlmService) RevealAIResponse(ctx context.Context, llmPrompt LlmConfig, matchStatus api_items_story_types_v1.ExchangeImageMatchStatus, intentionDecoding string, reasoning string) (string, error) {
	revealPrompt := llmPrompt.RevealAIResponse

	userContent := strings.Replace(revealPrompt.UserPromptTemplate, "{intention_decoding}", intentionDecoding, -1)
	userContent = strings.Replace(userContent, "{reasoning}", reasoning, -1)

	// convert match status to string, but not use matchStatus.String(). use int value instead
	matchStatusString := fmt.Sprintf("%d", int(matchStatus))
	userContent = strings.Replace(userContent, "{match_level}", matchStatusString, -1)

	userPromptContent := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: userContent,
		},
	}
	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: revealPrompt.SystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}

	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"reply": map[string]string{"type": "string"},
				},
				"required":             []string{"reply"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, prompts, revealPrompt.Model, revealPrompt.Temperature, responseFormat, nil, nil)
	if err != nil {
		return "", err
	}
	var parsed struct {
		Reply string `json:"reply"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return "", err
	}
	return parsed.Reply, nil
}

func (s *LlmService) ImageExchange(ctx context.Context, imageKey domain_entities_resource.ImageResourcePath, condition domain_entities_items.ExchangeImageCondition) (api_items_story_types_v1.ExchangeImageMatchStatus, string, error) {
	// TODO(squids): add ab switch to select version
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.RevealLLMV2Config)
	// llmPrompt, err = s.LoadSnapshotPrompt(ctx, repos_app_settings.RevealLLMConfig)

	if err != nil {
		return api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, "", err
	}
	matchStatus, _, _, aiResponse, err := s.RevealImageMatching(ctx, llmPrompt, imageKey, condition)
	if err != nil {
		return api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, "", err
	}
	if matchStatus == api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_MATCHED {
		return matchStatus, aiResponse, nil
	}
	// if no match, use intention decoding as ai response
	// aiResponse, err := s.RevealAIResponse(ctx, llmPrompt, matchStatus, intentionDecoding, reasoning)
	if err != nil {
		return api_items_story_types_v1.ExchangeImageMatchStatus_EXCHANGE_IMAGE_MATCH_STATUS_UNSPECIFIED, "", err
	}
	return matchStatus, aiResponse, nil
}

func (s *LlmService) TurtleSoupIntentMatching(ctx context.Context, llmPrompt LlmConfig, userMessage string, intentPrompt string, caption string, customAIResponse []*domain_entities_items.StoryPlayTurtleSoupCustomAiResponse, tryCount uint32) (
	matchStatus api_items_story_types_v1.TurtleSoupMatchStatus,
	aiResponse string,
	err error,
) {
	turtleSoupPrompt := llmPrompt.PeekMatch
	// user prompt content = user image content + creator's condition content

	// ### User Input \\n- Creator-defined criteria: {intent_prompt}\\n\\n
	// - Creator caption : {creator_caption}\\n\\n
	// - User''s submission: {user_response}\\n\\n
	// - Try count: {try_count}

	customAIResponseContent, _ := json.Marshal(customAIResponse)
	conditionContent := strings.Replace(turtleSoupPrompt.UserPromptTemplate, "{example_response}", string(customAIResponseContent), -1)
	conditionContent = strings.Replace(conditionContent, "{intent_prompt}", intentPrompt, -1)
	conditionContent = strings.Replace(conditionContent, "{creator_caption}", caption, -1)
	conditionContent = strings.Replace(conditionContent, "{user_response}", userMessage, -1)
	conditionContent = strings.Replace(conditionContent, "{try_count}", fmt.Sprintf("%d", tryCount), -1)

	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: turtleSoupPrompt.SystemPrompt,
		},
		{
			Role: "user",
			MultiContent: []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: conditionContent,
				},
			},
		},
	}

	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"match_level": map[string]string{"type": "integer"},
					"reply":       map[string]string{"type": "string"},
				},
				"required":             []string{"match_level", "reply"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, prompts, "Qwen/Qwen3-32B", turtleSoupPrompt.Temperature, responseFormat,
		lo.ToPtr("http://147.185.41.243:30002/v1"),
		lo.ToPtr("sk-2ef48774de90db2f8611ac28fc0fa627"))
	if err != nil {
		return api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, "", err
	}
	var parsed struct {
		MatchLevel int32  `json:"match_level"`
		Reply      string `json:"reply"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, "", err
	}
	return api_items_story_types_v1.TurtleSoupMatchStatus(parsed.MatchLevel), parsed.Reply, nil

}

func (s *LlmService) TurtleSoupHitWords(ctx context.Context, llmPrompt LlmConfig, userMessage string, intentPrompt string) (hitWords []string, err error) {
	hitWordsPrompt := llmPrompt.PeekMatch.HitWordsTemplate
	hitWordsPrompt = strings.Replace(hitWordsPrompt, "{intent_prompt}", intentPrompt, -1)
	hitWordsPrompt = strings.Replace(hitWordsPrompt, "{user_response}", userMessage, -1)
	userPromptContent := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: hitWordsPrompt,
		},
	}
	prompt := []openai.ChatCompletionMessage{
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}
	response, err := s.OpenRouterComplete(ctx, prompt, llmPrompt.PeekMatch.Model, llmPrompt.PeekMatch.Temperature, NewResponseFormat{}, nil, nil)
	if err != nil {
		return nil, err
	}
	return strings.Split(response, " "), nil
}

func (s *LlmService) TurtleSoupConsume(ctx context.Context, userID int64, storyID int64, userMessage string, intentPrompt string, caption string, customAIResponse []*domain_entities_items.StoryPlayTurtleSoupCustomAiResponse, tryCount uint32) (
	aiResponse string,
	hitWords []string,
	matchStatus api_items_story_types_v1.TurtleSoupMatchStatus,
	err error,
) {
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.PeekLLMConfig)
	if err != nil {
		return "", nil, api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, err
	}

	// 1. 从数据库读取历史对话
	historyMessages, err := s.getConversationHistory(ctx, userID, storyID)
	if err != nil {
		return "", nil, api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, err
	}

	request := &TypeMultiturnLLMRequest{
		UserMessage:  userMessage,
		IntentPrompt: intentPrompt,
		Caption:      caption,
		TryCount:     tryCount,
	}

	// 2. 调用 LLM 获取响应
	matchStatus, aiResponse, newHistoryMessages, err := s.TypeMultiturnLLMConversation(ctx, llmPrompt, request, historyMessages)
	if err != nil {
		return "", nil, api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, err
	}

	// 3. 将更新的对话历史存回数据库
	if err := s.saveConversationHistory(ctx, userID, storyID, newHistoryMessages); err != nil {
		s.logger.Errorf("Failed to save conversation history: %v", err)
		// 不影响主流程，继续返回结果
	}

	// if matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED {
	// 	hitWords, err = s.TurtleSoupHitWords(ctx, llmPrompt, userMessage, intentPrompt)
	// 	if err != nil {
	// 		return "", nil, api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, err
	// 	}
	// }
	return aiResponse, hitWords, matchStatus, nil
}

func (s *LlmService) RealTurtleSoupConsume(ctx context.Context, llmPrompt LlmConfig, userMessage string, intentPrompt string, caption string, tryCount uint32) (
	aiResponse string,
	hitWords []string,
	matchStatus api_items_story_types_v1.TurtleSoupMatchStatus,
	err error,
) {
	matchStatus, aiResponse, err = s.TurtleSoupIntentMatching(ctx, llmPrompt, userMessage, intentPrompt, caption, []*domain_entities_items.StoryPlayTurtleSoupCustomAiResponse{}, tryCount)
	if err != nil {
		return "", nil, api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, err
	}

	// if matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_PARTIALLY_MATCHED {
	// 	hitWords, err = s.TurtleSoupHitWords(ctx, llmPrompt, userMessage, intentPrompt)
	// 	if err != nil {
	// 		return "", nil, api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, err
	// 	}
	// }
	return aiResponse, hitWords, matchStatus, nil
}

// Generate implements domain_services_fizz.ICaptionGenerator.
func (s *LlmService) Generate(ctx context.Context, resource *domain_entities_fizz.Resource, description string) (string, error) {
	// TODO @Ricky: 需要实现
	return description, nil
}

// GenerateShooting implement the copilot for stir up
func (s *LlmService) GenerateShooting(ctx context.Context, userId int64, theme string, style string, preResults []*api_fizz_stirup_v1.GenerateShootingRequest_ResourceSamplingResult, summaries []string, hints []string) (api_fizz_resource_types_v1.ShootingModel, string, string, error) {
	inputConfig := &StirupPanelInput{
		UserInputTheme: theme,
		UserInputStyle: style, // 需要添加的字段
	}

	if len(preResults) == 0 {
		shootingMode, _, caption, err := s.CheckStirupInit(ctx, theme, style)
		if err != nil {
			return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
		}
		return shootingMode, "", caption, nil
	}
	f := func(preResult *api_fizz_stirup_v1.GenerateShootingRequest_ResourceSamplingResult, summary string) StageInputPanel {
		s := StageInputPanel{
			AsrResult: preResult.UserVoiceAsr,
		}
		// 这里如果上一个Panel的purpose不为空("")，则作为当前阶段的ImageDescription，把Frames留空即可，加一个条件判断
		if len(summary) > 0 {
			s.ImageDescription = summary
		} else {
			s.Frames = lo.Map(preResult.ScreenshotFrames, func(frames string, _ int) domain_entities_resource.ImageResourcePath {
				return domain_entities_resource.ImageResourcePath(frames)
			})
		}
		return s
	}
	if len(preResults) > 0 {
		purpose := ""
		if len(summaries) > 0 {
			purpose = summaries[0]
		}
		if len(hints) > 0 && len(hints[0]) > 0 {
			err := json.Unmarshal([]byte(hints[0]), &inputConfig.HintStage0)
			if err != nil {
				return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", nil
			}
		}
		inputConfig.UserInputPanel1 = f(preResults[0], purpose)
	}
	if len(preResults) > 1 {
		purpose := ""
		if len(summaries) > 1 {
			purpose = summaries[1]
		}
		if len(hints) > 1 && len(hints[1]) > 0 {
			err := json.Unmarshal([]byte(hints[1]), &inputConfig.HintStage1)
			if err != nil {
				return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", nil
			}
		}
		// 理论上这里 purpose 应该为空
		inputConfig.UserInputPanel2 = f(preResults[1], purpose)
	}
	// 理论不应该会有第三个阶段，这里可以放一个panic
	if len(preResults) > 2 {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", errors.New("should not have more than 3 panels")
	}
	shootingMode, purpose, caption, err := s.CheckStirupPanel(ctx, inputConfig)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	return shootingMode, purpose, caption, nil
}

// CheckAnswer implements domain_services_fizz.IJoinConditionChecker.
func (s *LlmService) CheckAnswer(ctx context.Context, fizzQuestion *domain_entities_fizz.FizzJoinConditionQuestion, userInput string) (bool, error) {
	llmPromptConfig, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.FizzLLMConfig)
	if err != nil {
		return false, err
	}
	return s.RealCheckAnswer(ctx, llmPromptConfig, fizzQuestion, userInput)
}

func (s *LlmService) RealCheckAnswer(ctx context.Context, llmConfig LlmConfig, fizzQuestion *domain_entities_fizz.FizzJoinConditionQuestion, userInput string) (bool, error) {
	userPromptContent := strings.Replace(llmConfig.FizzJoin.QuestionPromptTemplate, "{fizz_question}", fizzQuestion.Question, -1)
	userPromptContent = strings.Replace(userPromptContent, "{user_input}", userInput, -1)
	if len(fizzQuestion.AnswerPrompt) != 0 {
		userPromptContent = fizzQuestion.AnswerPrompt + userPromptContent
	}

	fizzPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.FizzJoin.QuestionSystemPrompt,
		},
		{
			Role: "user",
			MultiContent: []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: userPromptContent,
				},
			},
		},
	}
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"answer": map[string]string{"type": "boolean"},
				},
				"required":             []string{"answer"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, fizzPrompt, llmConfig.FizzJoin.Model, llmConfig.FizzJoin.Temperature, responseFormat, nil, nil)
	if err != nil {
		return false, err
	}
	var parsed struct {
		Answer bool `json:"answer"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return false, err
	}
	return parsed.Answer, nil
}

// CheckUnmute implements domain_services_fizz.IJoinConditionChecker.
func (s *LlmService) CheckUnmute(ctx context.Context, creatorIntention string, creatorPrompt string, consumerAudioObjectKey string, tryCount uint32) (bool, string, string, string, error) {
	// 1. Load LLM Prompt
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.PeekLLMConfig)
	if err != nil {
		return false, "", "", "", err
	}
	return s.RealCheckUnmute(ctx, llmPrompt, creatorIntention, creatorPrompt, consumerAudioObjectKey, tryCount)
}

// OUT: parsed, suggestion, improvedResponse, transcribeResult
func (s *LlmService) RealCheckUnmute(ctx context.Context, llmConfig LlmConfig, creatorIntention string, creatorPrompt string, consumerAudioObjectKey string, tryCount uint32) (bool, string, string, string, error) {
	// 0. basic check
	if s.asrClient == nil {
		return false, "", "", "", errors.New("asr client is not initialized, please check the config")
	}

	// 1. Transcribe Audio
	asrService := NewAsrService(s.asrClient, s.conf)
	// similarity *float32, expectedTxt *string
	similarity := float32(0.8) // just a dummy value
	expectedTxt := "I'm good"  // just a dummy value
	transcribeResult, _, err := asrService.Transcribe(ctx, consumerAudioObjectKey, &similarity, &expectedTxt)
	if err != nil {
		return false, "", "", "", err
	}

	// [New] 2. call TurtleSoupIntentMatching
	matchStatus, aiResponse, err := s.TurtleSoupIntentMatching(
		ctx, llmConfig, transcribeResult, creatorIntention, creatorPrompt,
		[]*domain_entities_items.StoryPlayTurtleSoupCustomAiResponse{}, tryCount)
	if err != nil {
		return false, "", "", "", err
	}
	return matchStatus == api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_MATCHED,
		aiResponse, "", transcribeResult, nil

	// // 2. Prepare Prompt
	// userPromptContent := strings.Replace(llmConfig.UnmuteAIResponse.UserPromptTemplate, "{unmute_question}", creatorIntention, -1)
	// userPromptContent = strings.Replace(userPromptContent, "{user_input}", transcribeResult, -1)
	// userPromptContent = strings.Replace(userPromptContent, "{try_count}", fmt.Sprintf("%d", tryCount), -1)

	// // 3. Call LLM
	// responseFormat := NewResponseFormat{
	// 	Type: "json_schema",
	// 	JSONSchema: &struct {
	// 		Name   string                `json:"name"`
	// 		Schema *ResponseFormatSchema `json:"schema"`
	// 	}{
	// 		Name: "reply",
	// 		Schema: &ResponseFormatSchema{
	// 			"type": "object",
	// 			"properties": map[string]interface{}{
	// 				"answer": map[string]string{"type": "boolean"},
	// 				"score": map[string]interface{}{
	// 					"type":    "integer",
	// 					"minimum": 0,
	// 					"maximum": 100,
	// 				},
	// 				"improvement_suggestion": map[string]interface{}{"type": "string"},
	// 			},
	// 			"required": []string{"answer", "score", "improvement_suggestion"},
	// 		},
	// 	},
	// }
	// response, err := s.OpenRouterComplete(ctx, []openai.ChatCompletionMessage{
	// 	{
	// 		Role:    "system",
	// 		Content: llmConfig.UnmuteAIResponse.SystemPrompt,
	// 	},
	// 	{
	// 		Role: "user",
	// 		MultiContent: []openai.ChatMessagePart{
	// 			{
	// 				Type: openai.ChatMessagePartTypeText,
	// 				Text: userPromptContent,
	// 			},
	// 		},
	// 	},
	// }, llmConfig.UnmuteAIResponse.Model, llmConfig.UnmuteAIResponse.Temperature, responseFormat, nil, nil)
	// if err != nil {
	// 	return false, "", "", "", err
	// }

	// var parsed struct {
	// 	Answer                bool   `json:"answer"`
	// 	Score                 int32  `json:"score"`
	// 	ImprovementSuggestion string `json:"improvement_suggestion"`
	// }
	// if err := json.Unmarshal([]byte(response), &parsed); err != nil {
	// 	return false, "", "", "", err
	// }

	// return parsed.Answer, parsed.ImprovementSuggestion, "", transcribeResult, nil
}

// CheckPhoto implements domain_services_fizz.IJoinConditionChecker.
func (s *LlmService) CheckPhoto(ctx context.Context, description string, examplePhotoKey domain_entities_resource.ImageResourcePath, userInputPhotoKey domain_entities_resource.ImageResourcePath) (bool, error) {
	llmPromptConfig, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.FizzLLMConfig)
	if err != nil {
		return false, err
	}
	return s.RealCheckPhoto(ctx, llmPromptConfig, description, examplePhotoKey, userInputPhotoKey)
}

func (s *LlmService) RealCheckPhoto(ctx context.Context, llmConfig LlmConfig, description string, examplePhotoKey domain_entities_resource.ImageResourcePath, userInputPhotoKey domain_entities_resource.ImageResourcePath) (bool, error) {
	userPromptContent := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: strings.Replace(llmConfig.FizzJoin.PhotoPromptTemplate, "{image_caption}", description, -1),
		},
		{
			Type: openai.ChatMessagePartTypeText,
			Text: "The user input image:",
		},
		{
			Type: openai.ChatMessagePartTypeImageURL,
			ImageURL: &openai.ChatMessageImageURL{
				URL:    userInputPhotoKey.UserCopilotMessage(),
				Detail: openai.ImageURLDetailAuto,
			},
		},
	}
	fizzPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.FizzJoin.PhotoSystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"answer":     map[string]string{"type": "boolean"},
					"score":      map[string]string{"type": "integer"},
					"reason":     map[string]string{"type": "string"},
					"suggestion": map[string]string{"type": "string"},
				},
				"required":             []string{"answer", "score", "reason", "suggestion"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, fizzPrompt, llmConfig.FizzJoin.Model, llmConfig.FizzJoin.Temperature, responseFormat, nil, nil)
	if err != nil {
		return false, err
	}
	var parsed struct {
		Answer bool `json:"answer"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return false, err
	}
	return parsed.Answer, nil
}

func (s *LlmService) genSystemPrompt(prompt string) map[string]string {
	return map[string]string{
		"role":    "system",
		"content": prompt,
	}
}

func (s *LlmService) UnmuteConsume(ctx context.Context, intention string, prompt string, audioKey string, customAIResponses []*domain_entities_items.StoryPlayUnmuteCustomAiResponse, tryCount uint32) (api_items_story_types_v1.UnmuteMatchStatus, string, error) {
	parsed, improvingSuggestion, _, _, err := s.CheckUnmute(ctx, intention, prompt, audioKey, tryCount)
	if err != nil {
		return api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_UNSPECIFIED, "", err
	}
	if parsed {
		return api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_MATCHED, improvingSuggestion, nil
	}
	return api_items_story_types_v1.UnmuteMatchStatus_UNMUTE_MATCH_STATUS_NOT_MATCHED, improvingSuggestion, nil
}

// A func to convert shooting mode (str) to api_fizz_resource_types_v1.ShootingModel
func convertShootingMode(shootingMode string) (api_fizz_resource_types_v1.ShootingModel, error) {
	ShootingModel_LLM_value := map[string]int32{
		"Error":        0,
		"Dual":         1,
		"Front":        2,
		"Back":         3,
		"Pano":         4,
		"Timelapse":    5,
		"SlowMo":       6,
		"Cinematic":    7,
		"WalkShot":     8,
		"TalkToCamera": 9,
		"CaptionOnly":  10,
	}

	if val, ok := ShootingModel_LLM_value[shootingMode]; ok {
		return api_fizz_resource_types_v1.ShootingModel(val), nil
	}
	return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, nil
}

// CheckStirupInit implements Stirup Init
func (s *LlmService) CheckStirupInit(ctx context.Context, inputTheme string, inputStyle string) (api_fizz_resource_types_v1.ShootingModel, string, string, error) {
	// 1. Load LLM Prompt
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.StirupLLMConfig)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	return s.RealCheckStirupInit(ctx, llmPrompt, inputTheme, inputStyle)
}

func (s *LlmService) RealCheckStirupInit(ctx context.Context, llmConfig LlmConfig, inputTheme string, inputStyle string) (api_fizz_resource_types_v1.ShootingModel, string, string, error) {
	userInputInfo := strings.Replace(llmConfig.StirupInit.UserInputTheme, "{user_input}", inputTheme, -1)
	userInputInfo = strings.Replace(userInputInfo, "{user_style}", inputStyle, -1)

	stirupInitPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.StirupInit.InitPrompt,
		},
		{
			Role: "user",
			MultiContent: []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: userInputInfo,
				},
			},
		},
	}
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"story_purpose": map[string]string{"type": "string"},
					"shooting_mode": map[string]string{"type": "string"},
					"caption":       map[string]string{"type": "string"},
				},
				"required":             []string{"story_purpose", "shooting_mode", "caption"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, stirupInitPrompt, llmConfig.StirupInit.Model, llmConfig.StirupInit.Temperature, responseFormat, nil, nil)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	var parsed struct {
		StoryPurpose string `json:"story_purpose"`
		ShootingMode string `json:"shooting_mode"`
		Caption      string `json:"caption"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	shooting_mode, err := convertShootingMode(parsed.ShootingMode)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	return shooting_mode, parsed.StoryPurpose, parsed.Caption, nil
}

// Stirup panel stages
func (s *LlmService) CheckStirupPanel(ctx context.Context, input_cfg *StirupPanelInput) (api_fizz_resource_types_v1.ShootingModel, string, string, error) {
	// 1. Load LLM Prompt
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.StirupLLMConfig)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	return s.RealCheckStirupPanel(ctx, llmPrompt, input_cfg)
}

func (s *LlmService) RealCheckStirupPanel(ctx context.Context, llmConfig LlmConfig, input_cfg *StirupPanelInput) (api_fizz_resource_types_v1.ShootingModel, string, string, error) {
	userInputInfo := strings.Replace(llmConfig.StirupPanel.UserInputTheme, "{user_theme}", input_cfg.UserInputTheme, -1)
	userInputInfo = strings.Replace(userInputInfo, "{user_style}", input_cfg.UserInputStyle, -1)

	userPromptContent := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: userInputInfo,
		},
	}

	appendPanel := func(prefix string, p StageInputPanel) {
		userPromptContent = append(userPromptContent,
			openai.ChatMessagePart{
				Type: openai.ChatMessagePartTypeText,
				Text: fmt.Sprintf("%s Panel Frames:", prefix),
			},
		)

		if len(p.Frames) == 0 {
			userPromptContent = append(userPromptContent,
				openai.ChatMessagePart{
					Type: openai.ChatMessagePartTypeText,
					Text: fmt.Sprintf("%s Panel Summary: ", prefix) + p.ImageDescription,
				},
			)
		} else {
			for _, frame := range p.Frames {
				userPromptContent = append(userPromptContent,
					openai.ChatMessagePart{
						Type: openai.ChatMessagePartTypeImageURL,
						ImageURL: &openai.ChatMessageImageURL{
							URL:    frame.UserCopilotMessage(),
							Detail: openai.ImageURLDetailAuto,
						},
					},
				)
			}
		}

		userPromptContent = append(userPromptContent,
			openai.ChatMessagePart{
				Type: openai.ChatMessagePartTypeText,
				Text: fmt.Sprintf("%s Panel ASR Result: ", prefix) + p.AsrResult,
			},
		)
	}

	// helper: append one hint: story purpose / shooting mode / caption
	appendHint := func(prefix string, h StirupHint) {
		userPromptContent = append(userPromptContent,
			// map[string]string{"type": "text", "text": fmt.Sprintf(
			// 	"%s Hint - StoryPurpose: %s", prefix, h.StoryPurpose,
			// )},
			openai.ChatMessagePart{
				Type: openai.ChatMessagePartTypeText,
				Text: fmt.Sprintf("%s Hint - ShootingMode: %s", prefix, h.ShootingMode),
			},
			openai.ChatMessagePart{
				Type: openai.ChatMessagePartTypeText,
				Text: fmt.Sprintf("%s Hint - Caption: %s", prefix, h.Caption),
			},
		)
	}

	// 2. concatenate all hints and panels
	// 1) Stage0
	appendHint("Stage0", input_cfg.HintStage0)

	// 2) put all stages together
	stages := []struct {
		prefix string
		panel  StageInputPanel
		hint   StirupHint
	}{
		{"Stage1", input_cfg.UserInputPanel1, input_cfg.HintStage1},
		{"Stage2", input_cfg.UserInputPanel2, input_cfg.HintStage2},
	}

	for _, st := range stages {
		appendPanel(st.prefix, st.panel)
		if st.hint.Caption == "" {
			break
		}
		appendHint(st.prefix, st.hint)
	}

	stirupPanelPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.StirupPanel.PanelPrompt,
		},
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}

	rf := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"story_purpose": map[string]string{"type": "string"},
					"shooting_mode": map[string]string{"type": "string"},
					"caption":       map[string]string{"type": "string"},
					"stage":         map[string]string{"type": "integer"},
				},
				"required":             []string{"story_purpose", "shooting_mode", "caption", "stage"},
				"additionalProperties": false,
			},
		},
	}

	// 4) LLM
	raw, err := s.OpenRouterComplete(ctx, stirupPanelPrompt, llmConfig.StirupPanel.Model, llmConfig.StirupPanel.Temperature, rf, nil, nil)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}

	// 5) story_purpose / shooting_mode / caption / stage」
	var chk struct {
		StoryPurpose string `json:"story_purpose"`
		ShootingMode string `json:"shooting_mode"`
		Caption      string `json:"caption"`
		Stage        int    `json:"stage"`
	}
	if err := json.Unmarshal([]byte(raw), &chk); err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", fmt.Errorf("invalid JSON from LLM: %w", err)
	}
	shootingMode, err := convertShootingMode(chk.ShootingMode)
	if err != nil {
		return api_fizz_resource_types_v1.ShootingModel_SHOOTING_MODEL_UNSPECIFIED, "", "", err
	}
	return shootingMode, chk.StoryPurpose, chk.Caption, nil
}

func (s *LlmService) WassupVideoConsume(ctx context.Context, input_video *WassupVideo) (string, error) {
	// 1. Load LLM Prompt
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.WassupVideoConfig)
	if err != nil {
		return "", err
	}
	return s.RealCheckWassupVideoConclusion(ctx, llmPrompt, input_video)
}

func (s *LlmService) RealCheckWassupVideoConclusion(ctx context.Context, llmConfig LlmConfig, input_video *WassupVideo) (string, error) {
	userPromptContent := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: "Video Frames: ",
		},
	}

	for _, frame := range input_video.Frames {
		userPromptContent = append(userPromptContent,
			openai.ChatMessagePart{
				Type: openai.ChatMessagePartTypeImageURL,
				ImageURL: &openai.ChatMessageImageURL{
					URL:    frame.UserCopilotMessage(),
					Detail: openai.ImageURLDetailAuto,
				},
			},
		)
	}
	userPromptContent = append(userPromptContent,
		openai.ChatMessagePart{
			Type: openai.ChatMessagePartTypeText,
			Text: "Video ASR Result: " + input_video.AsrResult,
		},
	)

	wassupVideoPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.WassupVideoConclusion.SystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: userPromptContent,
		},
	}
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "caption",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"caption": map[string]string{"type": "string"},
				},
				"required":             []string{"caption"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, wassupVideoPrompt, llmConfig.WassupVideoConclusion.Model, llmConfig.WassupVideoConclusion.Temperature, responseFormat, nil, nil)
	if err != nil {
		return "", err
	}
	var parsed struct {
		Caption string `json:"caption"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return "", err
	}
	return parsed.Caption, nil
}

// GenerateCapsuleConsume 返回
// - 引导 guidance，
// - 追问是否结束 end，
// - 聊天历史 chatHistory
func (s *LlmService) GenerateCapsuleConsume(ctx context.Context, userInputPhotoKey domain_entities_resource.ImageResourcePath, extraInfo string, chatHistory []CapsuleChatRound, userReply string) (string, bool, []CapsuleChatRound, error) {
	// 1. Load LLM Prompt
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.CapsuleConfig)
	if err != nil {
		return "", false, nil, err
	}
	return s.RealGenerateCapsule(ctx, llmPrompt, userInputPhotoKey, extraInfo, chatHistory, userReply)
}

func (s *LlmService) RealGenerateCapsule(
	ctx context.Context,
	llmConfig LlmConfig,
	userInputPhotoKey domain_entities_resource.ImageResourcePath,
	extraInfo string,
	chatHistory []CapsuleChatRound, // past chat history, 最开始初始化为空，然后保存，每轮重新作为输入
	userReply string,
) (string, bool, []CapsuleChatRound, error) {
	// --- Params ---
	agentSystemPrompt := llmConfig.CapsuleAIResponse.SystemPrompt
	model := llmConfig.CapsuleAIResponse.Model
	temperature := llmConfig.CapsuleAIResponse.Temperature

	// --- Agent Response Format ---
	rfAgent := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "agent_reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"reasoning": map[string]string{"type": "string"},
					"guidance":  map[string]string{"type": "string"},
					"end":       map[string]string{"type": "boolean"},
				},
				"required":             []string{"reasoning", "guidance", "end"},
				"additionalProperties": false,
			},
		},
	}
	// --- Build Input Blocks ---
	imageInput := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: "The user input image:",
		},
		{
			Type: openai.ChatMessagePartTypeImageURL,
			ImageURL: &openai.ChatMessageImageURL{
				URL:    userInputPhotoKey.UserCopilotMessage(),
				Detail: openai.ImageURLDetailAuto,
			},
		},
	}
	if extraInfo != "" {
		imageInput = append(imageInput, openai.ChatMessagePart{
			Type: openai.ChatMessagePartTypeText,
			Text: "Extra information of the image: " + extraInfo,
		})
	}

	// --- Append user reply to last round ---
	if userReply != "" && len(chatHistory) > 0 {
		chatHistory[len(chatHistory)-1].Reply = userReply
	}

	// --- Build agent prompt ---
	agentPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: agentSystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: buildContentBlocks(imageInput, chatHistory),
		},
	}

	// --- Query LLM ---
	response, err := s.OpenRouterComplete(ctx, agentPrompt, model, temperature, rfAgent, nil, nil)
	if err != nil {
		return "", false, chatHistory, err
	}

	// --- Parse agent response ---
	var out struct {
		Guidance string `json:"guidance"`
		End      bool   `json:"end"`
	}
	if err := json.Unmarshal([]byte(response), &out); err != nil {
		return "", false, chatHistory, fmt.Errorf("invalid agent JSON: %w, got=%v", err, response)
	}

	// --- Append new round with guidance ---
	newRound := CapsuleChatRound{Guidance: out.Guidance}
	chatHistory = append(chatHistory, newRound)

	return out.Guidance, out.End, chatHistory, nil
}

// GenerateCapsule 仅供测试使用
func (s *LlmService) GenerateCapsule(ctx context.Context, llmConfig LlmConfig, userInputPhotoKey domain_entities_resource.ImageResourcePath, extraInfo string) (string, error) {
	// 1. Prepare chat rounds
	var chat CapsuleChat
	var chatHistory []CapsuleChatRound

	userSimulatorSystemPrompt := llmConfig.CapsuleAIResponse.UserSimulatorSystemPrompt
	agentSystemPrompt := llmConfig.CapsuleAIResponse.SystemPrompt
	model := llmConfig.CapsuleAIResponse.Model
	temperature := llmConfig.CapsuleAIResponse.Temperature

	rfAgent := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "agent_reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"reasoning": map[string]string{"type": "string"},
					"guidance":  map[string]string{"type": "string"},
					"end":       map[string]string{"type": "boolean"},
				},
			},
		},
	}

	rfUser := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "user_reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"reasoning": map[string]string{"type": "string"},
					"Reply":     map[string]string{"type": "string"},
				},
			},
		},
	}
	imageInput := []openai.ChatMessagePart{
		{
			Type: openai.ChatMessagePartTypeText,
			Text: "The user input image:",
		},
		{
			Type: openai.ChatMessagePartTypeImageURL,
			ImageURL: &openai.ChatMessageImageURL{
				URL:    userInputPhotoKey.UserCopilotMessage(),
				Detail: openai.ImageURLDetailAuto,
			},
		},
	}

	imageInput = append(imageInput, openai.ChatMessagePart{
		Type: openai.ChatMessagePartTypeText,
		Text: "Extra information of the image: " + extraInfo,
	})

	// Step 1: agent issues initial guidance/question
	agentPrompt := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: agentSystemPrompt,
		},
		{
			Role:         "user",
			MultiContent: imageInput,
		},
	}
	// chatHistory is initially empty
	// loop until agent outputs end=true
	for step := 0; step < 10; step++ {
		// 1. agent generates next guidance
		if len(chatHistory) > 0 {
			// supply chat history
			agentPrompt = []openai.ChatCompletionMessage{
				{
					Role:    "system",
					Content: agentSystemPrompt,
				},
				{
					Role:         "user",
					MultiContent: buildContentBlocks(imageInput, chatHistory),
				},
			}
		}
		response, err := s.OpenRouterComplete(ctx, agentPrompt, model, temperature, rfAgent, nil, nil)
		if err != nil {
			return "", err
		}
		// Parse agent response JSON
		var out struct {
			Reasoning string `json:"reasoning"`
			Guidance  string `json:"guidance"`
			End       bool   `json:"end"`
		}
		if err := json.Unmarshal([]byte(response), &out); err != nil {
			return "", fmt.Errorf("Agent response not valid JSON: %w, got=%v", err, response)
		}
		// add agent round
		thisRound := CapsuleChatRound{Guidance: out.Guidance}
		chatHistory = append(chatHistory, thisRound)

		// 2. user simulates reply
		userPrompt := []openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: userSimulatorSystemPrompt,
			},
			{
				Role:         "user",
				MultiContent: buildContentBlocks(imageInput, chatHistory),
			},
		}
		userResponse, err := s.OpenRouterComplete(ctx, userPrompt, model, temperature, rfUser, nil, nil)
		if err != nil {
			return "", err
		}
		var userOut struct {
			Reasoning string `json:"reasoning"`
			Reply     string `json:"reply"`
		}
		if err := json.Unmarshal([]byte(userResponse), &userOut); err != nil {
			return "", fmt.Errorf("User response not valid JSON: %w, got=%v", err, userResponse)
		}
		// append reply to the previous round
		chatHistory[len(chatHistory)-1].Reply = userOut.Reply

		// break if agent ends
		if out.End {
			break
		}
	}
	chat.ChatRounds = chatHistory
	b, _ := json.MarshalIndent(chat, "", "  ")
	return string(b), nil
}

// Helper to convert history to prompt content (implement as you need)
func ChatHistoryContent(rounds []CapsuleChatRound) interface{} {
	history := make([]map[string]string, 0, len(rounds))
	for _, r := range rounds {
		m := map[string]string{"guidance": r.Guidance}
		if r.Reply != "" {
			m["reply"] = r.Reply
		}
		history = append(history, m)
	}
	return map[string]interface{}{"chat_history": history}
}

func buildContentBlocks(imageInput []openai.ChatMessagePart, chatHistory []CapsuleChatRound) []openai.ChatMessagePart {
	blocks := make([]openai.ChatMessagePart, 0, len(imageInput)+1)
	blocks = append(blocks, imageInput...)
	// 历史转为单独一段 text
	var history strings.Builder
	for idx, r := range chatHistory {
		history.WriteString(fmt.Sprintf("Guidance %d: %s. ", idx+1, r.Guidance))
		if r.Reply != "" {
			history.WriteString(fmt.Sprintf("User Reply %d: %s. ", idx+1, r.Reply))
		}
	}
	if history.Len() > 0 {
		blocks = append(blocks, openai.ChatMessagePart{
			Type: openai.ChatMessagePartTypeText,
			Text: "Chat history:\n" + history.String(),
		})
	}

	return blocks
}

// 从数据库读取历史对话
func (s *LlmService) getConversationHistory(ctx context.Context, userID int64, storyID int64) ([]openai.ChatCompletionMessage, error) {
	query := dal.Use(s.data.MasterDB(ctx)).TypeConversation

	conversation, err := query.WithContext(ctx).Where(
		query.UserID.Eq(userID),
		query.StoryID.Eq(storyID),
	).First()

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 没有历史记录，返回空切片
			return []openai.ChatCompletionMessage{}, nil
		}
		return nil, fmt.Errorf("userID: %d, storyID: %d, err: %w", userID, storyID, err)
	}

	var messages []openai.ChatCompletionMessage
	if err := json.Unmarshal([]byte(conversation.Conversation), &messages); err != nil {
		return nil, fmt.Errorf("failed to unmarshal conversation, userID: %d, storyID: %d, err: %w", userID, storyID, err)
	}

	return messages, nil
}

// 将对话历史存回数据库
func (s *LlmService) saveConversationHistory(ctx context.Context, userID int64, storyID int64, messages []openai.ChatCompletionMessage) error {
	conversationJSON, err := json.Marshal(messages)
	if err != nil {
		return fmt.Errorf("failed to marshal conversation, userID: %d, storyID: %d, err: %w", userID, storyID, err)
	}

	query := dal.Use(s.data.MasterDB(ctx)).TypeConversation

	// 使用 UPSERT 操作：如果记录存在则更新，不存在则创建
	// created_at 和 updated_at 字段使用数据库默认值
	err = query.WithContext(ctx).Save(&model.TypeConversation{
		UserID:       userID,
		StoryID:      storyID,
		Conversation: string(conversationJSON),
	})

	if err != nil {
		return fmt.Errorf("failed to save conversation, userID: %d, storyID: %d, err: %w", userID, storyID, err)
	}

	return nil
}
