package domain_services_ai

import (
	repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	"context"
	"encoding/json"
	"fmt"
	// "math/rand"
	"strings"

	"github.com/sashabaranov/go-openai"
)

// RoastedR1Response represents the response from R1 stage
type RoastedR1Response struct {
	Thinking string `json:"thinking"`
	R1       string `json:"R1"`
}

// RoastedR2Input represents the input for R2 stage
type RoastedR2Input struct {
	Thinking   string `json:"thinking"`
	R1         string `json:"R1"`
	UserAnswer string `json:"user_answer"`
}

// GenerateRoastedR1 generates the R1 question and thinking process
// 题库生成
func (s *LlmService) generateRoastedR1(ctx context.Context) (*RoastedR1Response, error) {
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.RoastedLLMConfig)
	if err != nil {
		return nil, err
	}
	return s.realGenerateRoastedR1(ctx, llmPrompt)
}

// RealGenerateRoastedR1 implements the actual R1 generation logic
func (s *LlmService) realGenerateRoastedR1(ctx context.Context, llmConfig LlmConfig) (*RoastedR1Response, error) {
	userPromptContent := []interface{}{
		map[string]string{
			"type": "text",
			"text": "now please roast me with a question!",
		},
	}

	responseFormat := ResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                 `json:"name"`
			Schema map[string]interface{} `json:"schema"`
		}{
			Name: "reply",
			Schema: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"thinking": map[string]string{"type": "string"},
					"R1":       map[string]string{"type": "string"},
				},
				"required": []string{"thinking", "R1"},
			},
		},
	}

	response, err := s.Complete(ctx, []interface{}{
		s.genSystemPrompt(llmConfig.RoastedAIResponse.R1SystemPrompt),
		UserPrompt{Role: "user", Content: userPromptContent},
	}, llmConfig.RoastedAIResponse.Model, llmConfig.RoastedAIResponse.Temperature, responseFormat)
	if err != nil {
		return nil, err
	}

	var parsed RoastedR1Response
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return nil, err
	}
	return &parsed, nil
}

// GenerateRoastedR2 generates the R2 response based on user's answer
// 获取 Topic 的追问
func (s *LlmService) GenerateRoastedR2(ctx context.Context, thinking string, r1Question string, userAnswer string, currentFollowQuestionCount uint32, totalVideoDurationSeconds uint32) (string, bool, error) {
	//! 0. Load LLM Config
	llmPrompt, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.RoastedLLMConfig)
	if err != nil {
		return "", true, err
	}

	//! 0. Judge if stop
	isStop := false

	// if currentFollowQuestionCount == 2, then stop with a 80% probability
	// if currentFollowQuestionCount == 3 {
	// 	if rand.Float64() < 0.6 {
	// 		isStop = true
	// 	}
	// } else if currentFollowQuestionCount == 4 {
	// 	if rand.Float64() < 0.5 {  // (1-0.6) * 0.5 = 0.2
	// 		isStop = true
	// 	}
	// } else if currentFollowQuestionCount == 5 {
	// 	isStop = true   
	// }

	// // if consumed over 3minutes, then stop
	// if totalVideoDurationSeconds > 300 {
	// 	isStop = true
	// }

	if isStop {
		return "", true, nil
	}

	//! 1. Construct Input & Query R2
	input := &RoastedR2Input{
		Thinking:   thinking,
		R1:         r1Question,
		UserAnswer: userAnswer,
	}
	ch, err := s.realGenerateRoastedR2(ctx, llmPrompt, input)

	//! 2. Fetch tokens from ch
	token_list := []string{}
	for token := range ch {
		token_list = append(token_list, token)
	}
	roast_question := strings.Join(token_list, "")


	if strings.Contains(roast_question, "[STOP]") {
		isStop = true
	}

	return roast_question, isStop, err
}

// RealGenerateRoastedR2 implements the actual R2 generation logic
func (s *LlmService) realGenerateRoastedR2(ctx context.Context, llmConfig LlmConfig, input *RoastedR2Input) (<-chan string, error) {
	// userPromptContent := fmt.Sprintf("Thinking from R1 stage: %s\n\nR1 Question asked: %s\n\nUser's answer (ASR): %s",
	// 	input.Thinking, input.R1, input.UserAnswer)
	// construct user prompt content from template
	userPromptContent := fmt.Sprintf(llmConfig.RoastedAIResponse.R2UserPromptTemplate, input.Thinking, input.R1, input.UserAnswer)
	// "Thinking from R1 stage: {thinking_from_r1}\n\nR1 Question asked: {r1_question}\n\nUser's answer (ASR): {user_answer_asr}"
	userPromptContent = strings.ReplaceAll(userPromptContent, "{thinking_from_r1}", input.Thinking) // used for conversation history not used for R2
	userPromptContent = strings.ReplaceAll(userPromptContent, "{r1_question}", input.R1)
	userPromptContent = strings.ReplaceAll(userPromptContent, "{user_answer_asr}", input.UserAnswer)

	// 构建 OpenRouter 需要的消息格式
	prompts := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.RoastedAIResponse.R2SystemPrompt,
		},
		{
			Role: "user",
			MultiContent: []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: userPromptContent,
				},
			},
		},
	}

	responseFormat := NewResponseFormat{
		Type: "text",
	}

	responseChan := make(chan string)
	go func() {
		defer close(responseChan)

		response, err := s.OpenRouterComplete(ctx, prompts, llmConfig.RoastedAIResponse.Model, llmConfig.RoastedAIResponse.Temperature, responseFormat, nil, nil)
		if err != nil {
			responseChan <- fmt.Sprintf("Error: %v", err)
			return
		}
		responseChan <- response
	}()

	return responseChan, nil
}
