package domain_services_ai

import (
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/sashabaranov/go-openai"
)

type TypeMultiturnLLMRequest struct {
	UserMessage  string `json:"user_message"`
	IntentPrompt string `json:"intent_prompt"`
	Caption      string `json:"caption"`
	TryCount     uint32 `json:"try_count"`
}

func (s *LlmService) TypeMultiturnLLMConversation(ctx context.Context, llmPrompt LlmConfig, request *TypeMultiturnLLMRequest, history_msg []openai.ChatCompletionMessage) (
	matchStatus api_items_story_types_v1.TurtleSoupMatchStatus,
	aiResponse string,
	history_msg_new []openai.ChatCompletionMessage,
	err error,
) {
	turtleSoupPrompt := llmPrompt.PeekMatch
	// user prompt content = user image content + creator's condition content

	// ### User Input \\n- Creator-defined criteria: {intent_prompt}\\n\\n
	// - Creator caption : {creator_caption}\\n\\n
	// - User''s submission: {user_response}\\n\\n
	// - Try count: {try_count}
	conditionContent := strings.Replace(turtleSoupPrompt.UserPromptTemplate, "{intent_prompt}", request.IntentPrompt, -1)
	conditionContent = strings.Replace(conditionContent, "{creator_caption}", request.Caption, -1)
	conditionContent = strings.Replace(conditionContent, "{user_response}", request.UserMessage, -1)
	conditionContent = strings.Replace(conditionContent, "{try_count}", fmt.Sprintf("%d", request.TryCount), -1)

	if len(history_msg) == 0 {
		history_msg = append(history_msg, openai.ChatCompletionMessage{
			Role:    "system",
			Content: turtleSoupPrompt.SystemPrompt,
		})
	} else {
		// force set history_msg[0] to system prompt
		history_msg[0] = openai.ChatCompletionMessage{
			Role:    "system",
			Content: turtleSoupPrompt.SystemPrompt,
		}
	}

	userMessage := openai.ChatCompletionMessage{
		Role:    "user",
		Content: conditionContent,
	}
	history_msg = append(history_msg, userMessage)

	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "reply",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"match_level": map[string]string{"type": "integer"},
					"reply":       map[string]string{"type": "string"},
				},
				"required":             []string{"match_level", "reply"},
				"additionalProperties": false,
			},
		},
	}
	response, err := s.OpenRouterComplete(ctx, history_msg, turtleSoupPrompt.Model, turtleSoupPrompt.Temperature, responseFormat, nil, nil)
	if err != nil {
		return api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, "", history_msg, err
	}
	var parsed struct {
		MatchLevel int32  `json:"match_level"`
		Reply      string `json:"reply"`
	}
	if err := json.Unmarshal([]byte(response), &parsed); err != nil {
		return api_items_story_types_v1.TurtleSoupMatchStatus_TURTLE_SOUP_MATCH_STATUS_UNSPECIFIED, "", history_msg, err
	}

	history_msg = append(history_msg, openai.ChatCompletionMessage{
		Role:    "assistant",
		Content: response,
	})

	return api_items_story_types_v1.TurtleSoupMatchStatus(parsed.MatchLevel), parsed.Reply, history_msg, nil

}
