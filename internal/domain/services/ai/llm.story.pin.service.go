package domain_services_ai

import (
	api_resource_types_v1 "boson/api/resource/types/v1"
	repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_interfaces "boson/internal/domain/interfaces"
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"image"
	"image/draw"
	"io"
	"math"
	"mime/multipart"
	"net/http"
	"os"
	"time"

	"image/jpeg"
	_ "image/png" // 导入PNG解码器

	_ "golang.org/x/image/webp" // 导入WebP解码器

	"github.com/sashabaranov/go-openai"
)

// GeneratePinSceneParsing
type PinSceneParsingRequest struct {
	ImageKey domain_entities_resource.ImageResourcePath `json:"image_key"`
}

type PinSceneParsingEntry struct {
	TextDescription string     `json:"text_description"`
	Emoji           string     `json:"emoji"`
	BBox            [4]float64 `json:"bbox"`
	Reasoning       string     `json:"reasoning"`
}

type PinSceneParsingEntryNewFormat struct {
	TextDescription string  `json:"text_description"`
	Emoji           string  `json:"emoji"`
	Reasoning       string  `json:"reasoning"`
	CenterX         float64 `json:"center_x"`
	CenterY         float64 `json:"center_y"`
	Width           float64 `json:"width"`
	Height          float64 `json:"height"`
}

type PinSceneParsingResponse []PinSceneParsingEntryNewFormat

// GeneratePinSceneParsing analyzes an image and returns 4-6 emoji entries with bboxes.
func (s *LlmService) GeneratePinSceneParsing(ctx context.Context, req *PinSceneParsingRequest) (PinSceneParsingResponse, error) {
	// 1) Load config
	llmConfig, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.PinSceneParsingLLMConfig)
	if err != nil {
		return nil, err
	}
	return s.RealGeneratePinSceneParsing(ctx, req, llmConfig)
}

func (s *LlmService) RealGeneratePinSceneParsing(ctx context.Context, req *PinSceneParsingRequest, llmConfig LlmConfig) (PinSceneParsingResponse, error) {

	// 2) Build messages with system prompt and image URL
	messages := []openai.ChatCompletionMessage{
		{
			Role:    "system",
			Content: llmConfig.PinSceneParsing.SystemPrompt,
		},
		{
			Role: "user",
			MultiContent: []openai.ChatMessagePart{
				{
					Type: openai.ChatMessagePartTypeText,
					Text: "Analyze the image and return entries as per the JSON schema.",
				},
				{
					Type: openai.ChatMessagePartTypeImageURL,
					ImageURL: &openai.ChatMessageImageURL{
						URL:    req.ImageKey.UserCopilotMessage(),
						Detail: openai.ImageURLDetailAuto,
					},
				},
			},
		},
	}

	// 3) Response schema definition
	schema := &ResponseFormatSchema{
		"type": "array",
		"items": map[string]interface{}{
			"type": "object",
			"properties": map[string]interface{}{
				"text_description": map[string]string{"type": "string"},
				"emoji":            map[string]string{"type": "string"},
				"bbox": map[string]interface{}{
					"type":     "array",
					"items":    map[string]string{"type": "number"},
					"minItems": 4,
					"maxItems": 4,
				},
				"reasoning": map[string]string{"type": "string"},
			},
			"required":             []string{"text_description", "emoji", "bbox", "reasoning"},
			"additionalProperties": false,
		},
		"minItems": 2,
		"maxItems": 6,
	}

	rf := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name:   "pin_scene_parsing",
			Schema: schema,
		},
	}

	// 4) Call LLM
	raw, err := s.OpenRouterComplete(ctx, messages, llmConfig.PinSceneParsing.Model, llmConfig.PinSceneParsing.Temperature, rf, nil, nil)
	if err != nil {
		return nil, err
	}

	// 5) Parse JSON
	var out []PinSceneParsingEntry
	if err := json.Unmarshal([]byte(raw), &out); err != nil {
		return nil, err
	}

	outNewFormat := make(PinSceneParsingResponse, len(out))
	for i, entry := range out {
		outNewFormat[i] = PinSceneParsingEntryNewFormat{
			TextDescription: entry.TextDescription,
			Emoji:           entry.Emoji,
			Reasoning:       entry.Reasoning,
		}
		outNewFormat[i].CenterX = (entry.BBox[0] + entry.BBox[2]) / 2
		outNewFormat[i].CenterY = (entry.BBox[1] + entry.BBox[3]) / 2
		outNewFormat[i].Width = entry.BBox[2] - entry.BBox[0]
		outNewFormat[i].Height = entry.BBox[3] - entry.BBox[1]
	}
	return outNewFormat, nil
}

// GeneratePinObjectEmoji
// If user is not satisfied with the emoji, the user can ask for a new one.
type PinObjectEmojiRequest struct {
	ImageKey domain_entities_resource.ImageResourcePath `json:"image_key"`
	CenterX  float64                                    `json:"center_x"`
	CenterY  float64                                    `json:"center_y"`
	Width    float64                                    `json:"width"`
	Height   float64                                    `json:"height"`
}

type PinObjectEmojiResponse struct {
	ImageKey domain_entities_resource.ImageResourcePath `json:"image_key"` // Generated emoji
}

func (s *LlmService) GeneratePinObjectEmoji(ctx context.Context, req *PinObjectEmojiRequest) (PinObjectEmojiResponse, error) {
	// Download ImageKey, crop using center_x/center_y/width/height, and convert to base64
	url := req.ImageKey.UserCopilotMessage()
	s.logger.Infof("[GeneratePinObjectEmoji] Downloading image from URL: %s", url)

	resp, err := http.Get(url)
	if err != nil {
		s.logger.Errorf("[GeneratePinObjectEmoji] HTTP GET failed: %v", err)
		return PinObjectEmojiResponse{}, err
	}
	defer resp.Body.Close()

	s.logger.Infof("[GeneratePinObjectEmoji] HTTP response status: %d, content-type: %s", resp.StatusCode, resp.Header.Get("Content-Type"))

	if resp.StatusCode != http.StatusOK {
		b, _ := io.ReadAll(resp.Body)
		s.logger.Errorf("[GeneratePinObjectEmoji] HTTP error response body: %s", string(b))
		return PinObjectEmojiResponse{}, fmt.Errorf("fetch image failed: %d, %s", resp.StatusCode, string(b))
	}

	imgBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Errorf("[GeneratePinObjectEmoji] Failed to read response body: %v", err)
		return PinObjectEmojiResponse{}, err
	}

	s.logger.Infof("[GeneratePinObjectEmoji] Downloaded %d bytes, first 20 bytes: %x", len(imgBytes), imgBytes[:min(20, len(imgBytes))])

	img, format, err := image.Decode(bytes.NewReader(imgBytes))
	if err != nil {
		s.logger.Errorf("[GeneratePinObjectEmoji] Image decode failed: %v, format detected: %s, bytes length: %d", err, format, len(imgBytes))
		return PinObjectEmojiResponse{}, fmt.Errorf("image decode failed: %v", err)
	}

	s.logger.Infof("[GeneratePinObjectEmoji] Image decoded successfully, format: %s, dimensions: %dx%d", format, img.Bounds().Dx(), img.Bounds().Dy())

	bounds := img.Bounds()
	w := bounds.Dx()
	h := bounds.Dy()
	clamp := func(v float64) float64 {
		if v < 0 {
			return 0
		}
		if v > 1 {
			return 1
		}
		return v
	}

	// Convert center/size -> corner bbox
	x1n := clamp(req.CenterX - req.Width/2)
	y1n := clamp(req.CenterY - req.Height/2)
	x2n := clamp(req.CenterX + req.Width/2)
	y2n := clamp(req.CenterY + req.Height/2)

	x1 := int(math.Round(float64(w) * x1n))
	y1 := int(math.Round(float64(h) * y1n))
	x2 := int(math.Round(float64(w) * x2n))
	y2 := int(math.Round(float64(h) * y2n))
	if x2 <= x1 {
		x2 = int(math.Min(float64(w), float64(x1+1)))
	}
	if y2 <= y1 {
		y2 = int(math.Min(float64(h), float64(y1+1)))
	}
	cropRect := image.Rect(x1, y1, x2, y2).Intersect(bounds)
	if cropRect.Empty() {
		// fallback: use full image
		cropRect = bounds
	}

	// Create RGBA and draw crop
	rgba := image.NewRGBA(image.Rect(0, 0, cropRect.Dx(), cropRect.Dy()))
	draw.Draw(rgba, rgba.Bounds(), img, cropRect.Min, draw.Src)

	// Encode to JPEG for external API
	var buf bytes.Buffer
	if err := jpeg.Encode(&buf, rgba, &jpeg.Options{Quality: 90}); err != nil {
		return PinObjectEmojiResponse{}, err
	}
	croppedImageBytes := buf.Bytes()

	s.logger.Infof("[GeneratePinObjectEmoji] Cropped image encoded: %d bytes, crop size: %dx%d",
		len(croppedImageBytes), cropRect.Dx(), cropRect.Dy())

	// Call external image API with multipart form data in correct order
	apiURL := os.Getenv("EXTERNAL_IMG_API_URL")
	if apiURL == "" {
		apiURL = "http://************:9001/generate-with-image-and-return"
	}

	prompt := "turn this into an APPLE_EMOJI, white background"
	width := "512"
	height := "512"
	imageStrength := "0.8"
	imageGuidance := "1.5"

	var body bytes.Buffer
	mw := multipart.NewWriter(&body)

	// 1) image first (preserving field order as specified)
	// 手动创建文件字段并设置正确的Content-Type
	header := make(map[string][]string)
	header["Content-Disposition"] = []string{`form-data; name="image"; filename="cropped_image.jpg"`}
	header["Content-Type"] = []string{"image/jpeg"}
	imagePart, err := mw.CreatePart(header)
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}
	if _, err := imagePart.Write(croppedImageBytes); err != nil {
		return PinObjectEmojiResponse{}, err
	}

	// 2) ordered fields
	_ = mw.WriteField("prompt", prompt)
	_ = mw.WriteField("width", width)
	_ = mw.WriteField("height", height)
	_ = mw.WriteField("image_strength", imageStrength)
	_ = mw.WriteField("image_guidance", imageGuidance)
	if err := mw.Close(); err != nil {
		return PinObjectEmojiResponse{}, err
	}

	reqCtx, cancel := context.WithTimeout(ctx, 180*time.Second)
	defer cancel()
	httpReq, err := http.NewRequestWithContext(reqCtx, http.MethodPost, apiURL, &body)
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}
	httpReq.Header.Set("Content-Type", mw.FormDataContentType())
	httpReq.Header.Set("Accept", "image/png, */*")

	resp2, err := http.DefaultClient.Do(httpReq)
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}
	defer resp2.Body.Close()
	generatedImageBytes, err := io.ReadAll(resp2.Body)
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}

	s.logger.Infof("[ExternalIMG] status=%d ct=%s bytes=%d", resp2.StatusCode, resp2.Header.Get("Content-Type"), len(generatedImageBytes))

	if resp2.StatusCode != http.StatusOK {
		s.logger.Errorf("[ExternalIMG] API failed with status %d, response body: %s", resp2.StatusCode, string(generatedImageBytes))
		return PinObjectEmojiResponse{}, fmt.Errorf("external API failed: %d, body: %s", resp2.StatusCode, string(generatedImageBytes))
	}

	// Upload generated image to CDN
	uploadInfo, err := s.cdnService.GetUploadInfo(ctx, 1, domain_interfaces.GetUploadFileInfoAttr{
		ContentType: api_resource_types_v1.ContentType_CONTENT_TYPE_IMAGE_PNG,
		Scenario:    api_resource_types_v1.Scenario_SCENARIO_STORY_RESOURCE,
	})
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}

	uploadReq, err := http.NewRequestWithContext(ctx, http.MethodPut, uploadInfo.UploadURL, bytes.NewReader(generatedImageBytes))
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}
	uploadReq.Header.Set("Content-Type", uploadInfo.ContentType)
	uploadReq.Header.Set("Expires", uploadInfo.ExpiresStr)

	uploadResp, err := http.DefaultClient.Do(uploadReq)
	if err != nil {
		return PinObjectEmojiResponse{}, err
	}
	uploadResp.Body.Close()

	if uploadResp.StatusCode != http.StatusOK {
		return PinObjectEmojiResponse{}, fmt.Errorf("failed to upload to CDN: %d", uploadResp.StatusCode)
	}

	s.logger.Infof("Generated emoji uploaded to CDN: objectKey=%s", uploadInfo.ObjectKey)

	return PinObjectEmojiResponse{
		ImageKey: domain_entities_resource.ImageResourcePath(uploadInfo.ObjectKey),
	}, nil
}

type PinObjectGroundingRequest struct {
	ImageKey             domain_entities_resource.ImageResourcePath `json:"image_key"`
	GroundingDescription string                                     `json:"grounding_description"`
}

type PinObjectGroundingResponse struct {
	CenterX float64 `json:"center_x"`
	CenterY float64 `json:"center_y"`
	Width   float64 `json:"width"`
	Height  float64 `json:"height"`
}

func (s *LlmService) GeneratePinObjectGrounding(ctx context.Context, req *PinObjectGroundingRequest) (PinObjectGroundingResponse, error) {
	return PinObjectGroundingResponse{
		CenterX: 0.5,
		CenterY: 0.5,
		Width:   1.0,
		Height:  1.0,
	}, nil
}
