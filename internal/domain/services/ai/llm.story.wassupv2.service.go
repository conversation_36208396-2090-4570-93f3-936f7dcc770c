package domain_services_ai

import (
	repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_resource "boson/internal/domain/entities/resource"
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"github.com/sashabaranov/go-openai"
)

// Story Co-Pilot Request
// Request for the story co-pilot functionality
type StoryCoPilotRequest struct {
	ImageKey      domain_entities_resource.ImageResourcePath `json:"image_key"`
	UserNarration string                                     `json:"user_narration,omitempty"`
}

// Story Co-Pilot Response
// Response format for the story co-pilot functionality
type StoryCoPilotResponse struct {
	Turn     int      `json:"turn"`
	Response []string `json:"response"`
	End      bool     `json:"end"`
}

// GenerateStoryCoPilotResponse generates a response for the story co-pilot functionality
func (s *LlmService) GenerateStoryCoPilotResponse(ctx context.Context, request *StoryCoPilotRequest, history_msg []openai.ChatCompletionMessage) (StoryCoPilotResponse, []openai.ChatCompletionMessage, error) {
	llmConfig, err := s.LoadSnapshotPrompt(ctx, repos_app_settings.StoryCoPilotLLMConfig)
	if err != nil {
		return StoryCoPilotResponse{}, history_msg, errors.Wrapf(err, "failed to load story co-pilot LLM config")
	}

	// Make a copy of history_msg for the final return
	history_msg_new := make([]openai.ChatCompletionMessage, len(history_msg))
	copy(history_msg_new, history_msg)

	// If length of history_msg is 0, then we need to generate the first message
	if len(history_msg_new) == 0 {
		system_prompt := llmConfig.StoryCoPilotAIResponse.SystemPrompt
		history_msg_new = append(history_msg_new, openai.ChatCompletionMessage{
			Role:    "system",
			Content: system_prompt,
		})
	}

	// Build user prompt with image and optional information
	var userPromptContent []openai.ChatMessagePart

	// If this is the first turn (no history), include image analysis
	if len(history_msg) == 0 {
		userPromptContent = []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeText,
				Text: "Please analyze this image and provide story co-pilot guidance.",
			},
			{
				Type: openai.ChatMessagePartTypeImageURL,
				ImageURL: &openai.ChatMessageImageURL{
					URL:    request.ImageKey.UserCopilotMessage(),
					Detail: openai.ImageURLDetailAuto,
				},
			},
		}
	} else {
		// For subsequent turns, only include user narration
		userPromptContent = []openai.ChatMessagePart{
			{
				Type: openai.ChatMessagePartTypeText,
				Text: "User narration: " + request.UserNarration,
			},
		}
	}

	userMessage := openai.ChatCompletionMessage{
		Role:         "user",
		MultiContent: userPromptContent,
	}
	history_msg_new = append(history_msg_new, userMessage)

	// Call openrouter complete
	responseFormat := NewResponseFormat{
		Type: "json_schema",
		JSONSchema: &struct {
			Name   string                `json:"name"`
			Schema *ResponseFormatSchema `json:"schema"`
		}{
			Name: "story_co_pilot_response",
			Schema: &ResponseFormatSchema{
				"type": "object",
				"properties": map[string]interface{}{
					"turn": map[string]string{"type": "integer"},
					"response": map[string]interface{}{
						"type": "array",
						"items": map[string]string{
							"type": "string",
						},
					},
					"end": map[string]string{"type": "boolean"},
				},
				"required":             []string{"turn", "response", "end"},
				"additionalProperties": false,
			},
		},
	}

	response, err := s.OpenRouterComplete(ctx, history_msg_new, llmConfig.StoryCoPilotAIResponse.Model, llmConfig.StoryCoPilotAIResponse.Temperature, responseFormat, nil, nil)
	if err != nil {
		return StoryCoPilotResponse{}, history_msg_new, errors.Wrapf(err, "failed to call OpenRouter for story co-pilot")
	}

	// Parse the response
	var storyCoPilotResponse StoryCoPilotResponse
	if err := json.Unmarshal([]byte(response), &storyCoPilotResponse); err != nil {
		return StoryCoPilotResponse{}, history_msg_new, errors.Wrapf(err, "failed to parse story co-pilot response: %s", response)
	}

	// Append assistant output to history_msg_copy
	history_msg_new = append(history_msg_new, openai.ChatCompletionMessage{
		Role:    "assistant",
		Content: response,
	})

	// Return the response and history_msg_copy
	return storyCoPilotResponse, history_msg_new, nil
}
