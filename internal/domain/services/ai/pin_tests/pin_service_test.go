package pin_tests

import (
	"context"
	"os"
	"testing"
	"time"

	adapter_driving_services_cdn "boson/internal/adapter/driving/services/cdn"
	"boson/internal/adapter/driving/services/cdn/s3"
	adapter_driving_services_openai "boson/internal/adapter/driving/services/openai"
	adapter_driving_services_openrouter "boson/internal/adapter/driving/services/openrouter"
	"boson/internal/conf"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_ai "boson/internal/domain/services/ai"
	"boson/internal/infra/data"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// !!! --- 集成测试配置 --- !!!
// !!! 警告: 请勿将真实的密钥提交到版本控制中 !!!
const (
	// 测试图片路径
	testPinImagePath  = "flippop/image/item/story/1/202506261046/a0e26043b6f34a2d9245337b244a969e.png"
	testPinImagePath2 = "flippop/image/item/story/1/202506261046/a0e26043b6f34a2d9245337b244a969e.png"

	// OpenRouter API Key
	openRouterAPIKey = "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"
)

// setupRealLlmService 创建真实的 LlmService 用于集成测试 - 使用真实的 S3 CDN 服务
func setupRealLlmService(t *testing.T) *domain_services_ai.LlmService {
	logger := log.NewHelper(log.NewStdLogger(os.Stdout)) // 输出日志到控制台

	// 使用真实的 S3 CDN 服务（通过适配器包装）
	s3Service := s3.NewService(&conf.Bootstrap{
		ThirdParty: &conf.ThirdParty{
			S3: &conf.ThirdParty_AWS{
				Region:          "us-east-1",
				AccessKeyId:     "********************",
				SecretAccessKey: "W2QmiEKzdyJKYvyz69WYUfmuXKbs5qrCee68HToR",
			},
		},
	})
	cdnService := adapter_driving_services_cdn.NewService(s3Service)

	// 创建真实的 OpenAI 客户端
	openAIClient := adapter_driving_services_openai.NewOpenAIClient(cdnService, logger)

	// 创建 OpenRouter 客户端
	openRouterClient := adapter_driving_services_openrouter.NewClient(&conf.Bootstrap{
		ThirdParty: &conf.ThirdParty{
			OpenRouter: &conf.ThirdParty_OpenRouter{
				ApiKey: openRouterAPIKey,
			},
		},
	}, logger)

	// 创建 mock data - 这里不会被使用，因为我们直接调用 RealGeneratePinSceneParsing
	mockData := &data.Data{}

	return domain_services_ai.NewLlmService(
		&conf.Bootstrap{
			Env: conf.ENV_ENV_DEV,
			ThirdParty: &conf.ThirdParty{
				OpenRouter: &conf.ThirdParty_OpenRouter{
					ApiKey: openRouterAPIKey,
				},
			},
		},
		logger,
		openAIClient,
		openAIClient,
		nil, // appSettings - 不会被使用
		openRouterClient,
		mockData,
		cdnService,
	)
}

func TestLlmService_BasicFunctionality(t *testing.T) {
	service := setupRealLlmService(t)

	// 验证服务不为空
	assert.NotNil(t, service)

	t.Log("✅ LlmService successfully initialized for pin functionality testing")
}

func TestLlmService_GeneratePinSceneParsing_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode.")
	}

	// 创建 LLM 服务用于测试
	service := setupRealLlmService(t)
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Minute)
	defer cancel()

	// 测试参数
	imageKey := domain_entities_resource.ImageResourcePath(testPinImagePath)
	t.Logf("🚀 Testing RealGeneratePinSceneParsing with image: %s", imageKey)
	t.Logf("📸 Image URL: %s", imageKey.UserCopilotMessage())

	req := &domain_services_ai.PinSceneParsingRequest{
		ImageKey: imageKey,
	}

	// 创建 LLM 配置
	llmConfig := domain_services_ai.LlmConfig{}
	llmConfig.PinSceneParsing.SystemPrompt = `You are a professional image analyzer. Given an image, your task is to use 4-6 emojis to describe the image and locate their regions of interest. 
For each emoji, you must provide four pieces of information:
reasoning: The reasoning process of the emoji and the bbox. Avoid too big or too small bbox.
emoji: A single emoji that can best describe a part of the image. Be specific, not abstract like atmosphere.
text description: Concise and precise description of the emoji, within 5 words. Use simple words.
bbox: The normalized coordinates of the bbox of the region of interest (rounded to 3 decimal places), represented as a tuple of floats (x1, y1, x2, y2) where x1, y1, x2, y2 are between 0 and 1. x1 and x2 corresponds to the horizontal axis (from left to right) and y1 and y2 to the vertical axis (from top to bottom). Box the selected object or area as precisely as possible.
Your output should be a JSON object containing a list of these structured entries, with each entry corresponding to a single emoji and its region of interest.
Example strict JSON structure:
JSON
[
  {
    "text_description": "...",
    "emoji": "🔍",
    "bbox": [x1, y1, x2, y2],
    "reasoning": "..."
  },
  {
    "text_description": "...",
    "emoji": "💡",
    "bbox": [x1, y1, x2, y2],
    "reasoning": "..."
  }
]

Rules:
- The bbox should be as precise as possible.
- Do NOT split a complete object.
- Do NOT select too small objects like bottles, sunglasses, etc.
- Do NOT try to describe the entire image.`
	llmConfig.PinSceneParsing.Model = "google/gemini-2.5-flash"
	llmConfig.PinSceneParsing.Temperature = 1.0

	t.Logf("🤖 Using model: %s", llmConfig.PinSceneParsing.Model)
	t.Logf("🌡️  Temperature: %.1f", llmConfig.PinSceneParsing.Temperature)

	// 直接调用 RealGeneratePinSceneParsing
	result, err := service.RealGeneratePinSceneParsing(ctx, req, llmConfig)

	// 验证结果
	require.NoError(t, err, "RealGeneratePinSceneParsing should succeed")
	assert.NotEmpty(t, result)
	assert.GreaterOrEqual(t, len(result), 4, "Should return at least 4 entries")
	assert.LessOrEqual(t, len(result), 6, "Should return at most 6 entries")

	t.Logf("🎯 Found %d objects in the image:", len(result))
	for i, entry := range result {
		t.Logf("  %d. %s %s", i+1, entry.Emoji, entry.TextDescription)
		t.Logf("     📍 Position: center(%.3f, %.3f) size(%.3f, %.3f)",
			entry.CenterX, entry.CenterY, entry.Width, entry.Height)
		t.Logf("     💭 Reasoning: %s", entry.Reasoning)

		// 验证每个条目的字段
		assert.NotEmpty(t, entry.TextDescription, "TextDescription should not be empty")
		assert.NotEmpty(t, entry.Emoji, "Emoji should not be empty")
		assert.NotEmpty(t, entry.Reasoning, "Reasoning should not be empty")

		// 验证坐标范围 (0-1)
		assert.GreaterOrEqual(t, entry.CenterX, 0.0, "CenterX should be >= 0")
		assert.LessOrEqual(t, entry.CenterX, 1.0, "CenterX should be <= 1")
		assert.GreaterOrEqual(t, entry.CenterY, 0.0, "CenterY should be >= 0")
		assert.LessOrEqual(t, entry.CenterY, 1.0, "CenterY should be <= 1")
		assert.Greater(t, entry.Width, 0.0, "Width should be > 0")
		assert.LessOrEqual(t, entry.Width, 1.0, "Width should be <= 1")
		assert.Greater(t, entry.Height, 0.0, "Height should be > 0")
		assert.LessOrEqual(t, entry.Height, 1.0, "Height should be <= 1")
	}

	t.Logf("✅ RealGeneratePinSceneParsing completed successfully with %d entries", len(result))
}

func TestLlmService_GeneratePinObjectEmoji_Integration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode.")
	}

	// 创建 LLM 服务用于测试
	service := setupRealLlmService(t)
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute) // 更长超时，因为涉及外部API和CDN
	defer cancel()

	// 测试参数 - 使用已知的测试图片
	imageKey := domain_entities_resource.ImageResourcePath(testPinImagePath)
	t.Logf("🚀 Testing GeneratePinObjectEmoji with image: %s", imageKey)
	t.Logf("📸 Image URL: %s", imageKey.UserCopilotMessage())

	// 模拟从场景解析结果中选择一个区域进行emoji生成
	// 使用图片中心区域作为测试
	req := &domain_services_ai.PinObjectEmojiRequest{
		ImageKey: imageKey,
		CenterX:  0.5, // 图片中心
		CenterY:  0.4, // 稍微偏上，通常人脸位置
		Width:    0.3, // 30% 宽度
		Height:   0.4, // 40% 高度
	}

	t.Logf("🎯 Crop region: center(%.1f, %.1f) size(%.1f, %.1f)",
		req.CenterX, req.CenterY, req.Width, req.Height)

	// 调用 GeneratePinObjectEmoji
	result, err := service.GeneratePinObjectEmoji(ctx, req)

	// 验证结果
	require.NoError(t, err, "GeneratePinObjectEmoji should succeed")
	assert.NotEmpty(t, result.ImageKey, "Generated emoji ImageKey should not be empty")

	t.Logf("✅ Generated emoji uploaded to CDN: %s", result.ImageKey)
	t.Logf("🔗 Emoji URL: %s", result.ImageKey.UserCopilotMessage())

	// 验证生成的图片key格式正确（应该是CDN路径）
	imageKeyStr := string(result.ImageKey)
	assert.Contains(t, imageKeyStr, "flippop/", "Generated ImageKey should contain flippop path")

	// 验证生成的图片URL可访问
	emojiURL := result.ImageKey.UserCopilotMessage()
	assert.True(t, len(emojiURL) > 0, "Emoji URL should not be empty")
	assert.Contains(t, emojiURL, "https://", "Emoji URL should be HTTPS")

	t.Logf("✅ GeneratePinObjectEmoji completed successfully")
	t.Logf("📊 Generated emoji key: %s", result.ImageKey)
}
