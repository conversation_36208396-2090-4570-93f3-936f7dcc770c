package domain_services_fizz

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	api_errors_v1 "boson/api/errors/v1"
	api_fizz_resource_types_v1 "boson/api/fizz/resource/types/v1"
	api_fizz_user_relation_types_v1 "boson/api/fizz/user_relation/types/v1"
	"boson/internal/conf"
	domain_entities_fizz "boson/internal/domain/entities/fizz"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_entities_users "boson/internal/domain/entities/users"
	domain_services_users_info "boson/internal/domain/services/users/info"
	"boson/internal/infra/data"
)

const (
	// user_id, fizz_id, check_type
	cacheKeyFmt = "join_check_cache_%d_%d_%s"
	lockKeyFmt  = "join_check_cache_lock_%d_%d_%s"
)

type JoinAttr struct {
	PhotoKey *string
	Answer   *string
	Location *domain_entities_fizz.FizzLocation
}

type IJoinConditionChecker interface {
	CheckAnswer(ctx context.Context, fizzQuestion *domain_entities_fizz.FizzJoinConditionQuestion, userInput string) (bool, error)
	CheckPhoto(ctx context.Context, description string, examplePhotoKey domain_entities_resource.ImageResourcePath, userInputPhotoKey domain_entities_resource.ImageResourcePath) (bool, error)
}
type IJoinConditionRepository interface {
	CreateJoinRelation(ctx context.Context, userId int64, fizzId int64, checkResult *domain_entities_fizz.JoinConditionCheckResult) (err error)
	BatchListRecentJoinedUsers(ctx context.Context, userId int64, fizzIds []int64, from, end time.Time, limit uint32) (map[int64][]int64, error)
}

type UserRelationService struct {
	conf                    *conf.Bootstrap
	data                    *data.Data
	checker                 IJoinConditionChecker
	joinConditionRepository IJoinConditionRepository
	fizzQueryService        *FizzQueryService
	mediaService            *FizzMediaService
	userQueryService        *domain_services_users_info.UsersInfoService
}

func NewUserRelationService(
	conf *conf.Bootstrap,
	data *data.Data,
	checker IJoinConditionChecker,
	joinConditionRepository IJoinConditionRepository,
	fizzQueryService *FizzQueryService,
	mediaService *FizzMediaService,
	userQueryService *domain_services_users_info.UsersInfoService,
) *UserRelationService {
	return &UserRelationService{
		conf,
		data,
		checker,
		joinConditionRepository,
		fizzQueryService,
		mediaService,
		userQueryService,
	}
}

func (s *UserRelationService) batchListRecentJoinedUsers(ctx context.Context, userId int64, fizzIds []int64, from, end time.Time, limit uint32) (map[int64][]*domain_entities_users.UserSummaryEntity, error) {
	fizzIdUserIdsMap, err := s.joinConditionRepository.BatchListRecentJoinedUsers(ctx, userId, fizzIds, from, end, limit)
	if err != nil {
		return nil, err
	}
	userIds := lo.Flatten(lo.Values(fizzIdUserIdsMap))
	users, err := s.userQueryService.BatchGetUserInfo(ctx, userId, userIds...)
	if err != nil {
		return nil, err
	}
	result := make(map[int64][]*domain_entities_users.UserSummaryEntity)
	for fizzId, userIds := range fizzIdUserIdsMap {
		for _, userId := range userIds {
			if user, ok := users[userId]; ok {
				result[fizzId] = append(result[fizzId], user)
			}
		}
	}
	return result, nil
}

func (s *UserRelationService) JoinFizz(ctx context.Context, userId int64, fizzId int64, withOutCheck bool, resources ...*domain_entities_fizz.Resource) (err error) {
	fizz, err := s.fizzQueryService.GetFizzDetail(ctx, userId, fizzId)
	if err != nil {
		return err
	}
	if fizz.IsJoined() {
		return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("fizz already joined"))
	}

	var checkResult *domain_entities_fizz.JoinConditionCheckResult
	if !withOutCheck {
		checkResult, err = s.GetJoinFizzConditionCheckCache(ctx, userId, fizzId)
		if err != nil {
			return err
		}
		if fizz.Config.Location != nil &&
			fizz.Config.JoinCondition.DistanceLimit != nil &&
			!checkResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION].IsSatisfied {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("location not satisfied"))
		}

		if fizz.Config.JoinCondition.Question != nil && !checkResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER].IsSatisfied {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("answer not satisfied"))
		}

		if fizz.Config.ExampleResource != nil &&
			fizz.Config.ExampleResource.Resource != nil &&
			fizz.Config.ExampleResource.Resource.ResourceKey != "" &&
			fizz.Config.ExampleResource.Caption != "" &&
			!checkResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO].IsSatisfied {
			return errors.WithStack(api_errors_v1.ErrorErrorReasonBadRequestError("photo not satisfied"))
		}
	}

	if err := s.joinConditionRepository.CreateJoinRelation(ctx, userId, fizzId, checkResult); err != nil {
		return err
	}
	if len(resources) > 0 {
		// 把资源放进 fizz
		if _, err := s.mediaService.AddMediaIntoFizz(ctx, userId, fizzId, &AddMediaIntoFizzAttr{
			Resources: resources,
			// 非创作者加入 fizz，则创建事件
			CreateEvent: userId != fizz.Author.ID,
		}); err != nil {
			return err
		}
	}
	// TODO 延长fizz时长，发送消息等逻辑
	return nil
}

func (s *UserRelationService) GetJoinFizzConditionCheckCache(ctx context.Context, userId int64, fizzId int64) (joinResult *domain_entities_fizz.JoinConditionCheckResult, err error) {
	joinResult, err = s.getJoinCheckCache(ctx, userId, fizzId, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION)
	if err != nil {
		return nil, err
	}
	return joinResult, nil
}

func (s *UserRelationService) JoinFizzConditionCheck(ctx context.Context, userId int64, fizzId int64, req *JoinAttr) (joinResult *domain_entities_fizz.JoinConditionCheckResult, err error) {
	fizz, err := s.fizzQueryService.GetFizzDetail(ctx, userId, fizzId)
	if err != nil {
		return nil, err
	}

	var deleteCheckTypes []api_fizz_user_relation_types_v1.CheckType
	if req.PhotoKey == nil || *req.PhotoKey == "" {
		deleteCheckTypes = append(deleteCheckTypes, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO)
	}
	if req.Answer == nil || *req.Answer == "" {
		deleteCheckTypes = append(deleteCheckTypes, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER)
	}
	if req.Location == nil {
		deleteCheckTypes = append(deleteCheckTypes, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION)
	}
	// 端上删除缓存
	if len(deleteCheckTypes) > 0 {
		if err := s.deleteJoinCheckCache(ctx, userId, fizzId, deleteCheckTypes...); err != nil {
			return nil, err
		}
	}

	joinResult, err = s.getJoinCheckCache(ctx, userId, fizzId, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION)
	if err != nil {
		return nil, err
	}

	if req.PhotoKey != nil && *req.PhotoKey != "" {
		checkResult := &domain_entities_fizz.CheckResult{
			CheckType: api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO,
			Input:     req.PhotoKey,
		}
		usecache, satisfied, err := s.tryCheckPhotoAndSaveCache(ctx, userId, fizz, *req.PhotoKey)
		if err != nil {
			return nil, err
		}
		checkResult.UseCached = usecache
		checkResult.IsSatisfied = satisfied
		joinResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO] = checkResult
	}

	if req.Answer != nil && *req.Answer != "" {
		checkResult := &domain_entities_fizz.CheckResult{
			CheckType: api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER,
			Input:     req.Answer,
		}
		usecache, satisfied, err := s.tryCheckAnswerAndSaveCache(ctx, userId, fizz, *req.Answer)
		if err != nil {
			return nil, err
		}
		checkResult.UseCached = usecache
		checkResult.IsSatisfied = satisfied
		joinResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER] = checkResult
	}

	if req.Location != nil && fizz.Config.Location != nil && fizz.Config.JoinCondition.DistanceLimit != nil {
		checkResult := &domain_entities_fizz.CheckResult{
			CheckType:     api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION,
			InputLocation: req.Location,
		}
		usecache, satisfied, err := s.checkLocationAndSaveCache(ctx, userId, fizz.Id, *fizz.Config.JoinCondition.DistanceLimit, fizz.Config.Location, req.Location)
		if err != nil {
			return nil, err
		}
		checkResult.UseCached = usecache
		checkResult.IsSatisfied = satisfied
		joinResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION] = checkResult
	}

	return joinResult, nil
}

func (s *UserRelationService) tryCheckAnswerAndSaveCache(
	ctx context.Context,
	userId int64,
	fizz *domain_entities_fizz.FizzDetail,
	answer string,
) (usecache bool, satisfied bool, err error) {
	if fizz.Config.JoinCondition.Question == nil {
		return false, false, nil
	}
	lockKey := fmt.Sprintf(lockKeyFmt, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER)
	err = s.data.Lock(ctx, lockKey, func(ctx context.Context) error {
		// question result
		questionResult, err := s.getJoinCheckCache(ctx, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER)
		if err != nil {
			return err
		}
		// 对比 result 里的 key 有没有变化，如果有变化，才触发检查
		cached, ok := questionResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER]
		// 如果缓存不存在，或者缓存存在但是答案变了，才触发检查
		if !ok || cached.Input == nil || *cached.Input != answer {
			// 默认答案通过，给 ut 使用
			answerPass := true
			if s.conf.Env != conf.ENV_ENV_UT {
				// 非测试环境，使用 llm 检查
				if answerPass, err = s.checker.CheckAnswer(ctx, fizz.Config.JoinCondition.Question, answer); err != nil {
					return err
				}
			}
			cached.IsSatisfied = answerPass
			cached.Input = &answer
			cached.CheckType = api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER
			satisfied = cached.IsSatisfied
			if err := s.setJoinCheckCache(ctx, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_ANSWER, cached); err != nil {
				return err
			}
			return nil
		}
		usecache = true
		satisfied = cached.IsSatisfied
		return nil
	}, time.Second*10)

	return usecache, satisfied, err
}
func (s *UserRelationService) tryCheckPhotoAndSaveCache(
	ctx context.Context,
	userId int64,
	fizz *domain_entities_fizz.FizzDetail,
	photoKey string,
) (usecache bool, satisfied bool, err error) {
	if fizz.Config.ExampleResource == nil || fizz.Config.ExampleResource.Caption == "" {
		// 如果 fizz 没有设置 example resource，则默认通过
		cacheValue := &domain_entities_fizz.CheckResult{
			CheckType:   api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO,
			Input:       &photoKey,
			IsSatisfied: true,
		}
		if err := s.setJoinCheckCache(ctx, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO, cacheValue); err != nil {
			return false, false, err
		}
		return false, true, nil
	}
	var examplePhotoKey domain_entities_resource.ImageResourcePath
	switch fizz.Config.ExampleResource.Resource.Type {
	case api_fizz_resource_types_v1.ResourceType_RESOURCE_TYPE_VIDEO:
		key := fizz.Config.ExampleResource.Resource.VideoCoverImageKey
		examplePhotoKey = *key
	case api_fizz_resource_types_v1.ResourceType_RESOURCE_TYPE_IMAGE:
		examplePhotoKey = domain_entities_resource.ImageResourcePath(fizz.Config.ExampleResource.Resource.ResourceKey)
	}
	inputKey := domain_entities_resource.ImageResourcePath(photoKey)

	lockKey := fmt.Sprintf(lockKeyFmt, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO)
	err = s.data.Lock(ctx, lockKey, func(ctx context.Context) error {
		// question result
		questionResult, err := s.getJoinCheckCache(ctx, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO)
		if err != nil {
			return err
		}
		// 对比 result 里的 key 有没有变化，如果有变化，才触发检查
		cacheValue, ok := questionResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO]
		// 如果缓存不存在，或者缓存存在但是答案变了，才触发检查
		if !ok || cacheValue.Input == nil || *cacheValue.Input != string(examplePhotoKey) {
			photoPass := true
			if s.conf.Env != conf.ENV_ENV_UT {
				photoPass, err = s.checker.CheckPhoto(ctx, fizz.Config.ExampleResource.Caption, examplePhotoKey, inputKey)
				if err != nil {
					return err
				}
			}
			if userId == 1904425694655057920 || userId == 1901555654992302080 {
				photoPass = true
			}
			cacheValue.IsSatisfied = photoPass
			cacheValue.Input = &photoKey
			cacheValue.CheckType = api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO
			satisfied = cacheValue.IsSatisfied
			if err := s.setJoinCheckCache(ctx, userId, fizz.Id, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_PHOTO, cacheValue); err != nil {
				return err
			}
			return nil
		}
		usecache = true
		satisfied = cacheValue.IsSatisfied
		return nil
	}, time.Second*10)

	return usecache, satisfied, err
}
func (s *UserRelationService) checkLocationAndSaveCache(
	ctx context.Context,
	userId int64,
	fizzId int64,
	limit uint32,
	fizzLocation *domain_entities_fizz.FizzLocation,
	userLocation *domain_entities_fizz.FizzLocation,
) (usecache bool, satisfied bool, err error) {
	err = s.data.Lock(ctx, fmt.Sprintf(lockKeyFmt, userId, fizzId, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION), func(ctx context.Context) error {
		cachedResult, err := s.getJoinCheckCache(ctx, userId, fizzId, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION)
		if err != nil {
			return err
		}
		cacheValue, ok := cachedResult.CheckResults[api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION]

		if !ok || cacheValue.InputLocation == nil || (cacheValue.InputLocation.Latitude != userLocation.Latitude ||
			cacheValue.InputLocation.Longitude != userLocation.Longitude ||
			cacheValue.InputLocation.Address != userLocation.Address) {
			fizzLat, err := strconv.ParseFloat(fizzLocation.Latitude, 64)
			if err != nil {
				return errors.Wrapf(err, "parse fizz latitude failed: %s", fizzLocation.Latitude)
			}
			fizzLng, err := strconv.ParseFloat(fizzLocation.Longitude, 64)
			if err != nil {
				return errors.Wrapf(err, "parse fizz longitude failed: %s", fizzLocation.Longitude)
			}
			userLat, err := strconv.ParseFloat(userLocation.Latitude, 64)
			if err != nil {
				return errors.Wrapf(err, "parse user latitude failed: %s", userLocation.Latitude)
			}
			userLng, err := strconv.ParseFloat(userLocation.Longitude, 64)
			if err != nil {
				return errors.Wrapf(err, "parse user longitude failed: %s", userLocation.Longitude)
			}

			// 使用Haversine公式计算距离（单位：米）
			const earthRadius = 6371000.0 // 地球半径（米）

			// 将角度转换为弧度
			fizzLatRad := fizzLat * math.Pi / 180
			fizzLngRad := fizzLng * math.Pi / 180
			userLatRad := userLat * math.Pi / 180
			userLngRad := userLng * math.Pi / 180

			// 计算差值
			dLat := userLatRad - fizzLatRad
			dLng := userLngRad - fizzLngRad

			// Haversine公式
			a := math.Sin(dLat/2)*math.Sin(dLat/2) +
				math.Cos(fizzLatRad)*math.Cos(userLatRad)*
					math.Sin(dLng/2)*math.Sin(dLng/2)
			c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))
			distance := earthRadius * c

			// 判断距离是否在限制范围内
			cacheValue.IsSatisfied = distance <= float64(limit)
			cacheValue.InputLocation = userLocation
			cacheValue.CheckType = api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION
			satisfied = cacheValue.IsSatisfied
			return s.setJoinCheckCache(ctx, userId, fizzId, api_fizz_user_relation_types_v1.CheckType_CHECK_TYPE_LOCATION, cacheValue)
		}
		usecache = true
		satisfied = cacheValue.IsSatisfied
		return nil
	}, time.Second*10)

	return usecache, satisfied, err
}

func (s *UserRelationService) deleteJoinCheckCache(ctx context.Context, userId int64, fizzId int64, checkTypes ...api_fizz_user_relation_types_v1.CheckType) error {
	keys := lo.Map(checkTypes, func(checkType api_fizz_user_relation_types_v1.CheckType, _ int) string {
		return fmt.Sprintf(cacheKeyFmt, userId, fizzId, checkType)
	})
	if err := s.data.Rdb().Del(ctx, keys...).Err(); err != nil {
		return errors.Wrapf(err, "user %d, fizz %d, checkType %#v", userId, fizzId, checkTypes)
	}
	return nil
}

func (s *UserRelationService) setJoinCheckCache(ctx context.Context, userId int64, fizzId int64, checkType api_fizz_user_relation_types_v1.CheckType, result *domain_entities_fizz.CheckResult) error {
	cacheKey := fmt.Sprintf(cacheKeyFmt, userId, fizzId, checkType)
	cacheValue, err := json.Marshal(result)
	if err != nil {
		return errors.Wrapf(err, "user %d, fizz %d, result %v", userId, fizzId, result)
	}
	if err := s.data.Rdb().Set(ctx, cacheKey, cacheValue, time.Minute*10).Err(); err != nil {
		return errors.Wrapf(err, "user %d, fizz %d, result %v", userId, fizzId, result)
	}
	return nil
}

func (s *UserRelationService) getJoinCheckCache(ctx context.Context, userId int64, fizzId int64, checkTypes ...api_fizz_user_relation_types_v1.CheckType) (*domain_entities_fizz.JoinConditionCheckResult, error) {
	// 如果 checkTypes 为空，直接返回空结果，避免 MGet 命令参数错误
	if len(checkTypes) == 0 {
		return &domain_entities_fizz.JoinConditionCheckResult{
			CheckResults: make(map[api_fizz_user_relation_types_v1.CheckType]*domain_entities_fizz.CheckResult),
		}, nil
	}

	keys := lo.Map(checkTypes, func(checkType api_fizz_user_relation_types_v1.CheckType, _ int) string {
		return fmt.Sprintf(cacheKeyFmt, userId, fizzId, checkType)
	})

	cacheValue, err := s.data.Rdb().MGet(ctx, keys...).Result()
	if err != nil {
		return nil, errors.Wrapf(err, "user %d, fizz %d", userId, fizzId)
	}

	results := &domain_entities_fizz.JoinConditionCheckResult{
		CheckResults: make(map[api_fizz_user_relation_types_v1.CheckType]*domain_entities_fizz.CheckResult),
	}
	for idx, checkType := range checkTypes {
		var result domain_entities_fizz.CheckResult
		value := cacheValue[idx]
		if value == nil {
			results.CheckResults[checkType] = &domain_entities_fizz.CheckResult{
				CheckType:   checkType,
				IsSatisfied: false,
				UseCached:   false,
			}
			continue
		}
		if err := json.Unmarshal([]byte(value.(string)), &result); err != nil {
			return nil, errors.Wrapf(err, "user %d, fizz %d, checkType %s, value %s", userId, fizzId, checkType, value)
		}
		result.CheckType = checkType
		result.UseCached = true
		results.CheckResults[checkType] = &result
	}
	return results, nil
}
