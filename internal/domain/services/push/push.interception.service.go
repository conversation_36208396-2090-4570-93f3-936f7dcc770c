package domain_services_push

import (
	"context"
	"time"

	"github.com/pkg/errors"

	domain_entities_push "boson/internal/domain/entities/push"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_interfaces_push "boson/internal/domain/services/interfaces/push"
)

// PushInterceptionService 推送拦截服务
type PushInterceptionService struct {
	idGenerator                domain_interfaces.UniqIdGenerator
	pushInterceptionRepository domain_services_interfaces_push.IPushInterceptionRepository
}

// NewPushInterceptionService 创建推送拦截服务
func NewPushInterceptionService(
	idGenerator domain_interfaces.UniqIdGenerator,
	pushInterceptionRepository domain_services_interfaces_push.IPushInterceptionRepository,
) *PushInterceptionService {
	return &PushInterceptionService{
		idGenerator:                idGenerator,
		pushInterceptionRepository: pushInterceptionRepository,
	}
}

// ShouldInterceptPush 检查是否应该拦截推送
func (s *PushInterceptionService) ShouldInterceptPush(ctx context.Context, userID int64, pushType string, pushValue string) (bool, error) {
	attr := domain_entities_push.CheckPushInterceptionAttr{
		UserID:      userID,
		PushType:    pushType,
		PushValue:   pushValue,
		WithinHours: 24, // 24小时内
	}

	exists, err := s.pushInterceptionRepository.CheckPushInterceptionExists(ctx, attr)
	if err != nil {
		return false, errors.Wrapf(err, "userID: %d, pushType: %s, pushValue: %s", userID, pushType, pushValue)
	}

	return exists, nil
}

// RecordPushInterception 记录推送拦截
func (s *PushInterceptionService) RecordPushInterception(ctx context.Context, userID int64, pushType string, pushValue string) error {
	attr := domain_entities_push.CreatePushInterceptionAttr{
		ID:        s.idGenerator.Generate(),
		UserID:    userID,
		PushType:  pushType,
		PushValue: pushValue,
		CreatedAt: time.Now(),
	}

	err := s.pushInterceptionRepository.CreatePushInterception(ctx, attr)
	if err != nil {
		return errors.Wrapf(err, "userID: %d, pushType: %s, pushValue: %s", userID, pushType, pushValue)
	}

	return nil
}
