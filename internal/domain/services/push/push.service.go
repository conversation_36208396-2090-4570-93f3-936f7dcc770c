package domain_services_push

import (
	"context"

	"github.com/pkg/errors"

	api_push_types_v1 "boson/api/push/types/v1"
	driven_services_push_types "boson/internal/adapter/driving/services/push/types"
	"boson/internal/conf"
	domain_interfaces "boson/internal/domain/interfaces"
	domain_services_interfaces_push "boson/internal/domain/services/interfaces/push"
)

type CreatePushTokenAttr struct {
	ID        int64
	UserID    int64 // 可能为0，表示未登录用户
	DeviceID  string
	Token     string
	TokenType string
}

type PushTokenRepository interface {
	CreatePushToken(ctx context.Context, attr CreatePushTokenAttr) error
	BatchGetPushToken(ctx context.Context, userIDs []int64) (map[int64]string, error)
}

type PushService struct {
	conf                    *conf.Bootstrap
	idGenerator             domain_interfaces.UniqIdGenerator
	pushTokenRepository     PushTokenRepository
	pushService             domain_interfaces.IPushService
	pushInterceptionService domain_services_interfaces_push.IPushInterceptionService
}

func NewPushService(
	conf *conf.Bootstrap,
	idGenerator domain_interfaces.UniqIdGenerator,
	pushTokenRepository PushTokenRepository,
	pushService domain_interfaces.IPushService,
	pushInterceptionService domain_services_interfaces_push.IPushInterceptionService,
) *PushService {
	return &PushService{
		conf:                    conf,
		idGenerator:             idGenerator,
		pushTokenRepository:     pushTokenRepository,
		pushService:             pushService,
		pushInterceptionService: pushInterceptionService,
	}
}

func (s *PushService) RegisterToken(ctx context.Context, userID int64, deviceID string, token string, tokenType api_push_types_v1.PushTokenType) error {
	attr := CreatePushTokenAttr{
		ID:        s.idGenerator.Generate(),
		UserID:    userID,
		DeviceID:  deviceID,
		Token:     token,
		TokenType: tokenType.String(),
	}
	return s.pushTokenRepository.CreatePushToken(ctx, attr)
}

func (s *PushService) Push(
	ctx context.Context,
	userIDs []int64,
	title string,
	content string,
	imageURL string,
	jumpScheme string,
) error {
	if s.conf.Env == conf.ENV_ENV_UT {
		return nil
	}

	tokensMap, err := s.pushTokenRepository.BatchGetPushToken(ctx, userIDs)
	if err != nil {
		return err
	}
	tokens := []string{}
	for _, token := range tokensMap {
		tokens = append(tokens, token)
	}
	if len(tokens) == 0 {
		return errors.New("no push token")
	}

	pushMessage := &driven_services_push_types.PushMessage{
		Title:      title,
		Content:    content,
		JumpScheme: jumpScheme,
	}
	if imageURL != "" {
		pushMessage.ImageUrl = &imageURL
	}
	if len(tokens) == 1 {
		_, err := s.pushService.SendToDevice(ctx, tokens[0], pushMessage)
		return err
	}
	_, err = s.pushService.SendToMultipleDevices(ctx, tokens, pushMessage)
	return err
}

// PushWithInterception 带拦截功能的推送方法
func (s *PushService) PushWithInterception(
	ctx context.Context,
	userIDs []int64,
	title string,
	content string,
	imageURL string,
	jumpScheme string,
	pushType string,
	pushValue string,
) error {
	// 过滤需要拦截的用户
	filteredUserIDs := make([]int64, 0, len(userIDs))

	for _, userID := range userIDs {
		// 检查是否应该拦截推送
		shouldIntercept, err := s.pushInterceptionService.ShouldInterceptPush(ctx, userID, pushType, pushValue)
		if err != nil {
			// 记录错误但继续处理其他用户
			continue
		}

		if !shouldIntercept {
			filteredUserIDs = append(filteredUserIDs, userID)
		}
	}

	// 如果所有用户都被拦截，直接返回
	if len(filteredUserIDs) == 0 {
		return nil
	}

	// 发送推送给未被拦截的用户
	err := s.Push(ctx, filteredUserIDs, title, content, imageURL, jumpScheme)
	if err != nil {
		return err
	}

	// 为所有接收推送的用户记录拦截记录
	for _, userID := range filteredUserIDs {
		recordErr := s.pushInterceptionService.RecordPushInterception(ctx, userID, pushType, pushValue)
		if recordErr != nil {
			// 记录错误但不影响推送结果
			continue
		}
	}

	return nil
}
