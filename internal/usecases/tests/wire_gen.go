// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package usecasestest

import (
	"boson/internal/adapter/driving/repos/app_settings"
	"boson/internal/adapter/driving/repos/fizz"
	"boson/internal/adapter/driving/repos/fizz/play_room"
	"boson/internal/adapter/driving/repos/fizz/vibe"
	"boson/internal/adapter/driving/repos/items"
	"boson/internal/adapter/driving/repos/items/story"
	"boson/internal/adapter/driving/repos/push"
	"boson/internal/adapter/driving/repos/search"
	"boson/internal/adapter/driving/repos/users/binds"
	"boson/internal/adapter/driving/repos/users/boo"
	"boson/internal/adapter/driving/repos/users/device"
	"boson/internal/adapter/driving/repos/users/highlights"
	"boson/internal/adapter/driving/repos/users/im"
	"boson/internal/adapter/driving/repos/users/info"
	"boson/internal/adapter/driving/repos/users/notifications"
	"boson/internal/adapter/driving/repos/users/relations"
	"boson/internal/adapter/driving/services/agora"
	"boson/internal/adapter/driving/services/agora_rtc"
	driving_services_agora2 "boson/internal/adapter/driving/services/agora_rtm"
	"boson/internal/adapter/driving/services/cdn"
	"boson/internal/adapter/driving/services/cdn/s3"
	"boson/internal/adapter/driving/services/kling"
	"boson/internal/adapter/driving/services/openai"
	"boson/internal/adapter/driving/services/openrouter"
	"boson/internal/adapter/driving/services/polly"
	"boson/internal/adapter/driving/services/push/google_firebase"
	"boson/internal/adapter/driving/services/recommend"
	"boson/internal/adapter/driving/services/sam"
	"boson/internal/adapter/driving/services/search"
	"boson/internal/adapter/driving/services/third_party_auth"
	"boson/internal/adapter/driving/services/third_party_auth/apple"
	"boson/internal/adapter/driving/services/third_party_auth/facebook"
	"boson/internal/adapter/driving/services/third_party_auth/google"
	"boson/internal/adapter/driving/services/twillo"
	"boson/internal/adapter/driving/services/uniq_id_generator"
	"boson/internal/conf"
	"boson/internal/domain/services/ai"
	"boson/internal/domain/services/boo_world"
	"boson/internal/domain/services/fizz"
	"boson/internal/domain/services/fizz/peek"
	"boson/internal/domain/services/fizz/play_room/relay"
	"boson/internal/domain/services/fizz/play_room/rush"
	"boson/internal/domain/services/fizz/vibe"
	"boson/internal/domain/services/im"
	"boson/internal/domain/services/items"
	"boson/internal/domain/services/items/portal"
	"boson/internal/domain/services/items/story"
	"boson/internal/domain/services/push"
	"boson/internal/domain/services/rtm"
	"boson/internal/domain/services/search"
	"boson/internal/domain/services/users/auth"
	"boson/internal/domain/services/users/boo"
	"boson/internal/domain/services/users/bot"
	"boson/internal/domain/services/users/highlights"
	"boson/internal/domain/services/users/info"
	"boson/internal/domain/services/users/nofications"
	"boson/internal/domain/services/users/push"
	"boson/internal/domain/services/users/relations"
	"boson/internal/infra/data"
	"boson/internal/usecases/boo_world"
	"boson/internal/usecases/fizz"
	"boson/internal/usecases/items"
	"boson/internal/usecases/search"
	"boson/internal/usecases/users"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

func usecaseTestApp(conf2 *conf.Bootstrap, logger *log.Helper) (*TestApp, func(), error) {
	dataData, cleanup := data.NewData(conf2, logger)
	repo := adapter_driving_repos_app_settings.NewRepo(dataData)
	twillioClient := adapter_driving_services_twillo.NewTwillioClient(conf2)
	auth := adapter_driving_service_google_auth.NewAuth(conf2)
	adapter_driving_service_facebook_authAuth := adapter_driving_service_facebook_auth.NewAuth(conf2)
	appleAuthClient := adapter_driving_service_third_party_auth_apple.NewAppleAuthClient(conf2)
	provider := adapter_driving_service_third_party_auth.NewProvider(auth, adapter_driving_service_facebook_authAuth, appleAuthClient)
	adapter_driving_repos_users_relationsRepo := adapter_driving_repos_users_relations.NewRepo(dataData)
	queryRepo := adapter_driving_repos_users_binds.NewQueryRepo(dataData)
	adapter_driving_repos_users_infoQueryRepo := adapter_driving_repos_users_info.NewQueryRepo(dataData, adapter_driving_repos_users_relationsRepo, queryRepo)
	cmdRepo := adapter_driving_repos_users_info.NewCmdRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	imUuidRepo := adapter_driving_repos_users_im.NewImUuidRepo(dataData)
	client := driving_services_agora.NewClient(conf2, imUuidRepo, logger, adapter_driving_repos_users_infoQueryRepo)
	usersInfoService := domain_services_users_info.NewUsersInfoService(adapter_driving_repos_users_infoQueryRepo, cmdRepo, client)
	uniqIdGenerator := uniq_id_generator.NewUniqIdGenerator(dataData, logger)
	deviceRepo := adapter_driving_repos_users_device.NewDeviceRepo(dataData, adapter_driving_repos_users_infoQueryRepo, uniqIdGenerator)
	userVisitorService := domain_services_users_auth.NewUserVisitorService(deviceRepo)
	usersAuthService := domain_services_users_auth.NewUsersAuthService(twillioClient, provider, adapter_driving_repos_users_infoQueryRepo, usersInfoService, userVisitorService, client, dataData)
	userAuthUsecase := usecases_users.NewUserAuthUsecase(usersAuthService)
	driving_services_agoraClient := driving_services_agora2.NewClient(conf2)
	rtmService := domain_services_rtm.NewRtmService(driving_services_agoraClient)
	usersRtmUsecase := usecases_users.NewUsersRtmUsecase(rtmService)
	userPushService := domain_services_users_push.NewUserPushService(deviceRepo)
	userInfoUsecase := usecases_users.NewUserInfoUsecase(usersInfoService, userPushService)
	fizzTagRepo := adapter_driving_repos_fizz.NewFizzTagRepo(dataData)
	fizzUserRelationRepo := adapter_driving_repos_fizz.NewFizzUserRelationRepo(dataData)
	fizzTopRepo := adapter_driving_repos_fizz.NewFizzTopRepo(dataData, logger)
	adapter_driving_repos_fizzRepo := adapter_driving_repos_fizz.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo, fizzTagRepo, fizzUserRelationRepo, fizzTopRepo)
	fizzMediaUserRelationRepo := adapter_driving_repos_fizz.NewFizzMediaUserRelationRepo(dataData)
	fizzMediaQueryRepo := adapter_driving_repos_fizz.NewFizzMediaQueryRepo(dataData, adapter_driving_repos_users_infoQueryRepo, fizzMediaUserRelationRepo)
	fizzEventsRepo := adapter_driving_repos_fizz.NewFizzEventsRepo(dataData, fizzMediaQueryRepo)
	adapter_driving_repos_users_notificationsRepo := adapter_driving_repos_users_notifications.NewRepo(dataData, adapter_driving_repos_fizzRepo, fizzEventsRepo, adapter_driving_repos_users_infoQueryRepo)
	adapter_driving_repos_pushRepo := adapter_driving_repos_push.NewRepo(dataData)
	firebaseService := driving_services_push_firebase.NewFirebaseService(conf2, logger)
	pushInterceptionRepo := adapter_driving_repos_push.NewPushInterceptionRepo(dataData)
	pushInterceptionService := domain_services_push.NewPushInterceptionService(uniqIdGenerator, pushInterceptionRepo)
	pushService := domain_services_push.NewPushService(conf2, uniqIdGenerator, adapter_driving_repos_pushRepo, firebaseService, pushInterceptionService)
	adapter_driving_repos_users_booRepo := adapter_driving_repos_users_boo.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	userPortalRelationRepo := adapter_driving_repos_items_story.NewUserPortalRelationRepo(dataData)
	storyContextRepo := adapter_driving_repos_items_story.NewStoryContextRepo(dataData, userPortalRelationRepo)
	storyStatRepo := adapter_driving_repos_items_story.NewStoryStatRepo(dataData)
	storyReactionRepo := adapter_driving_repos_items_story.NewStoryReactionRepo(dataData, storyStatRepo)
	storyTopRepo := adapter_driving_repos_items_story.NewStoryTopRepo(dataData, logger)
	storyQueryRepo := adapter_driving_repos_items_story.NewStoryQueryRepo(dataData, adapter_driving_repos_users_relationsRepo, adapter_driving_repos_users_infoQueryRepo, adapter_driving_repos_users_booRepo, storyContextRepo, storyReactionRepo, storyTopRepo, storyStatRepo)
	storyTemplateRepo := adapter_driving_repos_items_story.NewStoryTemplateRepo(dataData, uniqIdGenerator, repo)
	adapter_driving_services_recommendClient := adapter_driving_services_recommend.NewClient(conf2)
	storyHauntBooShowInfoRepo := adapter_driving_repos_items_story.NewStoryHauntBooShowInfoRepo(dataData, storyQueryRepo)
	hauntBooShowInfoService := domain_services_items_story.NewHauntBooShowInfoService(storyHauntBooShowInfoRepo, dataData, dataData)
	storyQueryService := domain_services_items_story.NewStoryQueryService(storyQueryRepo, storyTemplateRepo, adapter_driving_services_recommendClient, usersInfoService, repo, hauntBooShowInfoService)
	usersNotificationsService := domain_services_users_notifications.NewUsersNotificationsService(uniqIdGenerator, adapter_driving_repos_users_notificationsRepo, usersInfoService, fizzUserRelationRepo, pushService, storyQueryService, logger)
	userRelationsService := domain_services_user_relations.NewUserRelationsService(dataData, adapter_driving_repos_users_relationsRepo, usersInfoService, usersNotificationsService, logger)
	userRelationUsecase := usecases_users.NewUserRelationUsecase(userRelationsService)
	itemMusicRepo := adapter_driving_repos_items.NewItemMusicRepo(dataData)
	itemsQueryRepo := adapter_driving_repos_items.NewItemsQueryRepo(dataData, adapter_driving_repos_users_infoQueryRepo, itemMusicRepo)
	itemCmdRepo := adapter_driving_repos_items.NewItemCmdRepo(dataData, itemsQueryRepo)
	itemCmdService := domain_services_items.NewItemCmdService(itemCmdRepo, uniqIdGenerator)
	itemCmdUsecase := usecases_items.NewItemCmdUsecase(itemCmdService)
	portalRepo := adapter_driving_repos_items_story.NewPortalCmdRepo(dataData, uniqIdGenerator, storyQueryRepo, adapter_driving_repos_users_infoQueryRepo, adapter_driving_repos_users_booRepo, dataData, userPortalRelationRepo, logger)
	storyCmdRepo := adapter_driving_repos_items_story.NewStoryCmdRepo(dataData, storyTemplateRepo, storyTopRepo, storyStatRepo, portalRepo, storyQueryRepo)
	service := s3.NewService(conf2)
	adapter_driving_services_cdnService := adapter_driving_services_cdn.NewService(service)
	openAIClient := adapter_driving_services_openai.NewOpenAIClient(adapter_driving_services_cdnService, logger)
	adapter_driving_services_openrouterClient := adapter_driving_services_openrouter.NewClient(conf2, logger)
	llmService := domain_services_ai.NewLlmService(conf2, logger, openAIClient, openAIClient, repo, adapter_driving_services_openrouterClient, dataData, adapter_driving_services_cdnService)
	pollySynthesizer := adapter_driving_services_polly.NewPollySynthesizer(conf2)
	ttsService := domain_services_ai.NewTtsService(pollySynthesizer, adapter_driving_services_cdnService, conf2, logger)
	imService := domain_services_im.NewIMService(client)
	asrService := domain_services_ai.NewAsrService(openAIClient, conf2)
	samClient := adapter_driving_services_sam.NewSamClient(logger, service)
	hideStickersRepo := adapter_driving_repos_items_story.NewHideStickersRepo(dataData)
	hideStickerService := domain_services_items_story.NewHideStickerService(hideStickersRepo)
	storyPlayService := domain_services_items_story.NewStoryPlayService(storyContextRepo, llmService, ttsService, storyQueryService, dataData, imService, asrService, usersInfoService, repo, adapter_driving_services_cdnService, samClient, hideStickerService, storyQueryRepo, adapter_driving_repos_users_booRepo, hauntBooShowInfoService, dataData, uniqIdGenerator)
	klingClient := adapter_driving_services_kling.NewKlingClient(logger, conf2)
	booAIService := domain_services_ai.NewBooAIService(conf2, logger, klingClient, adapter_driving_services_cdnService, openAIClient, openAIClient)
	storyCmdService := domain_services_items_story.NewStoryCmdService(uniqIdGenerator, storyCmdRepo, storyPlayService, storyQueryService, logger, dataData, hideStickerService, ttsService, repo, llmService, storyQueryRepo, adapter_driving_repos_users_booRepo, booAIService)
	storyReactionService := domain_services_items_story.NewStoryReactionService(usersInfoService, storyQueryService, storyReactionRepo)
	portalService := domain_services_items_portal.NewPortalService(uniqIdGenerator, portalRepo, storyQueryService, usersInfoService, adapter_driving_services_recommendClient, hauntBooShowInfoService, imService)
	itemStoryUsecase := usecases_items.NewItemStoryUsecase(storyCmdService, storyPlayService, storyQueryService, storyReactionService, hideStickerService, portalService, hauntBooShowInfoService)
	fizzMediaCmdRepo := adapter_driving_repos_fizz.NewFizzMediaCmdRepo(dataData)
	fizzQueryService := domain_services_fizz.NewFizzQueryService(adapter_driving_repos_fizzRepo, adapter_driving_services_recommendClient)
	fizzEventsFireService := domain_services_fizz.NewFizzEventsFireService(fizzEventsRepo, uniqIdGenerator, usersNotificationsService)
	fizzMediaService := domain_services_fizz.NewFizzMediaService(uniqIdGenerator, fizzMediaCmdRepo, fizzMediaQueryRepo, fizzQueryService, fizzEventsFireService)
	userRelationService := domain_services_fizz.NewUserRelationService(conf2, dataData, llmService, fizzUserRelationRepo, fizzQueryService, fizzMediaService, usersInfoService)
	fizzCmdService := domain_services_fizz.NewFizzCmdService(uniqIdGenerator, adapter_driving_repos_fizzRepo, fizzMediaService, fizzQueryService, userRelationService, llmService)
	fizzMediaMessageCmdRepo := adapter_driving_repos_fizz.NewFizzMediaMessageCmdRepo(dataData, fizzMediaCmdRepo)
	fizzMediaUserRelationInteractionService := domain_services_fizz.NewFizzMediaUserRelationInteractionService(dataData, fizzMediaQueryRepo, fizzMediaUserRelationRepo, fizzMediaMessageCmdRepo, usersInfoService, imService, conf2)
	fizzEventsQueryService := domain_services_fizz.NewFizzEventsQueryService(fizzEventsRepo, fizzMediaService)
	fizzPeekRepo := adapter_driving_repos_fizz.NewFizzPeekRepo(dataData, adapter_driving_repos_fizzRepo, adapter_driving_repos_users_infoQueryRepo)
	userBotService := domain_services_users_bot.NewUserBotService(userVisitorService, usersInfoService, cmdRepo)
	fizzPeekMatchingService := domain_services_fizz_peek.NewFizzPeekMatchingService(dataData, uniqIdGenerator, fizzPeekRepo, logger, dataData, pushService, fizzCmdService, userRelationService, userBotService, repo, usersInfoService)
	fizzPeekService := domain_services_fizz_peek.NewFizzPeekService(uniqIdGenerator, fizzPeekRepo, fizzPeekMatchingService, fizzQueryService)
	fizzRelayRepo := adapter_driving_repos_fizz_play_room.NewFizzRelayRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	driving_services_agora_rtcClient := driving_services_agora_rtc.NewClient(conf2)
	fizzPlayRoomRelayService := domain_services_fizz_play_room_relay.NewFizzPlayRoomRelayService(fizzRelayRepo, dataData, dataData, driving_services_agora_rtcClient, uniqIdGenerator, imService, rtmService, logger)
	fizzRushRepo := adapter_driving_repos_fizz_play_room.NewFizzRushRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	fizzPlayRoomRushService := domain_services_fizz_play_room_rush.NewFizzPlayRoomRushService(fizzRushRepo, dataData, dataData, driving_services_agora_rtcClient, uniqIdGenerator, imService, rtmService, logger)
	adapter_driving_repos_fizz_vibeRepo := adapter_driving_repos_fizz_vibe.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	vibeService := domain_services_fizz_vibe.NewVibeService(uniqIdGenerator, adapter_driving_repos_fizz_vibeRepo, adapter_driving_repos_fizz_vibeRepo)
	fizzUsecase := usecases_fizz.NewFizzUsecase(fizzCmdService, fizzQueryService, userRelationService, fizzMediaService, fizzMediaUserRelationInteractionService, fizzEventsFireService, fizzEventsQueryService, fizzPeekService, fizzPeekMatchingService, fizzPlayRoomRelayService, fizzPlayRoomRushService, vibeService)
	userHighlightsRepo := adapter_driving_repos_users_highlights.NewUserHighlightsRepo(dataData)
	userHighlightsService := domain_services_users_highlights.NewUserHighlightsService(userHighlightsRepo, uniqIdGenerator, fizzMediaService)
	userHighlightsUsecase := usecases_users.NewUserHighlightsUsecase(userHighlightsService)
	adapter_driving_services_searchClient := adapter_driving_services_search.NewClient(dataData)
	searchService := domain_services_search.NewSearchService(adapter_driving_services_searchClient, usersInfoService)
	searchHistoryRepo := adapter_driving_repos_search.NewSearchHistoryRepo(dataData)
	searchHistoryService := domain_services_search.NewSearchHistoryService(searchHistoryRepo)
	searchUsecase := usecases_search.NewSearchUsecase(searchService, searchHistoryService)
	userNotificationUsecase := usecases_users.NewUserNotificationUsecase(usersNotificationsService)
	booService := domain_services_users_boo.NewBooService(adapter_driving_repos_users_booRepo, dataData, dataData, uniqIdGenerator, imService, adapter_driving_repos_users_infoQueryRepo, rtmService, hideStickersRepo)
	jobRepo := adapter_driving_repos_users_boo.NewJobRepo(dataData, adapter_driving_repos_users_booRepo)
	configService := domain_services_users_boo.NewConfigService(repo)
	booJobServiceV2 := domain_services_users_boo.NewBooJobServiceV2(uniqIdGenerator, dataData, jobRepo, dataData, configService, rtmService, booService)
	handlerProvider := domain_services_users_boo.NewHandlerProvider(booAIService, booAIService, conf2)
	scheduler := domain_services_users_boo.NewDefaultSchedulerFromConfigService(jobRepo, configService, booJobServiceV2, handlerProvider, logger, dataData)
	booUsecase := usecases_users.NewBooUsecase(booService, booJobServiceV2, scheduler)
	booWorldResourceRepo := adapter_driving_repos_users_boo.NewBooWorldResourceRepo(dataData)
	booWorldMapRepo := adapter_driving_repos_users_boo.NewBooWorldMapRepo(dataData)
	booWorldMapService := domain_services_boo_world.NewBooWorldMapService(uniqIdGenerator, booWorldResourceRepo, booWorldMapRepo, dataData, booService, usersInfoService)
	booWorldCaptureRecordsRepo := adapter_driving_repos_users_boo.NewBooWorldCaptureRecordsRepo(dataData, booService, usersInfoService)
	booWorldCaptureService := domain_services_boo_world.NewBooWorldCaptureService(booWorldCaptureRecordsRepo, uniqIdGenerator, booService)
	booWorldUsecase := usecases_boo_world.NewBooWorldUsecase(booWorldMapService, booWorldCaptureService)
	testApp := NewTestApp(conf2, dataData, repo, userAuthUsecase, usersRtmUsecase, userInfoUsecase, userRelationUsecase, itemCmdUsecase, itemStoryUsecase, fizzUsecase, userHighlightsUsecase, searchUsecase, userNotificationUsecase, booUsecase, booWorldUsecase)
	return testApp, func() {
		cleanup()
	}, nil
}

// wire.go:

type TestApp struct {
	Data               *data.Data
	Conf               *conf.Bootstrap
	AppSettingStorage  adapter_driving_repos_app_settings.ConfigStorage
	AuthUc             *usecases_users.UserAuthUsecase
	UserRTMUc          *usecases_users.UsersRtmUsecase
	InfoUc             *usecases_users.UserInfoUsecase
	RelationUc         *usecases_users.UserRelationUsecase
	ItemUc             *usecases_items.ItemCmdUsecase
	StoryUc            *usecases_items.ItemStoryUsecase
	FizzUc             *usecases_fizz.FizzUsecase
	UserHighlightsUc   *usecases_users.UserHighlightsUsecase
	SearchUc           *usecases_search.SearchUsecase
	UserNotificationUc *usecases_users.UserNotificationUsecase
	BooUc              *usecases_users.BooUsecase
	BooWorldUc         *usecases_boo_world.BooWorldUsecase
}

func NewTestApp(conf2 *conf.Bootstrap, data2 *data.Data,
	appSettingStorage adapter_driving_repos_app_settings.ConfigStorage,
	authUc *usecases_users.UserAuthUsecase,
	userRTMUc *usecases_users.UsersRtmUsecase,
	infoUc *usecases_users.UserInfoUsecase,
	relationUc *usecases_users.UserRelationUsecase,
	itemUc *usecases_items.ItemCmdUsecase,
	storyUc *usecases_items.ItemStoryUsecase,
	fizzUc *usecases_fizz.FizzUsecase,
	userHighlightsUc *usecases_users.UserHighlightsUsecase,
	searchUc *usecases_search.SearchUsecase,
	userNotificationUc *usecases_users.UserNotificationUsecase,
	booUc *usecases_users.BooUsecase,
	booWorldUc *usecases_boo_world.BooWorldUsecase,
) *TestApp {
	return &TestApp{
		Data:               data2,
		Conf:               conf2,
		AppSettingStorage:  appSettingStorage,
		AuthUc:             authUc,
		UserRTMUc:          userRTMUc,
		InfoUc:             infoUc,
		RelationUc:         relationUc,
		ItemUc:             itemUc,
		StoryUc:            storyUc,
		FizzUc:             fizzUc,
		UserHighlightsUc:   userHighlightsUc,
		SearchUc:           searchUc,
		UserNotificationUc: userNotificationUc,
		BooUc:              booUc,
		BooWorldUc:         booWorldUc,
	}
}
