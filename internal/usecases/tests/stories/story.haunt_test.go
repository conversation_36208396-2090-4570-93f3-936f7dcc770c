package tests_stories

import (
	api_common_v1 "boson/api/common/v1"
	api_items_portal_moment_types_v1 "boson/api/items/portal/moments/types/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	driven_http_types "boson/internal/adapter/driven/http/types"
	adapter_driving_repos_app_settings "boson/internal/adapter/driving/repos/app_settings"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_story "boson/internal/domain/services/items/story"
	usecasestest "boson/internal/usecases/tests"
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestHauntBooShowInfo(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()
	ctx := context.Background()
	type FeedConfig struct {
		MinV2Version driven_http_types.AppVersion `json:"min_v2_version"`
	}
	cfg := FeedConfig{
		MinV2Version: driven_http_types.AppVersion("1.0.0"),
	}
	adapter_driving_repos_app_settings.Save(ctx, adapter_driving_repos_app_settings.StoryFeedVersion, cfg, app.AppSettingStorage)

	userA, err := app.AuthUc.GenerateVisitor(ctx)
	if err != nil {
		t.Fatal(err)
	}

	// 创建一个测试头像
	avatars, err := app.BooUc.CreateAvatars(ctx, userA.Summary.ID, 1, []domain_entities_resource.ImageResourcePath{
		"test.png",
	})
	if err != nil {
		t.Fatal(err)
	}
	avatar := avatars[0]

	createdStory, err := app.StoryUc.CreateHauntStory(ctx, userA.Summary.ID, domain_services_items_story.CreateHauntStoryAttr{
		PlayConfig: &domain_entities_items.HauntStoryConfig{
			CreatorBooWithQuestionsAndAnswers: &domain_entities_items.HauntBoo{
				CreatorId: userA.Summary.ID,
				AvatarId:  avatar.Id,
				VideoKey:  "test.mp4",
				QuestionsWithAnswers: []*domain_entities_items.HauntQuestionWithAnswer{
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test",
							Description: "test",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test2",
							Description: "test2",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test3",
							Description: "test3",
							IsRequired:  false,
						},
						Answer: "ok",
					},
				},
			},
		},
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userA.Summary.ID,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.EqualValues(t, api_items_story_types_v1.StoryStatus_STORY_STATUS_GENERATING, createdStory.Summary.Status)
	time.Sleep(10 * time.Second)

	// user B 消费失败
	userB, err := app.AuthUc.GenerateVisitor(ctx)
	if err != nil {
		t.Fatal(err)
	}

	// 非 feed 接入，污染 feed
	storyAfterUnlocked, err := app.StoryUc.ConsumeHauntStory(ctx, userB.Summary.ID, createdStory.Summary.Id, false, false)
	if err != nil {
		t.Fatal(err)
	}
	assert.False(t, storyAfterUnlocked.HauntPlayContext.Unlocked)
	assert.EqualValues(t, 1, storyAfterUnlocked.HauntPlayContext.TryCount)

	// user B 能拉取到 show info 了
	showInfo, err := app.StoryUc.GetUserHauntBooShowInfo(ctx, userB.Summary.ID, api_items_story_types_v1.HauntBooShowInfoSence_SHOW_INFO_SENCE_FEED_POLLUTION, nil)
	if err != nil {
		t.Fatal(err)
	}

	assert.NotEmpty(t, showInfo.CreatorBoo)

	// userB 再次消费失败，但是要从 feed 进入
	storyAfterUnlocked, err = app.StoryUc.ConsumeHauntStory(ctx, userB.Summary.ID, createdStory.Summary.Id, false, true)
	if err != nil {
		t.Fatal(err)
	}
	assert.False(t, storyAfterUnlocked.HauntPlayContext.Unlocked)
	assert.EqualValues(t, 2, storyAfterUnlocked.HauntPlayContext.TryCount)

	// userB 发布内容
	// 创建一个测试头像
	userBavatars, err := app.BooUc.CreateAvatars(ctx, userB.Summary.ID, 1, []domain_entities_resource.ImageResourcePath{
		"testUserB.png",
	})
	if err != nil {
		t.Fatal(err)
	}
	userBavatar := userBavatars[0]

	userBcreatedStory, err := app.StoryUc.CreateHauntStory(ctx, userB.Summary.ID, domain_services_items_story.CreateHauntStoryAttr{
		PlayConfig: &domain_entities_items.HauntStoryConfig{
			CreatorBooWithQuestionsAndAnswers: &domain_entities_items.HauntBoo{
				CreatorId: userB.Summary.ID,
				AvatarId:  userBavatar.Id,
				VideoKey:  "test.mp4",
				QuestionsWithAnswers: []*domain_entities_items.HauntQuestionWithAnswer{
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test",
							Description: "test",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test2",
							Description: "test2",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test3",
							Description: "test3",
							IsRequired:  false,
						},
						Answer: "ok",
					},
				},
			},
		},
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userB.Summary.ID,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.EqualValues(t, api_items_story_types_v1.StoryStatus_STORY_STATUS_GENERATING, userBcreatedStory.Summary.Status)
	time.Sleep(10 * time.Second)

	// 此时，首页能刷到内容被污染了
	stories, _, _, _, err := app.StoryUc.ListHomePageStory(ctx, userA.Summary.ID, &api_common_v1.ListRequest{
		PageSize: "10",
	}, false, []api_items_story_types_v1.StoryPlayType{
		api_items_story_types_v1.StoryPlayType_STORY_PLAY_TYPE_HAUNT,
	})
	if err != nil {
		t.Fatal(err)
	}
	story := stories[0]
	assert.NotNil(t, story.HauntShowInfo)
}

func TestHauntStory(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()
	ctx := context.Background()

	userA, err := app.AuthUc.GenerateVisitor(ctx)
	if err != nil {
		t.Fatal(err)
	}

	// 创建一个测试头像
	avatars, err := app.BooUc.CreateAvatars(ctx, userA.Summary.ID, 1, []domain_entities_resource.ImageResourcePath{
		"test.png",
	})
	if err != nil {
		t.Fatal(err)
	}
	avatar := avatars[0]

	createdStory, err := app.StoryUc.CreateHauntStory(ctx, userA.Summary.ID, domain_services_items_story.CreateHauntStoryAttr{
		PlayConfig: &domain_entities_items.HauntStoryConfig{
			CreatorBooWithQuestionsAndAnswers: &domain_entities_items.HauntBoo{
				CreatorId: userA.Summary.ID,
				AvatarId:  avatar.Id,
				VideoKey:  "test.mp4",
				QuestionsWithAnswers: []*domain_entities_items.HauntQuestionWithAnswer{
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test",
							Description: "test",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test2",
							Description: "test2",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test3",
							Description: "test3",
							IsRequired:  false,
						},
						Answer: "ok",
					},
				},
			},
		},
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userA.Summary.ID,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	assert.EqualValues(t, api_items_story_types_v1.StoryStatus_STORY_STATUS_GENERATING, createdStory.Summary.Status)
	time.Sleep(10 * time.Second)

	story, err := app.StoryUc.GetStoryDetail(ctx, userA.Summary.ID, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}

	assert.EqualValues(t, api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED, story.Summary.Status)
	assert.NotEmpty(t, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Hint)
	assert.NotEmpty(t, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.VideoKey)
	assert.NotEmpty(t, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar)
	assert.NotEmpty(t, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar.ObjectPath)

	// user b 收藏 sticker
	userB, err := app.AuthUc.GenerateVisitor(ctx)
	if err != nil {
		t.Fatal(err)
	}
	sticker, err := app.StoryUc.AddCapturedBooInToCollectedStickers(ctx, userB.Summary.ID, story.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.True(t, sticker.Collected)

	// 获取收藏列表内包含
	_, collectedStickers, err := app.StoryUc.ListMyCollectedStickers(ctx, userB.Summary.ID, &api_common_v1.ListRequest{
		PageSize: "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(collectedStickers))
	assert.Equal(t, sticker.Id, collectedStickers[0].Sticker.Id)
	// 测试转 dto
	stickerDto := adapter_driven_assembler.ConvertHideStoryStickerToDto(sticker)
	assert.NotEmpty(t, stickerDto.Resource)

	// user B 把 user A 的 Story 解锁掉
	storyAfterUnlocked, err := app.StoryUc.ConsumeHauntStory(ctx, userB.Summary.ID, createdStory.Summary.Id, true, true)
	if err != nil {
		t.Fatal(err)
	}
	assert.True(t, storyAfterUnlocked.HauntPlayContext.Unlocked)

	// 再获取一次
	storyAfterUnlocked, err = app.StoryUc.GetStoryDetail(ctx, userB.Summary.ID, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.True(t, storyAfterUnlocked.HauntPlayContext.Unlocked)
	assert.Greater(t, storyAfterUnlocked.JoinedPortalId, int64(0))

	// userB 有这个 portal 了，且有 moment & 类型为
	portals, _, _, err := app.StoryUc.PortalService.ListMyPortals(ctx, userB.Summary.ID, &api_common_v1.ListRequest{
		PageSize: "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(portals))
	assert.Equal(t, 1, len(portals[0].Moments))
	assert.Equal(t, api_items_portal_moment_types_v1.MomentType_MOMENT_TYPE_HAUNT, portals[0].Moments[0].Type)

	// userB 把 user A 的 boo 加入到助手内
	if err := app.StoryUc.AddCaptureBooIntoMyAssist(ctx, userB.Summary.ID, createdStory.Summary.Id, storyAfterUnlocked.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id); err != nil {
		t.Fatal(err)
	}

	// 拉取正常
	boos, lisp, err := app.StoryUc.ListHauntBooAssist(ctx, userB.Summary.ID, &api_common_v1.ListRequest{
		PageSize: "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(boos))
	assert.False(t, lisp.HasMore)

	// UserB 创建头像，把 UserA 的鬼作为助手加入
	userBAvatars, err := app.BooUc.CreateAvatars(ctx, userB.Summary.ID, 2, []domain_entities_resource.ImageResourcePath{
		"test2.png",
	})
	if err != nil {
		t.Fatal(err)
	}
	userBAvatar := userBAvatars[0]

	userBcreatedStory, err := app.StoryUc.CreateHauntStory(ctx, userB.Summary.ID, domain_services_items_story.CreateHauntStoryAttr{
		PlayConfig: &domain_entities_items.HauntStoryConfig{
			AssitBoosWithQuestionsAndAnswers: []*domain_entities_items.HauntBoo{
				{
					Id: storyAfterUnlocked.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id,
				},
			},
			CreatorBooWithQuestionsAndAnswers: &domain_entities_items.HauntBoo{
				CreatorId: userB.Summary.ID,
				AvatarId:  userBAvatar.Id,
				VideoKey:  "test.mp4",
				QuestionsWithAnswers: []*domain_entities_items.HauntQuestionWithAnswer{
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test",
							Description: "test",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test2",
							Description: "test2",
							IsRequired:  false,
						},
						Answer: "ok",
					},
					{
						Question: &domain_entities_items.HauntQuestion{
							Title:       "test3",
							Description: "test3",
							IsRequired:  false,
						},
						Answer: "ok",
					},
				},
			},
		},
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userB.Summary.ID,
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, api_items_story_types_v1.StoryStatus_STORY_STATUS_GENERATING, userBcreatedStory.Summary.Status)
	// 等待生成
	time.Sleep(10 * time.Second)
	userBcreatedStory, err = app.StoryUc.GetStoryDetail(ctx, userB.Summary.ID, userBcreatedStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, api_items_story_types_v1.StoryStatus_STORY_STATUS_PUBLISHED, userBcreatedStory.Summary.Status)
	assert.NotEmpty(t, userBcreatedStory.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Hint)
	// 确定有助手鬼
	assert.Equal(t, 1, len(userBcreatedStory.Summary.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers))
	assitBoo := userBcreatedStory.Summary.HauntPlayConfig.AssitBoosWithQuestionsAndAnswers[0]
	assert.Equal(t, storyAfterUnlocked.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Id, assitBoo.Id)
	assert.Equal(t, storyAfterUnlocked.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar.Id, assitBoo.Avatar.Id)
	assert.Equal(t, storyAfterUnlocked.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar.ObjectPath, assitBoo.Avatar.ObjectPath)
	assert.Equal(t, storyAfterUnlocked.Summary.HauntPlayConfig.CreatorBooWithQuestionsAndAnswers.Avatar.ObjectPath, assitBoo.Avatar.ObjectPath)

	// 再拉取助手，应该已经没有了
	boos, lisp, err = app.StoryUc.ListHauntBooAssist(ctx, userB.Summary.ID, &api_common_v1.ListRequest{
		PageSize: "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 0, len(boos))
	assert.False(t, lisp.HasMore)
}
