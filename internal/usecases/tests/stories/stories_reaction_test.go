package tests_stories

import (
	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	domain_entities_users "boson/internal/domain/entities/users"
	usecasestest "boson/internal/usecases/tests"
	"context"
	"testing"

	"github.com/samber/lo"
	"github.com/test-go/testify/assert"
	"golang.org/x/sync/errgroup"
)

func TestListActivities(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 story
	story, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 reaction
	_, err = app.StoryUc.CreateReaction(
		context.Background(),
		userA.Summary.ID,
		story.Summary.Id,
		"🔥",
		"test",
	)
	if err != nil {
		t.Fatal(err)
	}

	// 并发
	var eg errgroup.Group
	parallel := 50
	usersChan := make(chan *domain_entities_users.UserDetailEntity, parallel)
	for i := 0; i < parallel; i++ {
		eg.Go(func() error {
			user, err := app.AuthUc.GenerateVisitor(context.Background())
			if err != nil {
				return err
			}
			usersChan <- user
			_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "🔥", "test")
			if err != nil {
				return err
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		t.Fatal(err)
	}

	activities, resp, err := app.StoryUc.ListActivities(
		context.Background(),
		userA.Summary.ID,
		&api_common_v1.ListRequest{
			PageSize: "20",
		},
	)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 20, len(activities))
	assert.Equal(t, true, resp.HasMore)

	activities, resp, err = app.StoryUc.ListActivities(
		context.Background(),
		userA.Summary.ID,
		&api_common_v1.ListRequest{
			PageToken: resp.NextPageToken,
			PageSize:  "40",
		},
	)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 30, len(activities)) // 创建了50个，前20个被分页了，这里应该是30个;
	assert.Equal(t, false, resp.HasMore)
}

func TestGetActivityUnreadCount(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	// 创建一个 story
	story, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 reaction
	_, err = app.StoryUc.CreateReaction(
		context.Background(),
		userB.Summary.ID,
		story.Summary.Id,
		"🔥",
		"test",
	)
	if err != nil {
		t.Fatal(err)
	}

	count, err := app.StoryUc.GetActivityUnreadCount(context.Background(), userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, int64(1), count)

	// 创建一个 reaction
	_, err = app.StoryUc.CreateReaction(
		context.Background(),
		userA.Summary.ID,
		story.Summary.Id,
		"🔥",
		"test",
	)
	if err != nil {
		t.Fatal(err)
	}
	count, err = app.StoryUc.GetActivityUnreadCount(context.Background(), userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, int64(1), count)
}

func TestMarkActivitiesAsRead(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 story
	story, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 reaction
	_, err = app.StoryUc.CreateReaction(
		context.Background(),
		userB.Summary.ID,
		story.Summary.Id,
		"🔥",
		"test",
	)
	if err != nil {
		t.Fatal(err)
	}
	// 校验未读数应为 1
	count, err := app.StoryUc.GetActivityUnreadCount(context.Background(), userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, 1, count)
	// 标记为已读
	err = app.StoryUc.MarkActivitiesAsRead(
		context.Background(),
		userA.Summary.ID,
	)
	if err != nil {
		t.Fatal(err)
	}
	// 校验未读数应为 0
	count, err = app.StoryUc.GetActivityUnreadCount(context.Background(), userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, 0, count)
}

func TestCreateReaction(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 story
	story, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	// 创建一个 reaction
	storyAfterReaction, err := app.StoryUc.CreateReaction(
		context.Background(),
		userA.Summary.ID,
		story.Summary.Id,
		"👍",
		"test",
	)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, storyAfterReaction.Summary.LoginUserMadeReactions[0].EmojiStr, "👍")
	// 统计数据正确
	assert.EqualValues(t, 1, storyAfterReaction.Summary.Stats.ReactionEmojiStats["👍"])

	// 并发
	var eg errgroup.Group
	parallel := 50
	usersChan := make(chan *domain_entities_users.UserDetailEntity, parallel)
	for i := 0; i < parallel; i++ {
		eg.Go(func() error {
			user, err := app.AuthUc.GenerateVisitor(context.Background())
			if err != nil {
				return err
			}
			usersChan <- user
			_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "👍", "test")
			if err != nil {
				return err
			}
			storyAfterReaction, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "👍", "test")
			if err != nil {
				return err
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		t.Fatal(err)
	}
	close(usersChan)
	for user := range usersChan {
		story, err := app.StoryUc.GetStoryDetail(context.Background(), user.Summary.ID, story.Summary.Id)
		if err != nil {
			t.Fatal(err)
		}
		assert.Equal(t, story.Summary.LoginUserMadeReactions[0].EmojiStr, "👍")
		assert.EqualValues(t, parallel+1, story.Summary.Stats.ReactionEmojiStats["👍"])
	}

	// 单用户同一个 emoji 并发
	story, err = usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	if err != nil {
		t.Fatal(err)
	}
	user, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	for i := 0; i < 10; i++ {
		eg.Go(func() error {
			_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "😯", "test")
			if err != nil {
				return err
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		t.Fatal(err)
	}
	story, err = app.StoryUc.GetStoryDetail(context.Background(), user.Summary.ID, story.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.Len(t, story.Summary.LoginUserMadeReactions, 1)
	assert.True(t, story.Summary.IsLoginUserMadeReactionMade("😯"))
	assert.EqualValues(t, 1, story.Summary.Stats.ReactionEmojiStats["😯"])

	// 单用户不同的 emoji 并发
	story, err = usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	if err != nil {
		t.Fatal(err)
	}
	user, err = app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	emojis := []string{"😯", "👍", "👎", "🤔", "🤨", "🤯", "🤠", "🤡", "🤠", "🤡"}
	for _, emoji := range emojis {
		eg.Go(func() error {
			_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, emoji, "test")
			if err != nil {
				return err
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		t.Fatal(err)
	}
	story, err = app.StoryUc.GetStoryDetail(context.Background(), user.Summary.ID, story.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, len(story.Summary.LoginUserMadeReactions), len(lo.Uniq(emojis)))
	for _, emoji := range emojis {
		assert.True(t, story.Summary.IsLoginUserMadeReactionMade(emoji))
		assert.EqualValues(t, 1, story.Summary.Stats.ReactionEmojiStats[emoji])
	}

	// 错误的 emoji 应该返回错误
	_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "👍👍", "test")
	assert.True(t, api_errors_v1.IsErrorReasonBadRequestError(err))

	// 普通字符串应该返回错误
	_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "test", "test")
	assert.True(t, api_errors_v1.IsErrorReasonBadRequestError(err))

	// 空字符串应该返回错误
	_, err = app.StoryUc.CreateReaction(context.Background(), user.Summary.ID, story.Summary.Id, "", "test")
	assert.True(t, api_errors_v1.IsErrorReasonBadRequestError(err))
}
