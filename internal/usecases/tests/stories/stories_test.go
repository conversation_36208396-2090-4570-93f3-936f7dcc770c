package tests_stories

import (
	"context"
	"testing"

	api_common_v1 "boson/api/common/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_users_relations_types_v1 "boson/api/users/relations/types/v1"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_story "boson/internal/domain/services/items/story"
	usecasestest "boson/internal/usecases/tests"

	"github.com/samber/lo"
	"github.com/test-go/testify/assert"
)

func TestListSameAuthorStoryWithAnchor(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	var createdStories []*domain_entities_items.StoryDetail
	// 创建 20 个 createdStory
	for i := 0; i < 20; i++ {
		createdStory, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
		if err != nil {
			t.Fatal(err)
		}
		createdStories = append(createdStories, createdStory)
	}

	// 使用最中间的 story 作为锚点
	anchorStory := createdStories[len(createdStories)/2]

	// 正反向各拉取 5 个 createdStory
	storiesSet, err := app.StoryUc.ListSameAuthorStoryWithAnchor(
		context.Background(),
		userA.Summary.ID,
		anchorStory.Summary.Id,
		[]*domain_services_items_story.ListSameAuthorStoryWithAnchorAttr{
			{
				IdDesc: false,
				Limit:  5,
			},
			{
				IdDesc: true,
				Limit:  5,
			},
		},
	)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 2, len(storiesSet))
	assert.Equal(t, 5, len(storiesSet[0].Details))
	assert.Equal(t, 5, len(storiesSet[1].Details))

	for _, stories := range storiesSet {
		if stories.IdDesc {
			for idx, story := range stories.Details {
				if idx > 0 {
					// id 越来越小
					assert.True(t, story.Summary.Id < stories.Details[idx-1].Summary.Id)
				}
			}
		} else {
			for idx, story := range stories.Details {
				if idx > 0 {
					// id 越来越大
					assert.True(t, story.Summary.Id > stories.Details[idx-1].Summary.Id)
				}
			}
		}
	}
}

func TestRoastedStory(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	createdStory, err := app.StoryUc.CreateRoastedStory(context.Background(), userA.Summary.ID, domain_services_items_story.CreateRoastedStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userA.Summary.ID,
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CoverImage: domain_entities_items.StoryCoverImage{
				Path:   domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/8871303a9b6841de983d70e043cfac0a.jpeg"),
				Width:  428,
				Height: 570,
			},
		},
		PlayConfig: &domain_entities_items.StoryPlayRoastedConfig{
			Cover: domain_entities_resource.NewResourceBuilder().WithImageObjectKey(
				domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/8871303a9b6841de983d70e043cfac0a.jpeg"),
				428,
				570,
			).Build(),
			BaseResource: domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(
				domain_entities_resource.VideoResourcePath("flippop/video/item/story/1923251145282854912/202508220811/c520984273924467a9b33cc63e16d7c4.mp4"),
				domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/5a1d76039a1940538eaaafa92cb3fec6.jpeg"),
				1080,
				1440,
			).Build(),
			Topic: &domain_entities_items.StoryPlayRoastedTopic{
				Greeting: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     "开场白",
					TTSAudioKey: "test.mp3",
				},
				Ending: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     "结束语",
					TTSAudioKey: "test.mp3",
				},
				Questions: []*domain_entities_items.StoryPlayRoastedQuestion{
					{
						Question:    "问题1",
						Thinking:    "思考1",
						TTSAudioKey: "test.mp3",
					},
				},
			},
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, createdStory)
	assert.NotNil(t, createdStory.Summary.RoastedPlayConfig.BaseResource)
	assert.NotNil(t, createdStory.Summary.RoastedPlayConfig.Cover)

	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	userBCreatedStory, err := app.StoryUc.CreateRoastedStory(context.Background(), userB.Summary.ID, domain_services_items_story.CreateRoastedStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userB.Summary.ID,
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CoverImage: domain_entities_items.StoryCoverImage{
				Path:   domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/8871303a9b6841de983d70e043cfac0a.jpeg"),
				Width:  428,
				Height: 570,
			},
		},
		PlayConfig: &domain_entities_items.StoryPlayRoastedConfig{
			Cover: domain_entities_resource.NewResourceBuilder().WithImageObjectKey(
				domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/8871303a9b6841de983d70e043cfac0a.jpeg"),
				428,
				570,
			).Build(),
			BaseResource: domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(
				domain_entities_resource.VideoResourcePath("flippop/video/item/story/1923251145282854912/202508220811/c520984273924467a9b33cc63e16d7c4.mp4"),
				domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/5a1d76039a1940538eaaafa92cb3fec6.jpeg"),
				1080,
				1440,
			).Build(),
			Topic: &domain_entities_items.StoryPlayRoastedTopic{
				Greeting: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     "开场白",
					TTSAudioKey: "test.mp3",
				},
				Ending: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     "结束语",
					TTSAudioKey: "test.mp3",
				},
				Questions: []*domain_entities_items.StoryPlayRoastedQuestion{
					{
						Question:    "问题1",
						Thinking:    "思考1",
						TTSAudioKey: "test.mp3",
					},
				},
			},
			FromStoryID: &createdStory.Summary.Id,
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, userBCreatedStory)

	// 此时，B 拉取 A 的作品，是有 conext 的
	story, err := app.StoryUc.GetStoryDetail(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, story)
	assert.True(t, story.RoastedPlayContext.IsConsumed)
	assert.True(t, story.Summary.HasUnlocked)
	assert.EqualValues(t, 1, len(story.Summary.UnlockedUsersInfo.Users))
	assert.EqualValues(t, 1, story.Summary.UnlockedUsersInfo.Total)
}

func TestCreateBasePlayStory(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	createdStory, err := usecasestest.CreateBasePlayStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, createdStory)

	// 拉取 createdStory
	story, err := app.StoryUc.GetStoryDetail(context.Background(), createdStory.Summary.Id, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, story)
	assert.NotEmpty(t, story.Summary.BasePlayConfig)
	assert.NotEmpty(t, story.Summary.BasePlayConfig.Nodes)
	assert.NotEmpty(t, story.Summary.PlayBasePlayExample.CoverImageUrl)
}

func TestTemplateCreate(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	// A 创建 createdStory
	createdStory, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, createdStory)

	// A 拉取海龟汤模板，能拉到 diy
	templates, err := app.StoryUc.ListTurtleSoupStoryConditionTemplates(context.Background(), userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(templates))
	assert.Equal(t, api_items_story_types_v1.TemplateType_TEMPLATE_TYPE_USER_DIY, templates[0].Typ)

	// B 拉不到
	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	templates, err = app.StoryUc.ListTurtleSoupStoryConditionTemplates(context.Background(), userB.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 0, len(templates))
}

func TestCreateStory(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	// A 关注 B
	if _, err := app.RelationUc.SaveRelationToUser(
		context.Background(),
		userA.Summary.ID,
		api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING,
		userB.Summary.ID,
	); err != nil {
		t.Fatal(err)
	}

	// A 去拉 关注 story 列表，应该是空的
	stories, _, _, _, err := app.StoryUc.ListFollowingCreatorStory(context.Background(), userA.Summary.ID, &api_common_v1.ListRequest{
		PageSize:  "10",
		PageToken: "",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 0, len(stories))

	// B 创建 createdStory
	createdStory, err := usecasestest.CreateExchangeImageStory(context.Background(), app, userB.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, createdStory)
	// B 创建列表有值
	bCreatedStories, _, err := app.StoryUc.ListUserCreatedStory(context.Background(), userB.Summary.ID, userB.Summary.ID, &api_common_v1.ListRequest{
		PageSize:  "10",
		PageToken: "",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(bCreatedStories))

	// B 的统计信息
	userBInfo, err := app.InfoUc.BatchGetUserInfo(context.Background(), userB.Summary.ID, userB.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, 1, userBInfo[userB.Summary.ID].UserStat.TotalStoriesCount)

	// A 去拉 关注 story 列表，应该有 B 的 story
	stories, _, _, _, err = app.StoryUc.ListFollowingCreatorStory(context.Background(), userA.Summary.ID, &api_common_v1.ListRequest{
		PageSize:  "10",
		PageToken: "",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(stories))
	assert.Equal(t, createdStory.Summary.Id, stories[0].Summary.Id)

	// 取消关注后，数组变为空
	if _, err := app.RelationUc.RemoveRelationFromUser(
		context.Background(),
		userA.Summary.ID,
		api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING,
		userB.Summary.ID,
	); err != nil {
		t.Fatal(err)
	}
	stories, _, _, _, err = app.StoryUc.ListFollowingCreatorStory(context.Background(), userA.Summary.ID, &api_common_v1.ListRequest{
		PageSize:  "10",
		PageToken: "",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 0, len(stories))
}

func TestUpdateUserSubmittedResource(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}
	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	if _, err := app.RelationUc.SaveRelationToUser(
		context.Background(),
		userA.Summary.ID,
		api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING,
		userB.Summary.ID,
	); err != nil {
		t.Fatal(err)
	}

	createdStory, err := app.StoryUc.CreateRoastedStory(context.Background(), userA.Summary.ID, domain_services_items_story.CreateRoastedStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			CreatorID: userA.Summary.ID,
			Version:   lo.ToPtr(int32(domain_entities_items.StoryVersion_V2)),
			CoverImage: domain_entities_items.StoryCoverImage{
				Path:   domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/8871303a9b6841de983d70e043cfac0a.jpeg"),
				Width:  428,
				Height: 570,
			},
		},
		PlayConfig: &domain_entities_items.StoryPlayRoastedConfig{
			Cover: domain_entities_resource.NewResourceBuilder().WithImageObjectKey(
				domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/8871303a9b6841de983d70e043cfac0a.jpeg"),
				428,
				570,
			).Build(),
			BaseResource: domain_entities_resource.NewResourceBuilder().WithVideoObjectKey(
				domain_entities_resource.VideoResourcePath("flippop/video/item/story/1923251145282854912/202508220811/c520984273924467a9b33cc63e16d7c4.mp4"),
				domain_entities_resource.ImageResourcePath("flippop/image/item/story/1923251145282854912/202508220811/5a1d76039a1940538eaaafa92cb3fec6.jpeg"),
				1080,
				1440,
			).Build(),
			Topic: &domain_entities_items.StoryPlayRoastedTopic{
				Greeting: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     "开场白",
					TTSAudioKey: "test.mp3",
				},
				Ending: &domain_entities_items.StoryPlayRoastedTopicBotAnnouncement{
					Content:     "结束语",
					TTSAudioKey: "test.mp3",
				},
				Questions: []*domain_entities_items.StoryPlayRoastedQuestion{
					{
						Question:    "问题1",
						Thinking:    "思考1",
						TTSAudioKey: "test.mp3",
					},
				},
			},
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.NotNil(t, createdStory)

	story, err := app.StoryUc.UpdateUserSubmittedResource(context.Background(), userB.Summary.ID, createdStory.Summary.Id, domain_entities_resource.NewResourceBuilder().WithImageObjectKey(
		domain_entities_resource.ImageResourcePath("test"),
		20,
		20,
	).Build())
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(story.RoastedPlayContext.UserSubmittedResources))
	assert.Equal(t, false, story.RoastedPlayContext.IsPostMoment)

	app.StoryUc.UpdateStoryPostMomentStatus(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	story, err = app.StoryUc.GetStoryDetail(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, len(story.RoastedPlayContext.UserSubmittedResources))
	assert.Equal(t, true, story.RoastedPlayContext.IsPostMoment)
}
