package tests_stories

import (
	adapter_driven_assembler "boson/internal/adapter/driven/assembler"
	domain_entities_items "boson/internal/domain/entities/items"
	domain_entities_resource "boson/internal/domain/entities/resource"
	domain_services_items_story "boson/internal/domain/services/items/story"
	usecasestest "boson/internal/usecases/tests"
	"context"
	"testing"

	"github.com/test-go/testify/assert"
)

func TestPinStory(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()
	ctx := context.Background()

	userA, err := app.AuthUc.GenerateVisitor(ctx)

	if err != nil {
		t.Fatalf("failed to generate visitor: %v", err)
	}

	var (
		backgroundImage = domain_entities_resource.NewResourceBuilder().WithImageObjectKey(domain_entities_resource.ImageResourcePath("test"), 100, 100).Build()
		caption         = &domain_entities_items.AttachedText{
			Text: "test caption",
		}
		pinEmojiResources = []*domain_entities_items.PinEmojiResource{
			{
				DefaultEmoji: "👌🏻",
				Area: &domain_entities_items.Area{
					X:      0.1,
					Y:      0.2,
					Width:  0.3,
					Height: 0.4,
				},
			},
			{
				GeneratedEmojiResource: domain_entities_resource.NewResourceBuilder().WithImageObjectKey(domain_entities_resource.ImageResourcePath("emoji"), 100, 100).Build(),
				Area: &domain_entities_items.Area{
					X:      0.1,
					Y:      0.2,
					Width:  0.3,
					Height: 0.4,
				},
			},
		}
	)

	// 创建一个 pin story
	pinStory, err := app.StoryUc.CreatePinStory(ctx, userA.Summary.ID, domain_services_items_story.CreatePinStoryAttr{
		StoryBaseCreateAttr: domain_services_items_story.StoryBaseCreateAttr{
			PrivacySettings: &domain_services_items_story.PrivacySettingUpdateAttr{},
		},
		PlayConfig: &domain_entities_items.StoryPinConfig{
			BackgroundImage:   backgroundImage,
			PinEmojiResources: pinEmojiResources,
			Caption:           caption,
		},
	})
	if err != nil {
		t.Fatalf("failed to create pin story: %v", err)
	}

	assert.NotEmpty(t, pinStory.Summary.Id)

	userB, err := app.AuthUc.GenerateVisitor(ctx)
	if err != nil {
		t.Fatalf("failed to generate visitor: %v", err)
	}

	afterDetail, err := app.StoryUc.ConsumePinStory(ctx, userB.Summary.ID, pinStory.Summary.Id, true, nil, 10)
	if err != nil {
		t.Fatalf("failed to consume pin story: %v", err)
	}

	assert.True(t, afterDetail.PinPlayContext.IsUnlocked)
	assert.EqualValues(t, 10, afterDetail.PinPlayContext.SuccessCostSeconds)
	assert.Nil(t, afterDetail.PinPlayContext.FailedImage)

	dto := adapter_driven_assembler.ConvertStoryDetailToDto(afterDetail)
	assert.EqualValues(t, dto.GetPinConfig().BackgroundImage.ObjectKey, backgroundImage.GetObjectKey())
	assert.Len(t, dto.GetPinConfig().PinEmojiResources, 2)
	assert.True(t, dto.GetPinContext().IsUnlocked)
}
