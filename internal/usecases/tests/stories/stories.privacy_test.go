package tests_stories

import (
	api_common_v1 "boson/api/common/v1"
	api_errors_v1 "boson/api/errors/v1"
	api_items_story_types_v1 "boson/api/items/story/types/v1"
	api_users_relations_types_v1 "boson/api/users/relations/types/v1"
	domain_services_items_story "boson/internal/domain/services/items/story"
	usecasestest "boson/internal/usecases/tests"
	"context"
	"testing"
	"time"

	"github.com/test-go/testify/assert"
)

func TestUpdateStoryPrivacy(t *testing.T) {
	app := usecasestest.GetTestApp()
	app.Init()

	userA, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	createdStory, err := usecasestest.CreateTurtleSoupStory(context.Background(), app, userA.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}

	userB, err := app.AuthUc.GenerateVisitor(context.Background())
	if err != nil {
		t.Fatal(err)
	}

	// 获取 A 创建的 story
	story, err := app.StoryUc.GetStoryDetail(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	// 正常访问
	if err != nil {
		t.Fatal(err)
	}
	// 正常获取
	assert.EqualValues(t, story.Summary.Id, createdStory.Summary.Id)

	// list created story
	// 正常获取
	stories, _, err := app.StoryUc.ListUserCreatedStory(context.Background(), userB.Summary.ID, userA.Summary.ID, &api_common_v1.ListRequest{
		PageToken: "",
		PageSize:  "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, len(stories), 1)
	assert.EqualValues(t, stories[0].Story.Id, createdStory.Summary.Id)

	homepageStories, _, _, _, err := app.StoryUc.ListHomePageStory(context.Background(), userA.Summary.ID, &api_common_v1.ListRequest{
		PageToken: "",
		PageSize:  "10",
	}, false, nil)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, len(homepageStories), 1)

	// 设置为私密
	privacyType := api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_PRIVATE
	_, err = app.StoryUc.UpdateStory(context.Background(), userA.Summary.ID, createdStory.Summary.Id, domain_services_items_story.UpdateStoryAttr{
		PrivacySettings: &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType: &privacyType,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	// 获取 A 创建的 story
	storyAfterUpdate, err := app.StoryUc.GetStoryDetail(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	// 无法访问了，会 404
	assert.True(t, api_errors_v1.IsErrorReasonContentNotFound(err))
	assert.Nil(t, storyAfterUpdate)

	// 但是 A 可以正常访问
	storyAfterUpdate, err = app.StoryUc.GetStoryDetail(context.Background(), userA.Summary.ID, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, storyAfterUpdate.Summary.Id, createdStory.Summary.Id)
	// list created story
	// 正常获取
	stories, _, err = app.StoryUc.ListUserCreatedStory(context.Background(), userA.Summary.ID, userA.Summary.ID, &api_common_v1.ListRequest{
		PageToken: "",
		PageSize:  "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, len(stories), 1)
	assert.EqualValues(t, stories[0].Story.Id, createdStory.Summary.Id)

	// A 修改为朋友可见
	privacyType = api_items_story_types_v1.PrivacyType_PRIVACY_TYPE_FRIEND
	_, err = app.StoryUc.UpdateStory(context.Background(), userA.Summary.ID, createdStory.Summary.Id, domain_services_items_story.UpdateStoryAttr{
		PrivacySettings: &domain_services_items_story.PrivacySettingUpdateAttr{
			PrivacyType: &privacyType,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	// 获取 A 创建的 story
	storyAfterUpdate, err = app.StoryUc.GetStoryDetail(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	// 无法访问了，会 404
	assert.True(t, api_errors_v1.IsErrorReasonContentNotFound(err))
	assert.Nil(t, storyAfterUpdate)

	// 此时，A 关注 了 B
	_, err = app.RelationUc.SaveRelationToUser(context.Background(), userA.Summary.ID, api_users_relations_types_v1.UserRelationType_USER_RELATION_TYPE_FOLLOWING, userB.Summary.ID)
	if err != nil {
		t.Fatal(err)
	}

	// B 可以访问 A 的 story
	storyAfterUpdate, err = app.StoryUc.GetStoryDetail(context.Background(), userB.Summary.ID, createdStory.Summary.Id)
	if err != nil {
		t.Fatal(err)
	}
	assert.EqualValues(t, storyAfterUpdate.Summary.Id, createdStory.Summary.Id)
	// list created story
	// 正常获取
	stories, _, err = app.StoryUc.ListUserCreatedStory(context.Background(), userB.Summary.ID, userA.Summary.ID, &api_common_v1.ListRequest{
		PageToken: "",
		PageSize:  "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, len(stories), 1)
	assert.EqualValues(t, stories[0].Story.Id, createdStory.Summary.Id)

	// 再次设置为 1s 后过期，等待 2s，不可以在访问了
	visibleBeforeTimestamp := uint32(time.Now().Add(time.Second * 1).Unix())
	_, err = app.StoryUc.UpdateStory(context.Background(), userA.Summary.ID, createdStory.Summary.Id, domain_services_items_story.UpdateStoryAttr{
		PrivacySettings: &domain_services_items_story.PrivacySettingUpdateAttr{
			VisibleBeforeTimestamp: &visibleBeforeTimestamp,
		},
	})
	if err != nil {
		t.Fatal(err)
	}

	time.Sleep(time.Second * 2)
	stories, _, err = app.StoryUc.ListUserCreatedStory(context.Background(), userB.Summary.ID, userA.Summary.ID, &api_common_v1.ListRequest{
		PageToken: "",
		PageSize:  "10",
	})
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, len(stories), 0)
}
