package usecases_search

import (
	api_search_v1 "boson/api/search/v1"
	domain_services_search "boson/internal/domain/services/search"
	"context"

	"github.com/spf13/cast"
)

type SearchUsecase struct {
	*domain_services_search.SearchService
	searchHistoryService *domain_services_search.SearchHistoryService
}

func NewSearchUsecase(
	searchService *domain_services_search.SearchService,
	searchHistoryService *domain_services_search.SearchHistoryService,
) *SearchUsecase {
	return &SearchUsecase{
		SearchService:        searchService,
		searchHistoryService: searchHistoryService,
	}
}

// GetSearchHistory 获取用户搜索历史
func (uc *SearchUsecase) GetSearchHistory(
	ctx context.Context,
	userID int64,
	count int32,
) ([]*api_search_v1.SearchHistoryItem, error) {
	histories, err := uc.searchHistoryService.GetSearchHistory(ctx, userID, count)
	if err != nil {
		return nil, err
	}

	var result []*api_search_v1.SearchHistoryItem
	for _, history := range histories {
		result = append(result, &api_search_v1.SearchHistoryItem{
			SearchId:   cast.ToString(history.ID),
			SearchText: history.SearchText,
			SearchType: history.SearchType,
		})
	}

	return result, nil
}

// DeleteSearchHistory 删除用户搜索历史
func (uc *SearchUsecase) DeleteSearchHistory(
	ctx context.Context,
	userID int64,
	searchIDs []string,
) error {
	return uc.searchHistoryService.DeleteSearchHistory(ctx, userID, searchIDs)
}

// UploadSearchHistory 上传用户搜索历史
func (uc *SearchUsecase) UploadSearchHistory(
	ctx context.Context,
	userID int64,
	searchItems []*api_search_v1.SearchHistoryUploadItem,
) ([]string, error) {
	return uc.searchHistoryService.UploadSearchHistory(ctx, userID, searchItems)
}
