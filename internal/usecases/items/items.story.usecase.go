package usecases_items

import (
	domain_services_items_portal "boson/internal/domain/services/items/portal"
	domain_services_items_story "boson/internal/domain/services/items/story"
)

type ItemStoryUsecase struct {
	*domain_services_items_story.StoryCmdService
	*domain_services_items_story.StoryPlayService
	*domain_services_items_story.StoryQueryService
	*domain_services_items_story.StoryReactionService
	*domain_services_items_story.HideStickerService
	*domain_services_items_portal.PortalService
	*domain_services_items_story.HauntBooShowInfoService
}

func NewItemStoryUsecase(
	storyService *domain_services_items_story.StoryCmdService,
	storyPlayService *domain_services_items_story.StoryPlayService,
	storyQueryService *domain_services_items_story.StoryQueryService,
	storyReactionService *domain_services_items_story.StoryReactionService,
	hideStickerService *domain_services_items_story.HideStickerService,
	portalService *domain_services_items_portal.PortalService,
	hauntBooShowInfoService *domain_services_items_story.HauntBooShowInfoService,
) *ItemStoryUsecase {
	return &ItemStoryUsecase{
		StoryCmdService:         storyService,
		StoryPlayService:        storyPlayService,
		StoryQueryService:       storyQueryService,
		StoryReactionService:    storyReactionService,
		HideStickerService:      hideStickerService,
		PortalService:           portalService,
		HauntBooShowInfoService: hauntBooShowInfoService,
	}
}
