// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"boson/internal/adapter/driven/http/boson_api"
	"boson/internal/adapter/driven/http/services/boo_world"
	"boson/internal/adapter/driven/http/services/fizz"
	"boson/internal/adapter/driven/http/services/im"
	"boson/internal/adapter/driven/http/services/items"
	"boson/internal/adapter/driven/http/services/push"
	"boson/internal/adapter/driven/http/services/resources"
	"boson/internal/adapter/driven/http/services/search"
	"boson/internal/adapter/driven/http/services/tracking"
	"boson/internal/adapter/driven/http/services/users"
	"boson/internal/adapter/driving/repos/app_settings"
	"boson/internal/adapter/driving/repos/fizz"
	"boson/internal/adapter/driving/repos/fizz/play_room"
	"boson/internal/adapter/driving/repos/fizz/vibe"
	"boson/internal/adapter/driving/repos/items"
	"boson/internal/adapter/driving/repos/items/comments"
	"boson/internal/adapter/driving/repos/items/story"
	"boson/internal/adapter/driving/repos/push"
	"boson/internal/adapter/driving/repos/search"
	"boson/internal/adapter/driving/repos/users/albums"
	"boson/internal/adapter/driving/repos/users/binds"
	"boson/internal/adapter/driving/repos/users/boo"
	"boson/internal/adapter/driving/repos/users/device"
	"boson/internal/adapter/driving/repos/users/highlights"
	"boson/internal/adapter/driving/repos/users/im"
	"boson/internal/adapter/driving/repos/users/info"
	"boson/internal/adapter/driving/repos/users/notifications"
	"boson/internal/adapter/driving/repos/users/relations"
	"boson/internal/adapter/driving/services/agora"
	"boson/internal/adapter/driving/services/agora_rtc"
	driving_services_agora2 "boson/internal/adapter/driving/services/agora_rtm"
	"boson/internal/adapter/driving/services/cdn"
	"boson/internal/adapter/driving/services/cdn/s3"
	"boson/internal/adapter/driving/services/kling"
	"boson/internal/adapter/driving/services/openai"
	"boson/internal/adapter/driving/services/openrouter"
	"boson/internal/adapter/driving/services/polly"
	"boson/internal/adapter/driving/services/push/google_firebase"
	"boson/internal/adapter/driving/services/recommend"
	"boson/internal/adapter/driving/services/sam"
	"boson/internal/adapter/driving/services/search"
	"boson/internal/adapter/driving/services/third_party_auth"
	"boson/internal/adapter/driving/services/third_party_auth/apple"
	"boson/internal/adapter/driving/services/third_party_auth/facebook"
	"boson/internal/adapter/driving/services/third_party_auth/google"
	"boson/internal/adapter/driving/services/twillo"
	"boson/internal/adapter/driving/services/uniq_id_generator"
	"boson/internal/conf"
	"boson/internal/domain/services/ai"
	"boson/internal/domain/services/boo_world"
	"boson/internal/domain/services/fizz"
	"boson/internal/domain/services/fizz/peek"
	"boson/internal/domain/services/fizz/play_room/relay"
	"boson/internal/domain/services/fizz/play_room/rush"
	"boson/internal/domain/services/fizz/vibe"
	"boson/internal/domain/services/im"
	"boson/internal/domain/services/items"
	"boson/internal/domain/services/items/comments"
	"boson/internal/domain/services/items/portal"
	"boson/internal/domain/services/items/story"
	"boson/internal/domain/services/push"
	"boson/internal/domain/services/resources"
	"boson/internal/domain/services/rtm"
	"boson/internal/domain/services/search"
	"boson/internal/domain/services/users/albums"
	"boson/internal/domain/services/users/auth"
	"boson/internal/domain/services/users/boo"
	"boson/internal/domain/services/users/bot"
	"boson/internal/domain/services/users/highlights"
	"boson/internal/domain/services/users/info"
	"boson/internal/domain/services/users/nofications"
	"boson/internal/domain/services/users/push"
	"boson/internal/domain/services/users/recommend"
	"boson/internal/domain/services/users/relations"
	"boson/internal/infra/data"
	"boson/internal/usecases/boo_world"
	"boson/internal/usecases/fizz"
	"boson/internal/usecases/im"
	"boson/internal/usecases/items"
	"boson/internal/usecases/push"
	"boson/internal/usecases/resources"
	"boson/internal/usecases/search"
	"boson/internal/usecases/tracking"
	"boson/internal/usecases/users"
	"boson/pkg/auth"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
)

import (
	_ "go.uber.org/automaxprocs"
	_ "net/http/pprof"
)

// Injectors from wire.go:

func wireApp(bootstrap *conf.Bootstrap, logger log.Logger, helper *log.Helper, jwt *auth.Jwt) (*kratos.App, func(), error) {
	dataData, cleanup := data.NewData(bootstrap, helper)
	service := s3.NewService(bootstrap)
	adapter_driving_services_cdnService := adapter_driving_services_cdn.NewService(service)
	domain_services_resourcesService := domain_services_resources.NewService(adapter_driving_services_cdnService)
	resourceUsecase := usecases_resources.NewResourceUsecase(domain_services_resourcesService)
	resourceService := adapter_driven_http_services_resources.NewResourceService(resourceUsecase, jwt)
	repo := adapter_driving_repos_users_relations.NewRepo(dataData)
	queryRepo := adapter_driving_repos_users_binds.NewQueryRepo(dataData)
	adapter_driving_repos_users_infoQueryRepo := adapter_driving_repos_users_info.NewQueryRepo(dataData, repo, queryRepo)
	itemMusicRepo := adapter_driving_repos_items.NewItemMusicRepo(dataData)
	itemsQueryRepo := adapter_driving_repos_items.NewItemsQueryRepo(dataData, adapter_driving_repos_users_infoQueryRepo, itemMusicRepo)
	itemCmdRepo := adapter_driving_repos_items.NewItemCmdRepo(dataData, itemsQueryRepo)
	uniqIdGenerator := uniq_id_generator.NewUniqIdGenerator(dataData, helper)
	itemCmdService := domain_services_items.NewItemCmdService(itemCmdRepo, uniqIdGenerator)
	itemCmdUsecase := usecases_items.NewItemCmdUsecase(itemCmdService)
	itemQueryService := domain_services_items.NewItemQueryService(itemsQueryRepo)
	itemQueryUsecase := usecases_items.NewItemQueryUsecase(itemQueryService)
	openAIClient := adapter_driving_services_openai.NewOpenAIClient(adapter_driving_services_cdnService, helper)
	asrService := domain_services_ai.NewAsrService(openAIClient, bootstrap)
	adapter_driving_repos_app_settingsRepo := adapter_driving_repos_app_settings.NewRepo(dataData)
	client := adapter_driving_services_openrouter.NewClient(bootstrap, helper)
	llmService := domain_services_ai.NewLlmService(bootstrap, helper, openAIClient, openAIClient, adapter_driving_repos_app_settingsRepo, client, dataData, adapter_driving_services_cdnService)
	itemAiUsecase := usecases_items.NewItemAiUsecase(asrService, llmService)
	itemMusicSearchService := domain_services_items.NewItemMusicSearchService(itemMusicRepo)
	itemMusicSearchUsecase := usecases_items.NewItemMusicSearchUsecase(itemMusicSearchService)
	itemCommentsRepo := adapter_driving_repos_items_comments.NewItemCommentsRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	itemCommentsService := domain_services_items_comments.NewItemCommentsService(itemCommentsRepo)
	itemCommentsUsecase := usecases_items.NewItemCommentsUsecase(itemCommentsService)
	storyTemplateRepo := adapter_driving_repos_items_story.NewStoryTemplateRepo(dataData, uniqIdGenerator, adapter_driving_repos_app_settingsRepo)
	storyTopRepo := adapter_driving_repos_items_story.NewStoryTopRepo(dataData, helper)
	storyStatRepo := adapter_driving_repos_items_story.NewStoryStatRepo(dataData)
	adapter_driving_repos_users_booRepo := adapter_driving_repos_users_boo.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	userPortalRelationRepo := adapter_driving_repos_items_story.NewUserPortalRelationRepo(dataData)
	storyContextRepo := adapter_driving_repos_items_story.NewStoryContextRepo(dataData, userPortalRelationRepo)
	storyReactionRepo := adapter_driving_repos_items_story.NewStoryReactionRepo(dataData, storyStatRepo)
	storyQueryRepo := adapter_driving_repos_items_story.NewStoryQueryRepo(dataData, repo, adapter_driving_repos_users_infoQueryRepo, adapter_driving_repos_users_booRepo, storyContextRepo, storyReactionRepo, storyTopRepo, storyStatRepo)
	portalRepo := adapter_driving_repos_items_story.NewPortalCmdRepo(dataData, uniqIdGenerator, storyQueryRepo, adapter_driving_repos_users_infoQueryRepo, adapter_driving_repos_users_booRepo, dataData, userPortalRelationRepo, helper)
	storyCmdRepo := adapter_driving_repos_items_story.NewStoryCmdRepo(dataData, storyTemplateRepo, storyTopRepo, storyStatRepo, portalRepo, storyQueryRepo)
	pollySynthesizer := adapter_driving_services_polly.NewPollySynthesizer(bootstrap)
	ttsService := domain_services_ai.NewTtsService(pollySynthesizer, adapter_driving_services_cdnService, bootstrap, helper)
	adapter_driving_services_recommendClient := adapter_driving_services_recommend.NewClient(bootstrap)
	cmdRepo := adapter_driving_repos_users_info.NewCmdRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	imUuidRepo := adapter_driving_repos_users_im.NewImUuidRepo(dataData)
	driving_services_agoraClient := driving_services_agora.NewClient(bootstrap, imUuidRepo, helper, adapter_driving_repos_users_infoQueryRepo)
	usersInfoService := domain_services_users_info.NewUsersInfoService(adapter_driving_repos_users_infoQueryRepo, cmdRepo, driving_services_agoraClient)
	storyHauntBooShowInfoRepo := adapter_driving_repos_items_story.NewStoryHauntBooShowInfoRepo(dataData, storyQueryRepo)
	hauntBooShowInfoService := domain_services_items_story.NewHauntBooShowInfoService(storyHauntBooShowInfoRepo, dataData, dataData)
	storyQueryService := domain_services_items_story.NewStoryQueryService(storyQueryRepo, storyTemplateRepo, adapter_driving_services_recommendClient, usersInfoService, adapter_driving_repos_app_settingsRepo, hauntBooShowInfoService)
	imService := domain_services_im.NewIMService(driving_services_agoraClient)
	samClient := adapter_driving_services_sam.NewSamClient(helper, service)
	hideStickersRepo := adapter_driving_repos_items_story.NewHideStickersRepo(dataData)
	hideStickerService := domain_services_items_story.NewHideStickerService(hideStickersRepo)
	storyPlayService := domain_services_items_story.NewStoryPlayService(storyContextRepo, llmService, ttsService, storyQueryService, dataData, imService, asrService, usersInfoService, adapter_driving_repos_app_settingsRepo, adapter_driving_services_cdnService, samClient, hideStickerService, storyQueryRepo, adapter_driving_repos_users_booRepo, hauntBooShowInfoService, dataData, uniqIdGenerator)
	klingClient := adapter_driving_services_kling.NewKlingClient(helper, bootstrap)
	booAIService := domain_services_ai.NewBooAIService(bootstrap, helper, klingClient, adapter_driving_services_cdnService, openAIClient, openAIClient)
	storyCmdService := domain_services_items_story.NewStoryCmdService(uniqIdGenerator, storyCmdRepo, storyPlayService, storyQueryService, helper, dataData, hideStickerService, ttsService, adapter_driving_repos_app_settingsRepo, llmService, storyQueryRepo, adapter_driving_repos_users_booRepo, booAIService)
	storyReactionService := domain_services_items_story.NewStoryReactionService(usersInfoService, storyQueryService, storyReactionRepo)
	portalService := domain_services_items_portal.NewPortalService(uniqIdGenerator, portalRepo, storyQueryService, usersInfoService, adapter_driving_services_recommendClient, hauntBooShowInfoService, imService)
	itemStoryUsecase := usecases_items.NewItemStoryUsecase(storyCmdService, storyPlayService, storyQueryService, storyReactionService, hideStickerService, portalService, hauntBooShowInfoService)
	deviceRepo := adapter_driving_repos_users_device.NewDeviceRepo(dataData, adapter_driving_repos_users_infoQueryRepo, uniqIdGenerator)
	userPushService := domain_services_users_push.NewUserPushService(deviceRepo)
	userInfoUsecase := usecases_users.NewUserInfoUsecase(usersInfoService, userPushService)
	itemsService := adapter_driven_http_services_items.NewItemsService(itemCmdUsecase, itemQueryUsecase, itemAiUsecase, itemMusicSearchUsecase, itemCommentsUsecase, itemStoryUsecase, userInfoUsecase, jwt)
	twillioClient := adapter_driving_services_twillo.NewTwillioClient(bootstrap)
	adapter_driving_service_google_authAuth := adapter_driving_service_google_auth.NewAuth(bootstrap)
	adapter_driving_service_facebook_authAuth := adapter_driving_service_facebook_auth.NewAuth(bootstrap)
	appleAuthClient := adapter_driving_service_third_party_auth_apple.NewAppleAuthClient(bootstrap)
	provider := adapter_driving_service_third_party_auth.NewProvider(adapter_driving_service_google_authAuth, adapter_driving_service_facebook_authAuth, appleAuthClient)
	userVisitorService := domain_services_users_auth.NewUserVisitorService(deviceRepo)
	usersAuthService := domain_services_users_auth.NewUsersAuthService(twillioClient, provider, adapter_driving_repos_users_infoQueryRepo, usersInfoService, userVisitorService, driving_services_agoraClient, dataData)
	userAuthUsecase := usecases_users.NewUserAuthUsecase(usersAuthService)
	fizzTagRepo := adapter_driving_repos_fizz.NewFizzTagRepo(dataData)
	fizzUserRelationRepo := adapter_driving_repos_fizz.NewFizzUserRelationRepo(dataData)
	fizzTopRepo := adapter_driving_repos_fizz.NewFizzTopRepo(dataData, helper)
	adapter_driving_repos_fizzRepo := adapter_driving_repos_fizz.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo, fizzTagRepo, fizzUserRelationRepo, fizzTopRepo)
	fizzMediaUserRelationRepo := adapter_driving_repos_fizz.NewFizzMediaUserRelationRepo(dataData)
	fizzMediaQueryRepo := adapter_driving_repos_fizz.NewFizzMediaQueryRepo(dataData, adapter_driving_repos_users_infoQueryRepo, fizzMediaUserRelationRepo)
	fizzEventsRepo := adapter_driving_repos_fizz.NewFizzEventsRepo(dataData, fizzMediaQueryRepo)
	adapter_driving_repos_users_notificationsRepo := adapter_driving_repos_users_notifications.NewRepo(dataData, adapter_driving_repos_fizzRepo, fizzEventsRepo, adapter_driving_repos_users_infoQueryRepo)
	adapter_driving_repos_pushRepo := adapter_driving_repos_push.NewRepo(dataData)
	firebaseService := driving_services_push_firebase.NewFirebaseService(bootstrap, helper)
	pushInterceptionRepo := adapter_driving_repos_push.NewPushInterceptionRepo(dataData)
	pushInterceptionService := domain_services_push.NewPushInterceptionService(uniqIdGenerator, pushInterceptionRepo)
	pushService := domain_services_push.NewPushService(bootstrap, uniqIdGenerator, adapter_driving_repos_pushRepo, firebaseService, pushInterceptionService)
	usersNotificationsService := domain_services_users_notifications.NewUsersNotificationsService(uniqIdGenerator, adapter_driving_repos_users_notificationsRepo, usersInfoService, fizzUserRelationRepo, pushService, storyQueryService, helper)
	userRelationsService := domain_services_user_relations.NewUserRelationsService(dataData, repo, usersInfoService, usersNotificationsService, helper)
	userRelationUsecase := usecases_users.NewUserRelationUsecase(userRelationsService)
	userHighlightsRepo := adapter_driving_repos_users_highlights.NewUserHighlightsRepo(dataData)
	fizzMediaCmdRepo := adapter_driving_repos_fizz.NewFizzMediaCmdRepo(dataData)
	fizzQueryService := domain_services_fizz.NewFizzQueryService(adapter_driving_repos_fizzRepo, adapter_driving_services_recommendClient)
	fizzEventsFireService := domain_services_fizz.NewFizzEventsFireService(fizzEventsRepo, uniqIdGenerator, usersNotificationsService)
	fizzMediaService := domain_services_fizz.NewFizzMediaService(uniqIdGenerator, fizzMediaCmdRepo, fizzMediaQueryRepo, fizzQueryService, fizzEventsFireService)
	userHighlightsService := domain_services_users_highlights.NewUserHighlightsService(userHighlightsRepo, uniqIdGenerator, fizzMediaService)
	userHighlightsUsecase := usecases_users.NewUserHighlightsUsecase(userHighlightsService)
	usersRecommendService := domain_services_users_recommend.NewUsersRecommendService(adapter_driving_services_recommendClient, usersInfoService)
	usersRecommendUsecase := usecases_users.NewUsersRecommendUsecase(usersRecommendService)
	userNotificationUsecase := usecases_users.NewUserNotificationUsecase(usersNotificationsService)
	client2 := driving_services_agora2.NewClient(bootstrap)
	rtmService := domain_services_rtm.NewRtmService(client2)
	usersRtmUsecase := usecases_users.NewUsersRtmUsecase(rtmService)
	booService := domain_services_users_boo.NewBooService(adapter_driving_repos_users_booRepo, dataData, dataData, uniqIdGenerator, imService, adapter_driving_repos_users_infoQueryRepo, rtmService, hideStickersRepo)
	jobRepo := adapter_driving_repos_users_boo.NewJobRepo(dataData, adapter_driving_repos_users_booRepo)
	configService := domain_services_users_boo.NewConfigService(adapter_driving_repos_app_settingsRepo)
	booJobServiceV2 := domain_services_users_boo.NewBooJobServiceV2(uniqIdGenerator, dataData, jobRepo, dataData, configService, rtmService, booService)
	handlerProvider := domain_services_users_boo.NewHandlerProvider(booAIService, booAIService, bootstrap)
	scheduler := domain_services_users_boo.NewDefaultSchedulerFromConfigService(jobRepo, configService, booJobServiceV2, handlerProvider, helper, dataData)
	booUsecase := usecases_users.NewBooUsecase(booService, booJobServiceV2, scheduler)
	adapter_driving_repos_users_albumsRepo := adapter_driving_repos_users_albums.NewRepo(dataData)
	userAlbumsService := domain_services_user_albums.NewUserAlbumsService(uniqIdGenerator, dataData, adapter_driving_repos_users_albumsRepo, adapter_driving_repos_users_albumsRepo, storyQueryRepo)
	userAlbumUsecase := usecases_users.NewUserAlbumUsecase(userAlbumsService)
	usersService := adapter_driven_http_services_users.NewUsersService(jwt, userInfoUsecase, userAuthUsecase, userRelationUsecase, userHighlightsUsecase, usersRecommendUsecase, userNotificationUsecase, usersRtmUsecase, booUsecase, userAlbumUsecase)
	adapter_driving_services_searchClient := adapter_driving_services_search.NewClient(dataData)
	searchService := domain_services_search.NewSearchService(adapter_driving_services_searchClient, usersInfoService)
	searchHistoryRepo := adapter_driving_repos_search.NewSearchHistoryRepo(dataData)
	searchHistoryService := domain_services_search.NewSearchHistoryService(searchHistoryRepo)
	searchUsecase := usecases_search.NewSearchUsecase(searchService, searchHistoryService)
	adapter_driven_http_services_searchSearchService := adapter_driven_http_services_search.NewSearchService(searchUsecase, jwt)
	userRelationService := domain_services_fizz.NewUserRelationService(bootstrap, dataData, llmService, fizzUserRelationRepo, fizzQueryService, fizzMediaService, usersInfoService)
	fizzCmdService := domain_services_fizz.NewFizzCmdService(uniqIdGenerator, adapter_driving_repos_fizzRepo, fizzMediaService, fizzQueryService, userRelationService, llmService)
	fizzMediaMessageCmdRepo := adapter_driving_repos_fizz.NewFizzMediaMessageCmdRepo(dataData, fizzMediaCmdRepo)
	fizzMediaUserRelationInteractionService := domain_services_fizz.NewFizzMediaUserRelationInteractionService(dataData, fizzMediaQueryRepo, fizzMediaUserRelationRepo, fizzMediaMessageCmdRepo, usersInfoService, imService, bootstrap)
	fizzEventsQueryService := domain_services_fizz.NewFizzEventsQueryService(fizzEventsRepo, fizzMediaService)
	fizzPeekRepo := adapter_driving_repos_fizz.NewFizzPeekRepo(dataData, adapter_driving_repos_fizzRepo, adapter_driving_repos_users_infoQueryRepo)
	userBotService := domain_services_users_bot.NewUserBotService(userVisitorService, usersInfoService, cmdRepo)
	fizzPeekMatchingService := domain_services_fizz_peek.NewFizzPeekMatchingService(dataData, uniqIdGenerator, fizzPeekRepo, helper, dataData, pushService, fizzCmdService, userRelationService, userBotService, adapter_driving_repos_app_settingsRepo, usersInfoService)
	fizzPeekService := domain_services_fizz_peek.NewFizzPeekService(uniqIdGenerator, fizzPeekRepo, fizzPeekMatchingService, fizzQueryService)
	fizzRelayRepo := adapter_driving_repos_fizz_play_room.NewFizzRelayRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	driving_services_agora_rtcClient := driving_services_agora_rtc.NewClient(bootstrap)
	fizzPlayRoomRelayService := domain_services_fizz_play_room_relay.NewFizzPlayRoomRelayService(fizzRelayRepo, dataData, dataData, driving_services_agora_rtcClient, uniqIdGenerator, imService, rtmService, helper)
	fizzRushRepo := adapter_driving_repos_fizz_play_room.NewFizzRushRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	fizzPlayRoomRushService := domain_services_fizz_play_room_rush.NewFizzPlayRoomRushService(fizzRushRepo, dataData, dataData, driving_services_agora_rtcClient, uniqIdGenerator, imService, rtmService, helper)
	adapter_driving_repos_fizz_vibeRepo := adapter_driving_repos_fizz_vibe.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	vibeService := domain_services_fizz_vibe.NewVibeService(uniqIdGenerator, adapter_driving_repos_fizz_vibeRepo, adapter_driving_repos_fizz_vibeRepo)
	fizzUsecase := usecases_fizz.NewFizzUsecase(fizzCmdService, fizzQueryService, userRelationService, fizzMediaService, fizzMediaUserRelationInteractionService, fizzEventsFireService, fizzEventsQueryService, fizzPeekService, fizzPeekMatchingService, fizzPlayRoomRelayService, fizzPlayRoomRushService, vibeService)
	fizzService := adapter_driven_http_services_fizz.NewFizzService(fizzUsecase, jwt)
	pushUsecase := usecases_push.NewPushUsecase(pushService)
	adapter_driven_http_services_pushPushService := adapter_driven_http_services_push.NewPushService(pushUsecase, jwt)
	trackingEventReportUsecase := usecases_tracking.NewTrackingEventReportUsecase(dataData)
	trackingEventReportService := adapter_driven_http_services_tracking.NewTrackingEventReportService(trackingEventReportUsecase, jwt)
	im := usecases_im.NewIM(imService)
	adapter_driven_http_services_imIM := adapter_driven_http_services_im.NewIM(im, jwt)
	booWorldResourceRepo := adapter_driving_repos_users_boo.NewBooWorldResourceRepo(dataData)
	booWorldMapRepo := adapter_driving_repos_users_boo.NewBooWorldMapRepo(dataData)
	booWorldMapService := domain_services_boo_world.NewBooWorldMapService(uniqIdGenerator, booWorldResourceRepo, booWorldMapRepo, dataData, booService, usersInfoService)
	booWorldCaptureRecordsRepo := adapter_driving_repos_users_boo.NewBooWorldCaptureRecordsRepo(dataData, booService, usersInfoService)
	booWorldCaptureService := domain_services_boo_world.NewBooWorldCaptureService(booWorldCaptureRecordsRepo, uniqIdGenerator, booService)
	booWorldUsecase := usecases_boo_world.NewBooWorldUsecase(booWorldMapService, booWorldCaptureService)
	booWorldService := adapter_driven_http_services_boo_world.NewBooWorldService(jwt, booWorldUsecase)
	httpServer := boson_api.NewHTTPServer(bootstrap, dataData, logger, jwt, resourceService, itemsService, usersService, adapter_driven_http_services_searchSearchService, fizzService, adapter_driven_http_services_pushPushService, trackingEventReportService, adapter_driven_http_services_imIM, booWorldService)
	app := newApp(logger, httpServer)
	return app, func() {
		cleanup()
	}, nil
}
