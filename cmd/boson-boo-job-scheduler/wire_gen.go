// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"boson/internal/adapter/driving/repos/app_settings"
	"boson/internal/adapter/driving/repos/items/story"
	"boson/internal/adapter/driving/repos/users/binds"
	"boson/internal/adapter/driving/repos/users/boo"
	"boson/internal/adapter/driving/repos/users/im"
	"boson/internal/adapter/driving/repos/users/info"
	"boson/internal/adapter/driving/repos/users/relations"
	"boson/internal/adapter/driving/services/agora"
	driving_services_agora2 "boson/internal/adapter/driving/services/agora_rtm"
	"boson/internal/adapter/driving/services/cdn"
	"boson/internal/adapter/driving/services/cdn/s3"
	"boson/internal/adapter/driving/services/kling"
	"boson/internal/adapter/driving/services/openai"
	"boson/internal/adapter/driving/services/uniq_id_generator"
	"boson/internal/conf"
	"boson/internal/domain/services/ai"
	"boson/internal/domain/services/im"
	"boson/internal/domain/services/rtm"
	"boson/internal/domain/services/users/boo"
	"boson/internal/infra/data"
	"boson/internal/usecases/users"
	"github.com/go-kratos/kratos/v2/log"
)

// Injectors from wire.go:

func wireApp(bootstrap *conf.Bootstrap, logger log.Logger, helper *log.Helper) (*SchedulerApp, func(), error) {
	dataData, cleanup := data.NewData(bootstrap, helper)
	repo := adapter_driving_repos_users_relations.NewRepo(dataData)
	queryRepo := adapter_driving_repos_users_binds.NewQueryRepo(dataData)
	adapter_driving_repos_users_infoQueryRepo := adapter_driving_repos_users_info.NewQueryRepo(dataData, repo, queryRepo)
	adapter_driving_repos_users_booRepo := adapter_driving_repos_users_boo.NewRepo(dataData, adapter_driving_repos_users_infoQueryRepo)
	uniqIdGenerator := uniq_id_generator.NewUniqIdGenerator(dataData, helper)
	imUuidRepo := adapter_driving_repos_users_im.NewImUuidRepo(dataData)
	client := driving_services_agora.NewClient(bootstrap, imUuidRepo, helper, adapter_driving_repos_users_infoQueryRepo)
	imService := domain_services_im.NewIMService(client)
	driving_services_agoraClient := driving_services_agora2.NewClient(bootstrap)
	rtmService := domain_services_rtm.NewRtmService(driving_services_agoraClient)
	hideStickersRepo := adapter_driving_repos_items_story.NewHideStickersRepo(dataData)
	booService := domain_services_users_boo.NewBooService(adapter_driving_repos_users_booRepo, dataData, dataData, uniqIdGenerator, imService, adapter_driving_repos_users_infoQueryRepo, rtmService, hideStickersRepo)
	jobRepo := adapter_driving_repos_users_boo.NewJobRepo(dataData, adapter_driving_repos_users_booRepo)
	adapter_driving_repos_app_settingsRepo := adapter_driving_repos_app_settings.NewRepo(dataData)
	configService := domain_services_users_boo.NewConfigService(adapter_driving_repos_app_settingsRepo)
	booJobServiceV2 := domain_services_users_boo.NewBooJobServiceV2(uniqIdGenerator, dataData, jobRepo, dataData, configService, rtmService, booService)
	klingClient := adapter_driving_services_kling.NewKlingClient(helper, bootstrap)
	service := s3.NewService(bootstrap)
	adapter_driving_services_cdnService := adapter_driving_services_cdn.NewService(service)
	openAIClient := adapter_driving_services_openai.NewOpenAIClient(adapter_driving_services_cdnService, helper)
	booAIService := domain_services_ai.NewBooAIService(bootstrap, helper, klingClient, adapter_driving_services_cdnService, openAIClient, openAIClient)
	handlerProvider := domain_services_users_boo.NewHandlerProvider(booAIService, booAIService, bootstrap)
	scheduler := domain_services_users_boo.NewDefaultSchedulerFromConfigService(jobRepo, configService, booJobServiceV2, handlerProvider, helper, dataData)
	booUsecase := usecases_users.NewBooUsecase(booService, booJobServiceV2, scheduler)
	schedulerApp := NewSchedulerApp(helper, booUsecase)
	return schedulerApp, func() {
		cleanup()
	}, nil
}
