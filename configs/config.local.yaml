env: ENV_LOCAL
server:
  open_telemetry:
    report_host: ""
    report_path: ""
  http:
    addr: 0.0.0.0:8001
    timeout: 120s
  jwt_token_authentication:
    key: "xjsxoajo"
    valid_time: 604800s
    need_refresh_time: 302400s
data:
  kafka:
    brokers:
      - "localhost:9092"
  database:
    master_driver: "root:root@tcp(localhost:3306)/flippop?charset=utf8mb4&parseTime=True&loc=Asia%2FShanghai"
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
  dynamodb:
    access_key_id: "local"
    secret_access_key: "local"
    region: us-west-2
    endpoint: "http://localhost:8000"
third_party:
  kling:
    access_key: "local-ak"
    secret_key: "local-sk"
  agora:
    host: "agora.com"
    org_name: "org"
    app_name: "app"
    app_id: "app_id"
    app_cert: "app_cert"
    rtm:
      app_id: ""
      app_cert: ""
      customer_key: ""
      customer_secret: ""
  s3:
    region: ""
    access_key_id: ""
    secret_access_key: ""
  twilio:
    app_id: ""
    account_sid: ""
    auth_token: ""
